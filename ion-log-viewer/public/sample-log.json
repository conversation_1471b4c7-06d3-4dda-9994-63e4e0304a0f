{"metadata": {"startTime": 1640995200000, "endTime": 1640995260000, "duration": 60000, "recordingDate": "2022-01-01T00:00:00Z", "version": "1.0.0", "robotModel": "TurtleBot3", "robotName": "tb3_robot", "robotVersion": "3.2.0"}, "session": {"startTime": 1640995200000, "endTime": 1640995260000, "duration": 60000, "recordingDate": "2022-01-01T00:00:00Z", "version": "1.0.0"}, "robot": {"model": "TurtleBot3", "name": "tb3_robot", "version": "3.2.0"}, "topics": [{"name": "/rosout_agg", "type": "rosgraph_msgs/Log", "frequency": 10.0, "messages": [{"timestamp": 1640995201000, "content": {"level": "INFO", "msg": "Robot initialization complete", "name": "robot_node", "file": "robot.cpp", "line": 42}}, {"timestamp": 1640995205000, "content": {"level": "WARN", "msg": "Battery level low: 15%", "name": "battery_monitor", "file": "battery.cpp", "line": 128}}, {"timestamp": 1640995210000, "content": {"level": "INFO", "msg": "Navigation started", "name": "nav_node", "file": "navigation.cpp", "line": 67}}, {"timestamp": 1640995215000, "content": {"level": "DEBUG", "msg": "Sensor data received", "name": "sensor_node", "file": "sensors.cpp", "line": 89}}, {"timestamp": 1640995220000, "content": {"level": "ERROR", "msg": "Communication timeout with base station", "name": "comm_node", "file": "communication.cpp", "line": 156}}]}, {"name": "/tb_control/wheel_odom", "type": "nav_msgs/Odometry", "frequency": 50.0, "messages": [{"timestamp": 1640995201000, "content": {"pose": {"pose": {"position": {"x": 0.0, "y": 0.0, "z": 0.0}, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}}}, {"timestamp": 1640995202000, "content": {"pose": {"pose": {"position": {"x": 0.1, "y": 0.0, "z": 0.0}, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}}}, {"timestamp": 1640995203000, "content": {"pose": {"pose": {"position": {"x": 0.2, "y": 0.1, "z": 0.0}, "orientation": {"x": 0.0, "y": 0.0, "z": 0.1, "w": 0.995}}}}}, {"timestamp": 1640995204000, "content": {"pose": {"pose": {"position": {"x": 0.3, "y": 0.2, "z": 0.0}, "orientation": {"x": 0.0, "y": 0.0, "z": 0.2, "w": 0.98}}}}}, {"timestamp": 1640995205000, "content": {"pose": {"pose": {"position": {"x": 0.4, "y": 0.4, "z": 0.0}, "orientation": {"x": 0.0, "y": 0.0, "z": 0.3, "w": 0.954}}}}}]}, {"name": "/sensor_data", "type": "sensor_msgs/LaserScan", "frequency": 10.0, "messages": [{"timestamp": 1640995201000, "content": {"angle_min": -3.14159, "angle_max": 3.14159, "angle_increment": 0.0174533, "range_min": 0.1, "range_max": 10.0, "ranges": [1.0, 1.5, 2.0, 2.5, 3.0]}}, {"timestamp": 1640995202000, "content": {"angle_min": -3.14159, "angle_max": 3.14159, "angle_increment": 0.0174533, "range_min": 0.1, "range_max": 10.0, "ranges": [1.1, 1.6, 2.1, 2.6, 3.1]}}]}]}