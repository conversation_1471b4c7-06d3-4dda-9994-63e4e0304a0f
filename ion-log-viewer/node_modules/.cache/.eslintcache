[{"/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts": "4", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/SessionInfoPanel.tsx": "5", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/ThreeDViewport.tsx": "6", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/RobotInfoPanel.tsx": "7", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/LogConsole.tsx": "8", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/TopicReader.tsx": "9", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/CameraView.tsx": "10", "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/PlaybackControl.tsx": "11"}, {"size": 554, "mtime": 1748518054912, "results": "12", "hashOfConfig": "13"}, {"size": 425, "mtime": 1748518054912, "results": "14", "hashOfConfig": "13"}, {"size": 5331, "mtime": 1748518754638, "results": "15", "hashOfConfig": "13"}, {"size": 19424, "mtime": 1748519601249, "results": "16", "hashOfConfig": "13"}, {"size": 4746, "mtime": 1748519516562, "results": "17", "hashOfConfig": "13"}, {"size": 8826, "mtime": 1748518800658, "results": "18", "hashOfConfig": "13"}, {"size": 4157, "mtime": 1748519547317, "results": "19", "hashOfConfig": "13"}, {"size": 7415, "mtime": 1748518269794, "results": "20", "hashOfConfig": "13"}, {"size": 8799, "mtime": 1748519235445, "results": "21", "hashOfConfig": "13"}, {"size": 9234, "mtime": 1748518301094, "results": "22", "hashOfConfig": "13"}, {"size": 5018, "mtime": 1748518458892, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1weq6ym", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/SessionInfoPanel.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/ThreeDViewport.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/RobotInfoPanel.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/LogConsole.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/TopicReader.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/CameraView.tsx", [], [], "/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/PlaybackControl.tsx", [], []]