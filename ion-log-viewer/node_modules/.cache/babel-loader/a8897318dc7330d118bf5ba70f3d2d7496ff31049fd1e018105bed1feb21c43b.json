{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { BigIntSerde } from \"./BigIntSerde\";\nimport * as IonBinary from \"./IonBinary\";\nimport { IVM } from \"./IonConstants\";\nimport { Decimal } from \"./IonDecimal\";\nimport { Timestamp, TimestampPrecision } from \"./IonTimestamp\";\nimport { IonTypes } from \"./IonTypes\";\nimport { decodeUtf8 } from \"./IonUnicode\";\nimport SignAndMagnitudeInt from \"./SignAndMagnitudeInt\";\nconst EOF = -1;\nconst TB_DATAGRAM = 20;\nfunction get_ion_type(rt) {\n  switch (rt) {\n    case IonBinary.TB_NULL:\n      return IonTypes.NULL;\n    case IonBinary.TB_BOOL:\n      return IonTypes.BOOL;\n    case IonBinary.TB_INT:\n      return IonTypes.INT;\n    case IonBinary.TB_NEG_INT:\n      return IonTypes.INT;\n    case IonBinary.TB_FLOAT:\n      return IonTypes.FLOAT;\n    case IonBinary.TB_DECIMAL:\n      return IonTypes.DECIMAL;\n    case IonBinary.TB_TIMESTAMP:\n      return IonTypes.TIMESTAMP;\n    case IonBinary.TB_SYMBOL:\n      return IonTypes.SYMBOL;\n    case IonBinary.TB_STRING:\n      return IonTypes.STRING;\n    case IonBinary.TB_CLOB:\n      return IonTypes.CLOB;\n    case IonBinary.TB_BLOB:\n      return IonTypes.BLOB;\n    case IonBinary.TB_SEXP:\n      return IonTypes.SEXP;\n    case IonBinary.TB_LIST:\n      return IonTypes.LIST;\n    case IonBinary.TB_STRUCT:\n      return IonTypes.STRUCT;\n    default:\n      throw new Error(\"Unrecognized type code \" + rt);\n  }\n}\nconst VINT_SHIFT = 7;\nconst VINT_MASK = 0x7f;\nconst VINT_FLAG = 0x80;\nfunction high_nibble(tb) {\n  return tb >> IonBinary.TYPE_SHIFT & IonBinary.NIBBLE_MASK;\n}\nfunction low_nibble(tb) {\n  return tb & IonBinary.NIBBLE_MASK;\n}\nconst empty_array = [];\nconst ivm_sid = IVM.sid;\nconst ivm_image_0 = IVM.binary[0];\nconst ivm_image_1 = IVM.binary[1];\nconst ivm_image_2 = IVM.binary[2];\nconst ivm_image_3 = IVM.binary[3];\nclass EncodingContainer {\n  constructor(type, length) {\n    this.type = type;\n    this.length = length;\n  }\n}\nexport class ParserBinaryRaw {\n  constructor(source) {\n    this._raw_type = EOF;\n    this._len = -1;\n    this._curr = undefined;\n    this._null = false;\n    this._fid = null;\n    this._as = -1;\n    this._ae = -1;\n    this._a = [];\n    this._ts = [new EncodingContainer(TB_DATAGRAM, 0)];\n    this._in_struct = false;\n    this._in = source;\n  }\n  static _readFloatFrom(input, numberOfBytes) {\n    let tempBuf;\n    switch (numberOfBytes) {\n      case 0:\n        return 0.0;\n      case 4:\n        tempBuf = new DataView(input.chunk(4).buffer);\n        return tempBuf.getFloat32(0, false);\n      case 8:\n        tempBuf = new DataView(input.chunk(8).buffer);\n        return tempBuf.getFloat64(0, false);\n      case 15:\n        return null;\n      default:\n        throw new Error(\"Illegal float length: \" + numberOfBytes);\n    }\n  }\n  static _readVarUnsignedIntFrom(input) {\n    let numberOfBits = 0;\n    let byte;\n    let magnitude = 0;\n    while (true) {\n      byte = input.next();\n      magnitude = magnitude << 7 | byte & 0x7f;\n      numberOfBits += 7;\n      if (byte & 0x80) {\n        break;\n      }\n    }\n    if (numberOfBits > 31) {\n      throw new Error(\"VarUInt values larger than 31 bits must be read using SignAndMagnitudeInt.\");\n    }\n    return magnitude;\n  }\n  static _readVarSignedIntFrom(input) {\n    let v = input.next(),\n      byte;\n    const isNegative = v & 0x40;\n    let stopBit = v & 0x80;\n    v &= 0x3f;\n    let bits = 6;\n    while (!stopBit) {\n      byte = input.next();\n      stopBit = byte & 0x80;\n      byte &= 0x7f;\n      v <<= 7;\n      v |= byte;\n      bits += 7;\n    }\n    if (bits > 32) {\n      throw new Error(\"VarInt values larger than 32 bits must be read using SignAndMagnitudeInt\");\n    }\n    return isNegative ? -v : v;\n  }\n  static _readSignedIntFrom(input, numberOfBytes) {\n    if (numberOfBytes == 0) {\n      return new SignAndMagnitudeInt(0n);\n    }\n    const bytes = input.view(numberOfBytes);\n    const isNegative = (bytes[0] & 0x80) == 0x80;\n    const numbers = Array.prototype.slice.call(bytes);\n    numbers[0] = bytes[0] & 0x7f;\n    const magnitude = BigIntSerde.fromUnsignedBytes(numbers);\n    return new SignAndMagnitudeInt(magnitude, isNegative);\n  }\n  static _readUnsignedIntAsBigIntFrom(input, numberOfBytes) {\n    return BigIntSerde.fromUnsignedBytes(Array.prototype.slice.call(input.view(numberOfBytes)));\n  }\n  static _readUnsignedIntAsNumberFrom(input, numberOfBytes) {\n    let value = 0;\n    let bytesRead = 0;\n    const bytesAvailable = input.getRemaining();\n    let byte;\n    if (numberOfBytes < 1) {\n      return 0;\n    } else if (numberOfBytes > 6) {\n      throw new Error(`Attempted to read a ${numberOfBytes}-byte unsigned integer,` + ` which is too large for a to be stored in a number without losing precision.`);\n    }\n    if (bytesAvailable < numberOfBytes) {\n      throw new Error(`Attempted to read a ${numberOfBytes}-byte unsigned integer,` + ` but only ${bytesAvailable} bytes were available.`);\n    }\n    while (bytesRead < numberOfBytes) {\n      byte = input.next();\n      bytesRead++;\n      if (numberOfBytes < 4) {\n        value <<= 8;\n      } else {\n        value *= 256;\n      }\n      value = value + byte;\n    }\n    return value;\n  }\n  static readDecimalValueFrom(input, numberOfBytes) {\n    const initialPosition = input.position();\n    const exponent = ParserBinaryRaw._readVarSignedIntFrom(input);\n    const numberOfExponentBytes = input.position() - initialPosition;\n    const numberOfCoefficientBytes = numberOfBytes - numberOfExponentBytes;\n    const signedInt = ParserBinaryRaw._readSignedIntFrom(input, numberOfCoefficientBytes);\n    const isNegative = signedInt.isNegative;\n    const coefficient = isNegative ? -signedInt.magnitude : signedInt.magnitude;\n    return Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);\n  }\n  source() {\n    return this._in;\n  }\n  next() {\n    if (this._curr === undefined && this._len > 0) {\n      this._in.skip(this._len);\n    }\n    this.clear_value();\n    if (this._in_struct) {\n      this._fid = this.readVarUnsignedInt();\n    }\n    return this.load_next();\n  }\n  stepIn() {\n    let len, ts;\n    const t = this;\n    switch (t._raw_type) {\n      case IonBinary.TB_STRUCT:\n      case IonBinary.TB_LIST:\n      case IonBinary.TB_SEXP:\n        break;\n      default:\n        throw new Error(\"you can only 'stepIn' to a container\");\n    }\n    len = t._in.getRemaining() - t._len;\n    ts = new EncodingContainer(t._raw_type, len);\n    t._ts.push(ts);\n    t._in_struct = t._raw_type === IonBinary.TB_STRUCT;\n    t._in.setRemaining(t._len);\n    t.clear_value();\n  }\n  stepOut() {\n    let parent_type, ts, l, r;\n    const t = this;\n    if (t._ts.length < 2) {\n      throw new Error(\"Cannot stepOut any further, already at top level\");\n    }\n    ts = t._ts.pop();\n    l = ts.length;\n    parent_type = t._ts[t._ts.length - 1].type;\n    t._in_struct = parent_type === IonBinary.TB_STRUCT;\n    t.clear_value();\n    r = t._in.getRemaining();\n    t._in.skip(r);\n    t._in.setRemaining(l);\n  }\n  isNull() {\n    return this._null;\n  }\n  depth() {\n    return this._ts.length - 1;\n  }\n  getFieldId() {\n    return this._fid;\n  }\n  hasAnnotations() {\n    return this._as >= 0;\n  }\n  getAnnotations() {\n    const t = this;\n    if (t._a === undefined || t._a.length === 0) {\n      t.load_annotation_values();\n    }\n    return t._a;\n  }\n  getAnnotation(index) {\n    const t = this;\n    if (t._a === undefined || t._a.length === 0) {\n      t.load_annotation_values();\n    }\n    return t._a[index];\n  }\n  ionType() {\n    return get_ion_type(this._raw_type);\n  }\n  _getSid() {\n    this.load_value();\n    if (this._raw_type == IonBinary.TB_SYMBOL) {\n      return this._curr === undefined || this._curr === null ? null : this._curr;\n    }\n    return null;\n  }\n  byteValue() {\n    return this.uInt8ArrayValue();\n  }\n  uInt8ArrayValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_CLOB:\n      case IonBinary.TB_BLOB:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        return this._curr;\n      default:\n        throw new Error(\"Current value is not a blob or clob.\");\n    }\n  }\n  booleanValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_BOOL:\n        if (this.isNull()) {\n          return null;\n        }\n        return this._curr;\n    }\n    throw new Error(\"Current value is not a Boolean.\");\n  }\n  decimalValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_DECIMAL:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        return this._curr;\n    }\n    throw new Error(\"Current value is not a decimal.\");\n  }\n  bigIntValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_INT:\n      case IonBinary.TB_NEG_INT:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        if (!(typeof this._curr === \"bigint\")) {\n          const num = this._curr;\n          return BigInt(num);\n        }\n        return this._curr;\n      default:\n        throw new Error(\"bigIntValue() was called when the current value was not an int.\");\n    }\n  }\n  numberValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_INT:\n      case IonBinary.TB_NEG_INT:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        if (typeof this._curr === \"bigint\") {\n          const bigInt = this._curr;\n          return Number(bigInt);\n        }\n        return this._curr;\n      case IonBinary.TB_FLOAT:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        return this._curr;\n      default:\n        throw new Error(\"Current value is not a float or int.\");\n    }\n  }\n  stringValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_STRING:\n      case IonBinary.TB_SYMBOL:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        return this._curr;\n    }\n    throw new Error(\"Current value is not a string or symbol.\");\n  }\n  timestampValue() {\n    switch (this._raw_type) {\n      case IonBinary.TB_NULL:\n        return null;\n      case IonBinary.TB_TIMESTAMP:\n        if (this.isNull()) {\n          return null;\n        }\n        this.load_value();\n        return this._curr;\n    }\n    throw new Error(\"Current value is not a timestamp.\");\n  }\n  read_binary_float() {\n    return ParserBinaryRaw._readFloatFrom(this._in, this._len);\n  }\n  readVarUnsignedInt() {\n    return ParserBinaryRaw._readVarUnsignedIntFrom(this._in);\n  }\n  readVarSignedInt() {\n    return ParserBinaryRaw._readVarSignedIntFrom(this._in);\n  }\n  readUnsignedIntAsBigInt() {\n    return ParserBinaryRaw._readUnsignedIntAsBigIntFrom(this._in, this._len);\n  }\n  readUnsignedIntAsNumber() {\n    return ParserBinaryRaw._readUnsignedIntAsNumberFrom(this._in, this._len);\n  }\n  read_decimal_value() {\n    return ParserBinaryRaw.readDecimalValueFrom(this._in, this._len);\n  }\n  read_timestamp_value() {\n    if (!(this._len > 0)) {\n      return null;\n    }\n    let offset;\n    let year;\n    let month = null;\n    let day = null;\n    let hour = null;\n    let minute = null;\n    let secondInt = null;\n    let fractionalSeconds = Decimal.ZERO;\n    let precision = TimestampPrecision.YEAR;\n    const end = this._in.position() + this._len;\n    offset = this.readVarSignedInt();\n    if (this._in.position() < end) {\n      year = this.readVarUnsignedInt();\n    } else {\n      throw new Error(\"Timestamps must include a year.\");\n    }\n    if (this._in.position() < end) {\n      month = this.readVarUnsignedInt();\n      precision = TimestampPrecision.MONTH;\n    }\n    if (this._in.position() < end) {\n      day = this.readVarUnsignedInt();\n      precision = TimestampPrecision.DAY;\n    }\n    if (this._in.position() < end) {\n      hour = this.readVarUnsignedInt();\n      if (this._in.position() >= end) {\n        throw new Error(\"Timestamps with an hour must include a minute.\");\n      } else {\n        minute = this.readVarUnsignedInt();\n      }\n      precision = TimestampPrecision.HOUR_AND_MINUTE;\n    }\n    if (this._in.position() < end) {\n      secondInt = this.readVarUnsignedInt();\n      precision = TimestampPrecision.SECONDS;\n    }\n    if (this._in.position() < end) {\n      const exponent = this.readVarSignedInt();\n      let coefficient = 0n;\n      let isNegative = false;\n      if (this._in.position() < end) {\n        const deserializedSignedInt = ParserBinaryRaw._readSignedIntFrom(this._in, end - this._in.position());\n        isNegative = deserializedSignedInt._isNegative;\n        coefficient = deserializedSignedInt._magnitude;\n      }\n      const dec = Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);\n      const [_, fractionStr] = Timestamp._splitSecondsDecimal(dec);\n      fractionalSeconds = Decimal.parse(secondInt + \".\" + fractionStr);\n    }\n    let msSinceEpoch = Date.UTC(year, month ? month - 1 : 0, day ? day : 1, hour ? hour : 0, minute ? minute : 0, secondInt ? secondInt : 0, 0);\n    msSinceEpoch = Timestamp._adjustMsSinceEpochIfNeeded(year, msSinceEpoch);\n    const date = new Date(msSinceEpoch);\n    return Timestamp._valueOf(date, offset, fractionalSeconds, precision);\n  }\n  read_string_value() {\n    return decodeUtf8(this._in.chunk(this._len));\n  }\n  clear_value() {\n    this._raw_type = EOF;\n    this._curr = undefined;\n    this._a = empty_array;\n    this._as = -1;\n    this._null = false;\n    this._fid = null;\n    this._len = -1;\n  }\n  load_length(tb) {\n    const t = this;\n    t._len = low_nibble(tb);\n    switch (t._len) {\n      case 1:\n        if (high_nibble(tb) === IonBinary.TB_STRUCT) {\n          t._len = this.readVarUnsignedInt();\n        }\n        t._null = false;\n        break;\n      case IonBinary.LEN_VAR:\n        t._null = false;\n        t._len = this.readVarUnsignedInt();\n        break;\n      case IonBinary.LEN_NULL:\n        t._null = true;\n        t._len = 0;\n        break;\n      default:\n        t._null = false;\n        break;\n    }\n  }\n  load_next() {\n    const t = this;\n    let rt, tb;\n    t._as = -1;\n    if (t._in.is_empty()) {\n      t.clear_value();\n      return undefined;\n    }\n    tb = t._in.next();\n    rt = high_nibble(tb);\n    t.load_length(tb);\n    if (rt === IonBinary.TB_ANNOTATION) {\n      if (t._len < 1 && t.depth() === 0) {\n        rt = t.load_ivm();\n      } else {\n        rt = t.load_annotations();\n      }\n    }\n    switch (rt) {\n      case IonBinary.TB_NULL:\n        t._null = true;\n        break;\n      case IonBinary.TB_BOOL:\n        if (t._len === 0 || t._len === 1) {\n          t._curr = t._len === 1;\n          t._len = 0;\n        }\n        break;\n    }\n    t._raw_type = rt;\n    return rt;\n  }\n  load_annotations() {\n    const t = this;\n    let tb, type_, annotation_len;\n    if (t._len < 1 && t.depth() === 0) {\n      type_ = t.load_ivm();\n    } else {\n      annotation_len = this.readVarUnsignedInt();\n      t._as = t._in.position();\n      t._in.skip(annotation_len);\n      t._ae = t._in.position();\n      tb = t._in.next();\n      t.load_length(tb);\n      type_ = high_nibble(tb);\n    }\n    return type_;\n  }\n  load_ivm() {\n    const t = this;\n    const span = t._in;\n    if (span.next() !== ivm_image_1) {\n      throw new Error(\"invalid binary Ion at \" + span.position());\n    }\n    if (span.next() !== ivm_image_2) {\n      throw new Error(\"invalid binary Ion at \" + span.position());\n    }\n    if (span.next() !== ivm_image_3) {\n      throw new Error(\"invalid binary Ion at \" + span.position());\n    }\n    t._curr = ivm_sid;\n    t._len = 0;\n    return IonBinary.TB_SYMBOL;\n  }\n  load_annotation_values() {\n    const t = this;\n    let a, b, pos, limit, arr;\n    if ((pos = t._as) < 0) {\n      return;\n    }\n    arr = [];\n    limit = t._ae;\n    a = 0;\n    while (pos < limit) {\n      b = t._in.valueAt(pos);\n      pos++;\n      a = a << VINT_SHIFT | b & VINT_MASK;\n      if ((b & VINT_FLAG) !== 0) {\n        if (a === 0) {\n          throw new Error(\"Symbol ID zero is unsupported.\");\n        }\n        arr.push(a);\n        a = 0;\n      }\n    }\n    t._a = arr;\n  }\n  _readIntegerMagnitude() {\n    if (this._len === 0) {\n      return 0n;\n    }\n    if (this._len < 6) {\n      return this.readUnsignedIntAsNumber();\n    }\n    return this.readUnsignedIntAsBigInt();\n  }\n  load_value() {\n    if (this._curr != undefined) {\n      return;\n    }\n    if (this.isNull()) {\n      return;\n    }\n    switch (this._raw_type) {\n      case IonBinary.TB_BOOL:\n        break;\n      case IonBinary.TB_INT:\n        this._curr = this._readIntegerMagnitude();\n        break;\n      case IonBinary.TB_NEG_INT:\n        let value = this._readIntegerMagnitude();\n        this._curr = typeof value === \"bigint\" ? -value : -value;\n        break;\n      case IonBinary.TB_FLOAT:\n        this._curr = this.read_binary_float();\n        break;\n      case IonBinary.TB_DECIMAL:\n        if (this._len === 0) {\n          this._curr = Decimal.ZERO;\n        } else {\n          this._curr = this.read_decimal_value();\n        }\n        break;\n      case IonBinary.TB_TIMESTAMP:\n        this._curr = this.read_timestamp_value();\n        break;\n      case IonBinary.TB_SYMBOL:\n        this._curr = this.readUnsignedIntAsNumber();\n        break;\n      case IonBinary.TB_STRING:\n        this._curr = this.read_string_value();\n        break;\n      case IonBinary.TB_CLOB:\n      case IonBinary.TB_BLOB:\n        if (this.isNull()) {\n          break;\n        }\n        this._curr = this._in.chunk(this._len);\n        break;\n      default:\n        throw new Error(\"Unexpected type: \" + this._raw_type);\n    }\n  }\n}", "map": {"version": 3, "names": ["BigIntSerde", "IonBinary", "IVM", "Decimal", "Timestamp", "TimestampPrecision", "IonTypes", "decodeUtf8", "SignAndMagnitudeInt", "EOF", "TB_DATAGRAM", "get_ion_type", "rt", "TB_NULL", "NULL", "TB_BOOL", "BOOL", "TB_INT", "INT", "TB_NEG_INT", "TB_FLOAT", "FLOAT", "TB_DECIMAL", "DECIMAL", "TB_TIMESTAMP", "TIMESTAMP", "TB_SYMBOL", "SYMBOL", "TB_STRING", "STRING", "TB_CLOB", "CLOB", "TB_BLOB", "BLOB", "TB_SEXP", "SEXP", "TB_LIST", "LIST", "TB_STRUCT", "STRUCT", "Error", "VINT_SHIFT", "VINT_MASK", "VINT_FLAG", "high_nibble", "tb", "TYPE_SHIFT", "NIBBLE_MASK", "low_nibble", "empty_array", "ivm_sid", "sid", "ivm_image_0", "binary", "ivm_image_1", "ivm_image_2", "ivm_image_3", "EncodingContainer", "constructor", "type", "length", "ParserBinaryRaw", "source", "_raw_type", "_len", "_curr", "undefined", "_null", "_fid", "_as", "_ae", "_a", "_ts", "_in_struct", "_in", "_readFloatFrom", "input", "numberOfBytes", "tempBuf", "DataView", "chunk", "buffer", "getFloat32", "getFloat64", "_readVarUnsignedIntFrom", "numberOfBits", "byte", "magnitude", "next", "_readVarSignedIntFrom", "v", "isNegative", "stopBit", "bits", "_readSignedIntFrom", "bytes", "view", "numbers", "Array", "prototype", "slice", "call", "fromUnsignedBytes", "_readUnsignedIntAsBigIntFrom", "_readUnsignedIntAsNumberFrom", "value", "bytesRead", "bytesAvailable", "getRemaining", "readDecimalValueFrom", "initialPosition", "position", "exponent", "numberOfExponentBytes", "numberOfCoefficientBytes", "signedInt", "coefficient", "_fromBigIntCoefficient", "skip", "clear_value", "readVarUnsignedInt", "load_next", "stepIn", "len", "ts", "t", "push", "setRemaining", "stepOut", "parent_type", "l", "r", "pop", "isNull", "depth", "getFieldId", "hasAnnotations", "getAnnotations", "load_annotation_values", "getAnnotation", "index", "ionType", "_getSid", "load_value", "byteValue", "uInt8ArrayValue", "booleanValue", "decimalValue", "bigIntValue", "num", "BigInt", "numberValue", "bigInt", "Number", "stringValue", "timestampValue", "read_binary_float", "readVarSignedInt", "readUnsignedIntAsBigInt", "readUnsignedIntAsNumber", "read_decimal_value", "read_timestamp_value", "offset", "year", "month", "day", "hour", "minute", "secondInt", "fractionalSeconds", "ZERO", "precision", "YEAR", "end", "MONTH", "DAY", "HOUR_AND_MINUTE", "SECONDS", "deserializedSignedInt", "_isNegative", "_magnitude", "dec", "_", "fractionStr", "_splitSecondsDecimal", "parse", "msSinceEpoch", "Date", "UTC", "_adjustMsSinceEpochIfNeeded", "date", "_valueOf", "read_string_value", "load_length", "LEN_VAR", "LEN_NULL", "is_empty", "TB_ANNOTATION", "load_ivm", "load_annotations", "type_", "annotation_len", "span", "a", "b", "pos", "limit", "arr", "valueAt", "_readIntegerMagnitude"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonParserBinaryRaw.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { BigIntSerde } from \"./BigIntSerde\";\nimport * as IonBinary from \"./IonBinary\";\nimport { IVM } from \"./IonConstants\";\nimport { Decimal } from \"./IonDecimal\";\nimport { Timestamp, TimestampPrecision } from \"./IonTimestamp\";\nimport { IonTypes } from \"./IonTypes\";\nimport { decodeUtf8 } from \"./IonUnicode\";\nimport SignAndMagnitudeInt from \"./SignAndMagnitudeInt\";\nconst EOF = -1;\nconst TB_DATAGRAM = 20;\nfunction get_ion_type(rt) {\n    switch (rt) {\n        case IonBinary.TB_NULL:\n            return IonTypes.NULL;\n        case IonBinary.TB_BOOL:\n            return IonTypes.BOOL;\n        case IonBinary.TB_INT:\n            return IonTypes.INT;\n        case IonBinary.TB_NEG_INT:\n            return IonTypes.INT;\n        case IonBinary.TB_FLOAT:\n            return IonTypes.FLOAT;\n        case IonBinary.TB_DECIMAL:\n            return IonTypes.DECIMAL;\n        case IonBinary.TB_TIMESTAMP:\n            return IonTypes.TIMESTAMP;\n        case IonBinary.TB_SYMBOL:\n            return IonTypes.SYMBOL;\n        case IonBinary.TB_STRING:\n            return IonTypes.STRING;\n        case IonBinary.TB_CLOB:\n            return IonTypes.CLOB;\n        case IonBinary.TB_BLOB:\n            return IonTypes.BLOB;\n        case IonBinary.TB_SEXP:\n            return IonTypes.SEXP;\n        case IonBinary.TB_LIST:\n            return IonTypes.LIST;\n        case IonBinary.TB_STRUCT:\n            return IonTypes.STRUCT;\n        default:\n            throw new Error(\"Unrecognized type code \" + rt);\n    }\n}\nconst VINT_SHIFT = 7;\nconst VINT_MASK = 0x7f;\nconst VINT_FLAG = 0x80;\nfunction high_nibble(tb) {\n    return (tb >> IonBinary.TYPE_SHIFT) & IonBinary.NIBBLE_MASK;\n}\nfunction low_nibble(tb) {\n    return tb & IonBinary.NIBBLE_MASK;\n}\nconst empty_array = [];\nconst ivm_sid = IVM.sid;\nconst ivm_image_0 = IVM.binary[0];\nconst ivm_image_1 = IVM.binary[1];\nconst ivm_image_2 = IVM.binary[2];\nconst ivm_image_3 = IVM.binary[3];\nclass EncodingContainer {\n    constructor(type, length) {\n        this.type = type;\n        this.length = length;\n    }\n}\nexport class ParserBinaryRaw {\n    constructor(source) {\n        this._raw_type = EOF;\n        this._len = -1;\n        this._curr = undefined;\n        this._null = false;\n        this._fid = null;\n        this._as = -1;\n        this._ae = -1;\n        this._a = [];\n        this._ts = [new EncodingContainer(TB_DATAGRAM, 0)];\n        this._in_struct = false;\n        this._in = source;\n    }\n    static _readFloatFrom(input, numberOfBytes) {\n        let tempBuf;\n        switch (numberOfBytes) {\n            case 0:\n                return 0.0;\n            case 4:\n                tempBuf = new DataView(input.chunk(4).buffer);\n                return tempBuf.getFloat32(0, false);\n            case 8:\n                tempBuf = new DataView(input.chunk(8).buffer);\n                return tempBuf.getFloat64(0, false);\n            case 15:\n                return null;\n            default:\n                throw new Error(\"Illegal float length: \" + numberOfBytes);\n        }\n    }\n    static _readVarUnsignedIntFrom(input) {\n        let numberOfBits = 0;\n        let byte;\n        let magnitude = 0;\n        while (true) {\n            byte = input.next();\n            magnitude = (magnitude << 7) | (byte & 0x7f);\n            numberOfBits += 7;\n            if (byte & 0x80) {\n                break;\n            }\n        }\n        if (numberOfBits > 31) {\n            throw new Error(\"VarUInt values larger than 31 bits must be read using SignAndMagnitudeInt.\");\n        }\n        return magnitude;\n    }\n    static _readVarSignedIntFrom(input) {\n        let v = input.next(), byte;\n        const isNegative = v & 0x40;\n        let stopBit = v & 0x80;\n        v &= 0x3f;\n        let bits = 6;\n        while (!stopBit) {\n            byte = input.next();\n            stopBit = byte & 0x80;\n            byte &= 0x7f;\n            v <<= 7;\n            v |= byte;\n            bits += 7;\n        }\n        if (bits > 32) {\n            throw new Error(\"VarInt values larger than 32 bits must be read using SignAndMagnitudeInt\");\n        }\n        return isNegative ? -v : v;\n    }\n    static _readSignedIntFrom(input, numberOfBytes) {\n        if (numberOfBytes == 0) {\n            return new SignAndMagnitudeInt(0n);\n        }\n        const bytes = input.view(numberOfBytes);\n        const isNegative = (bytes[0] & 0x80) == 0x80;\n        const numbers = Array.prototype.slice.call(bytes);\n        numbers[0] = bytes[0] & 0x7f;\n        const magnitude = BigIntSerde.fromUnsignedBytes(numbers);\n        return new SignAndMagnitudeInt(magnitude, isNegative);\n    }\n    static _readUnsignedIntAsBigIntFrom(input, numberOfBytes) {\n        return BigIntSerde.fromUnsignedBytes(Array.prototype.slice.call(input.view(numberOfBytes)));\n    }\n    static _readUnsignedIntAsNumberFrom(input, numberOfBytes) {\n        let value = 0;\n        let bytesRead = 0;\n        const bytesAvailable = input.getRemaining();\n        let byte;\n        if (numberOfBytes < 1) {\n            return 0;\n        }\n        else if (numberOfBytes > 6) {\n            throw new Error(`Attempted to read a ${numberOfBytes}-byte unsigned integer,` +\n                ` which is too large for a to be stored in a number without losing precision.`);\n        }\n        if (bytesAvailable < numberOfBytes) {\n            throw new Error(`Attempted to read a ${numberOfBytes}-byte unsigned integer,` +\n                ` but only ${bytesAvailable} bytes were available.`);\n        }\n        while (bytesRead < numberOfBytes) {\n            byte = input.next();\n            bytesRead++;\n            if (numberOfBytes < 4) {\n                value <<= 8;\n            }\n            else {\n                value *= 256;\n            }\n            value = value + byte;\n        }\n        return value;\n    }\n    static readDecimalValueFrom(input, numberOfBytes) {\n        const initialPosition = input.position();\n        const exponent = ParserBinaryRaw._readVarSignedIntFrom(input);\n        const numberOfExponentBytes = input.position() - initialPosition;\n        const numberOfCoefficientBytes = numberOfBytes - numberOfExponentBytes;\n        const signedInt = ParserBinaryRaw._readSignedIntFrom(input, numberOfCoefficientBytes);\n        const isNegative = signedInt.isNegative;\n        const coefficient = isNegative ? -signedInt.magnitude : signedInt.magnitude;\n        return Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);\n    }\n    source() {\n        return this._in;\n    }\n    next() {\n        if (this._curr === undefined && this._len > 0) {\n            this._in.skip(this._len);\n        }\n        this.clear_value();\n        if (this._in_struct) {\n            this._fid = this.readVarUnsignedInt();\n        }\n        return this.load_next();\n    }\n    stepIn() {\n        let len, ts;\n        const t = this;\n        switch (t._raw_type) {\n            case IonBinary.TB_STRUCT:\n            case IonBinary.TB_LIST:\n            case IonBinary.TB_SEXP:\n                break;\n            default:\n                throw new Error(\"you can only 'stepIn' to a container\");\n        }\n        len = t._in.getRemaining() - t._len;\n        ts = new EncodingContainer(t._raw_type, len);\n        t._ts.push(ts);\n        t._in_struct = t._raw_type === IonBinary.TB_STRUCT;\n        t._in.setRemaining(t._len);\n        t.clear_value();\n    }\n    stepOut() {\n        let parent_type, ts, l, r;\n        const t = this;\n        if (t._ts.length < 2) {\n            throw new Error(\"Cannot stepOut any further, already at top level\");\n        }\n        ts = t._ts.pop();\n        l = ts.length;\n        parent_type = t._ts[t._ts.length - 1].type;\n        t._in_struct = parent_type === IonBinary.TB_STRUCT;\n        t.clear_value();\n        r = t._in.getRemaining();\n        t._in.skip(r);\n        t._in.setRemaining(l);\n    }\n    isNull() {\n        return this._null;\n    }\n    depth() {\n        return this._ts.length - 1;\n    }\n    getFieldId() {\n        return this._fid;\n    }\n    hasAnnotations() {\n        return this._as >= 0;\n    }\n    getAnnotations() {\n        const t = this;\n        if (t._a === undefined || t._a.length === 0) {\n            t.load_annotation_values();\n        }\n        return t._a;\n    }\n    getAnnotation(index) {\n        const t = this;\n        if (t._a === undefined || t._a.length === 0) {\n            t.load_annotation_values();\n        }\n        return t._a[index];\n    }\n    ionType() {\n        return get_ion_type(this._raw_type);\n    }\n    _getSid() {\n        this.load_value();\n        if (this._raw_type == IonBinary.TB_SYMBOL) {\n            return this._curr === undefined || this._curr === null\n                ? null\n                : this._curr;\n        }\n        return null;\n    }\n    byteValue() {\n        return this.uInt8ArrayValue();\n    }\n    uInt8ArrayValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_CLOB:\n            case IonBinary.TB_BLOB:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                return this._curr;\n            default:\n                throw new Error(\"Current value is not a blob or clob.\");\n        }\n    }\n    booleanValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_BOOL:\n                if (this.isNull()) {\n                    return null;\n                }\n                return this._curr;\n        }\n        throw new Error(\"Current value is not a Boolean.\");\n    }\n    decimalValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_DECIMAL:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                return this._curr;\n        }\n        throw new Error(\"Current value is not a decimal.\");\n    }\n    bigIntValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_INT:\n            case IonBinary.TB_NEG_INT:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                if (!(typeof this._curr === \"bigint\")) {\n                    const num = this._curr;\n                    return BigInt(num);\n                }\n                return this._curr;\n            default:\n                throw new Error(\"bigIntValue() was called when the current value was not an int.\");\n        }\n    }\n    numberValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_INT:\n            case IonBinary.TB_NEG_INT:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                if (typeof this._curr === \"bigint\") {\n                    const bigInt = this._curr;\n                    return Number(bigInt);\n                }\n                return this._curr;\n            case IonBinary.TB_FLOAT:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                return this._curr;\n            default:\n                throw new Error(\"Current value is not a float or int.\");\n        }\n    }\n    stringValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_STRING:\n            case IonBinary.TB_SYMBOL:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                return this._curr;\n        }\n        throw new Error(\"Current value is not a string or symbol.\");\n    }\n    timestampValue() {\n        switch (this._raw_type) {\n            case IonBinary.TB_NULL:\n                return null;\n            case IonBinary.TB_TIMESTAMP:\n                if (this.isNull()) {\n                    return null;\n                }\n                this.load_value();\n                return this._curr;\n        }\n        throw new Error(\"Current value is not a timestamp.\");\n    }\n    read_binary_float() {\n        return ParserBinaryRaw._readFloatFrom(this._in, this._len);\n    }\n    readVarUnsignedInt() {\n        return ParserBinaryRaw._readVarUnsignedIntFrom(this._in);\n    }\n    readVarSignedInt() {\n        return ParserBinaryRaw._readVarSignedIntFrom(this._in);\n    }\n    readUnsignedIntAsBigInt() {\n        return ParserBinaryRaw._readUnsignedIntAsBigIntFrom(this._in, this._len);\n    }\n    readUnsignedIntAsNumber() {\n        return ParserBinaryRaw._readUnsignedIntAsNumberFrom(this._in, this._len);\n    }\n    read_decimal_value() {\n        return ParserBinaryRaw.readDecimalValueFrom(this._in, this._len);\n    }\n    read_timestamp_value() {\n        if (!(this._len > 0)) {\n            return null;\n        }\n        let offset;\n        let year;\n        let month = null;\n        let day = null;\n        let hour = null;\n        let minute = null;\n        let secondInt = null;\n        let fractionalSeconds = Decimal.ZERO;\n        let precision = TimestampPrecision.YEAR;\n        const end = this._in.position() + this._len;\n        offset = this.readVarSignedInt();\n        if (this._in.position() < end) {\n            year = this.readVarUnsignedInt();\n        }\n        else {\n            throw new Error(\"Timestamps must include a year.\");\n        }\n        if (this._in.position() < end) {\n            month = this.readVarUnsignedInt();\n            precision = TimestampPrecision.MONTH;\n        }\n        if (this._in.position() < end) {\n            day = this.readVarUnsignedInt();\n            precision = TimestampPrecision.DAY;\n        }\n        if (this._in.position() < end) {\n            hour = this.readVarUnsignedInt();\n            if (this._in.position() >= end) {\n                throw new Error(\"Timestamps with an hour must include a minute.\");\n            }\n            else {\n                minute = this.readVarUnsignedInt();\n            }\n            precision = TimestampPrecision.HOUR_AND_MINUTE;\n        }\n        if (this._in.position() < end) {\n            secondInt = this.readVarUnsignedInt();\n            precision = TimestampPrecision.SECONDS;\n        }\n        if (this._in.position() < end) {\n            const exponent = this.readVarSignedInt();\n            let coefficient = 0n;\n            let isNegative = false;\n            if (this._in.position() < end) {\n                const deserializedSignedInt = ParserBinaryRaw._readSignedIntFrom(this._in, end - this._in.position());\n                isNegative = deserializedSignedInt._isNegative;\n                coefficient = deserializedSignedInt._magnitude;\n            }\n            const dec = Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);\n            const [_, fractionStr] = Timestamp._splitSecondsDecimal(dec);\n            fractionalSeconds = Decimal.parse(secondInt + \".\" + fractionStr);\n        }\n        let msSinceEpoch = Date.UTC(year, month ? month - 1 : 0, day ? day : 1, hour ? hour : 0, minute ? minute : 0, secondInt ? secondInt : 0, 0);\n        msSinceEpoch = Timestamp._adjustMsSinceEpochIfNeeded(year, msSinceEpoch);\n        const date = new Date(msSinceEpoch);\n        return Timestamp._valueOf(date, offset, fractionalSeconds, precision);\n    }\n    read_string_value() {\n        return decodeUtf8(this._in.chunk(this._len));\n    }\n    clear_value() {\n        this._raw_type = EOF;\n        this._curr = undefined;\n        this._a = empty_array;\n        this._as = -1;\n        this._null = false;\n        this._fid = null;\n        this._len = -1;\n    }\n    load_length(tb) {\n        const t = this;\n        t._len = low_nibble(tb);\n        switch (t._len) {\n            case 1:\n                if (high_nibble(tb) === IonBinary.TB_STRUCT) {\n                    t._len = this.readVarUnsignedInt();\n                }\n                t._null = false;\n                break;\n            case IonBinary.LEN_VAR:\n                t._null = false;\n                t._len = this.readVarUnsignedInt();\n                break;\n            case IonBinary.LEN_NULL:\n                t._null = true;\n                t._len = 0;\n                break;\n            default:\n                t._null = false;\n                break;\n        }\n    }\n    load_next() {\n        const t = this;\n        let rt, tb;\n        t._as = -1;\n        if (t._in.is_empty()) {\n            t.clear_value();\n            return undefined;\n        }\n        tb = t._in.next();\n        rt = high_nibble(tb);\n        t.load_length(tb);\n        if (rt === IonBinary.TB_ANNOTATION) {\n            if (t._len < 1 && t.depth() === 0) {\n                rt = t.load_ivm();\n            }\n            else {\n                rt = t.load_annotations();\n            }\n        }\n        switch (rt) {\n            case IonBinary.TB_NULL:\n                t._null = true;\n                break;\n            case IonBinary.TB_BOOL:\n                if (t._len === 0 || t._len === 1) {\n                    t._curr = t._len === 1;\n                    t._len = 0;\n                }\n                break;\n        }\n        t._raw_type = rt;\n        return rt;\n    }\n    load_annotations() {\n        const t = this;\n        let tb, type_, annotation_len;\n        if (t._len < 1 && t.depth() === 0) {\n            type_ = t.load_ivm();\n        }\n        else {\n            annotation_len = this.readVarUnsignedInt();\n            t._as = t._in.position();\n            t._in.skip(annotation_len);\n            t._ae = t._in.position();\n            tb = t._in.next();\n            t.load_length(tb);\n            type_ = high_nibble(tb);\n        }\n        return type_;\n    }\n    load_ivm() {\n        const t = this;\n        const span = t._in;\n        if (span.next() !== ivm_image_1) {\n            throw new Error(\"invalid binary Ion at \" + span.position());\n        }\n        if (span.next() !== ivm_image_2) {\n            throw new Error(\"invalid binary Ion at \" + span.position());\n        }\n        if (span.next() !== ivm_image_3) {\n            throw new Error(\"invalid binary Ion at \" + span.position());\n        }\n        t._curr = ivm_sid;\n        t._len = 0;\n        return IonBinary.TB_SYMBOL;\n    }\n    load_annotation_values() {\n        const t = this;\n        let a, b, pos, limit, arr;\n        if ((pos = t._as) < 0) {\n            return;\n        }\n        arr = [];\n        limit = t._ae;\n        a = 0;\n        while (pos < limit) {\n            b = t._in.valueAt(pos);\n            pos++;\n            a = (a << VINT_SHIFT) | (b & VINT_MASK);\n            if ((b & VINT_FLAG) !== 0) {\n                if (a === 0) {\n                    throw new Error(\"Symbol ID zero is unsupported.\");\n                }\n                arr.push(a);\n                a = 0;\n            }\n        }\n        t._a = arr;\n    }\n    _readIntegerMagnitude() {\n        if (this._len === 0) {\n            return 0n;\n        }\n        if (this._len < 6) {\n            return this.readUnsignedIntAsNumber();\n        }\n        return this.readUnsignedIntAsBigInt();\n    }\n    load_value() {\n        if (this._curr != undefined) {\n            return;\n        }\n        if (this.isNull()) {\n            return;\n        }\n        switch (this._raw_type) {\n            case IonBinary.TB_BOOL:\n                break;\n            case IonBinary.TB_INT:\n                this._curr = this._readIntegerMagnitude();\n                break;\n            case IonBinary.TB_NEG_INT:\n                let value = this._readIntegerMagnitude();\n                this._curr = typeof value === \"bigint\" ? -value : -value;\n                break;\n            case IonBinary.TB_FLOAT:\n                this._curr = this.read_binary_float();\n                break;\n            case IonBinary.TB_DECIMAL:\n                if (this._len === 0) {\n                    this._curr = Decimal.ZERO;\n                }\n                else {\n                    this._curr = this.read_decimal_value();\n                }\n                break;\n            case IonBinary.TB_TIMESTAMP:\n                this._curr = this.read_timestamp_value();\n                break;\n            case IonBinary.TB_SYMBOL:\n                this._curr = this.readUnsignedIntAsNumber();\n                break;\n            case IonBinary.TB_STRING:\n                this._curr = this.read_string_value();\n                break;\n            case IonBinary.TB_CLOB:\n            case IonBinary.TB_BLOB:\n                if (this.isNull()) {\n                    break;\n                }\n                this._curr = this._in.chunk(this._len);\n                break;\n            default:\n                throw new Error(\"Unexpected type: \" + this._raw_type);\n        }\n    }\n}\n//# sourceMappingURL=IonParserBinaryRaw.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,eAAe;AAC3C,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,EAAEC,kBAAkB,QAAQ,gBAAgB;AAC9D,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,MAAMC,GAAG,GAAG,CAAC,CAAC;AACd,MAAMC,WAAW,GAAG,EAAE;AACtB,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,QAAQA,EAAE;IACN,KAAKX,SAAS,CAACY,OAAO;MAClB,OAAOP,QAAQ,CAACQ,IAAI;IACxB,KAAKb,SAAS,CAACc,OAAO;MAClB,OAAOT,QAAQ,CAACU,IAAI;IACxB,KAAKf,SAAS,CAACgB,MAAM;MACjB,OAAOX,QAAQ,CAACY,GAAG;IACvB,KAAKjB,SAAS,CAACkB,UAAU;MACrB,OAAOb,QAAQ,CAACY,GAAG;IACvB,KAAKjB,SAAS,CAACmB,QAAQ;MACnB,OAAOd,QAAQ,CAACe,KAAK;IACzB,KAAKpB,SAAS,CAACqB,UAAU;MACrB,OAAOhB,QAAQ,CAACiB,OAAO;IAC3B,KAAKtB,SAAS,CAACuB,YAAY;MACvB,OAAOlB,QAAQ,CAACmB,SAAS;IAC7B,KAAKxB,SAAS,CAACyB,SAAS;MACpB,OAAOpB,QAAQ,CAACqB,MAAM;IAC1B,KAAK1B,SAAS,CAAC2B,SAAS;MACpB,OAAOtB,QAAQ,CAACuB,MAAM;IAC1B,KAAK5B,SAAS,CAAC6B,OAAO;MAClB,OAAOxB,QAAQ,CAACyB,IAAI;IACxB,KAAK9B,SAAS,CAAC+B,OAAO;MAClB,OAAO1B,QAAQ,CAAC2B,IAAI;IACxB,KAAKhC,SAAS,CAACiC,OAAO;MAClB,OAAO5B,QAAQ,CAAC6B,IAAI;IACxB,KAAKlC,SAAS,CAACmC,OAAO;MAClB,OAAO9B,QAAQ,CAAC+B,IAAI;IACxB,KAAKpC,SAAS,CAACqC,SAAS;MACpB,OAAOhC,QAAQ,CAACiC,MAAM;IAC1B;MACI,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAG5B,EAAE,CAAC;EACvD;AACJ;AACA,MAAM6B,UAAU,GAAG,CAAC;AACpB,MAAMC,SAAS,GAAG,IAAI;AACtB,MAAMC,SAAS,GAAG,IAAI;AACtB,SAASC,WAAWA,CAACC,EAAE,EAAE;EACrB,OAAQA,EAAE,IAAI5C,SAAS,CAAC6C,UAAU,GAAI7C,SAAS,CAAC8C,WAAW;AAC/D;AACA,SAASC,UAAUA,CAACH,EAAE,EAAE;EACpB,OAAOA,EAAE,GAAG5C,SAAS,CAAC8C,WAAW;AACrC;AACA,MAAME,WAAW,GAAG,EAAE;AACtB,MAAMC,OAAO,GAAGhD,GAAG,CAACiD,GAAG;AACvB,MAAMC,WAAW,GAAGlD,GAAG,CAACmD,MAAM,CAAC,CAAC,CAAC;AACjC,MAAMC,WAAW,GAAGpD,GAAG,CAACmD,MAAM,CAAC,CAAC,CAAC;AACjC,MAAME,WAAW,GAAGrD,GAAG,CAACmD,MAAM,CAAC,CAAC,CAAC;AACjC,MAAMG,WAAW,GAAGtD,GAAG,CAACmD,MAAM,CAAC,CAAC,CAAC;AACjC,MAAMI,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAE;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBH,WAAWA,CAACI,MAAM,EAAE;IAChB,IAAI,CAACC,SAAS,GAAGtD,GAAG;IACpB,IAAI,CAACuD,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,GAAG,GAAG,CAAC,IAAIf,iBAAiB,CAAC/C,WAAW,EAAE,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC+D,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,GAAG,GAAGZ,MAAM;EACrB;EACA,OAAOa,cAAcA,CAACC,KAAK,EAAEC,aAAa,EAAE;IACxC,IAAIC,OAAO;IACX,QAAQD,aAAa;MACjB,KAAK,CAAC;QACF,OAAO,GAAG;MACd,KAAK,CAAC;QACFC,OAAO,GAAG,IAAIC,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QAC7C,OAAOH,OAAO,CAACI,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;MACvC,KAAK,CAAC;QACFJ,OAAO,GAAG,IAAIC,QAAQ,CAACH,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QAC7C,OAAOH,OAAO,CAACK,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;MACvC,KAAK,EAAE;QACH,OAAO,IAAI;MACf;QACI,MAAM,IAAI3C,KAAK,CAAC,wBAAwB,GAAGqC,aAAa,CAAC;IACjE;EACJ;EACA,OAAOO,uBAAuBA,CAACR,KAAK,EAAE;IAClC,IAAIS,YAAY,GAAG,CAAC;IACpB,IAAIC,IAAI;IACR,IAAIC,SAAS,GAAG,CAAC;IACjB,OAAO,IAAI,EAAE;MACTD,IAAI,GAAGV,KAAK,CAACY,IAAI,CAAC,CAAC;MACnBD,SAAS,GAAIA,SAAS,IAAI,CAAC,GAAKD,IAAI,GAAG,IAAK;MAC5CD,YAAY,IAAI,CAAC;MACjB,IAAIC,IAAI,GAAG,IAAI,EAAE;QACb;MACJ;IACJ;IACA,IAAID,YAAY,GAAG,EAAE,EAAE;MACnB,MAAM,IAAI7C,KAAK,CAAC,4EAA4E,CAAC;IACjG;IACA,OAAO+C,SAAS;EACpB;EACA,OAAOE,qBAAqBA,CAACb,KAAK,EAAE;IAChC,IAAIc,CAAC,GAAGd,KAAK,CAACY,IAAI,CAAC,CAAC;MAAEF,IAAI;IAC1B,MAAMK,UAAU,GAAGD,CAAC,GAAG,IAAI;IAC3B,IAAIE,OAAO,GAAGF,CAAC,GAAG,IAAI;IACtBA,CAAC,IAAI,IAAI;IACT,IAAIG,IAAI,GAAG,CAAC;IACZ,OAAO,CAACD,OAAO,EAAE;MACbN,IAAI,GAAGV,KAAK,CAACY,IAAI,CAAC,CAAC;MACnBI,OAAO,GAAGN,IAAI,GAAG,IAAI;MACrBA,IAAI,IAAI,IAAI;MACZI,CAAC,KAAK,CAAC;MACPA,CAAC,IAAIJ,IAAI;MACTO,IAAI,IAAI,CAAC;IACb;IACA,IAAIA,IAAI,GAAG,EAAE,EAAE;MACX,MAAM,IAAIrD,KAAK,CAAC,0EAA0E,CAAC;IAC/F;IACA,OAAOmD,UAAU,GAAG,CAACD,CAAC,GAAGA,CAAC;EAC9B;EACA,OAAOI,kBAAkBA,CAAClB,KAAK,EAAEC,aAAa,EAAE;IAC5C,IAAIA,aAAa,IAAI,CAAC,EAAE;MACpB,OAAO,IAAIrE,mBAAmB,CAAC,EAAE,CAAC;IACtC;IACA,MAAMuF,KAAK,GAAGnB,KAAK,CAACoB,IAAI,CAACnB,aAAa,CAAC;IACvC,MAAMc,UAAU,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI;IAC5C,MAAME,OAAO,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACN,KAAK,CAAC;IACjDE,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5B,MAAMR,SAAS,GAAGvF,WAAW,CAACsG,iBAAiB,CAACL,OAAO,CAAC;IACxD,OAAO,IAAIzF,mBAAmB,CAAC+E,SAAS,EAAEI,UAAU,CAAC;EACzD;EACA,OAAOY,4BAA4BA,CAAC3B,KAAK,EAAEC,aAAa,EAAE;IACtD,OAAO7E,WAAW,CAACsG,iBAAiB,CAACJ,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACzB,KAAK,CAACoB,IAAI,CAACnB,aAAa,CAAC,CAAC,CAAC;EAC/F;EACA,OAAO2B,4BAA4BA,CAAC5B,KAAK,EAAEC,aAAa,EAAE;IACtD,IAAI4B,KAAK,GAAG,CAAC;IACb,IAAIC,SAAS,GAAG,CAAC;IACjB,MAAMC,cAAc,GAAG/B,KAAK,CAACgC,YAAY,CAAC,CAAC;IAC3C,IAAItB,IAAI;IACR,IAAIT,aAAa,GAAG,CAAC,EAAE;MACnB,OAAO,CAAC;IACZ,CAAC,MACI,IAAIA,aAAa,GAAG,CAAC,EAAE;MACxB,MAAM,IAAIrC,KAAK,CAAC,uBAAuBqC,aAAa,yBAAyB,GACzE,8EAA8E,CAAC;IACvF;IACA,IAAI8B,cAAc,GAAG9B,aAAa,EAAE;MAChC,MAAM,IAAIrC,KAAK,CAAC,uBAAuBqC,aAAa,yBAAyB,GACzE,aAAa8B,cAAc,wBAAwB,CAAC;IAC5D;IACA,OAAOD,SAAS,GAAG7B,aAAa,EAAE;MAC9BS,IAAI,GAAGV,KAAK,CAACY,IAAI,CAAC,CAAC;MACnBkB,SAAS,EAAE;MACX,IAAI7B,aAAa,GAAG,CAAC,EAAE;QACnB4B,KAAK,KAAK,CAAC;MACf,CAAC,MACI;QACDA,KAAK,IAAI,GAAG;MAChB;MACAA,KAAK,GAAGA,KAAK,GAAGnB,IAAI;IACxB;IACA,OAAOmB,KAAK;EAChB;EACA,OAAOI,oBAAoBA,CAACjC,KAAK,EAAEC,aAAa,EAAE;IAC9C,MAAMiC,eAAe,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,CAAC;IACxC,MAAMC,QAAQ,GAAGnD,eAAe,CAAC4B,qBAAqB,CAACb,KAAK,CAAC;IAC7D,MAAMqC,qBAAqB,GAAGrC,KAAK,CAACmC,QAAQ,CAAC,CAAC,GAAGD,eAAe;IAChE,MAAMI,wBAAwB,GAAGrC,aAAa,GAAGoC,qBAAqB;IACtE,MAAME,SAAS,GAAGtD,eAAe,CAACiC,kBAAkB,CAAClB,KAAK,EAAEsC,wBAAwB,CAAC;IACrF,MAAMvB,UAAU,GAAGwB,SAAS,CAACxB,UAAU;IACvC,MAAMyB,WAAW,GAAGzB,UAAU,GAAG,CAACwB,SAAS,CAAC5B,SAAS,GAAG4B,SAAS,CAAC5B,SAAS;IAC3E,OAAOpF,OAAO,CAACkH,sBAAsB,CAAC1B,UAAU,EAAEyB,WAAW,EAAEJ,QAAQ,CAAC;EAC5E;EACAlD,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACY,GAAG;EACnB;EACAc,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACvB,KAAK,KAAKC,SAAS,IAAI,IAAI,CAACF,IAAI,GAAG,CAAC,EAAE;MAC3C,IAAI,CAACU,GAAG,CAAC4C,IAAI,CAAC,IAAI,CAACtD,IAAI,CAAC;IAC5B;IACA,IAAI,CAACuD,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC9C,UAAU,EAAE;MACjB,IAAI,CAACL,IAAI,GAAG,IAAI,CAACoD,kBAAkB,CAAC,CAAC;IACzC;IACA,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;EAC3B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAIC,GAAG,EAAEC,EAAE;IACX,MAAMC,CAAC,GAAG,IAAI;IACd,QAAQA,CAAC,CAAC9D,SAAS;MACf,KAAK9D,SAAS,CAACqC,SAAS;MACxB,KAAKrC,SAAS,CAACmC,OAAO;MACtB,KAAKnC,SAAS,CAACiC,OAAO;QAClB;MACJ;QACI,MAAM,IAAIM,KAAK,CAAC,sCAAsC,CAAC;IAC/D;IACAmF,GAAG,GAAGE,CAAC,CAACnD,GAAG,CAACkC,YAAY,CAAC,CAAC,GAAGiB,CAAC,CAAC7D,IAAI;IACnC4D,EAAE,GAAG,IAAInE,iBAAiB,CAACoE,CAAC,CAAC9D,SAAS,EAAE4D,GAAG,CAAC;IAC5CE,CAAC,CAACrD,GAAG,CAACsD,IAAI,CAACF,EAAE,CAAC;IACdC,CAAC,CAACpD,UAAU,GAAGoD,CAAC,CAAC9D,SAAS,KAAK9D,SAAS,CAACqC,SAAS;IAClDuF,CAAC,CAACnD,GAAG,CAACqD,YAAY,CAACF,CAAC,CAAC7D,IAAI,CAAC;IAC1B6D,CAAC,CAACN,WAAW,CAAC,CAAC;EACnB;EACAS,OAAOA,CAAA,EAAG;IACN,IAAIC,WAAW,EAAEL,EAAE,EAAEM,CAAC,EAAEC,CAAC;IACzB,MAAMN,CAAC,GAAG,IAAI;IACd,IAAIA,CAAC,CAACrD,GAAG,CAACZ,MAAM,GAAG,CAAC,EAAE;MAClB,MAAM,IAAIpB,KAAK,CAAC,kDAAkD,CAAC;IACvE;IACAoF,EAAE,GAAGC,CAAC,CAACrD,GAAG,CAAC4D,GAAG,CAAC,CAAC;IAChBF,CAAC,GAAGN,EAAE,CAAChE,MAAM;IACbqE,WAAW,GAAGJ,CAAC,CAACrD,GAAG,CAACqD,CAAC,CAACrD,GAAG,CAACZ,MAAM,GAAG,CAAC,CAAC,CAACD,IAAI;IAC1CkE,CAAC,CAACpD,UAAU,GAAGwD,WAAW,KAAKhI,SAAS,CAACqC,SAAS;IAClDuF,CAAC,CAACN,WAAW,CAAC,CAAC;IACfY,CAAC,GAAGN,CAAC,CAACnD,GAAG,CAACkC,YAAY,CAAC,CAAC;IACxBiB,CAAC,CAACnD,GAAG,CAAC4C,IAAI,CAACa,CAAC,CAAC;IACbN,CAAC,CAACnD,GAAG,CAACqD,YAAY,CAACG,CAAC,CAAC;EACzB;EACAG,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAClE,KAAK;EACrB;EACAmE,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAAC9D,GAAG,CAACZ,MAAM,GAAG,CAAC;EAC9B;EACA2E,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnE,IAAI;EACpB;EACAoE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnE,GAAG,IAAI,CAAC;EACxB;EACAoE,cAAcA,CAAA,EAAG;IACb,MAAMZ,CAAC,GAAG,IAAI;IACd,IAAIA,CAAC,CAACtD,EAAE,KAAKL,SAAS,IAAI2D,CAAC,CAACtD,EAAE,CAACX,MAAM,KAAK,CAAC,EAAE;MACzCiE,CAAC,CAACa,sBAAsB,CAAC,CAAC;IAC9B;IACA,OAAOb,CAAC,CAACtD,EAAE;EACf;EACAoE,aAAaA,CAACC,KAAK,EAAE;IACjB,MAAMf,CAAC,GAAG,IAAI;IACd,IAAIA,CAAC,CAACtD,EAAE,KAAKL,SAAS,IAAI2D,CAAC,CAACtD,EAAE,CAACX,MAAM,KAAK,CAAC,EAAE;MACzCiE,CAAC,CAACa,sBAAsB,CAAC,CAAC;IAC9B;IACA,OAAOb,CAAC,CAACtD,EAAE,CAACqE,KAAK,CAAC;EACtB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAOlI,YAAY,CAAC,IAAI,CAACoD,SAAS,CAAC;EACvC;EACA+E,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,IAAI,CAAChF,SAAS,IAAI9D,SAAS,CAACyB,SAAS,EAAE;MACvC,OAAO,IAAI,CAACuC,KAAK,KAAKC,SAAS,IAAI,IAAI,CAACD,KAAK,KAAK,IAAI,GAChD,IAAI,GACJ,IAAI,CAACA,KAAK;IACpB;IACA,OAAO,IAAI;EACf;EACA+E,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;EACjC;EACAA,eAAeA,CAAA,EAAG;IACd,QAAQ,IAAI,CAAClF,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAAC6B,OAAO;MACtB,KAAK7B,SAAS,CAAC+B,OAAO;QAClB,IAAI,IAAI,CAACqG,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC9E,KAAK;MACrB;QACI,MAAM,IAAIzB,KAAK,CAAC,sCAAsC,CAAC;IAC/D;EACJ;EACA0G,YAAYA,CAAA,EAAG;IACX,QAAQ,IAAI,CAACnF,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAACc,OAAO;QAClB,IAAI,IAAI,CAACsH,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,OAAO,IAAI,CAACpE,KAAK;IACzB;IACA,MAAM,IAAIzB,KAAK,CAAC,iCAAiC,CAAC;EACtD;EACA2G,YAAYA,CAAA,EAAG;IACX,QAAQ,IAAI,CAACpF,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAACqB,UAAU;QACrB,IAAI,IAAI,CAAC+G,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC9E,KAAK;IACzB;IACA,MAAM,IAAIzB,KAAK,CAAC,iCAAiC,CAAC;EACtD;EACA4G,WAAWA,CAAA,EAAG;IACV,QAAQ,IAAI,CAACrF,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAACgB,MAAM;MACrB,KAAKhB,SAAS,CAACkB,UAAU;QACrB,IAAI,IAAI,CAACkH,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,IAAI,EAAE,OAAO,IAAI,CAAC9E,KAAK,KAAK,QAAQ,CAAC,EAAE;UACnC,MAAMoF,GAAG,GAAG,IAAI,CAACpF,KAAK;UACtB,OAAOqF,MAAM,CAACD,GAAG,CAAC;QACtB;QACA,OAAO,IAAI,CAACpF,KAAK;MACrB;QACI,MAAM,IAAIzB,KAAK,CAAC,iEAAiE,CAAC;IAC1F;EACJ;EACA+G,WAAWA,CAAA,EAAG;IACV,QAAQ,IAAI,CAACxF,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAACgB,MAAM;MACrB,KAAKhB,SAAS,CAACkB,UAAU;QACrB,IAAI,IAAI,CAACkH,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,IAAI,OAAO,IAAI,CAAC9E,KAAK,KAAK,QAAQ,EAAE;UAChC,MAAMuF,MAAM,GAAG,IAAI,CAACvF,KAAK;UACzB,OAAOwF,MAAM,CAACD,MAAM,CAAC;QACzB;QACA,OAAO,IAAI,CAACvF,KAAK;MACrB,KAAKhE,SAAS,CAACmB,QAAQ;QACnB,IAAI,IAAI,CAACiH,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC9E,KAAK;MACrB;QACI,MAAM,IAAIzB,KAAK,CAAC,sCAAsC,CAAC;IAC/D;EACJ;EACAkH,WAAWA,CAAA,EAAG;IACV,QAAQ,IAAI,CAAC3F,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAAC2B,SAAS;MACxB,KAAK3B,SAAS,CAACyB,SAAS;QACpB,IAAI,IAAI,CAAC2G,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC9E,KAAK;IACzB;IACA,MAAM,IAAIzB,KAAK,CAAC,0CAA0C,CAAC;EAC/D;EACAmH,cAAcA,CAAA,EAAG;IACb,QAAQ,IAAI,CAAC5F,SAAS;MAClB,KAAK9D,SAAS,CAACY,OAAO;QAClB,OAAO,IAAI;MACf,KAAKZ,SAAS,CAACuB,YAAY;QACvB,IAAI,IAAI,CAAC6G,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,IAAI,CAACU,UAAU,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC9E,KAAK;IACzB;IACA,MAAM,IAAIzB,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACAoH,iBAAiBA,CAAA,EAAG;IAChB,OAAO/F,eAAe,CAACc,cAAc,CAAC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACV,IAAI,CAAC;EAC9D;EACAwD,kBAAkBA,CAAA,EAAG;IACjB,OAAO3D,eAAe,CAACuB,uBAAuB,CAAC,IAAI,CAACV,GAAG,CAAC;EAC5D;EACAmF,gBAAgBA,CAAA,EAAG;IACf,OAAOhG,eAAe,CAAC4B,qBAAqB,CAAC,IAAI,CAACf,GAAG,CAAC;EAC1D;EACAoF,uBAAuBA,CAAA,EAAG;IACtB,OAAOjG,eAAe,CAAC0C,4BAA4B,CAAC,IAAI,CAAC7B,GAAG,EAAE,IAAI,CAACV,IAAI,CAAC;EAC5E;EACA+F,uBAAuBA,CAAA,EAAG;IACtB,OAAOlG,eAAe,CAAC2C,4BAA4B,CAAC,IAAI,CAAC9B,GAAG,EAAE,IAAI,CAACV,IAAI,CAAC;EAC5E;EACAgG,kBAAkBA,CAAA,EAAG;IACjB,OAAOnG,eAAe,CAACgD,oBAAoB,CAAC,IAAI,CAACnC,GAAG,EAAE,IAAI,CAACV,IAAI,CAAC;EACpE;EACAiG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,EAAE,IAAI,CAACjG,IAAI,GAAG,CAAC,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIkG,MAAM;IACV,IAAIC,IAAI;IACR,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,iBAAiB,GAAGtK,OAAO,CAACuK,IAAI;IACpC,IAAIC,SAAS,GAAGtK,kBAAkB,CAACuK,IAAI;IACvC,MAAMC,GAAG,GAAG,IAAI,CAACnG,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC/C,IAAI;IAC3CkG,MAAM,GAAG,IAAI,CAACL,gBAAgB,CAAC,CAAC;IAChC,IAAI,IAAI,CAACnF,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3BV,IAAI,GAAG,IAAI,CAAC3C,kBAAkB,CAAC,CAAC;IACpC,CAAC,MACI;MACD,MAAM,IAAIhF,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAI,IAAI,CAACkC,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3BT,KAAK,GAAG,IAAI,CAAC5C,kBAAkB,CAAC,CAAC;MACjCmD,SAAS,GAAGtK,kBAAkB,CAACyK,KAAK;IACxC;IACA,IAAI,IAAI,CAACpG,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3BR,GAAG,GAAG,IAAI,CAAC7C,kBAAkB,CAAC,CAAC;MAC/BmD,SAAS,GAAGtK,kBAAkB,CAAC0K,GAAG;IACtC;IACA,IAAI,IAAI,CAACrG,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3BP,IAAI,GAAG,IAAI,CAAC9C,kBAAkB,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC9C,GAAG,CAACqC,QAAQ,CAAC,CAAC,IAAI8D,GAAG,EAAE;QAC5B,MAAM,IAAIrI,KAAK,CAAC,gDAAgD,CAAC;MACrE,CAAC,MACI;QACD+H,MAAM,GAAG,IAAI,CAAC/C,kBAAkB,CAAC,CAAC;MACtC;MACAmD,SAAS,GAAGtK,kBAAkB,CAAC2K,eAAe;IAClD;IACA,IAAI,IAAI,CAACtG,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3BL,SAAS,GAAG,IAAI,CAAChD,kBAAkB,CAAC,CAAC;MACrCmD,SAAS,GAAGtK,kBAAkB,CAAC4K,OAAO;IAC1C;IACA,IAAI,IAAI,CAACvG,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;MAC3B,MAAM7D,QAAQ,GAAG,IAAI,CAAC6C,gBAAgB,CAAC,CAAC;MACxC,IAAIzC,WAAW,GAAG,EAAE;MACpB,IAAIzB,UAAU,GAAG,KAAK;MACtB,IAAI,IAAI,CAACjB,GAAG,CAACqC,QAAQ,CAAC,CAAC,GAAG8D,GAAG,EAAE;QAC3B,MAAMK,qBAAqB,GAAGrH,eAAe,CAACiC,kBAAkB,CAAC,IAAI,CAACpB,GAAG,EAAEmG,GAAG,GAAG,IAAI,CAACnG,GAAG,CAACqC,QAAQ,CAAC,CAAC,CAAC;QACrGpB,UAAU,GAAGuF,qBAAqB,CAACC,WAAW;QAC9C/D,WAAW,GAAG8D,qBAAqB,CAACE,UAAU;MAClD;MACA,MAAMC,GAAG,GAAGlL,OAAO,CAACkH,sBAAsB,CAAC1B,UAAU,EAAEyB,WAAW,EAAEJ,QAAQ,CAAC;MAC7E,MAAM,CAACsE,CAAC,EAAEC,WAAW,CAAC,GAAGnL,SAAS,CAACoL,oBAAoB,CAACH,GAAG,CAAC;MAC5DZ,iBAAiB,GAAGtK,OAAO,CAACsL,KAAK,CAACjB,SAAS,GAAG,GAAG,GAAGe,WAAW,CAAC;IACpE;IACA,IAAIG,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACzB,IAAI,EAAEC,KAAK,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGA,GAAG,GAAG,CAAC,EAAEC,IAAI,GAAGA,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAGA,MAAM,GAAG,CAAC,EAAEC,SAAS,GAAGA,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;IAC3IkB,YAAY,GAAGtL,SAAS,CAACyL,2BAA2B,CAAC1B,IAAI,EAAEuB,YAAY,CAAC;IACxE,MAAMI,IAAI,GAAG,IAAIH,IAAI,CAACD,YAAY,CAAC;IACnC,OAAOtL,SAAS,CAAC2L,QAAQ,CAACD,IAAI,EAAE5B,MAAM,EAAEO,iBAAiB,EAAEE,SAAS,CAAC;EACzE;EACAqB,iBAAiBA,CAAA,EAAG;IAChB,OAAOzL,UAAU,CAAC,IAAI,CAACmE,GAAG,CAACM,KAAK,CAAC,IAAI,CAAChB,IAAI,CAAC,CAAC;EAChD;EACAuD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxD,SAAS,GAAGtD,GAAG;IACpB,IAAI,CAACwD,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACK,EAAE,GAAGtB,WAAW;IACrB,IAAI,CAACoB,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAACF,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACJ,IAAI,GAAG,CAAC,CAAC;EAClB;EACAiI,WAAWA,CAACpJ,EAAE,EAAE;IACZ,MAAMgF,CAAC,GAAG,IAAI;IACdA,CAAC,CAAC7D,IAAI,GAAGhB,UAAU,CAACH,EAAE,CAAC;IACvB,QAAQgF,CAAC,CAAC7D,IAAI;MACV,KAAK,CAAC;QACF,IAAIpB,WAAW,CAACC,EAAE,CAAC,KAAK5C,SAAS,CAACqC,SAAS,EAAE;UACzCuF,CAAC,CAAC7D,IAAI,GAAG,IAAI,CAACwD,kBAAkB,CAAC,CAAC;QACtC;QACAK,CAAC,CAAC1D,KAAK,GAAG,KAAK;QACf;MACJ,KAAKlE,SAAS,CAACiM,OAAO;QAClBrE,CAAC,CAAC1D,KAAK,GAAG,KAAK;QACf0D,CAAC,CAAC7D,IAAI,GAAG,IAAI,CAACwD,kBAAkB,CAAC,CAAC;QAClC;MACJ,KAAKvH,SAAS,CAACkM,QAAQ;QACnBtE,CAAC,CAAC1D,KAAK,GAAG,IAAI;QACd0D,CAAC,CAAC7D,IAAI,GAAG,CAAC;QACV;MACJ;QACI6D,CAAC,CAAC1D,KAAK,GAAG,KAAK;QACf;IACR;EACJ;EACAsD,SAASA,CAAA,EAAG;IACR,MAAMI,CAAC,GAAG,IAAI;IACd,IAAIjH,EAAE,EAAEiC,EAAE;IACVgF,CAAC,CAACxD,GAAG,GAAG,CAAC,CAAC;IACV,IAAIwD,CAAC,CAACnD,GAAG,CAAC0H,QAAQ,CAAC,CAAC,EAAE;MAClBvE,CAAC,CAACN,WAAW,CAAC,CAAC;MACf,OAAOrD,SAAS;IACpB;IACArB,EAAE,GAAGgF,CAAC,CAACnD,GAAG,CAACc,IAAI,CAAC,CAAC;IACjB5E,EAAE,GAAGgC,WAAW,CAACC,EAAE,CAAC;IACpBgF,CAAC,CAACoE,WAAW,CAACpJ,EAAE,CAAC;IACjB,IAAIjC,EAAE,KAAKX,SAAS,CAACoM,aAAa,EAAE;MAChC,IAAIxE,CAAC,CAAC7D,IAAI,GAAG,CAAC,IAAI6D,CAAC,CAACS,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;QAC/B1H,EAAE,GAAGiH,CAAC,CAACyE,QAAQ,CAAC,CAAC;MACrB,CAAC,MACI;QACD1L,EAAE,GAAGiH,CAAC,CAAC0E,gBAAgB,CAAC,CAAC;MAC7B;IACJ;IACA,QAAQ3L,EAAE;MACN,KAAKX,SAAS,CAACY,OAAO;QAClBgH,CAAC,CAAC1D,KAAK,GAAG,IAAI;QACd;MACJ,KAAKlE,SAAS,CAACc,OAAO;QAClB,IAAI8G,CAAC,CAAC7D,IAAI,KAAK,CAAC,IAAI6D,CAAC,CAAC7D,IAAI,KAAK,CAAC,EAAE;UAC9B6D,CAAC,CAAC5D,KAAK,GAAG4D,CAAC,CAAC7D,IAAI,KAAK,CAAC;UACtB6D,CAAC,CAAC7D,IAAI,GAAG,CAAC;QACd;QACA;IACR;IACA6D,CAAC,CAAC9D,SAAS,GAAGnD,EAAE;IAChB,OAAOA,EAAE;EACb;EACA2L,gBAAgBA,CAAA,EAAG;IACf,MAAM1E,CAAC,GAAG,IAAI;IACd,IAAIhF,EAAE,EAAE2J,KAAK,EAAEC,cAAc;IAC7B,IAAI5E,CAAC,CAAC7D,IAAI,GAAG,CAAC,IAAI6D,CAAC,CAACS,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;MAC/BkE,KAAK,GAAG3E,CAAC,CAACyE,QAAQ,CAAC,CAAC;IACxB,CAAC,MACI;MACDG,cAAc,GAAG,IAAI,CAACjF,kBAAkB,CAAC,CAAC;MAC1CK,CAAC,CAACxD,GAAG,GAAGwD,CAAC,CAACnD,GAAG,CAACqC,QAAQ,CAAC,CAAC;MACxBc,CAAC,CAACnD,GAAG,CAAC4C,IAAI,CAACmF,cAAc,CAAC;MAC1B5E,CAAC,CAACvD,GAAG,GAAGuD,CAAC,CAACnD,GAAG,CAACqC,QAAQ,CAAC,CAAC;MACxBlE,EAAE,GAAGgF,CAAC,CAACnD,GAAG,CAACc,IAAI,CAAC,CAAC;MACjBqC,CAAC,CAACoE,WAAW,CAACpJ,EAAE,CAAC;MACjB2J,KAAK,GAAG5J,WAAW,CAACC,EAAE,CAAC;IAC3B;IACA,OAAO2J,KAAK;EAChB;EACAF,QAAQA,CAAA,EAAG;IACP,MAAMzE,CAAC,GAAG,IAAI;IACd,MAAM6E,IAAI,GAAG7E,CAAC,CAACnD,GAAG;IAClB,IAAIgI,IAAI,CAAClH,IAAI,CAAC,CAAC,KAAKlC,WAAW,EAAE;MAC7B,MAAM,IAAId,KAAK,CAAC,wBAAwB,GAAGkK,IAAI,CAAC3F,QAAQ,CAAC,CAAC,CAAC;IAC/D;IACA,IAAI2F,IAAI,CAAClH,IAAI,CAAC,CAAC,KAAKjC,WAAW,EAAE;MAC7B,MAAM,IAAIf,KAAK,CAAC,wBAAwB,GAAGkK,IAAI,CAAC3F,QAAQ,CAAC,CAAC,CAAC;IAC/D;IACA,IAAI2F,IAAI,CAAClH,IAAI,CAAC,CAAC,KAAKhC,WAAW,EAAE;MAC7B,MAAM,IAAIhB,KAAK,CAAC,wBAAwB,GAAGkK,IAAI,CAAC3F,QAAQ,CAAC,CAAC,CAAC;IAC/D;IACAc,CAAC,CAAC5D,KAAK,GAAGf,OAAO;IACjB2E,CAAC,CAAC7D,IAAI,GAAG,CAAC;IACV,OAAO/D,SAAS,CAACyB,SAAS;EAC9B;EACAgH,sBAAsBA,CAAA,EAAG;IACrB,MAAMb,CAAC,GAAG,IAAI;IACd,IAAI8E,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAEC,KAAK,EAAEC,GAAG;IACzB,IAAI,CAACF,GAAG,GAAGhF,CAAC,CAACxD,GAAG,IAAI,CAAC,EAAE;MACnB;IACJ;IACA0I,GAAG,GAAG,EAAE;IACRD,KAAK,GAAGjF,CAAC,CAACvD,GAAG;IACbqI,CAAC,GAAG,CAAC;IACL,OAAOE,GAAG,GAAGC,KAAK,EAAE;MAChBF,CAAC,GAAG/E,CAAC,CAACnD,GAAG,CAACsI,OAAO,CAACH,GAAG,CAAC;MACtBA,GAAG,EAAE;MACLF,CAAC,GAAIA,CAAC,IAAIlK,UAAU,GAAKmK,CAAC,GAAGlK,SAAU;MACvC,IAAI,CAACkK,CAAC,GAAGjK,SAAS,MAAM,CAAC,EAAE;QACvB,IAAIgK,CAAC,KAAK,CAAC,EAAE;UACT,MAAM,IAAInK,KAAK,CAAC,gCAAgC,CAAC;QACrD;QACAuK,GAAG,CAACjF,IAAI,CAAC6E,CAAC,CAAC;QACXA,CAAC,GAAG,CAAC;MACT;IACJ;IACA9E,CAAC,CAACtD,EAAE,GAAGwI,GAAG;EACd;EACAE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACjJ,IAAI,KAAK,CAAC,EAAE;MACjB,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,EAAE;MACf,OAAO,IAAI,CAAC+F,uBAAuB,CAAC,CAAC;IACzC;IACA,OAAO,IAAI,CAACD,uBAAuB,CAAC,CAAC;EACzC;EACAf,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAC9E,KAAK,IAAIC,SAAS,EAAE;MACzB;IACJ;IACA,IAAI,IAAI,CAACmE,MAAM,CAAC,CAAC,EAAE;MACf;IACJ;IACA,QAAQ,IAAI,CAACtE,SAAS;MAClB,KAAK9D,SAAS,CAACc,OAAO;QAClB;MACJ,KAAKd,SAAS,CAACgB,MAAM;QACjB,IAAI,CAACgD,KAAK,GAAG,IAAI,CAACgJ,qBAAqB,CAAC,CAAC;QACzC;MACJ,KAAKhN,SAAS,CAACkB,UAAU;QACrB,IAAIsF,KAAK,GAAG,IAAI,CAACwG,qBAAqB,CAAC,CAAC;QACxC,IAAI,CAAChJ,KAAK,GAAG,OAAOwC,KAAK,KAAK,QAAQ,GAAG,CAACA,KAAK,GAAG,CAACA,KAAK;QACxD;MACJ,KAAKxG,SAAS,CAACmB,QAAQ;QACnB,IAAI,CAAC6C,KAAK,GAAG,IAAI,CAAC2F,iBAAiB,CAAC,CAAC;QACrC;MACJ,KAAK3J,SAAS,CAACqB,UAAU;QACrB,IAAI,IAAI,CAAC0C,IAAI,KAAK,CAAC,EAAE;UACjB,IAAI,CAACC,KAAK,GAAG9D,OAAO,CAACuK,IAAI;QAC7B,CAAC,MACI;UACD,IAAI,CAACzG,KAAK,GAAG,IAAI,CAAC+F,kBAAkB,CAAC,CAAC;QAC1C;QACA;MACJ,KAAK/J,SAAS,CAACuB,YAAY;QACvB,IAAI,CAACyC,KAAK,GAAG,IAAI,CAACgG,oBAAoB,CAAC,CAAC;QACxC;MACJ,KAAKhK,SAAS,CAACyB,SAAS;QACpB,IAAI,CAACuC,KAAK,GAAG,IAAI,CAAC8F,uBAAuB,CAAC,CAAC;QAC3C;MACJ,KAAK9J,SAAS,CAAC2B,SAAS;QACpB,IAAI,CAACqC,KAAK,GAAG,IAAI,CAAC+H,iBAAiB,CAAC,CAAC;QACrC;MACJ,KAAK/L,SAAS,CAAC6B,OAAO;MACtB,KAAK7B,SAAS,CAAC+B,OAAO;QAClB,IAAI,IAAI,CAACqG,MAAM,CAAC,CAAC,EAAE;UACf;QACJ;QACA,IAAI,CAACpE,KAAK,GAAG,IAAI,CAACS,GAAG,CAACM,KAAK,CAAC,IAAI,CAAChB,IAAI,CAAC;QACtC;MACJ;QACI,MAAM,IAAIxB,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAACuB,SAAS,CAAC;IAC7D;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}