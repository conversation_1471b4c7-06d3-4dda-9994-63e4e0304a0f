{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Quaternion, Matrix4 } from \"three\";\nimport { CharsetEncoder } from \"../libs/mmdparser.js\";\nclass MMDExporter {\n  constructor() {\n    // Unicode to Shift_JIS table\n    __publicField(this, \"u2sTable\");\n  }\n  /* TODO: implement\n  // mesh -> pmd\n  this.parsePmd = function ( object ) {\n  };\n  */\n  /* TODO: implement\n  // mesh -> pmx\n  this.parsePmx = function ( object ) {\n  };\n  */\n  /* TODO: implement\n  // animation + skeleton -> vmd\n  this.parseVmd = function ( object ) {\n  };\n  */\n  /*\n   * skeleton -> vpd\n   * Returns Shift_JIS encoded Uint8Array. Otherwise return strings.\n   */\n  parseVpd(skin, outputShiftJis, useOriginalBones) {\n    if (skin.isSkinnedMesh !== true) {\n      console.warn(\"THREE.MMDExporter: parseVpd() requires SkinnedMesh instance.\");\n      return null;\n    }\n    function toStringsFromNumber(num) {\n      if (Math.abs(num) < 1e-6) num = 0;\n      let a = num.toString();\n      if (a.indexOf(\".\") === -1) {\n        a += \".\";\n      }\n      a += \"000000\";\n      const index = a.indexOf(\".\");\n      const d = a.slice(0, index);\n      const p = a.slice(index + 1, index + 7);\n      return d + \".\" + p;\n    }\n    function toStringsFromArray(array2) {\n      const a = [];\n      for (let i = 0, il = array2.length; i < il; i++) {\n        a.push(toStringsFromNumber(array2[i]));\n      }\n      return a.join(\",\");\n    }\n    skin.updateMatrixWorld(true);\n    const bones = skin.skeleton.bones;\n    const bones2 = this.getBindBones(skin);\n    const position = new Vector3();\n    const quaternion = new Quaternion();\n    const quaternion2 = new Quaternion();\n    const matrix = new Matrix4();\n    const array = [];\n    array.push(\"Vocaloid Pose Data file\");\n    array.push(\"\");\n    array.push((skin.name !== \"\" ? skin.name.replace(/\\s/g, \"_\") : \"skin\") + \".osm;\");\n    array.push(bones.length + \";\");\n    array.push(\"\");\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i];\n      const bone2 = bones2[i];\n      if (useOriginalBones === true && bone.userData.ik !== void 0 && bone.userData.ik.originalMatrix !== void 0) {\n        matrix.fromArray(bone.userData.ik.originalMatrix);\n      } else {\n        matrix.copy(bone.matrix);\n      }\n      position.setFromMatrixPosition(matrix);\n      quaternion.setFromRotationMatrix(matrix);\n      const pArray = position.sub(bone2.position).toArray();\n      const qArray = quaternion2.copy(bone2.quaternion).conjugate().multiply(quaternion).toArray();\n      pArray[2] = -pArray[2];\n      qArray[0] = -qArray[0];\n      qArray[1] = -qArray[1];\n      array.push(\"Bone\" + i + \"{\" + bone.name);\n      array.push(\"  \" + toStringsFromArray(pArray) + \";\");\n      array.push(\"  \" + toStringsFromArray(qArray) + \";\");\n      array.push(\"}\");\n      array.push(\"\");\n    }\n    array.push(\"\");\n    const lines = array.join(\"\\n\");\n    return outputShiftJis === true ? this.unicodeToShiftjis(lines) : lines;\n  }\n  unicodeToShiftjis(str) {\n    if (this.u2sTable === void 0) {\n      const encoder = new CharsetEncoder();\n      const table = encoder.s2uTable;\n      this.u2sTable = {};\n      const keys = Object.keys(table);\n      for (let i = 0, il = keys.length; i < il; i++) {\n        let key = keys[i];\n        const value = table[key];\n        this.u2sTable[value] = parseInt(key);\n      }\n    }\n    const array = [];\n    for (let i = 0, il = str.length; i < il; i++) {\n      const code = str.charCodeAt(i);\n      const value = this.u2sTable[code];\n      if (value === void 0) {\n        throw \"cannot convert charcode 0x\" + code.toString(16);\n      } else if (value > 255) {\n        array.push(value >> 8 & 255);\n        array.push(value & 255);\n      } else {\n        array.push(value & 255);\n      }\n    }\n    return new Uint8Array(array);\n  }\n  getBindBones(skin) {\n    const poseSkin = skin.clone();\n    poseSkin.pose();\n    return poseSkin.skeleton.bones;\n  }\n}\nexport { MMDExporter };", "map": {"version": 3, "names": ["MMDExporter", "constructor", "__publicField", "parseVpd", "skin", "outputShiftJis", "useOriginalBones", "isSkinnedMesh", "console", "warn", "toStringsFromNumber", "num", "Math", "abs", "a", "toString", "indexOf", "index", "d", "slice", "p", "toStringsFromArray", "array2", "i", "il", "length", "push", "join", "updateMatrixWorld", "bones", "skeleton", "bones2", "getBindBones", "position", "Vector3", "quaternion", "Quaternion", "quaternion2", "matrix", "Matrix4", "array", "name", "replace", "bone", "bone2", "userData", "ik", "originalMatrix", "fromArray", "copy", "setFromMatrixPosition", "setFromRotationMatrix", "p<PERSON><PERSON>y", "sub", "toArray", "qArray", "conjugate", "multiply", "lines", "unicodeToShiftjis", "str", "u2sTable", "encoder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "table", "s2uTable", "keys", "Object", "key", "value", "parseInt", "code", "charCodeAt", "Uint8Array", "pose<PERSON>kin", "clone", "pose"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/exporters/MMDExporter.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'three'\n// @ts-ignore\nimport { CharsetEncoder } from '../libs/mmdparser'\n\n/**\n * Dependencies\n *  - mmd-parser https://github.com/takahirox/mmd-parser\n */\n\nclass MMDExporter {\n  /* TODO: implement\n\t// mesh -> pmd\n\tthis.parsePmd = function ( object ) {\n\t};\n\t*/\n\n  /* TODO: implement\n\t// mesh -> pmx\n\tthis.parsePmx = function ( object ) {\n\t};\n\t*/\n\n  /* TODO: implement\n\t// animation + skeleton -> vmd\n\tthis.parseVmd = function ( object ) {\n\t};\n\t*/\n\n  /*\n   * skeleton -> vpd\n   * Returns Shift_JIS encoded Uint8Array. Otherwise return strings.\n   */\n  public parseVpd(skin: SkinnedMesh, outputShiftJis: boolean, useOriginalBones: boolean): Uint8Array | string | null {\n    if (skin.isSkinnedMesh !== true) {\n      console.warn('THREE.MMDExporter: parseVpd() requires SkinnedMesh instance.')\n      return null\n    }\n\n    function toStringsFromNumber(num: number): string {\n      if (Math.abs(num) < 1e-6) num = 0\n\n      let a = num.toString()\n\n      if (a.indexOf('.') === -1) {\n        a += '.'\n      }\n\n      a += '000000'\n\n      const index = a.indexOf('.')\n\n      const d = a.slice(0, index)\n      const p = a.slice(index + 1, index + 7)\n\n      return d + '.' + p\n    }\n\n    function toStringsFromArray(array: number[]): string {\n      const a = []\n\n      for (let i = 0, il = array.length; i < il; i++) {\n        a.push(toStringsFromNumber(array[i]))\n      }\n\n      return a.join(',')\n    }\n\n    skin.updateMatrixWorld(true)\n\n    const bones = skin.skeleton.bones\n    const bones2 = this.getBindBones(skin)\n\n    const position = new Vector3()\n    const quaternion = new Quaternion()\n    const quaternion2 = new Quaternion()\n    const matrix = new Matrix4()\n\n    const array = []\n    array.push('Vocaloid Pose Data file')\n    array.push('')\n    array.push((skin.name !== '' ? skin.name.replace(/\\s/g, '_') : 'skin') + '.osm;')\n    array.push(bones.length + ';')\n    array.push('')\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i]\n      const bone2 = bones2[i]\n\n      /*\n       * use the bone matrix saved before solving IK.\n       * see CCDIKSolver for the detail.\n       */\n      if (\n        useOriginalBones === true &&\n        bone.userData.ik !== undefined &&\n        bone.userData.ik.originalMatrix !== undefined\n      ) {\n        matrix.fromArray(bone.userData.ik.originalMatrix)\n      } else {\n        matrix.copy(bone.matrix)\n      }\n\n      position.setFromMatrixPosition(matrix)\n      quaternion.setFromRotationMatrix(matrix)\n\n      const pArray = position.sub(bone2.position).toArray()\n      const qArray = quaternion2.copy(bone2.quaternion).conjugate().multiply(quaternion).toArray()\n\n      // right to left\n      pArray[2] = -pArray[2]\n      qArray[0] = -qArray[0]\n      qArray[1] = -qArray[1]\n\n      array.push('Bone' + i + '{' + bone.name)\n      array.push('  ' + toStringsFromArray(pArray) + ';')\n      array.push('  ' + toStringsFromArray(qArray) + ';')\n      array.push('}')\n      array.push('')\n    }\n\n    array.push('')\n\n    const lines = array.join('\\n')\n\n    return outputShiftJis === true ? this.unicodeToShiftjis(lines) : lines\n  }\n\n  // Unicode to Shift_JIS table\n  private u2sTable: { [key: string]: number | undefined } | undefined\n\n  private unicodeToShiftjis(str: string): Uint8Array {\n    if (this.u2sTable === undefined) {\n      const encoder = new CharsetEncoder()\n      const table = encoder.s2uTable\n      this.u2sTable = {}\n\n      const keys = Object.keys(table)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        let key = keys[i]\n\n        const value = table[key]\n\n        this.u2sTable[value] = parseInt(key)\n      }\n    }\n\n    const array = []\n\n    for (let i = 0, il = str.length; i < il; i++) {\n      const code = str.charCodeAt(i)\n\n      const value = this.u2sTable[code]\n\n      if (value === undefined) {\n        throw 'cannot convert charcode 0x' + code.toString(16)\n      } else if (value > 0xff) {\n        array.push((value >> 8) & 0xff)\n        array.push(value & 0xff)\n      } else {\n        array.push(value & 0xff)\n      }\n    }\n\n    return new Uint8Array(array)\n  }\n\n  private getBindBones(skin: SkinnedMesh): Bone[] {\n    // any more efficient ways?\n    const poseSkin = skin.clone()\n    poseSkin.pose()\n    return poseSkin.skeleton.bones\n  }\n}\n\nexport { MMDExporter }\n"], "mappings": ";;;;;;;;;;;;;AASA,MAAMA,WAAA,CAAY;EAAlBC,YAAA;IAuHU;IAAAC,aAAA;EAAA;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;AAAA;EAhGDC,SAASC,IAAA,EAAmBC,cAAA,EAAyBC,gBAAA,EAAuD;IAC7G,IAAAF,IAAA,CAAKG,aAAA,KAAkB,MAAM;MAC/BC,OAAA,CAAQC,IAAA,CAAK,8DAA8D;MACpE;IACT;IAEA,SAASC,oBAAoBC,GAAA,EAAqB;MAC5C,IAAAC,IAAA,CAAKC,GAAA,CAAIF,GAAG,IAAI,MAAYA,GAAA;MAE5B,IAAAG,CAAA,GAAIH,GAAA,CAAII,QAAA;MAEZ,IAAID,CAAA,CAAEE,OAAA,CAAQ,GAAG,MAAM,IAAI;QACpBF,CAAA;MACP;MAEKA,CAAA;MAEC,MAAAG,KAAA,GAAQH,CAAA,CAAEE,OAAA,CAAQ,GAAG;MAE3B,MAAME,CAAA,GAAIJ,CAAA,CAAEK,KAAA,CAAM,GAAGF,KAAK;MAC1B,MAAMG,CAAA,GAAIN,CAAA,CAAEK,KAAA,CAAMF,KAAA,GAAQ,GAAGA,KAAA,GAAQ,CAAC;MAEtC,OAAOC,CAAA,GAAI,MAAME,CAAA;IACnB;IAEA,SAASC,mBAAmBC,MAAA,EAAyB;MACnD,MAAMR,CAAA,GAAI;MAEV,SAASS,CAAA,GAAI,GAAGC,EAAA,GAAKF,MAAA,CAAMG,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9CT,CAAA,CAAEY,IAAA,CAAKhB,mBAAA,CAAoBY,MAAA,CAAMC,CAAC,CAAC,CAAC;MACtC;MAEO,OAAAT,CAAA,CAAEa,IAAA,CAAK,GAAG;IACnB;IAEAvB,IAAA,CAAKwB,iBAAA,CAAkB,IAAI;IAErB,MAAAC,KAAA,GAAQzB,IAAA,CAAK0B,QAAA,CAASD,KAAA;IACtB,MAAAE,MAAA,GAAS,KAAKC,YAAA,CAAa5B,IAAI;IAE/B,MAAA6B,QAAA,GAAW,IAAIC,OAAA;IACf,MAAAC,UAAA,GAAa,IAAIC,UAAA;IACjB,MAAAC,WAAA,GAAc,IAAID,UAAA;IAClB,MAAAE,MAAA,GAAS,IAAIC,OAAA;IAEnB,MAAMC,KAAA,GAAQ;IACdA,KAAA,CAAMd,IAAA,CAAK,yBAAyB;IACpCc,KAAA,CAAMd,IAAA,CAAK,EAAE;IACPc,KAAA,CAAAd,IAAA,EAAMtB,IAAA,CAAKqC,IAAA,KAAS,KAAKrC,IAAA,CAAKqC,IAAA,CAAKC,OAAA,CAAQ,OAAO,GAAG,IAAI,UAAU,OAAO;IAC1EF,KAAA,CAAAd,IAAA,CAAKG,KAAA,CAAMJ,MAAA,GAAS,GAAG;IAC7Be,KAAA,CAAMd,IAAA,CAAK,EAAE;IAEb,SAASH,CAAA,GAAI,GAAGC,EAAA,GAAKK,KAAA,CAAMJ,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACxC,MAAAoB,IAAA,GAAOd,KAAA,CAAMN,CAAC;MACd,MAAAqB,KAAA,GAAQb,MAAA,CAAOR,CAAC;MAOpB,IAAAjB,gBAAA,KAAqB,QACrBqC,IAAA,CAAKE,QAAA,CAASC,EAAA,KAAO,UACrBH,IAAA,CAAKE,QAAA,CAASC,EAAA,CAAGC,cAAA,KAAmB,QACpC;QACAT,MAAA,CAAOU,SAAA,CAAUL,IAAA,CAAKE,QAAA,CAASC,EAAA,CAAGC,cAAc;MAAA,OAC3C;QACET,MAAA,CAAAW,IAAA,CAAKN,IAAA,CAAKL,MAAM;MACzB;MAEAL,QAAA,CAASiB,qBAAA,CAAsBZ,MAAM;MACrCH,UAAA,CAAWgB,qBAAA,CAAsBb,MAAM;MAEvC,MAAMc,MAAA,GAASnB,QAAA,CAASoB,GAAA,CAAIT,KAAA,CAAMX,QAAQ,EAAEqB,OAAA;MACtC,MAAAC,MAAA,GAASlB,WAAA,CAAYY,IAAA,CAAKL,KAAA,CAAMT,UAAU,EAAEqB,SAAA,GAAYC,QAAA,CAAStB,UAAU,EAAEmB,OAAA,CAAQ;MAG3FF,MAAA,CAAO,CAAC,IAAI,CAACA,MAAA,CAAO,CAAC;MACrBG,MAAA,CAAO,CAAC,IAAI,CAACA,MAAA,CAAO,CAAC;MACrBA,MAAA,CAAO,CAAC,IAAI,CAACA,MAAA,CAAO,CAAC;MAErBf,KAAA,CAAMd,IAAA,CAAK,SAASH,CAAA,GAAI,MAAMoB,IAAA,CAAKF,IAAI;MACvCD,KAAA,CAAMd,IAAA,CAAK,OAAOL,kBAAA,CAAmB+B,MAAM,IAAI,GAAG;MAClDZ,KAAA,CAAMd,IAAA,CAAK,OAAOL,kBAAA,CAAmBkC,MAAM,IAAI,GAAG;MAClDf,KAAA,CAAMd,IAAA,CAAK,GAAG;MACdc,KAAA,CAAMd,IAAA,CAAK,EAAE;IACf;IAEAc,KAAA,CAAMd,IAAA,CAAK,EAAE;IAEP,MAAAgC,KAAA,GAAQlB,KAAA,CAAMb,IAAA,CAAK,IAAI;IAE7B,OAAOtB,cAAA,KAAmB,OAAO,KAAKsD,iBAAA,CAAkBD,KAAK,IAAIA,KAAA;EACnE;EAKQC,kBAAkBC,GAAA,EAAyB;IAC7C,SAAKC,QAAA,KAAa,QAAW;MACzB,MAAAC,OAAA,GAAU,IAAIC,cAAA;MACpB,MAAMC,KAAA,GAAQF,OAAA,CAAQG,QAAA;MACtB,KAAKJ,QAAA,GAAW;MAEV,MAAAK,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKF,KAAK;MAE9B,SAASzC,CAAA,GAAI,GAAGC,EAAA,GAAK0C,IAAA,CAAKzC,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACzC,IAAA6C,GAAA,GAAMF,IAAA,CAAK3C,CAAC;QAEV,MAAA8C,KAAA,GAAQL,KAAA,CAAMI,GAAG;QAEvB,KAAKP,QAAA,CAASQ,KAAK,IAAIC,QAAA,CAASF,GAAG;MACrC;IACF;IAEA,MAAM5B,KAAA,GAAQ;IAEd,SAASjB,CAAA,GAAI,GAAGC,EAAA,GAAKoC,GAAA,CAAInC,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACtC,MAAAgD,IAAA,GAAOX,GAAA,CAAIY,UAAA,CAAWjD,CAAC;MAEvB,MAAA8C,KAAA,GAAQ,KAAKR,QAAA,CAASU,IAAI;MAEhC,IAAIF,KAAA,KAAU,QAAW;QACjB,qCAA+BE,IAAA,CAAKxD,QAAA,CAAS,EAAE;MAAA,WAC5CsD,KAAA,GAAQ,KAAM;QACjB7B,KAAA,CAAAd,IAAA,CAAM2C,KAAA,IAAS,IAAK,GAAI;QACxB7B,KAAA,CAAAd,IAAA,CAAK2C,KAAA,GAAQ,GAAI;MAAA,OAClB;QACC7B,KAAA,CAAAd,IAAA,CAAK2C,KAAA,GAAQ,GAAI;MACzB;IACF;IAEO,WAAII,UAAA,CAAWjC,KAAK;EAC7B;EAEQR,aAAa5B,IAAA,EAA2B;IAExC,MAAAsE,QAAA,GAAWtE,IAAA,CAAKuE,KAAA;IACtBD,QAAA,CAASE,IAAA,CAAK;IACd,OAAOF,QAAA,CAAS5C,QAAA,CAASD,KAAA;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}