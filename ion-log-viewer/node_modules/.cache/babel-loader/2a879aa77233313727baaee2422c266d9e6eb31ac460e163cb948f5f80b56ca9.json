{"ast": null, "code": "import { Triangle, Vector3 } from \"three\";\nconst _face = /* @__PURE__ */new Triangle();\nconst _color = /* @__PURE__ */new Vector3();\nclass MeshSurfaceSampler {\n  constructor(mesh) {\n    let geometry = mesh.geometry;\n    if (geometry.index) {\n      console.warn(\"THREE.MeshSurfaceSampler: Converting geometry to non-indexed BufferGeometry.\");\n      geometry = geometry.toNonIndexed();\n    }\n    this.geometry = geometry;\n    this.randomFunction = Math.random;\n    this.positionAttribute = this.geometry.getAttribute(\"position\");\n    this.colorAttribute = this.geometry.getAttribute(\"color\");\n    this.weightAttribute = null;\n    this.distribution = null;\n  }\n  setWeightAttribute(name) {\n    this.weightAttribute = name ? this.geometry.getAttribute(name) : null;\n    return this;\n  }\n  build() {\n    const positionAttribute = this.positionAttribute;\n    const weightAttribute = this.weightAttribute;\n    const faceWeights = new Float32Array(positionAttribute.count / 3);\n    for (let i = 0; i < positionAttribute.count; i += 3) {\n      let faceWeight = 1;\n      if (weightAttribute) {\n        faceWeight = weightAttribute.getX(i) + weightAttribute.getX(i + 1) + weightAttribute.getX(i + 2);\n      }\n      _face.a.fromBufferAttribute(positionAttribute, i);\n      _face.b.fromBufferAttribute(positionAttribute, i + 1);\n      _face.c.fromBufferAttribute(positionAttribute, i + 2);\n      faceWeight *= _face.getArea();\n      faceWeights[i / 3] = faceWeight;\n    }\n    this.distribution = new Float32Array(positionAttribute.count / 3);\n    let cumulativeTotal = 0;\n    for (let i = 0; i < faceWeights.length; i++) {\n      cumulativeTotal += faceWeights[i];\n      this.distribution[i] = cumulativeTotal;\n    }\n    return this;\n  }\n  setRandomGenerator(randomFunction) {\n    this.randomFunction = randomFunction;\n    return this;\n  }\n  sample(targetPosition, targetNormal, targetColor) {\n    const faceIndex = this.sampleFaceIndex();\n    return this.sampleFace(faceIndex, targetPosition, targetNormal, targetColor);\n  }\n  sampleFaceIndex() {\n    const cumulativeTotal = this.distribution[this.distribution.length - 1];\n    return this.binarySearch(this.randomFunction() * cumulativeTotal);\n  }\n  binarySearch(x) {\n    const dist = this.distribution;\n    let start = 0;\n    let end = dist.length - 1;\n    let index = -1;\n    while (start <= end) {\n      const mid = Math.ceil((start + end) / 2);\n      if (mid === 0 || dist[mid - 1] <= x && dist[mid] > x) {\n        index = mid;\n        break;\n      } else if (x < dist[mid]) {\n        end = mid - 1;\n      } else {\n        start = mid + 1;\n      }\n    }\n    return index;\n  }\n  sampleFace(faceIndex, targetPosition, targetNormal, targetColor) {\n    let u = this.randomFunction();\n    let v = this.randomFunction();\n    if (u + v > 1) {\n      u = 1 - u;\n      v = 1 - v;\n    }\n    _face.a.fromBufferAttribute(this.positionAttribute, faceIndex * 3);\n    _face.b.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 1);\n    _face.c.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 2);\n    targetPosition.set(0, 0, 0).addScaledVector(_face.a, u).addScaledVector(_face.b, v).addScaledVector(_face.c, 1 - (u + v));\n    if (targetNormal !== void 0) {\n      _face.getNormal(targetNormal);\n    }\n    if (targetColor !== void 0 && this.colorAttribute !== void 0) {\n      _face.a.fromBufferAttribute(this.colorAttribute, faceIndex * 3);\n      _face.b.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 1);\n      _face.c.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 2);\n      _color.set(0, 0, 0).addScaledVector(_face.a, u).addScaledVector(_face.b, v).addScaledVector(_face.c, 1 - (u + v));\n      targetColor.r = _color.x;\n      targetColor.g = _color.y;\n      targetColor.b = _color.z;\n    }\n    return this;\n  }\n}\nexport { MeshSurfaceSampler };", "map": {"version": 3, "names": ["_face", "Triangle", "_color", "Vector3", "MeshSurfaceSampler", "constructor", "mesh", "geometry", "index", "console", "warn", "toNonIndexed", "randomFunction", "Math", "random", "positionAttribute", "getAttribute", "colorAttribute", "weightAttribute", "distribution", "setWeightAttribute", "name", "build", "faceWeights", "Float32Array", "count", "i", "faceWeight", "getX", "a", "fromBufferAttribute", "b", "c", "getArea", "cumulativeTotal", "length", "setRandomGenerator", "sample", "targetPosition", "targetNormal", "targetColor", "faceIndex", "sampleFaceIndex", "sampleFace", "binarySearch", "x", "dist", "start", "end", "mid", "ceil", "u", "v", "set", "addScaledVector", "getNormal", "r", "g", "y", "z"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/math/MeshSurfaceSampler.js"], "sourcesContent": ["import { Triangle, Vector3 } from 'three'\n\n/**\n * Utility class for sampling weighted random points on the surface of a mesh.\n *\n * Building the sampler is a one-time O(n) operation. Once built, any number of\n * random samples may be selected in O(logn) time. Memory usage is O(n).\n *\n * References:\n * - http://www.joesfer.com/?p=84\n * - https://stackoverflow.com/a/4322940/1314762\n */\n\nconst _face = /* @__PURE__ */ new Triangle()\nconst _color = /* @__PURE__ */ new Vector3()\n\nclass MeshSurfaceSampler {\n  constructor(mesh) {\n    let geometry = mesh.geometry\n\n    if (geometry.index) {\n      console.warn('THREE.MeshSurfaceSampler: Converting geometry to non-indexed BufferGeometry.')\n\n      geometry = geometry.toNonIndexed()\n    }\n\n    this.geometry = geometry\n    this.randomFunction = Math.random\n\n    this.positionAttribute = this.geometry.getAttribute('position')\n    this.colorAttribute = this.geometry.getAttribute('color')\n    this.weightAttribute = null\n\n    this.distribution = null\n  }\n\n  setWeightAttribute(name) {\n    this.weightAttribute = name ? this.geometry.getAttribute(name) : null\n\n    return this\n  }\n\n  build() {\n    const positionAttribute = this.positionAttribute\n    const weightAttribute = this.weightAttribute\n\n    const faceWeights = new Float32Array(positionAttribute.count / 3)\n\n    // Accumulate weights for each mesh face.\n\n    for (let i = 0; i < positionAttribute.count; i += 3) {\n      let faceWeight = 1\n\n      if (weightAttribute) {\n        faceWeight = weightAttribute.getX(i) + weightAttribute.getX(i + 1) + weightAttribute.getX(i + 2)\n      }\n\n      _face.a.fromBufferAttribute(positionAttribute, i)\n      _face.b.fromBufferAttribute(positionAttribute, i + 1)\n      _face.c.fromBufferAttribute(positionAttribute, i + 2)\n      faceWeight *= _face.getArea()\n\n      faceWeights[i / 3] = faceWeight\n    }\n\n    // Store cumulative total face weights in an array, where weight index\n    // corresponds to face index.\n\n    this.distribution = new Float32Array(positionAttribute.count / 3)\n\n    let cumulativeTotal = 0\n\n    for (let i = 0; i < faceWeights.length; i++) {\n      cumulativeTotal += faceWeights[i]\n\n      this.distribution[i] = cumulativeTotal\n    }\n\n    return this\n  }\n\n  setRandomGenerator(randomFunction) {\n    this.randomFunction = randomFunction\n    return this\n  }\n\n  sample(targetPosition, targetNormal, targetColor) {\n    const faceIndex = this.sampleFaceIndex()\n    return this.sampleFace(faceIndex, targetPosition, targetNormal, targetColor)\n  }\n\n  sampleFaceIndex() {\n    const cumulativeTotal = this.distribution[this.distribution.length - 1]\n    return this.binarySearch(this.randomFunction() * cumulativeTotal)\n  }\n\n  binarySearch(x) {\n    const dist = this.distribution\n    let start = 0\n    let end = dist.length - 1\n\n    let index = -1\n\n    while (start <= end) {\n      const mid = Math.ceil((start + end) / 2)\n\n      if (mid === 0 || (dist[mid - 1] <= x && dist[mid] > x)) {\n        index = mid\n\n        break\n      } else if (x < dist[mid]) {\n        end = mid - 1\n      } else {\n        start = mid + 1\n      }\n    }\n\n    return index\n  }\n\n  sampleFace(faceIndex, targetPosition, targetNormal, targetColor) {\n    let u = this.randomFunction()\n    let v = this.randomFunction()\n\n    if (u + v > 1) {\n      u = 1 - u\n      v = 1 - v\n    }\n\n    _face.a.fromBufferAttribute(this.positionAttribute, faceIndex * 3)\n    _face.b.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 1)\n    _face.c.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 2)\n\n    targetPosition\n      .set(0, 0, 0)\n      .addScaledVector(_face.a, u)\n      .addScaledVector(_face.b, v)\n      .addScaledVector(_face.c, 1 - (u + v))\n\n    if (targetNormal !== undefined) {\n      _face.getNormal(targetNormal)\n    }\n\n    if (targetColor !== undefined && this.colorAttribute !== undefined) {\n      _face.a.fromBufferAttribute(this.colorAttribute, faceIndex * 3)\n      _face.b.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 1)\n      _face.c.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 2)\n\n      _color\n        .set(0, 0, 0)\n        .addScaledVector(_face.a, u)\n        .addScaledVector(_face.b, v)\n        .addScaledVector(_face.c, 1 - (u + v))\n\n      targetColor.r = _color.x\n      targetColor.g = _color.y\n      targetColor.b = _color.z\n    }\n\n    return this\n  }\n}\n\nexport { MeshSurfaceSampler }\n"], "mappings": ";AAaA,MAAMA,KAAA,GAAwB,mBAAIC,QAAA,CAAU;AAC5C,MAAMC,MAAA,GAAyB,mBAAIC,OAAA,CAAS;AAE5C,MAAMC,kBAAA,CAAmB;EACvBC,YAAYC,IAAA,EAAM;IAChB,IAAIC,QAAA,GAAWD,IAAA,CAAKC,QAAA;IAEpB,IAAIA,QAAA,CAASC,KAAA,EAAO;MAClBC,OAAA,CAAQC,IAAA,CAAK,8EAA8E;MAE3FH,QAAA,GAAWA,QAAA,CAASI,YAAA,CAAc;IACnC;IAED,KAAKJ,QAAA,GAAWA,QAAA;IAChB,KAAKK,cAAA,GAAiBC,IAAA,CAAKC,MAAA;IAE3B,KAAKC,iBAAA,GAAoB,KAAKR,QAAA,CAASS,YAAA,CAAa,UAAU;IAC9D,KAAKC,cAAA,GAAiB,KAAKV,QAAA,CAASS,YAAA,CAAa,OAAO;IACxD,KAAKE,eAAA,GAAkB;IAEvB,KAAKC,YAAA,GAAe;EACrB;EAEDC,mBAAmBC,IAAA,EAAM;IACvB,KAAKH,eAAA,GAAkBG,IAAA,GAAO,KAAKd,QAAA,CAASS,YAAA,CAAaK,IAAI,IAAI;IAEjE,OAAO;EACR;EAEDC,MAAA,EAAQ;IACN,MAAMP,iBAAA,GAAoB,KAAKA,iBAAA;IAC/B,MAAMG,eAAA,GAAkB,KAAKA,eAAA;IAE7B,MAAMK,WAAA,GAAc,IAAIC,YAAA,CAAaT,iBAAA,CAAkBU,KAAA,GAAQ,CAAC;IAIhE,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIX,iBAAA,CAAkBU,KAAA,EAAOC,CAAA,IAAK,GAAG;MACnD,IAAIC,UAAA,GAAa;MAEjB,IAAIT,eAAA,EAAiB;QACnBS,UAAA,GAAaT,eAAA,CAAgBU,IAAA,CAAKF,CAAC,IAAIR,eAAA,CAAgBU,IAAA,CAAKF,CAAA,GAAI,CAAC,IAAIR,eAAA,CAAgBU,IAAA,CAAKF,CAAA,GAAI,CAAC;MAChG;MAED1B,KAAA,CAAM6B,CAAA,CAAEC,mBAAA,CAAoBf,iBAAA,EAAmBW,CAAC;MAChD1B,KAAA,CAAM+B,CAAA,CAAED,mBAAA,CAAoBf,iBAAA,EAAmBW,CAAA,GAAI,CAAC;MACpD1B,KAAA,CAAMgC,CAAA,CAAEF,mBAAA,CAAoBf,iBAAA,EAAmBW,CAAA,GAAI,CAAC;MACpDC,UAAA,IAAc3B,KAAA,CAAMiC,OAAA,CAAS;MAE7BV,WAAA,CAAYG,CAAA,GAAI,CAAC,IAAIC,UAAA;IACtB;IAKD,KAAKR,YAAA,GAAe,IAAIK,YAAA,CAAaT,iBAAA,CAAkBU,KAAA,GAAQ,CAAC;IAEhE,IAAIS,eAAA,GAAkB;IAEtB,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAIH,WAAA,CAAYY,MAAA,EAAQT,CAAA,IAAK;MAC3CQ,eAAA,IAAmBX,WAAA,CAAYG,CAAC;MAEhC,KAAKP,YAAA,CAAaO,CAAC,IAAIQ,eAAA;IACxB;IAED,OAAO;EACR;EAEDE,mBAAmBxB,cAAA,EAAgB;IACjC,KAAKA,cAAA,GAAiBA,cAAA;IACtB,OAAO;EACR;EAEDyB,OAAOC,cAAA,EAAgBC,YAAA,EAAcC,WAAA,EAAa;IAChD,MAAMC,SAAA,GAAY,KAAKC,eAAA,CAAiB;IACxC,OAAO,KAAKC,UAAA,CAAWF,SAAA,EAAWH,cAAA,EAAgBC,YAAA,EAAcC,WAAW;EAC5E;EAEDE,gBAAA,EAAkB;IAChB,MAAMR,eAAA,GAAkB,KAAKf,YAAA,CAAa,KAAKA,YAAA,CAAagB,MAAA,GAAS,CAAC;IACtE,OAAO,KAAKS,YAAA,CAAa,KAAKhC,cAAA,CAAc,IAAKsB,eAAe;EACjE;EAEDU,aAAaC,CAAA,EAAG;IACd,MAAMC,IAAA,GAAO,KAAK3B,YAAA;IAClB,IAAI4B,KAAA,GAAQ;IACZ,IAAIC,GAAA,GAAMF,IAAA,CAAKX,MAAA,GAAS;IAExB,IAAI3B,KAAA,GAAQ;IAEZ,OAAOuC,KAAA,IAASC,GAAA,EAAK;MACnB,MAAMC,GAAA,GAAMpC,IAAA,CAAKqC,IAAA,EAAMH,KAAA,GAAQC,GAAA,IAAO,CAAC;MAEvC,IAAIC,GAAA,KAAQ,KAAMH,IAAA,CAAKG,GAAA,GAAM,CAAC,KAAKJ,CAAA,IAAKC,IAAA,CAAKG,GAAG,IAAIJ,CAAA,EAAI;QACtDrC,KAAA,GAAQyC,GAAA;QAER;MACD,WAAUJ,CAAA,GAAIC,IAAA,CAAKG,GAAG,GAAG;QACxBD,GAAA,GAAMC,GAAA,GAAM;MACpB,OAAa;QACLF,KAAA,GAAQE,GAAA,GAAM;MACf;IACF;IAED,OAAOzC,KAAA;EACR;EAEDmC,WAAWF,SAAA,EAAWH,cAAA,EAAgBC,YAAA,EAAcC,WAAA,EAAa;IAC/D,IAAIW,CAAA,GAAI,KAAKvC,cAAA,CAAgB;IAC7B,IAAIwC,CAAA,GAAI,KAAKxC,cAAA,CAAgB;IAE7B,IAAIuC,CAAA,GAAIC,CAAA,GAAI,GAAG;MACbD,CAAA,GAAI,IAAIA,CAAA;MACRC,CAAA,GAAI,IAAIA,CAAA;IACT;IAEDpD,KAAA,CAAM6B,CAAA,CAAEC,mBAAA,CAAoB,KAAKf,iBAAA,EAAmB0B,SAAA,GAAY,CAAC;IACjEzC,KAAA,CAAM+B,CAAA,CAAED,mBAAA,CAAoB,KAAKf,iBAAA,EAAmB0B,SAAA,GAAY,IAAI,CAAC;IACrEzC,KAAA,CAAMgC,CAAA,CAAEF,mBAAA,CAAoB,KAAKf,iBAAA,EAAmB0B,SAAA,GAAY,IAAI,CAAC;IAErEH,cAAA,CACGe,GAAA,CAAI,GAAG,GAAG,CAAC,EACXC,eAAA,CAAgBtD,KAAA,CAAM6B,CAAA,EAAGsB,CAAC,EAC1BG,eAAA,CAAgBtD,KAAA,CAAM+B,CAAA,EAAGqB,CAAC,EAC1BE,eAAA,CAAgBtD,KAAA,CAAMgC,CAAA,EAAG,KAAKmB,CAAA,GAAIC,CAAA,CAAE;IAEvC,IAAIb,YAAA,KAAiB,QAAW;MAC9BvC,KAAA,CAAMuD,SAAA,CAAUhB,YAAY;IAC7B;IAED,IAAIC,WAAA,KAAgB,UAAa,KAAKvB,cAAA,KAAmB,QAAW;MAClEjB,KAAA,CAAM6B,CAAA,CAAEC,mBAAA,CAAoB,KAAKb,cAAA,EAAgBwB,SAAA,GAAY,CAAC;MAC9DzC,KAAA,CAAM+B,CAAA,CAAED,mBAAA,CAAoB,KAAKb,cAAA,EAAgBwB,SAAA,GAAY,IAAI,CAAC;MAClEzC,KAAA,CAAMgC,CAAA,CAAEF,mBAAA,CAAoB,KAAKb,cAAA,EAAgBwB,SAAA,GAAY,IAAI,CAAC;MAElEvC,MAAA,CACGmD,GAAA,CAAI,GAAG,GAAG,CAAC,EACXC,eAAA,CAAgBtD,KAAA,CAAM6B,CAAA,EAAGsB,CAAC,EAC1BG,eAAA,CAAgBtD,KAAA,CAAM+B,CAAA,EAAGqB,CAAC,EAC1BE,eAAA,CAAgBtD,KAAA,CAAMgC,CAAA,EAAG,KAAKmB,CAAA,GAAIC,CAAA,CAAE;MAEvCZ,WAAA,CAAYgB,CAAA,GAAItD,MAAA,CAAO2C,CAAA;MACvBL,WAAA,CAAYiB,CAAA,GAAIvD,MAAA,CAAOwD,CAAA;MACvBlB,WAAA,CAAYT,CAAA,GAAI7B,MAAA,CAAOyD,CAAA;IACxB;IAED,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}