{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, BufferGeometry, Float32BufferAttribute } from \"three\";\nimport { mergeVertices } from \"../utils/BufferGeometryUtils.js\";\nconst cb = /* @__PURE__ */new Vector3();\nconst ab = /* @__PURE__ */new Vector3();\nfunction pushIfUnique(array, object) {\n  if (array.indexOf(object) === -1) array.push(object);\n}\nfunction removeFromArray(array, object) {\n  const k = array.indexOf(object);\n  if (k > -1) array.splice(k, 1);\n}\nclass Vertex {\n  constructor(v, id) {\n    __publicField(this, \"position\");\n    __publicField(this, \"id\");\n    __publicField(this, \"faces\");\n    __publicField(this, \"neighbors\");\n    __publicField(this, \"collapseCost\");\n    __publicField(this, \"collapseNeighbor\");\n    __publicField(this, \"minCost\", 0);\n    __publicField(this, \"totalCost\", 0);\n    __publicField(this, \"costCount\", 0);\n    this.position = v;\n    this.id = id;\n    this.faces = [];\n    this.neighbors = [];\n    this.collapseCost = 0;\n    this.collapseNeighbor = null;\n  }\n  addUniqueNeighbor(vertex) {\n    pushIfUnique(this.neighbors, vertex);\n  }\n  removeIfNonNeighbor(n) {\n    const neighbors = this.neighbors;\n    const faces = this.faces;\n    const offset = neighbors.indexOf(n);\n    if (offset === -1) return;\n    for (let i = 0; i < faces.length; i++) {\n      if (faces[i].hasVertex(n)) return;\n    }\n    neighbors.splice(offset, 1);\n  }\n}\nclass Triangle {\n  constructor(v1, v2, v3, a, b, c) {\n    __publicField(this, \"a\");\n    __publicField(this, \"b\");\n    __publicField(this, \"c\");\n    __publicField(this, \"v1\");\n    __publicField(this, \"v2\");\n    __publicField(this, \"v3\");\n    __publicField(this, \"normal\", new Vector3());\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.v1 = v1;\n    this.v2 = v2;\n    this.v3 = v3;\n    this.computeNormal();\n    v1.faces.push(this);\n    v1.addUniqueNeighbor(v2);\n    v1.addUniqueNeighbor(v3);\n    v2.faces.push(this);\n    v2.addUniqueNeighbor(v1);\n    v2.addUniqueNeighbor(v3);\n    v3.faces.push(this);\n    v3.addUniqueNeighbor(v1);\n    v3.addUniqueNeighbor(v2);\n  }\n  computeNormal() {\n    const vA = this.v1.position;\n    const vB = this.v2.position;\n    const vC = this.v3.position;\n    cb.subVectors(vC, vB);\n    ab.subVectors(vA, vB);\n    cb.cross(ab).normalize();\n    this.normal.copy(cb);\n  }\n  hasVertex(v) {\n    return v === this.v1 || v === this.v2 || v === this.v3;\n  }\n  replaceVertex(oldv, newv) {\n    if (oldv === this.v1) this.v1 = newv;else if (oldv === this.v2) this.v2 = newv;else if (oldv === this.v3) this.v3 = newv;\n    removeFromArray(oldv.faces, this);\n    newv.faces.push(this);\n    oldv.removeIfNonNeighbor(this.v1);\n    this.v1.removeIfNonNeighbor(oldv);\n    oldv.removeIfNonNeighbor(this.v2);\n    this.v2.removeIfNonNeighbor(oldv);\n    oldv.removeIfNonNeighbor(this.v3);\n    this.v3.removeIfNonNeighbor(oldv);\n    this.v1.addUniqueNeighbor(this.v2);\n    this.v1.addUniqueNeighbor(this.v3);\n    this.v2.addUniqueNeighbor(this.v1);\n    this.v2.addUniqueNeighbor(this.v3);\n    this.v3.addUniqueNeighbor(this.v1);\n    this.v3.addUniqueNeighbor(this.v2);\n    this.computeNormal();\n  }\n}\nclass SimplifyModifier {\n  constructor() {\n    __publicField(this, \"computeEdgeCollapseCost\", (u, v) => {\n      const edgelength = v.position.distanceTo(u.position);\n      let curvature = 0;\n      const sideFaces = [];\n      let i,\n        il = u.faces.length,\n        face,\n        sideFace;\n      for (i = 0; i < il; i++) {\n        face = u.faces[i];\n        if (face.hasVertex(v)) {\n          sideFaces.push(face);\n        }\n      }\n      for (i = 0; i < il; i++) {\n        let minCurvature = 1;\n        face = u.faces[i];\n        for (let j = 0; j < sideFaces.length; j++) {\n          sideFace = sideFaces[j];\n          const dotProd = face.normal.dot(sideFace.normal);\n          minCurvature = Math.min(minCurvature, (1.001 - dotProd) / 2);\n        }\n        curvature = Math.max(curvature, minCurvature);\n      }\n      const borders = 0;\n      if (sideFaces.length < 2) {\n        curvature = 1;\n      }\n      const amt = edgelength * curvature + borders;\n      return amt;\n    });\n    __publicField(this, \"computeEdgeCostAtVertex\", v => {\n      if (v.neighbors.length === 0) {\n        v.collapseNeighbor = null;\n        v.collapseCost = -0.01;\n        return;\n      }\n      v.collapseCost = 1e5;\n      v.collapseNeighbor = null;\n      for (let i = 0; i < v.neighbors.length; i++) {\n        const collapseCost = this.computeEdgeCollapseCost(v, v.neighbors[i]);\n        if (!v.collapseNeighbor) {\n          v.collapseNeighbor = v.neighbors[i];\n          v.collapseCost = collapseCost;\n          v.minCost = collapseCost;\n          v.totalCost = 0;\n          v.costCount = 0;\n        }\n        v.costCount++;\n        v.totalCost += collapseCost;\n        if (collapseCost < v.minCost) {\n          v.collapseNeighbor = v.neighbors[i];\n          v.minCost = collapseCost;\n        }\n      }\n      v.collapseCost = v.totalCost / v.costCount;\n    });\n    __publicField(this, \"removeFace\", (f, faces) => {\n      removeFromArray(faces, f);\n      if (f.v1) removeFromArray(f.v1.faces, f);\n      if (f.v2) removeFromArray(f.v2.faces, f);\n      if (f.v3) removeFromArray(f.v3.faces, f);\n      const vs = [f.v1, f.v2, f.v3];\n      let v1, v2;\n      for (let i = 0; i < 3; i++) {\n        v1 = vs[i];\n        v2 = vs[(i + 1) % 3];\n        if (!v1 || !v2) continue;\n        v1.removeIfNonNeighbor(v2);\n        v2.removeIfNonNeighbor(v1);\n      }\n    });\n    __publicField(this, \"collapse\", (vertices, faces, u, v) => {\n      if (!v) {\n        this.removeVertex(u, vertices);\n        return;\n      }\n      let i;\n      const tmpVertices = [];\n      for (i = 0; i < u.neighbors.length; i++) {\n        tmpVertices.push(u.neighbors[i]);\n      }\n      for (i = u.faces.length - 1; i >= 0; i--) {\n        if (u.faces[i].hasVertex(v)) {\n          this.removeFace(u.faces[i], faces);\n        }\n      }\n      for (i = u.faces.length - 1; i >= 0; i--) {\n        u.faces[i].replaceVertex(u, v);\n      }\n      this.removeVertex(u, vertices);\n      for (i = 0; i < tmpVertices.length; i++) {\n        this.computeEdgeCostAtVertex(tmpVertices[i]);\n      }\n    });\n    __publicField(this, \"minimumCostEdge\", vertices => {\n      let least = vertices[0];\n      for (let i = 0; i < vertices.length; i++) {\n        if (vertices[i].collapseCost < least.collapseCost) {\n          least = vertices[i];\n        }\n      }\n      return least;\n    });\n    __publicField(this, \"modify\", (geometry, count) => {\n      geometry = geometry.clone();\n      const attributes = geometry.attributes;\n      for (let name in attributes) {\n        if (name !== \"position\") geometry.deleteAttribute(name);\n      }\n      geometry = mergeVertices(geometry);\n      const vertices = [];\n      const faces = [];\n      const positionAttribute = geometry.getAttribute(\"position\");\n      for (let i = 0; i < positionAttribute.count; i++) {\n        const v = new Vector3().fromBufferAttribute(positionAttribute, i);\n        const vertex = new Vertex(v, i);\n        vertices.push(vertex);\n      }\n      const geomIndex = geometry.getIndex();\n      if (geomIndex !== null) {\n        for (let i = 0; i < geomIndex.count; i += 3) {\n          const a = geomIndex.getX(i);\n          const b = geomIndex.getX(i + 1);\n          const c = geomIndex.getX(i + 2);\n          const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c);\n          faces.push(triangle);\n        }\n      } else {\n        for (let i = 0; i < positionAttribute.count; i += 3) {\n          const a = i;\n          const b = i + 1;\n          const c = i + 2;\n          const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c);\n          faces.push(triangle);\n        }\n      }\n      for (let i = 0, il = vertices.length; i < il; i++) {\n        this.computeEdgeCostAtVertex(vertices[i]);\n      }\n      let nextVertex;\n      let z = count;\n      while (z--) {\n        nextVertex = this.minimumCostEdge(vertices);\n        if (!nextVertex) {\n          console.log(\"THREE.SimplifyModifier: No next vertex\");\n          break;\n        } else {\n          this.collapse(vertices, faces, nextVertex, nextVertex.collapseNeighbor);\n        }\n      }\n      const simplifiedGeometry = new BufferGeometry();\n      const position = [];\n      let index = [];\n      for (let i = 0; i < vertices.length; i++) {\n        const vertex = vertices[i].position;\n        position.push(vertex.x, vertex.y, vertex.z);\n      }\n      for (let i = 0; i < faces.length; i++) {\n        const face = faces[i];\n        const a = vertices.indexOf(face.v1);\n        const b = vertices.indexOf(face.v2);\n        const c = vertices.indexOf(face.v3);\n        index.push(a, b, c);\n      }\n      simplifiedGeometry.setAttribute(\"position\", new Float32BufferAttribute(position, 3));\n      simplifiedGeometry.setIndex(index);\n      return simplifiedGeometry;\n    });\n  }\n  removeVertex(v, vertices) {\n    console.assert(v.faces.length === 0);\n    while (v.neighbors.length) {\n      const n = v.neighbors.pop();\n      removeFromArray(n.neighbors, v);\n    }\n    removeFromArray(vertices, v);\n  }\n}\nexport { SimplifyModifier };", "map": {"version": 3, "names": ["cb", "Vector3", "ab", "pushIfUnique", "array", "object", "indexOf", "push", "removeFromArray", "k", "splice", "Vertex", "constructor", "v", "id", "__publicField", "position", "faces", "neighbors", "collapseCost", "collapseNeighbor", "addUniqueNeighbor", "vertex", "removeIfNonNeighbor", "n", "offset", "i", "length", "hasVertex", "Triangle", "v1", "v2", "v3", "a", "b", "c", "computeNormal", "vA", "vB", "vC", "subVectors", "cross", "normalize", "normal", "copy", "replaceVertex", "oldv", "newv", "SimplifyModifier", "u", "edgelength", "distanceTo", "curvature", "sideFaces", "il", "face", "sideFace", "minCurvature", "j", "dotProd", "dot", "Math", "min", "max", "borders", "amt", "computeEdgeCollapseCost", "minCost", "totalCost", "costCount", "f", "vs", "vertices", "removeVertex", "tmpVertices", "removeFace", "computeEdgeCostAtVertex", "least", "geometry", "count", "clone", "attributes", "name", "deleteAttribute", "mergeVertices", "positionAttribute", "getAttribute", "fromBufferAttribute", "geomIndex", "getIndex", "getX", "triangle", "nextVertex", "z", "minimumCostEdge", "console", "log", "collapse", "simplifiedGeometry", "BufferGeometry", "index", "x", "y", "setAttribute", "Float32BufferAttribute", "setIndex", "assert", "pop"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/modifiers/SimplifyModifier.ts"], "sourcesContent": ["import { <PERSON><PERSON>erGeo<PERSON>, Float32BufferAttribute, Vector3 } from 'three'\nimport * as BufferGeometryUtils from '../utils/BufferGeometryUtils'\n\nconst cb = /* @__PURE__ */ new Vector3()\nconst ab = /* @__PURE__ */ new Vector3()\n\nfunction pushIfUnique<TItem>(array: TItem[], object: TItem): void {\n  if (array.indexOf(object) === -1) array.push(object)\n}\n\nfunction removeFromArray<TItem>(array: TItem[], object: TItem): void {\n  const k = array.indexOf(object)\n  if (k > -1) array.splice(k, 1)\n}\n\nclass Vertex {\n  public position: Vector3\n  private id: number\n\n  public faces: Triangle[]\n  public neighbors: Vertex[]\n\n  public collapseCost: number\n  public collapseNeighbor: null | Vertex\n\n  public minCost: number = 0\n  public totalCost: number = 0\n  public costCount: number = 0\n\n  constructor(v: Vector3, id: number) {\n    this.position = v\n    this.id = id // old index id\n\n    this.faces = [] // faces vertex is connected\n    this.neighbors = [] // neighbouring vertices aka \"adjacentVertices\"\n\n    // these will be computed in computeEdgeCostAtVertex()\n    this.collapseCost = 0 // cost of collapsing this vertex, the less the better. aka objdist\n    this.collapseNeighbor = null // best candinate for collapsing\n  }\n\n  public addUniqueNeighbor(vertex: Vertex): void {\n    pushIfUnique(this.neighbors, vertex)\n  }\n\n  public removeIfNonNeighbor(n: Vertex): void {\n    const neighbors = this.neighbors\n    const faces = this.faces\n\n    const offset = neighbors.indexOf(n)\n    if (offset === -1) return\n    for (let i = 0; i < faces.length; i++) {\n      if (faces[i].hasVertex(n)) return\n    }\n\n    neighbors.splice(offset, 1)\n  }\n}\n\n// we use a triangle class to represent structure of face slightly differently\nclass Triangle {\n  private a: number\n  private b: number\n  private c: Number\n\n  public v1: Vertex\n  public v2: Vertex\n  public v3: Vertex\n\n  public normal = new Vector3()\n\n  constructor(v1: Vertex, v2: Vertex, v3: Vertex, a: number, b: number, c: number) {\n    this.a = a\n    this.b = b\n    this.c = c\n\n    this.v1 = v1\n    this.v2 = v2\n    this.v3 = v3\n\n    this.computeNormal()\n\n    v1.faces.push(this)\n    v1.addUniqueNeighbor(v2)\n    v1.addUniqueNeighbor(v3)\n\n    v2.faces.push(this)\n    v2.addUniqueNeighbor(v1)\n    v2.addUniqueNeighbor(v3)\n\n    v3.faces.push(this)\n    v3.addUniqueNeighbor(v1)\n    v3.addUniqueNeighbor(v2)\n  }\n\n  private computeNormal(): void {\n    const vA = this.v1.position\n    const vB = this.v2.position\n    const vC = this.v3.position\n\n    cb.subVectors(vC, vB)\n    ab.subVectors(vA, vB)\n    cb.cross(ab).normalize()\n\n    this.normal.copy(cb)\n  }\n\n  public hasVertex(v: Vertex): boolean {\n    return v === this.v1 || v === this.v2 || v === this.v3\n  }\n\n  public replaceVertex(oldv: Vertex, newv: Vertex): void {\n    if (oldv === this.v1) this.v1 = newv\n    else if (oldv === this.v2) this.v2 = newv\n    else if (oldv === this.v3) this.v3 = newv\n\n    removeFromArray(oldv.faces, this)\n    newv.faces.push(this)\n\n    oldv.removeIfNonNeighbor(this.v1)\n    this.v1.removeIfNonNeighbor(oldv)\n\n    oldv.removeIfNonNeighbor(this.v2)\n    this.v2.removeIfNonNeighbor(oldv)\n\n    oldv.removeIfNonNeighbor(this.v3)\n    this.v3.removeIfNonNeighbor(oldv)\n\n    this.v1.addUniqueNeighbor(this.v2)\n    this.v1.addUniqueNeighbor(this.v3)\n\n    this.v2.addUniqueNeighbor(this.v1)\n    this.v2.addUniqueNeighbor(this.v3)\n\n    this.v3.addUniqueNeighbor(this.v1)\n    this.v3.addUniqueNeighbor(this.v2)\n\n    this.computeNormal()\n  }\n}\n\n/**\n *\tSimplification Geometry Modifier\n *    - based on code and technique\n *\t  - by Stan Melax in 1998\n *\t  - Progressive Mesh type Polygon Reduction Algorithm\n *    - http://www.melax.com/polychop/\n */\n\nclass SimplifyModifier {\n  constructor() {}\n\n  private computeEdgeCollapseCost = (u: Vertex, v: Vertex): number => {\n    // if we collapse edge uv by moving u to v then how\n    // much different will the model change, i.e. the \"error\".\n\n    const edgelength = v.position.distanceTo(u.position)\n    let curvature = 0\n\n    const sideFaces = []\n    let i,\n      il = u.faces.length,\n      face,\n      sideFace\n\n    // find the \"sides\" triangles that are on the edge uv\n    for (i = 0; i < il; i++) {\n      face = u.faces[i]\n\n      if (face.hasVertex(v)) {\n        sideFaces.push(face)\n      }\n    }\n\n    // use the triangle facing most away from the sides\n    // to determine our curvature term\n    for (i = 0; i < il; i++) {\n      let minCurvature = 1\n      face = u.faces[i]\n\n      for (let j = 0; j < sideFaces.length; j++) {\n        sideFace = sideFaces[j]\n        // use dot product of face normals.\n        const dotProd = face.normal.dot(sideFace.normal)\n        minCurvature = Math.min(minCurvature, (1.001 - dotProd) / 2)\n      }\n\n      curvature = Math.max(curvature, minCurvature)\n    }\n\n    // crude approach in attempt to preserve borders\n    // though it seems not to be totally correct\n    const borders = 0\n    if (sideFaces.length < 2) {\n      // we add some arbitrary cost for borders,\n      // borders += 10;\n      curvature = 1\n    }\n\n    const amt = edgelength * curvature + borders\n\n    return amt\n  }\n\n  private removeVertex(v: Vertex, vertices: Vertex[]): void {\n    console.assert(v.faces.length === 0)\n\n    while (v.neighbors.length) {\n      const n = v.neighbors.pop() as Vertex\n      removeFromArray(n.neighbors, v)\n    }\n\n    removeFromArray(vertices, v)\n  }\n\n  private computeEdgeCostAtVertex = (v: Vertex): void => {\n    // compute the edge collapse cost for all edges that start\n    // from vertex v.  Since we are only interested in reducing\n    // the object by selecting the min cost edge at each step, we\n    // only cache the cost of the least cost edge at this vertex\n    // (in member variable collapse) as well as the value of the\n    // cost (in member variable collapseCost).\n\n    if (v.neighbors.length === 0) {\n      // collapse if no neighbors.\n      v.collapseNeighbor = null\n      v.collapseCost = -0.01\n\n      return\n    }\n\n    v.collapseCost = 100000\n    v.collapseNeighbor = null\n\n    // search all neighboring edges for \"least cost\" edge\n    for (let i = 0; i < v.neighbors.length; i++) {\n      const collapseCost = this.computeEdgeCollapseCost(v, v.neighbors[i])\n\n      if (!v.collapseNeighbor) {\n        v.collapseNeighbor = v.neighbors[i]\n        v.collapseCost = collapseCost\n        v.minCost = collapseCost\n        v.totalCost = 0\n        v.costCount = 0\n      }\n\n      v.costCount++\n      v.totalCost += collapseCost\n\n      if (collapseCost < v.minCost) {\n        v.collapseNeighbor = v.neighbors[i]\n        v.minCost = collapseCost\n      }\n    }\n\n    // we average the cost of collapsing at this vertex\n    v.collapseCost = v.totalCost / v.costCount\n    // v.collapseCost = v.minCost;\n  }\n\n  private removeFace = (f: Triangle, faces: Triangle[]): void => {\n    removeFromArray(faces, f)\n\n    if (f.v1) removeFromArray(f.v1.faces, f)\n    if (f.v2) removeFromArray(f.v2.faces, f)\n    if (f.v3) removeFromArray(f.v3.faces, f)\n\n    // TODO optimize this!\n    const vs = [f.v1, f.v2, f.v3]\n    let v1, v2\n\n    for (let i = 0; i < 3; i++) {\n      v1 = vs[i]\n      v2 = vs[(i + 1) % 3]\n\n      if (!v1 || !v2) continue\n\n      v1.removeIfNonNeighbor(v2)\n      v2.removeIfNonNeighbor(v1)\n    }\n  }\n\n  private collapse = (vertices: Vertex[], faces: Triangle[], u: Vertex, v: Vertex): void => {\n    // u and v are pointers to vertices of an edge\n\n    // Collapse the edge uv by moving vertex u onto v\n\n    if (!v) {\n      // u is a vertex all by itself so just delete it..\n      this.removeVertex(u, vertices)\n      return\n    }\n\n    let i\n    const tmpVertices = []\n\n    for (i = 0; i < u.neighbors.length; i++) {\n      tmpVertices.push(u.neighbors[i])\n    }\n\n    // delete triangles on edge uv:\n    for (i = u.faces.length - 1; i >= 0; i--) {\n      if (u.faces[i].hasVertex(v)) {\n        this.removeFace(u.faces[i], faces)\n      }\n    }\n\n    // update remaining triangles to have v instead of u\n    for (i = u.faces.length - 1; i >= 0; i--) {\n      u.faces[i].replaceVertex(u, v)\n    }\n\n    this.removeVertex(u, vertices)\n\n    // recompute the edge collapse costs in neighborhood\n    for (i = 0; i < tmpVertices.length; i++) {\n      this.computeEdgeCostAtVertex(tmpVertices[i])\n    }\n  }\n\n  private minimumCostEdge = (vertices: Vertex[]): Vertex => {\n    // O(n * n) approach. TODO optimize this\n\n    let least = vertices[0]\n\n    for (let i = 0; i < vertices.length; i++) {\n      if (vertices[i].collapseCost < least.collapseCost) {\n        least = vertices[i]\n      }\n    }\n\n    return least\n  }\n\n  public modify = (geometry: BufferGeometry, count: number): BufferGeometry => {\n    geometry = geometry.clone()\n    const attributes = geometry.attributes\n\n    // this modifier can only process indexed and non-indexed geomtries with a position attribute\n\n    for (let name in attributes) {\n      if (name !== 'position') geometry.deleteAttribute(name)\n    }\n\n    geometry = BufferGeometryUtils.mergeVertices(geometry)\n\n    //\n    // put data of original geometry in different data structures\n    //\n\n    const vertices = []\n    const faces = []\n\n    // add vertices\n\n    const positionAttribute = geometry.getAttribute('position')\n\n    for (let i = 0; i < positionAttribute.count; i++) {\n      const v = new Vector3().fromBufferAttribute(positionAttribute, i)\n\n      const vertex = new Vertex(v, i)\n      vertices.push(vertex)\n    }\n\n    // add faces\n\n    const geomIndex = geometry.getIndex()\n\n    if (geomIndex !== null) {\n      for (let i = 0; i < geomIndex.count; i += 3) {\n        const a = geomIndex.getX(i)\n        const b = geomIndex.getX(i + 1)\n        const c = geomIndex.getX(i + 2)\n\n        const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c)\n        faces.push(triangle)\n      }\n    } else {\n      for (let i = 0; i < positionAttribute.count; i += 3) {\n        const a = i\n        const b = i + 1\n        const c = i + 2\n\n        const triangle = new Triangle(vertices[a], vertices[b], vertices[c], a, b, c)\n        faces.push(triangle)\n      }\n    }\n\n    // compute all edge collapse costs\n\n    for (let i = 0, il = vertices.length; i < il; i++) {\n      this.computeEdgeCostAtVertex(vertices[i])\n    }\n\n    let nextVertex\n\n    let z = count\n\n    while (z--) {\n      nextVertex = this.minimumCostEdge(vertices)\n\n      if (!nextVertex) {\n        console.log('THREE.SimplifyModifier: No next vertex')\n        break\n      } else {\n        this.collapse(vertices, faces, nextVertex, nextVertex.collapseNeighbor as Vertex)\n      }\n    }\n\n    //\n\n    const simplifiedGeometry = new BufferGeometry()\n    const position = []\n    let index = []\n\n    //\n\n    for (let i = 0; i < vertices.length; i++) {\n      const vertex = vertices[i].position\n      position.push(vertex.x, vertex.y, vertex.z)\n    }\n\n    //\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      const a = vertices.indexOf(face.v1)\n      const b = vertices.indexOf(face.v2)\n      const c = vertices.indexOf(face.v3)\n\n      index.push(a, b, c)\n    }\n\n    //\n\n    simplifiedGeometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n    simplifiedGeometry.setIndex(index)\n\n    return simplifiedGeometry\n  }\n}\n\nexport { SimplifyModifier }\n"], "mappings": ";;;;;;;;;;;;;AAGA,MAAMA,EAAA,sBAAyBC,OAAA;AAC/B,MAAMC,EAAA,sBAAyBD,OAAA;AAE/B,SAASE,aAAoBC,KAAA,EAAgBC,MAAA,EAAqB;EAC5D,IAAAD,KAAA,CAAME,OAAA,CAAQD,MAAM,MAAM,IAAID,KAAA,CAAMG,IAAA,CAAKF,MAAM;AACrD;AAEA,SAASG,gBAAuBJ,KAAA,EAAgBC,MAAA,EAAqB;EAC7D,MAAAI,CAAA,GAAIL,KAAA,CAAME,OAAA,CAAQD,MAAM;EAC9B,IAAII,CAAA,GAAI,IAAUL,KAAA,CAAAM,MAAA,CAAOD,CAAA,EAAG,CAAC;AAC/B;AAEA,MAAME,MAAA,CAAO;EAcXC,YAAYC,CAAA,EAAYC,EAAA,EAAY;IAb7BC,aAAA;IACCA,aAAA;IAEDA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA,kBAAkB;IAClBA,aAAA,oBAAoB;IACpBA,aAAA,oBAAoB;IAGzB,KAAKC,QAAA,GAAWH,CAAA;IAChB,KAAKC,EAAA,GAAKA,EAAA;IAEV,KAAKG,KAAA,GAAQ;IACb,KAAKC,SAAA,GAAY;IAGjB,KAAKC,YAAA,GAAe;IACpB,KAAKC,gBAAA,GAAmB;EAC1B;EAEOC,kBAAkBC,MAAA,EAAsB;IAChCnB,YAAA,MAAKe,SAAA,EAAWI,MAAM;EACrC;EAEOC,oBAAoBC,CAAA,EAAiB;IAC1C,MAAMN,SAAA,GAAY,KAAKA,SAAA;IACvB,MAAMD,KAAA,GAAQ,KAAKA,KAAA;IAEb,MAAAQ,MAAA,GAASP,SAAA,CAAUZ,OAAA,CAAQkB,CAAC;IAClC,IAAIC,MAAA,KAAW,IAAI;IACnB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIT,KAAA,CAAMU,MAAA,EAAQD,CAAA,IAAK;MACrC,IAAIT,KAAA,CAAMS,CAAC,EAAEE,SAAA,CAAUJ,CAAC,GAAG;IAC7B;IAEUN,SAAA,CAAAR,MAAA,CAAOe,MAAA,EAAQ,CAAC;EAC5B;AACF;AAGA,MAAMI,QAAA,CAAS;EAWbjB,YAAYkB,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAW;IAVzEpB,aAAA;IACAA,aAAA;IACAA,aAAA;IAEDA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA,iBAAS,IAAId,OAAA;IAGlB,KAAKgC,CAAA,GAAIA,CAAA;IACT,KAAKC,CAAA,GAAIA,CAAA;IACT,KAAKC,CAAA,GAAIA,CAAA;IAET,KAAKL,EAAA,GAAKA,EAAA;IACV,KAAKC,EAAA,GAAKA,EAAA;IACV,KAAKC,EAAA,GAAKA,EAAA;IAEV,KAAKI,aAAA,CAAc;IAEhBN,EAAA,CAAAb,KAAA,CAAMV,IAAA,CAAK,IAAI;IAClBuB,EAAA,CAAGT,iBAAA,CAAkBU,EAAE;IACvBD,EAAA,CAAGT,iBAAA,CAAkBW,EAAE;IAEpBD,EAAA,CAAAd,KAAA,CAAMV,IAAA,CAAK,IAAI;IAClBwB,EAAA,CAAGV,iBAAA,CAAkBS,EAAE;IACvBC,EAAA,CAAGV,iBAAA,CAAkBW,EAAE;IAEpBA,EAAA,CAAAf,KAAA,CAAMV,IAAA,CAAK,IAAI;IAClByB,EAAA,CAAGX,iBAAA,CAAkBS,EAAE;IACvBE,EAAA,CAAGX,iBAAA,CAAkBU,EAAE;EACzB;EAEQK,cAAA,EAAsB;IACtB,MAAAC,EAAA,GAAK,KAAKP,EAAA,CAAGd,QAAA;IACb,MAAAsB,EAAA,GAAK,KAAKP,EAAA,CAAGf,QAAA;IACb,MAAAuB,EAAA,GAAK,KAAKP,EAAA,CAAGhB,QAAA;IAEhBhB,EAAA,CAAAwC,UAAA,CAAWD,EAAA,EAAID,EAAE;IACjBpC,EAAA,CAAAsC,UAAA,CAAWH,EAAA,EAAIC,EAAE;IACjBtC,EAAA,CAAAyC,KAAA,CAAMvC,EAAE,EAAEwC,SAAA,CAAU;IAElB,KAAAC,MAAA,CAAOC,IAAA,CAAK5C,EAAE;EACrB;EAEO4B,UAAUf,CAAA,EAAoB;IACnC,OAAOA,CAAA,KAAM,KAAKiB,EAAA,IAAMjB,CAAA,KAAM,KAAKkB,EAAA,IAAMlB,CAAA,KAAM,KAAKmB,EAAA;EACtD;EAEOa,cAAcC,IAAA,EAAcC,IAAA,EAAoB;IACrD,IAAID,IAAA,KAAS,KAAKhB,EAAA,EAAI,KAAKA,EAAA,GAAKiB,IAAA,UACvBD,IAAA,KAAS,KAAKf,EAAA,EAAI,KAAKA,EAAA,GAAKgB,IAAA,UAC5BD,IAAA,KAAS,KAAKd,EAAA,EAAI,KAAKA,EAAA,GAAKe,IAAA;IAErBvC,eAAA,CAAAsC,IAAA,CAAK7B,KAAA,EAAO,IAAI;IAC3B8B,IAAA,CAAA9B,KAAA,CAAMV,IAAA,CAAK,IAAI;IAEfuC,IAAA,CAAAvB,mBAAA,CAAoB,KAAKO,EAAE;IAC3B,KAAAA,EAAA,CAAGP,mBAAA,CAAoBuB,IAAI;IAE3BA,IAAA,CAAAvB,mBAAA,CAAoB,KAAKQ,EAAE;IAC3B,KAAAA,EAAA,CAAGR,mBAAA,CAAoBuB,IAAI;IAE3BA,IAAA,CAAAvB,mBAAA,CAAoB,KAAKS,EAAE;IAC3B,KAAAA,EAAA,CAAGT,mBAAA,CAAoBuB,IAAI;IAE3B,KAAAhB,EAAA,CAAGT,iBAAA,CAAkB,KAAKU,EAAE;IAC5B,KAAAD,EAAA,CAAGT,iBAAA,CAAkB,KAAKW,EAAE;IAE5B,KAAAD,EAAA,CAAGV,iBAAA,CAAkB,KAAKS,EAAE;IAC5B,KAAAC,EAAA,CAAGV,iBAAA,CAAkB,KAAKW,EAAE;IAE5B,KAAAA,EAAA,CAAGX,iBAAA,CAAkB,KAAKS,EAAE;IAC5B,KAAAE,EAAA,CAAGX,iBAAA,CAAkB,KAAKU,EAAE;IAEjC,KAAKK,aAAA,CAAc;EACrB;AACF;AAUA,MAAMY,gBAAA,CAAiB;EACrBpC,YAAA,EAAc;IAENG,aAAA,kCAA0B,CAACkC,CAAA,EAAWpC,CAAA,KAAsB;MAIlE,MAAMqC,UAAA,GAAarC,CAAA,CAAEG,QAAA,CAASmC,UAAA,CAAWF,CAAA,CAAEjC,QAAQ;MACnD,IAAIoC,SAAA,GAAY;MAEhB,MAAMC,SAAA,GAAY;MAClB,IAAI3B,CAAA;QACF4B,EAAA,GAAKL,CAAA,CAAEhC,KAAA,CAAMU,MAAA;QACb4B,IAAA;QACAC,QAAA;MAGF,KAAK9B,CAAA,GAAI,GAAGA,CAAA,GAAI4B,EAAA,EAAI5B,CAAA,IAAK;QAChB6B,IAAA,GAAAN,CAAA,CAAEhC,KAAA,CAAMS,CAAC;QAEZ,IAAA6B,IAAA,CAAK3B,SAAA,CAAUf,CAAC,GAAG;UACrBwC,SAAA,CAAU9C,IAAA,CAAKgD,IAAI;QACrB;MACF;MAIA,KAAK7B,CAAA,GAAI,GAAGA,CAAA,GAAI4B,EAAA,EAAI5B,CAAA,IAAK;QACvB,IAAI+B,YAAA,GAAe;QACZF,IAAA,GAAAN,CAAA,CAAEhC,KAAA,CAAMS,CAAC;QAEhB,SAASgC,CAAA,GAAI,GAAGA,CAAA,GAAIL,SAAA,CAAU1B,MAAA,EAAQ+B,CAAA,IAAK;UACzCF,QAAA,GAAWH,SAAA,CAAUK,CAAC;UAEtB,MAAMC,OAAA,GAAUJ,IAAA,CAAKZ,MAAA,CAAOiB,GAAA,CAAIJ,QAAA,CAASb,MAAM;UAC/Cc,YAAA,GAAeI,IAAA,CAAKC,GAAA,CAAIL,YAAA,GAAe,QAAQE,OAAA,IAAW,CAAC;QAC7D;QAEYP,SAAA,GAAAS,IAAA,CAAKE,GAAA,CAAIX,SAAA,EAAWK,YAAY;MAC9C;MAIA,MAAMO,OAAA,GAAU;MACZ,IAAAX,SAAA,CAAU1B,MAAA,GAAS,GAAG;QAGZyB,SAAA;MACd;MAEM,MAAAa,GAAA,GAAMf,UAAA,GAAaE,SAAA,GAAYY,OAAA;MAE9B,OAAAC,GAAA;IAAA;IAcDlD,aAAA,kCAA2BF,CAAA,IAAoB;MAQjD,IAAAA,CAAA,CAAEK,SAAA,CAAUS,MAAA,KAAW,GAAG;QAE5Bd,CAAA,CAAEO,gBAAA,GAAmB;QACrBP,CAAA,CAAEM,YAAA,GAAe;QAEjB;MACF;MAEAN,CAAA,CAAEM,YAAA,GAAe;MACjBN,CAAA,CAAEO,gBAAA,GAAmB;MAGrB,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAIb,CAAA,CAAEK,SAAA,CAAUS,MAAA,EAAQD,CAAA,IAAK;QAC3C,MAAMP,YAAA,GAAe,KAAK+C,uBAAA,CAAwBrD,CAAA,EAAGA,CAAA,CAAEK,SAAA,CAAUQ,CAAC,CAAC;QAE/D,KAACb,CAAA,CAAEO,gBAAA,EAAkB;UACrBP,CAAA,CAAAO,gBAAA,GAAmBP,CAAA,CAAEK,SAAA,CAAUQ,CAAC;UAClCb,CAAA,CAAEM,YAAA,GAAeA,YAAA;UACjBN,CAAA,CAAEsD,OAAA,GAAUhD,YAAA;UACZN,CAAA,CAAEuD,SAAA,GAAY;UACdvD,CAAA,CAAEwD,SAAA,GAAY;QAChB;QAEExD,CAAA,CAAAwD,SAAA;QACFxD,CAAA,CAAEuD,SAAA,IAAajD,YAAA;QAEX,IAAAA,YAAA,GAAeN,CAAA,CAAEsD,OAAA,EAAS;UAC1BtD,CAAA,CAAAO,gBAAA,GAAmBP,CAAA,CAAEK,SAAA,CAAUQ,CAAC;UAClCb,CAAA,CAAEsD,OAAA,GAAUhD,YAAA;QACd;MACF;MAGEN,CAAA,CAAAM,YAAA,GAAeN,CAAA,CAAEuD,SAAA,GAAYvD,CAAA,CAAEwD,SAAA;IAAA;IAI3BtD,aAAA,qBAAa,CAACuD,CAAA,EAAarD,KAAA,KAA4B;MAC7DT,eAAA,CAAgBS,KAAA,EAAOqD,CAAC;MAExB,IAAIA,CAAA,CAAExC,EAAA,EAAoBtB,eAAA,CAAA8D,CAAA,CAAExC,EAAA,CAAGb,KAAA,EAAOqD,CAAC;MACvC,IAAIA,CAAA,CAAEvC,EAAA,EAAoBvB,eAAA,CAAA8D,CAAA,CAAEvC,EAAA,CAAGd,KAAA,EAAOqD,CAAC;MACvC,IAAIA,CAAA,CAAEtC,EAAA,EAAoBxB,eAAA,CAAA8D,CAAA,CAAEtC,EAAA,CAAGf,KAAA,EAAOqD,CAAC;MAGvC,MAAMC,EAAA,GAAK,CAACD,CAAA,CAAExC,EAAA,EAAIwC,CAAA,CAAEvC,EAAA,EAAIuC,CAAA,CAAEtC,EAAE;MAC5B,IAAIF,EAAA,EAAIC,EAAA;MAER,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BI,EAAA,GAAKyC,EAAA,CAAG7C,CAAC;QACJK,EAAA,GAAAwC,EAAA,EAAI7C,CAAA,GAAI,KAAK,CAAC;QAEf,KAACI,EAAA,IAAM,CAACC,EAAA,EAAI;QAEhBD,EAAA,CAAGP,mBAAA,CAAoBQ,EAAE;QACzBA,EAAA,CAAGR,mBAAA,CAAoBO,EAAE;MAC3B;IAAA;IAGMf,aAAA,mBAAW,CAACyD,QAAA,EAAoBvD,KAAA,EAAmBgC,CAAA,EAAWpC,CAAA,KAAoB;MAKxF,IAAI,CAACA,CAAA,EAAG;QAED,KAAA4D,YAAA,CAAaxB,CAAA,EAAGuB,QAAQ;QAC7B;MACF;MAEI,IAAA9C,CAAA;MACJ,MAAMgD,WAAA,GAAc;MAEpB,KAAKhD,CAAA,GAAI,GAAGA,CAAA,GAAIuB,CAAA,CAAE/B,SAAA,CAAUS,MAAA,EAAQD,CAAA,IAAK;QACvCgD,WAAA,CAAYnE,IAAA,CAAK0C,CAAA,CAAE/B,SAAA,CAAUQ,CAAC,CAAC;MACjC;MAGA,KAAKA,CAAA,GAAIuB,CAAA,CAAEhC,KAAA,CAAMU,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAGA,CAAA,IAAK;QACxC,IAAIuB,CAAA,CAAEhC,KAAA,CAAMS,CAAC,EAAEE,SAAA,CAAUf,CAAC,GAAG;UAC3B,KAAK8D,UAAA,CAAW1B,CAAA,CAAEhC,KAAA,CAAMS,CAAC,GAAGT,KAAK;QACnC;MACF;MAGA,KAAKS,CAAA,GAAIuB,CAAA,CAAEhC,KAAA,CAAMU,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAGA,CAAA,IAAK;QACxCuB,CAAA,CAAEhC,KAAA,CAAMS,CAAC,EAAEmB,aAAA,CAAcI,CAAA,EAAGpC,CAAC;MAC/B;MAEK,KAAA4D,YAAA,CAAaxB,CAAA,EAAGuB,QAAQ;MAG7B,KAAK9C,CAAA,GAAI,GAAGA,CAAA,GAAIgD,WAAA,CAAY/C,MAAA,EAAQD,CAAA,IAAK;QAClC,KAAAkD,uBAAA,CAAwBF,WAAA,CAAYhD,CAAC,CAAC;MAC7C;IAAA;IAGMX,aAAA,0BAAmByD,QAAA,IAA+B;MAGpD,IAAAK,KAAA,GAAQL,QAAA,CAAS,CAAC;MAEtB,SAAS9C,CAAA,GAAI,GAAGA,CAAA,GAAI8C,QAAA,CAAS7C,MAAA,EAAQD,CAAA,IAAK;QACxC,IAAI8C,QAAA,CAAS9C,CAAC,EAAEP,YAAA,GAAe0D,KAAA,CAAM1D,YAAA,EAAc;UACjD0D,KAAA,GAAQL,QAAA,CAAS9C,CAAC;QACpB;MACF;MAEO,OAAAmD,KAAA;IAAA;IAGF9D,aAAA,iBAAS,CAAC+D,QAAA,EAA0BC,KAAA,KAAkC;MAC3ED,QAAA,GAAWA,QAAA,CAASE,KAAA;MACpB,MAAMC,UAAA,GAAaH,QAAA,CAASG,UAAA;MAI5B,SAASC,IAAA,IAAQD,UAAA,EAAY;QAC3B,IAAIC,IAAA,KAAS,YAAYJ,QAAA,CAASK,eAAA,CAAgBD,IAAI;MACxD;MAEWJ,QAAA,GAAAM,aAAA,CAAkCN,QAAQ;MAMrD,MAAMN,QAAA,GAAW;MACjB,MAAMvD,KAAA,GAAQ;MAIR,MAAAoE,iBAAA,GAAoBP,QAAA,CAASQ,YAAA,CAAa,UAAU;MAE1D,SAAS5D,CAAA,GAAI,GAAGA,CAAA,GAAI2D,iBAAA,CAAkBN,KAAA,EAAOrD,CAAA,IAAK;QAChD,MAAMb,CAAA,GAAI,IAAIZ,OAAA,GAAUsF,mBAAA,CAAoBF,iBAAA,EAAmB3D,CAAC;QAEhE,MAAMJ,MAAA,GAAS,IAAIX,MAAA,CAAOE,CAAA,EAAGa,CAAC;QAC9B8C,QAAA,CAASjE,IAAA,CAAKe,MAAM;MACtB;MAIM,MAAAkE,SAAA,GAAYV,QAAA,CAASW,QAAA;MAE3B,IAAID,SAAA,KAAc,MAAM;QACtB,SAAS9D,CAAA,GAAI,GAAGA,CAAA,GAAI8D,SAAA,CAAUT,KAAA,EAAOrD,CAAA,IAAK,GAAG;UACrC,MAAAO,CAAA,GAAIuD,SAAA,CAAUE,IAAA,CAAKhE,CAAC;UAC1B,MAAMQ,CAAA,GAAIsD,SAAA,CAAUE,IAAA,CAAKhE,CAAA,GAAI,CAAC;UAC9B,MAAMS,CAAA,GAAIqD,SAAA,CAAUE,IAAA,CAAKhE,CAAA,GAAI,CAAC;UAE9B,MAAMiE,QAAA,GAAW,IAAI9D,QAAA,CAAS2C,QAAA,CAASvC,CAAC,GAAGuC,QAAA,CAAStC,CAAC,GAAGsC,QAAA,CAASrC,CAAC,GAAGF,CAAA,EAAGC,CAAA,EAAGC,CAAC;UAC5ElB,KAAA,CAAMV,IAAA,CAAKoF,QAAQ;QACrB;MAAA,OACK;QACL,SAASjE,CAAA,GAAI,GAAGA,CAAA,GAAI2D,iBAAA,CAAkBN,KAAA,EAAOrD,CAAA,IAAK,GAAG;UACnD,MAAMO,CAAA,GAAIP,CAAA;UACV,MAAMQ,CAAA,GAAIR,CAAA,GAAI;UACd,MAAMS,CAAA,GAAIT,CAAA,GAAI;UAEd,MAAMiE,QAAA,GAAW,IAAI9D,QAAA,CAAS2C,QAAA,CAASvC,CAAC,GAAGuC,QAAA,CAAStC,CAAC,GAAGsC,QAAA,CAASrC,CAAC,GAAGF,CAAA,EAAGC,CAAA,EAAGC,CAAC;UAC5ElB,KAAA,CAAMV,IAAA,CAAKoF,QAAQ;QACrB;MACF;MAIA,SAASjE,CAAA,GAAI,GAAG4B,EAAA,GAAKkB,QAAA,CAAS7C,MAAA,EAAQD,CAAA,GAAI4B,EAAA,EAAI5B,CAAA,IAAK;QAC5C,KAAAkD,uBAAA,CAAwBJ,QAAA,CAAS9C,CAAC,CAAC;MAC1C;MAEI,IAAAkE,UAAA;MAEJ,IAAIC,CAAA,GAAId,KAAA;MAER,OAAOc,CAAA,IAAK;QACGD,UAAA,QAAKE,eAAA,CAAgBtB,QAAQ;QAE1C,IAAI,CAACoB,UAAA,EAAY;UACfG,OAAA,CAAQC,GAAA,CAAI,wCAAwC;UACpD;QAAA,OACK;UACL,KAAKC,QAAA,CAASzB,QAAA,EAAUvD,KAAA,EAAO2E,UAAA,EAAYA,UAAA,CAAWxE,gBAA0B;QAClF;MACF;MAIM,MAAA8E,kBAAA,GAAqB,IAAIC,cAAA;MAC/B,MAAMnF,QAAA,GAAW;MACjB,IAAIoF,KAAA,GAAQ;MAIZ,SAAS1E,CAAA,GAAI,GAAGA,CAAA,GAAI8C,QAAA,CAAS7C,MAAA,EAAQD,CAAA,IAAK;QAClC,MAAAJ,MAAA,GAASkD,QAAA,CAAS9C,CAAC,EAAEV,QAAA;QAC3BA,QAAA,CAAST,IAAA,CAAKe,MAAA,CAAO+E,CAAA,EAAG/E,MAAA,CAAOgF,CAAA,EAAGhF,MAAA,CAAOuE,CAAC;MAC5C;MAIA,SAASnE,CAAA,GAAI,GAAGA,CAAA,GAAIT,KAAA,CAAMU,MAAA,EAAQD,CAAA,IAAK;QAC/B,MAAA6B,IAAA,GAAOtC,KAAA,CAAMS,CAAC;QAEpB,MAAMO,CAAA,GAAIuC,QAAA,CAASlE,OAAA,CAAQiD,IAAA,CAAKzB,EAAE;QAClC,MAAMI,CAAA,GAAIsC,QAAA,CAASlE,OAAA,CAAQiD,IAAA,CAAKxB,EAAE;QAClC,MAAMI,CAAA,GAAIqC,QAAA,CAASlE,OAAA,CAAQiD,IAAA,CAAKvB,EAAE;QAE5BoE,KAAA,CAAA7F,IAAA,CAAK0B,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACpB;MAIA+D,kBAAA,CAAmBK,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBxF,QAAA,EAAU,CAAC,CAAC;MACnFkF,kBAAA,CAAmBO,QAAA,CAASL,KAAK;MAE1B,OAAAF,kBAAA;IAAA;EAjSM;EAsDPzB,aAAa5D,CAAA,EAAW2D,QAAA,EAA0B;IACxDuB,OAAA,CAAQW,MAAA,CAAO7F,CAAA,CAAEI,KAAA,CAAMU,MAAA,KAAW,CAAC;IAE5B,OAAAd,CAAA,CAAEK,SAAA,CAAUS,MAAA,EAAQ;MACnB,MAAAH,CAAA,GAAIX,CAAA,CAAEK,SAAA,CAAUyF,GAAA,CAAI;MACVnG,eAAA,CAAAgB,CAAA,CAAEN,SAAA,EAAWL,CAAC;IAChC;IAEAL,eAAA,CAAgBgE,QAAA,EAAU3D,CAAC;EAC7B;AAoOF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}