{"ast": null, "code": "import { Vector3, <PERSON>al<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON>k, Vector2, Matrix4, Box3 } from \"three\";\nimport { CSMFrustum } from \"./CSMFrustum.js\";\nimport { CSMShader } from \"./CSMShader.js\";\nconst _cameraToLightMatrix = /* @__PURE__ */new Matrix4();\nconst _lightSpaceFrustum = /* @__PURE__ */new CSMFrustum();\nconst _center = /* @__PURE__ */new Vector3();\nconst _bbox = /* @__PURE__ */new Box3();\nconst _uniformArray = [];\nconst _logArray = [];\nclass CSM {\n  constructor(data) {\n    data = data || {};\n    this.camera = data.camera;\n    this.parent = data.parent;\n    this.cascades = data.cascades || 3;\n    this.maxFar = data.maxFar || 1e5;\n    this.mode = data.mode || \"practical\";\n    this.shadowMapSize = data.shadowMapSize || 2048;\n    this.shadowBias = data.shadowBias || 1e-6;\n    this.lightDirection = data.lightDirection || new Vector3(1, -1, 1).normalize();\n    this.lightIntensity = data.lightIntensity || 1;\n    this.lightNear = data.lightNear || 1;\n    this.lightFar = data.lightFar || 2e3;\n    this.lightMargin = data.lightMargin || 200;\n    this.customSplitsCallback = data.customSplitsCallback;\n    this.fade = false;\n    this.mainFrustum = new CSMFrustum();\n    this.frustums = [];\n    this.breaks = [];\n    this.lights = [];\n    this.shaders = /* @__PURE__ */new Map();\n    this.createLights();\n    this.updateFrustums();\n    this.injectInclude();\n  }\n  createLights() {\n    for (let i = 0; i < this.cascades; i++) {\n      const light = new DirectionalLight(16777215, this.lightIntensity);\n      light.castShadow = true;\n      light.shadow.mapSize.width = this.shadowMapSize;\n      light.shadow.mapSize.height = this.shadowMapSize;\n      light.shadow.camera.near = this.lightNear;\n      light.shadow.camera.far = this.lightFar;\n      light.shadow.bias = this.shadowBias;\n      this.parent.add(light);\n      this.parent.add(light.target);\n      this.lights.push(light);\n    }\n  }\n  initCascades() {\n    const camera = this.camera;\n    camera.updateProjectionMatrix();\n    this.mainFrustum.setFromProjectionMatrix(camera.projectionMatrix, this.maxFar);\n    this.mainFrustum.split(this.breaks, this.frustums);\n  }\n  updateShadowBounds() {\n    const frustums = this.frustums;\n    for (let i = 0; i < frustums.length; i++) {\n      const light = this.lights[i];\n      const shadowCam = light.shadow.camera;\n      const frustum = this.frustums[i];\n      const nearVerts = frustum.vertices.near;\n      const farVerts = frustum.vertices.far;\n      const point1 = farVerts[0];\n      let point2;\n      if (point1.distanceTo(farVerts[2]) > point1.distanceTo(nearVerts[2])) {\n        point2 = farVerts[2];\n      } else {\n        point2 = nearVerts[2];\n      }\n      let squaredBBWidth = point1.distanceTo(point2);\n      if (this.fade) {\n        const camera = this.camera;\n        const far = Math.max(camera.far, this.maxFar);\n        const linearDepth = frustum.vertices.far[0].z / (far - camera.near);\n        const margin = 0.25 * Math.pow(linearDepth, 2) * (far - camera.near);\n        squaredBBWidth += margin;\n      }\n      shadowCam.left = -squaredBBWidth / 2;\n      shadowCam.right = squaredBBWidth / 2;\n      shadowCam.top = squaredBBWidth / 2;\n      shadowCam.bottom = -squaredBBWidth / 2;\n      shadowCam.updateProjectionMatrix();\n    }\n  }\n  getBreaks() {\n    const camera = this.camera;\n    const far = Math.min(camera.far, this.maxFar);\n    this.breaks.length = 0;\n    switch (this.mode) {\n      case \"uniform\":\n        uniformSplit(this.cascades, camera.near, far, this.breaks);\n        break;\n      case \"logarithmic\":\n        logarithmicSplit(this.cascades, camera.near, far, this.breaks);\n        break;\n      case \"practical\":\n        practicalSplit(this.cascades, camera.near, far, 0.5, this.breaks);\n        break;\n      case \"custom\":\n        if (this.customSplitsCallback === void 0) console.error(\"CSM: Custom split scheme callback not defined.\");\n        this.customSplitsCallback(this.cascades, camera.near, far, this.breaks);\n        break;\n    }\n    function uniformSplit(amount, near, far2, target) {\n      for (let i = 1; i < amount; i++) {\n        target.push((near + (far2 - near) * i / amount) / far2);\n      }\n      target.push(1);\n    }\n    function logarithmicSplit(amount, near, far2, target) {\n      for (let i = 1; i < amount; i++) {\n        target.push(near * (far2 / near) ** (i / amount) / far2);\n      }\n      target.push(1);\n    }\n    function practicalSplit(amount, near, far2, lambda, target) {\n      _uniformArray.length = 0;\n      _logArray.length = 0;\n      logarithmicSplit(amount, near, far2, _logArray);\n      uniformSplit(amount, near, far2, _uniformArray);\n      for (let i = 1; i < amount; i++) {\n        target.push(MathUtils.lerp(_uniformArray[i - 1], _logArray[i - 1], lambda));\n      }\n      target.push(1);\n    }\n  }\n  update() {\n    const camera = this.camera;\n    const frustums = this.frustums;\n    for (let i = 0; i < frustums.length; i++) {\n      const light = this.lights[i];\n      const shadowCam = light.shadow.camera;\n      const texelWidth = (shadowCam.right - shadowCam.left) / this.shadowMapSize;\n      const texelHeight = (shadowCam.top - shadowCam.bottom) / this.shadowMapSize;\n      light.shadow.camera.updateMatrixWorld(true);\n      _cameraToLightMatrix.multiplyMatrices(light.shadow.camera.matrixWorldInverse, camera.matrixWorld);\n      frustums[i].toSpace(_cameraToLightMatrix, _lightSpaceFrustum);\n      const nearVerts = _lightSpaceFrustum.vertices.near;\n      const farVerts = _lightSpaceFrustum.vertices.far;\n      _bbox.makeEmpty();\n      for (let j = 0; j < 4; j++) {\n        _bbox.expandByPoint(nearVerts[j]);\n        _bbox.expandByPoint(farVerts[j]);\n      }\n      _bbox.getCenter(_center);\n      _center.z = _bbox.max.z + this.lightMargin;\n      _center.x = Math.floor(_center.x / texelWidth) * texelWidth;\n      _center.y = Math.floor(_center.y / texelHeight) * texelHeight;\n      _center.applyMatrix4(light.shadow.camera.matrixWorld);\n      light.position.copy(_center);\n      light.target.position.copy(_center);\n      light.target.position.x += this.lightDirection.x;\n      light.target.position.y += this.lightDirection.y;\n      light.target.position.z += this.lightDirection.z;\n    }\n  }\n  injectInclude() {\n    ShaderChunk.lights_fragment_begin = CSMShader.lights_fragment_begin;\n    ShaderChunk.lights_pars_begin = CSMShader.lights_pars_begin;\n  }\n  setupMaterial(material) {\n    material.defines = material.defines || {};\n    material.defines.USE_CSM = 1;\n    material.defines.CSM_CASCADES = this.cascades;\n    if (this.fade) {\n      material.defines.CSM_FADE = \"\";\n    }\n    const breaksVec2 = [];\n    const scope = this;\n    const shaders = this.shaders;\n    material.onBeforeCompile = function (shader) {\n      const far = Math.min(scope.camera.far, scope.maxFar);\n      scope.getExtendedBreaks(breaksVec2);\n      shader.uniforms.CSM_cascades = {\n        value: breaksVec2\n      };\n      shader.uniforms.cameraNear = {\n        value: scope.camera.near\n      };\n      shader.uniforms.shadowFar = {\n        value: far\n      };\n      shaders.set(material, shader);\n    };\n    shaders.set(material, null);\n  }\n  updateUniforms() {\n    const far = Math.min(this.camera.far, this.maxFar);\n    const shaders = this.shaders;\n    shaders.forEach(function (shader, material) {\n      if (shader !== null) {\n        const uniforms = shader.uniforms;\n        this.getExtendedBreaks(uniforms.CSM_cascades.value);\n        uniforms.cameraNear.value = this.camera.near;\n        uniforms.shadowFar.value = far;\n      }\n      if (!this.fade && \"CSM_FADE\" in material.defines) {\n        delete material.defines.CSM_FADE;\n        material.needsUpdate = true;\n      } else if (this.fade && !(\"CSM_FADE\" in material.defines)) {\n        material.defines.CSM_FADE = \"\";\n        material.needsUpdate = true;\n      }\n    }, this);\n  }\n  getExtendedBreaks(target) {\n    while (target.length < this.breaks.length) {\n      target.push(new Vector2());\n    }\n    target.length = this.breaks.length;\n    for (let i = 0; i < this.cascades; i++) {\n      const amount = this.breaks[i];\n      const prev = this.breaks[i - 1] || 0;\n      target[i].x = prev;\n      target[i].y = amount;\n    }\n  }\n  updateFrustums() {\n    this.getBreaks();\n    this.initCascades();\n    this.updateShadowBounds();\n    this.updateUniforms();\n  }\n  remove() {\n    for (let i = 0; i < this.lights.length; i++) {\n      this.parent.remove(this.lights[i]);\n    }\n  }\n  dispose() {\n    const shaders = this.shaders;\n    shaders.forEach(function (shader, material) {\n      delete material.onBeforeCompile;\n      delete material.defines.USE_CSM;\n      delete material.defines.CSM_CASCADES;\n      delete material.defines.CSM_FADE;\n      if (shader !== null) {\n        delete shader.uniforms.CSM_cascades;\n        delete shader.uniforms.cameraNear;\n        delete shader.uniforms.shadowFar;\n      }\n      material.needsUpdate = true;\n    });\n    shaders.clear();\n  }\n}\nexport { CSM };", "map": {"version": 3, "names": ["_cameraToLightMatrix", "Matrix4", "_lightSpaceFrustum", "CSMFrustum", "_center", "Vector3", "_bbox", "Box3", "_uniformArray", "_logArray", "CSM", "constructor", "data", "camera", "parent", "cascades", "maxFar", "mode", "shadowMapSize", "<PERSON><PERSON><PERSON>", "lightDirection", "normalize", "lightIntensity", "lightNear", "lightFar", "light<PERSON><PERSON><PERSON>", "customSplitsCallback", "fade", "mainFrustum", "frustums", "breaks", "lights", "shaders", "Map", "createLights", "updateFrustums", "injectInclude", "i", "light", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "shadow", "mapSize", "width", "height", "near", "far", "bias", "add", "target", "push", "initCascades", "updateProjectionMatrix", "setFromProjectionMatrix", "projectionMatrix", "split", "updateShadowBounds", "length", "shadowCam", "frustum", "nearVerts", "vertices", "<PERSON><PERSON><PERSON><PERSON>", "point1", "point2", "distanceTo", "squaredBBWidth", "Math", "max", "linear<PERSON>epth", "z", "margin", "pow", "left", "right", "top", "bottom", "getBreaks", "min", "uniformSplit", "logarithmicSplit", "practicalSplit", "console", "error", "amount", "far2", "lambda", "MathUtils", "lerp", "update", "texelWidth", "texelHeight", "updateMatrixWorld", "multiplyMatrices", "matrixWorldInverse", "matrixWorld", "toSpace", "makeEmpty", "j", "expandByPoint", "getCenter", "x", "floor", "y", "applyMatrix4", "position", "copy", "ShaderChunk", "lights_fragment_begin", "CS<PERSON><PERSON>er", "lights_pars_begin", "setupMaterial", "material", "defines", "USE_CSM", "CSM_CASCADES", "CSM_FADE", "breaksVec2", "scope", "onBeforeCompile", "shader", "getExtendedBreaks", "uniforms", "CSM_cascades", "value", "cameraNear", "shadowFar", "set", "updateUniforms", "for<PERSON>ach", "needsUpdate", "Vector2", "prev", "remove", "dispose", "clear"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/csm/CSM.js"], "sourcesContent": ["import { Vector2, Vector3, <PERSON>al<PERSON>ight, Math<PERSON><PERSON>s, ShaderChunk, Matrix4, Box3 } from 'three'\nimport { CSMFrustum } from './CSMFrustum'\nimport { CSMShader } from './CSMShader'\n\nconst _cameraToLightMatrix = /* @__PURE__ */ new Matrix4()\nconst _lightSpaceFrustum = /* @__PURE__ */ new CSMFrustum()\nconst _center = /* @__PURE__ */ new Vector3()\nconst _bbox = /* @__PURE__ */ new Box3()\nconst _uniformArray = []\nconst _logArray = []\n\nexport class CSM {\n  constructor(data) {\n    data = data || {}\n\n    this.camera = data.camera\n    this.parent = data.parent\n    this.cascades = data.cascades || 3\n    this.maxFar = data.maxFar || 100000\n    this.mode = data.mode || 'practical'\n    this.shadowMapSize = data.shadowMapSize || 2048\n    this.shadowBias = data.shadowBias || 0.000001\n    this.lightDirection = data.lightDirection || new Vector3(1, -1, 1).normalize()\n    this.lightIntensity = data.lightIntensity || 1\n    this.lightNear = data.lightNear || 1\n    this.lightFar = data.lightFar || 2000\n    this.lightMargin = data.lightMargin || 200\n    this.customSplitsCallback = data.customSplitsCallback\n    this.fade = false\n    this.mainFrustum = new CSMFrustum()\n    this.frustums = []\n    this.breaks = []\n\n    this.lights = []\n    this.shaders = new Map()\n\n    this.createLights()\n    this.updateFrustums()\n    this.injectInclude()\n  }\n\n  createLights() {\n    for (let i = 0; i < this.cascades; i++) {\n      const light = new DirectionalLight(0xffffff, this.lightIntensity)\n      light.castShadow = true\n      light.shadow.mapSize.width = this.shadowMapSize\n      light.shadow.mapSize.height = this.shadowMapSize\n\n      light.shadow.camera.near = this.lightNear\n      light.shadow.camera.far = this.lightFar\n      light.shadow.bias = this.shadowBias\n\n      this.parent.add(light)\n      this.parent.add(light.target)\n      this.lights.push(light)\n    }\n  }\n\n  initCascades() {\n    const camera = this.camera\n    camera.updateProjectionMatrix()\n    this.mainFrustum.setFromProjectionMatrix(camera.projectionMatrix, this.maxFar)\n    this.mainFrustum.split(this.breaks, this.frustums)\n  }\n\n  updateShadowBounds() {\n    const frustums = this.frustums\n    for (let i = 0; i < frustums.length; i++) {\n      const light = this.lights[i]\n      const shadowCam = light.shadow.camera\n      const frustum = this.frustums[i]\n\n      // Get the two points that represent that furthest points on the frustum assuming\n      // that's either the diagonal across the far plane or the diagonal across the whole\n      // frustum itself.\n      const nearVerts = frustum.vertices.near\n      const farVerts = frustum.vertices.far\n      const point1 = farVerts[0]\n      let point2\n      if (point1.distanceTo(farVerts[2]) > point1.distanceTo(nearVerts[2])) {\n        point2 = farVerts[2]\n      } else {\n        point2 = nearVerts[2]\n      }\n\n      let squaredBBWidth = point1.distanceTo(point2)\n      if (this.fade) {\n        // expand the shadow extents by the fade margin if fade is enabled.\n        const camera = this.camera\n        const far = Math.max(camera.far, this.maxFar)\n        const linearDepth = frustum.vertices.far[0].z / (far - camera.near)\n        const margin = 0.25 * Math.pow(linearDepth, 2.0) * (far - camera.near)\n\n        squaredBBWidth += margin\n      }\n\n      shadowCam.left = -squaredBBWidth / 2\n      shadowCam.right = squaredBBWidth / 2\n      shadowCam.top = squaredBBWidth / 2\n      shadowCam.bottom = -squaredBBWidth / 2\n      shadowCam.updateProjectionMatrix()\n    }\n  }\n\n  getBreaks() {\n    const camera = this.camera\n    const far = Math.min(camera.far, this.maxFar)\n    this.breaks.length = 0\n\n    switch (this.mode) {\n      case 'uniform':\n        uniformSplit(this.cascades, camera.near, far, this.breaks)\n        break\n      case 'logarithmic':\n        logarithmicSplit(this.cascades, camera.near, far, this.breaks)\n        break\n      case 'practical':\n        practicalSplit(this.cascades, camera.near, far, 0.5, this.breaks)\n        break\n      case 'custom':\n        if (this.customSplitsCallback === undefined) console.error('CSM: Custom split scheme callback not defined.')\n        this.customSplitsCallback(this.cascades, camera.near, far, this.breaks)\n        break\n    }\n\n    function uniformSplit(amount, near, far, target) {\n      for (let i = 1; i < amount; i++) {\n        target.push((near + ((far - near) * i) / amount) / far)\n      }\n\n      target.push(1)\n    }\n\n    function logarithmicSplit(amount, near, far, target) {\n      for (let i = 1; i < amount; i++) {\n        target.push((near * (far / near) ** (i / amount)) / far)\n      }\n\n      target.push(1)\n    }\n\n    function practicalSplit(amount, near, far, lambda, target) {\n      _uniformArray.length = 0\n      _logArray.length = 0\n      logarithmicSplit(amount, near, far, _logArray)\n      uniformSplit(amount, near, far, _uniformArray)\n\n      for (let i = 1; i < amount; i++) {\n        target.push(MathUtils.lerp(_uniformArray[i - 1], _logArray[i - 1], lambda))\n      }\n\n      target.push(1)\n    }\n  }\n\n  update() {\n    const camera = this.camera\n    const frustums = this.frustums\n    for (let i = 0; i < frustums.length; i++) {\n      const light = this.lights[i]\n      const shadowCam = light.shadow.camera\n      const texelWidth = (shadowCam.right - shadowCam.left) / this.shadowMapSize\n      const texelHeight = (shadowCam.top - shadowCam.bottom) / this.shadowMapSize\n      light.shadow.camera.updateMatrixWorld(true)\n      _cameraToLightMatrix.multiplyMatrices(light.shadow.camera.matrixWorldInverse, camera.matrixWorld)\n      frustums[i].toSpace(_cameraToLightMatrix, _lightSpaceFrustum)\n\n      const nearVerts = _lightSpaceFrustum.vertices.near\n      const farVerts = _lightSpaceFrustum.vertices.far\n      _bbox.makeEmpty()\n      for (let j = 0; j < 4; j++) {\n        _bbox.expandByPoint(nearVerts[j])\n        _bbox.expandByPoint(farVerts[j])\n      }\n\n      _bbox.getCenter(_center)\n      _center.z = _bbox.max.z + this.lightMargin\n      _center.x = Math.floor(_center.x / texelWidth) * texelWidth\n      _center.y = Math.floor(_center.y / texelHeight) * texelHeight\n      _center.applyMatrix4(light.shadow.camera.matrixWorld)\n\n      light.position.copy(_center)\n      light.target.position.copy(_center)\n\n      light.target.position.x += this.lightDirection.x\n      light.target.position.y += this.lightDirection.y\n      light.target.position.z += this.lightDirection.z\n    }\n  }\n\n  injectInclude() {\n    ShaderChunk.lights_fragment_begin = CSMShader.lights_fragment_begin\n    ShaderChunk.lights_pars_begin = CSMShader.lights_pars_begin\n  }\n\n  setupMaterial(material) {\n    material.defines = material.defines || {}\n    material.defines.USE_CSM = 1\n    material.defines.CSM_CASCADES = this.cascades\n\n    if (this.fade) {\n      material.defines.CSM_FADE = ''\n    }\n\n    const breaksVec2 = []\n    const scope = this\n    const shaders = this.shaders\n\n    material.onBeforeCompile = function (shader) {\n      const far = Math.min(scope.camera.far, scope.maxFar)\n      scope.getExtendedBreaks(breaksVec2)\n\n      shader.uniforms.CSM_cascades = { value: breaksVec2 }\n      shader.uniforms.cameraNear = { value: scope.camera.near }\n      shader.uniforms.shadowFar = { value: far }\n\n      shaders.set(material, shader)\n    }\n\n    shaders.set(material, null)\n  }\n\n  updateUniforms() {\n    const far = Math.min(this.camera.far, this.maxFar)\n    const shaders = this.shaders\n\n    shaders.forEach(function (shader, material) {\n      if (shader !== null) {\n        const uniforms = shader.uniforms\n        this.getExtendedBreaks(uniforms.CSM_cascades.value)\n        uniforms.cameraNear.value = this.camera.near\n        uniforms.shadowFar.value = far\n      }\n\n      if (!this.fade && 'CSM_FADE' in material.defines) {\n        delete material.defines.CSM_FADE\n        material.needsUpdate = true\n      } else if (this.fade && !('CSM_FADE' in material.defines)) {\n        material.defines.CSM_FADE = ''\n        material.needsUpdate = true\n      }\n    }, this)\n  }\n\n  getExtendedBreaks(target) {\n    while (target.length < this.breaks.length) {\n      target.push(new Vector2())\n    }\n\n    target.length = this.breaks.length\n\n    for (let i = 0; i < this.cascades; i++) {\n      const amount = this.breaks[i]\n      const prev = this.breaks[i - 1] || 0\n      target[i].x = prev\n      target[i].y = amount\n    }\n  }\n\n  updateFrustums() {\n    this.getBreaks()\n    this.initCascades()\n    this.updateShadowBounds()\n    this.updateUniforms()\n  }\n\n  remove() {\n    for (let i = 0; i < this.lights.length; i++) {\n      this.parent.remove(this.lights[i])\n    }\n  }\n\n  dispose() {\n    const shaders = this.shaders\n    shaders.forEach(function (shader, material) {\n      delete material.onBeforeCompile\n      delete material.defines.USE_CSM\n      delete material.defines.CSM_CASCADES\n      delete material.defines.CSM_FADE\n\n      if (shader !== null) {\n        delete shader.uniforms.CSM_cascades\n        delete shader.uniforms.cameraNear\n        delete shader.uniforms.shadowFar\n      }\n\n      material.needsUpdate = true\n    })\n    shaders.clear()\n  }\n}\n"], "mappings": ";;;AAIA,MAAMA,oBAAA,GAAuC,mBAAIC,OAAA,CAAS;AAC1D,MAAMC,kBAAA,GAAqC,mBAAIC,UAAA,CAAY;AAC3D,MAAMC,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAC7C,MAAMC,KAAA,GAAwB,mBAAIC,IAAA,CAAM;AACxC,MAAMC,aAAA,GAAgB,EAAE;AACxB,MAAMC,SAAA,GAAY,EAAE;AAEb,MAAMC,GAAA,CAAI;EACfC,YAAYC,IAAA,EAAM;IAChBA,IAAA,GAAOA,IAAA,IAAQ,CAAE;IAEjB,KAAKC,MAAA,GAASD,IAAA,CAAKC,MAAA;IACnB,KAAKC,MAAA,GAASF,IAAA,CAAKE,MAAA;IACnB,KAAKC,QAAA,GAAWH,IAAA,CAAKG,QAAA,IAAY;IACjC,KAAKC,MAAA,GAASJ,IAAA,CAAKI,MAAA,IAAU;IAC7B,KAAKC,IAAA,GAAOL,IAAA,CAAKK,IAAA,IAAQ;IACzB,KAAKC,aAAA,GAAgBN,IAAA,CAAKM,aAAA,IAAiB;IAC3C,KAAKC,UAAA,GAAaP,IAAA,CAAKO,UAAA,IAAc;IACrC,KAAKC,cAAA,GAAiBR,IAAA,CAAKQ,cAAA,IAAkB,IAAIf,OAAA,CAAQ,GAAG,IAAI,CAAC,EAAEgB,SAAA,CAAW;IAC9E,KAAKC,cAAA,GAAiBV,IAAA,CAAKU,cAAA,IAAkB;IAC7C,KAAKC,SAAA,GAAYX,IAAA,CAAKW,SAAA,IAAa;IACnC,KAAKC,QAAA,GAAWZ,IAAA,CAAKY,QAAA,IAAY;IACjC,KAAKC,WAAA,GAAcb,IAAA,CAAKa,WAAA,IAAe;IACvC,KAAKC,oBAAA,GAAuBd,IAAA,CAAKc,oBAAA;IACjC,KAAKC,IAAA,GAAO;IACZ,KAAKC,WAAA,GAAc,IAAIzB,UAAA,CAAY;IACnC,KAAK0B,QAAA,GAAW,EAAE;IAClB,KAAKC,MAAA,GAAS,EAAE;IAEhB,KAAKC,MAAA,GAAS,EAAE;IAChB,KAAKC,OAAA,GAAU,mBAAIC,GAAA,CAAK;IAExB,KAAKC,YAAA,CAAc;IACnB,KAAKC,cAAA,CAAgB;IACrB,KAAKC,aAAA,CAAe;EACrB;EAEDF,aAAA,EAAe;IACb,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKtB,QAAA,EAAUsB,CAAA,IAAK;MACtC,MAAMC,KAAA,GAAQ,IAAIC,gBAAA,CAAiB,UAAU,KAAKjB,cAAc;MAChEgB,KAAA,CAAME,UAAA,GAAa;MACnBF,KAAA,CAAMG,MAAA,CAAOC,OAAA,CAAQC,KAAA,GAAQ,KAAKzB,aAAA;MAClCoB,KAAA,CAAMG,MAAA,CAAOC,OAAA,CAAQE,MAAA,GAAS,KAAK1B,aAAA;MAEnCoB,KAAA,CAAMG,MAAA,CAAO5B,MAAA,CAAOgC,IAAA,GAAO,KAAKtB,SAAA;MAChCe,KAAA,CAAMG,MAAA,CAAO5B,MAAA,CAAOiC,GAAA,GAAM,KAAKtB,QAAA;MAC/Bc,KAAA,CAAMG,MAAA,CAAOM,IAAA,GAAO,KAAK5B,UAAA;MAEzB,KAAKL,MAAA,CAAOkC,GAAA,CAAIV,KAAK;MACrB,KAAKxB,MAAA,CAAOkC,GAAA,CAAIV,KAAA,CAAMW,MAAM;MAC5B,KAAKlB,MAAA,CAAOmB,IAAA,CAAKZ,KAAK;IACvB;EACF;EAEDa,aAAA,EAAe;IACb,MAAMtC,MAAA,GAAS,KAAKA,MAAA;IACpBA,MAAA,CAAOuC,sBAAA,CAAwB;IAC/B,KAAKxB,WAAA,CAAYyB,uBAAA,CAAwBxC,MAAA,CAAOyC,gBAAA,EAAkB,KAAKtC,MAAM;IAC7E,KAAKY,WAAA,CAAY2B,KAAA,CAAM,KAAKzB,MAAA,EAAQ,KAAKD,QAAQ;EAClD;EAED2B,mBAAA,EAAqB;IACnB,MAAM3B,QAAA,GAAW,KAAKA,QAAA;IACtB,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAIR,QAAA,CAAS4B,MAAA,EAAQpB,CAAA,IAAK;MACxC,MAAMC,KAAA,GAAQ,KAAKP,MAAA,CAAOM,CAAC;MAC3B,MAAMqB,SAAA,GAAYpB,KAAA,CAAMG,MAAA,CAAO5B,MAAA;MAC/B,MAAM8C,OAAA,GAAU,KAAK9B,QAAA,CAASQ,CAAC;MAK/B,MAAMuB,SAAA,GAAYD,OAAA,CAAQE,QAAA,CAAShB,IAAA;MACnC,MAAMiB,QAAA,GAAWH,OAAA,CAAQE,QAAA,CAASf,GAAA;MAClC,MAAMiB,MAAA,GAASD,QAAA,CAAS,CAAC;MACzB,IAAIE,MAAA;MACJ,IAAID,MAAA,CAAOE,UAAA,CAAWH,QAAA,CAAS,CAAC,CAAC,IAAIC,MAAA,CAAOE,UAAA,CAAWL,SAAA,CAAU,CAAC,CAAC,GAAG;QACpEI,MAAA,GAASF,QAAA,CAAS,CAAC;MAC3B,OAAa;QACLE,MAAA,GAASJ,SAAA,CAAU,CAAC;MACrB;MAED,IAAIM,cAAA,GAAiBH,MAAA,CAAOE,UAAA,CAAWD,MAAM;MAC7C,IAAI,KAAKrC,IAAA,EAAM;QAEb,MAAMd,MAAA,GAAS,KAAKA,MAAA;QACpB,MAAMiC,GAAA,GAAMqB,IAAA,CAAKC,GAAA,CAAIvD,MAAA,CAAOiC,GAAA,EAAK,KAAK9B,MAAM;QAC5C,MAAMqD,WAAA,GAAcV,OAAA,CAAQE,QAAA,CAASf,GAAA,CAAI,CAAC,EAAEwB,CAAA,IAAKxB,GAAA,GAAMjC,MAAA,CAAOgC,IAAA;QAC9D,MAAM0B,MAAA,GAAS,OAAOJ,IAAA,CAAKK,GAAA,CAAIH,WAAA,EAAa,CAAG,KAAKvB,GAAA,GAAMjC,MAAA,CAAOgC,IAAA;QAEjEqB,cAAA,IAAkBK,MAAA;MACnB;MAEDb,SAAA,CAAUe,IAAA,GAAO,CAACP,cAAA,GAAiB;MACnCR,SAAA,CAAUgB,KAAA,GAAQR,cAAA,GAAiB;MACnCR,SAAA,CAAUiB,GAAA,GAAMT,cAAA,GAAiB;MACjCR,SAAA,CAAUkB,MAAA,GAAS,CAACV,cAAA,GAAiB;MACrCR,SAAA,CAAUN,sBAAA,CAAwB;IACnC;EACF;EAEDyB,UAAA,EAAY;IACV,MAAMhE,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMiC,GAAA,GAAMqB,IAAA,CAAKW,GAAA,CAAIjE,MAAA,CAAOiC,GAAA,EAAK,KAAK9B,MAAM;IAC5C,KAAKc,MAAA,CAAO2B,MAAA,GAAS;IAErB,QAAQ,KAAKxC,IAAA;MACX,KAAK;QACH8D,YAAA,CAAa,KAAKhE,QAAA,EAAUF,MAAA,CAAOgC,IAAA,EAAMC,GAAA,EAAK,KAAKhB,MAAM;QACzD;MACF,KAAK;QACHkD,gBAAA,CAAiB,KAAKjE,QAAA,EAAUF,MAAA,CAAOgC,IAAA,EAAMC,GAAA,EAAK,KAAKhB,MAAM;QAC7D;MACF,KAAK;QACHmD,cAAA,CAAe,KAAKlE,QAAA,EAAUF,MAAA,CAAOgC,IAAA,EAAMC,GAAA,EAAK,KAAK,KAAKhB,MAAM;QAChE;MACF,KAAK;QACH,IAAI,KAAKJ,oBAAA,KAAyB,QAAWwD,OAAA,CAAQC,KAAA,CAAM,gDAAgD;QAC3G,KAAKzD,oBAAA,CAAqB,KAAKX,QAAA,EAAUF,MAAA,CAAOgC,IAAA,EAAMC,GAAA,EAAK,KAAKhB,MAAM;QACtE;IACH;IAED,SAASiD,aAAaK,MAAA,EAAQvC,IAAA,EAAMwC,IAAA,EAAKpC,MAAA,EAAQ;MAC/C,SAASZ,CAAA,GAAI,GAAGA,CAAA,GAAI+C,MAAA,EAAQ/C,CAAA,IAAK;QAC/BY,MAAA,CAAOC,IAAA,EAAML,IAAA,IAASwC,IAAA,GAAMxC,IAAA,IAAQR,CAAA,GAAK+C,MAAA,IAAUC,IAAG;MACvD;MAEDpC,MAAA,CAAOC,IAAA,CAAK,CAAC;IACd;IAED,SAAS8B,iBAAiBI,MAAA,EAAQvC,IAAA,EAAMwC,IAAA,EAAKpC,MAAA,EAAQ;MACnD,SAASZ,CAAA,GAAI,GAAGA,CAAA,GAAI+C,MAAA,EAAQ/C,CAAA,IAAK;QAC/BY,MAAA,CAAOC,IAAA,CAAML,IAAA,IAAQwC,IAAA,GAAMxC,IAAA,MAAUR,CAAA,GAAI+C,MAAA,IAAWC,IAAG;MACxD;MAEDpC,MAAA,CAAOC,IAAA,CAAK,CAAC;IACd;IAED,SAAS+B,eAAeG,MAAA,EAAQvC,IAAA,EAAMwC,IAAA,EAAKC,MAAA,EAAQrC,MAAA,EAAQ;MACzDzC,aAAA,CAAciD,MAAA,GAAS;MACvBhD,SAAA,CAAUgD,MAAA,GAAS;MACnBuB,gBAAA,CAAiBI,MAAA,EAAQvC,IAAA,EAAMwC,IAAA,EAAK5E,SAAS;MAC7CsE,YAAA,CAAaK,MAAA,EAAQvC,IAAA,EAAMwC,IAAA,EAAK7E,aAAa;MAE7C,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI+C,MAAA,EAAQ/C,CAAA,IAAK;QAC/BY,MAAA,CAAOC,IAAA,CAAKqC,SAAA,CAAUC,IAAA,CAAKhF,aAAA,CAAc6B,CAAA,GAAI,CAAC,GAAG5B,SAAA,CAAU4B,CAAA,GAAI,CAAC,GAAGiD,MAAM,CAAC;MAC3E;MAEDrC,MAAA,CAAOC,IAAA,CAAK,CAAC;IACd;EACF;EAEDuC,OAAA,EAAS;IACP,MAAM5E,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgB,QAAA,GAAW,KAAKA,QAAA;IACtB,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAIR,QAAA,CAAS4B,MAAA,EAAQpB,CAAA,IAAK;MACxC,MAAMC,KAAA,GAAQ,KAAKP,MAAA,CAAOM,CAAC;MAC3B,MAAMqB,SAAA,GAAYpB,KAAA,CAAMG,MAAA,CAAO5B,MAAA;MAC/B,MAAM6E,UAAA,IAAchC,SAAA,CAAUgB,KAAA,GAAQhB,SAAA,CAAUe,IAAA,IAAQ,KAAKvD,aAAA;MAC7D,MAAMyE,WAAA,IAAejC,SAAA,CAAUiB,GAAA,GAAMjB,SAAA,CAAUkB,MAAA,IAAU,KAAK1D,aAAA;MAC9DoB,KAAA,CAAMG,MAAA,CAAO5B,MAAA,CAAO+E,iBAAA,CAAkB,IAAI;MAC1C5F,oBAAA,CAAqB6F,gBAAA,CAAiBvD,KAAA,CAAMG,MAAA,CAAO5B,MAAA,CAAOiF,kBAAA,EAAoBjF,MAAA,CAAOkF,WAAW;MAChGlE,QAAA,CAASQ,CAAC,EAAE2D,OAAA,CAAQhG,oBAAA,EAAsBE,kBAAkB;MAE5D,MAAM0D,SAAA,GAAY1D,kBAAA,CAAmB2D,QAAA,CAAShB,IAAA;MAC9C,MAAMiB,QAAA,GAAW5D,kBAAA,CAAmB2D,QAAA,CAASf,GAAA;MAC7CxC,KAAA,CAAM2F,SAAA,CAAW;MACjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B5F,KAAA,CAAM6F,aAAA,CAAcvC,SAAA,CAAUsC,CAAC,CAAC;QAChC5F,KAAA,CAAM6F,aAAA,CAAcrC,QAAA,CAASoC,CAAC,CAAC;MAChC;MAED5F,KAAA,CAAM8F,SAAA,CAAUhG,OAAO;MACvBA,OAAA,CAAQkE,CAAA,GAAIhE,KAAA,CAAM8D,GAAA,CAAIE,CAAA,GAAI,KAAK7C,WAAA;MAC/BrB,OAAA,CAAQiG,CAAA,GAAIlC,IAAA,CAAKmC,KAAA,CAAMlG,OAAA,CAAQiG,CAAA,GAAIX,UAAU,IAAIA,UAAA;MACjDtF,OAAA,CAAQmG,CAAA,GAAIpC,IAAA,CAAKmC,KAAA,CAAMlG,OAAA,CAAQmG,CAAA,GAAIZ,WAAW,IAAIA,WAAA;MAClDvF,OAAA,CAAQoG,YAAA,CAAalE,KAAA,CAAMG,MAAA,CAAO5B,MAAA,CAAOkF,WAAW;MAEpDzD,KAAA,CAAMmE,QAAA,CAASC,IAAA,CAAKtG,OAAO;MAC3BkC,KAAA,CAAMW,MAAA,CAAOwD,QAAA,CAASC,IAAA,CAAKtG,OAAO;MAElCkC,KAAA,CAAMW,MAAA,CAAOwD,QAAA,CAASJ,CAAA,IAAK,KAAKjF,cAAA,CAAeiF,CAAA;MAC/C/D,KAAA,CAAMW,MAAA,CAAOwD,QAAA,CAASF,CAAA,IAAK,KAAKnF,cAAA,CAAemF,CAAA;MAC/CjE,KAAA,CAAMW,MAAA,CAAOwD,QAAA,CAASnC,CAAA,IAAK,KAAKlD,cAAA,CAAekD,CAAA;IAChD;EACF;EAEDlC,cAAA,EAAgB;IACduE,WAAA,CAAYC,qBAAA,GAAwBC,SAAA,CAAUD,qBAAA;IAC9CD,WAAA,CAAYG,iBAAA,GAAoBD,SAAA,CAAUC,iBAAA;EAC3C;EAEDC,cAAcC,QAAA,EAAU;IACtBA,QAAA,CAASC,OAAA,GAAUD,QAAA,CAASC,OAAA,IAAW,CAAE;IACzCD,QAAA,CAASC,OAAA,CAAQC,OAAA,GAAU;IAC3BF,QAAA,CAASC,OAAA,CAAQE,YAAA,GAAe,KAAKpG,QAAA;IAErC,IAAI,KAAKY,IAAA,EAAM;MACbqF,QAAA,CAASC,OAAA,CAAQG,QAAA,GAAW;IAC7B;IAED,MAAMC,UAAA,GAAa,EAAE;IACrB,MAAMC,KAAA,GAAQ;IACd,MAAMtF,OAAA,GAAU,KAAKA,OAAA;IAErBgF,QAAA,CAASO,eAAA,GAAkB,UAAUC,MAAA,EAAQ;MAC3C,MAAM1E,GAAA,GAAMqB,IAAA,CAAKW,GAAA,CAAIwC,KAAA,CAAMzG,MAAA,CAAOiC,GAAA,EAAKwE,KAAA,CAAMtG,MAAM;MACnDsG,KAAA,CAAMG,iBAAA,CAAkBJ,UAAU;MAElCG,MAAA,CAAOE,QAAA,CAASC,YAAA,GAAe;QAAEC,KAAA,EAAOP;MAAY;MACpDG,MAAA,CAAOE,QAAA,CAASG,UAAA,GAAa;QAAED,KAAA,EAAON,KAAA,CAAMzG,MAAA,CAAOgC;MAAM;MACzD2E,MAAA,CAAOE,QAAA,CAASI,SAAA,GAAY;QAAEF,KAAA,EAAO9E;MAAK;MAE1Cd,OAAA,CAAQ+F,GAAA,CAAIf,QAAA,EAAUQ,MAAM;IAC7B;IAEDxF,OAAA,CAAQ+F,GAAA,CAAIf,QAAA,EAAU,IAAI;EAC3B;EAEDgB,eAAA,EAAiB;IACf,MAAMlF,GAAA,GAAMqB,IAAA,CAAKW,GAAA,CAAI,KAAKjE,MAAA,CAAOiC,GAAA,EAAK,KAAK9B,MAAM;IACjD,MAAMgB,OAAA,GAAU,KAAKA,OAAA;IAErBA,OAAA,CAAQiG,OAAA,CAAQ,UAAUT,MAAA,EAAQR,QAAA,EAAU;MAC1C,IAAIQ,MAAA,KAAW,MAAM;QACnB,MAAME,QAAA,GAAWF,MAAA,CAAOE,QAAA;QACxB,KAAKD,iBAAA,CAAkBC,QAAA,CAASC,YAAA,CAAaC,KAAK;QAClDF,QAAA,CAASG,UAAA,CAAWD,KAAA,GAAQ,KAAK/G,MAAA,CAAOgC,IAAA;QACxC6E,QAAA,CAASI,SAAA,CAAUF,KAAA,GAAQ9E,GAAA;MAC5B;MAED,IAAI,CAAC,KAAKnB,IAAA,IAAQ,cAAcqF,QAAA,CAASC,OAAA,EAAS;QAChD,OAAOD,QAAA,CAASC,OAAA,CAAQG,QAAA;QACxBJ,QAAA,CAASkB,WAAA,GAAc;MAC/B,WAAiB,KAAKvG,IAAA,IAAQ,EAAE,cAAcqF,QAAA,CAASC,OAAA,GAAU;QACzDD,QAAA,CAASC,OAAA,CAAQG,QAAA,GAAW;QAC5BJ,QAAA,CAASkB,WAAA,GAAc;MACxB;IACF,GAAE,IAAI;EACR;EAEDT,kBAAkBxE,MAAA,EAAQ;IACxB,OAAOA,MAAA,CAAOQ,MAAA,GAAS,KAAK3B,MAAA,CAAO2B,MAAA,EAAQ;MACzCR,MAAA,CAAOC,IAAA,CAAK,IAAIiF,OAAA,EAAS;IAC1B;IAEDlF,MAAA,CAAOQ,MAAA,GAAS,KAAK3B,MAAA,CAAO2B,MAAA;IAE5B,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKtB,QAAA,EAAUsB,CAAA,IAAK;MACtC,MAAM+C,MAAA,GAAS,KAAKtD,MAAA,CAAOO,CAAC;MAC5B,MAAM+F,IAAA,GAAO,KAAKtG,MAAA,CAAOO,CAAA,GAAI,CAAC,KAAK;MACnCY,MAAA,CAAOZ,CAAC,EAAEgE,CAAA,GAAI+B,IAAA;MACdnF,MAAA,CAAOZ,CAAC,EAAEkE,CAAA,GAAInB,MAAA;IACf;EACF;EAEDjD,eAAA,EAAiB;IACf,KAAK0C,SAAA,CAAW;IAChB,KAAK1B,YAAA,CAAc;IACnB,KAAKK,kBAAA,CAAoB;IACzB,KAAKwE,cAAA,CAAgB;EACtB;EAEDK,OAAA,EAAS;IACP,SAAShG,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKN,MAAA,CAAO0B,MAAA,EAAQpB,CAAA,IAAK;MAC3C,KAAKvB,MAAA,CAAOuH,MAAA,CAAO,KAAKtG,MAAA,CAAOM,CAAC,CAAC;IAClC;EACF;EAEDiG,QAAA,EAAU;IACR,MAAMtG,OAAA,GAAU,KAAKA,OAAA;IACrBA,OAAA,CAAQiG,OAAA,CAAQ,UAAUT,MAAA,EAAQR,QAAA,EAAU;MAC1C,OAAOA,QAAA,CAASO,eAAA;MAChB,OAAOP,QAAA,CAASC,OAAA,CAAQC,OAAA;MACxB,OAAOF,QAAA,CAASC,OAAA,CAAQE,YAAA;MACxB,OAAOH,QAAA,CAASC,OAAA,CAAQG,QAAA;MAExB,IAAII,MAAA,KAAW,MAAM;QACnB,OAAOA,MAAA,CAAOE,QAAA,CAASC,YAAA;QACvB,OAAOH,MAAA,CAAOE,QAAA,CAASG,UAAA;QACvB,OAAOL,MAAA,CAAOE,QAAA,CAASI,SAAA;MACxB;MAEDd,QAAA,CAASkB,WAAA,GAAc;IAC7B,CAAK;IACDlG,OAAA,CAAQuG,KAAA,CAAO;EAChB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}