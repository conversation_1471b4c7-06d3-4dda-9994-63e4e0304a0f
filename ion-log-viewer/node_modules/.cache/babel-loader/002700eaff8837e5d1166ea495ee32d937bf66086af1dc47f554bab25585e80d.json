{"ast": null, "code": "const WaterRefractionShader = {\n  uniforms: {\n    color: {\n      value: null\n    },\n    time: {\n      value: 0\n    },\n    tDiffuse: {\n      value: null\n    },\n    tDudv: {\n      value: null\n    },\n    textureMatrix: {\n      value: null\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    uniform mat4 textureMatrix;\n\n    varying vec2 vUv;\n    varying vec4 vUvRefraction;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tvUvRefraction = textureMatrix * vec4( position, 1.0 );\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 color;\n    uniform float time;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tDudv;\n\n    varying vec2 vUv;\n    varying vec4 vUvRefraction;\n\n    float blendOverlay( float base, float blend ) {\n\n    \treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n    }\n\n    vec3 blendOverlay( vec3 base, vec3 blend ) {\n\n    \treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ),blendOverlay( base.b, blend.b ) );\n\n    }\n\n    void main() {\n\n     float waveStrength = 0.1;\n     float waveSpeed = 0.03;\n\n    // simple distortion (ripple) via dudv map (see https://www.youtube.com/watch?v=6B7IF6GOu7s)\n\n    \tvec2 distortedUv = texture2D( tDudv, vec2( vUv.x + time * waveSpeed, vUv.y ) ).rg * waveStrength;\n    \tdistortedUv = vUv.xy + vec2( distortedUv.x, distortedUv.y + time * waveSpeed );\n    \tvec2 distortion = ( texture2D( tDudv, distortedUv ).rg * 2.0 - 1.0 ) * waveStrength;\n\n    // new uv coords\n\n     vec4 uv = vec4( vUvRefraction );\n     uv.xy += distortion;\n\n    \tvec4 base = texture2DProj( tDiffuse, uv );\n\n    \tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n    }\n  `)\n};\nexport { WaterRefractionShader };", "map": {"version": 3, "names": ["WaterRefractionShader", "uniforms", "color", "value", "time", "tDiffuse", "tDudv", "textureMatrix", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/shaders/WaterRefractionShader.ts"], "sourcesContent": ["export const WaterRefractionShader = {\n  uniforms: {\n    color: {\n      value: null,\n    },\n\n    time: {\n      value: 0,\n    },\n\n    tDiffuse: {\n      value: null,\n    },\n\n    tDudv: {\n      value: null,\n    },\n\n    textureMatrix: {\n      value: null,\n    },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform mat4 textureMatrix;\n\n    varying vec2 vUv;\n    varying vec4 vUvRefraction;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tvUvRefraction = textureMatrix * vec4( position, 1.0 );\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 color;\n    uniform float time;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tDudv;\n\n    varying vec2 vUv;\n    varying vec4 vUvRefraction;\n\n    float blendOverlay( float base, float blend ) {\n\n    \treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n    }\n\n    vec3 blendOverlay( vec3 base, vec3 blend ) {\n\n    \treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ),blendOverlay( base.b, blend.b ) );\n\n    }\n\n    void main() {\n\n     float waveStrength = 0.1;\n     float waveSpeed = 0.03;\n\n    // simple distortion (ripple) via dudv map (see https://www.youtube.com/watch?v=6B7IF6GOu7s)\n\n    \tvec2 distortedUv = texture2D( tDudv, vec2( vUv.x + time * waveSpeed, vUv.y ) ).rg * waveStrength;\n    \tdistortedUv = vUv.xy + vec2( distortedUv.x, distortedUv.y + time * waveSpeed );\n    \tvec2 distortion = ( texture2D( tDudv, distortedUv ).rg * 2.0 - 1.0 ) * waveStrength;\n\n    // new uv coords\n\n     vec4 uv = vec4( vUvRefraction );\n     uv.xy += distortion;\n\n    \tvec4 base = texture2DProj( tDiffuse, uv );\n\n    \tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n    }\n  `,\n}\n"], "mappings": "AAAO,MAAMA,qBAAA,GAAwB;EACnCC,QAAA,EAAU;IACRC,KAAA,EAAO;MACLC,KAAA,EAAO;IACT;IAEAC,IAAA,EAAM;MACJD,KAAA,EAAO;IACT;IAEAE,QAAA,EAAU;MACRF,KAAA,EAAO;IACT;IAEAG,KAAA,EAAO;MACLH,KAAA,EAAO;IACT;IAEAI,aAAA,EAAe;MACbJ,KAAA,EAAO;IACT;EACF;EAEAK,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAiBzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2C7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}