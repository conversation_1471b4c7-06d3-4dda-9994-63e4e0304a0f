{"ast": null, "code": "import { CompressedTextureLoader, RGBA_PVRTC_4BPPV1_Format, RGB_PVRTC_4BPPV1_Format, RGBA_PVRTC_2BPPV1_Format, RGB_PVRTC_2BPPV1_Format } from \"three\";\nclass PVRLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager);\n  }\n  parse(buffer, loadMipmaps) {\n    const headerLengthInt = 13;\n    const header = new Uint32Array(buffer, 0, headerLengthInt);\n    const pvrDatas = {\n      buffer,\n      header,\n      loadMipmaps\n    };\n    if (header[0] === 55727696) {\n      return _parseV3(pvrDatas);\n    } else if (header[11] === 559044176) {\n      return _parseV2(pvrDatas);\n    } else {\n      console.error(\"THREE.PVRLoader: Unknown PVR format.\");\n    }\n  }\n}\nfunction _parseV3(pvrDatas) {\n  const header = pvrDatas.header;\n  let bpp, format;\n  const metaLen = header[12],\n    pixelFormat = header[2],\n    height = header[6],\n    width = header[7],\n    numFaces = header[10],\n    numMipmaps = header[11];\n  switch (pixelFormat) {\n    case 0:\n      bpp = 2;\n      format = RGB_PVRTC_2BPPV1_Format;\n      break;\n    case 1:\n      bpp = 2;\n      format = RGBA_PVRTC_2BPPV1_Format;\n      break;\n    case 2:\n      bpp = 4;\n      format = RGB_PVRTC_4BPPV1_Format;\n      break;\n    case 3:\n      bpp = 4;\n      format = RGBA_PVRTC_4BPPV1_Format;\n      break;\n    default:\n      console.error(\"THREE.PVRLoader: Unsupported PVR format:\", pixelFormat);\n  }\n  pvrDatas.dataPtr = 52 + metaLen;\n  pvrDatas.bpp = bpp;\n  pvrDatas.format = format;\n  pvrDatas.width = width;\n  pvrDatas.height = height;\n  pvrDatas.numSurfaces = numFaces;\n  pvrDatas.numMipmaps = numMipmaps;\n  pvrDatas.isCubemap = numFaces === 6;\n  return _extract(pvrDatas);\n}\nfunction _parseV2(pvrDatas) {\n  const header = pvrDatas.header;\n  const headerLength = header[0],\n    height = header[1],\n    width = header[2],\n    numMipmaps = header[3],\n    flags = header[4],\n    bitmaskAlpha = header[10],\n    numSurfs = header[12];\n  const TYPE_MASK = 255;\n  const PVRTC_2 = 24,\n    PVRTC_4 = 25;\n  const formatFlags = flags & TYPE_MASK;\n  let bpp, format;\n  const _hasAlpha = bitmaskAlpha > 0;\n  if (formatFlags === PVRTC_4) {\n    format = _hasAlpha ? RGBA_PVRTC_4BPPV1_Format : RGB_PVRTC_4BPPV1_Format;\n    bpp = 4;\n  } else if (formatFlags === PVRTC_2) {\n    format = _hasAlpha ? RGBA_PVRTC_2BPPV1_Format : RGB_PVRTC_2BPPV1_Format;\n    bpp = 2;\n  } else {\n    console.error(\"THREE.PVRLoader: Unknown PVR format:\", formatFlags);\n  }\n  pvrDatas.dataPtr = headerLength;\n  pvrDatas.bpp = bpp;\n  pvrDatas.format = format;\n  pvrDatas.width = width;\n  pvrDatas.height = height;\n  pvrDatas.numSurfaces = numSurfs;\n  pvrDatas.numMipmaps = numMipmaps + 1;\n  pvrDatas.isCubemap = numSurfs === 6;\n  return _extract(pvrDatas);\n}\nfunction _extract(pvrDatas) {\n  const pvr = {\n    mipmaps: [],\n    width: pvrDatas.width,\n    height: pvrDatas.height,\n    format: pvrDatas.format,\n    mipmapCount: pvrDatas.numMipmaps,\n    isCubemap: pvrDatas.isCubemap\n  };\n  const buffer = pvrDatas.buffer;\n  let dataOffset = pvrDatas.dataPtr,\n    dataSize = 0,\n    blockSize = 0,\n    blockWidth = 0,\n    blockHeight = 0,\n    widthBlocks = 0,\n    heightBlocks = 0;\n  const bpp = pvrDatas.bpp,\n    numSurfs = pvrDatas.numSurfaces;\n  if (bpp === 2) {\n    blockWidth = 8;\n    blockHeight = 4;\n  } else {\n    blockWidth = 4;\n    blockHeight = 4;\n  }\n  blockSize = blockWidth * blockHeight * bpp / 8;\n  pvr.mipmaps.length = pvrDatas.numMipmaps * numSurfs;\n  let mipLevel = 0;\n  while (mipLevel < pvrDatas.numMipmaps) {\n    const sWidth = pvrDatas.width >> mipLevel,\n      sHeight = pvrDatas.height >> mipLevel;\n    widthBlocks = sWidth / blockWidth;\n    heightBlocks = sHeight / blockHeight;\n    if (widthBlocks < 2) widthBlocks = 2;\n    if (heightBlocks < 2) heightBlocks = 2;\n    dataSize = widthBlocks * heightBlocks * blockSize;\n    for (let surfIndex = 0; surfIndex < numSurfs; surfIndex++) {\n      const byteArray = new Uint8Array(buffer, dataOffset, dataSize);\n      const mipmap = {\n        data: byteArray,\n        width: sWidth,\n        height: sHeight\n      };\n      pvr.mipmaps[surfIndex * pvrDatas.numMipmaps + mipLevel] = mipmap;\n      dataOffset += dataSize;\n    }\n    mipLevel++;\n  }\n  return pvr;\n}\nexport { PVRLoader };", "map": {"version": 3, "names": ["P<PERSON><PERSON><PERSON>der", "CompressedTextureLoader", "constructor", "manager", "parse", "buffer", "loadMipmaps", "headerLengthInt", "header", "Uint32Array", "pvrDatas", "_parseV3", "_parseV2", "console", "error", "bpp", "format", "metaLen", "pixelFormat", "height", "width", "numFaces", "numMipmaps", "RGB_PVRTC_2BPPV1_Format", "RGBA_PVRTC_2BPPV1_Format", "RGB_PVRTC_4BPPV1_Format", "RGBA_PVRTC_4BPPV1_Format", "dataPtr", "numSurfaces", "isCubemap", "_extract", "<PERSON><PERSON><PERSON><PERSON>", "flags", "bitmaskAlpha", "numSurfs", "TYPE_MASK", "PVRTC_2", "PVRTC_4", "formatFlags", "_hasAlpha", "pvr", "mipmaps", "mipmapCount", "dataOffset", "dataSize", "blockSize", "blockWidth", "blockHeight", "widthBlocks", "heightBlocks", "length", "mipLevel", "sWidth", "sHeight", "surfIndex", "byteArray", "Uint8Array", "mipmap", "data"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/PVRLoader.js"], "sourcesContent": ["import {\n  CompressedTextureLoader,\n  RGBA_PVRTC_2BPPV1_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGB_PVRTC_2BPPV1_Format,\n  RGB_PVRTC_4BPPV1_Format,\n} from 'three'\n\n/*\n *\t PVR v2 (legacy) parser\n *   TODO : Add Support for PVR v3 format\n *   TODO : implement loadMipmaps option\n */\n\nclass PVRLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer, loadMipmaps) {\n    const headerLengthInt = 13\n    const header = new Uint32Array(buffer, 0, headerLengthInt)\n\n    const pvrDatas = {\n      buffer: buffer,\n      header: header,\n      loadMipmaps: loadMipmaps,\n    }\n\n    if (header[0] === 0x03525650) {\n      // PVR v3\n\n      return _parseV3(pvrDatas)\n    } else if (header[11] === 0x21525650) {\n      // PVR v2\n\n      return _parseV2(pvrDatas)\n    } else {\n      console.error('THREE.PVRLoader: Unknown PVR format.')\n    }\n  }\n}\n\nfunction _parseV3(pvrDatas) {\n  const header = pvrDatas.header\n  let bpp, format\n\n  const metaLen = header[12],\n    pixelFormat = header[2],\n    height = header[6],\n    width = header[7],\n    // numSurfs = header[ 9 ],\n    numFaces = header[10],\n    numMipmaps = header[11]\n\n  switch (pixelFormat) {\n    case 0: // PVRTC 2bpp RGB\n      bpp = 2\n      format = RGB_PVRTC_2BPPV1_Format\n      break\n\n    case 1: // PVRTC 2bpp RGBA\n      bpp = 2\n      format = RGBA_PVRTC_2BPPV1_Format\n      break\n\n    case 2: // PVRTC 4bpp RGB\n      bpp = 4\n      format = RGB_PVRTC_4BPPV1_Format\n      break\n\n    case 3: // PVRTC 4bpp RGBA\n      bpp = 4\n      format = RGBA_PVRTC_4BPPV1_Format\n      break\n\n    default:\n      console.error('THREE.PVRLoader: Unsupported PVR format:', pixelFormat)\n  }\n\n  pvrDatas.dataPtr = 52 + metaLen\n  pvrDatas.bpp = bpp\n  pvrDatas.format = format\n  pvrDatas.width = width\n  pvrDatas.height = height\n  pvrDatas.numSurfaces = numFaces\n  pvrDatas.numMipmaps = numMipmaps\n  pvrDatas.isCubemap = numFaces === 6\n\n  return _extract(pvrDatas)\n}\n\nfunction _parseV2(pvrDatas) {\n  const header = pvrDatas.header\n\n  const headerLength = header[0],\n    height = header[1],\n    width = header[2],\n    numMipmaps = header[3],\n    flags = header[4],\n    // dataLength = header[ 5 ],\n    // bpp =  header[ 6 ],\n    // bitmaskRed = header[ 7 ],\n    // bitmaskGreen = header[ 8 ],\n    // bitmaskBlue = header[ 9 ],\n    bitmaskAlpha = header[10],\n    // pvrTag = header[ 11 ],\n    numSurfs = header[12]\n\n  const TYPE_MASK = 0xff\n  const PVRTC_2 = 24,\n    PVRTC_4 = 25\n\n  const formatFlags = flags & TYPE_MASK\n\n  let bpp, format\n  const _hasAlpha = bitmaskAlpha > 0\n\n  if (formatFlags === PVRTC_4) {\n    format = _hasAlpha ? RGBA_PVRTC_4BPPV1_Format : RGB_PVRTC_4BPPV1_Format\n    bpp = 4\n  } else if (formatFlags === PVRTC_2) {\n    format = _hasAlpha ? RGBA_PVRTC_2BPPV1_Format : RGB_PVRTC_2BPPV1_Format\n    bpp = 2\n  } else {\n    console.error('THREE.PVRLoader: Unknown PVR format:', formatFlags)\n  }\n\n  pvrDatas.dataPtr = headerLength\n  pvrDatas.bpp = bpp\n  pvrDatas.format = format\n  pvrDatas.width = width\n  pvrDatas.height = height\n  pvrDatas.numSurfaces = numSurfs\n  pvrDatas.numMipmaps = numMipmaps + 1\n\n  // guess cubemap type seems tricky in v2\n  // it juste a pvr containing 6 surface (no explicit cubemap type)\n  pvrDatas.isCubemap = numSurfs === 6\n\n  return _extract(pvrDatas)\n}\n\nfunction _extract(pvrDatas) {\n  const pvr = {\n    mipmaps: [],\n    width: pvrDatas.width,\n    height: pvrDatas.height,\n    format: pvrDatas.format,\n    mipmapCount: pvrDatas.numMipmaps,\n    isCubemap: pvrDatas.isCubemap,\n  }\n\n  const buffer = pvrDatas.buffer\n\n  let dataOffset = pvrDatas.dataPtr,\n    dataSize = 0,\n    blockSize = 0,\n    blockWidth = 0,\n    blockHeight = 0,\n    widthBlocks = 0,\n    heightBlocks = 0\n\n  const bpp = pvrDatas.bpp,\n    numSurfs = pvrDatas.numSurfaces\n\n  if (bpp === 2) {\n    blockWidth = 8\n    blockHeight = 4\n  } else {\n    blockWidth = 4\n    blockHeight = 4\n  }\n\n  blockSize = (blockWidth * blockHeight * bpp) / 8\n\n  pvr.mipmaps.length = pvrDatas.numMipmaps * numSurfs\n\n  let mipLevel = 0\n\n  while (mipLevel < pvrDatas.numMipmaps) {\n    const sWidth = pvrDatas.width >> mipLevel,\n      sHeight = pvrDatas.height >> mipLevel\n\n    widthBlocks = sWidth / blockWidth\n    heightBlocks = sHeight / blockHeight\n\n    // Clamp to minimum number of blocks\n    if (widthBlocks < 2) widthBlocks = 2\n    if (heightBlocks < 2) heightBlocks = 2\n\n    dataSize = widthBlocks * heightBlocks * blockSize\n\n    for (let surfIndex = 0; surfIndex < numSurfs; surfIndex++) {\n      const byteArray = new Uint8Array(buffer, dataOffset, dataSize)\n\n      const mipmap = {\n        data: byteArray,\n        width: sWidth,\n        height: sHeight,\n      }\n\n      pvr.mipmaps[surfIndex * pvrDatas.numMipmaps + mipLevel] = mipmap\n\n      dataOffset += dataSize\n    }\n\n    mipLevel++\n  }\n\n  return pvr\n}\n\nexport { PVRLoader }\n"], "mappings": ";AAcA,MAAMA,SAAA,SAAkBC,uBAAA,CAAwB;EAC9CC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,MAAMC,MAAA,EAAQC,WAAA,EAAa;IACzB,MAAMC,eAAA,GAAkB;IACxB,MAAMC,MAAA,GAAS,IAAIC,WAAA,CAAYJ,MAAA,EAAQ,GAAGE,eAAe;IAEzD,MAAMG,QAAA,GAAW;MACfL,MAAA;MACAG,MAAA;MACAF;IACD;IAED,IAAIE,MAAA,CAAO,CAAC,MAAM,UAAY;MAG5B,OAAOG,QAAA,CAASD,QAAQ;IACzB,WAAUF,MAAA,CAAO,EAAE,MAAM,WAAY;MAGpC,OAAOI,QAAA,CAASF,QAAQ;IAC9B,OAAW;MACLG,OAAA,CAAQC,KAAA,CAAM,sCAAsC;IACrD;EACF;AACH;AAEA,SAASH,SAASD,QAAA,EAAU;EAC1B,MAAMF,MAAA,GAASE,QAAA,CAASF,MAAA;EACxB,IAAIO,GAAA,EAAKC,MAAA;EAET,MAAMC,OAAA,GAAUT,MAAA,CAAO,EAAE;IACvBU,WAAA,GAAcV,MAAA,CAAO,CAAC;IACtBW,MAAA,GAASX,MAAA,CAAO,CAAC;IACjBY,KAAA,GAAQZ,MAAA,CAAO,CAAC;IAEhBa,QAAA,GAAWb,MAAA,CAAO,EAAE;IACpBc,UAAA,GAAad,MAAA,CAAO,EAAE;EAExB,QAAQU,WAAA;IACN,KAAK;MACHH,GAAA,GAAM;MACNC,MAAA,GAASO,uBAAA;MACT;IAEF,KAAK;MACHR,GAAA,GAAM;MACNC,MAAA,GAASQ,wBAAA;MACT;IAEF,KAAK;MACHT,GAAA,GAAM;MACNC,MAAA,GAASS,uBAAA;MACT;IAEF,KAAK;MACHV,GAAA,GAAM;MACNC,MAAA,GAASU,wBAAA;MACT;IAEF;MACEb,OAAA,CAAQC,KAAA,CAAM,4CAA4CI,WAAW;EACxE;EAEDR,QAAA,CAASiB,OAAA,GAAU,KAAKV,OAAA;EACxBP,QAAA,CAASK,GAAA,GAAMA,GAAA;EACfL,QAAA,CAASM,MAAA,GAASA,MAAA;EAClBN,QAAA,CAASU,KAAA,GAAQA,KAAA;EACjBV,QAAA,CAASS,MAAA,GAASA,MAAA;EAClBT,QAAA,CAASkB,WAAA,GAAcP,QAAA;EACvBX,QAAA,CAASY,UAAA,GAAaA,UAAA;EACtBZ,QAAA,CAASmB,SAAA,GAAYR,QAAA,KAAa;EAElC,OAAOS,QAAA,CAASpB,QAAQ;AAC1B;AAEA,SAASE,SAASF,QAAA,EAAU;EAC1B,MAAMF,MAAA,GAASE,QAAA,CAASF,MAAA;EAExB,MAAMuB,YAAA,GAAevB,MAAA,CAAO,CAAC;IAC3BW,MAAA,GAASX,MAAA,CAAO,CAAC;IACjBY,KAAA,GAAQZ,MAAA,CAAO,CAAC;IAChBc,UAAA,GAAad,MAAA,CAAO,CAAC;IACrBwB,KAAA,GAAQxB,MAAA,CAAO,CAAC;IAMhByB,YAAA,GAAezB,MAAA,CAAO,EAAE;IAExB0B,QAAA,GAAW1B,MAAA,CAAO,EAAE;EAEtB,MAAM2B,SAAA,GAAY;EAClB,MAAMC,OAAA,GAAU;IACdC,OAAA,GAAU;EAEZ,MAAMC,WAAA,GAAcN,KAAA,GAAQG,SAAA;EAE5B,IAAIpB,GAAA,EAAKC,MAAA;EACT,MAAMuB,SAAA,GAAYN,YAAA,GAAe;EAEjC,IAAIK,WAAA,KAAgBD,OAAA,EAAS;IAC3BrB,MAAA,GAASuB,SAAA,GAAYb,wBAAA,GAA2BD,uBAAA;IAChDV,GAAA,GAAM;EACV,WAAauB,WAAA,KAAgBF,OAAA,EAAS;IAClCpB,MAAA,GAASuB,SAAA,GAAYf,wBAAA,GAA2BD,uBAAA;IAChDR,GAAA,GAAM;EACV,OAAS;IACLF,OAAA,CAAQC,KAAA,CAAM,wCAAwCwB,WAAW;EAClE;EAED5B,QAAA,CAASiB,OAAA,GAAUI,YAAA;EACnBrB,QAAA,CAASK,GAAA,GAAMA,GAAA;EACfL,QAAA,CAASM,MAAA,GAASA,MAAA;EAClBN,QAAA,CAASU,KAAA,GAAQA,KAAA;EACjBV,QAAA,CAASS,MAAA,GAASA,MAAA;EAClBT,QAAA,CAASkB,WAAA,GAAcM,QAAA;EACvBxB,QAAA,CAASY,UAAA,GAAaA,UAAA,GAAa;EAInCZ,QAAA,CAASmB,SAAA,GAAYK,QAAA,KAAa;EAElC,OAAOJ,QAAA,CAASpB,QAAQ;AAC1B;AAEA,SAASoB,SAASpB,QAAA,EAAU;EAC1B,MAAM8B,GAAA,GAAM;IACVC,OAAA,EAAS,EAAE;IACXrB,KAAA,EAAOV,QAAA,CAASU,KAAA;IAChBD,MAAA,EAAQT,QAAA,CAASS,MAAA;IACjBH,MAAA,EAAQN,QAAA,CAASM,MAAA;IACjB0B,WAAA,EAAahC,QAAA,CAASY,UAAA;IACtBO,SAAA,EAAWnB,QAAA,CAASmB;EACrB;EAED,MAAMxB,MAAA,GAASK,QAAA,CAASL,MAAA;EAExB,IAAIsC,UAAA,GAAajC,QAAA,CAASiB,OAAA;IACxBiB,QAAA,GAAW;IACXC,SAAA,GAAY;IACZC,UAAA,GAAa;IACbC,WAAA,GAAc;IACdC,WAAA,GAAc;IACdC,YAAA,GAAe;EAEjB,MAAMlC,GAAA,GAAML,QAAA,CAASK,GAAA;IACnBmB,QAAA,GAAWxB,QAAA,CAASkB,WAAA;EAEtB,IAAIb,GAAA,KAAQ,GAAG;IACb+B,UAAA,GAAa;IACbC,WAAA,GAAc;EAClB,OAAS;IACLD,UAAA,GAAa;IACbC,WAAA,GAAc;EACf;EAEDF,SAAA,GAAaC,UAAA,GAAaC,WAAA,GAAchC,GAAA,GAAO;EAE/CyB,GAAA,CAAIC,OAAA,CAAQS,MAAA,GAASxC,QAAA,CAASY,UAAA,GAAaY,QAAA;EAE3C,IAAIiB,QAAA,GAAW;EAEf,OAAOA,QAAA,GAAWzC,QAAA,CAASY,UAAA,EAAY;IACrC,MAAM8B,MAAA,GAAS1C,QAAA,CAASU,KAAA,IAAS+B,QAAA;MAC/BE,OAAA,GAAU3C,QAAA,CAASS,MAAA,IAAUgC,QAAA;IAE/BH,WAAA,GAAcI,MAAA,GAASN,UAAA;IACvBG,YAAA,GAAeI,OAAA,GAAUN,WAAA;IAGzB,IAAIC,WAAA,GAAc,GAAGA,WAAA,GAAc;IACnC,IAAIC,YAAA,GAAe,GAAGA,YAAA,GAAe;IAErCL,QAAA,GAAWI,WAAA,GAAcC,YAAA,GAAeJ,SAAA;IAExC,SAASS,SAAA,GAAY,GAAGA,SAAA,GAAYpB,QAAA,EAAUoB,SAAA,IAAa;MACzD,MAAMC,SAAA,GAAY,IAAIC,UAAA,CAAWnD,MAAA,EAAQsC,UAAA,EAAYC,QAAQ;MAE7D,MAAMa,MAAA,GAAS;QACbC,IAAA,EAAMH,SAAA;QACNnC,KAAA,EAAOgC,MAAA;QACPjC,MAAA,EAAQkC;MACT;MAEDb,GAAA,CAAIC,OAAA,CAAQa,SAAA,GAAY5C,QAAA,CAASY,UAAA,GAAa6B,QAAQ,IAAIM,MAAA;MAE1Dd,UAAA,IAAcC,QAAA;IACf;IAEDO,QAAA;EACD;EAED,OAAOX,GAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}