{"ast": null, "code": "import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\n\n// ION value handlers for both ION DOM and plain JavaScript objects\nexport class IonValueHandler {\n  static handleValue(value) {\n    var _value$timestampValue;\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        switch (ionType) {\n          case ion.IonTypes.NULL:\n            return null;\n          case ion.IonTypes.BOOL:\n            return value.booleanValue();\n          case ion.IonTypes.INT:\n            return value.numberValue();\n          case ion.IonTypes.FLOAT:\n            return value.numberValue();\n          case ion.IonTypes.DECIMAL:\n            return value.numberValue();\n          case ion.IonTypes.TIMESTAMP:\n            return ((_value$timestampValue = value.timestampValue()) === null || _value$timestampValue === void 0 ? void 0 : _value$timestampValue.getTime()) || value.numberValue();\n          case ion.IonTypes.STRING:\n            return value.stringValue();\n          case ion.IonTypes.SYMBOL:\n            return value.stringValue();\n          case ion.IonTypes.BLOB:\n            return value.uInt8ArrayValue();\n          case ion.IonTypes.CLOB:\n            return value.stringValue();\n          case ion.IonTypes.LIST:\n            try {\n              const elements = value.elements();\n              if (elements && typeof elements[Symbol.iterator] === 'function') {\n                return Array.from(elements).map(item => this.handleValue(item));\n              }\n              return [];\n            } catch (error) {\n              console.warn('Error processing ION list:', error);\n              return [];\n            }\n          case ion.IonTypes.SEXP:\n            try {\n              const elements = value.elements();\n              if (elements && typeof elements[Symbol.iterator] === 'function') {\n                return Array.from(elements).map(item => this.handleValue(item));\n              }\n              return [];\n            } catch (error) {\n              console.warn('Error processing ION S-expression:', error);\n              return [];\n            }\n          case ion.IonTypes.STRUCT:\n            try {\n              const result = {};\n              const fields = value.fields();\n              if (fields && typeof fields[Symbol.iterator] === 'function') {\n                for (const field of fields) {\n                  const fieldName = field.fieldName();\n                  if (fieldName) {\n                    result[fieldName] = this.handleValue(field);\n                  }\n                }\n              }\n              return result;\n            } catch (error) {\n              console.warn('Error processing ION struct:', error);\n              return {};\n            }\n          default:\n            console.warn('Unknown ION type:', ionType);\n            return value.value ? value.value() : value;\n        }\n      } catch (error) {\n        console.warn('Error processing ION value:', error);\n        // Fall back to trying to get the raw value\n        try {\n          return value.value ? value.value() : value;\n        } catch {\n          return String(value);\n        }\n      }\n    }\n\n    // Handle plain JavaScript values\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n    if (typeof value === 'object') {\n      const result = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n    return value;\n  }\n  static isHumanReadable(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n        if (ionType === ion.IonTypes.CLOB) return true;\n        if (ionType === ion.IonTypes.LIST || ionType === ion.IonTypes.SEXP) {\n          return value.elements().every(item => this.isHumanReadable(item));\n        }\n        if (ionType === ion.IonTypes.STRUCT) {\n          return value.fields().every(field => this.isHumanReadable(field));\n        }\n        return true;\n      } catch {\n        return true; // Default to human readable if we can't determine\n      }\n    }\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data) {\n  try {\n    let parsedData;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    let processedData;\n    try {\n      processedData = IonValueHandler.handleValue(parsedData);\n      console.log('Processed data:', processedData);\n    } catch (error) {\n      console.error('Error processing ION data:', error);\n      // If processing fails, try to use the raw parsed data\n      processedData = parsedData;\n      console.log('Using raw parsed data due to processing error');\n    }\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    console.log('Extracting session info from:', processedData);\n    const sessionInfo = extractSessionInfo(processedData);\n    console.log('Extracted session info:', sessionInfo);\n\n    // Extract robot information\n    console.log('Extracting robot info from:', processedData);\n    const robotInfo = extractRobotInfo(processedData);\n    console.log('Extracted robot info:', robotInfo);\n\n    // Extract topics\n    console.log('Extracting topics from:', processedData);\n    const topics = extractTopics(processedData);\n    console.log('Extracted topics:', topics.map(t => ({\n      name: t.name,\n      messageCount: t.messages.length\n    })));\n\n    // Calculate time bounds\n    const {\n      startTime,\n      endTime,\n      totalDuration\n    } = calculateTimeBounds(topics);\n    console.log('Extraction complete:', {\n      sessionInfo,\n      robotInfo,\n      topicCount: topics.length\n    });\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\nfunction extractSessionInfo(data) {\n  const sessionInfo = {};\n  console.log('Session extraction - input data keys:', Object.keys(data));\n\n  // Look for session-related fields in various possible locations\n  if (data.session) {\n    console.log('Found session object:', data.session);\n    Object.assign(sessionInfo, data.session);\n  }\n  if (data.metadata) {\n    console.log('Found metadata object:', data.metadata);\n    const metadata = data.metadata;\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  // Look for session info in root level\n  if (data.startTime) sessionInfo.startTime = data.startTime;\n  if (data.endTime) sessionInfo.endTime = data.endTime;\n  if (data.duration) sessionInfo.duration = data.duration;\n  if (data.recordingDate) sessionInfo.recordingDate = data.recordingDate;\n  if (data.version) sessionInfo.version = data.version;\n\n  // Look for common ION log fields\n  if (data.header) {\n    console.log('Found header object:', data.header);\n    const header = data.header;\n    if (header.startTime) sessionInfo.startTime = header.startTime;\n    if (header.endTime) sessionInfo.endTime = header.endTime;\n    if (header.duration) sessionInfo.duration = header.duration;\n  }\n\n  // Look for timestamp-related fields that might indicate session bounds\n  if (data.start_time) sessionInfo.startTime = data.start_time;\n  if (data.end_time) sessionInfo.endTime = data.end_time;\n  if (data.recording_start) sessionInfo.startTime = data.recording_start;\n  if (data.recording_end) sessionInfo.endTime = data.recording_end;\n\n  // Look for version info in various places\n  if (data.file_version) sessionInfo.version = data.file_version;\n  if (data.format_version) sessionInfo.version = data.format_version;\n\n  // Try to extract from any nested structures\n  for (const [key, value] of Object.entries(data)) {\n    if (typeof value === 'object' && value !== null) {\n      // Check if this looks like session metadata\n      if (key.toLowerCase().includes('session') || key.toLowerCase().includes('metadata') || key.toLowerCase().includes('header') || key.toLowerCase().includes('info')) {\n        console.log(`Found potential session data in ${key}:`, value);\n        if (value.startTime) sessionInfo.startTime = value.startTime;\n        if (value.endTime) sessionInfo.endTime = value.endTime;\n        if (value.duration) sessionInfo.duration = value.duration;\n        if (value.recordingDate) sessionInfo.recordingDate = value.recordingDate;\n        if (value.version) sessionInfo.version = value.version;\n        if (value.start_time) sessionInfo.startTime = value.start_time;\n        if (value.end_time) sessionInfo.endTime = value.end_time;\n      }\n    }\n  }\n  console.log('Final session info:', sessionInfo);\n  return sessionInfo;\n}\nfunction extractRobotInfo(data) {\n  const robotInfo = {};\n  console.log('Robot extraction - input data keys:', Object.keys(data));\n\n  // Look for robot-related fields\n  if (data.robot) {\n    console.log('Found robot object:', data.robot);\n    Object.assign(robotInfo, data.robot);\n  }\n  if (data.metadata) {\n    console.log('Found metadata object for robot:', data.metadata);\n    const metadata = data.metadata;\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n    if (metadata.robot_model) robotInfo.model = metadata.robot_model;\n    if (metadata.robot_name) robotInfo.name = metadata.robot_name;\n    if (metadata.robot_version) robotInfo.version = metadata.robot_version;\n  }\n\n  // Look for robot info in root level\n  if (data.robotModel) robotInfo.model = data.robotModel;\n  if (data.robotName) robotInfo.name = data.robotName;\n  if (data.robotVersion) robotInfo.version = data.robotVersion;\n  if (data.botModel) robotInfo.botModel = data.botModel;\n  if (data.robot_model) robotInfo.model = data.robot_model;\n  if (data.robot_name) robotInfo.name = data.robot_name;\n  if (data.robot_version) robotInfo.version = data.robot_version;\n\n  // Look for common robot fields\n  if (data.model) robotInfo.model = data.model;\n  if (data.name) robotInfo.name = data.name;\n  if (data.platform) robotInfo.model = data.platform;\n  if (data.device) robotInfo.model = data.device;\n\n  // Try to extract from any nested structures\n  for (const [key, value] of Object.entries(data)) {\n    if (typeof value === 'object' && value !== null) {\n      // Check if this looks like robot metadata\n      if (key.toLowerCase().includes('robot') || key.toLowerCase().includes('device') || key.toLowerCase().includes('platform') || key.toLowerCase().includes('system')) {\n        console.log(`Found potential robot data in ${key}:`, value);\n        if (value.model) robotInfo.model = value.model;\n        if (value.name) robotInfo.name = value.name;\n        if (value.version) robotInfo.version = value.version;\n        if (value.robot_model) robotInfo.model = value.robot_model;\n        if (value.robot_name) robotInfo.name = value.robot_name;\n        if (value.robot_version) robotInfo.version = value.robot_version;\n        if (value.botModel) robotInfo.botModel = value.botModel;\n      }\n    }\n  }\n  console.log('Final robot info:', robotInfo);\n  return robotInfo;\n}\nfunction extractTopics(data) {\n  const topics = [];\n\n  // Try different possible structures for topics\n  let topicsData = [];\n  if (data.topics && Array.isArray(data.topics)) {\n    topicsData = data.topics;\n  } else if (data.messages && Array.isArray(data.messages)) {\n    // Some ION logs might have messages directly\n    topicsData = data.messages;\n  } else if (Array.isArray(data)) {\n    // The root might be an array of topics/messages\n    topicsData = data;\n  } else {\n    // Try to find topic-like structures in the data\n    for (const [key, value] of Object.entries(data)) {\n      if (Array.isArray(value) && value.length > 0) {\n        // Check if this looks like a topic\n        const firstItem = value[0];\n        if (firstItem && typeof firstItem === 'object' && (firstItem.timestamp || firstItem.time || firstItem.header)) {\n          // This looks like a topic with messages\n          topicsData.push({\n            name: key,\n            type: 'unknown',\n            messages: value\n          });\n        }\n      }\n    }\n  }\n  for (const topicData of topicsData) {\n    const topic = topicData;\n\n    // Extract topic name and type\n    let topicName = topic.name || topic.topic || 'unknown';\n    let topicType = topic.type || topic.messageType || 'unknown';\n\n    // If this is a direct message array, use the key as topic name\n    if (!topic.name && !topic.topic && topicData === data[topicName]) {\n      // This case is handled above\n    }\n    const messages = [];\n\n    // Extract messages\n    let messagesArray = topic.messages || topic.data || [];\n    if (!Array.isArray(messagesArray) && topic.timestamp) {\n      // This might be a single message\n      messagesArray = [topic];\n    }\n    if (Array.isArray(messagesArray)) {\n      for (const msgData of messagesArray) {\n        const message = msgData;\n\n        // Extract timestamp from various possible fields\n        let timestamp = message.timestamp || message.time || message.stamp || 0;\n        if (message.header && message.header.stamp) {\n          timestamp = message.header.stamp.sec * 1000 + (message.header.stamp.nsec || 0) / 1000000;\n        }\n        messages.push({\n          timestamp: timestamp,\n          publishTime: message.publishTime || message.publish_time,\n          content: message.content || message.data || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 || messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n    if (topicName && messages.length > 0) {\n      topics.push({\n        name: topicName,\n        type: topicType,\n        frequency: topic.frequency,\n        messages,\n        isHumanReadable\n      });\n    }\n  }\n  console.log(`Extracted ${topics.length} topics:`, topics.map(t => ({\n    name: t.name,\n    messageCount: t.messages.length\n  })));\n  return topics;\n}\nfunction calculateTimeBounds(topics) {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n  const totalDuration = endTime - startTime;\n  return {\n    startTime,\n    endTime,\n    totalDuration\n  };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages, targetTime) {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages, targetTime) {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}", "map": {"version": 3, "names": ["ion", "IonValueHandler", "handleValue", "value", "_value$timestampValue", "undefined", "getType", "ionType", "IonTypes", "NULL", "BOOL", "booleanValue", "INT", "numberValue", "FLOAT", "DECIMAL", "TIMESTAMP", "timestampValue", "getTime", "STRING", "stringValue", "SYMBOL", "BLOB", "uInt8ArrayValue", "CLOB", "LIST", "elements", "Symbol", "iterator", "Array", "from", "map", "item", "error", "console", "warn", "SEXP", "STRUCT", "result", "fields", "field", "fieldName", "String", "isArray", "key", "val", "Object", "entries", "isHumanReadable", "every", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "values", "parseIonLog", "data", "parsedData", "log", "load", "ionError", "textData", "TextDecoder", "decode", "ionTextError", "JSON", "parse", "jsonError", "Error", "message", "processedData", "sessionInfo", "extractSessionInfo", "robotInfo", "extractRobotInfo", "topics", "extractTopics", "t", "name", "messageCount", "messages", "length", "startTime", "endTime", "totalDuration", "calculateTimeBounds", "topicCount", "metadata", "keys", "session", "assign", "duration", "recordingDate", "version", "header", "start_time", "end_time", "recording_start", "recording_end", "file_version", "format_version", "toLowerCase", "includes", "robot", "robotModel", "model", "robotName", "robotVersion", "botModel", "robot_model", "robot_name", "robot_version", "platform", "device", "topicsData", "firstItem", "timestamp", "time", "push", "topicData", "topic", "topicName", "topicType", "messageType", "messagesArray", "msgData", "stamp", "sec", "nsec", "publishTime", "publish_time", "content", "sort", "a", "b", "msg", "frequency", "Infinity", "findMessageAtTime", "targetTime", "left", "right", "closest", "mid", "Math", "floor", "abs", "getMessagesUpToTime", "filter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts"], "sourcesContent": ["import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\nexport interface IonMessage {\n  timestamp: number;\n  publishTime?: number;\n  content: any;\n}\n\nexport interface IonTopic {\n  name: string;\n  type: string;\n  frequency?: number;\n  messages: IonMessage[];\n  isHumanReadable: boolean;\n}\n\nexport interface IonSessionInfo {\n  startTime?: number;\n  endTime?: number;\n  duration?: number;\n  recordingDate?: string;\n  version?: string;\n  [key: string]: any;\n}\n\nexport interface IonRobotInfo {\n  model?: string;\n  name?: string;\n  version?: string;\n  botModel?: Uint8Array; // 3D model data\n  [key: string]: any;\n}\n\nexport interface IonLogData {\n  sessionInfo: IonSessionInfo;\n  robotInfo: IonRobotInfo;\n  topics: IonTopic[];\n  metadata: any;\n  totalDuration: number;\n  startTime: number;\n  endTime: number;\n}\n\n// ION value handlers for both ION DOM and plain JavaScript objects\nexport class IonValueHandler {\n  static handleValue(value: any): any {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n\n        switch (ionType) {\n          case ion.IonTypes.NULL:\n            return null;\n          case ion.IonTypes.BOOL:\n            return value.booleanValue();\n          case ion.IonTypes.INT:\n            return value.numberValue();\n          case ion.IonTypes.FLOAT:\n            return value.numberValue();\n          case ion.IonTypes.DECIMAL:\n            return value.numberValue();\n          case ion.IonTypes.TIMESTAMP:\n            return value.timestampValue()?.getTime() || value.numberValue();\n          case ion.IonTypes.STRING:\n            return value.stringValue();\n          case ion.IonTypes.SYMBOL:\n            return value.stringValue();\n          case ion.IonTypes.BLOB:\n            return value.uInt8ArrayValue();\n          case ion.IonTypes.CLOB:\n            return value.stringValue();\n          case ion.IonTypes.LIST:\n            try {\n              const elements = value.elements();\n              if (elements && typeof elements[Symbol.iterator] === 'function') {\n                return Array.from(elements).map((item: any) => this.handleValue(item));\n              }\n              return [];\n            } catch (error) {\n              console.warn('Error processing ION list:', error);\n              return [];\n            }\n          case ion.IonTypes.SEXP:\n            try {\n              const elements = value.elements();\n              if (elements && typeof elements[Symbol.iterator] === 'function') {\n                return Array.from(elements).map((item: any) => this.handleValue(item));\n              }\n              return [];\n            } catch (error) {\n              console.warn('Error processing ION S-expression:', error);\n              return [];\n            }\n          case ion.IonTypes.STRUCT:\n            try {\n              const result: any = {};\n              const fields = value.fields();\n              if (fields && typeof fields[Symbol.iterator] === 'function') {\n                for (const field of fields) {\n                  const fieldName = field.fieldName();\n                  if (fieldName) {\n                    result[fieldName] = this.handleValue(field);\n                  }\n                }\n              }\n              return result;\n            } catch (error) {\n              console.warn('Error processing ION struct:', error);\n              return {};\n            }\n          default:\n            console.warn('Unknown ION type:', ionType);\n            return value.value ? value.value() : value;\n        }\n      } catch (error) {\n        console.warn('Error processing ION value:', error);\n        // Fall back to trying to get the raw value\n        try {\n          return value.value ? value.value() : value;\n        } catch {\n          return String(value);\n        }\n      }\n    }\n\n    // Handle plain JavaScript values\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n\n    if (typeof value === 'object') {\n      const result: any = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n\n    return value;\n  }\n\n  static isHumanReadable(value: any): boolean {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n        if (ionType === ion.IonTypes.CLOB) return true;\n        if (ionType === ion.IonTypes.LIST || ionType === ion.IonTypes.SEXP) {\n          return value.elements().every((item: any) => this.isHumanReadable(item));\n        }\n        if (ionType === ion.IonTypes.STRUCT) {\n          return value.fields().every((field: any) => this.isHumanReadable(field));\n        }\n        return true;\n      } catch {\n        return true; // Default to human readable if we can't determine\n      }\n    }\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data: Uint8Array): Promise<IonLogData> {\n  try {\n    let parsedData: any;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    let processedData: any;\n    try {\n      processedData = IonValueHandler.handleValue(parsedData);\n      console.log('Processed data:', processedData);\n    } catch (error) {\n      console.error('Error processing ION data:', error);\n      // If processing fails, try to use the raw parsed data\n      processedData = parsedData;\n      console.log('Using raw parsed data due to processing error');\n    }\n\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    console.log('Extracting session info from:', processedData);\n    const sessionInfo: IonSessionInfo = extractSessionInfo(processedData);\n    console.log('Extracted session info:', sessionInfo);\n\n    // Extract robot information\n    console.log('Extracting robot info from:', processedData);\n    const robotInfo: IonRobotInfo = extractRobotInfo(processedData);\n    console.log('Extracted robot info:', robotInfo);\n\n    // Extract topics\n    console.log('Extracting topics from:', processedData);\n    const topics: IonTopic[] = extractTopics(processedData);\n    console.log('Extracted topics:', topics.map(t => ({ name: t.name, messageCount: t.messages.length })));\n\n    // Calculate time bounds\n    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);\n\n    console.log('Extraction complete:', { sessionInfo, robotInfo, topicCount: topics.length });\n\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\nfunction extractSessionInfo(data: any): IonSessionInfo {\n  const sessionInfo: IonSessionInfo = {};\n\n  console.log('Session extraction - input data keys:', Object.keys(data));\n\n  // Look for session-related fields in various possible locations\n  if (data.session) {\n    console.log('Found session object:', data.session);\n    Object.assign(sessionInfo, data.session);\n  }\n\n  if (data.metadata) {\n    console.log('Found metadata object:', data.metadata);\n    const metadata = data.metadata;\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  // Look for session info in root level\n  if (data.startTime) sessionInfo.startTime = data.startTime;\n  if (data.endTime) sessionInfo.endTime = data.endTime;\n  if (data.duration) sessionInfo.duration = data.duration;\n  if (data.recordingDate) sessionInfo.recordingDate = data.recordingDate;\n  if (data.version) sessionInfo.version = data.version;\n\n  // Look for common ION log fields\n  if (data.header) {\n    console.log('Found header object:', data.header);\n    const header = data.header;\n    if (header.startTime) sessionInfo.startTime = header.startTime;\n    if (header.endTime) sessionInfo.endTime = header.endTime;\n    if (header.duration) sessionInfo.duration = header.duration;\n  }\n\n  // Look for timestamp-related fields that might indicate session bounds\n  if (data.start_time) sessionInfo.startTime = data.start_time;\n  if (data.end_time) sessionInfo.endTime = data.end_time;\n  if (data.recording_start) sessionInfo.startTime = data.recording_start;\n  if (data.recording_end) sessionInfo.endTime = data.recording_end;\n\n  // Look for version info in various places\n  if (data.file_version) sessionInfo.version = data.file_version;\n  if (data.format_version) sessionInfo.version = data.format_version;\n\n  // Try to extract from any nested structures\n  for (const [key, value] of Object.entries(data)) {\n    if (typeof value === 'object' && value !== null) {\n      // Check if this looks like session metadata\n      if (key.toLowerCase().includes('session') ||\n          key.toLowerCase().includes('metadata') ||\n          key.toLowerCase().includes('header') ||\n          key.toLowerCase().includes('info')) {\n        console.log(`Found potential session data in ${key}:`, value);\n\n        if (value.startTime) sessionInfo.startTime = value.startTime;\n        if (value.endTime) sessionInfo.endTime = value.endTime;\n        if (value.duration) sessionInfo.duration = value.duration;\n        if (value.recordingDate) sessionInfo.recordingDate = value.recordingDate;\n        if (value.version) sessionInfo.version = value.version;\n        if (value.start_time) sessionInfo.startTime = value.start_time;\n        if (value.end_time) sessionInfo.endTime = value.end_time;\n      }\n    }\n  }\n\n  console.log('Final session info:', sessionInfo);\n  return sessionInfo;\n}\n\nfunction extractRobotInfo(data: any): IonRobotInfo {\n  const robotInfo: IonRobotInfo = {};\n\n  console.log('Robot extraction - input data keys:', Object.keys(data));\n\n  // Look for robot-related fields\n  if (data.robot) {\n    console.log('Found robot object:', data.robot);\n    Object.assign(robotInfo, data.robot);\n  }\n\n  if (data.metadata) {\n    console.log('Found metadata object for robot:', data.metadata);\n    const metadata = data.metadata;\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n    if (metadata.robot_model) robotInfo.model = metadata.robot_model;\n    if (metadata.robot_name) robotInfo.name = metadata.robot_name;\n    if (metadata.robot_version) robotInfo.version = metadata.robot_version;\n  }\n\n  // Look for robot info in root level\n  if (data.robotModel) robotInfo.model = data.robotModel;\n  if (data.robotName) robotInfo.name = data.robotName;\n  if (data.robotVersion) robotInfo.version = data.robotVersion;\n  if (data.botModel) robotInfo.botModel = data.botModel;\n  if (data.robot_model) robotInfo.model = data.robot_model;\n  if (data.robot_name) robotInfo.name = data.robot_name;\n  if (data.robot_version) robotInfo.version = data.robot_version;\n\n  // Look for common robot fields\n  if (data.model) robotInfo.model = data.model;\n  if (data.name) robotInfo.name = data.name;\n  if (data.platform) robotInfo.model = data.platform;\n  if (data.device) robotInfo.model = data.device;\n\n  // Try to extract from any nested structures\n  for (const [key, value] of Object.entries(data)) {\n    if (typeof value === 'object' && value !== null) {\n      // Check if this looks like robot metadata\n      if (key.toLowerCase().includes('robot') ||\n          key.toLowerCase().includes('device') ||\n          key.toLowerCase().includes('platform') ||\n          key.toLowerCase().includes('system')) {\n        console.log(`Found potential robot data in ${key}:`, value);\n\n        if (value.model) robotInfo.model = value.model;\n        if (value.name) robotInfo.name = value.name;\n        if (value.version) robotInfo.version = value.version;\n        if (value.robot_model) robotInfo.model = value.robot_model;\n        if (value.robot_name) robotInfo.name = value.robot_name;\n        if (value.robot_version) robotInfo.version = value.robot_version;\n        if (value.botModel) robotInfo.botModel = value.botModel;\n      }\n    }\n  }\n\n  console.log('Final robot info:', robotInfo);\n  return robotInfo;\n}\n\nfunction extractTopics(data: any): IonTopic[] {\n  const topics: IonTopic[] = [];\n\n  // Try different possible structures for topics\n  let topicsData: any[] = [];\n\n  if (data.topics && Array.isArray(data.topics)) {\n    topicsData = data.topics;\n  } else if (data.messages && Array.isArray(data.messages)) {\n    // Some ION logs might have messages directly\n    topicsData = data.messages;\n  } else if (Array.isArray(data)) {\n    // The root might be an array of topics/messages\n    topicsData = data;\n  } else {\n    // Try to find topic-like structures in the data\n    for (const [key, value] of Object.entries(data)) {\n      if (Array.isArray(value) && value.length > 0) {\n        // Check if this looks like a topic\n        const firstItem = value[0];\n        if (firstItem && typeof firstItem === 'object' &&\n            (firstItem.timestamp || firstItem.time || firstItem.header)) {\n          // This looks like a topic with messages\n          topicsData.push({\n            name: key,\n            type: 'unknown',\n            messages: value\n          });\n        }\n      }\n    }\n  }\n\n  for (const topicData of topicsData) {\n    const topic = topicData;\n\n    // Extract topic name and type\n    let topicName = topic.name || topic.topic || 'unknown';\n    let topicType = topic.type || topic.messageType || 'unknown';\n\n    // If this is a direct message array, use the key as topic name\n    if (!topic.name && !topic.topic && topicData === data[topicName]) {\n      // This case is handled above\n    }\n\n    const messages: IonMessage[] = [];\n\n    // Extract messages\n    let messagesArray = topic.messages || topic.data || [];\n    if (!Array.isArray(messagesArray) && topic.timestamp) {\n      // This might be a single message\n      messagesArray = [topic];\n    }\n\n    if (Array.isArray(messagesArray)) {\n      for (const msgData of messagesArray) {\n        const message = msgData;\n\n        // Extract timestamp from various possible fields\n        let timestamp = message.timestamp || message.time || message.stamp || 0;\n        if (message.header && message.header.stamp) {\n          timestamp = message.header.stamp.sec * 1000 + (message.header.stamp.nsec || 0) / 1000000;\n        }\n\n        messages.push({\n          timestamp: timestamp,\n          publishTime: message.publishTime || message.publish_time,\n          content: message.content || message.data || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 ||\n      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n\n    if (topicName && messages.length > 0) {\n      topics.push({\n        name: topicName,\n        type: topicType,\n        frequency: topic.frequency,\n        messages,\n        isHumanReadable\n      });\n    }\n  }\n\n  console.log(`Extracted ${topics.length} topics:`, topics.map(t => ({ name: t.name, messageCount: t.messages.length })));\n  return topics;\n}\n\nfunction calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n\n  const totalDuration = endTime - startTime;\n\n  return { startTime, endTime, totalDuration };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;;AAE7B;;AA0CA;AACA,OAAO,MAAMC,eAAe,CAAC;EAC3B,OAAOC,WAAWA,CAACC,KAAU,EAAO;IAAA,IAAAC,qBAAA;IAClC,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;;IAEA;IACA,IAAIF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACG,OAAO,KAAK,UAAU,EAAE;MAC7E,IAAI;QACF,MAAMC,OAAO,GAAGJ,KAAK,CAACG,OAAO,CAAC,CAAC;QAE/B,QAAQC,OAAO;UACb,KAAKP,GAAG,CAACQ,QAAQ,CAACC,IAAI;YACpB,OAAO,IAAI;UACb,KAAKT,GAAG,CAACQ,QAAQ,CAACE,IAAI;YACpB,OAAOP,KAAK,CAACQ,YAAY,CAAC,CAAC;UAC7B,KAAKX,GAAG,CAACQ,QAAQ,CAACI,GAAG;YACnB,OAAOT,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACM,KAAK;YACrB,OAAOX,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACO,OAAO;YACvB,OAAOZ,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACQ,SAAS;YACzB,OAAO,EAAAZ,qBAAA,GAAAD,KAAK,CAACc,cAAc,CAAC,CAAC,cAAAb,qBAAA,uBAAtBA,qBAAA,CAAwBc,OAAO,CAAC,CAAC,KAAIf,KAAK,CAACU,WAAW,CAAC,CAAC;UACjE,KAAKb,GAAG,CAACQ,QAAQ,CAACW,MAAM;YACtB,OAAOhB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACa,MAAM;YACtB,OAAOlB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACc,IAAI;YACpB,OAAOnB,KAAK,CAACoB,eAAe,CAAC,CAAC;UAChC,KAAKvB,GAAG,CAACQ,QAAQ,CAACgB,IAAI;YACpB,OAAOrB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACiB,IAAI;YACpB,IAAI;cACF,MAAMC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ,CAAC,CAAC;cACjC,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;gBAC/D,OAAOC,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,GAAG,CAAEC,IAAS,IAAK,IAAI,CAAC9B,WAAW,CAAC8B,IAAI,CAAC,CAAC;cACxE;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOC,KAAK,EAAE;cACdC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;cACjD,OAAO,EAAE;YACX;UACF,KAAKjC,GAAG,CAACQ,QAAQ,CAAC4B,IAAI;YACpB,IAAI;cACF,MAAMV,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ,CAAC,CAAC;cACjC,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;gBAC/D,OAAOC,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,GAAG,CAAEC,IAAS,IAAK,IAAI,CAAC9B,WAAW,CAAC8B,IAAI,CAAC,CAAC;cACxE;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOC,KAAK,EAAE;cACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;cACzD,OAAO,EAAE;YACX;UACF,KAAKjC,GAAG,CAACQ,QAAQ,CAAC6B,MAAM;YACtB,IAAI;cACF,MAAMC,MAAW,GAAG,CAAC,CAAC;cACtB,MAAMC,MAAM,GAAGpC,KAAK,CAACoC,MAAM,CAAC,CAAC;cAC7B,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACZ,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;gBAC3D,KAAK,MAAMY,KAAK,IAAID,MAAM,EAAE;kBAC1B,MAAME,SAAS,GAAGD,KAAK,CAACC,SAAS,CAAC,CAAC;kBACnC,IAAIA,SAAS,EAAE;oBACbH,MAAM,CAACG,SAAS,CAAC,GAAG,IAAI,CAACvC,WAAW,CAACsC,KAAK,CAAC;kBAC7C;gBACF;cACF;cACA,OAAOF,MAAM;YACf,CAAC,CAAC,OAAOL,KAAK,EAAE;cACdC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;cACnD,OAAO,CAAC,CAAC;YACX;UACF;YACEC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAE5B,OAAO,CAAC;YAC1C,OAAOJ,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC,GAAGA,KAAK;QAC9C;MACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;QAClD;QACA,IAAI;UACF,OAAO9B,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC,GAAGA,KAAK;QAC5C,CAAC,CAAC,MAAM;UACN,OAAOuC,MAAM,CAACvC,KAAK,CAAC;QACtB;MACF;IACF;;IAEA;IACA,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxF,OAAOA,KAAK;IACd;IAEA,IAAI0B,KAAK,CAACc,OAAO,CAACxC,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAAC4B,GAAG,CAACC,IAAI,IAAI,IAAI,CAAC9B,WAAW,CAAC8B,IAAI,CAAC,CAAC;IAClD;IAEA,IAAI,OAAO7B,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMmC,MAAW,GAAG,CAAC,CAAC;MACtB,KAAK,MAAM,CAACM,GAAG,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC5C,KAAK,CAAC,EAAE;QAC9CmC,MAAM,CAACM,GAAG,CAAC,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,GAAG,CAAC;MACrC;MACA,OAAOP,MAAM;IACf;IAEA,OAAOnC,KAAK;EACd;EAEA,OAAO6C,eAAeA,CAAC7C,KAAU,EAAW;IAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE,OAAO,IAAI;IACtD,IAAI,OAAOF,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErG;IACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACG,OAAO,KAAK,UAAU,EAAE;MAC7E,IAAI;QACF,MAAMC,OAAO,GAAGJ,KAAK,CAACG,OAAO,CAAC,CAAC;QAC/B,IAAIC,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACc,IAAI,EAAE,OAAO,KAAK,CAAC,CAAC;QACjD,IAAIf,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACgB,IAAI,EAAE,OAAO,IAAI;QAC9C,IAAIjB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACiB,IAAI,IAAIlB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAAC4B,IAAI,EAAE;UAClE,OAAOjC,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACuB,KAAK,CAAEjB,IAAS,IAAK,IAAI,CAACgB,eAAe,CAAChB,IAAI,CAAC,CAAC;QAC1E;QACA,IAAIzB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAAC6B,MAAM,EAAE;UACnC,OAAOlC,KAAK,CAACoC,MAAM,CAAC,CAAC,CAACU,KAAK,CAAET,KAAU,IAAK,IAAI,CAACQ,eAAe,CAACR,KAAK,CAAC,CAAC;QAC1E;QACA,OAAO,IAAI;MACb,CAAC,CAAC,MAAM;QACN,OAAO,IAAI,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAIrC,KAAK,YAAY+C,UAAU,IAAI/C,KAAK,YAAYgD,WAAW,EAAE,OAAO,KAAK;IAC7E,IAAI,OAAOhD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiD,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;IAExE,IAAIvB,KAAK,CAACc,OAAO,CAACxC,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAAC8C,KAAK,CAACjB,IAAI,IAAI,IAAI,CAACgB,eAAe,CAAChB,IAAI,CAAC,CAAC;IACxD;IAEA,IAAI,OAAO7B,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO2C,MAAM,CAACO,MAAM,CAAClD,KAAK,CAAC,CAAC8C,KAAK,CAACJ,GAAG,IAAI,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC,CAAC;IACrE;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,eAAeS,WAAWA,CAACC,IAAgB,EAAuB;EACvE,IAAI;IACF,IAAIC,UAAe;;IAEnB;IACA,IAAI;MACFtB,OAAO,CAACuB,GAAG,CAAC,6CAA6C,CAAC;MAC1DD,UAAU,GAAGxD,GAAG,CAAC0D,IAAI,CAACH,IAAI,CAAC;MAC3BrB,OAAO,CAACuB,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,OAAOE,QAAQ,EAAE;MACjBzB,OAAO,CAACuB,GAAG,CAAC,kDAAkD,EAAEE,QAAQ,CAAC;;MAEzE;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACP,IAAI,CAAC;QAC/CC,UAAU,GAAGxD,GAAG,CAAC0D,IAAI,CAACE,QAAQ,CAAC;QAC/B1B,OAAO,CAACuB,GAAG,CAAC,iCAAiC,CAAC;MAChD,CAAC,CAAC,OAAOM,YAAY,EAAE;QACrB7B,OAAO,CAACuB,GAAG,CAAC,yCAAyC,EAAEM,YAAY,CAAC;;QAEpE;QACA,IAAI;UACF,MAAMH,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACP,IAAI,CAAC;UAC/CC,UAAU,GAAGQ,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;UACjC1B,OAAO,CAACuB,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,CAAC,OAAOS,SAAS,EAAE;UAClB,MAAM,IAAIC,KAAK,CAAC,mFAAmFD,SAAS,YAAYC,KAAK,GAAGD,SAAS,CAACE,OAAO,GAAG,eAAe,EAAE,CAAC;QACxK;MACF;IACF;IAEA,IAAI,CAACZ,UAAU,EAAE;MACf,MAAM,IAAIW,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEAjC,OAAO,CAACuB,GAAG,CAAC,wBAAwB,EAAED,UAAU,CAAC;;IAEjD;IACA,IAAIa,aAAkB;IACtB,IAAI;MACFA,aAAa,GAAGpE,eAAe,CAACC,WAAW,CAACsD,UAAU,CAAC;MACvDtB,OAAO,CAACuB,GAAG,CAAC,iBAAiB,EAAEY,aAAa,CAAC;IAC/C,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAoC,aAAa,GAAGb,UAAU;MAC1BtB,OAAO,CAACuB,GAAG,CAAC,+CAA+C,CAAC;IAC9D;IAEA,IAAI,CAACY,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACvD,MAAM,IAAIF,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACAjC,OAAO,CAACuB,GAAG,CAAC,+BAA+B,EAAEY,aAAa,CAAC;IAC3D,MAAMC,WAA2B,GAAGC,kBAAkB,CAACF,aAAa,CAAC;IACrEnC,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAEa,WAAW,CAAC;;IAEnD;IACApC,OAAO,CAACuB,GAAG,CAAC,6BAA6B,EAAEY,aAAa,CAAC;IACzD,MAAMG,SAAuB,GAAGC,gBAAgB,CAACJ,aAAa,CAAC;IAC/DnC,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAEe,SAAS,CAAC;;IAE/C;IACAtC,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAEY,aAAa,CAAC;IACrD,MAAMK,MAAkB,GAAGC,aAAa,CAACN,aAAa,CAAC;IACvDnC,OAAO,CAACuB,GAAG,CAAC,mBAAmB,EAAEiB,MAAM,CAAC3C,GAAG,CAAC6C,CAAC,KAAK;MAAEC,IAAI,EAAED,CAAC,CAACC,IAAI;MAAEC,YAAY,EAAEF,CAAC,CAACG,QAAQ,CAACC;IAAO,CAAC,CAAC,CAAC,CAAC;;IAEtG;IACA,MAAM;MAAEC,SAAS;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGC,mBAAmB,CAACV,MAAM,CAAC;IAEzExC,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAE;MAAEa,WAAW;MAAEE,SAAS;MAAEa,UAAU,EAAEX,MAAM,CAACM;IAAO,CAAC,CAAC;IAE1F,OAAO;MACLV,WAAW;MACXE,SAAS;MACTE,MAAM;MACNY,QAAQ,EAAEjB,aAAa;MACvBc,aAAa;MACbF,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC,OAAOjD,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACtC,MAAM,IAAIkC,KAAK,CAAC,wBAAwBlC,KAAK,YAAYkC,KAAK,GAAGlC,KAAK,CAACmC,OAAO,GAAG,eAAe,EAAE,CAAC;EACrG;AACF;AAEA,SAASG,kBAAkBA,CAAChB,IAAS,EAAkB;EACrD,MAAMe,WAA2B,GAAG,CAAC,CAAC;EAEtCpC,OAAO,CAACuB,GAAG,CAAC,uCAAuC,EAAEX,MAAM,CAACyC,IAAI,CAAChC,IAAI,CAAC,CAAC;;EAEvE;EACA,IAAIA,IAAI,CAACiC,OAAO,EAAE;IAChBtD,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAEF,IAAI,CAACiC,OAAO,CAAC;IAClD1C,MAAM,CAAC2C,MAAM,CAACnB,WAAW,EAAEf,IAAI,CAACiC,OAAO,CAAC;EAC1C;EAEA,IAAIjC,IAAI,CAAC+B,QAAQ,EAAE;IACjBpD,OAAO,CAACuB,GAAG,CAAC,wBAAwB,EAAEF,IAAI,CAAC+B,QAAQ,CAAC;IACpD,MAAMA,QAAQ,GAAG/B,IAAI,CAAC+B,QAAQ;IAC9B,IAAIA,QAAQ,CAACL,SAAS,EAAEX,WAAW,CAACW,SAAS,GAAGK,QAAQ,CAACL,SAAS;IAClE,IAAIK,QAAQ,CAACJ,OAAO,EAAEZ,WAAW,CAACY,OAAO,GAAGI,QAAQ,CAACJ,OAAO;IAC5D,IAAII,QAAQ,CAACI,QAAQ,EAAEpB,WAAW,CAACoB,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;IAC/D,IAAIJ,QAAQ,CAACK,aAAa,EAAErB,WAAW,CAACqB,aAAa,GAAGL,QAAQ,CAACK,aAAa;IAC9E,IAAIL,QAAQ,CAACM,OAAO,EAAEtB,WAAW,CAACsB,OAAO,GAAGN,QAAQ,CAACM,OAAO;EAC9D;;EAEA;EACA,IAAIrC,IAAI,CAAC0B,SAAS,EAAEX,WAAW,CAACW,SAAS,GAAG1B,IAAI,CAAC0B,SAAS;EAC1D,IAAI1B,IAAI,CAAC2B,OAAO,EAAEZ,WAAW,CAACY,OAAO,GAAG3B,IAAI,CAAC2B,OAAO;EACpD,IAAI3B,IAAI,CAACmC,QAAQ,EAAEpB,WAAW,CAACoB,QAAQ,GAAGnC,IAAI,CAACmC,QAAQ;EACvD,IAAInC,IAAI,CAACoC,aAAa,EAAErB,WAAW,CAACqB,aAAa,GAAGpC,IAAI,CAACoC,aAAa;EACtE,IAAIpC,IAAI,CAACqC,OAAO,EAAEtB,WAAW,CAACsB,OAAO,GAAGrC,IAAI,CAACqC,OAAO;;EAEpD;EACA,IAAIrC,IAAI,CAACsC,MAAM,EAAE;IACf3D,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAEF,IAAI,CAACsC,MAAM,CAAC;IAChD,MAAMA,MAAM,GAAGtC,IAAI,CAACsC,MAAM;IAC1B,IAAIA,MAAM,CAACZ,SAAS,EAAEX,WAAW,CAACW,SAAS,GAAGY,MAAM,CAACZ,SAAS;IAC9D,IAAIY,MAAM,CAACX,OAAO,EAAEZ,WAAW,CAACY,OAAO,GAAGW,MAAM,CAACX,OAAO;IACxD,IAAIW,MAAM,CAACH,QAAQ,EAAEpB,WAAW,CAACoB,QAAQ,GAAGG,MAAM,CAACH,QAAQ;EAC7D;;EAEA;EACA,IAAInC,IAAI,CAACuC,UAAU,EAAExB,WAAW,CAACW,SAAS,GAAG1B,IAAI,CAACuC,UAAU;EAC5D,IAAIvC,IAAI,CAACwC,QAAQ,EAAEzB,WAAW,CAACY,OAAO,GAAG3B,IAAI,CAACwC,QAAQ;EACtD,IAAIxC,IAAI,CAACyC,eAAe,EAAE1B,WAAW,CAACW,SAAS,GAAG1B,IAAI,CAACyC,eAAe;EACtE,IAAIzC,IAAI,CAAC0C,aAAa,EAAE3B,WAAW,CAACY,OAAO,GAAG3B,IAAI,CAAC0C,aAAa;;EAEhE;EACA,IAAI1C,IAAI,CAAC2C,YAAY,EAAE5B,WAAW,CAACsB,OAAO,GAAGrC,IAAI,CAAC2C,YAAY;EAC9D,IAAI3C,IAAI,CAAC4C,cAAc,EAAE7B,WAAW,CAACsB,OAAO,GAAGrC,IAAI,CAAC4C,cAAc;;EAElE;EACA,KAAK,MAAM,CAACvD,GAAG,EAAEzC,KAAK,CAAC,IAAI2C,MAAM,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;IAC/C,IAAI,OAAOpD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C;MACA,IAAIyC,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACrCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IACtCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACtCnE,OAAO,CAACuB,GAAG,CAAC,mCAAmCb,GAAG,GAAG,EAAEzC,KAAK,CAAC;QAE7D,IAAIA,KAAK,CAAC8E,SAAS,EAAEX,WAAW,CAACW,SAAS,GAAG9E,KAAK,CAAC8E,SAAS;QAC5D,IAAI9E,KAAK,CAAC+E,OAAO,EAAEZ,WAAW,CAACY,OAAO,GAAG/E,KAAK,CAAC+E,OAAO;QACtD,IAAI/E,KAAK,CAACuF,QAAQ,EAAEpB,WAAW,CAACoB,QAAQ,GAAGvF,KAAK,CAACuF,QAAQ;QACzD,IAAIvF,KAAK,CAACwF,aAAa,EAAErB,WAAW,CAACqB,aAAa,GAAGxF,KAAK,CAACwF,aAAa;QACxE,IAAIxF,KAAK,CAACyF,OAAO,EAAEtB,WAAW,CAACsB,OAAO,GAAGzF,KAAK,CAACyF,OAAO;QACtD,IAAIzF,KAAK,CAAC2F,UAAU,EAAExB,WAAW,CAACW,SAAS,GAAG9E,KAAK,CAAC2F,UAAU;QAC9D,IAAI3F,KAAK,CAAC4F,QAAQ,EAAEzB,WAAW,CAACY,OAAO,GAAG/E,KAAK,CAAC4F,QAAQ;MAC1D;IACF;EACF;EAEA7D,OAAO,CAACuB,GAAG,CAAC,qBAAqB,EAAEa,WAAW,CAAC;EAC/C,OAAOA,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAAClB,IAAS,EAAgB;EACjD,MAAMiB,SAAuB,GAAG,CAAC,CAAC;EAElCtC,OAAO,CAACuB,GAAG,CAAC,qCAAqC,EAAEX,MAAM,CAACyC,IAAI,CAAChC,IAAI,CAAC,CAAC;;EAErE;EACA,IAAIA,IAAI,CAAC+C,KAAK,EAAE;IACdpE,OAAO,CAACuB,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC+C,KAAK,CAAC;IAC9CxD,MAAM,CAAC2C,MAAM,CAACjB,SAAS,EAAEjB,IAAI,CAAC+C,KAAK,CAAC;EACtC;EAEA,IAAI/C,IAAI,CAAC+B,QAAQ,EAAE;IACjBpD,OAAO,CAACuB,GAAG,CAAC,kCAAkC,EAAEF,IAAI,CAAC+B,QAAQ,CAAC;IAC9D,MAAMA,QAAQ,GAAG/B,IAAI,CAAC+B,QAAQ;IAC9B,IAAIA,QAAQ,CAACiB,UAAU,EAAE/B,SAAS,CAACgC,KAAK,GAAGlB,QAAQ,CAACiB,UAAU;IAC9D,IAAIjB,QAAQ,CAACmB,SAAS,EAAEjC,SAAS,CAACK,IAAI,GAAGS,QAAQ,CAACmB,SAAS;IAC3D,IAAInB,QAAQ,CAACoB,YAAY,EAAElC,SAAS,CAACoB,OAAO,GAAGN,QAAQ,CAACoB,YAAY;IACpE,IAAIpB,QAAQ,CAACqB,QAAQ,EAAEnC,SAAS,CAACmC,QAAQ,GAAGrB,QAAQ,CAACqB,QAAQ;IAC7D,IAAIrB,QAAQ,CAACsB,WAAW,EAAEpC,SAAS,CAACgC,KAAK,GAAGlB,QAAQ,CAACsB,WAAW;IAChE,IAAItB,QAAQ,CAACuB,UAAU,EAAErC,SAAS,CAACK,IAAI,GAAGS,QAAQ,CAACuB,UAAU;IAC7D,IAAIvB,QAAQ,CAACwB,aAAa,EAAEtC,SAAS,CAACoB,OAAO,GAAGN,QAAQ,CAACwB,aAAa;EACxE;;EAEA;EACA,IAAIvD,IAAI,CAACgD,UAAU,EAAE/B,SAAS,CAACgC,KAAK,GAAGjD,IAAI,CAACgD,UAAU;EACtD,IAAIhD,IAAI,CAACkD,SAAS,EAAEjC,SAAS,CAACK,IAAI,GAAGtB,IAAI,CAACkD,SAAS;EACnD,IAAIlD,IAAI,CAACmD,YAAY,EAAElC,SAAS,CAACoB,OAAO,GAAGrC,IAAI,CAACmD,YAAY;EAC5D,IAAInD,IAAI,CAACoD,QAAQ,EAAEnC,SAAS,CAACmC,QAAQ,GAAGpD,IAAI,CAACoD,QAAQ;EACrD,IAAIpD,IAAI,CAACqD,WAAW,EAAEpC,SAAS,CAACgC,KAAK,GAAGjD,IAAI,CAACqD,WAAW;EACxD,IAAIrD,IAAI,CAACsD,UAAU,EAAErC,SAAS,CAACK,IAAI,GAAGtB,IAAI,CAACsD,UAAU;EACrD,IAAItD,IAAI,CAACuD,aAAa,EAAEtC,SAAS,CAACoB,OAAO,GAAGrC,IAAI,CAACuD,aAAa;;EAE9D;EACA,IAAIvD,IAAI,CAACiD,KAAK,EAAEhC,SAAS,CAACgC,KAAK,GAAGjD,IAAI,CAACiD,KAAK;EAC5C,IAAIjD,IAAI,CAACsB,IAAI,EAAEL,SAAS,CAACK,IAAI,GAAGtB,IAAI,CAACsB,IAAI;EACzC,IAAItB,IAAI,CAACwD,QAAQ,EAAEvC,SAAS,CAACgC,KAAK,GAAGjD,IAAI,CAACwD,QAAQ;EAClD,IAAIxD,IAAI,CAACyD,MAAM,EAAExC,SAAS,CAACgC,KAAK,GAAGjD,IAAI,CAACyD,MAAM;;EAE9C;EACA,KAAK,MAAM,CAACpE,GAAG,EAAEzC,KAAK,CAAC,IAAI2C,MAAM,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;IAC/C,IAAI,OAAOpD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C;MACA,IAAIyC,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IACnCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACpCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IACtCzD,GAAG,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACxCnE,OAAO,CAACuB,GAAG,CAAC,iCAAiCb,GAAG,GAAG,EAAEzC,KAAK,CAAC;QAE3D,IAAIA,KAAK,CAACqG,KAAK,EAAEhC,SAAS,CAACgC,KAAK,GAAGrG,KAAK,CAACqG,KAAK;QAC9C,IAAIrG,KAAK,CAAC0E,IAAI,EAAEL,SAAS,CAACK,IAAI,GAAG1E,KAAK,CAAC0E,IAAI;QAC3C,IAAI1E,KAAK,CAACyF,OAAO,EAAEpB,SAAS,CAACoB,OAAO,GAAGzF,KAAK,CAACyF,OAAO;QACpD,IAAIzF,KAAK,CAACyG,WAAW,EAAEpC,SAAS,CAACgC,KAAK,GAAGrG,KAAK,CAACyG,WAAW;QAC1D,IAAIzG,KAAK,CAAC0G,UAAU,EAAErC,SAAS,CAACK,IAAI,GAAG1E,KAAK,CAAC0G,UAAU;QACvD,IAAI1G,KAAK,CAAC2G,aAAa,EAAEtC,SAAS,CAACoB,OAAO,GAAGzF,KAAK,CAAC2G,aAAa;QAChE,IAAI3G,KAAK,CAACwG,QAAQ,EAAEnC,SAAS,CAACmC,QAAQ,GAAGxG,KAAK,CAACwG,QAAQ;MACzD;IACF;EACF;EAEAzE,OAAO,CAACuB,GAAG,CAAC,mBAAmB,EAAEe,SAAS,CAAC;EAC3C,OAAOA,SAAS;AAClB;AAEA,SAASG,aAAaA,CAACpB,IAAS,EAAc;EAC5C,MAAMmB,MAAkB,GAAG,EAAE;;EAE7B;EACA,IAAIuC,UAAiB,GAAG,EAAE;EAE1B,IAAI1D,IAAI,CAACmB,MAAM,IAAI7C,KAAK,CAACc,OAAO,CAACY,IAAI,CAACmB,MAAM,CAAC,EAAE;IAC7CuC,UAAU,GAAG1D,IAAI,CAACmB,MAAM;EAC1B,CAAC,MAAM,IAAInB,IAAI,CAACwB,QAAQ,IAAIlD,KAAK,CAACc,OAAO,CAACY,IAAI,CAACwB,QAAQ,CAAC,EAAE;IACxD;IACAkC,UAAU,GAAG1D,IAAI,CAACwB,QAAQ;EAC5B,CAAC,MAAM,IAAIlD,KAAK,CAACc,OAAO,CAACY,IAAI,CAAC,EAAE;IAC9B;IACA0D,UAAU,GAAG1D,IAAI;EACnB,CAAC,MAAM;IACL;IACA,KAAK,MAAM,CAACX,GAAG,EAAEzC,KAAK,CAAC,IAAI2C,MAAM,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;MAC/C,IAAI1B,KAAK,CAACc,OAAO,CAACxC,KAAK,CAAC,IAAIA,KAAK,CAAC6E,MAAM,GAAG,CAAC,EAAE;QAC5C;QACA,MAAMkC,SAAS,GAAG/G,KAAK,CAAC,CAAC,CAAC;QAC1B,IAAI+G,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,KACzCA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACrB,MAAM,CAAC,EAAE;UAC/D;UACAoB,UAAU,CAACI,IAAI,CAAC;YACdxC,IAAI,EAAEjC,GAAG;YACTQ,IAAI,EAAE,SAAS;YACf2B,QAAQ,EAAE5E;UACZ,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA,KAAK,MAAMmH,SAAS,IAAIL,UAAU,EAAE;IAClC,MAAMM,KAAK,GAAGD,SAAS;;IAEvB;IACA,IAAIE,SAAS,GAAGD,KAAK,CAAC1C,IAAI,IAAI0C,KAAK,CAACA,KAAK,IAAI,SAAS;IACtD,IAAIE,SAAS,GAAGF,KAAK,CAACnE,IAAI,IAAImE,KAAK,CAACG,WAAW,IAAI,SAAS;;IAE5D;IACA,IAAI,CAACH,KAAK,CAAC1C,IAAI,IAAI,CAAC0C,KAAK,CAACA,KAAK,IAAID,SAAS,KAAK/D,IAAI,CAACiE,SAAS,CAAC,EAAE;MAChE;IAAA;IAGF,MAAMzC,QAAsB,GAAG,EAAE;;IAEjC;IACA,IAAI4C,aAAa,GAAGJ,KAAK,CAACxC,QAAQ,IAAIwC,KAAK,CAAChE,IAAI,IAAI,EAAE;IACtD,IAAI,CAAC1B,KAAK,CAACc,OAAO,CAACgF,aAAa,CAAC,IAAIJ,KAAK,CAACJ,SAAS,EAAE;MACpD;MACAQ,aAAa,GAAG,CAACJ,KAAK,CAAC;IACzB;IAEA,IAAI1F,KAAK,CAACc,OAAO,CAACgF,aAAa,CAAC,EAAE;MAChC,KAAK,MAAMC,OAAO,IAAID,aAAa,EAAE;QACnC,MAAMvD,OAAO,GAAGwD,OAAO;;QAEvB;QACA,IAAIT,SAAS,GAAG/C,OAAO,CAAC+C,SAAS,IAAI/C,OAAO,CAACgD,IAAI,IAAIhD,OAAO,CAACyD,KAAK,IAAI,CAAC;QACvE,IAAIzD,OAAO,CAACyB,MAAM,IAAIzB,OAAO,CAACyB,MAAM,CAACgC,KAAK,EAAE;UAC1CV,SAAS,GAAG/C,OAAO,CAACyB,MAAM,CAACgC,KAAK,CAACC,GAAG,GAAG,IAAI,GAAG,CAAC1D,OAAO,CAACyB,MAAM,CAACgC,KAAK,CAACE,IAAI,IAAI,CAAC,IAAI,OAAO;QAC1F;QAEAhD,QAAQ,CAACsC,IAAI,CAAC;UACZF,SAAS,EAAEA,SAAS;UACpBa,WAAW,EAAE5D,OAAO,CAAC4D,WAAW,IAAI5D,OAAO,CAAC6D,YAAY;UACxDC,OAAO,EAAE9D,OAAO,CAAC8D,OAAO,IAAI9D,OAAO,CAACb,IAAI,IAAIa;QAC9C,CAAC,CAAC;MACJ;IACF;;IAEA;IACAW,QAAQ,CAACoD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,SAAS,GAAGkB,CAAC,CAAClB,SAAS,CAAC;;IAElD;IACA,MAAMnE,eAAe,GAAG+B,QAAQ,CAACC,MAAM,KAAK,CAAC,IAC3CD,QAAQ,CAAC9B,KAAK,CAACqF,GAAG,IAAIrI,eAAe,CAAC+C,eAAe,CAACsF,GAAG,CAACJ,OAAO,CAAC,CAAC;IAErE,IAAIV,SAAS,IAAIzC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACpCN,MAAM,CAAC2C,IAAI,CAAC;QACVxC,IAAI,EAAE2C,SAAS;QACfpE,IAAI,EAAEqE,SAAS;QACfc,SAAS,EAAEhB,KAAK,CAACgB,SAAS;QAC1BxD,QAAQ;QACR/B;MACF,CAAC,CAAC;IACJ;EACF;EAEAd,OAAO,CAACuB,GAAG,CAAC,aAAaiB,MAAM,CAACM,MAAM,UAAU,EAAEN,MAAM,CAAC3C,GAAG,CAAC6C,CAAC,KAAK;IAAEC,IAAI,EAAED,CAAC,CAACC,IAAI;IAAEC,YAAY,EAAEF,CAAC,CAACG,QAAQ,CAACC;EAAO,CAAC,CAAC,CAAC,CAAC;EACvH,OAAON,MAAM;AACf;AAEA,SAASU,mBAAmBA,CAACV,MAAkB,EAAiE;EAC9G,IAAIO,SAAS,GAAGuD,QAAQ;EACxB,IAAItD,OAAO,GAAG,CAACsD,QAAQ;EAEvB,KAAK,MAAMjB,KAAK,IAAI7C,MAAM,EAAE;IAC1B,KAAK,MAAMN,OAAO,IAAImD,KAAK,CAACxC,QAAQ,EAAE;MACpC,IAAIX,OAAO,CAAC+C,SAAS,GAAGlC,SAAS,EAAE;QACjCA,SAAS,GAAGb,OAAO,CAAC+C,SAAS;MAC/B;MACA,IAAI/C,OAAO,CAAC+C,SAAS,GAAGjC,OAAO,EAAE;QAC/BA,OAAO,GAAGd,OAAO,CAAC+C,SAAS;MAC7B;IACF;EACF;EAEA,IAAIlC,SAAS,KAAKuD,QAAQ,EAAE;IAC1BvD,SAAS,GAAG,CAAC;IACbC,OAAO,GAAG,CAAC;EACb;EAEA,MAAMC,aAAa,GAAGD,OAAO,GAAGD,SAAS;EAEzC,OAAO;IAAEA,SAAS;IAAEC,OAAO;IAAEC;EAAc,CAAC;AAC9C;;AAEA;AACA,OAAO,SAASsD,iBAAiBA,CAAC1D,QAAsB,EAAE2D,UAAkB,EAAqB;EAC/F,IAAI3D,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEtC;EACA,IAAI2D,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAG7D,QAAQ,CAACC,MAAM,GAAG,CAAC;EAC/B,IAAI6D,OAAO,GAAG9D,QAAQ,CAAC,CAAC,CAAC;EAEzB,OAAO4D,IAAI,IAAIC,KAAK,EAAE;IACpB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC1C,MAAMxE,OAAO,GAAGW,QAAQ,CAAC+D,GAAG,CAAC;IAE7B,IAAIC,IAAI,CAACE,GAAG,CAAC7E,OAAO,CAAC+C,SAAS,GAAGuB,UAAU,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACJ,OAAO,CAAC1B,SAAS,GAAGuB,UAAU,CAAC,EAAE;MACvFG,OAAO,GAAGzE,OAAO;IACnB;IAEA,IAAIA,OAAO,CAAC+C,SAAS,GAAGuB,UAAU,EAAE;MAClCC,IAAI,GAAGG,GAAG,GAAG,CAAC;IAChB,CAAC,MAAM,IAAI1E,OAAO,CAAC+C,SAAS,GAAGuB,UAAU,EAAE;MACzCE,KAAK,GAAGE,GAAG,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,OAAO1E,OAAO;IAChB;EACF;EAEA,OAAOyE,OAAO;AAChB;;AAEA;AACA,OAAO,SAASK,mBAAmBA,CAACnE,QAAsB,EAAE2D,UAAkB,EAAgB;EAC5F,OAAO3D,QAAQ,CAACoE,MAAM,CAACb,GAAG,IAAIA,GAAG,CAACnB,SAAS,IAAIuB,UAAU,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}