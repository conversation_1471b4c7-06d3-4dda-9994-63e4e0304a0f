{"ast": null, "code": "// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = function (c, id, msg, transfer, cb) {\n  var w = new Worker(ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], {\n    type: 'text/javascript'\n  }))));\n  w.onerror = function (e) {\n    return cb(e.error, null);\n  };\n  w.onmessage = function (e) {\n    return cb(null, e.data);\n  };\n  w.postMessage(msg, transfer);\n  return w;\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array,\n  u16 = Uint16Array,\n  u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */0, 0, /* impossible */0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n  var b = new u16(31);\n  for (var i = 0; i < 31; ++i) {\n    b[i] = start += 1 << eb[i - 1];\n  }\n  // numbers here are at max 18 bits\n  var r = new u32(b[30]);\n  for (var i = 1; i < 30; ++i) {\n    for (var j = b[i]; j < b[i + 1]; ++j) {\n      r[j] = j - b[i] << 5 | i;\n    }\n  }\n  return [b, r];\n};\nvar _a = freb(fleb, 2),\n  fl = _a[0],\n  revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0),\n  fd = _b[0],\n  revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n  // reverse table algorithm from SO\n  var x = (i & 0xAAAA) >>> 1 | (i & 0x5555) << 1;\n  x = (x & 0xCCCC) >>> 2 | (x & 0x3333) << 2;\n  x = (x & 0xF0F0) >>> 4 | (x & 0x0F0F) << 4;\n  rev[i] = ((x & 0xFF00) >>> 8 | (x & 0x00FF) << 8) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = function (cd, mb, r) {\n  var s = cd.length;\n  // index\n  var i = 0;\n  // u16 \"map\": index -> # of codes with bit length = index\n  var l = new u16(mb);\n  // length of cd must be 288 (total # of codes)\n  for (; i < s; ++i) ++l[cd[i] - 1];\n  // u16 \"map\": index -> minimum code for bit length = index\n  var le = new u16(mb);\n  for (i = 0; i < mb; ++i) {\n    le[i] = le[i - 1] + l[i - 1] << 1;\n  }\n  var co;\n  if (r) {\n    // u16 \"map\": index -> number of actual bits, symbol for code\n    co = new u16(1 << mb);\n    // bits to remove for reverser\n    var rvb = 15 - mb;\n    for (i = 0; i < s; ++i) {\n      // ignore 0 lengths\n      if (cd[i]) {\n        // num encoding both symbol and bits read\n        var sv = i << 4 | cd[i];\n        // free bits\n        var r_1 = mb - cd[i];\n        // start value\n        var v = le[cd[i] - 1]++ << r_1;\n        // m is end value\n        for (var m = v | (1 << r_1) - 1; v <= m; ++v) {\n          // every 16 bit value starting with the code yields the same result\n          co[rev[v] >>> rvb] = sv;\n        }\n      }\n    }\n  } else {\n    co = new u16(s);\n    for (i = 0; i < s; ++i) {\n      if (cd[i]) {\n        co[i] = rev[le[cd[i] - 1]++] >>> 15 - cd[i];\n      }\n    }\n  }\n  return co;\n};\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i) flt[i] = 8;\nfor (var i = 144; i < 256; ++i) flt[i] = 9;\nfor (var i = 256; i < 280; ++i) flt[i] = 7;\nfor (var i = 280; i < 288; ++i) flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i) fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/hMap(flt, 9, 0),\n  flrm = /*#__PURE__*/hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/hMap(fdt, 5, 0),\n  fdrm = /*#__PURE__*/hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n  var m = a[0];\n  for (var i = 1; i < a.length; ++i) {\n    if (a[i] > m) m = a[i];\n  }\n  return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n  var o = p / 8 | 0;\n  return (d[o] | d[o + 1] << 8) >> (p & 7) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n  var o = p / 8 | 0;\n  return (d[o] | d[o + 1] << 8 | d[o + 2] << 16) >> (p & 7);\n};\n// get end of byte\nvar shft = function (p) {\n  return (p / 8 | 0) + (p & 7 && 1);\n};\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n  if (s == null || s < 0) s = 0;\n  if (e == null || e > v.length) e = v.length;\n  // can't use .constructor in case user-supplied\n  var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n  n.set(v.subarray(s, e));\n  return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n  // source length\n  var sl = dat.length;\n  if (!sl || st && !st.l && sl < 5) return buf || new u8(0);\n  // have to estimate size\n  var noBuf = !buf || st;\n  // no state\n  var noSt = !st || st.i;\n  if (!st) st = {};\n  // Assumes roughly 33% compression ratio average\n  if (!buf) buf = new u8(sl * 3);\n  // ensure buffer can fit at least l elements\n  var cbuf = function (l) {\n    var bl = buf.length;\n    // need to increase size to fit\n    if (l > bl) {\n      // Double or set to necessary, whichever is greater\n      var nbuf = new u8(Math.max(bl * 2, l));\n      nbuf.set(buf);\n      buf = nbuf;\n    }\n  };\n  //  last chunk         bitpos           bytes\n  var final = st.f || 0,\n    pos = st.p || 0,\n    bt = st.b || 0,\n    lm = st.l,\n    dm = st.d,\n    lbt = st.m,\n    dbt = st.n;\n  // total bits\n  var tbts = sl * 8;\n  do {\n    if (!lm) {\n      // BFINAL - this is only 1 when last chunk is next\n      st.f = final = bits(dat, pos, 1);\n      // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n      var type = bits(dat, pos + 1, 3);\n      pos += 3;\n      if (!type) {\n        // go to end of byte boundary\n        var s = shft(pos) + 4,\n          l = dat[s - 4] | dat[s - 3] << 8,\n          t = s + l;\n        if (t > sl) {\n          if (noSt) throw 'unexpected EOF';\n          break;\n        }\n        // ensure size\n        if (noBuf) cbuf(bt + l);\n        // Copy over uncompressed data\n        buf.set(dat.subarray(s, t), bt);\n        // Get new bitpos, update byte count\n        st.b = bt += l, st.p = pos = t * 8;\n        continue;\n      } else if (type == 1) lm = flrm, dm = fdrm, lbt = 9, dbt = 5;else if (type == 2) {\n        //  literal                            lengths\n        var hLit = bits(dat, pos, 31) + 257,\n          hcLen = bits(dat, pos + 10, 15) + 4;\n        var tl = hLit + bits(dat, pos + 5, 31) + 1;\n        pos += 14;\n        // length+distance tree\n        var ldt = new u8(tl);\n        // code length tree\n        var clt = new u8(19);\n        for (var i = 0; i < hcLen; ++i) {\n          // use index map to get real code\n          clt[clim[i]] = bits(dat, pos + i * 3, 7);\n        }\n        pos += hcLen * 3;\n        // code lengths bits\n        var clb = max(clt),\n          clbmsk = (1 << clb) - 1;\n        // code lengths map\n        var clm = hMap(clt, clb, 1);\n        for (var i = 0; i < tl;) {\n          var r = clm[bits(dat, pos, clbmsk)];\n          // bits read\n          pos += r & 15;\n          // symbol\n          var s = r >>> 4;\n          // code length to copy\n          if (s < 16) {\n            ldt[i++] = s;\n          } else {\n            //  copy   count\n            var c = 0,\n              n = 0;\n            if (s == 16) n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];else if (s == 17) n = 3 + bits(dat, pos, 7), pos += 3;else if (s == 18) n = 11 + bits(dat, pos, 127), pos += 7;\n            while (n--) ldt[i++] = c;\n          }\n        }\n        //    length tree                 distance tree\n        var lt = ldt.subarray(0, hLit),\n          dt = ldt.subarray(hLit);\n        // max length bits\n        lbt = max(lt);\n        // max dist bits\n        dbt = max(dt);\n        lm = hMap(lt, lbt, 1);\n        dm = hMap(dt, dbt, 1);\n      } else throw 'invalid block type';\n      if (pos > tbts) {\n        if (noSt) throw 'unexpected EOF';\n        break;\n      }\n    }\n    // Make sure the buffer can hold this + the largest possible addition\n    // Maximum chunk size (practically, theoretically infinite) is 2^17;\n    if (noBuf) cbuf(bt + 131072);\n    var lms = (1 << lbt) - 1,\n      dms = (1 << dbt) - 1;\n    var lpos = pos;\n    for (;; lpos = pos) {\n      // bits read, code\n      var c = lm[bits16(dat, pos) & lms],\n        sym = c >>> 4;\n      pos += c & 15;\n      if (pos > tbts) {\n        if (noSt) throw 'unexpected EOF';\n        break;\n      }\n      if (!c) throw 'invalid length/literal';\n      if (sym < 256) buf[bt++] = sym;else if (sym == 256) {\n        lpos = pos, lm = null;\n        break;\n      } else {\n        var add = sym - 254;\n        // no extra bits needed if less\n        if (sym > 264) {\n          // index\n          var i = sym - 257,\n            b = fleb[i];\n          add = bits(dat, pos, (1 << b) - 1) + fl[i];\n          pos += b;\n        }\n        // dist\n        var d = dm[bits16(dat, pos) & dms],\n          dsym = d >>> 4;\n        if (!d) throw 'invalid distance';\n        pos += d & 15;\n        var dt = fd[dsym];\n        if (dsym > 3) {\n          var b = fdeb[dsym];\n          dt += bits16(dat, pos) & (1 << b) - 1, pos += b;\n        }\n        if (pos > tbts) {\n          if (noSt) throw 'unexpected EOF';\n          break;\n        }\n        if (noBuf) cbuf(bt + 131072);\n        var end = bt + add;\n        for (; bt < end; bt += 4) {\n          buf[bt] = buf[bt - dt];\n          buf[bt + 1] = buf[bt + 1 - dt];\n          buf[bt + 2] = buf[bt + 2 - dt];\n          buf[bt + 3] = buf[bt + 3 - dt];\n        }\n        bt = end;\n      }\n    }\n    st.l = lm, st.p = lpos, st.b = bt;\n    if (lm) final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n  } while (!final);\n  return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 | 0;\n  d[o] |= v;\n  d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 | 0;\n  d[o] |= v;\n  d[o + 1] |= v >>> 8;\n  d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n  // Need extra info to make a tree\n  var t = [];\n  for (var i = 0; i < d.length; ++i) {\n    if (d[i]) t.push({\n      s: i,\n      f: d[i]\n    });\n  }\n  var s = t.length;\n  var t2 = t.slice();\n  if (!s) return [et, 0];\n  if (s == 1) {\n    var v = new u8(t[0].s + 1);\n    v[t[0].s] = 1;\n    return [v, 1];\n  }\n  t.sort(function (a, b) {\n    return a.f - b.f;\n  });\n  // after i2 reaches last ind, will be stopped\n  // freq must be greater than largest possible number of symbols\n  t.push({\n    s: -1,\n    f: 25001\n  });\n  var l = t[0],\n    r = t[1],\n    i0 = 0,\n    i1 = 1,\n    i2 = 2;\n  t[0] = {\n    s: -1,\n    f: l.f + r.f,\n    l: l,\n    r: r\n  };\n  // efficient algorithm from UZIP.js\n  // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n  // symbols that combined have high freq, will start processing i2 (high-freq,\n  // non-composite) symbols instead\n  // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n  while (i1 != s - 1) {\n    l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n    r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n    t[i1++] = {\n      s: -1,\n      f: l.f + r.f,\n      l: l,\n      r: r\n    };\n  }\n  var maxSym = t2[0].s;\n  for (var i = 1; i < s; ++i) {\n    if (t2[i].s > maxSym) maxSym = t2[i].s;\n  }\n  // code lengths\n  var tr = new u16(maxSym + 1);\n  // max bits in tree\n  var mbt = ln(t[i1 - 1], tr, 0);\n  if (mbt > mb) {\n    // more algorithms from UZIP.js\n    // TODO: find out how this code works (debt)\n    //  ind    debt\n    var i = 0,\n      dt = 0;\n    //    left            cost\n    var lft = mbt - mb,\n      cst = 1 << lft;\n    t2.sort(function (a, b) {\n      return tr[b.s] - tr[a.s] || a.f - b.f;\n    });\n    for (; i < s; ++i) {\n      var i2_1 = t2[i].s;\n      if (tr[i2_1] > mb) {\n        dt += cst - (1 << mbt - tr[i2_1]);\n        tr[i2_1] = mb;\n      } else break;\n    }\n    dt >>>= lft;\n    while (dt > 0) {\n      var i2_2 = t2[i].s;\n      if (tr[i2_2] < mb) dt -= 1 << mb - tr[i2_2]++ - 1;else ++i;\n    }\n    for (; i >= 0 && dt; --i) {\n      var i2_3 = t2[i].s;\n      if (tr[i2_3] == mb) {\n        --tr[i2_3];\n        ++dt;\n      }\n    }\n    mbt = mb;\n  }\n  return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n  return n.s == -1 ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1)) : l[n.s] = d;\n};\n// length codes generation\nvar lc = function (c) {\n  var s = c.length;\n  // Note that the semicolon was intentional\n  while (s && !c[--s]);\n  var cl = new u16(++s);\n  //  ind      num         streak\n  var cli = 0,\n    cln = c[0],\n    cls = 1;\n  var w = function (v) {\n    cl[cli++] = v;\n  };\n  for (var i = 1; i <= s; ++i) {\n    if (c[i] == cln && i != s) ++cls;else {\n      if (!cln && cls > 2) {\n        for (; cls > 138; cls -= 138) w(32754);\n        if (cls > 2) {\n          w(cls > 10 ? cls - 11 << 5 | 28690 : cls - 3 << 5 | 12305);\n          cls = 0;\n        }\n      } else if (cls > 3) {\n        w(cln), --cls;\n        for (; cls > 6; cls -= 6) w(8304);\n        if (cls > 2) w(cls - 3 << 5 | 8208), cls = 0;\n      }\n      while (cls--) w(cln);\n      cls = 1;\n      cln = c[i];\n    }\n  }\n  return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n  var l = 0;\n  for (var i = 0; i < cl.length; ++i) l += cf[i] * cl[i];\n  return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n  // no need to write 00 as type: TypedArray defaults to 0\n  var s = dat.length;\n  var o = shft(pos + 2);\n  out[o] = s & 255;\n  out[o + 1] = s >>> 8;\n  out[o + 2] = out[o] ^ 255;\n  out[o + 3] = out[o + 1] ^ 255;\n  for (var i = 0; i < s; ++i) out[o + i + 4] = dat[i];\n  return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n  wbits(out, p++, final);\n  ++lf[256];\n  var _a = hTree(lf, 15),\n    dlt = _a[0],\n    mlb = _a[1];\n  var _b = hTree(df, 15),\n    ddt = _b[0],\n    mdb = _b[1];\n  var _c = lc(dlt),\n    lclt = _c[0],\n    nlc = _c[1];\n  var _d = lc(ddt),\n    lcdt = _d[0],\n    ndc = _d[1];\n  var lcfreq = new u16(19);\n  for (var i = 0; i < lclt.length; ++i) lcfreq[lclt[i] & 31]++;\n  for (var i = 0; i < lcdt.length; ++i) lcfreq[lcdt[i] & 31]++;\n  var _e = hTree(lcfreq, 7),\n    lct = _e[0],\n    mlcb = _e[1];\n  var nlcc = 19;\n  for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc);\n  var flen = bl + 5 << 3;\n  var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n  var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n  if (flen <= ftlen && flen <= dtlen) return wfblk(out, p, dat.subarray(bs, bs + bl));\n  var lm, ll, dm, dl;\n  wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n  if (dtlen < ftlen) {\n    lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n    var llm = hMap(lct, mlcb, 0);\n    wbits(out, p, nlc - 257);\n    wbits(out, p + 5, ndc - 1);\n    wbits(out, p + 10, nlcc - 4);\n    p += 14;\n    for (var i = 0; i < nlcc; ++i) wbits(out, p + 3 * i, lct[clim[i]]);\n    p += 3 * nlcc;\n    var lcts = [lclt, lcdt];\n    for (var it = 0; it < 2; ++it) {\n      var clct = lcts[it];\n      for (var i = 0; i < clct.length; ++i) {\n        var len = clct[i] & 31;\n        wbits(out, p, llm[len]), p += lct[len];\n        if (len > 15) wbits(out, p, clct[i] >>> 5 & 127), p += clct[i] >>> 12;\n      }\n    }\n  } else {\n    lm = flm, ll = flt, dm = fdm, dl = fdt;\n  }\n  for (var i = 0; i < li; ++i) {\n    if (syms[i] > 255) {\n      var len = syms[i] >>> 18 & 31;\n      wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n      if (len > 7) wbits(out, p, syms[i] >>> 23 & 31), p += fleb[len];\n      var dst = syms[i] & 31;\n      wbits16(out, p, dm[dst]), p += dl[dst];\n      if (dst > 3) wbits16(out, p, syms[i] >>> 5 & 8191), p += fdeb[dst];\n    } else {\n      wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n    }\n  }\n  wbits16(out, p, lm[256]);\n  return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n  var s = dat.length;\n  var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n  // writing to this writes to the output buffer\n  var w = o.subarray(pre, o.length - post);\n  var pos = 0;\n  if (!lvl || s < 8) {\n    for (var i = 0; i <= s; i += 65535) {\n      // end\n      var e = i + 65535;\n      if (e < s) {\n        // write full block\n        pos = wfblk(w, pos, dat.subarray(i, e));\n      } else {\n        // write final block\n        w[i] = lst;\n        pos = wfblk(w, pos, dat.subarray(i, s));\n      }\n    }\n  } else {\n    var opt = deo[lvl - 1];\n    var n = opt >>> 13,\n      c = opt & 8191;\n    var msk_1 = (1 << plvl) - 1;\n    //    prev 2-byte val map    curr 2-byte val map\n    var prev = new u16(32768),\n      head = new u16(msk_1 + 1);\n    var bs1_1 = Math.ceil(plvl / 3),\n      bs2_1 = 2 * bs1_1;\n    var hsh = function (i) {\n      return (dat[i] ^ dat[i + 1] << bs1_1 ^ dat[i + 2] << bs2_1) & msk_1;\n    };\n    // 24576 is an arbitrary number of maximum symbols per block\n    // 424 buffer for last block\n    var syms = new u32(25000);\n    // length/literal freq   distance freq\n    var lf = new u16(288),\n      df = new u16(32);\n    //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n    var lc_1 = 0,\n      eb = 0,\n      i = 0,\n      li = 0,\n      wi = 0,\n      bs = 0;\n    for (; i < s; ++i) {\n      // hash value\n      // deopt when i > s - 3 - at end, deopt acceptable\n      var hv = hsh(i);\n      // index mod 32768    previous index mod\n      var imod = i & 32767,\n        pimod = head[hv];\n      prev[imod] = pimod;\n      head[hv] = imod;\n      // We always should modify head and prev, but only add symbols if\n      // this data is not yet processed (\"wait\" for wait index)\n      if (wi <= i) {\n        // bytes remaining\n        var rem = s - i;\n        if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n          pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n          li = lc_1 = eb = 0, bs = i;\n          for (var j = 0; j < 286; ++j) lf[j] = 0;\n          for (var j = 0; j < 30; ++j) df[j] = 0;\n        }\n        //  len    dist   chain\n        var l = 2,\n          d = 0,\n          ch_1 = c,\n          dif = imod - pimod & 32767;\n        if (rem > 2 && hv == hsh(i - dif)) {\n          var maxn = Math.min(n, rem) - 1;\n          var maxd = Math.min(32767, i);\n          // max possible length\n          // not capped at dif because decompressors implement \"rolling\" index population\n          var ml = Math.min(258, rem);\n          while (dif <= maxd && --ch_1 && imod != pimod) {\n            if (dat[i + l] == dat[i + l - dif]) {\n              var nl = 0;\n              for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl);\n              if (nl > l) {\n                l = nl, d = dif;\n                // break out early when we reach \"nice\" (we are satisfied enough)\n                if (nl > maxn) break;\n                // now, find the rarest 2-byte sequence within this\n                // length of literals and search for that instead.\n                // Much faster than just using the start\n                var mmd = Math.min(dif, nl - 2);\n                var md = 0;\n                for (var j = 0; j < mmd; ++j) {\n                  var ti = i - dif + j + 32768 & 32767;\n                  var pti = prev[ti];\n                  var cd = ti - pti + 32768 & 32767;\n                  if (cd > md) md = cd, pimod = ti;\n                }\n              }\n            }\n            // check the previous match\n            imod = pimod, pimod = prev[imod];\n            dif += imod - pimod + 32768 & 32767;\n          }\n        }\n        // d will be nonzero only when a match was found\n        if (d) {\n          // store both dist and len data in one Uint32\n          // Make sure this is recognized as a len/dist with 28th bit (2^28)\n          syms[li++] = 268435456 | revfl[l] << 18 | revfd[d];\n          var lin = revfl[l] & 31,\n            din = revfd[d] & 31;\n          eb += fleb[lin] + fdeb[din];\n          ++lf[257 + lin];\n          ++df[din];\n          wi = i + l;\n          ++lc_1;\n        } else {\n          syms[li++] = dat[i];\n          ++lf[dat[i]];\n        }\n      }\n    }\n    pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n    // this is the easiest way to avoid needing to maintain state\n    if (!lst && pos & 7) pos = wfblk(w, pos + 1, et);\n  }\n  return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/function () {\n  var t = new Int32Array(256);\n  for (var i = 0; i < 256; ++i) {\n    var c = i,\n      k = 9;\n    while (--k) c = (c & 1 && -306674912) ^ c >>> 1;\n    t[i] = c;\n  }\n  return t;\n}();\n// CRC32\nvar crc = function () {\n  var c = -1;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var cr = c;\n      for (var i = 0; i < d.length; ++i) cr = crct[cr & 255 ^ d[i]] ^ cr >>> 8;\n      c = cr;\n    },\n    d: function () {\n      return ~c;\n    }\n  };\n};\n// Alder32\nvar adler = function () {\n  var a = 1,\n    b = 0;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var n = a,\n        m = b;\n      var l = d.length;\n      for (var i = 0; i != l;) {\n        var e = Math.min(i + 2655, l);\n        for (; i < e; ++i) m += n += d[i];\n        n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n      }\n      a = n, b = m;\n    },\n    d: function () {\n      a %= 65521, b %= 65521;\n      return (a & 255) << 24 | a >>> 8 << 16 | (b & 255) << 8 | b >>> 8;\n    }\n  };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n  return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : 12 + opt.mem, pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n  var o = {};\n  for (var k in a) o[k] = a[k];\n  for (var k in b) o[k] = b[k];\n  return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n  var dt = fn();\n  var st = fn.toString();\n  var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n  for (var i = 0; i < dt.length; ++i) {\n    var v = dt[i],\n      k = ks[i];\n    if (typeof v == 'function') {\n      fnStr += ';' + k + '=';\n      var st_1 = v.toString();\n      if (v.prototype) {\n        // for global objects\n        if (st_1.indexOf('[native code]') != -1) {\n          var spInd = st_1.indexOf(' ', 8) + 1;\n          fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n        } else {\n          fnStr += st_1;\n          for (var t in v.prototype) fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n        }\n      } else fnStr += st_1;\n    } else td[k] = v;\n  }\n  return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n  var tl = [];\n  for (var k in v) {\n    if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32) tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n  }\n  return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n  var _a;\n  if (!ch[id]) {\n    var fnStr = '',\n      td_1 = {},\n      m = fns.length - 1;\n    for (var i = 0; i < m; ++i) _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n    ch[id] = wcln(fns[m], fnStr, td_1);\n  }\n  var td = mrg({}, ch[id][1]);\n  return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () {\n  return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8];\n};\nvar bDflt = function () {\n  return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf];\n};\n// gzip extra\nvar gze = function () {\n  return [gzh, gzhl, wbytes, crc, crct];\n};\n// gunzip extra\nvar guze = function () {\n  return [gzs, gzl];\n};\n// zlib extra\nvar zle = function () {\n  return [zlh, wbytes, adler];\n};\n// unzlib extra\nvar zule = function () {\n  return [zlv];\n};\n// post buf\nvar pbf = function (msg) {\n  return postMessage(msg, [msg.buffer]);\n};\n// get u8\nvar gu8 = function (o) {\n  return o && o.size && new u8(o.size);\n};\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n  var w = wrkr(fns, init, id, function (err, dat) {\n    w.terminate();\n    cb(err, dat);\n  });\n  w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n  return function () {\n    w.terminate();\n  };\n};\n// auto stream\nvar astrm = function (strm) {\n  strm.ondata = function (dat, final) {\n    return postMessage([dat, final], [dat.buffer]);\n  };\n  return function (ev) {\n    return strm.push(ev.data[0], ev.data[1]);\n  };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n  var t;\n  var w = wrkr(fns, init, id, function (err, dat) {\n    if (err) w.terminate(), strm.ondata.call(strm, err);else {\n      if (dat[1]) w.terminate();\n      strm.ondata.call(strm, err, dat[0], dat[1]);\n    }\n  });\n  w.postMessage(opts);\n  strm.push = function (d, f) {\n    if (t) throw 'stream finished';\n    if (!strm.ondata) throw 'no stream handler';\n    w.postMessage([d, t = f], [d.buffer]);\n  };\n  strm.terminate = function () {\n    w.terminate();\n  };\n};\n// read 2 bytes\nvar b2 = function (d, b) {\n  return d[b] | d[b + 1] << 8;\n};\n// read 4 bytes\nvar b4 = function (d, b) {\n  return (d[b] | d[b + 1] << 8 | d[b + 2] << 16 | d[b + 3] << 24) >>> 0;\n};\nvar b8 = function (d, b) {\n  return b4(d, b) + b4(d, b + 4) * 4294967296;\n};\n// write bytes\nvar wbytes = function (d, b, v) {\n  for (; v; ++b) d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n  var fn = o.filename;\n  c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n  if (o.mtime != 0) wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n  if (fn) {\n    c[3] = 8;\n    for (var i = 0; i <= fn.length; ++i) c[i + 10] = fn.charCodeAt(i);\n  }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n  if (d[0] != 31 || d[1] != 139 || d[2] != 8) throw 'invalid gzip data';\n  var flg = d[3];\n  var st = 10;\n  if (flg & 4) st += d[10] | (d[11] << 8) + 2;\n  for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++]);\n  return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n  var l = d.length;\n  return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16 | d[l - 1] << 24) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) {\n  return 10 + (o.filename && o.filename.length + 1 || 0);\n};\n// zlib header\nvar zlh = function (c, o) {\n  var lv = o.level,\n    fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n  c[0] = 120, c[1] = fl << 6 | (fl ? 32 - 2 * fl : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n  if ((d[0] & 15) != 8 || d[0] >>> 4 > 7 || (d[0] << 8 | d[1]) % 31) throw 'invalid zlib data';\n  if (d[1] & 32) throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n  if (!cb && typeof opts == 'function') cb = opts, opts = {};\n  this.ondata = cb;\n  return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/function () {\n  function Deflate(opts, cb) {\n    if (!cb && typeof opts == 'function') cb = opts, opts = {};\n    this.ondata = cb;\n    this.o = opts || {};\n  }\n  Deflate.prototype.p = function (c, f) {\n    this.ondata(dopt(c, this.o, 0, 0, !f), f);\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Deflate.prototype.push = function (chunk, final) {\n    if (this.d) throw 'stream finished';\n    if (!this.ondata) throw 'no stream handler';\n    this.d = final;\n    this.p(chunk, final || false);\n  };\n  return Deflate;\n}();\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/function () {\n  function AsyncDeflate(opts, cb) {\n    astrmify([bDflt, function () {\n      return [astrm, Deflate];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Deflate(ev.data);\n      onmessage = astrm(strm);\n    }, 6);\n  }\n  return AsyncDeflate;\n}();\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt], function (ev) {\n    return pbf(deflateSync(ev.data[0], ev.data[1]));\n  }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n  return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/function () {\n  /**\n   * Creates an inflation stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Inflate(cb) {\n    this.s = {};\n    this.p = new u8(0);\n    this.ondata = cb;\n  }\n  Inflate.prototype.e = function (c) {\n    if (this.d) throw 'stream finished';\n    if (!this.ondata) throw 'no stream handler';\n    var l = this.p.length;\n    var n = new u8(l + c.length);\n    n.set(this.p), n.set(c, l), this.p = n;\n  };\n  Inflate.prototype.c = function (final) {\n    this.d = this.s.i = final || false;\n    var bts = this.s.b;\n    var dt = inflt(this.p, this.o, this.s);\n    this.ondata(slc(dt, bts, this.s.b), this.d);\n    this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n    this.p = slc(this.p, this.s.p / 8 | 0), this.s.p &= 7;\n  };\n  /**\n   * Pushes a chunk to be inflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the final chunk\n   */\n  Inflate.prototype.push = function (chunk, final) {\n    this.e(chunk), this.c(final);\n  };\n  return Inflate;\n}();\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous inflation stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncInflate(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, function () {\n      return [astrm, Inflate];\n    }], this, 0, function () {\n      var strm = new Inflate();\n      onmessage = astrm(strm);\n    }, 7);\n  }\n  return AsyncInflate;\n}();\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt], function (ev) {\n    return pbf(inflateSync(ev.data[0], gu8(ev.data[1])));\n  }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n  return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/function () {\n  function Gzip(opts, cb) {\n    this.c = crc();\n    this.l = 0;\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be GZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gzip.prototype.push = function (chunk, final) {\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Gzip.prototype.p = function (c, f) {\n    this.c.p(c);\n    this.l += c.length;\n    var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n    if (this.v) gzh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n    this.ondata(raw, f);\n  };\n  return Gzip;\n}();\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/function () {\n  function AsyncGzip(opts, cb) {\n    astrmify([bDflt, gze, function () {\n      return [astrm, Deflate, Gzip];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Gzip(ev.data);\n      onmessage = astrm(strm);\n    }, 8);\n  }\n  return AsyncGzip;\n}();\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt, gze, function () {\n    return [gzipSync];\n  }], function (ev) {\n    return pbf(gzipSync(ev.data[0], ev.data[1]));\n  }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n  if (!opts) opts = {};\n  var c = crc(),\n    l = data.length;\n  c.p(data);\n  var d = dopt(data, opts, gzhl(opts), 8),\n    s = d.length;\n  return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/function () {\n  /**\n   * Creates a GUNZIP stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Gunzip(cb) {\n    this.v = 1;\n    Inflate.call(this, cb);\n  }\n  /**\n   * Pushes a chunk to be GUNZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gunzip.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    if (this.v) {\n      var s = this.p.length > 3 ? gzs(this.p) : 4;\n      if (s >= this.p.length && !final) return;\n      this.p = this.p.subarray(s), this.v = 0;\n    }\n    if (final) {\n      if (this.p.length < 8) throw 'invalid gzip stream';\n      this.p = this.p.subarray(0, -8);\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n  };\n  return Gunzip;\n}();\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous GUNZIP stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncGunzip(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, guze, function () {\n      return [astrm, Inflate, Gunzip];\n    }], this, 0, function () {\n      var strm = new Gunzip();\n      onmessage = astrm(strm);\n    }, 9);\n  }\n  return AsyncGunzip;\n}();\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt, guze, function () {\n    return [gunzipSync];\n  }], function (ev) {\n    return pbf(gunzipSync(ev.data[0]));\n  }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n  return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/function () {\n  function Zlib(opts, cb) {\n    this.c = adler();\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be zlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Zlib.prototype.push = function (chunk, final) {\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Zlib.prototype.p = function (c, f) {\n    this.c.p(c);\n    var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n    if (this.v) zlh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 4, this.c.d());\n    this.ondata(raw, f);\n  };\n  return Zlib;\n}();\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/function () {\n  function AsyncZlib(opts, cb) {\n    astrmify([bDflt, zle, function () {\n      return [astrm, Deflate, Zlib];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Zlib(ev.data);\n      onmessage = astrm(strm);\n    }, 10);\n  }\n  return AsyncZlib;\n}();\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt, zle, function () {\n    return [zlibSync];\n  }], function (ev) {\n    return pbf(zlibSync(ev.data[0], ev.data[1]));\n  }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n  if (!opts) opts = {};\n  var a = adler();\n  a.p(data);\n  var d = dopt(data, opts, 2, 4);\n  return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/function () {\n  /**\n   * Creates a Zlib decompression stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Unzlib(cb) {\n    this.v = 1;\n    Inflate.call(this, cb);\n  }\n  /**\n   * Pushes a chunk to be unzlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Unzlib.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    if (this.v) {\n      if (this.p.length < 2 && !final) return;\n      this.p = this.p.subarray(2), this.v = 0;\n    }\n    if (final) {\n      if (this.p.length < 4) throw 'invalid zlib stream';\n      this.p = this.p.subarray(0, -4);\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n  };\n  return Unzlib;\n}();\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous Zlib decompression stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncUnzlib(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, zule, function () {\n      return [astrm, Inflate, Unzlib];\n    }], this, 0, function () {\n      var strm = new Unzlib();\n      onmessage = astrm(strm);\n    }, 11);\n  }\n  return AsyncUnzlib;\n}();\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt, zule, function () {\n    return [unzlibSync];\n  }], function (ev) {\n    return pbf(unzlibSync(ev.data[0], gu8(ev.data[1])));\n  }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n  return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/function () {\n  /**\n   * Creates a decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n  function Decompress(cb) {\n    this.G = Gunzip;\n    this.I = Inflate;\n    this.Z = Unzlib;\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Decompress.prototype.push = function (chunk, final) {\n    if (!this.ondata) throw 'no stream handler';\n    if (!this.s) {\n      if (this.p && this.p.length) {\n        var n = new u8(this.p.length + chunk.length);\n        n.set(this.p), n.set(chunk, this.p.length);\n      } else this.p = chunk;\n      if (this.p.length > 2) {\n        var _this_1 = this;\n        var cb = function () {\n          _this_1.ondata.apply(_this_1, arguments);\n        };\n        this.s = this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8 ? new this.G(cb) : (this.p[0] & 15) != 8 || this.p[0] >> 4 > 7 || (this.p[0] << 8 | this.p[1]) % 31 ? new this.I(cb) : new this.Z(cb);\n        this.s.push(this.p, final);\n        this.p = null;\n      }\n    } else this.s.push(chunk, final);\n  };\n  return Decompress;\n}();\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/function () {\n  /**\n  * Creates an asynchronous decompression stream\n  * @param cb The callback to call whenever data is decompressed\n  */\n  function AsyncDecompress(cb) {\n    this.G = AsyncGunzip;\n    this.I = AsyncInflate;\n    this.Z = AsyncUnzlib;\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  AsyncDecompress.prototype.push = function (chunk, final) {\n    Decompress.prototype.push.call(this, chunk, final);\n  };\n  return AsyncDecompress;\n}();\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzip(data, opts, cb) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflate(data, opts, cb) : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzipSync(data, out) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflateSync(data, out) : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n  for (var k in d) {\n    var val = d[k],\n      n = p + k;\n    if (val instanceof u8) t[n] = [val, o];else if (Array.isArray(val)) t[n] = [val[0], mrg(o, val[1])];else fltn(val, n + '/', t, o);\n  }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n  td.decode(et, {\n    stream: true\n  });\n  tds = 1;\n} catch (e) {}\n// decode UTF8\nvar dutf8 = function (d) {\n  for (var r = '', i = 0;;) {\n    var c = d[i++];\n    var eb = (c > 127) + (c > 223) + (c > 239);\n    if (i + eb > d.length) return [r, slc(d, i - 1)];\n    if (!eb) r += String.fromCharCode(c);else if (eb == 3) {\n      c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | d[i++] & 63) - 65536, r += String.fromCharCode(55296 | c >> 10, 56320 | c & 1023);\n    } else if (eb & 1) r += String.fromCharCode((c & 31) << 6 | d[i++] & 63);else r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | d[i++] & 63);\n  }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/function () {\n  /**\n   * Creates a UTF-8 decoding stream\n   * @param cb The callback to call whenever data is decoded\n   */\n  function DecodeUTF8(cb) {\n    this.ondata = cb;\n    if (tds) this.t = new TextDecoder();else this.p = et;\n  }\n  /**\n   * Pushes a chunk to be decoded from UTF-8 binary\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  DecodeUTF8.prototype.push = function (chunk, final) {\n    if (!this.ondata) throw 'no callback';\n    final = !!final;\n    if (this.t) {\n      this.ondata(this.t.decode(chunk, {\n        stream: true\n      }), final);\n      if (final) {\n        if (this.t.decode().length) throw 'invalid utf-8 data';\n        this.t = null;\n      }\n      return;\n    }\n    if (!this.p) throw 'stream finished';\n    var dat = new u8(this.p.length + chunk.length);\n    dat.set(this.p);\n    dat.set(chunk, this.p.length);\n    var _a = dutf8(dat),\n      ch = _a[0],\n      np = _a[1];\n    if (final) {\n      if (np.length) throw 'invalid utf-8 data';\n      this.p = null;\n    } else this.p = np;\n    this.ondata(ch, final);\n  };\n  return DecodeUTF8;\n}();\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/function () {\n  /**\n   * Creates a UTF-8 decoding stream\n   * @param cb The callback to call whenever data is encoded\n   */\n  function EncodeUTF8(cb) {\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be encoded to UTF-8\n   * @param chunk The string data to push\n   * @param final Whether this is the last chunk\n   */\n  EncodeUTF8.prototype.push = function (chunk, final) {\n    if (!this.ondata) throw 'no callback';\n    if (this.d) throw 'stream finished';\n    this.ondata(strToU8(chunk), this.d = final || false);\n  };\n  return EncodeUTF8;\n}();\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n  if (latin1) {\n    var ar_1 = new u8(str.length);\n    for (var i = 0; i < str.length; ++i) ar_1[i] = str.charCodeAt(i);\n    return ar_1;\n  }\n  if (te) return te.encode(str);\n  var l = str.length;\n  var ar = new u8(str.length + (str.length >> 1));\n  var ai = 0;\n  var w = function (v) {\n    ar[ai++] = v;\n  };\n  for (var i = 0; i < l; ++i) {\n    if (ai + 5 > ar.length) {\n      var n = new u8(ai + 8 + (l - i << 1));\n      n.set(ar);\n      ar = n;\n    }\n    var c = str.charCodeAt(i);\n    if (c < 128 || latin1) w(c);else if (c < 2048) w(192 | c >> 6), w(128 | c & 63);else if (c > 55295 && c < 57344) c = 65536 + (c & 1023 << 10) | str.charCodeAt(++i) & 1023, w(240 | c >> 18), w(128 | c >> 12 & 63), w(128 | c >> 6 & 63), w(128 | c & 63);else w(224 | c >> 12), w(128 | c >> 6 & 63), w(128 | c & 63);\n  }\n  return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n  if (latin1) {\n    var r = '';\n    for (var i = 0; i < dat.length; i += 16384) r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n    return r;\n  } else if (td) return td.decode(dat);else {\n    var _a = dutf8(dat),\n      out = _a[0],\n      ext = _a[1];\n    if (ext.length) throw 'invalid utf-8 data';\n    return out;\n  }\n}\n;\n// deflate bit flag\nvar dbf = function (l) {\n  return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0;\n};\n// skip local zip header\nvar slzh = function (d, b) {\n  return b + 30 + b2(d, b + 26) + b2(d, b + 28);\n};\n// read zip header\nvar zh = function (d, b, z) {\n  var fnl = b2(d, b + 28),\n    fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)),\n    es = b + 46 + fnl,\n    bs = b4(d, b + 20);\n  var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)],\n    sc = _a[0],\n    su = _a[1],\n    off = _a[2];\n  return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n  for (; b2(d, b) != 1; b += 4 + b2(d, b + 2));\n  return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n  var le = 0;\n  if (ex) {\n    for (var k in ex) {\n      var l = ex[k].length;\n      if (l > 65535) throw 'extra field too long';\n      le += l + 4;\n    }\n  }\n  return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n  var fl = fn.length,\n    ex = f.extra,\n    col = co && co.length;\n  var exl = exfl(ex);\n  wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n  if (ce != null) d[b++] = 20, d[b++] = f.os;\n  d[b] = 20, b += 2; // spec compliance? what's that?\n  d[b++] = f.flag << 1 | (c == null && 8), d[b++] = u && 8;\n  d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n  var dt = new Date(f.mtime == null ? Date.now() : f.mtime),\n    y = dt.getFullYear() - 1980;\n  if (y < 0 || y > 119) throw 'date not in range 1980-2099';\n  wbytes(d, b, y << 25 | dt.getMonth() + 1 << 21 | dt.getDate() << 16 | dt.getHours() << 11 | dt.getMinutes() << 5 | dt.getSeconds() >>> 1), b += 4;\n  if (c != null) {\n    wbytes(d, b, f.crc);\n    wbytes(d, b + 4, c);\n    wbytes(d, b + 8, f.size);\n  }\n  wbytes(d, b + 12, fl);\n  wbytes(d, b + 14, exl), b += 16;\n  if (ce != null) {\n    wbytes(d, b, col);\n    wbytes(d, b + 6, f.attrs);\n    wbytes(d, b + 10, ce), b += 14;\n  }\n  d.set(fn, b);\n  b += fl;\n  if (exl) {\n    for (var k in ex) {\n      var exf = ex[k],\n        l = exf.length;\n      wbytes(d, b, +k);\n      wbytes(d, b + 2, l);\n      d.set(exf, b + 4), b += 4 + l;\n    }\n  }\n  if (col) d.set(co, b), b += col;\n  return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n  wbytes(o, b, 0x6054B50); // skip disk\n  wbytes(o, b + 8, c);\n  wbytes(o, b + 10, c);\n  wbytes(o, b + 12, d);\n  wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/function () {\n  /**\n   * Creates a pass-through stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   */\n  function ZipPassThrough(filename) {\n    this.filename = filename;\n    this.c = crc();\n    this.size = 0;\n    this.compression = 0;\n  }\n  /**\n   * Processes a chunk and pushes to the output stream. You can override this\n   * method in a subclass for custom behavior, but by default this passes\n   * the data through. You must call this.ondata(err, chunk, final) at some\n   * point in this method.\n   * @param chunk The chunk to process\n   * @param final Whether this is the last chunk\n   */\n  ZipPassThrough.prototype.process = function (chunk, final) {\n    this.ondata(null, chunk, final);\n  };\n  /**\n   * Pushes a chunk to be added. If you are subclassing this with a custom\n   * compression algorithm, note that you must push data from the source\n   * file only, pre-compression.\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  ZipPassThrough.prototype.push = function (chunk, final) {\n    if (!this.ondata) throw 'no callback - add to ZIP archive before pushing';\n    this.c.p(chunk);\n    this.size += chunk.length;\n    if (final) this.crc = this.c.d();\n    this.process(chunk, final || false);\n  };\n  return ZipPassThrough;\n}();\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   * @param opts The compression options\n   */\n  function ZipDeflate(filename, opts) {\n    var _this_1 = this;\n    if (!opts) opts = {};\n    ZipPassThrough.call(this, filename);\n    this.d = new Deflate(opts, function (dat, final) {\n      _this_1.ondata(null, dat, final);\n    });\n    this.compression = 8;\n    this.flag = dbf(opts.level);\n  }\n  ZipDeflate.prototype.process = function (chunk, final) {\n    try {\n      this.d.push(chunk, final);\n    } catch (e) {\n      this.ondata(e, null, final);\n    }\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  ZipDeflate.prototype.push = function (chunk, final) {\n    ZipPassThrough.prototype.push.call(this, chunk, final);\n  };\n  return ZipDeflate;\n}();\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   * @param opts The compression options\n   */\n  function AsyncZipDeflate(filename, opts) {\n    var _this_1 = this;\n    if (!opts) opts = {};\n    ZipPassThrough.call(this, filename);\n    this.d = new AsyncDeflate(opts, function (err, dat, final) {\n      _this_1.ondata(err, dat, final);\n    });\n    this.compression = 8;\n    this.flag = dbf(opts.level);\n    this.terminate = this.d.terminate;\n  }\n  AsyncZipDeflate.prototype.process = function (chunk, final) {\n    this.d.push(chunk, final);\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  AsyncZipDeflate.prototype.push = function (chunk, final) {\n    ZipPassThrough.prototype.push.call(this, chunk, final);\n  };\n  return AsyncZipDeflate;\n}();\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/function () {\n  /**\n   * Creates an empty ZIP archive to which files can be added\n   * @param cb The callback to call whenever data for the generated ZIP archive\n   *           is available\n   */\n  function Zip(cb) {\n    this.ondata = cb;\n    this.u = [];\n    this.d = 1;\n  }\n  /**\n   * Adds a file to the ZIP archive\n   * @param file The file stream to add\n   */\n  Zip.prototype.add = function (file) {\n    var _this_1 = this;\n    if (this.d & 2) throw 'stream finished';\n    var f = strToU8(file.filename),\n      fl = f.length;\n    var com = file.comment,\n      o = com && strToU8(com);\n    var u = fl != file.filename.length || o && com.length != o.length;\n    var hl = fl + exfl(file.extra) + 30;\n    if (fl > 65535) throw 'filename too long';\n    var header = new u8(hl);\n    wzh(header, 0, file, f, u);\n    var chks = [header];\n    var pAll = function () {\n      for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n        var chk = chks_1[_i];\n        _this_1.ondata(null, chk, false);\n      }\n      chks = [];\n    };\n    var tr = this.d;\n    this.d = 0;\n    var ind = this.u.length;\n    var uf = mrg(file, {\n      f: f,\n      u: u,\n      o: o,\n      t: function () {\n        if (file.terminate) file.terminate();\n      },\n      r: function () {\n        pAll();\n        if (tr) {\n          var nxt = _this_1.u[ind + 1];\n          if (nxt) nxt.r();else _this_1.d = 1;\n        }\n        tr = 1;\n      }\n    });\n    var cl = 0;\n    file.ondata = function (err, dat, final) {\n      if (err) {\n        _this_1.ondata(err, dat, final);\n        _this_1.terminate();\n      } else {\n        cl += dat.length;\n        chks.push(dat);\n        if (final) {\n          var dd = new u8(16);\n          wbytes(dd, 0, 0x8074B50);\n          wbytes(dd, 4, file.crc);\n          wbytes(dd, 8, cl);\n          wbytes(dd, 12, file.size);\n          chks.push(dd);\n          uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n          if (tr) uf.r();\n          tr = 1;\n        } else if (tr) pAll();\n      }\n    };\n    this.u.push(uf);\n  };\n  /**\n   * Ends the process of adding files and prepares to emit the final chunks.\n   * This *must* be called after adding all desired files for the resulting\n   * ZIP file to work properly.\n   */\n  Zip.prototype.end = function () {\n    var _this_1 = this;\n    if (this.d & 2) {\n      if (this.d & 1) throw 'stream finishing';\n      throw 'stream finished';\n    }\n    if (this.d) this.e();else this.u.push({\n      r: function () {\n        if (!(_this_1.d & 1)) return;\n        _this_1.u.splice(-1, 1);\n        _this_1.e();\n      },\n      t: function () {}\n    });\n    this.d = 3;\n  };\n  Zip.prototype.e = function () {\n    var bt = 0,\n      l = 0,\n      tl = 0;\n    for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n      var f = _a[_i];\n      tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n    }\n    var out = new u8(tl + 22);\n    for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n      var f = _c[_b];\n      wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n      bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n    }\n    wzf(out, bt, this.u.length, tl, l);\n    this.ondata(null, out, true);\n    this.d = 2;\n  };\n  /**\n   * A method to terminate any internal workers used by the stream. Subsequent\n   * calls to add() will fail.\n   */\n  Zip.prototype.terminate = function () {\n    for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n      var f = _a[_i];\n      f.t();\n    }\n    this.d = 2;\n  };\n  return Zip;\n}();\nexport { Zip };\nexport function zip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  var r = {};\n  fltn(data, '', r, opts);\n  var k = Object.keys(r);\n  var lft = k.length,\n    o = 0,\n    tot = 0;\n  var slft = lft,\n    files = new Array(lft);\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var cbf = function () {\n    var out = new u8(tot + 22),\n      oe = o,\n      cdl = tot - o;\n    tot = 0;\n    for (var i = 0; i < slft; ++i) {\n      var f = files[i];\n      try {\n        var l = f.c.length;\n        wzh(out, tot, f, f.f, f.u, l);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        var loc = tot + badd;\n        out.set(f.c, loc);\n        wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n      } catch (e) {\n        return cb(e, null);\n      }\n    }\n    wzf(out, o, files.length, cdl, oe);\n    cb(null, out);\n  };\n  if (!lft) cbf();\n  var _loop_1 = function (i) {\n    var fn = k[i];\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var c = crc(),\n      size = file.length;\n    c.p(file);\n    var f = strToU8(fn),\n      s = f.length;\n    var com = p.comment,\n      m = com && strToU8(com),\n      ms = m && m.length;\n    var exl = exfl(p.extra);\n    var compression = p.level == 0 ? 0 : 8;\n    var cbl = function (e, d) {\n      if (e) {\n        tAll();\n        cb(e, null);\n      } else {\n        var l = d.length;\n        files[i] = mrg(p, {\n          size: size,\n          crc: c.d(),\n          c: d,\n          f: f,\n          m: m,\n          u: s != fn.length || m && com.length != ms,\n          compression: compression\n        });\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n        if (! --lft) cbf();\n      }\n    };\n    if (s > 65535) cbl('filename too long', null);\n    if (!compression) cbl(null, file);else if (size < 160000) {\n      try {\n        cbl(null, deflateSync(file, p));\n      } catch (e) {\n        cbl(e, null);\n      }\n    } else term.push(deflate(file, p, cbl));\n  };\n  // Cannot use lft because it can decrease\n  for (var i = 0; i < slft; ++i) {\n    _loop_1(i);\n  }\n  return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n  if (!opts) opts = {};\n  var r = {};\n  var files = [];\n  fltn(data, '', r, opts);\n  var o = 0;\n  var tot = 0;\n  for (var fn in r) {\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var compression = p.level == 0 ? 0 : 8;\n    var f = strToU8(fn),\n      s = f.length;\n    var com = p.comment,\n      m = com && strToU8(com),\n      ms = m && m.length;\n    var exl = exfl(p.extra);\n    if (s > 65535) throw 'filename too long';\n    var d = compression ? deflateSync(file, p) : file,\n      l = d.length;\n    var c = crc();\n    c.p(file);\n    files.push(mrg(p, {\n      size: file.length,\n      crc: c.d(),\n      c: d,\n      f: f,\n      m: m,\n      u: s != fn.length || m && com.length != ms,\n      o: o,\n      compression: compression\n    }));\n    o += 30 + s + exl + l;\n    tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n  }\n  var out = new u8(tot + 22),\n    oe = o,\n    cdl = tot - o;\n  for (var i = 0; i < files.length; ++i) {\n    var f = files[i];\n    wzh(out, f.o, f, f.f, f.u, f.c.length);\n    var badd = 30 + f.f.length + exfl(f.extra);\n    out.set(f.c, f.o + badd);\n    wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n  }\n  wzf(out, o, files.length, cdl, oe);\n  return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/function () {\n  function UnzipPassThrough() {}\n  UnzipPassThrough.prototype.push = function (data, final) {\n    this.ondata(null, data, final);\n  };\n  UnzipPassThrough.compression = 0;\n  return UnzipPassThrough;\n}();\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE decompression that can be used in ZIP archives\n   */\n  function UnzipInflate() {\n    var _this_1 = this;\n    this.i = new Inflate(function (dat, final) {\n      _this_1.ondata(null, dat, final);\n    });\n  }\n  UnzipInflate.prototype.push = function (data, final) {\n    try {\n      this.i.push(data, final);\n    } catch (e) {\n      this.ondata(e, data, final);\n    }\n  };\n  UnzipInflate.compression = 8;\n  return UnzipInflate;\n}();\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE decompression that can be used in ZIP archives\n   */\n  function AsyncUnzipInflate(_, sz) {\n    var _this_1 = this;\n    if (sz < 320000) {\n      this.i = new Inflate(function (dat, final) {\n        _this_1.ondata(null, dat, final);\n      });\n    } else {\n      this.i = new AsyncInflate(function (err, dat, final) {\n        _this_1.ondata(err, dat, final);\n      });\n      this.terminate = this.i.terminate;\n    }\n  }\n  AsyncUnzipInflate.prototype.push = function (data, final) {\n    if (this.i.terminate) data = slc(data, 0);\n    this.i.push(data, final);\n  };\n  AsyncUnzipInflate.compression = 8;\n  return AsyncUnzipInflate;\n}();\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/function () {\n  /**\n   * Creates a ZIP decompression stream\n   * @param cb The callback to call whenever a file in the ZIP archive is found\n   */\n  function Unzip(cb) {\n    this.onfile = cb;\n    this.k = [];\n    this.o = {\n      0: UnzipPassThrough\n    };\n    this.p = et;\n  }\n  /**\n   * Pushes a chunk to be unzipped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Unzip.prototype.push = function (chunk, final) {\n    var _this_1 = this;\n    if (!this.onfile) throw 'no callback';\n    if (!this.p) throw 'stream finished';\n    if (this.c > 0) {\n      var len = Math.min(this.c, chunk.length);\n      var toAdd = chunk.subarray(0, len);\n      this.c -= len;\n      if (this.d) this.d.push(toAdd, !this.c);else this.k[0].push(toAdd);\n      chunk = chunk.subarray(len);\n      if (chunk.length) return this.push(chunk, final);\n    } else {\n      var f = 0,\n        i = 0,\n        is = void 0,\n        buf = void 0;\n      if (!this.p.length) buf = chunk;else if (!chunk.length) buf = this.p;else {\n        buf = new u8(this.p.length + chunk.length);\n        buf.set(this.p), buf.set(chunk, this.p.length);\n      }\n      var l = buf.length,\n        oc = this.c,\n        add = oc && this.d;\n      var _loop_2 = function () {\n        var _a;\n        var sig = b4(buf, i);\n        if (sig == 0x4034B50) {\n          f = 1, is = i;\n          this_1.d = null;\n          this_1.c = 0;\n          var bf = b2(buf, i + 6),\n            cmp_1 = b2(buf, i + 8),\n            u = bf & 2048,\n            dd = bf & 8,\n            fnl = b2(buf, i + 26),\n            es = b2(buf, i + 28);\n          if (l > i + 30 + fnl + es) {\n            var chks_2 = [];\n            this_1.k.unshift(chks_2);\n            f = 2;\n            var sc_1 = b4(buf, i + 18),\n              su_1 = b4(buf, i + 22);\n            var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n            if (sc_1 == 4294967295) {\n              _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n            } else if (dd) sc_1 = -1;\n            i += es;\n            this_1.c = sc_1;\n            var d_1;\n            var file_1 = {\n              name: fn_1,\n              compression: cmp_1,\n              start: function () {\n                if (!file_1.ondata) throw 'no callback';\n                if (!sc_1) file_1.ondata(null, et, true);else {\n                  var ctr = _this_1.o[cmp_1];\n                  if (!ctr) throw 'unknown compression type ' + cmp_1;\n                  d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                  d_1.ondata = function (err, dat, final) {\n                    file_1.ondata(err, dat, final);\n                  };\n                  for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                    var dat = chks_3[_i];\n                    d_1.push(dat, false);\n                  }\n                  if (_this_1.k[0] == chks_2 && _this_1.c) _this_1.d = d_1;else d_1.push(et, true);\n                }\n              },\n              terminate: function () {\n                if (d_1 && d_1.terminate) d_1.terminate();\n              }\n            };\n            if (sc_1 >= 0) file_1.size = sc_1, file_1.originalSize = su_1;\n            this_1.onfile(file_1);\n          }\n          return \"break\";\n        } else if (oc) {\n          if (sig == 0x8074B50) {\n            is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n            return \"break\";\n          } else if (sig == 0x2014B50) {\n            is = i -= 4, f = 3, this_1.c = 0;\n            return \"break\";\n          }\n        }\n      };\n      var this_1 = this;\n      for (; i < l - 4; ++i) {\n        var state_1 = _loop_2();\n        if (state_1 === \"break\") break;\n      }\n      this.p = et;\n      if (oc < 0) {\n        var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n        if (add) add.push(dat, !!f);else this.k[+(f == 2)].push(dat);\n      }\n      if (f & 2) return this.push(buf.subarray(i), final);\n      this.p = buf.subarray(i);\n    }\n    if (final) {\n      if (this.c) throw 'invalid zip file';\n      this.p = null;\n    }\n  };\n  /**\n   * Registers a decoder with the stream, allowing for files compressed with\n   * the compression type provided to be expanded correctly\n   * @param decoder The decoder constructor\n   */\n  Unzip.prototype.register = function (decoder) {\n    this.o[decoder.compression] = decoder;\n  };\n  return Unzip;\n}();\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n  if (typeof cb != 'function') throw 'no callback';\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var files = {};\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) {\n      cb('invalid zip file', null);\n      return;\n    }\n  }\n  ;\n  var lft = b2(data, e + 8);\n  if (!lft) cb(null, {});\n  var c = lft;\n  var o = b4(data, e + 16);\n  var z = o == 4294967295;\n  if (z) {\n    e = b4(data, e - 12);\n    if (b4(data, e) != 0x6064B50) {\n      cb('invalid zip file', null);\n      return;\n    }\n    c = lft = b4(data, e + 32);\n    o = b4(data, e + 48);\n  }\n  var _loop_3 = function (i) {\n    var _a = zh(data, o, z),\n      c_1 = _a[0],\n      sc = _a[1],\n      su = _a[2],\n      fn = _a[3],\n      no = _a[4],\n      off = _a[5],\n      b = slzh(data, off);\n    o = no;\n    var cbl = function (e, d) {\n      if (e) {\n        tAll();\n        cb(e, null);\n      } else {\n        files[fn] = d;\n        if (! --lft) cb(null, files);\n      }\n    };\n    if (!c_1) cbl(null, slc(data, b, b + sc));else if (c_1 == 8) {\n      var infl = data.subarray(b, b + sc);\n      if (sc < 320000) {\n        try {\n          cbl(null, inflateSync(infl, new u8(su)));\n        } catch (e) {\n          cbl(e, null);\n        }\n      } else term.push(inflate(infl, {\n        size: su\n      }, cbl));\n    } else cbl('unknown compression type ' + c_1, null);\n  };\n  for (var i = 0; i < c; ++i) {\n    _loop_3(i);\n  }\n  return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n  var files = {};\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) throw 'invalid zip file';\n  }\n  ;\n  var c = b2(data, e + 8);\n  if (!c) return {};\n  var o = b4(data, e + 16);\n  var z = o == 4294967295;\n  if (z) {\n    e = b4(data, e - 12);\n    if (b4(data, e) != 0x6064B50) throw 'invalid zip file';\n    c = b4(data, e + 32);\n    o = b4(data, e + 48);\n  }\n  for (var i = 0; i < c; ++i) {\n    var _a = zh(data, o, z),\n      c_2 = _a[0],\n      sc = _a[1],\n      su = _a[2],\n      fn = _a[3],\n      no = _a[4],\n      off = _a[5],\n      b = slzh(data, off);\n    o = no;\n    if (!c_2) files[fn] = slc(data, b, b + sc);else if (c_2 == 8) files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));else throw 'unknown compression type ' + c_2;\n  }\n  return files;\n}", "map": {"version": 3, "names": ["ch2", "wk", "c", "id", "msg", "transfer", "cb", "w", "Worker", "URL", "createObjectURL", "Blob", "type", "onerror", "e", "error", "onmessage", "data", "postMessage", "u8", "Uint8Array", "u16", "Uint16Array", "u32", "Uint32Array", "fleb", "fdeb", "clim", "freb", "eb", "start", "b", "i", "r", "j", "_a", "fl", "revfl", "_b", "fd", "revfd", "rev", "x", "hMap", "cd", "mb", "s", "length", "l", "le", "co", "rvb", "sv", "r_1", "v", "m", "flt", "fdt", "flm", "flrm", "fdm", "fdrm", "max", "a", "bits", "d", "p", "o", "bits16", "shft", "slc", "n", "set", "subarray", "inflt", "dat", "buf", "st", "sl", "noBuf", "noSt", "cbuf", "bl", "nbuf", "Math", "final", "f", "pos", "bt", "lm", "dm", "lbt", "dbt", "tbts", "t", "hLit", "hcLen", "tl", "ldt", "clt", "clb", "clbmsk", "clm", "lt", "dt", "lms", "dms", "lpos", "sym", "add", "dsym", "end", "wbits", "wbits16", "hTree", "push", "t2", "slice", "et", "sort", "i0", "i1", "i2", "maxSym", "tr", "mbt", "ln", "lft", "cst", "i2_1", "i2_2", "i2_3", "lc", "cl", "cli", "cln", "cls", "clen", "cf", "wfblk", "out", "wblk", "syms", "lf", "df", "li", "bs", "dlt", "mlb", "ddt", "mdb", "_c", "lclt", "nlc", "_d", "lcdt", "ndc", "lcfreq", "_e", "lct", "mlcb", "nlcc", "flen", "ftlen", "dtlen", "ll", "dl", "llm", "lcts", "it", "clct", "len", "dst", "deo", "dflt", "lvl", "plvl", "pre", "post", "lst", "ceil", "opt", "msk_1", "prev", "head", "bs1_1", "bs2_1", "hsh", "lc_1", "wi", "hv", "imod", "pimod", "rem", "ch_1", "dif", "maxn", "min", "maxd", "ml", "nl", "mmd", "md", "ti", "pti", "lin", "din", "crct", "Int32Array", "k", "crc", "cr", "<PERSON><PERSON>", "dopt", "level", "mem", "log", "mrg", "wcln", "fn", "fnStr", "td", "toString", "ks", "indexOf", "lastIndexOf", "replace", "split", "st_1", "prototype", "spInd", "ch", "cbfs", "constructor", "buffer", "wrkr", "fns", "init", "td_1", "bInflt", "inflateSync", "pbf", "gu8", "bDflt", "deflateSync", "gze", "gzh", "gzhl", "wbytes", "guze", "gzs", "gzl", "zle", "zlh", "zule", "zlv", "size", "cbify", "opts", "err", "terminate", "consume", "astrm", "strm", "ondata", "ev", "astrmify", "call", "b2", "b4", "b8", "filename", "mtime", "floor", "Date", "now", "charCodeAt", "flg", "zs", "lv", "AsyncCmpStrm", "Deflate", "chunk", "AsyncDeflate", "deflate", "Inflate", "bts", "AsyncInflate", "inflate", "Gzip", "raw", "AsyncGzip", "gzip", "gzipSync", "<PERSON><PERSON><PERSON>", "AsyncGunzip", "gunzip", "gunzipSync", "<PERSON><PERSON><PERSON>", "AsyncZlib", "zlib", "zlibSync", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "unz<PERSON>b", "unzlibSync", "compress", "AsyncCompress", "compressSync", "Compress", "Decompress", "G", "I", "Z", "_this_1", "apply", "arguments", "AsyncDecompress", "decompress", "decompressSync", "fltn", "val", "Array", "isArray", "te", "TextEncoder", "TextDecoder", "tds", "decode", "stream", "dutf8", "String", "fromCharCode", "DecodeUTF8", "np", "EncodeUTF8", "strToU8", "str", "latin1", "ar_1", "encode", "ar", "ai", "strFromU8", "ext", "dbf", "slzh", "zh", "z", "fnl", "es", "z64e", "sc", "su", "off", "exfl", "ex", "wzh", "u", "ce", "extra", "col", "exl", "os", "flag", "compression", "y", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "attrs", "exf", "wzf", "ZipPassThrough", "process", "ZipDeflate", "AsyncZipDeflate", "Zip", "file", "com", "comment", "hl", "header", "chks", "pAll", "_i", "chks_1", "chk", "ind", "uf", "nxt", "dd", "splice", "zip", "Object", "keys", "tot", "slft", "files", "term", "tAll", "cbf", "oe", "cdl", "badd", "loc", "_loop_1", "ms", "cbl", "zipSync", "UnzipPassThrough", "UnzipInflate", "AsyncUnzipInflate", "_", "sz", "Unzip", "onfile", "toAdd", "is", "oc", "_loop_2", "sig", "this_1", "bf", "cmp_1", "chks_2", "unshift", "sc_1", "su_1", "fn_1", "d_1", "file_1", "name", "ctr", "chks_3", "originalSize", "state_1", "register", "decoder", "unzip", "_loop_3", "c_1", "no", "infl", "unzipSync", "c_2"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-stdlib/node_modules/fflate/esm/browser.js"], "sourcesContent": ["// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = (function (c, id, msg, transfer, cb) {\n    var w = new Worker(ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], { type: 'text/javascript' }))));\n    w.onerror = function (e) { return cb(e.error, null); };\n    w.onmessage = function (e) { return cb(null, e.data); };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,GAAG,GAAG,CAAC,CAAC;AACZ,IAAIC,EAAE,GAAI,SAAAA,CAAUC,CAAC,EAAEC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EAC1C,IAAIC,CAAC,GAAG,IAAIC,MAAM,CAACR,GAAG,CAACG,EAAE,CAAC,KAAKH,GAAG,CAACG,EAAE,CAAC,GAAGM,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACT,CAAC,CAAC,EAAE;IAAEU,IAAI,EAAE;EAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1GL,CAAC,CAACM,OAAO,GAAG,UAAUC,CAAC,EAAE;IAAE,OAAOR,EAAE,CAACQ,CAAC,CAACC,KAAK,EAAE,IAAI,CAAC;EAAE,CAAC;EACtDR,CAAC,CAACS,SAAS,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAOR,EAAE,CAAC,IAAI,EAAEQ,CAAC,CAACG,IAAI,CAAC;EAAE,CAAC;EACvDV,CAAC,CAACW,WAAW,CAACd,GAAG,EAAEC,QAAQ,CAAC;EAC5B,OAAOE,CAAC;AACZ,CAAE;;AAEF;AACA,IAAIY,EAAE,GAAGC,UAAU;EAAEC,GAAG,GAAGC,WAAW;EAAEC,GAAG,GAAGC,WAAW;AACzD;AACA,IAAIC,IAAI,GAAG,IAAIN,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAa,CAAC,EAAE,CAAC,EAAE,gBAAiB,CAAC,CAAC,CAAC;AACjJ;AACA;AACA,IAAIO,IAAI,GAAG,IAAIP,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AACxI;AACA,IAAIQ,IAAI,GAAG,IAAIR,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACrF;AACA,IAAIS,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAE;EAC5B,IAAIC,CAAC,GAAG,IAAIV,GAAG,CAAC,EAAE,CAAC;EACnB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;IACzBD,CAAC,CAACC,CAAC,CAAC,GAAGF,KAAK,IAAI,CAAC,IAAID,EAAE,CAACG,CAAC,GAAG,CAAC,CAAC;EAClC;EACA;EACA,IAAIC,CAAC,GAAG,IAAIV,GAAG,CAACQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;IACzB,KAAK,IAAIE,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC,EAAEE,CAAC,GAAGH,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAEE,CAAC,EAAE;MAClCD,CAAC,CAACC,CAAC,CAAC,GAAKA,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC,IAAK,CAAC,GAAIA,CAAC;IAChC;EACJ;EACA,OAAO,CAACD,CAAC,EAAEE,CAAC,CAAC;AACjB,CAAC;AACD,IAAIE,EAAE,GAAGP,IAAI,CAACH,IAAI,EAAE,CAAC,CAAC;EAAEW,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;EAAEE,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;AACjD;AACAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;AAC7B,IAAIC,EAAE,GAAGV,IAAI,CAACF,IAAI,EAAE,CAAC,CAAC;EAAEa,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;EAAEE,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;AACjD;AACA,IAAIG,GAAG,GAAG,IAAIpB,GAAG,CAAC,KAAK,CAAC;AACxB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,KAAK,EAAE,EAAEA,CAAC,EAAE;EAC5B;EACA,IAAIU,CAAC,GAAI,CAACV,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAClDU,CAAC,GAAI,CAACA,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAC9CA,CAAC,GAAI,CAACA,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAC9CD,GAAG,CAACT,CAAC,CAAC,GAAG,CAAE,CAACU,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE,MAAM,CAAC;AAC/D;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAI,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAE;EAC7B,IAAIa,CAAC,GAAGF,EAAE,CAACG,MAAM;EACjB;EACA,IAAIf,CAAC,GAAG,CAAC;EACT;EACA,IAAIgB,CAAC,GAAG,IAAI3B,GAAG,CAACwB,EAAE,CAAC;EACnB;EACA,OAAOb,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EACb,EAAEgB,CAAC,CAACJ,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB;EACA,IAAIiB,EAAE,GAAG,IAAI5B,GAAG,CAACwB,EAAE,CAAC;EACpB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,EAAE,EAAE,EAAEb,CAAC,EAAE;IACrBiB,EAAE,CAACjB,CAAC,CAAC,GAAIiB,EAAE,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAGgB,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC;EACvC;EACA,IAAIkB,EAAE;EACN,IAAIjB,CAAC,EAAE;IACH;IACAiB,EAAE,GAAG,IAAI7B,GAAG,CAAC,CAAC,IAAIwB,EAAE,CAAC;IACrB;IACA,IAAIM,GAAG,GAAG,EAAE,GAAGN,EAAE;IACjB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACpB;MACA,IAAIY,EAAE,CAACZ,CAAC,CAAC,EAAE;QACP;QACA,IAAIoB,EAAE,GAAIpB,CAAC,IAAI,CAAC,GAAIY,EAAE,CAACZ,CAAC,CAAC;QACzB;QACA,IAAIqB,GAAG,GAAGR,EAAE,GAAGD,EAAE,CAACZ,CAAC,CAAC;QACpB;QACA,IAAIsB,CAAC,GAAGL,EAAE,CAACL,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAIqB,GAAG;QAC9B;QACA,KAAK,IAAIE,CAAC,GAAGD,CAAC,GAAI,CAAC,CAAC,IAAID,GAAG,IAAI,CAAE,EAAEC,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;UAC5C;UACAJ,EAAE,CAACT,GAAG,CAACa,CAAC,CAAC,KAAKH,GAAG,CAAC,GAAGC,EAAE;QAC3B;MACJ;IACJ;EACJ,CAAC,MACI;IACDF,EAAE,GAAG,IAAI7B,GAAG,CAACyB,CAAC,CAAC;IACf,KAAKd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACpB,IAAIY,EAAE,CAACZ,CAAC,CAAC,EAAE;QACPkB,EAAE,CAAClB,CAAC,CAAC,GAAGS,GAAG,CAACQ,EAAE,CAACL,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAM,EAAE,GAAGY,EAAE,CAACZ,CAAC,CAAE;MACjD;IACJ;EACJ;EACA,OAAOkB,EAAE;AACb,CAAE;AACF;AACA,IAAIM,GAAG,GAAG,IAAIrC,EAAE,CAAC,GAAG,CAAC;AACrB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EACxBwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd;AACA,IAAIyB,GAAG,GAAG,IAAItC,EAAE,CAAC,EAAE,CAAC;AACpB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EACvByB,GAAG,CAACzB,CAAC,CAAC,GAAG,CAAC;AACd;AACA,IAAI0B,GAAG,GAAG,aAAcf,IAAI,CAACa,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAEG,IAAI,GAAG,aAAchB,IAAI,CAACa,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7E;AACA,IAAII,GAAG,GAAG,aAAcjB,IAAI,CAACc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAEI,IAAI,GAAG,aAAclB,IAAI,CAACc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7E;AACA,IAAIK,GAAG,GAAG,SAAAA,CAAUC,CAAC,EAAE;EACnB,IAAIR,CAAC,GAAGQ,CAAC,CAAC,CAAC,CAAC;EACZ,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,CAAC,CAAChB,MAAM,EAAE,EAAEf,CAAC,EAAE;IAC/B,IAAI+B,CAAC,CAAC/B,CAAC,CAAC,GAAGuB,CAAC,EACRA,CAAC,GAAGQ,CAAC,CAAC/B,CAAC,CAAC;EAChB;EACA,OAAOuB,CAAC;AACZ,CAAC;AACD;AACA,IAAIS,IAAI,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAEX,CAAC,EAAE;EAC1B,IAAIY,CAAC,GAAID,CAAC,GAAG,CAAC,GAAI,CAAC;EACnB,OAAQ,CAACD,CAAC,CAACE,CAAC,CAAC,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,MAAMD,CAAC,GAAG,CAAC,CAAC,GAAIX,CAAC;AACpD,CAAC;AACD;AACA,IAAIa,MAAM,GAAG,SAAAA,CAAUH,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,GAAI,CAAC;EACnB,OAAQ,CAACD,CAAC,CAACE,CAAC,CAAC,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,MAAMD,CAAC,GAAG,CAAC,CAAC;AAClE,CAAC;AACD;AACA,IAAIG,IAAI,GAAG,SAAAA,CAAUH,CAAC,EAAE;EAAE,OAAO,CAAEA,CAAC,GAAG,CAAC,GAAI,CAAC,KAAKA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAE,CAAC;AAChE;AACA;AACA,IAAII,GAAG,GAAG,SAAAA,CAAUhB,CAAC,EAAER,CAAC,EAAEhC,CAAC,EAAE;EACzB,IAAIgC,CAAC,IAAI,IAAI,IAAIA,CAAC,GAAG,CAAC,EAClBA,CAAC,GAAG,CAAC;EACT,IAAIhC,CAAC,IAAI,IAAI,IAAIA,CAAC,GAAGwC,CAAC,CAACP,MAAM,EACzBjC,CAAC,GAAGwC,CAAC,CAACP,MAAM;EAChB;EACA,IAAIwB,CAAC,GAAG,KAAKjB,CAAC,YAAYjC,GAAG,GAAGA,GAAG,GAAGiC,CAAC,YAAY/B,GAAG,GAAGA,GAAG,GAAGJ,EAAE,EAAEL,CAAC,GAAGgC,CAAC,CAAC;EACzEyB,CAAC,CAACC,GAAG,CAAClB,CAAC,CAACmB,QAAQ,CAAC3B,CAAC,EAAEhC,CAAC,CAAC,CAAC;EACvB,OAAOyD,CAAC;AACZ,CAAC;AACD;AACA,IAAIG,KAAK,GAAG,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;EAChC;EACA,IAAIC,EAAE,GAAGH,GAAG,CAAC5B,MAAM;EACnB,IAAI,CAAC+B,EAAE,IAAKD,EAAE,IAAI,CAACA,EAAE,CAAC7B,CAAC,IAAI8B,EAAE,GAAG,CAAE,EAC9B,OAAOF,GAAG,IAAI,IAAIzD,EAAE,CAAC,CAAC,CAAC;EAC3B;EACA,IAAI4D,KAAK,GAAG,CAACH,GAAG,IAAIC,EAAE;EACtB;EACA,IAAIG,IAAI,GAAG,CAACH,EAAE,IAAIA,EAAE,CAAC7C,CAAC;EACtB,IAAI,CAAC6C,EAAE,EACHA,EAAE,GAAG,CAAC,CAAC;EACX;EACA,IAAI,CAACD,GAAG,EACJA,GAAG,GAAG,IAAIzD,EAAE,CAAC2D,EAAE,GAAG,CAAC,CAAC;EACxB;EACA,IAAIG,IAAI,GAAG,SAAAA,CAAUjC,CAAC,EAAE;IACpB,IAAIkC,EAAE,GAAGN,GAAG,CAAC7B,MAAM;IACnB;IACA,IAAIC,CAAC,GAAGkC,EAAE,EAAE;MACR;MACA,IAAIC,IAAI,GAAG,IAAIhE,EAAE,CAACiE,IAAI,CAACtB,GAAG,CAACoB,EAAE,GAAG,CAAC,EAAElC,CAAC,CAAC,CAAC;MACtCmC,IAAI,CAACX,GAAG,CAACI,GAAG,CAAC;MACbA,GAAG,GAAGO,IAAI;IACd;EACJ,CAAC;EACD;EACA,IAAIE,KAAK,GAAGR,EAAE,CAACS,CAAC,IAAI,CAAC;IAAEC,GAAG,GAAGV,EAAE,CAACX,CAAC,IAAI,CAAC;IAAEsB,EAAE,GAAGX,EAAE,CAAC9C,CAAC,IAAI,CAAC;IAAE0D,EAAE,GAAGZ,EAAE,CAAC7B,CAAC;IAAE0C,EAAE,GAAGb,EAAE,CAACZ,CAAC;IAAE0B,GAAG,GAAGd,EAAE,CAACtB,CAAC;IAAEqC,GAAG,GAAGf,EAAE,CAACN,CAAC;EACpG;EACA,IAAIsB,IAAI,GAAGf,EAAE,GAAG,CAAC;EACjB,GAAG;IACC,IAAI,CAACW,EAAE,EAAE;MACL;MACAZ,EAAE,CAACS,CAAC,GAAGD,KAAK,GAAGrB,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC;MAChC;MACA,IAAI3E,IAAI,GAAGoD,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;MAChCA,GAAG,IAAI,CAAC;MACR,IAAI,CAAC3E,IAAI,EAAE;QACP;QACA,IAAIkC,CAAC,GAAGuB,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAC;UAAEvC,CAAC,GAAG2B,GAAG,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAI6B,GAAG,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE;UAAEgD,CAAC,GAAGhD,CAAC,GAAGE,CAAC;QACpE,IAAI8C,CAAC,GAAGhB,EAAE,EAAE;UACR,IAAIE,IAAI,EACJ,MAAM,gBAAgB;UAC1B;QACJ;QACA;QACA,IAAID,KAAK,EACLE,IAAI,CAACO,EAAE,GAAGxC,CAAC,CAAC;QAChB;QACA4B,GAAG,CAACJ,GAAG,CAACG,GAAG,CAACF,QAAQ,CAAC3B,CAAC,EAAEgD,CAAC,CAAC,EAAEN,EAAE,CAAC;QAC/B;QACAX,EAAE,CAAC9C,CAAC,GAAGyD,EAAE,IAAIxC,CAAC,EAAE6B,EAAE,CAACX,CAAC,GAAGqB,GAAG,GAAGO,CAAC,GAAG,CAAC;QAClC;MACJ,CAAC,MACI,IAAIlF,IAAI,IAAI,CAAC,EACd6E,EAAE,GAAG9B,IAAI,EAAE+B,EAAE,GAAG7B,IAAI,EAAE8B,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,CAAC,KACtC,IAAIhF,IAAI,IAAI,CAAC,EAAE;QAChB;QACA,IAAImF,IAAI,GAAG/B,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;UAAES,KAAK,GAAGhC,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;QACxE,IAAIU,EAAE,GAAGF,IAAI,GAAG/B,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC1CA,GAAG,IAAI,EAAE;QACT;QACA,IAAIW,GAAG,GAAG,IAAI/E,EAAE,CAAC8E,EAAE,CAAC;QACpB;QACA,IAAIE,GAAG,GAAG,IAAIhF,EAAE,CAAC,EAAE,CAAC;QACpB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,KAAK,EAAE,EAAEhE,CAAC,EAAE;UAC5B;UACAmE,GAAG,CAACxE,IAAI,CAACK,CAAC,CAAC,CAAC,GAAGgC,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAGvD,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5C;QACAuD,GAAG,IAAIS,KAAK,GAAG,CAAC;QAChB;QACA,IAAII,GAAG,GAAGtC,GAAG,CAACqC,GAAG,CAAC;UAAEE,MAAM,GAAG,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC;QAC3C;QACA,IAAIE,GAAG,GAAG3D,IAAI,CAACwD,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;QAC3B,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,EAAE,GAAG;UACrB,IAAIhE,CAAC,GAAGqE,GAAG,CAACtC,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAEc,MAAM,CAAC,CAAC;UACnC;UACAd,GAAG,IAAItD,CAAC,GAAG,EAAE;UACb;UACA,IAAIa,CAAC,GAAGb,CAAC,KAAK,CAAC;UACf;UACA,IAAIa,CAAC,GAAG,EAAE,EAAE;YACRoD,GAAG,CAAClE,CAAC,EAAE,CAAC,GAAGc,CAAC;UAChB,CAAC,MACI;YACD;YACA,IAAI5C,CAAC,GAAG,CAAC;cAAEqE,CAAC,GAAG,CAAC;YAChB,IAAIzB,CAAC,IAAI,EAAE,EACPyB,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAErF,CAAC,GAAGgG,GAAG,CAAClE,CAAC,GAAG,CAAC,CAAC,CAAC,KACnD,IAAIc,CAAC,IAAI,EAAE,EACZyB,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,IAAI,CAAC,CAAC,KACnC,IAAIzC,CAAC,IAAI,EAAE,EACZyB,CAAC,GAAG,EAAE,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC;YAC1C,OAAOhB,CAAC,EAAE,EACN2B,GAAG,CAAClE,CAAC,EAAE,CAAC,GAAG9B,CAAC;UACpB;QACJ;QACA;QACA,IAAIqG,EAAE,GAAGL,GAAG,CAACzB,QAAQ,CAAC,CAAC,EAAEsB,IAAI,CAAC;UAAES,EAAE,GAAGN,GAAG,CAACzB,QAAQ,CAACsB,IAAI,CAAC;QACvD;QACAJ,GAAG,GAAG7B,GAAG,CAACyC,EAAE,CAAC;QACb;QACAX,GAAG,GAAG9B,GAAG,CAAC0C,EAAE,CAAC;QACbf,EAAE,GAAG9C,IAAI,CAAC4D,EAAE,EAAEZ,GAAG,EAAE,CAAC,CAAC;QACrBD,EAAE,GAAG/C,IAAI,CAAC6D,EAAE,EAAEZ,GAAG,EAAE,CAAC,CAAC;MACzB,CAAC,MAEG,MAAM,oBAAoB;MAC9B,IAAIL,GAAG,GAAGM,IAAI,EAAE;QACZ,IAAIb,IAAI,EACJ,MAAM,gBAAgB;QAC1B;MACJ;IACJ;IACA;IACA;IACA,IAAID,KAAK,EACLE,IAAI,CAACO,EAAE,GAAG,MAAM,CAAC;IACrB,IAAIiB,GAAG,GAAG,CAAC,CAAC,IAAId,GAAG,IAAI,CAAC;MAAEe,GAAG,GAAG,CAAC,CAAC,IAAId,GAAG,IAAI,CAAC;IAC9C,IAAIe,IAAI,GAAGpB,GAAG;IACd,QAAQoB,IAAI,GAAGpB,GAAG,EAAE;MAChB;MACA,IAAIrF,CAAC,GAAGuF,EAAE,CAACrB,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAGkB,GAAG,CAAC;QAAEG,GAAG,GAAG1G,CAAC,KAAK,CAAC;MACjDqF,GAAG,IAAIrF,CAAC,GAAG,EAAE;MACb,IAAIqF,GAAG,GAAGM,IAAI,EAAE;QACZ,IAAIb,IAAI,EACJ,MAAM,gBAAgB;QAC1B;MACJ;MACA,IAAI,CAAC9E,CAAC,EACF,MAAM,wBAAwB;MAClC,IAAI0G,GAAG,GAAG,GAAG,EACThC,GAAG,CAACY,EAAE,EAAE,CAAC,GAAGoB,GAAG,CAAC,KACf,IAAIA,GAAG,IAAI,GAAG,EAAE;QACjBD,IAAI,GAAGpB,GAAG,EAAEE,EAAE,GAAG,IAAI;QACrB;MACJ,CAAC,MACI;QACD,IAAIoB,GAAG,GAAGD,GAAG,GAAG,GAAG;QACnB;QACA,IAAIA,GAAG,GAAG,GAAG,EAAE;UACX;UACA,IAAI5E,CAAC,GAAG4E,GAAG,GAAG,GAAG;YAAE7E,CAAC,GAAGN,IAAI,CAACO,CAAC,CAAC;UAC9B6E,GAAG,GAAG7C,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,IAAIxD,CAAC,IAAI,CAAC,CAAC,GAAGK,EAAE,CAACJ,CAAC,CAAC;UAC1CuD,GAAG,IAAIxD,CAAC;QACZ;QACA;QACA,IAAIkC,CAAC,GAAGyB,EAAE,CAACtB,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAGmB,GAAG,CAAC;UAAEI,IAAI,GAAG7C,CAAC,KAAK,CAAC;QAClD,IAAI,CAACA,CAAC,EACF,MAAM,kBAAkB;QAC5BsB,GAAG,IAAItB,CAAC,GAAG,EAAE;QACb,IAAIuC,EAAE,GAAGjE,EAAE,CAACuE,IAAI,CAAC;QACjB,IAAIA,IAAI,GAAG,CAAC,EAAE;UACV,IAAI/E,CAAC,GAAGL,IAAI,CAACoF,IAAI,CAAC;UAClBN,EAAE,IAAIpC,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxD,CAAC,IAAI,CAAE,EAAEwD,GAAG,IAAIxD,CAAC;QACrD;QACA,IAAIwD,GAAG,GAAGM,IAAI,EAAE;UACZ,IAAIb,IAAI,EACJ,MAAM,gBAAgB;UAC1B;QACJ;QACA,IAAID,KAAK,EACLE,IAAI,CAACO,EAAE,GAAG,MAAM,CAAC;QACrB,IAAIuB,GAAG,GAAGvB,EAAE,GAAGqB,GAAG;QAClB,OAAOrB,EAAE,GAAGuB,GAAG,EAAEvB,EAAE,IAAI,CAAC,EAAE;UACtBZ,GAAG,CAACY,EAAE,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAGgB,EAAE,CAAC;UACtB5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;UAC9B5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;UAC9B5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;QAClC;QACAhB,EAAE,GAAGuB,GAAG;MACZ;IACJ;IACAlC,EAAE,CAAC7B,CAAC,GAAGyC,EAAE,EAAEZ,EAAE,CAACX,CAAC,GAAGyC,IAAI,EAAE9B,EAAE,CAAC9C,CAAC,GAAGyD,EAAE;IACjC,IAAIC,EAAE,EACFJ,KAAK,GAAG,CAAC,EAAER,EAAE,CAACtB,CAAC,GAAGoC,GAAG,EAAEd,EAAE,CAACZ,CAAC,GAAGyB,EAAE,EAAEb,EAAE,CAACN,CAAC,GAAGqB,GAAG;EACpD,CAAC,QAAQ,CAACP,KAAK;EACf,OAAOG,EAAE,IAAIZ,GAAG,CAAC7B,MAAM,GAAG6B,GAAG,GAAGN,GAAG,CAACM,GAAG,EAAE,CAAC,EAAEY,EAAE,CAAC;AACnD,CAAC;AACD;AACA,IAAIwB,KAAK,GAAG,SAAAA,CAAU/C,CAAC,EAAEC,CAAC,EAAEZ,CAAC,EAAE;EAC3BA,CAAC,KAAKY,CAAC,GAAG,CAAC;EACX,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,GAAI,CAAC;EACnBD,CAAC,CAACE,CAAC,CAAC,IAAIb,CAAC;EACTW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,CAAC;AACvB,CAAC;AACD;AACA,IAAI2D,OAAO,GAAG,SAAAA,CAAUhD,CAAC,EAAEC,CAAC,EAAEZ,CAAC,EAAE;EAC7BA,CAAC,KAAKY,CAAC,GAAG,CAAC;EACX,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,GAAI,CAAC;EACnBD,CAAC,CAACE,CAAC,CAAC,IAAIb,CAAC;EACTW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,CAAC;EACnBW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,EAAE;AACxB,CAAC;AACD;AACA,IAAI4D,KAAK,GAAG,SAAAA,CAAUjD,CAAC,EAAEpB,EAAE,EAAE;EACzB;EACA,IAAIiD,CAAC,GAAG,EAAE;EACV,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,CAAC,CAAClB,MAAM,EAAE,EAAEf,CAAC,EAAE;IAC/B,IAAIiC,CAAC,CAACjC,CAAC,CAAC,EACJ8D,CAAC,CAACqB,IAAI,CAAC;MAAErE,CAAC,EAAEd,CAAC;MAAEsD,CAAC,EAAErB,CAAC,CAACjC,CAAC;IAAE,CAAC,CAAC;EACjC;EACA,IAAIc,CAAC,GAAGgD,CAAC,CAAC/C,MAAM;EAChB,IAAIqE,EAAE,GAAGtB,CAAC,CAACuB,KAAK,CAAC,CAAC;EAClB,IAAI,CAACvE,CAAC,EACF,OAAO,CAACwE,EAAE,EAAE,CAAC,CAAC;EAClB,IAAIxE,CAAC,IAAI,CAAC,EAAE;IACR,IAAIQ,CAAC,GAAG,IAAInC,EAAE,CAAC2E,CAAC,CAAC,CAAC,CAAC,CAAChD,CAAC,GAAG,CAAC,CAAC;IAC1BQ,CAAC,CAACwC,CAAC,CAAC,CAAC,CAAC,CAAChD,CAAC,CAAC,GAAG,CAAC;IACb,OAAO,CAACQ,CAAC,EAAE,CAAC,CAAC;EACjB;EACAwC,CAAC,CAACyB,IAAI,CAAC,UAAUxD,CAAC,EAAEhC,CAAC,EAAE;IAAE,OAAOgC,CAAC,CAACuB,CAAC,GAAGvD,CAAC,CAACuD,CAAC;EAAE,CAAC,CAAC;EAC7C;EACA;EACAQ,CAAC,CAACqB,IAAI,CAAC;IAAErE,CAAC,EAAE,CAAC,CAAC;IAAEwC,CAAC,EAAE;EAAM,CAAC,CAAC;EAC3B,IAAItC,CAAC,GAAG8C,CAAC,CAAC,CAAC,CAAC;IAAE7D,CAAC,GAAG6D,CAAC,CAAC,CAAC,CAAC;IAAE0B,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;EAC9C5B,CAAC,CAAC,CAAC,CAAC,GAAG;IAAEhD,CAAC,EAAE,CAAC,CAAC;IAAEwC,CAAC,EAAEtC,CAAC,CAACsC,CAAC,GAAGrD,CAAC,CAACqD,CAAC;IAAEtC,CAAC,EAAEA,CAAC;IAAEf,CAAC,EAAEA;EAAE,CAAC;EAC1C;EACA;EACA;EACA;EACA;EACA,OAAOwF,EAAE,IAAI3E,CAAC,GAAG,CAAC,EAAE;IAChBE,CAAC,GAAG8C,CAAC,CAACA,CAAC,CAAC0B,EAAE,CAAC,CAAClC,CAAC,GAAGQ,CAAC,CAAC4B,EAAE,CAAC,CAACpC,CAAC,GAAGkC,EAAE,EAAE,GAAGE,EAAE,EAAE,CAAC;IACtCzF,CAAC,GAAG6D,CAAC,CAAC0B,EAAE,IAAIC,EAAE,IAAI3B,CAAC,CAAC0B,EAAE,CAAC,CAAClC,CAAC,GAAGQ,CAAC,CAAC4B,EAAE,CAAC,CAACpC,CAAC,GAAGkC,EAAE,EAAE,GAAGE,EAAE,EAAE,CAAC;IAClD5B,CAAC,CAAC2B,EAAE,EAAE,CAAC,GAAG;MAAE3E,CAAC,EAAE,CAAC,CAAC;MAAEwC,CAAC,EAAEtC,CAAC,CAACsC,CAAC,GAAGrD,CAAC,CAACqD,CAAC;MAAEtC,CAAC,EAAEA,CAAC;MAAEf,CAAC,EAAEA;IAAE,CAAC;EACjD;EACA,IAAI0F,MAAM,GAAGP,EAAE,CAAC,CAAC,CAAC,CAACtE,CAAC;EACpB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;IACxB,IAAIoF,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC,GAAG6E,MAAM,EAChBA,MAAM,GAAGP,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;EACxB;EACA;EACA,IAAI8E,EAAE,GAAG,IAAIvG,GAAG,CAACsG,MAAM,GAAG,CAAC,CAAC;EAC5B;EACA,IAAIE,GAAG,GAAGC,EAAE,CAAChC,CAAC,CAAC2B,EAAE,GAAG,CAAC,CAAC,EAAEG,EAAE,EAAE,CAAC,CAAC;EAC9B,IAAIC,GAAG,GAAGhF,EAAE,EAAE;IACV;IACA;IACA;IACA,IAAIb,CAAC,GAAG,CAAC;MAAEwE,EAAE,GAAG,CAAC;IACjB;IACA,IAAIuB,GAAG,GAAGF,GAAG,GAAGhF,EAAE;MAAEmF,GAAG,GAAG,CAAC,IAAID,GAAG;IAClCX,EAAE,CAACG,IAAI,CAAC,UAAUxD,CAAC,EAAEhC,CAAC,EAAE;MAAE,OAAO6F,EAAE,CAAC7F,CAAC,CAACe,CAAC,CAAC,GAAG8E,EAAE,CAAC7D,CAAC,CAACjB,CAAC,CAAC,IAAIiB,CAAC,CAACuB,CAAC,GAAGvD,CAAC,CAACuD,CAAC;IAAE,CAAC,CAAC;IACnE,OAAOtD,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACf,IAAIiG,IAAI,GAAGb,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI8E,EAAE,CAACK,IAAI,CAAC,GAAGpF,EAAE,EAAE;QACf2D,EAAE,IAAIwB,GAAG,IAAI,CAAC,IAAKH,GAAG,GAAGD,EAAE,CAACK,IAAI,CAAE,CAAC;QACnCL,EAAE,CAACK,IAAI,CAAC,GAAGpF,EAAE;MACjB,CAAC,MAEG;IACR;IACA2D,EAAE,MAAMuB,GAAG;IACX,OAAOvB,EAAE,GAAG,CAAC,EAAE;MACX,IAAI0B,IAAI,GAAGd,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI8E,EAAE,CAACM,IAAI,CAAC,GAAGrF,EAAE,EACb2D,EAAE,IAAI,CAAC,IAAK3D,EAAE,GAAG+E,EAAE,CAACM,IAAI,CAAC,EAAE,GAAG,CAAE,CAAC,KAEjC,EAAElG,CAAC;IACX;IACA,OAAOA,CAAC,IAAI,CAAC,IAAIwE,EAAE,EAAE,EAAExE,CAAC,EAAE;MACtB,IAAImG,IAAI,GAAGf,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI8E,EAAE,CAACO,IAAI,CAAC,IAAItF,EAAE,EAAE;QAChB,EAAE+E,EAAE,CAACO,IAAI,CAAC;QACV,EAAE3B,EAAE;MACR;IACJ;IACAqB,GAAG,GAAGhF,EAAE;EACZ;EACA,OAAO,CAAC,IAAI1B,EAAE,CAACyG,EAAE,CAAC,EAAEC,GAAG,CAAC;AAC5B,CAAC;AACD;AACA,IAAIC,EAAE,GAAG,SAAAA,CAAUvD,CAAC,EAAEvB,CAAC,EAAEiB,CAAC,EAAE;EACxB,OAAOM,CAAC,CAACzB,CAAC,IAAI,CAAC,CAAC,GACVsC,IAAI,CAACtB,GAAG,CAACgE,EAAE,CAACvD,CAAC,CAACvB,CAAC,EAAEA,CAAC,EAAEiB,CAAC,GAAG,CAAC,CAAC,EAAE6D,EAAE,CAACvD,CAAC,CAACtC,CAAC,EAAEe,CAAC,EAAEiB,CAAC,GAAG,CAAC,CAAC,CAAC,GAC7CjB,CAAC,CAACuB,CAAC,CAACzB,CAAC,CAAC,GAAGmB,CAAE;AACtB,CAAC;AACD;AACA,IAAImE,EAAE,GAAG,SAAAA,CAAUlI,CAAC,EAAE;EAClB,IAAI4C,CAAC,GAAG5C,CAAC,CAAC6C,MAAM;EAChB;EACA,OAAOD,CAAC,IAAI,CAAC5C,CAAC,CAAC,EAAE4C,CAAC,CAAC,CACf;EACJ,IAAIuF,EAAE,GAAG,IAAIhH,GAAG,CAAC,EAAEyB,CAAC,CAAC;EACrB;EACA,IAAIwF,GAAG,GAAG,CAAC;IAAEC,GAAG,GAAGrI,CAAC,CAAC,CAAC,CAAC;IAAEsI,GAAG,GAAG,CAAC;EAChC,IAAIjI,CAAC,GAAG,SAAAA,CAAU+C,CAAC,EAAE;IAAE+E,EAAE,CAACC,GAAG,EAAE,CAAC,GAAGhF,CAAC;EAAE,CAAC;EACvC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIc,CAAC,EAAE,EAAEd,CAAC,EAAE;IACzB,IAAI9B,CAAC,CAAC8B,CAAC,CAAC,IAAIuG,GAAG,IAAIvG,CAAC,IAAIc,CAAC,EACrB,EAAE0F,GAAG,CAAC,KACL;MACD,IAAI,CAACD,GAAG,IAAIC,GAAG,GAAG,CAAC,EAAE;QACjB,OAAOA,GAAG,GAAG,GAAG,EAAEA,GAAG,IAAI,GAAG,EACxBjI,CAAC,CAAC,KAAK,CAAC;QACZ,IAAIiI,GAAG,GAAG,CAAC,EAAE;UACTjI,CAAC,CAACiI,GAAG,GAAG,EAAE,GAAKA,GAAG,GAAG,EAAE,IAAK,CAAC,GAAI,KAAK,GAAKA,GAAG,GAAG,CAAC,IAAK,CAAC,GAAI,KAAK,CAAC;UAClEA,GAAG,GAAG,CAAC;QACX;MACJ,CAAC,MACI,IAAIA,GAAG,GAAG,CAAC,EAAE;QACdjI,CAAC,CAACgI,GAAG,CAAC,EAAE,EAAEC,GAAG;QACb,OAAOA,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EACpBjI,CAAC,CAAC,IAAI,CAAC;QACX,IAAIiI,GAAG,GAAG,CAAC,EACPjI,CAAC,CAAGiI,GAAG,GAAG,CAAC,IAAK,CAAC,GAAI,IAAI,CAAC,EAAEA,GAAG,GAAG,CAAC;MAC3C;MACA,OAAOA,GAAG,EAAE,EACRjI,CAAC,CAACgI,GAAG,CAAC;MACVC,GAAG,GAAG,CAAC;MACPD,GAAG,GAAGrI,CAAC,CAAC8B,CAAC,CAAC;IACd;EACJ;EACA,OAAO,CAACqG,EAAE,CAAC5D,QAAQ,CAAC,CAAC,EAAE6D,GAAG,CAAC,EAAExF,CAAC,CAAC;AACnC,CAAC;AACD;AACA,IAAI2F,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEL,EAAE,EAAE;EACzB,IAAIrF,CAAC,GAAG,CAAC;EACT,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,EAAE,CAACtF,MAAM,EAAE,EAAEf,CAAC,EAC9BgB,CAAC,IAAI0F,EAAE,CAAC1G,CAAC,CAAC,GAAGqG,EAAE,CAACrG,CAAC,CAAC;EACtB,OAAOgB,CAAC;AACZ,CAAC;AACD;AACA;AACA,IAAI2F,KAAK,GAAG,SAAAA,CAAUC,GAAG,EAAErD,GAAG,EAAEZ,GAAG,EAAE;EACjC;EACA,IAAI7B,CAAC,GAAG6B,GAAG,CAAC5B,MAAM;EAClB,IAAIoB,CAAC,GAAGE,IAAI,CAACkB,GAAG,GAAG,CAAC,CAAC;EACrBqD,GAAG,CAACzE,CAAC,CAAC,GAAGrB,CAAC,GAAG,GAAG;EAChB8F,GAAG,CAACzE,CAAC,GAAG,CAAC,CAAC,GAAGrB,CAAC,KAAK,CAAC;EACpB8F,GAAG,CAACzE,CAAC,GAAG,CAAC,CAAC,GAAGyE,GAAG,CAACzE,CAAC,CAAC,GAAG,GAAG;EACzByE,GAAG,CAACzE,CAAC,GAAG,CAAC,CAAC,GAAGyE,GAAG,CAACzE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EAC7B,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EACtB4G,GAAG,CAACzE,CAAC,GAAGnC,CAAC,GAAG,CAAC,CAAC,GAAG2C,GAAG,CAAC3C,CAAC,CAAC;EAC3B,OAAO,CAACmC,CAAC,GAAG,CAAC,GAAGrB,CAAC,IAAI,CAAC;AAC1B,CAAC;AACD;AACA,IAAI+F,IAAI,GAAG,SAAAA,CAAUlE,GAAG,EAAEiE,GAAG,EAAEvD,KAAK,EAAEyD,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEnH,EAAE,EAAEoH,EAAE,EAAEC,EAAE,EAAEhE,EAAE,EAAEhB,CAAC,EAAE;EACnE8C,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAE,EAAEmB,KAAK,CAAC;EACtB,EAAE0D,EAAE,CAAC,GAAG,CAAC;EACT,IAAI5G,EAAE,GAAG+E,KAAK,CAAC6B,EAAE,EAAE,EAAE,CAAC;IAAEI,GAAG,GAAGhH,EAAE,CAAC,CAAC,CAAC;IAAEiH,GAAG,GAAGjH,EAAE,CAAC,CAAC,CAAC;EAChD,IAAIG,EAAE,GAAG4E,KAAK,CAAC8B,EAAE,EAAE,EAAE,CAAC;IAAEK,GAAG,GAAG/G,EAAE,CAAC,CAAC,CAAC;IAAEgH,GAAG,GAAGhH,EAAE,CAAC,CAAC,CAAC;EAChD,IAAIiH,EAAE,GAAGnB,EAAE,CAACe,GAAG,CAAC;IAAEK,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIG,EAAE,GAAGtB,EAAE,CAACiB,GAAG,CAAC;IAAEM,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIG,MAAM,GAAG,IAAIxI,GAAG,CAAC,EAAE,CAAC;EACxB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,IAAI,CAACzG,MAAM,EAAE,EAAEf,CAAC,EAChC6H,MAAM,CAACL,IAAI,CAACxH,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;EAC1B,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2H,IAAI,CAAC5G,MAAM,EAAE,EAAEf,CAAC,EAChC6H,MAAM,CAACF,IAAI,CAAC3H,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;EAC1B,IAAI8H,EAAE,GAAG5C,KAAK,CAAC2C,MAAM,EAAE,CAAC,CAAC;IAAEE,GAAG,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;EACpD,IAAIG,IAAI,GAAG,EAAE;EACb,OAAOA,IAAI,GAAG,CAAC,IAAI,CAACF,GAAG,CAACpI,IAAI,CAACsI,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,EAAEA,IAAI,CAC3C;EACJ,IAAIC,IAAI,GAAIhF,EAAE,GAAG,CAAC,IAAK,CAAC;EACxB,IAAIiF,KAAK,GAAG1B,IAAI,CAACM,EAAE,EAAEvF,GAAG,CAAC,GAAGiF,IAAI,CAACO,EAAE,EAAEvF,GAAG,CAAC,GAAG5B,EAAE;EAC9C,IAAIuI,KAAK,GAAG3B,IAAI,CAACM,EAAE,EAAEI,GAAG,CAAC,GAAGV,IAAI,CAACO,EAAE,EAAEK,GAAG,CAAC,GAAGxH,EAAE,GAAG,EAAE,GAAG,CAAC,GAAGoI,IAAI,GAAGxB,IAAI,CAACoB,MAAM,EAAEE,GAAG,CAAC,IAAI,CAAC,GAAGF,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,CAAC;EACvI,IAAIK,IAAI,IAAIC,KAAK,IAAID,IAAI,IAAIE,KAAK,EAC9B,OAAOzB,KAAK,CAACC,GAAG,EAAE1E,CAAC,EAAES,GAAG,CAACF,QAAQ,CAACyE,EAAE,EAAEA,EAAE,GAAGhE,EAAE,CAAC,CAAC;EACnD,IAAIO,EAAE,EAAE4E,EAAE,EAAE3E,EAAE,EAAE4E,EAAE;EAClBtD,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAE,CAAC,IAAIkG,KAAK,GAAGD,KAAK,CAAC,CAAC,EAAEjG,CAAC,IAAI,CAAC;EAC1C,IAAIkG,KAAK,GAAGD,KAAK,EAAE;IACf1E,EAAE,GAAG9C,IAAI,CAACwG,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC,EAAEiB,EAAE,GAAGlB,GAAG,EAAEzD,EAAE,GAAG/C,IAAI,CAAC0G,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC,EAAEgB,EAAE,GAAGjB,GAAG;IAClE,IAAIkB,GAAG,GAAG5H,IAAI,CAACoH,GAAG,EAAEC,IAAI,EAAE,CAAC,CAAC;IAC5BhD,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAEuF,GAAG,GAAG,GAAG,CAAC;IACxBzC,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,GAAG,CAAC,EAAE0F,GAAG,GAAG,CAAC,CAAC;IAC1B5C,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,GAAG,EAAE,EAAE+F,IAAI,GAAG,CAAC,CAAC;IAC5B/F,CAAC,IAAI,EAAE;IACP,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiI,IAAI,EAAE,EAAEjI,CAAC,EACzBgF,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,GAAG,CAAC,GAAGlC,CAAC,EAAE+H,GAAG,CAACpI,IAAI,CAACK,CAAC,CAAC,CAAC,CAAC;IACvCkC,CAAC,IAAI,CAAC,GAAG+F,IAAI;IACb,IAAIO,IAAI,GAAG,CAAChB,IAAI,EAAEG,IAAI,CAAC;IACvB,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAE,EAAEA,EAAE,EAAE;MAC3B,IAAIC,IAAI,GAAGF,IAAI,CAACC,EAAE,CAAC;MACnB,KAAK,IAAIzI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0I,IAAI,CAAC3H,MAAM,EAAE,EAAEf,CAAC,EAAE;QAClC,IAAI2I,GAAG,GAAGD,IAAI,CAAC1I,CAAC,CAAC,GAAG,EAAE;QACtBgF,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAEqG,GAAG,CAACI,GAAG,CAAC,CAAC,EAAEzG,CAAC,IAAI6F,GAAG,CAACY,GAAG,CAAC;QACtC,IAAIA,GAAG,GAAG,EAAE,EACR3D,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAGwG,IAAI,CAAC1I,CAAC,CAAC,KAAK,CAAC,GAAI,GAAG,CAAC,EAAEkC,CAAC,IAAIwG,IAAI,CAAC1I,CAAC,CAAC,KAAK,EAAE;MACjE;IACJ;EACJ,CAAC,MACI;IACDyD,EAAE,GAAG/B,GAAG,EAAE2G,EAAE,GAAG7G,GAAG,EAAEkC,EAAE,GAAG9B,GAAG,EAAE0G,EAAE,GAAG7G,GAAG;EAC1C;EACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiH,EAAE,EAAE,EAAEjH,CAAC,EAAE;IACzB,IAAI8G,IAAI,CAAC9G,CAAC,CAAC,GAAG,GAAG,EAAE;MACf,IAAI2I,GAAG,GAAI7B,IAAI,CAAC9G,CAAC,CAAC,KAAK,EAAE,GAAI,EAAE;MAC/BiF,OAAO,CAAC2B,GAAG,EAAE1E,CAAC,EAAEuB,EAAE,CAACkF,GAAG,GAAG,GAAG,CAAC,CAAC,EAAEzG,CAAC,IAAImG,EAAE,CAACM,GAAG,GAAG,GAAG,CAAC;MAClD,IAAIA,GAAG,GAAG,CAAC,EACP3D,KAAK,CAAC4B,GAAG,EAAE1E,CAAC,EAAG4E,IAAI,CAAC9G,CAAC,CAAC,KAAK,EAAE,GAAI,EAAE,CAAC,EAAEkC,CAAC,IAAIzC,IAAI,CAACkJ,GAAG,CAAC;MACxD,IAAIC,GAAG,GAAG9B,IAAI,CAAC9G,CAAC,CAAC,GAAG,EAAE;MACtBiF,OAAO,CAAC2B,GAAG,EAAE1E,CAAC,EAAEwB,EAAE,CAACkF,GAAG,CAAC,CAAC,EAAE1G,CAAC,IAAIoG,EAAE,CAACM,GAAG,CAAC;MACtC,IAAIA,GAAG,GAAG,CAAC,EACP3D,OAAO,CAAC2B,GAAG,EAAE1E,CAAC,EAAG4E,IAAI,CAAC9G,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI,CAAC,EAAEkC,CAAC,IAAIxC,IAAI,CAACkJ,GAAG,CAAC;IAC/D,CAAC,MACI;MACD3D,OAAO,CAAC2B,GAAG,EAAE1E,CAAC,EAAEuB,EAAE,CAACqD,IAAI,CAAC9G,CAAC,CAAC,CAAC,CAAC,EAAEkC,CAAC,IAAImG,EAAE,CAACvB,IAAI,CAAC9G,CAAC,CAAC,CAAC;IAClD;EACJ;EACAiF,OAAO,CAAC2B,GAAG,EAAE1E,CAAC,EAAEuB,EAAE,CAAC,GAAG,CAAC,CAAC;EACxB,OAAOvB,CAAC,GAAGmG,EAAE,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,IAAIQ,GAAG,GAAG,aAAc,IAAItJ,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5G;AACA,IAAI+F,EAAE,GAAG,aAAc,IAAInG,EAAE,CAAC,CAAC,CAAC;AAChC;AACA,IAAI2J,IAAI,GAAG,SAAAA,CAAUnG,GAAG,EAAEoG,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAE;EACjD,IAAIrI,CAAC,GAAG6B,GAAG,CAAC5B,MAAM;EAClB,IAAIoB,CAAC,GAAG,IAAIhD,EAAE,CAAC8J,GAAG,GAAGnI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGsC,IAAI,CAACgG,IAAI,CAACtI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAGoI,IAAI,CAAC;EAC9D;EACA,IAAI3K,CAAC,GAAG4D,CAAC,CAACM,QAAQ,CAACwG,GAAG,EAAE9G,CAAC,CAACpB,MAAM,GAAGmI,IAAI,CAAC;EACxC,IAAI3F,GAAG,GAAG,CAAC;EACX,IAAI,CAACwF,GAAG,IAAIjI,CAAC,GAAG,CAAC,EAAE;IACf,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIc,CAAC,EAAEd,CAAC,IAAI,KAAK,EAAE;MAChC;MACA,IAAIlB,CAAC,GAAGkB,CAAC,GAAG,KAAK;MACjB,IAAIlB,CAAC,GAAGgC,CAAC,EAAE;QACP;QACAyC,GAAG,GAAGoD,KAAK,CAACpI,CAAC,EAAEgF,GAAG,EAAEZ,GAAG,CAACF,QAAQ,CAACzC,CAAC,EAAElB,CAAC,CAAC,CAAC;MAC3C,CAAC,MACI;QACD;QACAP,CAAC,CAACyB,CAAC,CAAC,GAAGmJ,GAAG;QACV5F,GAAG,GAAGoD,KAAK,CAACpI,CAAC,EAAEgF,GAAG,EAAEZ,GAAG,CAACF,QAAQ,CAACzC,CAAC,EAAEc,CAAC,CAAC,CAAC;MAC3C;IACJ;EACJ,CAAC,MACI;IACD,IAAIuI,GAAG,GAAGR,GAAG,CAACE,GAAG,GAAG,CAAC,CAAC;IACtB,IAAIxG,CAAC,GAAG8G,GAAG,KAAK,EAAE;MAAEnL,CAAC,GAAGmL,GAAG,GAAG,IAAI;IAClC,IAAIC,KAAK,GAAG,CAAC,CAAC,IAAIN,IAAI,IAAI,CAAC;IAC3B;IACA,IAAIO,IAAI,GAAG,IAAIlK,GAAG,CAAC,KAAK,CAAC;MAAEmK,IAAI,GAAG,IAAInK,GAAG,CAACiK,KAAK,GAAG,CAAC,CAAC;IACpD,IAAIG,KAAK,GAAGrG,IAAI,CAACgG,IAAI,CAACJ,IAAI,GAAG,CAAC,CAAC;MAAEU,KAAK,GAAG,CAAC,GAAGD,KAAK;IAClD,IAAIE,GAAG,GAAG,SAAAA,CAAU3J,CAAC,EAAE;MAAE,OAAO,CAAC2C,GAAG,CAAC3C,CAAC,CAAC,GAAI2C,GAAG,CAAC3C,CAAC,GAAG,CAAC,CAAC,IAAIyJ,KAAM,GAAI9G,GAAG,CAAC3C,CAAC,GAAG,CAAC,CAAC,IAAI0J,KAAM,IAAIJ,KAAK;IAAE,CAAC;IACnG;IACA;IACA,IAAIxC,IAAI,GAAG,IAAIvH,GAAG,CAAC,KAAK,CAAC;IACzB;IACA,IAAIwH,EAAE,GAAG,IAAI1H,GAAG,CAAC,GAAG,CAAC;MAAE2H,EAAE,GAAG,IAAI3H,GAAG,CAAC,EAAE,CAAC;IACvC;IACA,IAAIuK,IAAI,GAAG,CAAC;MAAE/J,EAAE,GAAG,CAAC;MAAEG,CAAC,GAAG,CAAC;MAAEiH,EAAE,GAAG,CAAC;MAAE4C,EAAE,GAAG,CAAC;MAAE3C,EAAE,GAAG,CAAC;IACnD,OAAOlH,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACf;MACA;MACA,IAAI8J,EAAE,GAAGH,GAAG,CAAC3J,CAAC,CAAC;MACf;MACA,IAAI+J,IAAI,GAAG/J,CAAC,GAAG,KAAK;QAAEgK,KAAK,GAAGR,IAAI,CAACM,EAAE,CAAC;MACtCP,IAAI,CAACQ,IAAI,CAAC,GAAGC,KAAK;MAClBR,IAAI,CAACM,EAAE,CAAC,GAAGC,IAAI;MACf;MACA;MACA,IAAIF,EAAE,IAAI7J,CAAC,EAAE;QACT;QACA,IAAIiK,GAAG,GAAGnJ,CAAC,GAAGd,CAAC;QACf,IAAI,CAAC4J,IAAI,GAAG,IAAI,IAAI3C,EAAE,GAAG,KAAK,KAAKgD,GAAG,GAAG,GAAG,EAAE;UAC1C1G,GAAG,GAAGsD,IAAI,CAAClE,GAAG,EAAEpE,CAAC,EAAE,CAAC,EAAEuI,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEnH,EAAE,EAAEoH,EAAE,EAAEC,EAAE,EAAElH,CAAC,GAAGkH,EAAE,EAAE3D,GAAG,CAAC;UAC5D0D,EAAE,GAAG2C,IAAI,GAAG/J,EAAE,GAAG,CAAC,EAAEqH,EAAE,GAAGlH,CAAC;UAC1B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EACxB6G,EAAE,CAAC7G,CAAC,CAAC,GAAG,CAAC;UACb,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EACvB8G,EAAE,CAAC9G,CAAC,CAAC,GAAG,CAAC;QACjB;QACA;QACA,IAAIc,CAAC,GAAG,CAAC;UAAEiB,CAAC,GAAG,CAAC;UAAEiI,IAAI,GAAGhM,CAAC;UAAEiM,GAAG,GAAIJ,IAAI,GAAGC,KAAK,GAAI,KAAK;QACxD,IAAIC,GAAG,GAAG,CAAC,IAAIH,EAAE,IAAIH,GAAG,CAAC3J,CAAC,GAAGmK,GAAG,CAAC,EAAE;UAC/B,IAAIC,IAAI,GAAGhH,IAAI,CAACiH,GAAG,CAAC9H,CAAC,EAAE0H,GAAG,CAAC,GAAG,CAAC;UAC/B,IAAIK,IAAI,GAAGlH,IAAI,CAACiH,GAAG,CAAC,KAAK,EAAErK,CAAC,CAAC;UAC7B;UACA;UACA,IAAIuK,EAAE,GAAGnH,IAAI,CAACiH,GAAG,CAAC,GAAG,EAAEJ,GAAG,CAAC;UAC3B,OAAOE,GAAG,IAAIG,IAAI,IAAI,EAAEJ,IAAI,IAAIH,IAAI,IAAIC,KAAK,EAAE;YAC3C,IAAIrH,GAAG,CAAC3C,CAAC,GAAGgB,CAAC,CAAC,IAAI2B,GAAG,CAAC3C,CAAC,GAAGgB,CAAC,GAAGmJ,GAAG,CAAC,EAAE;cAChC,IAAIK,EAAE,GAAG,CAAC;cACV,OAAOA,EAAE,GAAGD,EAAE,IAAI5H,GAAG,CAAC3C,CAAC,GAAGwK,EAAE,CAAC,IAAI7H,GAAG,CAAC3C,CAAC,GAAGwK,EAAE,GAAGL,GAAG,CAAC,EAAE,EAAEK,EAAE,CACpD;cACJ,IAAIA,EAAE,GAAGxJ,CAAC,EAAE;gBACRA,CAAC,GAAGwJ,EAAE,EAAEvI,CAAC,GAAGkI,GAAG;gBACf;gBACA,IAAIK,EAAE,GAAGJ,IAAI,EACT;gBACJ;gBACA;gBACA;gBACA,IAAIK,GAAG,GAAGrH,IAAI,CAACiH,GAAG,CAACF,GAAG,EAAEK,EAAE,GAAG,CAAC,CAAC;gBAC/B,IAAIE,EAAE,GAAG,CAAC;gBACV,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuK,GAAG,EAAE,EAAEvK,CAAC,EAAE;kBAC1B,IAAIyK,EAAE,GAAI3K,CAAC,GAAGmK,GAAG,GAAGjK,CAAC,GAAG,KAAK,GAAI,KAAK;kBACtC,IAAI0K,GAAG,GAAGrB,IAAI,CAACoB,EAAE,CAAC;kBAClB,IAAI/J,EAAE,GAAI+J,EAAE,GAAGC,GAAG,GAAG,KAAK,GAAI,KAAK;kBACnC,IAAIhK,EAAE,GAAG8J,EAAE,EACPA,EAAE,GAAG9J,EAAE,EAAEoJ,KAAK,GAAGW,EAAE;gBAC3B;cACJ;YACJ;YACA;YACAZ,IAAI,GAAGC,KAAK,EAAEA,KAAK,GAAGT,IAAI,CAACQ,IAAI,CAAC;YAChCI,GAAG,IAAKJ,IAAI,GAAGC,KAAK,GAAG,KAAK,GAAI,KAAK;UACzC;QACJ;QACA;QACA,IAAI/H,CAAC,EAAE;UACH;UACA;UACA6E,IAAI,CAACG,EAAE,EAAE,CAAC,GAAG,SAAS,GAAI5G,KAAK,CAACW,CAAC,CAAC,IAAI,EAAG,GAAGR,KAAK,CAACyB,CAAC,CAAC;UACpD,IAAI4I,GAAG,GAAGxK,KAAK,CAACW,CAAC,CAAC,GAAG,EAAE;YAAE8J,GAAG,GAAGtK,KAAK,CAACyB,CAAC,CAAC,GAAG,EAAE;UAC5CpC,EAAE,IAAIJ,IAAI,CAACoL,GAAG,CAAC,GAAGnL,IAAI,CAACoL,GAAG,CAAC;UAC3B,EAAE/D,EAAE,CAAC,GAAG,GAAG8D,GAAG,CAAC;UACf,EAAE7D,EAAE,CAAC8D,GAAG,CAAC;UACTjB,EAAE,GAAG7J,CAAC,GAAGgB,CAAC;UACV,EAAE4I,IAAI;QACV,CAAC,MACI;UACD9C,IAAI,CAACG,EAAE,EAAE,CAAC,GAAGtE,GAAG,CAAC3C,CAAC,CAAC;UACnB,EAAE+G,EAAE,CAACpE,GAAG,CAAC3C,CAAC,CAAC,CAAC;QAChB;MACJ;IACJ;IACAuD,GAAG,GAAGsD,IAAI,CAAClE,GAAG,EAAEpE,CAAC,EAAE4K,GAAG,EAAErC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEnH,EAAE,EAAEoH,EAAE,EAAEC,EAAE,EAAElH,CAAC,GAAGkH,EAAE,EAAE3D,GAAG,CAAC;IAC9D;IACA,IAAI,CAAC4F,GAAG,IAAI5F,GAAG,GAAG,CAAC,EACfA,GAAG,GAAGoD,KAAK,CAACpI,CAAC,EAAEgF,GAAG,GAAG,CAAC,EAAE+B,EAAE,CAAC;EACnC;EACA,OAAOhD,GAAG,CAACH,CAAC,EAAE,CAAC,EAAE8G,GAAG,GAAG5G,IAAI,CAACkB,GAAG,CAAC,GAAG2F,IAAI,CAAC;AAC5C,CAAC;AACD;AACA,IAAI6B,IAAI,GAAG,aAAe,YAAY;EAClC,IAAIjH,CAAC,GAAG,IAAIkH,UAAU,CAAC,GAAG,CAAC;EAC3B,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;IAC1B,IAAI9B,CAAC,GAAG8B,CAAC;MAAEiL,CAAC,GAAG,CAAC;IAChB,OAAO,EAAEA,CAAC,EACN/M,CAAC,GAAG,CAAEA,CAAC,GAAG,CAAC,IAAK,CAAC,SAAS,IAAKA,CAAC,KAAK,CAAE;IAC3C4F,CAAC,CAAC9D,CAAC,CAAC,GAAG9B,CAAC;EACZ;EACA,OAAO4F,CAAC;AACZ,CAAC,CAAE,CAAC;AACJ;AACA,IAAIoH,GAAG,GAAG,SAAAA,CAAA,EAAY;EAClB,IAAIhN,CAAC,GAAG,CAAC,CAAC;EACV,OAAO;IACHgE,CAAC,EAAE,SAAAA,CAAUD,CAAC,EAAE;MACZ;MACA,IAAIkJ,EAAE,GAAGjN,CAAC;MACV,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,CAAC,CAAClB,MAAM,EAAE,EAAEf,CAAC,EAC7BmL,EAAE,GAAGJ,IAAI,CAAEI,EAAE,GAAG,GAAG,GAAIlJ,CAAC,CAACjC,CAAC,CAAC,CAAC,GAAImL,EAAE,KAAK,CAAE;MAC7CjN,CAAC,GAAGiN,EAAE;IACV,CAAC;IACDlJ,CAAC,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC/D,CAAC;IAAE;EAChC,CAAC;AACL,CAAC;AACD;AACA,IAAIkN,KAAK,GAAG,SAAAA,CAAA,EAAY;EACpB,IAAIrJ,CAAC,GAAG,CAAC;IAAEhC,CAAC,GAAG,CAAC;EAChB,OAAO;IACHmC,CAAC,EAAE,SAAAA,CAAUD,CAAC,EAAE;MACZ;MACA,IAAIM,CAAC,GAAGR,CAAC;QAAER,CAAC,GAAGxB,CAAC;MAChB,IAAIiB,CAAC,GAAGiB,CAAC,CAAClB,MAAM;MAChB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIgB,CAAC,GAAG;QACrB,IAAIlC,CAAC,GAAGsE,IAAI,CAACiH,GAAG,CAACrK,CAAC,GAAG,IAAI,EAAEgB,CAAC,CAAC;QAC7B,OAAOhB,CAAC,GAAGlB,CAAC,EAAE,EAAEkB,CAAC,EACbuB,CAAC,IAAIgB,CAAC,IAAIN,CAAC,CAACjC,CAAC,CAAC;QAClBuC,CAAC,GAAG,CAACA,CAAC,GAAG,KAAK,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAACA,CAAC,GAAG,KAAK,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,CAAC;MACtE;MACAQ,CAAC,GAAGQ,CAAC,EAAExC,CAAC,GAAGwB,CAAC;IAChB,CAAC;IACDU,CAAC,EAAE,SAAAA,CAAA,EAAY;MACXF,CAAC,IAAI,KAAK,EAAEhC,CAAC,IAAI,KAAK;MACtB,OAAO,CAACgC,CAAC,GAAG,GAAG,KAAK,EAAE,GAAIA,CAAC,KAAK,CAAC,IAAK,EAAE,GAAG,CAAChC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAIA,CAAC,KAAK,CAAE;IACzE;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA,IAAIsL,IAAI,GAAG,SAAAA,CAAU1I,GAAG,EAAE0G,GAAG,EAAEJ,GAAG,EAAEC,IAAI,EAAErG,EAAE,EAAE;EAC1C,OAAOiG,IAAI,CAACnG,GAAG,EAAE0G,GAAG,CAACiC,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGjC,GAAG,CAACiC,KAAK,EAAEjC,GAAG,CAACkC,GAAG,IAAI,IAAI,GAAGnI,IAAI,CAACgG,IAAI,CAAChG,IAAI,CAACtB,GAAG,CAAC,CAAC,EAAEsB,IAAI,CAACiH,GAAG,CAAC,EAAE,EAAEjH,IAAI,CAACoI,GAAG,CAAC7I,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAI,EAAE,GAAGsI,GAAG,CAACkC,GAAI,EAAEtC,GAAG,EAAEC,IAAI,EAAE,CAACrG,EAAE,CAAC;AAC5K,CAAC;AACD;AACA,IAAI4I,GAAG,GAAG,SAAAA,CAAU1J,CAAC,EAAEhC,CAAC,EAAE;EACtB,IAAIoC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAI8I,CAAC,IAAIlJ,CAAC,EACXI,CAAC,CAAC8I,CAAC,CAAC,GAAGlJ,CAAC,CAACkJ,CAAC,CAAC;EACf,KAAK,IAAIA,CAAC,IAAIlL,CAAC,EACXoC,CAAC,CAAC8I,CAAC,CAAC,GAAGlL,CAAC,CAACkL,CAAC,CAAC;EACf,OAAO9I,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuJ,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAEC,EAAE,EAAE;EAChC,IAAIrH,EAAE,GAAGmH,EAAE,CAAC,CAAC;EACb,IAAI9I,EAAE,GAAG8I,EAAE,CAACG,QAAQ,CAAC,CAAC;EACtB,IAAIC,EAAE,GAAGlJ,EAAE,CAACwC,KAAK,CAACxC,EAAE,CAACmJ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEnJ,EAAE,CAACoJ,WAAW,CAAC,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACxF,KAAK,IAAInM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,EAAE,CAACzD,MAAM,EAAE,EAAEf,CAAC,EAAE;IAChC,IAAIsB,CAAC,GAAGkD,EAAE,CAACxE,CAAC,CAAC;MAAEiL,CAAC,GAAGc,EAAE,CAAC/L,CAAC,CAAC;IACxB,IAAI,OAAOsB,CAAC,IAAI,UAAU,EAAE;MACxBsK,KAAK,IAAI,GAAG,GAAGX,CAAC,GAAG,GAAG;MACtB,IAAImB,IAAI,GAAG9K,CAAC,CAACwK,QAAQ,CAAC,CAAC;MACvB,IAAIxK,CAAC,CAAC+K,SAAS,EAAE;QACb;QACA,IAAID,IAAI,CAACJ,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE;UACrC,IAAIM,KAAK,GAAGF,IAAI,CAACJ,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC;UACpCJ,KAAK,IAAIQ,IAAI,CAAC/G,KAAK,CAACiH,KAAK,EAAEF,IAAI,CAACJ,OAAO,CAAC,GAAG,EAAEM,KAAK,CAAC,CAAC;QACxD,CAAC,MACI;UACDV,KAAK,IAAIQ,IAAI;UACb,KAAK,IAAItI,CAAC,IAAIxC,CAAC,CAAC+K,SAAS,EACrBT,KAAK,IAAI,GAAG,GAAGX,CAAC,GAAG,aAAa,GAAGnH,CAAC,GAAG,GAAG,GAAGxC,CAAC,CAAC+K,SAAS,CAACvI,CAAC,CAAC,CAACgI,QAAQ,CAAC,CAAC;QAC9E;MACJ,CAAC,MAEGF,KAAK,IAAIQ,IAAI;IACrB,CAAC,MAEGP,EAAE,CAACZ,CAAC,CAAC,GAAG3J,CAAC;EACjB;EACA,OAAO,CAACsK,KAAK,EAAEC,EAAE,CAAC;AACtB,CAAC;AACD,IAAIU,EAAE,GAAG,EAAE;AACX;AACA,IAAIC,IAAI,GAAG,SAAAA,CAAUlL,CAAC,EAAE;EACpB,IAAI2C,EAAE,GAAG,EAAE;EACX,KAAK,IAAIgH,CAAC,IAAI3J,CAAC,EAAE;IACb,IAAIA,CAAC,CAAC2J,CAAC,CAAC,YAAY9L,EAAE,IAAImC,CAAC,CAAC2J,CAAC,CAAC,YAAY5L,GAAG,IAAIiC,CAAC,CAAC2J,CAAC,CAAC,YAAY1L,GAAG,EAChE0E,EAAE,CAACkB,IAAI,CAAC,CAAC7D,CAAC,CAAC2J,CAAC,CAAC,GAAG,IAAI3J,CAAC,CAAC2J,CAAC,CAAC,CAACwB,WAAW,CAACnL,CAAC,CAAC2J,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAAC;EAC3D;EACA,OAAOzI,EAAE;AACb,CAAC;AACD;AACA,IAAI0I,IAAI,GAAG,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE1O,EAAE,EAAEG,EAAE,EAAE;EACpC,IAAI6B,EAAE;EACN,IAAI,CAACoM,EAAE,CAACpO,EAAE,CAAC,EAAE;IACT,IAAIyN,KAAK,GAAG,EAAE;MAAEkB,IAAI,GAAG,CAAC,CAAC;MAAEvL,CAAC,GAAGqL,GAAG,CAAC7L,MAAM,GAAG,CAAC;IAC7C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,CAAC,EAAE,EAAEvB,CAAC,EACtBG,EAAE,GAAGuL,IAAI,CAACkB,GAAG,CAAC5M,CAAC,CAAC,EAAE4L,KAAK,EAAEkB,IAAI,CAAC,EAAElB,KAAK,GAAGzL,EAAE,CAAC,CAAC,CAAC,EAAE2M,IAAI,GAAG3M,EAAE,CAAC,CAAC,CAAC;IAC/DoM,EAAE,CAACpO,EAAE,CAAC,GAAGuN,IAAI,CAACkB,GAAG,CAACrL,CAAC,CAAC,EAAEqK,KAAK,EAAEkB,IAAI,CAAC;EACtC;EACA,IAAIjB,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC,EAAEc,EAAE,CAACpO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,OAAOF,EAAE,CAACsO,EAAE,CAACpO,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,yEAAyE,GAAG0O,IAAI,CAACf,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE3N,EAAE,EAAE0N,EAAE,EAAEW,IAAI,CAACX,EAAE,CAAC,EAAEvN,EAAE,CAAC;AAClJ,CAAC;AACD;AACA,IAAIyO,MAAM,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAAC5N,EAAE,EAAEE,GAAG,EAAEE,GAAG,EAAEE,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAES,EAAE,EAAEG,EAAE,EAAEoB,IAAI,EAAEE,IAAI,EAAEpB,GAAG,EAAEE,IAAI,EAAEmB,GAAG,EAAEE,IAAI,EAAEI,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEI,KAAK,EAAEsK,WAAW,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAAE,CAAC;AAChK,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAAChO,EAAE,EAAEE,GAAG,EAAEE,GAAG,EAAEE,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEU,KAAK,EAAEG,KAAK,EAAEkB,GAAG,EAAEF,GAAG,EAAEI,GAAG,EAAEH,GAAG,EAAEhB,GAAG,EAAEoI,GAAG,EAAEvD,EAAE,EAAE3E,IAAI,EAAEqE,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEY,EAAE,EAAEM,EAAE,EAAEK,IAAI,EAAEE,KAAK,EAAEE,IAAI,EAAExE,IAAI,EAAEC,GAAG,EAAEwG,IAAI,EAAEuC,IAAI,EAAE+B,WAAW,EAAEH,GAAG,CAAC;AAAE,CAAC;AACrN;AACA,IAAII,GAAG,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEtC,GAAG,EAAEH,IAAI,CAAC;AAAE,CAAC;AAChE;AACA,IAAI0C,IAAI,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEC,GAAG,CAAC;AAAE,CAAC;AAC7C;AACA,IAAIC,GAAG,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEL,MAAM,EAAEpC,KAAK,CAAC;AAAE,CAAC;AACtD;AACA,IAAI0C,IAAI,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,CAAC;AAAE,CAAC;AACxC;AACA,IAAId,GAAG,GAAG,SAAAA,CAAU7O,GAAG,EAAE;EAAE,OAAOc,WAAW,CAACd,GAAG,EAAE,CAACA,GAAG,CAACsO,MAAM,CAAC,CAAC;AAAE,CAAC;AACnE;AACA,IAAIQ,GAAG,GAAG,SAAAA,CAAU/K,CAAC,EAAE;EAAE,OAAOA,CAAC,IAAIA,CAAC,CAAC6L,IAAI,IAAI,IAAI7O,EAAE,CAACgD,CAAC,CAAC6L,IAAI,CAAC;AAAE,CAAC;AAChE;AACA,IAAIC,KAAK,GAAG,SAAAA,CAAUtL,GAAG,EAAEuL,IAAI,EAAEtB,GAAG,EAAEC,IAAI,EAAE1O,EAAE,EAAEG,EAAE,EAAE;EAChD,IAAIC,CAAC,GAAGoO,IAAI,CAACC,GAAG,EAAEC,IAAI,EAAE1O,EAAE,EAAE,UAAUgQ,GAAG,EAAExL,GAAG,EAAE;IAC5CpE,CAAC,CAAC6P,SAAS,CAAC,CAAC;IACb9P,EAAE,CAAC6P,GAAG,EAAExL,GAAG,CAAC;EAChB,CAAC,CAAC;EACFpE,CAAC,CAACW,WAAW,CAAC,CAACyD,GAAG,EAAEuL,IAAI,CAAC,EAAEA,IAAI,CAACG,OAAO,GAAG,CAAC1L,GAAG,CAAC+J,MAAM,CAAC,GAAG,EAAE,CAAC;EAC5D,OAAO,YAAY;IAAEnO,CAAC,CAAC6P,SAAS,CAAC,CAAC;EAAE,CAAC;AACzC,CAAC;AACD;AACA,IAAIE,KAAK,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACxBA,IAAI,CAACC,MAAM,GAAG,UAAU7L,GAAG,EAAEU,KAAK,EAAE;IAAE,OAAOnE,WAAW,CAAC,CAACyD,GAAG,EAAEU,KAAK,CAAC,EAAE,CAACV,GAAG,CAAC+J,MAAM,CAAC,CAAC;EAAE,CAAC;EACvF,OAAO,UAAU+B,EAAE,EAAE;IAAE,OAAOF,IAAI,CAACpJ,IAAI,CAACsJ,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC;AACtE,CAAC;AACD;AACA,IAAIyP,QAAQ,GAAG,SAAAA,CAAU9B,GAAG,EAAE2B,IAAI,EAAEL,IAAI,EAAErB,IAAI,EAAE1O,EAAE,EAAE;EAChD,IAAI2F,CAAC;EACL,IAAIvF,CAAC,GAAGoO,IAAI,CAACC,GAAG,EAAEC,IAAI,EAAE1O,EAAE,EAAE,UAAUgQ,GAAG,EAAExL,GAAG,EAAE;IAC5C,IAAIwL,GAAG,EACH5P,CAAC,CAAC6P,SAAS,CAAC,CAAC,EAAEG,IAAI,CAACC,MAAM,CAACG,IAAI,CAACJ,IAAI,EAAEJ,GAAG,CAAC,CAAC,KAC1C;MACD,IAAIxL,GAAG,CAAC,CAAC,CAAC,EACNpE,CAAC,CAAC6P,SAAS,CAAC,CAAC;MACjBG,IAAI,CAACC,MAAM,CAACG,IAAI,CAACJ,IAAI,EAAEJ,GAAG,EAAExL,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC;EACFpE,CAAC,CAACW,WAAW,CAACgP,IAAI,CAAC;EACnBK,IAAI,CAACpJ,IAAI,GAAG,UAAUlD,CAAC,EAAEqB,CAAC,EAAE;IACxB,IAAIQ,CAAC,EACD,MAAM,iBAAiB;IAC3B,IAAI,CAACyK,IAAI,CAACC,MAAM,EACZ,MAAM,mBAAmB;IAC7BjQ,CAAC,CAACW,WAAW,CAAC,CAAC+C,CAAC,EAAE6B,CAAC,GAAGR,CAAC,CAAC,EAAE,CAACrB,CAAC,CAACyK,MAAM,CAAC,CAAC;EACzC,CAAC;EACD6B,IAAI,CAACH,SAAS,GAAG,YAAY;IAAE7P,CAAC,CAAC6P,SAAS,CAAC,CAAC;EAAE,CAAC;AACnD,CAAC;AACD;AACA,IAAIQ,EAAE,GAAG,SAAAA,CAAU3M,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAOkC,CAAC,CAAClC,CAAC,CAAC,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE;AAAE,CAAC;AAC3D;AACA,IAAI8O,EAAE,GAAG,SAAAA,CAAU5M,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAO,CAACkC,CAAC,CAAClC,CAAC,CAAC,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,MAAM,CAAC;AAAE,CAAC;AACzG,IAAI+O,EAAE,GAAG,SAAAA,CAAU7M,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAO8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,CAAC,GAAI8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAW;AAAE,CAAC;AAC3E;AACA,IAAIyN,MAAM,GAAG,SAAAA,CAAUvL,CAAC,EAAElC,CAAC,EAAEuB,CAAC,EAAE;EAC5B,OAAOA,CAAC,EAAE,EAAEvB,CAAC,EACTkC,CAAC,CAAClC,CAAC,CAAC,GAAGuB,CAAC,EAAEA,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,IAAIgM,GAAG,GAAG,SAAAA,CAAUpP,CAAC,EAAEiE,CAAC,EAAE;EACtB,IAAIwJ,EAAE,GAAGxJ,CAAC,CAAC4M,QAAQ;EACnB7Q,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAGiE,CAAC,CAACmJ,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGnJ,CAAC,CAACmJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEpN,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1F,IAAIiE,CAAC,CAAC6M,KAAK,IAAI,CAAC,EACZxB,MAAM,CAACtP,CAAC,EAAE,CAAC,EAAEkF,IAAI,CAAC6L,KAAK,CAAC,IAAIC,IAAI,CAAC/M,CAAC,CAAC6M,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EACpE,IAAIxD,EAAE,EAAE;IACJzN,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACR,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2L,EAAE,CAAC5K,MAAM,EAAE,EAAEf,CAAC,EAC/B9B,CAAC,CAAC8B,CAAC,GAAG,EAAE,CAAC,GAAG2L,EAAE,CAACyD,UAAU,CAACpP,CAAC,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA,IAAI0N,GAAG,GAAG,SAAAA,CAAUzL,CAAC,EAAE;EACnB,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EACtC,MAAM,mBAAmB;EAC7B,IAAIoN,GAAG,GAAGpN,CAAC,CAAC,CAAC,CAAC;EACd,IAAIY,EAAE,GAAG,EAAE;EACX,IAAIwM,GAAG,GAAG,CAAC,EACPxM,EAAE,IAAIZ,CAAC,CAAC,EAAE,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;EAClC,KAAK,IAAIqN,EAAE,GAAG,CAACD,GAAG,IAAI,CAAC,GAAG,CAAC,KAAKA,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAACrN,CAAC,CAACY,EAAE,EAAE,CAAC,CACjE;EACJ,OAAOA,EAAE,IAAIwM,GAAG,GAAG,CAAC,CAAC;AACzB,CAAC;AACD;AACA,IAAI1B,GAAG,GAAG,SAAAA,CAAU1L,CAAC,EAAE;EACnB,IAAIjB,CAAC,GAAGiB,CAAC,CAAClB,MAAM;EAChB,OAAO,CAAEkB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAKiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,MAAM,CAAC;AACjF,CAAC;AACD;AACA,IAAIuM,IAAI,GAAG,SAAAA,CAAUpL,CAAC,EAAE;EAAE,OAAO,EAAE,IAAKA,CAAC,CAAC4M,QAAQ,IAAK5M,CAAC,CAAC4M,QAAQ,CAAChO,MAAM,GAAG,CAAE,IAAK,CAAC,CAAC;AAAE,CAAC;AACvF;AACA,IAAI8M,GAAG,GAAG,SAAAA,CAAU3P,CAAC,EAAEiE,CAAC,EAAE;EACtB,IAAIoN,EAAE,GAAGpN,CAAC,CAACmJ,KAAK;IAAElL,EAAE,GAAGmP,EAAE,IAAI,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGA,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACjErR,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAIkC,EAAE,IAAI,CAAC,IAAKA,EAAE,GAAI,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAI,CAAC,CAAC;AAC3D,CAAC;AACD;AACA,IAAI2N,GAAG,GAAG,SAAAA,CAAU9L,CAAC,EAAE;EACnB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,EACjE,MAAM,mBAAmB;EAC7B,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EACT,MAAM,sDAAsD;AACpE,CAAC;AACD,SAASuN,YAAYA,CAACtB,IAAI,EAAE5P,EAAE,EAAE;EAC5B,IAAI,CAACA,EAAE,IAAI,OAAO4P,IAAI,IAAI,UAAU,EAChC5P,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,CAACM,MAAM,GAAGlQ,EAAE;EAChB,OAAO4P,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,IAAIuB,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACvB,IAAI,EAAE5P,EAAE,EAAE;IACvB,IAAI,CAACA,EAAE,IAAI,OAAO4P,IAAI,IAAI,UAAU,EAChC5P,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;IACxB,IAAI,CAACM,MAAM,GAAGlQ,EAAE;IAChB,IAAI,CAAC6D,CAAC,GAAG+L,IAAI,IAAI,CAAC,CAAC;EACvB;EACAuB,OAAO,CAACpD,SAAS,CAACnK,CAAC,GAAG,UAAUhE,CAAC,EAAEoF,CAAC,EAAE;IAClC,IAAI,CAACkL,MAAM,CAACnD,IAAI,CAACnN,CAAC,EAAE,IAAI,CAACiE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACmB,CAAC,CAAC,EAAEA,CAAC,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;EACImM,OAAO,CAACpD,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC7C,IAAI,IAAI,CAACpB,CAAC,EACN,MAAM,iBAAiB;IAC3B,IAAI,CAAC,IAAI,CAACuM,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAI,CAACvM,CAAC,GAAGoB,KAAK;IACd,IAAI,CAACnB,CAAC,CAACwN,KAAK,EAAErM,KAAK,IAAI,KAAK,CAAC;EACjC,CAAC;EACD,OAAOoM,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO;AAChB;AACA;AACA;AACA,IAAIE,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACzB,IAAI,EAAE5P,EAAE,EAAE;IAC5BoQ,QAAQ,CAAC,CACLvB,KAAK,EACL,YAAY;MAAE,OAAO,CAACmB,KAAK,EAAEmB,OAAO,CAAC;IAAE,CAAC,CAC3C,EAAE,IAAI,EAAED,YAAY,CAACb,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE5P,EAAE,CAAC,EAAE,UAAUmQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAIkB,OAAO,CAAChB,EAAE,CAACxP,IAAI,CAAC;MAC/BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAOoB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,OAAO,SAASC,OAAOA,CAAC3Q,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACpC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,CACR,EAAE,UAAUsB,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACG,WAAW,CAACqB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8O,WAAWA,CAACnO,IAAI,EAAEiP,IAAI,EAAE;EACpC,OAAO7C,IAAI,CAACpM,IAAI,EAAEiP,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,IAAI2B,OAAO,GAAG,aAAe,YAAY;EACrC;AACJ;AACA;AACA;EACI,SAASA,OAAOA,CAACvR,EAAE,EAAE;IACjB,IAAI,CAACwC,CAAC,GAAG,CAAC,CAAC;IACX,IAAI,CAACoB,CAAC,GAAG,IAAI/C,EAAE,CAAC,CAAC,CAAC;IAClB,IAAI,CAACqP,MAAM,GAAGlQ,EAAE;EACpB;EACAuR,OAAO,CAACxD,SAAS,CAACvN,CAAC,GAAG,UAAUZ,CAAC,EAAE;IAC/B,IAAI,IAAI,CAAC+D,CAAC,EACN,MAAM,iBAAiB;IAC3B,IAAI,CAAC,IAAI,CAACuM,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAIxN,CAAC,GAAG,IAAI,CAACkB,CAAC,CAACnB,MAAM;IACrB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAAC6B,CAAC,GAAG9C,CAAC,CAAC6C,MAAM,CAAC;IAC5BwB,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,EAAEK,CAAC,CAACC,GAAG,CAACtE,CAAC,EAAE8C,CAAC,CAAC,EAAE,IAAI,CAACkB,CAAC,GAAGK,CAAC;EAC1C,CAAC;EACDsN,OAAO,CAACxD,SAAS,CAACnO,CAAC,GAAG,UAAUmF,KAAK,EAAE;IACnC,IAAI,CAACpB,CAAC,GAAG,IAAI,CAACnB,CAAC,CAACd,CAAC,GAAGqD,KAAK,IAAI,KAAK;IAClC,IAAIyM,GAAG,GAAG,IAAI,CAAChP,CAAC,CAACf,CAAC;IAClB,IAAIyE,EAAE,GAAG9B,KAAK,CAAC,IAAI,CAACR,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACrB,CAAC,CAAC;IACtC,IAAI,CAAC0N,MAAM,CAAClM,GAAG,CAACkC,EAAE,EAAEsL,GAAG,EAAE,IAAI,CAAChP,CAAC,CAACf,CAAC,CAAC,EAAE,IAAI,CAACkC,CAAC,CAAC;IAC3C,IAAI,CAACE,CAAC,GAAGG,GAAG,CAACkC,EAAE,EAAE,IAAI,CAAC1D,CAAC,CAACf,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,CAACe,CAAC,CAACf,CAAC,GAAG,IAAI,CAACoC,CAAC,CAACpB,MAAM;IAC5D,IAAI,CAACmB,CAAC,GAAGI,GAAG,CAAC,IAAI,CAACJ,CAAC,EAAG,IAAI,CAACpB,CAAC,CAACoB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,EAAE,IAAI,CAACpB,CAAC,CAACoB,CAAC,IAAI,CAAC;EAC3D,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI2N,OAAO,CAACxD,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC7C,IAAI,CAACvE,CAAC,CAAC4Q,KAAK,CAAC,EAAE,IAAI,CAACxR,CAAC,CAACmF,KAAK,CAAC;EAChC,CAAC;EACD,OAAOwM,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO;AAChB;AACA;AACA;AACA,IAAIE,YAAY,GAAG,aAAe,YAAY;EAC1C;AACJ;AACA;AACA;EACI,SAASA,YAAYA,CAACzR,EAAE,EAAE;IACtB,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;IAChBoQ,QAAQ,CAAC,CACL3B,MAAM,EACN,YAAY;MAAE,OAAO,CAACuB,KAAK,EAAEuB,OAAO,CAAC;IAAE,CAAC,CAC3C,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAItB,IAAI,GAAG,IAAIsB,OAAO,CAAC,CAAC;MACxB7Q,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAOwB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,OAAO,SAASC,OAAOA,CAAC/Q,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACpC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,CACT,EAAE,UAAU0B,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACD,WAAW,CAACyB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEiO,GAAG,CAACuB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0O,WAAWA,CAAC/N,IAAI,EAAE2H,GAAG,EAAE;EACnC,OAAOlE,KAAK,CAACzD,IAAI,EAAE2H,GAAG,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,IAAIqJ,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAAC/B,IAAI,EAAE5P,EAAE,EAAE;IACpB,IAAI,CAACJ,CAAC,GAAGgN,GAAG,CAAC,CAAC;IACd,IAAI,CAAClK,CAAC,GAAG,CAAC;IACV,IAAI,CAACM,CAAC,GAAG,CAAC;IACVmO,OAAO,CAACd,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE5P,EAAE,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI2R,IAAI,CAAC5D,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC1CoM,OAAO,CAACpD,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAErM,KAAK,CAAC;EACnD,CAAC;EACD4M,IAAI,CAAC5D,SAAS,CAACnK,CAAC,GAAG,UAAUhE,CAAC,EAAEoF,CAAC,EAAE;IAC/B,IAAI,CAACpF,CAAC,CAACgE,CAAC,CAAChE,CAAC,CAAC;IACX,IAAI,CAAC8C,CAAC,IAAI9C,CAAC,CAAC6C,MAAM;IAClB,IAAImP,GAAG,GAAG7E,IAAI,CAACnN,CAAC,EAAE,IAAI,CAACiE,CAAC,EAAE,IAAI,CAACb,CAAC,IAAIiM,IAAI,CAAC,IAAI,CAACpL,CAAC,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAE,CAACA,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAChC,CAAC,EACNgM,GAAG,CAAC4C,GAAG,EAAE,IAAI,CAAC/N,CAAC,CAAC,EAAE,IAAI,CAACb,CAAC,GAAG,CAAC;IAChC,IAAIgC,CAAC,EACDkK,MAAM,CAAC0C,GAAG,EAAEA,GAAG,CAACnP,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC7C,CAAC,CAAC+D,CAAC,CAAC,CAAC,CAAC,EAAEuL,MAAM,CAAC0C,GAAG,EAAEA,GAAG,CAACnP,MAAM,GAAG,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAChF,IAAI,CAACwN,MAAM,CAAC0B,GAAG,EAAE5M,CAAC,CAAC;EACvB,CAAC;EACD,OAAO2M,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,SAASA,IAAI;AACb;AACA;AACA;AACA,IAAIE,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACjC,IAAI,EAAE5P,EAAE,EAAE;IACzBoQ,QAAQ,CAAC,CACLvB,KAAK,EACLE,GAAG,EACH,YAAY;MAAE,OAAO,CAACiB,KAAK,EAAEmB,OAAO,EAAEQ,IAAI,CAAC;IAAE,CAAC,CACjD,EAAE,IAAI,EAAET,YAAY,CAACb,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE5P,EAAE,CAAC,EAAE,UAAUmQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAI0B,IAAI,CAACxB,EAAE,CAACxP,IAAI,CAAC;MAC5BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAO4B,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,OAAO,SAASC,IAAIA,CAACnR,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACjC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,EACLE,GAAG,EACH,YAAY;IAAE,OAAO,CAACgD,QAAQ,CAAC;EAAE,CAAC,CACrC,EAAE,UAAU5B,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACoD,QAAQ,CAAC5B,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+R,QAAQA,CAACpR,IAAI,EAAEiP,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,EACLA,IAAI,GAAG,CAAC,CAAC;EACb,IAAIhQ,CAAC,GAAGgN,GAAG,CAAC,CAAC;IAAElK,CAAC,GAAG/B,IAAI,CAAC8B,MAAM;EAC9B7C,CAAC,CAACgE,CAAC,CAACjD,IAAI,CAAC;EACT,IAAIgD,CAAC,GAAGoJ,IAAI,CAACpM,IAAI,EAAEiP,IAAI,EAAEX,IAAI,CAACW,IAAI,CAAC,EAAE,CAAC,CAAC;IAAEpN,CAAC,GAAGmB,CAAC,CAAClB,MAAM;EACrD,OAAOuM,GAAG,CAACrL,CAAC,EAAEiM,IAAI,CAAC,EAAEV,MAAM,CAACvL,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAE5C,CAAC,CAAC+D,CAAC,CAAC,CAAC,CAAC,EAAEuL,MAAM,CAACvL,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAEE,CAAC,CAAC,EAAEiB,CAAC;AACxE;AACA;AACA;AACA;AACA,IAAIqO,MAAM,GAAG,aAAe,YAAY;EACpC;AACJ;AACA;AACA;EACI,SAASA,MAAMA,CAAChS,EAAE,EAAE;IAChB,IAAI,CAACgD,CAAC,GAAG,CAAC;IACVuO,OAAO,CAAClB,IAAI,CAAC,IAAI,EAAErQ,EAAE,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIgS,MAAM,CAACjE,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC5CwM,OAAO,CAACxD,SAAS,CAACvN,CAAC,CAAC6P,IAAI,CAAC,IAAI,EAAEe,KAAK,CAAC;IACrC,IAAI,IAAI,CAACpO,CAAC,EAAE;MACR,IAAIR,CAAC,GAAG,IAAI,CAACoB,CAAC,CAACnB,MAAM,GAAG,CAAC,GAAG2M,GAAG,CAAC,IAAI,CAACxL,CAAC,CAAC,GAAG,CAAC;MAC3C,IAAIpB,CAAC,IAAI,IAAI,CAACoB,CAAC,CAACnB,MAAM,IAAI,CAACsC,KAAK,EAC5B;MACJ,IAAI,CAACnB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC3B,CAAC,CAAC,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;IAC3C;IACA,IAAI+B,KAAK,EAAE;MACP,IAAI,IAAI,CAACnB,CAAC,CAACnB,MAAM,GAAG,CAAC,EACjB,MAAM,qBAAqB;MAC/B,IAAI,CAACmB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC;IACA;IACA;IACAoN,OAAO,CAACxD,SAAS,CAACnO,CAAC,CAACyQ,IAAI,CAAC,IAAI,EAAEtL,KAAK,CAAC;EACzC,CAAC;EACD,OAAOiN,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC;AACJ;AACA;AACA;EACI,SAASA,WAAWA,CAACjS,EAAE,EAAE;IACrB,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;IAChBoQ,QAAQ,CAAC,CACL3B,MAAM,EACNU,IAAI,EACJ,YAAY;MAAE,OAAO,CAACa,KAAK,EAAEuB,OAAO,EAAES,MAAM,CAAC;IAAE,CAAC,CACnD,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAI/B,IAAI,GAAG,IAAI+B,MAAM,CAAC,CAAC;MACvBtR,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAOgC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,OAAO,SAASC,MAAMA,CAACvR,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACnC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,EACNU,IAAI,EACJ,YAAY;IAAE,OAAO,CAACgD,UAAU,CAAC;EAAE,CAAC,CACvC,EAAE,UAAUhC,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACwD,UAAU,CAAChC,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmS,UAAUA,CAACxR,IAAI,EAAE2H,GAAG,EAAE;EAClC,OAAOlE,KAAK,CAACzD,IAAI,CAACwD,QAAQ,CAACiL,GAAG,CAACzO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE2H,GAAG,IAAI,IAAIzH,EAAE,CAACwO,GAAG,CAAC1O,IAAI,CAAC,CAAC,CAAC;AACxE;AACA;AACA;AACA;AACA,IAAIyR,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAACxC,IAAI,EAAE5P,EAAE,EAAE;IACpB,IAAI,CAACJ,CAAC,GAAGkN,KAAK,CAAC,CAAC;IAChB,IAAI,CAAC9J,CAAC,GAAG,CAAC;IACVmO,OAAO,CAACd,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE5P,EAAE,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIoS,IAAI,CAACrE,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC1CoM,OAAO,CAACpD,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAErM,KAAK,CAAC;EACnD,CAAC;EACDqN,IAAI,CAACrE,SAAS,CAACnK,CAAC,GAAG,UAAUhE,CAAC,EAAEoF,CAAC,EAAE;IAC/B,IAAI,CAACpF,CAAC,CAACgE,CAAC,CAAChE,CAAC,CAAC;IACX,IAAIgS,GAAG,GAAG7E,IAAI,CAACnN,CAAC,EAAE,IAAI,CAACiE,CAAC,EAAE,IAAI,CAACb,CAAC,IAAI,CAAC,EAAEgC,CAAC,IAAI,CAAC,EAAE,CAACA,CAAC,CAAC;IAClD,IAAI,IAAI,CAAChC,CAAC,EACNuM,GAAG,CAACqC,GAAG,EAAE,IAAI,CAAC/N,CAAC,CAAC,EAAE,IAAI,CAACb,CAAC,GAAG,CAAC;IAChC,IAAIgC,CAAC,EACDkK,MAAM,CAAC0C,GAAG,EAAEA,GAAG,CAACnP,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC7C,CAAC,CAAC+D,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACuM,MAAM,CAAC0B,GAAG,EAAE5M,CAAC,CAAC;EACvB,CAAC;EACD,OAAOoN,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,SAASA,IAAI;AACb;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACzC,IAAI,EAAE5P,EAAE,EAAE;IACzBoQ,QAAQ,CAAC,CACLvB,KAAK,EACLS,GAAG,EACH,YAAY;MAAE,OAAO,CAACU,KAAK,EAAEmB,OAAO,EAAEiB,IAAI,CAAC;IAAE,CAAC,CACjD,EAAE,IAAI,EAAElB,YAAY,CAACb,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE5P,EAAE,CAAC,EAAE,UAAUmQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAImC,IAAI,CAACjC,EAAE,CAACxP,IAAI,CAAC;MAC5BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACV;EACA,OAAOoC,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,OAAO,SAASC,IAAIA,CAAC3R,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACjC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,EACLS,GAAG,EACH,YAAY;IAAE,OAAO,CAACiD,QAAQ,CAAC;EAAE,CAAC,CACrC,EAAE,UAAUpC,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAAC4D,QAAQ,CAACpC,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuS,QAAQA,CAAC5R,IAAI,EAAEiP,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,EACLA,IAAI,GAAG,CAAC,CAAC;EACb,IAAInM,CAAC,GAAGqJ,KAAK,CAAC,CAAC;EACfrJ,CAAC,CAACG,CAAC,CAACjD,IAAI,CAAC;EACT,IAAIgD,CAAC,GAAGoJ,IAAI,CAACpM,IAAI,EAAEiP,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,OAAOL,GAAG,CAAC5L,CAAC,EAAEiM,IAAI,CAAC,EAAEV,MAAM,CAACvL,CAAC,EAAEA,CAAC,CAAClB,MAAM,GAAG,CAAC,EAAEgB,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC;AAC1D;AACA;AACA;AACA;AACA,IAAI6O,MAAM,GAAG,aAAe,YAAY;EACpC;AACJ;AACA;AACA;EACI,SAASA,MAAMA,CAACxS,EAAE,EAAE;IAChB,IAAI,CAACgD,CAAC,GAAG,CAAC;IACVuO,OAAO,CAAClB,IAAI,CAAC,IAAI,EAAErQ,EAAE,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIwS,MAAM,CAACzE,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC5CwM,OAAO,CAACxD,SAAS,CAACvN,CAAC,CAAC6P,IAAI,CAAC,IAAI,EAAEe,KAAK,CAAC;IACrC,IAAI,IAAI,CAACpO,CAAC,EAAE;MACR,IAAI,IAAI,CAACY,CAAC,CAACnB,MAAM,GAAG,CAAC,IAAI,CAACsC,KAAK,EAC3B;MACJ,IAAI,CAACnB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnB,CAAC,GAAG,CAAC;IAC3C;IACA,IAAI+B,KAAK,EAAE;MACP,IAAI,IAAI,CAACnB,CAAC,CAACnB,MAAM,GAAG,CAAC,EACjB,MAAM,qBAAqB;MAC/B,IAAI,CAACmB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC;IACA;IACA;IACAoN,OAAO,CAACxD,SAAS,CAACnO,CAAC,CAACyQ,IAAI,CAAC,IAAI,EAAEtL,KAAK,CAAC;EACzC,CAAC;EACD,OAAOyN,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC;AACJ;AACA;AACA;EACI,SAASA,WAAWA,CAACzS,EAAE,EAAE;IACrB,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;IAChBoQ,QAAQ,CAAC,CACL3B,MAAM,EACNe,IAAI,EACJ,YAAY;MAAE,OAAO,CAACQ,KAAK,EAAEuB,OAAO,EAAEiB,MAAM,CAAC;IAAE,CAAC,CACnD,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAIvC,IAAI,GAAG,IAAIuC,MAAM,CAAC,CAAC;MACvB9R,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACV;EACA,OAAOwC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,OAAO,SAASC,MAAMA,CAAC/R,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACnC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO2P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,EACNe,IAAI,EACJ,YAAY;IAAE,OAAO,CAACmD,UAAU,CAAC;EAAE,CAAC,CACvC,EAAE,UAAUxC,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACgE,UAAU,CAACxC,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEiO,GAAG,CAACuB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2S,UAAUA,CAAChS,IAAI,EAAE2H,GAAG,EAAE;EAClC,OAAOlE,KAAK,EAAEqL,GAAG,CAAC9O,IAAI,CAAC,EAAEA,IAAI,CAACwD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGmE,GAAG,CAAC;AACxD;AACA;AACA,SAASwJ,IAAI,IAAIc,QAAQ,EAAEf,SAAS,IAAIgB,aAAa;AACrD;AACA,SAASd,QAAQ,IAAIe,YAAY,EAAEnB,IAAI,IAAIoB,QAAQ;AACnD;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;EACI,SAASA,UAAUA,CAAChT,EAAE,EAAE;IACpB,IAAI,CAACiT,CAAC,GAAGjB,MAAM;IACf,IAAI,CAACkB,CAAC,GAAG3B,OAAO;IAChB,IAAI,CAAC4B,CAAC,GAAGX,MAAM;IACf,IAAI,CAACtC,MAAM,GAAGlQ,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIgT,UAAU,CAACjF,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACmL,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAI,CAAC,IAAI,CAAC1N,CAAC,EAAE;MACT,IAAI,IAAI,CAACoB,CAAC,IAAI,IAAI,CAACA,CAAC,CAACnB,MAAM,EAAE;QACzB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAAC,IAAI,CAAC+C,CAAC,CAACnB,MAAM,GAAG2O,KAAK,CAAC3O,MAAM,CAAC;QAC5CwB,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,EAAEK,CAAC,CAACC,GAAG,CAACkN,KAAK,EAAE,IAAI,CAACxN,CAAC,CAACnB,MAAM,CAAC;MAC9C,CAAC,MAEG,IAAI,CAACmB,CAAC,GAAGwN,KAAK;MAClB,IAAI,IAAI,CAACxN,CAAC,CAACnB,MAAM,GAAG,CAAC,EAAE;QACnB,IAAI2Q,OAAO,GAAG,IAAI;QAClB,IAAIpT,EAAE,GAAG,SAAAA,CAAA,EAAY;UAAEoT,OAAO,CAAClD,MAAM,CAACmD,KAAK,CAACD,OAAO,EAAEE,SAAS,CAAC;QAAE,CAAC;QAClE,IAAI,CAAC9Q,CAAC,GAAI,IAAI,CAACoB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GACzD,IAAI,IAAI,CAACqP,CAAC,CAACjT,EAAE,CAAC,GACb,CAAC,IAAI,CAAC4D,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,GACjF,IAAI,IAAI,CAACsP,CAAC,CAAClT,EAAE,CAAC,GACd,IAAI,IAAI,CAACmT,CAAC,CAACnT,EAAE,CAAC;QACxB,IAAI,CAACwC,CAAC,CAACqE,IAAI,CAAC,IAAI,CAACjD,CAAC,EAAEmB,KAAK,CAAC;QAC1B,IAAI,CAACnB,CAAC,GAAG,IAAI;MACjB;IACJ,CAAC,MAEG,IAAI,CAACpB,CAAC,CAACqE,IAAI,CAACuK,KAAK,EAAErM,KAAK,CAAC;EACjC,CAAC;EACD,OAAOiO,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA;AACA,IAAIO,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;EACI,SAASA,eAAeA,CAACvT,EAAE,EAAE;IACzB,IAAI,CAACiT,CAAC,GAAGhB,WAAW;IACpB,IAAI,CAACiB,CAAC,GAAGzB,YAAY;IACrB,IAAI,CAAC0B,CAAC,GAAGV,WAAW;IACpB,IAAI,CAACvC,MAAM,GAAGlQ,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIuT,eAAe,CAACxF,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IACrDiO,UAAU,CAACjF,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAErM,KAAK,CAAC;EACtD,CAAC;EACD,OAAOwO,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,SAASA,eAAe;AACxB,OAAO,SAASC,UAAUA,CAAC7S,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EACvC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAQW,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACjDuR,MAAM,CAACvR,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,CAAC,GACrB,CAACW,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GACzE+Q,OAAO,CAAC/Q,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,CAAC,GACvB0S,MAAM,CAAC/R,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyT,cAAcA,CAAC9S,IAAI,EAAE2H,GAAG,EAAE;EACtC,OAAQ3H,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACjDwR,UAAU,CAACxR,IAAI,EAAE2H,GAAG,CAAC,GACpB,CAAC3H,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GACzE+N,WAAW,CAAC/N,IAAI,EAAE2H,GAAG,CAAC,GACtBqK,UAAU,CAAChS,IAAI,EAAE2H,GAAG,CAAC;AACnC;AACA;AACA,IAAIoL,IAAI,GAAG,SAAAA,CAAU/P,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAE3B,CAAC,EAAE;EAC7B,KAAK,IAAI8I,CAAC,IAAIhJ,CAAC,EAAE;IACb,IAAIgQ,GAAG,GAAGhQ,CAAC,CAACgJ,CAAC,CAAC;MAAE1I,CAAC,GAAGL,CAAC,GAAG+I,CAAC;IACzB,IAAIgH,GAAG,YAAY9S,EAAE,EACjB2E,CAAC,CAACvB,CAAC,CAAC,GAAG,CAAC0P,GAAG,EAAE9P,CAAC,CAAC,CAAC,KACf,IAAI+P,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EACvBnO,CAAC,CAACvB,CAAC,CAAC,GAAG,CAAC0P,GAAG,CAAC,CAAC,CAAC,EAAExG,GAAG,CAACtJ,CAAC,EAAE8P,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAEhCD,IAAI,CAACC,GAAG,EAAE1P,CAAC,GAAG,GAAG,EAAEuB,CAAC,EAAE3B,CAAC,CAAC;EAChC;AACJ,CAAC;AACD;AACA,IAAIiQ,EAAE,GAAG,OAAOC,WAAW,IAAI,WAAW,IAAI,aAAc,IAAIA,WAAW,CAAC,CAAC;AAC7E;AACA,IAAIxG,EAAE,GAAG,OAAOyG,WAAW,IAAI,WAAW,IAAI,aAAc,IAAIA,WAAW,CAAC,CAAC;AAC7E;AACA,IAAIC,GAAG,GAAG,CAAC;AACX,IAAI;EACA1G,EAAE,CAAC2G,MAAM,CAAClN,EAAE,EAAE;IAAEmN,MAAM,EAAE;EAAK,CAAC,CAAC;EAC/BF,GAAG,GAAG,CAAC;AACX,CAAC,CACD,OAAOzT,CAAC,EAAE,CAAE;AACZ;AACA,IAAI4T,KAAK,GAAG,SAAAA,CAAUzQ,CAAC,EAAE;EACrB,KAAK,IAAIhC,CAAC,GAAG,EAAE,EAAED,CAAC,GAAG,CAAC,IAAI;IACtB,IAAI9B,CAAC,GAAG+D,CAAC,CAACjC,CAAC,EAAE,CAAC;IACd,IAAIH,EAAE,GAAG,CAAC3B,CAAC,GAAG,GAAG,KAAKA,CAAC,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,CAAC;IAC1C,IAAI8B,CAAC,GAAGH,EAAE,GAAGoC,CAAC,CAAClB,MAAM,EACjB,OAAO,CAACd,CAAC,EAAEqC,GAAG,CAACL,CAAC,EAAEjC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACH,EAAE,EACHI,CAAC,IAAI0S,MAAM,CAACC,YAAY,CAAC1U,CAAC,CAAC,CAAC,KAC3B,IAAI2B,EAAE,IAAI,CAAC,EAAE;MACd3B,CAAC,GAAG,CAAC,CAACA,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC+D,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAACiC,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAIiC,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAG,IAAI,KAAK,EACnFC,CAAC,IAAI0S,MAAM,CAACC,YAAY,CAAC,KAAK,GAAI1U,CAAC,IAAI,EAAG,EAAE,KAAK,GAAIA,CAAC,GAAG,IAAK,CAAC;IACvE,CAAC,MACI,IAAI2B,EAAE,GAAG,CAAC,EACXI,CAAC,IAAI0S,MAAM,CAACC,YAAY,CAAC,CAAC1U,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI+D,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAG,CAAC,CAAC,KAExDC,CAAC,IAAI0S,MAAM,CAACC,YAAY,CAAC,CAAC1U,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC+D,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAIiC,CAAC,CAACjC,CAAC,EAAE,CAAC,GAAG,EAAG,CAAC;EACrF;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAI6S,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;EACI,SAASA,UAAUA,CAACvU,EAAE,EAAE;IACpB,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;IAChB,IAAIiU,GAAG,EACH,IAAI,CAACzO,CAAC,GAAG,IAAIwO,WAAW,CAAC,CAAC,CAAC,KAE3B,IAAI,CAACpQ,CAAC,GAAGoD,EAAE;EACnB;EACA;AACJ;AACA;AACA;AACA;EACIuN,UAAU,CAACxG,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACmL,MAAM,EACZ,MAAM,aAAa;IACvBnL,KAAK,GAAG,CAAC,CAACA,KAAK;IACf,IAAI,IAAI,CAACS,CAAC,EAAE;MACR,IAAI,CAAC0K,MAAM,CAAC,IAAI,CAAC1K,CAAC,CAAC0O,MAAM,CAAC9C,KAAK,EAAE;QAAE+C,MAAM,EAAE;MAAK,CAAC,CAAC,EAAEpP,KAAK,CAAC;MAC1D,IAAIA,KAAK,EAAE;QACP,IAAI,IAAI,CAACS,CAAC,CAAC0O,MAAM,CAAC,CAAC,CAACzR,MAAM,EACtB,MAAM,oBAAoB;QAC9B,IAAI,CAAC+C,CAAC,GAAG,IAAI;MACjB;MACA;IACJ;IACA,IAAI,CAAC,IAAI,CAAC5B,CAAC,EACP,MAAM,iBAAiB;IAC3B,IAAIS,GAAG,GAAG,IAAIxD,EAAE,CAAC,IAAI,CAAC+C,CAAC,CAACnB,MAAM,GAAG2O,KAAK,CAAC3O,MAAM,CAAC;IAC9C4B,GAAG,CAACH,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC;IACfS,GAAG,CAACH,GAAG,CAACkN,KAAK,EAAE,IAAI,CAACxN,CAAC,CAACnB,MAAM,CAAC;IAC7B,IAAIZ,EAAE,GAAGuS,KAAK,CAAC/P,GAAG,CAAC;MAAE4J,EAAE,GAAGpM,EAAE,CAAC,CAAC,CAAC;MAAE2S,EAAE,GAAG3S,EAAE,CAAC,CAAC,CAAC;IAC3C,IAAIkD,KAAK,EAAE;MACP,IAAIyP,EAAE,CAAC/R,MAAM,EACT,MAAM,oBAAoB;MAC9B,IAAI,CAACmB,CAAC,GAAG,IAAI;IACjB,CAAC,MAEG,IAAI,CAACA,CAAC,GAAG4Q,EAAE;IACf,IAAI,CAACtE,MAAM,CAACjC,EAAE,EAAElJ,KAAK,CAAC;EAC1B,CAAC;EACD,OAAOwP,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA;AACA,IAAIE,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;EACI,SAASA,UAAUA,CAACzU,EAAE,EAAE;IACpB,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIyU,UAAU,CAAC1G,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACmL,MAAM,EACZ,MAAM,aAAa;IACvB,IAAI,IAAI,CAACvM,CAAC,EACN,MAAM,iBAAiB;IAC3B,IAAI,CAACuM,MAAM,CAACwE,OAAO,CAACtD,KAAK,CAAC,EAAE,IAAI,CAACzN,CAAC,GAAGoB,KAAK,IAAI,KAAK,CAAC;EACxD,CAAC;EACD,OAAO0P,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACjC,IAAIA,MAAM,EAAE;IACR,IAAIC,IAAI,GAAG,IAAIhU,EAAE,CAAC8T,GAAG,CAAClS,MAAM,CAAC;IAC7B,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiT,GAAG,CAAClS,MAAM,EAAE,EAAEf,CAAC,EAC/BmT,IAAI,CAACnT,CAAC,CAAC,GAAGiT,GAAG,CAAC7D,UAAU,CAACpP,CAAC,CAAC;IAC/B,OAAOmT,IAAI;EACf;EACA,IAAIf,EAAE,EACF,OAAOA,EAAE,CAACgB,MAAM,CAACH,GAAG,CAAC;EACzB,IAAIjS,CAAC,GAAGiS,GAAG,CAAClS,MAAM;EAClB,IAAIsS,EAAE,GAAG,IAAIlU,EAAE,CAAC8T,GAAG,CAAClS,MAAM,IAAIkS,GAAG,CAAClS,MAAM,IAAI,CAAC,CAAC,CAAC;EAC/C,IAAIuS,EAAE,GAAG,CAAC;EACV,IAAI/U,CAAC,GAAG,SAAAA,CAAU+C,CAAC,EAAE;IAAE+R,EAAE,CAACC,EAAE,EAAE,CAAC,GAAGhS,CAAC;EAAE,CAAC;EACtC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,CAAC,EAAE,EAAEhB,CAAC,EAAE;IACxB,IAAIsT,EAAE,GAAG,CAAC,GAAGD,EAAE,CAACtS,MAAM,EAAE;MACpB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAACmU,EAAE,GAAG,CAAC,IAAKtS,CAAC,GAAGhB,CAAC,IAAK,CAAC,CAAC,CAAC;MACvCuC,CAAC,CAACC,GAAG,CAAC6Q,EAAE,CAAC;MACTA,EAAE,GAAG9Q,CAAC;IACV;IACA,IAAIrE,CAAC,GAAG+U,GAAG,CAAC7D,UAAU,CAACpP,CAAC,CAAC;IACzB,IAAI9B,CAAC,GAAG,GAAG,IAAIgV,MAAM,EACjB3U,CAAC,CAACL,CAAC,CAAC,CAAC,KACJ,IAAIA,CAAC,GAAG,IAAI,EACbK,CAAC,CAAC,GAAG,GAAIL,CAAC,IAAI,CAAE,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAIL,CAAC,GAAG,EAAG,CAAC,CAAC,KACpC,IAAIA,CAAC,GAAG,KAAK,IAAIA,CAAC,GAAG,KAAK,EAC3BA,CAAC,GAAG,KAAK,IAAIA,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAI+U,GAAG,CAAC7D,UAAU,CAAC,EAAEpP,CAAC,CAAC,GAAG,IAAK,EACvDzB,CAAC,CAAC,GAAG,GAAIL,CAAC,IAAI,EAAG,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAKL,CAAC,IAAI,EAAE,GAAI,EAAG,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAKL,CAAC,IAAI,CAAC,GAAI,EAAG,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAIL,CAAC,GAAG,EAAG,CAAC,CAAC,KAE/FK,CAAC,CAAC,GAAG,GAAIL,CAAC,IAAI,EAAG,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAKL,CAAC,IAAI,CAAC,GAAI,EAAG,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAIL,CAAC,GAAG,EAAG,CAAC;EACvE;EACA,OAAOoE,GAAG,CAAC+Q,EAAE,EAAE,CAAC,EAAEC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAC5Q,GAAG,EAAEuQ,MAAM,EAAE;EACnC,IAAIA,MAAM,EAAE;IACR,IAAIjT,CAAC,GAAG,EAAE;IACV,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,GAAG,CAAC5B,MAAM,EAAEf,CAAC,IAAI,KAAK,EACtCC,CAAC,IAAI0S,MAAM,CAACC,YAAY,CAACjB,KAAK,CAAC,IAAI,EAAEhP,GAAG,CAACF,QAAQ,CAACzC,CAAC,EAAEA,CAAC,GAAG,KAAK,CAAC,CAAC;IACpE,OAAOC,CAAC;EACZ,CAAC,MACI,IAAI4L,EAAE,EACP,OAAOA,EAAE,CAAC2G,MAAM,CAAC7P,GAAG,CAAC,CAAC,KACrB;IACD,IAAIxC,EAAE,GAAGuS,KAAK,CAAC/P,GAAG,CAAC;MAAEiE,GAAG,GAAGzG,EAAE,CAAC,CAAC,CAAC;MAAEqT,GAAG,GAAGrT,EAAE,CAAC,CAAC,CAAC;IAC7C,IAAIqT,GAAG,CAACzS,MAAM,EACV,MAAM,oBAAoB;IAC9B,OAAO6F,GAAG;EACd;AACJ;AACA;AACA;AACA,IAAI6M,GAAG,GAAG,SAAAA,CAAUzS,CAAC,EAAE;EAAE,OAAOA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAAE,CAAC;AAC1E;AACA,IAAI0S,IAAI,GAAG,SAAAA,CAAUzR,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAOA,CAAC,GAAG,EAAE,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC;AAAE,CAAC;AAC7E;AACA,IAAI4T,EAAE,GAAG,SAAAA,CAAU1R,CAAC,EAAElC,CAAC,EAAE6T,CAAC,EAAE;EACxB,IAAIC,GAAG,GAAGjF,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC;IAAE4L,EAAE,GAAG4H,SAAS,CAACtR,CAAC,CAACQ,QAAQ,CAAC1C,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,GAAG8T,GAAG,CAAC,EAAE,EAAEjF,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAAE+T,EAAE,GAAG/T,CAAC,GAAG,EAAE,GAAG8T,GAAG;IAAE3M,EAAE,GAAG2H,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC;EACxI,IAAII,EAAE,GAAGyT,CAAC,IAAI1M,EAAE,IAAI,UAAU,GAAG6M,IAAI,CAAC9R,CAAC,EAAE6R,EAAE,CAAC,GAAG,CAAC5M,EAAE,EAAE2H,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAE8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,CAAC;IAAEiU,EAAE,GAAG7T,EAAE,CAAC,CAAC,CAAC;IAAE8T,EAAE,GAAG9T,EAAE,CAAC,CAAC,CAAC;IAAE+T,GAAG,GAAG/T,EAAE,CAAC,CAAC,CAAC;EACtH,OAAO,CAACyO,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAEiU,EAAE,EAAEC,EAAE,EAAEtI,EAAE,EAAEmI,EAAE,GAAGlF,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAEmU,GAAG,CAAC;AAC/E,CAAC;AACD;AACA,IAAIH,IAAI,GAAG,SAAAA,CAAU9R,CAAC,EAAElC,CAAC,EAAE;EACvB,OAAO6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,CACvC;EACJ,OAAO,CAAC+O,EAAE,CAAC7M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAE+O,EAAE,CAAC7M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,EAAE+O,EAAE,CAAC7M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,CAAC;AACvD,CAAC;AACD;AACA,IAAIoU,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACrB,IAAInT,EAAE,GAAG,CAAC;EACV,IAAImT,EAAE,EAAE;IACJ,KAAK,IAAInJ,CAAC,IAAImJ,EAAE,EAAE;MACd,IAAIpT,CAAC,GAAGoT,EAAE,CAACnJ,CAAC,CAAC,CAAClK,MAAM;MACpB,IAAIC,CAAC,GAAG,KAAK,EACT,MAAM,sBAAsB;MAChCC,EAAE,IAAID,CAAC,GAAG,CAAC;IACf;EACJ;EACA,OAAOC,EAAE;AACb,CAAC;AACD;AACA,IAAIoT,GAAG,GAAG,SAAAA,CAAUpS,CAAC,EAAElC,CAAC,EAAEuD,CAAC,EAAEqI,EAAE,EAAE2I,CAAC,EAAEpW,CAAC,EAAEqW,EAAE,EAAErT,EAAE,EAAE;EAC3C,IAAId,EAAE,GAAGuL,EAAE,CAAC5K,MAAM;IAAEqT,EAAE,GAAG9Q,CAAC,CAACkR,KAAK;IAAEC,GAAG,GAAGvT,EAAE,IAAIA,EAAE,CAACH,MAAM;EACvD,IAAI2T,GAAG,GAAGP,IAAI,CAACC,EAAE,CAAC;EAClB5G,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAEwU,EAAE,IAAI,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC,EAAExU,CAAC,IAAI,CAAC;EACxD,IAAIwU,EAAE,IAAI,IAAI,EACVtS,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAEkC,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAGuD,CAAC,CAACqR,EAAE;EAC9B1S,CAAC,CAAClC,CAAC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,CAAC,CAAC;EACnBkC,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAIuD,CAAC,CAACsR,IAAI,IAAI,CAAC,IAAK1W,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE+D,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAGuU,CAAC,IAAI,CAAC;EAC1DrS,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAGuD,CAAC,CAACuR,WAAW,GAAG,GAAG,EAAE5S,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAGuD,CAAC,CAACuR,WAAW,IAAI,CAAC;EACzD,IAAIrQ,EAAE,GAAG,IAAI0K,IAAI,CAAC5L,CAAC,CAAC0L,KAAK,IAAI,IAAI,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG7L,CAAC,CAAC0L,KAAK,CAAC;IAAE8F,CAAC,GAAGtQ,EAAE,CAACuQ,WAAW,CAAC,CAAC,GAAG,IAAI;EACtF,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAChB,MAAM,6BAA6B;EACvCtH,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAG+U,CAAC,IAAI,EAAE,GAAMtQ,EAAE,CAACwQ,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAK,EAAG,GAAIxQ,EAAE,CAACyQ,OAAO,CAAC,CAAC,IAAI,EAAG,GAAIzQ,EAAE,CAAC0Q,QAAQ,CAAC,CAAC,IAAI,EAAG,GAAI1Q,EAAE,CAAC2Q,UAAU,CAAC,CAAC,IAAI,CAAE,GAAI3Q,EAAE,CAAC4Q,UAAU,CAAC,CAAC,KAAK,CAAE,CAAC,EAAErV,CAAC,IAAI,CAAC;EAC/J,IAAI7B,CAAC,IAAI,IAAI,EAAE;IACXsP,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAEuD,CAAC,CAAC4H,GAAG,CAAC;IACnBsC,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAE7B,CAAC,CAAC;IACnBsP,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAEuD,CAAC,CAAC0K,IAAI,CAAC;EAC5B;EACAR,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,EAAE,EAAEK,EAAE,CAAC;EACrBoN,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,EAAE,EAAE2U,GAAG,CAAC,EAAE3U,CAAC,IAAI,EAAE;EAC/B,IAAIwU,EAAE,IAAI,IAAI,EAAE;IACZ/G,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAE0U,GAAG,CAAC;IACjBjH,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAEuD,CAAC,CAAC+R,KAAK,CAAC;IACzB7H,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,EAAE,EAAEwU,EAAE,CAAC,EAAExU,CAAC,IAAI,EAAE;EAClC;EACAkC,CAAC,CAACO,GAAG,CAACmJ,EAAE,EAAE5L,CAAC,CAAC;EACZA,CAAC,IAAIK,EAAE;EACP,IAAIsU,GAAG,EAAE;IACL,KAAK,IAAIzJ,CAAC,IAAImJ,EAAE,EAAE;MACd,IAAIkB,GAAG,GAAGlB,EAAE,CAACnJ,CAAC,CAAC;QAAEjK,CAAC,GAAGsU,GAAG,CAACvU,MAAM;MAC/ByM,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAE,CAACkL,CAAC,CAAC;MAChBuC,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAEiB,CAAC,CAAC;MACnBiB,CAAC,CAACO,GAAG,CAAC8S,GAAG,EAAEvV,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,GAAGiB,CAAC;IACjC;EACJ;EACA,IAAIyT,GAAG,EACHxS,CAAC,CAACO,GAAG,CAACtB,EAAE,EAAEnB,CAAC,CAAC,EAAEA,CAAC,IAAI0U,GAAG;EAC1B,OAAO1U,CAAC;AACZ,CAAC;AACD;AACA,IAAIwV,GAAG,GAAG,SAAAA,CAAUpT,CAAC,EAAEpC,CAAC,EAAE7B,CAAC,EAAE+D,CAAC,EAAEnD,CAAC,EAAE;EAC/B0O,MAAM,CAACrL,CAAC,EAAEpC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzByN,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,CAAC,EAAE7B,CAAC,CAAC;EACnBsP,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAE7B,CAAC,CAAC;EACpBsP,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAEkC,CAAC,CAAC;EACpBuL,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAEjB,CAAC,CAAC;AACxB,CAAC;AACD;AACA;AACA;AACA,IAAI0W,cAAc,GAAG,aAAe,YAAY;EAC5C;AACJ;AACA;AACA;EACI,SAASA,cAAcA,CAACzG,QAAQ,EAAE;IAC9B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC7Q,CAAC,GAAGgN,GAAG,CAAC,CAAC;IACd,IAAI,CAAC8C,IAAI,GAAG,CAAC;IACb,IAAI,CAAC6G,WAAW,GAAG,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,cAAc,CAACnJ,SAAS,CAACoJ,OAAO,GAAG,UAAU/F,KAAK,EAAErM,KAAK,EAAE;IACvD,IAAI,CAACmL,MAAM,CAAC,IAAI,EAAEkB,KAAK,EAAErM,KAAK,CAAC;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACImS,cAAc,CAACnJ,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IACpD,IAAI,CAAC,IAAI,CAACmL,MAAM,EACZ,MAAM,iDAAiD;IAC3D,IAAI,CAACtQ,CAAC,CAACgE,CAAC,CAACwN,KAAK,CAAC;IACf,IAAI,CAAC1B,IAAI,IAAI0B,KAAK,CAAC3O,MAAM;IACzB,IAAIsC,KAAK,EACL,IAAI,CAAC6H,GAAG,GAAG,IAAI,CAAChN,CAAC,CAAC+D,CAAC,CAAC,CAAC;IACzB,IAAI,CAACwT,OAAO,CAAC/F,KAAK,EAAErM,KAAK,IAAI,KAAK,CAAC;EACvC,CAAC;EACD,OAAOmS,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc;AACvB;AACA;AACA;AACA;AACA;AACA,IAAIE,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;AACA;EACI,SAASA,UAAUA,CAAC3G,QAAQ,EAAEb,IAAI,EAAE;IAChC,IAAIwD,OAAO,GAAG,IAAI;IAClB,IAAI,CAACxD,IAAI,EACLA,IAAI,GAAG,CAAC,CAAC;IACbsH,cAAc,CAAC7G,IAAI,CAAC,IAAI,EAAEI,QAAQ,CAAC;IACnC,IAAI,CAAC9M,CAAC,GAAG,IAAIwN,OAAO,CAACvB,IAAI,EAAE,UAAUvL,GAAG,EAAEU,KAAK,EAAE;MAC7CqO,OAAO,CAAClD,MAAM,CAAC,IAAI,EAAE7L,GAAG,EAAEU,KAAK,CAAC;IACpC,CAAC,CAAC;IACF,IAAI,CAACwR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACD,IAAI,GAAGnB,GAAG,CAACvF,IAAI,CAAC5C,KAAK,CAAC;EAC/B;EACAoK,UAAU,CAACrJ,SAAS,CAACoJ,OAAO,GAAG,UAAU/F,KAAK,EAAErM,KAAK,EAAE;IACnD,IAAI;MACA,IAAI,CAACpB,CAAC,CAACkD,IAAI,CAACuK,KAAK,EAAErM,KAAK,CAAC;IAC7B,CAAC,CACD,OAAOvE,CAAC,EAAE;MACN,IAAI,CAAC0P,MAAM,CAAC1P,CAAC,EAAE,IAAI,EAAEuE,KAAK,CAAC;IAC/B;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIqS,UAAU,CAACrJ,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAChDmS,cAAc,CAACnJ,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAErM,KAAK,CAAC;EAC1D,CAAC;EACD,OAAOqS,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;AACA;EACI,SAASA,eAAeA,CAAC5G,QAAQ,EAAEb,IAAI,EAAE;IACrC,IAAIwD,OAAO,GAAG,IAAI;IAClB,IAAI,CAACxD,IAAI,EACLA,IAAI,GAAG,CAAC,CAAC;IACbsH,cAAc,CAAC7G,IAAI,CAAC,IAAI,EAAEI,QAAQ,CAAC;IACnC,IAAI,CAAC9M,CAAC,GAAG,IAAI0N,YAAY,CAACzB,IAAI,EAAE,UAAUC,GAAG,EAAExL,GAAG,EAAEU,KAAK,EAAE;MACvDqO,OAAO,CAAClD,MAAM,CAACL,GAAG,EAAExL,GAAG,EAAEU,KAAK,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,CAACwR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACD,IAAI,GAAGnB,GAAG,CAACvF,IAAI,CAAC5C,KAAK,CAAC;IAC3B,IAAI,CAAC8C,SAAS,GAAG,IAAI,CAACnM,CAAC,CAACmM,SAAS;EACrC;EACAuH,eAAe,CAACtJ,SAAS,CAACoJ,OAAO,GAAG,UAAU/F,KAAK,EAAErM,KAAK,EAAE;IACxD,IAAI,CAACpB,CAAC,CAACkD,IAAI,CAACuK,KAAK,EAAErM,KAAK,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIsS,eAAe,CAACtJ,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IACrDmS,cAAc,CAACnJ,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAErM,KAAK,CAAC;EAC1D,CAAC;EACD,OAAOsS,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,SAASA,eAAe;AACxB;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAG,aAAe,YAAY;EACjC;AACJ;AACA;AACA;AACA;EACI,SAASA,GAAGA,CAACtX,EAAE,EAAE;IACb,IAAI,CAACkQ,MAAM,GAAGlQ,EAAE;IAChB,IAAI,CAACgW,CAAC,GAAG,EAAE;IACX,IAAI,CAACrS,CAAC,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;EACI2T,GAAG,CAACvJ,SAAS,CAACxH,GAAG,GAAG,UAAUgR,IAAI,EAAE;IAChC,IAAInE,OAAO,GAAG,IAAI;IAClB,IAAI,IAAI,CAACzP,CAAC,GAAG,CAAC,EACV,MAAM,iBAAiB;IAC3B,IAAIqB,CAAC,GAAG0P,OAAO,CAAC6C,IAAI,CAAC9G,QAAQ,CAAC;MAAE3O,EAAE,GAAGkD,CAAC,CAACvC,MAAM;IAC7C,IAAI+U,GAAG,GAAGD,IAAI,CAACE,OAAO;MAAE5T,CAAC,GAAG2T,GAAG,IAAI9C,OAAO,CAAC8C,GAAG,CAAC;IAC/C,IAAIxB,CAAC,GAAGlU,EAAE,IAAIyV,IAAI,CAAC9G,QAAQ,CAAChO,MAAM,IAAKoB,CAAC,IAAK2T,GAAG,CAAC/U,MAAM,IAAIoB,CAAC,CAACpB,MAAQ;IACrE,IAAIiV,EAAE,GAAG5V,EAAE,GAAG+T,IAAI,CAAC0B,IAAI,CAACrB,KAAK,CAAC,GAAG,EAAE;IACnC,IAAIpU,EAAE,GAAG,KAAK,EACV,MAAM,mBAAmB;IAC7B,IAAI6V,MAAM,GAAG,IAAI9W,EAAE,CAAC6W,EAAE,CAAC;IACvB3B,GAAG,CAAC4B,MAAM,EAAE,CAAC,EAAEJ,IAAI,EAAEvS,CAAC,EAAEgR,CAAC,CAAC;IAC1B,IAAI4B,IAAI,GAAG,CAACD,MAAM,CAAC;IACnB,IAAIE,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,EAAEE,EAAE,GAAGC,MAAM,CAACtV,MAAM,EAAEqV,EAAE,EAAE,EAAE;QACtD,IAAIE,GAAG,GAAGD,MAAM,CAACD,EAAE,CAAC;QACpB1E,OAAO,CAAClD,MAAM,CAAC,IAAI,EAAE8H,GAAG,EAAE,KAAK,CAAC;MACpC;MACAJ,IAAI,GAAG,EAAE;IACb,CAAC;IACD,IAAItQ,EAAE,GAAG,IAAI,CAAC3D,CAAC;IACf,IAAI,CAACA,CAAC,GAAG,CAAC;IACV,IAAIsU,GAAG,GAAG,IAAI,CAACjC,CAAC,CAACvT,MAAM;IACvB,IAAIyV,EAAE,GAAG/K,GAAG,CAACoK,IAAI,EAAE;MACfvS,CAAC,EAAEA,CAAC;MACJgR,CAAC,EAAEA,CAAC;MACJnS,CAAC,EAAEA,CAAC;MACJ2B,CAAC,EAAE,SAAAA,CAAA,EAAY;QACX,IAAI+R,IAAI,CAACzH,SAAS,EACdyH,IAAI,CAACzH,SAAS,CAAC,CAAC;MACxB,CAAC;MACDnO,CAAC,EAAE,SAAAA,CAAA,EAAY;QACXkW,IAAI,CAAC,CAAC;QACN,IAAIvQ,EAAE,EAAE;UACJ,IAAI6Q,GAAG,GAAG/E,OAAO,CAAC4C,CAAC,CAACiC,GAAG,GAAG,CAAC,CAAC;UAC5B,IAAIE,GAAG,EACHA,GAAG,CAACxW,CAAC,CAAC,CAAC,CAAC,KAERyR,OAAO,CAACzP,CAAC,GAAG,CAAC;QACrB;QACA2D,EAAE,GAAG,CAAC;MACV;IACJ,CAAC,CAAC;IACF,IAAIS,EAAE,GAAG,CAAC;IACVwP,IAAI,CAACrH,MAAM,GAAG,UAAUL,GAAG,EAAExL,GAAG,EAAEU,KAAK,EAAE;MACrC,IAAI8K,GAAG,EAAE;QACLuD,OAAO,CAAClD,MAAM,CAACL,GAAG,EAAExL,GAAG,EAAEU,KAAK,CAAC;QAC/BqO,OAAO,CAACtD,SAAS,CAAC,CAAC;MACvB,CAAC,MACI;QACD/H,EAAE,IAAI1D,GAAG,CAAC5B,MAAM;QAChBmV,IAAI,CAAC/Q,IAAI,CAACxC,GAAG,CAAC;QACd,IAAIU,KAAK,EAAE;UACP,IAAIqT,EAAE,GAAG,IAAIvX,EAAE,CAAC,EAAE,CAAC;UACnBqO,MAAM,CAACkJ,EAAE,EAAE,CAAC,EAAE,SAAS,CAAC;UACxBlJ,MAAM,CAACkJ,EAAE,EAAE,CAAC,EAAEb,IAAI,CAAC3K,GAAG,CAAC;UACvBsC,MAAM,CAACkJ,EAAE,EAAE,CAAC,EAAErQ,EAAE,CAAC;UACjBmH,MAAM,CAACkJ,EAAE,EAAE,EAAE,EAAEb,IAAI,CAAC7H,IAAI,CAAC;UACzBkI,IAAI,CAAC/Q,IAAI,CAACuR,EAAE,CAAC;UACbF,EAAE,CAACtY,CAAC,GAAGmI,EAAE,EAAEmQ,EAAE,CAACzW,CAAC,GAAGiW,EAAE,GAAG3P,EAAE,GAAG,EAAE,EAAEmQ,EAAE,CAACtL,GAAG,GAAG2K,IAAI,CAAC3K,GAAG,EAAEsL,EAAE,CAACxI,IAAI,GAAG6H,IAAI,CAAC7H,IAAI;UACtE,IAAIpI,EAAE,EACF4Q,EAAE,CAACvW,CAAC,CAAC,CAAC;UACV2F,EAAE,GAAG,CAAC;QACV,CAAC,MACI,IAAIA,EAAE,EACPuQ,IAAI,CAAC,CAAC;MACd;IACJ,CAAC;IACD,IAAI,CAAC7B,CAAC,CAACnP,IAAI,CAACqR,EAAE,CAAC;EACnB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIZ,GAAG,CAACvJ,SAAS,CAACtH,GAAG,GAAG,YAAY;IAC5B,IAAI2M,OAAO,GAAG,IAAI;IAClB,IAAI,IAAI,CAACzP,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EACV,MAAM,kBAAkB;MAC5B,MAAM,iBAAiB;IAC3B;IACA,IAAI,IAAI,CAACA,CAAC,EACN,IAAI,CAACnD,CAAC,CAAC,CAAC,CAAC,KAET,IAAI,CAACwV,CAAC,CAACnP,IAAI,CAAC;MACRlF,CAAC,EAAE,SAAAA,CAAA,EAAY;QACX,IAAI,EAAEyR,OAAO,CAACzP,CAAC,GAAG,CAAC,CAAC,EAChB;QACJyP,OAAO,CAAC4C,CAAC,CAACqC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACvBjF,OAAO,CAAC5S,CAAC,CAAC,CAAC;MACf,CAAC;MACDgF,CAAC,EAAE,SAAAA,CAAA,EAAY,CAAE;IACrB,CAAC,CAAC;IACN,IAAI,CAAC7B,CAAC,GAAG,CAAC;EACd,CAAC;EACD2T,GAAG,CAACvJ,SAAS,CAACvN,CAAC,GAAG,YAAY;IAC1B,IAAI0E,EAAE,GAAG,CAAC;MAAExC,CAAC,GAAG,CAAC;MAAEiD,EAAE,GAAG,CAAC;IACzB,KAAK,IAAImS,EAAE,GAAG,CAAC,EAAEjW,EAAE,GAAG,IAAI,CAACmU,CAAC,EAAE8B,EAAE,GAAGjW,EAAE,CAACY,MAAM,EAAEqV,EAAE,EAAE,EAAE;MAChD,IAAI9S,CAAC,GAAGnD,EAAE,CAACiW,EAAE,CAAC;MACdnS,EAAE,IAAI,EAAE,GAAGX,CAAC,CAACA,CAAC,CAACvC,MAAM,GAAGoT,IAAI,CAAC7Q,CAAC,CAACkR,KAAK,CAAC,IAAIlR,CAAC,CAACnB,CAAC,GAAGmB,CAAC,CAACnB,CAAC,CAACpB,MAAM,GAAG,CAAC,CAAC;IAClE;IACA,IAAI6F,GAAG,GAAG,IAAIzH,EAAE,CAAC8E,EAAE,GAAG,EAAE,CAAC;IACzB,KAAK,IAAI3D,EAAE,GAAG,CAAC,EAAEiH,EAAE,GAAG,IAAI,CAAC+M,CAAC,EAAEhU,EAAE,GAAGiH,EAAE,CAACxG,MAAM,EAAET,EAAE,EAAE,EAAE;MAChD,IAAIgD,CAAC,GAAGiE,EAAE,CAACjH,EAAE,CAAC;MACd+T,GAAG,CAACzN,GAAG,EAAEpD,EAAE,EAAEF,CAAC,EAAEA,CAAC,CAACA,CAAC,EAAEA,CAAC,CAACgR,CAAC,EAAEhR,CAAC,CAACpF,CAAC,EAAE8C,CAAC,EAAEsC,CAAC,CAACnB,CAAC,CAAC;MACtCqB,EAAE,IAAI,EAAE,GAAGF,CAAC,CAACA,CAAC,CAACvC,MAAM,GAAGoT,IAAI,CAAC7Q,CAAC,CAACkR,KAAK,CAAC,IAAIlR,CAAC,CAACnB,CAAC,GAAGmB,CAAC,CAACnB,CAAC,CAACpB,MAAM,GAAG,CAAC,CAAC,EAAEC,CAAC,IAAIsC,CAAC,CAACvD,CAAC;IAC5E;IACAwV,GAAG,CAAC3O,GAAG,EAAEpD,EAAE,EAAE,IAAI,CAAC8Q,CAAC,CAACvT,MAAM,EAAEkD,EAAE,EAAEjD,CAAC,CAAC;IAClC,IAAI,CAACwN,MAAM,CAAC,IAAI,EAAE5H,GAAG,EAAE,IAAI,CAAC;IAC5B,IAAI,CAAC3E,CAAC,GAAG,CAAC;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI2T,GAAG,CAACvJ,SAAS,CAAC+B,SAAS,GAAG,YAAY;IAClC,KAAK,IAAIgI,EAAE,GAAG,CAAC,EAAEjW,EAAE,GAAG,IAAI,CAACmU,CAAC,EAAE8B,EAAE,GAAGjW,EAAE,CAACY,MAAM,EAAEqV,EAAE,EAAE,EAAE;MAChD,IAAI9S,CAAC,GAAGnD,EAAE,CAACiW,EAAE,CAAC;MACd9S,CAAC,CAACQ,CAAC,CAAC,CAAC;IACT;IACA,IAAI,CAAC7B,CAAC,GAAG,CAAC;EACd,CAAC;EACD,OAAO2T,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,SAASA,GAAG;AACZ,OAAO,SAASgB,GAAGA,CAAC3X,IAAI,EAAEiP,IAAI,EAAE5P,EAAE,EAAE;EAChC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG4P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO5P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,IAAI2B,CAAC,GAAG,CAAC,CAAC;EACV+R,IAAI,CAAC/S,IAAI,EAAE,EAAE,EAAEgB,CAAC,EAAEiO,IAAI,CAAC;EACvB,IAAIjD,CAAC,GAAG4L,MAAM,CAACC,IAAI,CAAC7W,CAAC,CAAC;EACtB,IAAI8F,GAAG,GAAGkF,CAAC,CAAClK,MAAM;IAAEoB,CAAC,GAAG,CAAC;IAAE4U,GAAG,GAAG,CAAC;EAClC,IAAIC,IAAI,GAAGjR,GAAG;IAAEkR,KAAK,GAAG,IAAI/E,KAAK,CAACnM,GAAG,CAAC;EACtC,IAAImR,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;IACnB,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkX,IAAI,CAACnW,MAAM,EAAE,EAAEf,CAAC,EAChCkX,IAAI,CAAClX,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,IAAIoX,GAAG,GAAG,SAAAA,CAAA,EAAY;IAClB,IAAIxQ,GAAG,GAAG,IAAIzH,EAAE,CAAC4X,GAAG,GAAG,EAAE,CAAC;MAAEM,EAAE,GAAGlV,CAAC;MAAEmV,GAAG,GAAGP,GAAG,GAAG5U,CAAC;IACjD4U,GAAG,GAAG,CAAC;IACP,KAAK,IAAI/W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgX,IAAI,EAAE,EAAEhX,CAAC,EAAE;MAC3B,IAAIsD,CAAC,GAAG2T,KAAK,CAACjX,CAAC,CAAC;MAChB,IAAI;QACA,IAAIgB,CAAC,GAAGsC,CAAC,CAACpF,CAAC,CAAC6C,MAAM;QAClBsT,GAAG,CAACzN,GAAG,EAAEmQ,GAAG,EAAEzT,CAAC,EAAEA,CAAC,CAACA,CAAC,EAAEA,CAAC,CAACgR,CAAC,EAAEtT,CAAC,CAAC;QAC7B,IAAIuW,IAAI,GAAG,EAAE,GAAGjU,CAAC,CAACA,CAAC,CAACvC,MAAM,GAAGoT,IAAI,CAAC7Q,CAAC,CAACkR,KAAK,CAAC;QAC1C,IAAIgD,GAAG,GAAGT,GAAG,GAAGQ,IAAI;QACpB3Q,GAAG,CAACpE,GAAG,CAACc,CAAC,CAACpF,CAAC,EAAEsZ,GAAG,CAAC;QACjBnD,GAAG,CAACzN,GAAG,EAAEzE,CAAC,EAAEmB,CAAC,EAAEA,CAAC,CAACA,CAAC,EAAEA,CAAC,CAACgR,CAAC,EAAEtT,CAAC,EAAE+V,GAAG,EAAEzT,CAAC,CAAC/B,CAAC,CAAC,EAAEY,CAAC,IAAI,EAAE,GAAGoV,IAAI,IAAIjU,CAAC,CAAC/B,CAAC,GAAG+B,CAAC,CAAC/B,CAAC,CAACR,MAAM,GAAG,CAAC,CAAC,EAAEgW,GAAG,GAAGS,GAAG,GAAGxW,CAAC;MACjG,CAAC,CACD,OAAOlC,CAAC,EAAE;QACN,OAAOR,EAAE,CAACQ,CAAC,EAAE,IAAI,CAAC;MACtB;IACJ;IACAyW,GAAG,CAAC3O,GAAG,EAAEzE,CAAC,EAAE8U,KAAK,CAAClW,MAAM,EAAEuW,GAAG,EAAED,EAAE,CAAC;IAClC/Y,EAAE,CAAC,IAAI,EAAEsI,GAAG,CAAC;EACjB,CAAC;EACD,IAAI,CAACb,GAAG,EACJqR,GAAG,CAAC,CAAC;EACT,IAAIK,OAAO,GAAG,SAAAA,CAAUzX,CAAC,EAAE;IACvB,IAAI2L,EAAE,GAAGV,CAAC,CAACjL,CAAC,CAAC;IACb,IAAIG,EAAE,GAAGF,CAAC,CAAC0L,EAAE,CAAC;MAAEkK,IAAI,GAAG1V,EAAE,CAAC,CAAC,CAAC;MAAE+B,CAAC,GAAG/B,EAAE,CAAC,CAAC,CAAC;IACvC,IAAIjC,CAAC,GAAGgN,GAAG,CAAC,CAAC;MAAE8C,IAAI,GAAG6H,IAAI,CAAC9U,MAAM;IACjC7C,CAAC,CAACgE,CAAC,CAAC2T,IAAI,CAAC;IACT,IAAIvS,CAAC,GAAG0P,OAAO,CAACrH,EAAE,CAAC;MAAE7K,CAAC,GAAGwC,CAAC,CAACvC,MAAM;IACjC,IAAI+U,GAAG,GAAG5T,CAAC,CAAC6T,OAAO;MAAExU,CAAC,GAAGuU,GAAG,IAAI9C,OAAO,CAAC8C,GAAG,CAAC;MAAE4B,EAAE,GAAGnW,CAAC,IAAIA,CAAC,CAACR,MAAM;IAChE,IAAI2T,GAAG,GAAGP,IAAI,CAACjS,CAAC,CAACsS,KAAK,CAAC;IACvB,IAAIK,WAAW,GAAG3S,CAAC,CAACoJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC,IAAIqM,GAAG,GAAG,SAAAA,CAAU7Y,CAAC,EAAEmD,CAAC,EAAE;MACtB,IAAInD,CAAC,EAAE;QACHqY,IAAI,CAAC,CAAC;QACN7Y,EAAE,CAACQ,CAAC,EAAE,IAAI,CAAC;MACf,CAAC,MACI;QACD,IAAIkC,CAAC,GAAGiB,CAAC,CAAClB,MAAM;QAChBkW,KAAK,CAACjX,CAAC,CAAC,GAAGyL,GAAG,CAACvJ,CAAC,EAAE;UACd8L,IAAI,EAAEA,IAAI;UACV9C,GAAG,EAAEhN,CAAC,CAAC+D,CAAC,CAAC,CAAC;UACV/D,CAAC,EAAE+D,CAAC;UACJqB,CAAC,EAAEA,CAAC;UACJ/B,CAAC,EAAEA,CAAC;UACJ+S,CAAC,EAAExT,CAAC,IAAI6K,EAAE,CAAC5K,MAAM,IAAKQ,CAAC,IAAKuU,GAAG,CAAC/U,MAAM,IAAI2W,EAAI;UAC9C7C,WAAW,EAAEA;QACjB,CAAC,CAAC;QACF1S,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAG4T,GAAG,GAAG1T,CAAC;QACrB+V,GAAG,IAAI,EAAE,GAAG,CAAC,IAAIjW,CAAC,GAAG4T,GAAG,CAAC,IAAIgD,EAAE,IAAI,CAAC,CAAC,GAAG1W,CAAC;QACzC,IAAI,CAAC,GAAE+E,GAAG,EACNqR,GAAG,CAAC,CAAC;MACb;IACJ,CAAC;IACD,IAAItW,CAAC,GAAG,KAAK,EACT6W,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAClC,IAAI,CAAC9C,WAAW,EACZ8C,GAAG,CAAC,IAAI,EAAE9B,IAAI,CAAC,CAAC,KACf,IAAI7H,IAAI,GAAG,MAAM,EAAE;MACpB,IAAI;QACA2J,GAAG,CAAC,IAAI,EAAEvK,WAAW,CAACyI,IAAI,EAAE3T,CAAC,CAAC,CAAC;MACnC,CAAC,CACD,OAAOpD,CAAC,EAAE;QACN6Y,GAAG,CAAC7Y,CAAC,EAAE,IAAI,CAAC;MAChB;IACJ,CAAC,MAEGoY,IAAI,CAAC/R,IAAI,CAACyK,OAAO,CAACiG,IAAI,EAAE3T,CAAC,EAAEyV,GAAG,CAAC,CAAC;EACxC,CAAC;EACD;EACA,KAAK,IAAI3X,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgX,IAAI,EAAE,EAAEhX,CAAC,EAAE;IAC3ByX,OAAO,CAACzX,CAAC,CAAC;EACd;EACA,OAAOmX,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,OAAOA,CAAC3Y,IAAI,EAAEiP,IAAI,EAAE;EAChC,IAAI,CAACA,IAAI,EACLA,IAAI,GAAG,CAAC,CAAC;EACb,IAAIjO,CAAC,GAAG,CAAC,CAAC;EACV,IAAIgX,KAAK,GAAG,EAAE;EACdjF,IAAI,CAAC/S,IAAI,EAAE,EAAE,EAAEgB,CAAC,EAAEiO,IAAI,CAAC;EACvB,IAAI/L,CAAC,GAAG,CAAC;EACT,IAAI4U,GAAG,GAAG,CAAC;EACX,KAAK,IAAIpL,EAAE,IAAI1L,CAAC,EAAE;IACd,IAAIE,EAAE,GAAGF,CAAC,CAAC0L,EAAE,CAAC;MAAEkK,IAAI,GAAG1V,EAAE,CAAC,CAAC,CAAC;MAAE+B,CAAC,GAAG/B,EAAE,CAAC,CAAC,CAAC;IACvC,IAAI0U,WAAW,GAAG3S,CAAC,CAACoJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC,IAAIhI,CAAC,GAAG0P,OAAO,CAACrH,EAAE,CAAC;MAAE7K,CAAC,GAAGwC,CAAC,CAACvC,MAAM;IACjC,IAAI+U,GAAG,GAAG5T,CAAC,CAAC6T,OAAO;MAAExU,CAAC,GAAGuU,GAAG,IAAI9C,OAAO,CAAC8C,GAAG,CAAC;MAAE4B,EAAE,GAAGnW,CAAC,IAAIA,CAAC,CAACR,MAAM;IAChE,IAAI2T,GAAG,GAAGP,IAAI,CAACjS,CAAC,CAACsS,KAAK,CAAC;IACvB,IAAI1T,CAAC,GAAG,KAAK,EACT,MAAM,mBAAmB;IAC7B,IAAImB,CAAC,GAAG4S,WAAW,GAAGzH,WAAW,CAACyI,IAAI,EAAE3T,CAAC,CAAC,GAAG2T,IAAI;MAAE7U,CAAC,GAAGiB,CAAC,CAAClB,MAAM;IAC/D,IAAI7C,CAAC,GAAGgN,GAAG,CAAC,CAAC;IACbhN,CAAC,CAACgE,CAAC,CAAC2T,IAAI,CAAC;IACToB,KAAK,CAAC9R,IAAI,CAACsG,GAAG,CAACvJ,CAAC,EAAE;MACd8L,IAAI,EAAE6H,IAAI,CAAC9U,MAAM;MACjBmK,GAAG,EAAEhN,CAAC,CAAC+D,CAAC,CAAC,CAAC;MACV/D,CAAC,EAAE+D,CAAC;MACJqB,CAAC,EAAEA,CAAC;MACJ/B,CAAC,EAAEA,CAAC;MACJ+S,CAAC,EAAExT,CAAC,IAAI6K,EAAE,CAAC5K,MAAM,IAAKQ,CAAC,IAAKuU,GAAG,CAAC/U,MAAM,IAAI2W,EAAI;MAC9CvV,CAAC,EAAEA,CAAC;MACJ0S,WAAW,EAAEA;IACjB,CAAC,CAAC,CAAC;IACH1S,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAG4T,GAAG,GAAG1T,CAAC;IACrB+V,GAAG,IAAI,EAAE,GAAG,CAAC,IAAIjW,CAAC,GAAG4T,GAAG,CAAC,IAAIgD,EAAE,IAAI,CAAC,CAAC,GAAG1W,CAAC;EAC7C;EACA,IAAI4F,GAAG,GAAG,IAAIzH,EAAE,CAAC4X,GAAG,GAAG,EAAE,CAAC;IAAEM,EAAE,GAAGlV,CAAC;IAAEmV,GAAG,GAAGP,GAAG,GAAG5U,CAAC;EACjD,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiX,KAAK,CAAClW,MAAM,EAAE,EAAEf,CAAC,EAAE;IACnC,IAAIsD,CAAC,GAAG2T,KAAK,CAACjX,CAAC,CAAC;IAChBqU,GAAG,CAACzN,GAAG,EAAEtD,CAAC,CAACnB,CAAC,EAAEmB,CAAC,EAAEA,CAAC,CAACA,CAAC,EAAEA,CAAC,CAACgR,CAAC,EAAEhR,CAAC,CAACpF,CAAC,CAAC6C,MAAM,CAAC;IACtC,IAAIwW,IAAI,GAAG,EAAE,GAAGjU,CAAC,CAACA,CAAC,CAACvC,MAAM,GAAGoT,IAAI,CAAC7Q,CAAC,CAACkR,KAAK,CAAC;IAC1C5N,GAAG,CAACpE,GAAG,CAACc,CAAC,CAACpF,CAAC,EAAEoF,CAAC,CAACnB,CAAC,GAAGoV,IAAI,CAAC;IACxBlD,GAAG,CAACzN,GAAG,EAAEzE,CAAC,EAAEmB,CAAC,EAAEA,CAAC,CAACA,CAAC,EAAEA,CAAC,CAACgR,CAAC,EAAEhR,CAAC,CAACpF,CAAC,CAAC6C,MAAM,EAAEuC,CAAC,CAACnB,CAAC,EAAEmB,CAAC,CAAC/B,CAAC,CAAC,EAAEY,CAAC,IAAI,EAAE,GAAGoV,IAAI,IAAIjU,CAAC,CAAC/B,CAAC,GAAG+B,CAAC,CAAC/B,CAAC,CAACR,MAAM,GAAG,CAAC,CAAC;EAC3F;EACAwU,GAAG,CAAC3O,GAAG,EAAEzE,CAAC,EAAE8U,KAAK,CAAClW,MAAM,EAAEuW,GAAG,EAAED,EAAE,CAAC;EAClC,OAAOzQ,GAAG;AACd;AACA;AACA;AACA;AACA,IAAIiR,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG,CAC5B;EACAA,gBAAgB,CAACxL,SAAS,CAAClH,IAAI,GAAG,UAAUlG,IAAI,EAAEoE,KAAK,EAAE;IACrD,IAAI,CAACmL,MAAM,CAAC,IAAI,EAAEvP,IAAI,EAAEoE,KAAK,CAAC;EAClC,CAAC;EACDwU,gBAAgB,CAAChD,WAAW,GAAG,CAAC;EAChC,OAAOgD,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,SAASA,gBAAgB;AACzB;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C;AACJ;AACA;EACI,SAASA,YAAYA,CAAA,EAAG;IACpB,IAAIpG,OAAO,GAAG,IAAI;IAClB,IAAI,CAAC1R,CAAC,GAAG,IAAI6P,OAAO,CAAC,UAAUlN,GAAG,EAAEU,KAAK,EAAE;MACvCqO,OAAO,CAAClD,MAAM,CAAC,IAAI,EAAE7L,GAAG,EAAEU,KAAK,CAAC;IACpC,CAAC,CAAC;EACN;EACAyU,YAAY,CAACzL,SAAS,CAAClH,IAAI,GAAG,UAAUlG,IAAI,EAAEoE,KAAK,EAAE;IACjD,IAAI;MACA,IAAI,CAACrD,CAAC,CAACmF,IAAI,CAAClG,IAAI,EAAEoE,KAAK,CAAC;IAC5B,CAAC,CACD,OAAOvE,CAAC,EAAE;MACN,IAAI,CAAC0P,MAAM,CAAC1P,CAAC,EAAEG,IAAI,EAAEoE,KAAK,CAAC;IAC/B;EACJ,CAAC;EACDyU,YAAY,CAACjD,WAAW,GAAG,CAAC;EAC5B,OAAOiD,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C;AACJ;AACA;EACI,SAASA,iBAAiBA,CAACC,CAAC,EAAEC,EAAE,EAAE;IAC9B,IAAIvG,OAAO,GAAG,IAAI;IAClB,IAAIuG,EAAE,GAAG,MAAM,EAAE;MACb,IAAI,CAACjY,CAAC,GAAG,IAAI6P,OAAO,CAAC,UAAUlN,GAAG,EAAEU,KAAK,EAAE;QACvCqO,OAAO,CAAClD,MAAM,CAAC,IAAI,EAAE7L,GAAG,EAAEU,KAAK,CAAC;MACpC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACrD,CAAC,GAAG,IAAI+P,YAAY,CAAC,UAAU5B,GAAG,EAAExL,GAAG,EAAEU,KAAK,EAAE;QACjDqO,OAAO,CAAClD,MAAM,CAACL,GAAG,EAAExL,GAAG,EAAEU,KAAK,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAAC+K,SAAS,GAAG,IAAI,CAACpO,CAAC,CAACoO,SAAS;IACrC;EACJ;EACA2J,iBAAiB,CAAC1L,SAAS,CAAClH,IAAI,GAAG,UAAUlG,IAAI,EAAEoE,KAAK,EAAE;IACtD,IAAI,IAAI,CAACrD,CAAC,CAACoO,SAAS,EAChBnP,IAAI,GAAGqD,GAAG,CAACrD,IAAI,EAAE,CAAC,CAAC;IACvB,IAAI,CAACe,CAAC,CAACmF,IAAI,CAAClG,IAAI,EAAEoE,KAAK,CAAC;EAC5B,CAAC;EACD0U,iBAAiB,CAAClD,WAAW,GAAG,CAAC;EACjC,OAAOkD,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,SAASA,iBAAiB;AAC1B;AACA;AACA;AACA,IAAIG,KAAK,GAAG,aAAe,YAAY;EACnC;AACJ;AACA;AACA;EACI,SAASA,KAAKA,CAAC5Z,EAAE,EAAE;IACf,IAAI,CAAC6Z,MAAM,GAAG7Z,EAAE;IAChB,IAAI,CAAC2M,CAAC,GAAG,EAAE;IACX,IAAI,CAAC9I,CAAC,GAAG;MACL,CAAC,EAAE0V;IACP,CAAC;IACD,IAAI,CAAC3V,CAAC,GAAGoD,EAAE;EACf;EACA;AACJ;AACA;AACA;AACA;EACI4S,KAAK,CAAC7L,SAAS,CAAClH,IAAI,GAAG,UAAUuK,KAAK,EAAErM,KAAK,EAAE;IAC3C,IAAIqO,OAAO,GAAG,IAAI;IAClB,IAAI,CAAC,IAAI,CAACyG,MAAM,EACZ,MAAM,aAAa;IACvB,IAAI,CAAC,IAAI,CAACjW,CAAC,EACP,MAAM,iBAAiB;IAC3B,IAAI,IAAI,CAAChE,CAAC,GAAG,CAAC,EAAE;MACZ,IAAIyK,GAAG,GAAGvF,IAAI,CAACiH,GAAG,CAAC,IAAI,CAACnM,CAAC,EAAEwR,KAAK,CAAC3O,MAAM,CAAC;MACxC,IAAIqX,KAAK,GAAG1I,KAAK,CAACjN,QAAQ,CAAC,CAAC,EAAEkG,GAAG,CAAC;MAClC,IAAI,CAACzK,CAAC,IAAIyK,GAAG;MACb,IAAI,IAAI,CAAC1G,CAAC,EACN,IAAI,CAACA,CAAC,CAACkD,IAAI,CAACiT,KAAK,EAAE,CAAC,IAAI,CAACla,CAAC,CAAC,CAAC,KAE5B,IAAI,CAAC+M,CAAC,CAAC,CAAC,CAAC,CAAC9F,IAAI,CAACiT,KAAK,CAAC;MACzB1I,KAAK,GAAGA,KAAK,CAACjN,QAAQ,CAACkG,GAAG,CAAC;MAC3B,IAAI+G,KAAK,CAAC3O,MAAM,EACZ,OAAO,IAAI,CAACoE,IAAI,CAACuK,KAAK,EAAErM,KAAK,CAAC;IACtC,CAAC,MACI;MACD,IAAIC,CAAC,GAAG,CAAC;QAAEtD,CAAC,GAAG,CAAC;QAAEqY,EAAE,GAAG,KAAK,CAAC;QAAEzV,GAAG,GAAG,KAAK,CAAC;MAC3C,IAAI,CAAC,IAAI,CAACV,CAAC,CAACnB,MAAM,EACd6B,GAAG,GAAG8M,KAAK,CAAC,KACX,IAAI,CAACA,KAAK,CAAC3O,MAAM,EAClB6B,GAAG,GAAG,IAAI,CAACV,CAAC,CAAC,KACZ;QACDU,GAAG,GAAG,IAAIzD,EAAE,CAAC,IAAI,CAAC+C,CAAC,CAACnB,MAAM,GAAG2O,KAAK,CAAC3O,MAAM,CAAC;QAC1C6B,GAAG,CAACJ,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,EAAEU,GAAG,CAACJ,GAAG,CAACkN,KAAK,EAAE,IAAI,CAACxN,CAAC,CAACnB,MAAM,CAAC;MAClD;MACA,IAAIC,CAAC,GAAG4B,GAAG,CAAC7B,MAAM;QAAEuX,EAAE,GAAG,IAAI,CAACpa,CAAC;QAAE2G,GAAG,GAAGyT,EAAE,IAAI,IAAI,CAACrW,CAAC;MACnD,IAAIsW,OAAO,GAAG,SAAAA,CAAA,EAAY;QACtB,IAAIpY,EAAE;QACN,IAAIqY,GAAG,GAAG3J,EAAE,CAACjM,GAAG,EAAE5C,CAAC,CAAC;QACpB,IAAIwY,GAAG,IAAI,SAAS,EAAE;UAClBlV,CAAC,GAAG,CAAC,EAAE+U,EAAE,GAAGrY,CAAC;UACbyY,MAAM,CAACxW,CAAC,GAAG,IAAI;UACfwW,MAAM,CAACva,CAAC,GAAG,CAAC;UACZ,IAAIwa,EAAE,GAAG9J,EAAE,CAAChM,GAAG,EAAE5C,CAAC,GAAG,CAAC,CAAC;YAAE2Y,KAAK,GAAG/J,EAAE,CAAChM,GAAG,EAAE5C,CAAC,GAAG,CAAC,CAAC;YAAEsU,CAAC,GAAGoE,EAAE,GAAG,IAAI;YAAEhC,EAAE,GAAGgC,EAAE,GAAG,CAAC;YAAE7E,GAAG,GAAGjF,EAAE,CAAChM,GAAG,EAAE5C,CAAC,GAAG,EAAE,CAAC;YAAE8T,EAAE,GAAGlF,EAAE,CAAChM,GAAG,EAAE5C,CAAC,GAAG,EAAE,CAAC;UACxH,IAAIgB,CAAC,GAAGhB,CAAC,GAAG,EAAE,GAAG6T,GAAG,GAAGC,EAAE,EAAE;YACvB,IAAI8E,MAAM,GAAG,EAAE;YACfH,MAAM,CAACxN,CAAC,CAAC4N,OAAO,CAACD,MAAM,CAAC;YACxBtV,CAAC,GAAG,CAAC;YACL,IAAIwV,IAAI,GAAGjK,EAAE,CAACjM,GAAG,EAAE5C,CAAC,GAAG,EAAE,CAAC;cAAE+Y,IAAI,GAAGlK,EAAE,CAACjM,GAAG,EAAE5C,CAAC,GAAG,EAAE,CAAC;YAClD,IAAIgZ,IAAI,GAAGzF,SAAS,CAAC3Q,GAAG,CAACH,QAAQ,CAACzC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,GAAG6T,GAAG,CAAC,EAAE,CAACS,CAAC,CAAC;YAC7D,IAAIwE,IAAI,IAAI,UAAU,EAAE;cACpB3Y,EAAE,GAAGuW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG3C,IAAI,CAACnR,GAAG,EAAE5C,CAAC,CAAC,EAAE8Y,IAAI,GAAG3Y,EAAE,CAAC,CAAC,CAAC,EAAE4Y,IAAI,GAAG5Y,EAAE,CAAC,CAAC,CAAC;YAC7D,CAAC,MACI,IAAIuW,EAAE,EACPoC,IAAI,GAAG,CAAC,CAAC;YACb9Y,CAAC,IAAI8T,EAAE;YACP2E,MAAM,CAACva,CAAC,GAAG4a,IAAI;YACf,IAAIG,GAAG;YACP,IAAIC,MAAM,GAAG;cACTC,IAAI,EAAEH,IAAI;cACVnE,WAAW,EAAE8D,KAAK;cAClB7Y,KAAK,EAAE,SAAAA,CAAA,EAAY;gBACf,IAAI,CAACoZ,MAAM,CAAC1K,MAAM,EACd,MAAM,aAAa;gBACvB,IAAI,CAACsK,IAAI,EACLI,MAAM,CAAC1K,MAAM,CAAC,IAAI,EAAElJ,EAAE,EAAE,IAAI,CAAC,CAAC,KAC7B;kBACD,IAAI8T,GAAG,GAAG1H,OAAO,CAACvP,CAAC,CAACwW,KAAK,CAAC;kBAC1B,IAAI,CAACS,GAAG,EACJ,MAAM,2BAA2B,GAAGT,KAAK;kBAC7CM,GAAG,GAAGH,IAAI,GAAG,CAAC,GAAG,IAAIM,GAAG,CAACJ,IAAI,CAAC,GAAG,IAAII,GAAG,CAACJ,IAAI,EAAEF,IAAI,EAAEC,IAAI,CAAC;kBAC1DE,GAAG,CAACzK,MAAM,GAAG,UAAUL,GAAG,EAAExL,GAAG,EAAEU,KAAK,EAAE;oBAAE6V,MAAM,CAAC1K,MAAM,CAACL,GAAG,EAAExL,GAAG,EAAEU,KAAK,CAAC;kBAAE,CAAC;kBAC3E,KAAK,IAAI+S,EAAE,GAAG,CAAC,EAAEiD,MAAM,GAAGT,MAAM,EAAExC,EAAE,GAAGiD,MAAM,CAACtY,MAAM,EAAEqV,EAAE,EAAE,EAAE;oBACxD,IAAIzT,GAAG,GAAG0W,MAAM,CAACjD,EAAE,CAAC;oBACpB6C,GAAG,CAAC9T,IAAI,CAACxC,GAAG,EAAE,KAAK,CAAC;kBACxB;kBACA,IAAI+O,OAAO,CAACzG,CAAC,CAAC,CAAC,CAAC,IAAI2N,MAAM,IAAIlH,OAAO,CAACxT,CAAC,EACnCwT,OAAO,CAACzP,CAAC,GAAGgX,GAAG,CAAC,KAEhBA,GAAG,CAAC9T,IAAI,CAACG,EAAE,EAAE,IAAI,CAAC;gBAC1B;cACJ,CAAC;cACD8I,SAAS,EAAE,SAAAA,CAAA,EAAY;gBACnB,IAAI6K,GAAG,IAAIA,GAAG,CAAC7K,SAAS,EACpB6K,GAAG,CAAC7K,SAAS,CAAC,CAAC;cACvB;YACJ,CAAC;YACD,IAAI0K,IAAI,IAAI,CAAC,EACTI,MAAM,CAAClL,IAAI,GAAG8K,IAAI,EAAEI,MAAM,CAACI,YAAY,GAAGP,IAAI;YAClDN,MAAM,CAACN,MAAM,CAACe,MAAM,CAAC;UACzB;UACA,OAAO,OAAO;QAClB,CAAC,MACI,IAAIZ,EAAE,EAAE;UACT,IAAIE,GAAG,IAAI,SAAS,EAAE;YAClBH,EAAE,GAAGrY,CAAC,IAAI,EAAE,IAAIsY,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEhV,CAAC,GAAG,CAAC,EAAEmV,MAAM,CAACva,CAAC,GAAG,CAAC;YACnD,OAAO,OAAO;UAClB,CAAC,MACI,IAAIsa,GAAG,IAAI,SAAS,EAAE;YACvBH,EAAE,GAAGrY,CAAC,IAAI,CAAC,EAAEsD,CAAC,GAAG,CAAC,EAAEmV,MAAM,CAACva,CAAC,GAAG,CAAC;YAChC,OAAO,OAAO;UAClB;QACJ;MACJ,CAAC;MACD,IAAIua,MAAM,GAAG,IAAI;MACjB,OAAOzY,CAAC,GAAGgB,CAAC,GAAG,CAAC,EAAE,EAAEhB,CAAC,EAAE;QACnB,IAAIuZ,OAAO,GAAGhB,OAAO,CAAC,CAAC;QACvB,IAAIgB,OAAO,KAAK,OAAO,EACnB;MACR;MACA,IAAI,CAACrX,CAAC,GAAGoD,EAAE;MACX,IAAIgT,EAAE,GAAG,CAAC,EAAE;QACR,IAAI3V,GAAG,GAAGW,CAAC,GAAGV,GAAG,CAACH,QAAQ,CAAC,CAAC,EAAE4V,EAAE,GAAG,EAAE,IAAIC,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIzJ,EAAE,CAACjM,GAAG,EAAEyV,EAAE,GAAG,EAAE,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,GAAGzV,GAAG,CAACH,QAAQ,CAAC,CAAC,EAAEzC,CAAC,CAAC;QACpH,IAAI6E,GAAG,EACHA,GAAG,CAACM,IAAI,CAACxC,GAAG,EAAE,CAAC,CAACW,CAAC,CAAC,CAAC,KAEnB,IAAI,CAAC2H,CAAC,CAAC,EAAE3H,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC6B,IAAI,CAACxC,GAAG,CAAC;MACnC;MACA,IAAIW,CAAC,GAAG,CAAC,EACL,OAAO,IAAI,CAAC6B,IAAI,CAACvC,GAAG,CAACH,QAAQ,CAACzC,CAAC,CAAC,EAAEqD,KAAK,CAAC;MAC5C,IAAI,CAACnB,CAAC,GAAGU,GAAG,CAACH,QAAQ,CAACzC,CAAC,CAAC;IAC5B;IACA,IAAIqD,KAAK,EAAE;MACP,IAAI,IAAI,CAACnF,CAAC,EACN,MAAM,kBAAkB;MAC5B,IAAI,CAACgE,CAAC,GAAG,IAAI;IACjB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIgW,KAAK,CAAC7L,SAAS,CAACmN,QAAQ,GAAG,UAAUC,OAAO,EAAE;IAC1C,IAAI,CAACtX,CAAC,CAACsX,OAAO,CAAC5E,WAAW,CAAC,GAAG4E,OAAO;EACzC,CAAC;EACD,OAAOvB,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,SAASA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,KAAKA,CAACza,IAAI,EAAEX,EAAE,EAAE;EAC5B,IAAI,OAAOA,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,IAAI4Y,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;IACnB,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkX,IAAI,CAACnW,MAAM,EAAE,EAAEf,CAAC,EAChCkX,IAAI,CAAClX,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,IAAIiX,KAAK,GAAG,CAAC,CAAC;EACd,IAAInY,CAAC,GAAGG,IAAI,CAAC8B,MAAM,GAAG,EAAE;EACxB,OAAO8N,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EAAE,EAAEA,CAAC,EAAE;IAClC,IAAI,CAACA,CAAC,IAAIG,IAAI,CAAC8B,MAAM,GAAGjC,CAAC,GAAG,KAAK,EAAE;MAC/BR,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC;MAC5B;IACJ;EACJ;EACA;EACA,IAAIyH,GAAG,GAAG6I,EAAE,CAAC3P,IAAI,EAAEH,CAAC,GAAG,CAAC,CAAC;EACzB,IAAI,CAACiH,GAAG,EACJzH,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAChB,IAAIJ,CAAC,GAAG6H,GAAG;EACX,IAAI5D,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB,IAAI8U,CAAC,GAAGzR,CAAC,IAAI,UAAU;EACvB,IAAIyR,CAAC,EAAE;IACH9U,CAAC,GAAG+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpB,IAAI+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EAAE;MAC1BR,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC;MAC5B;IACJ;IACAJ,CAAC,GAAG6H,GAAG,GAAG8I,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IAC1BqD,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB;EACA,IAAI6a,OAAO,GAAG,SAAAA,CAAU3Z,CAAC,EAAE;IACvB,IAAIG,EAAE,GAAGwT,EAAE,CAAC1U,IAAI,EAAEkD,CAAC,EAAEyR,CAAC,CAAC;MAAEgG,GAAG,GAAGzZ,EAAE,CAAC,CAAC,CAAC;MAAE6T,EAAE,GAAG7T,EAAE,CAAC,CAAC,CAAC;MAAE8T,EAAE,GAAG9T,EAAE,CAAC,CAAC,CAAC;MAAEwL,EAAE,GAAGxL,EAAE,CAAC,CAAC,CAAC;MAAE0Z,EAAE,GAAG1Z,EAAE,CAAC,CAAC,CAAC;MAAE+T,GAAG,GAAG/T,EAAE,CAAC,CAAC,CAAC;MAAEJ,CAAC,GAAG2T,IAAI,CAACzU,IAAI,EAAEiV,GAAG,CAAC;IACtH/R,CAAC,GAAG0X,EAAE;IACN,IAAIlC,GAAG,GAAG,SAAAA,CAAU7Y,CAAC,EAAEmD,CAAC,EAAE;MACtB,IAAInD,CAAC,EAAE;QACHqY,IAAI,CAAC,CAAC;QACN7Y,EAAE,CAACQ,CAAC,EAAE,IAAI,CAAC;MACf,CAAC,MACI;QACDmY,KAAK,CAACtL,EAAE,CAAC,GAAG1J,CAAC;QACb,IAAI,CAAC,GAAE8D,GAAG,EACNzH,EAAE,CAAC,IAAI,EAAE2Y,KAAK,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAAC2C,GAAG,EACJjC,GAAG,CAAC,IAAI,EAAErV,GAAG,CAACrD,IAAI,EAAEc,CAAC,EAAEA,CAAC,GAAGiU,EAAE,CAAC,CAAC,CAAC,KAC/B,IAAI4F,GAAG,IAAI,CAAC,EAAE;MACf,IAAIE,IAAI,GAAG7a,IAAI,CAACwD,QAAQ,CAAC1C,CAAC,EAAEA,CAAC,GAAGiU,EAAE,CAAC;MACnC,IAAIA,EAAE,GAAG,MAAM,EAAE;QACb,IAAI;UACA2D,GAAG,CAAC,IAAI,EAAE3K,WAAW,CAAC8M,IAAI,EAAE,IAAI3a,EAAE,CAAC8U,EAAE,CAAC,CAAC,CAAC;QAC5C,CAAC,CACD,OAAOnV,CAAC,EAAE;UACN6Y,GAAG,CAAC7Y,CAAC,EAAE,IAAI,CAAC;QAChB;MACJ,CAAC,MAEGoY,IAAI,CAAC/R,IAAI,CAAC6K,OAAO,CAAC8J,IAAI,EAAE;QAAE9L,IAAI,EAAEiG;MAAG,CAAC,EAAE0D,GAAG,CAAC,CAAC;IACnD,CAAC,MAEGA,GAAG,CAAC,2BAA2B,GAAGiC,GAAG,EAAE,IAAI,CAAC;EACpD,CAAC;EACD,KAAK,IAAI5Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,CAAC,EAAE,EAAE8B,CAAC,EAAE;IACxB2Z,OAAO,CAAC3Z,CAAC,CAAC;EACd;EACA,OAAOmX,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4C,SAASA,CAAC9a,IAAI,EAAE;EAC5B,IAAIgY,KAAK,GAAG,CAAC,CAAC;EACd,IAAInY,CAAC,GAAGG,IAAI,CAAC8B,MAAM,GAAG,EAAE;EACxB,OAAO8N,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EAAE,EAAEA,CAAC,EAAE;IAClC,IAAI,CAACA,CAAC,IAAIG,IAAI,CAAC8B,MAAM,GAAGjC,CAAC,GAAG,KAAK,EAC7B,MAAM,kBAAkB;EAChC;EACA;EACA,IAAIZ,CAAC,GAAG0Q,EAAE,CAAC3P,IAAI,EAAEH,CAAC,GAAG,CAAC,CAAC;EACvB,IAAI,CAACZ,CAAC,EACF,OAAO,CAAC,CAAC;EACb,IAAIiE,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB,IAAI8U,CAAC,GAAGzR,CAAC,IAAI,UAAU;EACvB,IAAIyR,CAAC,EAAE;IACH9U,CAAC,GAAG+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpB,IAAI+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EACxB,MAAM,kBAAkB;IAC5BZ,CAAC,GAAG2Q,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpBqD,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,CAAC,EAAE,EAAE8B,CAAC,EAAE;IACxB,IAAIG,EAAE,GAAGwT,EAAE,CAAC1U,IAAI,EAAEkD,CAAC,EAAEyR,CAAC,CAAC;MAAEoG,GAAG,GAAG7Z,EAAE,CAAC,CAAC,CAAC;MAAE6T,EAAE,GAAG7T,EAAE,CAAC,CAAC,CAAC;MAAE8T,EAAE,GAAG9T,EAAE,CAAC,CAAC,CAAC;MAAEwL,EAAE,GAAGxL,EAAE,CAAC,CAAC,CAAC;MAAE0Z,EAAE,GAAG1Z,EAAE,CAAC,CAAC,CAAC;MAAE+T,GAAG,GAAG/T,EAAE,CAAC,CAAC,CAAC;MAAEJ,CAAC,GAAG2T,IAAI,CAACzU,IAAI,EAAEiV,GAAG,CAAC;IACtH/R,CAAC,GAAG0X,EAAE;IACN,IAAI,CAACG,GAAG,EACJ/C,KAAK,CAACtL,EAAE,CAAC,GAAGrJ,GAAG,CAACrD,IAAI,EAAEc,CAAC,EAAEA,CAAC,GAAGiU,EAAE,CAAC,CAAC,KAChC,IAAIgG,GAAG,IAAI,CAAC,EACb/C,KAAK,CAACtL,EAAE,CAAC,GAAGqB,WAAW,CAAC/N,IAAI,CAACwD,QAAQ,CAAC1C,CAAC,EAAEA,CAAC,GAAGiU,EAAE,CAAC,EAAE,IAAI7U,EAAE,CAAC8U,EAAE,CAAC,CAAC,CAAC,KAE9D,MAAM,2BAA2B,GAAG+F,GAAG;EAC/C;EACA,OAAO/C,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}