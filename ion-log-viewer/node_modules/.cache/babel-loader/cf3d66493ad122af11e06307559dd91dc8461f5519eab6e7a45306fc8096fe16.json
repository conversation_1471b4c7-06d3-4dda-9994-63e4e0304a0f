{"ast": null, "code": "/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction(...funcs) {\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction(...args) {\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}", "map": {"version": 3, "names": ["createChainedFunction", "funcs", "reduce", "acc", "func", "chainedFunction", "args", "apply"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@mui/utils/esm/createChainedFunction/createChainedFunction.js"], "sourcesContent": ["/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction(...funcs) {\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction(...args) {\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAAC,GAAGC,KAAK,EAAE;EACtD,OAAOA,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IACjC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAOD,GAAG;IACZ;IACA,OAAO,SAASE,eAAeA,CAAC,GAAGC,IAAI,EAAE;MACvCH,GAAG,CAACI,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;MACrBF,IAAI,CAACG,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}