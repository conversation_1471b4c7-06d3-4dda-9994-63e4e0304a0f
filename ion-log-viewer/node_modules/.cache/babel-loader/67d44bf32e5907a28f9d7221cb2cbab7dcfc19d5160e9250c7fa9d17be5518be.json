{"ast": null, "code": "import { Object3D, Box3, <PERSON><PERSON><PERSON><PERSON>, MeshLambertMaterial, Mesh, TextureLoader, UVMapping } from \"three\";\nimport { MD2Loader } from \"../loaders/MD2Loader.js\";\nclass MD2Character {\n  constructor() {\n    this.scale = 1;\n    this.animationFPS = 6;\n    this.root = new Object3D();\n    this.meshBody = null;\n    this.meshWeapon = null;\n    this.skinsBody = [];\n    this.skinsWeapon = [];\n    this.weapons = [];\n    this.activeAnimation = null;\n    this.mixer = null;\n    this.onLoadComplete = function () {};\n    this.loadCounter = 0;\n  }\n  loadParts(config) {\n    const scope = this;\n    function createPart(geometry, skinMap) {\n      const materialWireframe = new MeshLambertMaterial({\n        color: 16755200,\n        wireframe: true,\n        morphTargets: true,\n        morphNormals: true\n      });\n      const materialTexture = new MeshLambertMaterial({\n        color: 16777215,\n        wireframe: false,\n        map: skinMap,\n        morphTargets: true,\n        morphNormals: true\n      });\n      const mesh = new Mesh(geometry, materialTexture);\n      mesh.rotation.y = -Math.PI / 2;\n      mesh.castShadow = true;\n      mesh.receiveShadow = true;\n      mesh.materialTexture = materialTexture;\n      mesh.materialWireframe = materialWireframe;\n      return mesh;\n    }\n    function loadTextures(baseUrl, textureUrls) {\n      const textureLoader = new TextureLoader();\n      const textures = [];\n      for (let i = 0; i < textureUrls.length; i++) {\n        textures[i] = textureLoader.load(baseUrl + textureUrls[i], checkLoadingComplete);\n        textures[i].mapping = UVMapping;\n        textures[i].name = textureUrls[i];\n        if (\"colorSpace\" in textures[i]) textures[i].colorSpace = \"srgb\";else textures[i].encoding = 3001;\n      }\n      return textures;\n    }\n    function checkLoadingComplete() {\n      scope.loadCounter -= 1;\n      if (scope.loadCounter === 0) scope.onLoadComplete();\n    }\n    this.loadCounter = config.weapons.length * 2 + config.skins.length + 1;\n    const weaponsTextures = [];\n    for (let i = 0; i < config.weapons.length; i++) weaponsTextures[i] = config.weapons[i][1];\n    this.skinsBody = loadTextures(config.baseUrl + \"skins/\", config.skins);\n    this.skinsWeapon = loadTextures(config.baseUrl + \"skins/\", weaponsTextures);\n    const loader = new MD2Loader();\n    loader.load(config.baseUrl + config.body, function (geo) {\n      const boundingBox = new Box3();\n      boundingBox.setFromBufferAttribute(geo.attributes.position);\n      scope.root.position.y = -scope.scale * boundingBox.min.y;\n      const mesh = createPart(geo, scope.skinsBody[0]);\n      mesh.scale.set(scope.scale, scope.scale, scope.scale);\n      scope.root.add(mesh);\n      scope.meshBody = mesh;\n      scope.meshBody.clipOffset = 0;\n      scope.activeAnimationClipName = mesh.geometry.animations[0].name;\n      scope.mixer = new AnimationMixer(mesh);\n      checkLoadingComplete();\n    });\n    const generateCallback = function (index, name) {\n      return function (geo) {\n        const mesh = createPart(geo, scope.skinsWeapon[index]);\n        mesh.scale.set(scope.scale, scope.scale, scope.scale);\n        mesh.visible = false;\n        mesh.name = name;\n        scope.root.add(mesh);\n        scope.weapons[index] = mesh;\n        scope.meshWeapon = mesh;\n        checkLoadingComplete();\n      };\n    };\n    for (let i = 0; i < config.weapons.length; i++) {\n      loader.load(config.baseUrl + config.weapons[i][0], generateCallback(i, config.weapons[i][0]));\n    }\n  }\n  setPlaybackRate(rate) {\n    if (rate !== 0) {\n      this.mixer.timeScale = 1 / rate;\n    } else {\n      this.mixer.timeScale = 0;\n    }\n  }\n  setWireframe(wireframeEnabled) {\n    if (wireframeEnabled) {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialWireframe;\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialWireframe;\n    } else {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialTexture;\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialTexture;\n    }\n  }\n  setSkin(index) {\n    if (this.meshBody && this.meshBody.material.wireframe === false) {\n      this.meshBody.material.map = this.skinsBody[index];\n    }\n  }\n  setWeapon(index) {\n    for (let i = 0; i < this.weapons.length; i++) this.weapons[i].visible = false;\n    const activeWeapon = this.weapons[index];\n    if (activeWeapon) {\n      activeWeapon.visible = true;\n      this.meshWeapon = activeWeapon;\n      this.syncWeaponAnimation();\n    }\n  }\n  setAnimation(clipName) {\n    if (this.meshBody) {\n      if (this.meshBody.activeAction) {\n        this.meshBody.activeAction.stop();\n        this.meshBody.activeAction = null;\n      }\n      const action = this.mixer.clipAction(clipName, this.meshBody);\n      if (action) {\n        this.meshBody.activeAction = action.play();\n      }\n    }\n    this.activeClipName = clipName;\n    this.syncWeaponAnimation();\n  }\n  syncWeaponAnimation() {\n    const clipName = this.activeClipName;\n    if (this.meshWeapon) {\n      if (this.meshWeapon.activeAction) {\n        this.meshWeapon.activeAction.stop();\n        this.meshWeapon.activeAction = null;\n      }\n      const action = this.mixer.clipAction(clipName, this.meshWeapon);\n      if (action) {\n        this.meshWeapon.activeAction = action.syncWith(this.meshBody.activeAction).play();\n      }\n    }\n  }\n  update(delta) {\n    if (this.mixer) this.mixer.update(delta);\n  }\n}\nexport { MD2Character };", "map": {"version": 3, "names": ["MD2Character", "constructor", "scale", "animationFPS", "root", "Object3D", "meshBody", "meshWeapon", "skinsBody", "skinsWeapon", "weapons", "activeAnimation", "mixer", "onLoadComplete", "loadCounter", "loadParts", "config", "scope", "createPart", "geometry", "skinMap", "materialWireframe", "MeshLambertMaterial", "color", "wireframe", "morphTargets", "morphNormals", "materialTexture", "map", "mesh", "<PERSON><PERSON>", "rotation", "y", "Math", "PI", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "loadTextures", "baseUrl", "textureUrls", "textureLoader", "TextureLoader", "textures", "i", "length", "load", "checkLoadingComplete", "mapping", "UVMapping", "name", "colorSpace", "encoding", "skins", "weaponsTextures", "loader", "MD2Loader", "body", "geo", "boundingBox", "Box3", "setFromBufferAttribute", "attributes", "position", "min", "set", "add", "clipOffset", "activeAnimationClipName", "animations", "AnimationMixer", "generateCallback", "index", "visible", "setPlaybackRate", "rate", "timeScale", "setWireframe", "wireframeEnabled", "material", "<PERSON><PERSON><PERSON>", "setWeapon", "activeWeapon", "syncWeaponAnimation", "setAnimation", "clipName", "activeAction", "stop", "action", "clipAction", "play", "activeClipName", "syncWith", "update", "delta"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/misc/MD2Character.js"], "sourcesContent": ["import { AnimationMixer, Box3, <PERSON><PERSON>, MeshLambertMaterial, Object3D, TextureLoader, UVMapping } from 'three'\nimport { MD2Loader } from '../loaders/MD2Loader'\n\nclass MD2Character {\n  constructor() {\n    this.scale = 1\n    this.animationFPS = 6\n\n    this.root = new Object3D()\n\n    this.meshBody = null\n    this.meshWeapon = null\n\n    this.skinsBody = []\n    this.skinsWeapon = []\n\n    this.weapons = []\n\n    this.activeAnimation = null\n\n    this.mixer = null\n\n    this.onLoadComplete = function () {}\n\n    this.loadCounter = 0\n  }\n\n  loadParts(config) {\n    const scope = this\n\n    function createPart(geometry, skinMap) {\n      const materialWireframe = new MeshLambertMaterial({\n        color: 0xffaa00,\n        wireframe: true,\n        morphTargets: true,\n        morphNormals: true,\n      })\n      const materialTexture = new MeshLambertMaterial({\n        color: 0xffffff,\n        wireframe: false,\n        map: skinMap,\n        morphTargets: true,\n        morphNormals: true,\n      })\n\n      //\n\n      const mesh = new Mesh(geometry, materialTexture)\n      mesh.rotation.y = -Math.PI / 2\n\n      mesh.castShadow = true\n      mesh.receiveShadow = true\n\n      //\n\n      mesh.materialTexture = materialTexture\n      mesh.materialWireframe = materialWireframe\n\n      return mesh\n    }\n\n    function loadTextures(baseUrl, textureUrls) {\n      const textureLoader = new TextureLoader()\n      const textures = []\n\n      for (let i = 0; i < textureUrls.length; i++) {\n        textures[i] = textureLoader.load(baseUrl + textureUrls[i], checkLoadingComplete)\n        textures[i].mapping = UVMapping\n        textures[i].name = textureUrls[i]\n        if ('colorSpace' in textures[i]) textures[i].colorSpace = 'srgb'\n        else textures[i].encoding = 3001 // sRGBEncoding\n      }\n\n      return textures\n    }\n\n    function checkLoadingComplete() {\n      scope.loadCounter -= 1\n\n      if (scope.loadCounter === 0) scope.onLoadComplete()\n    }\n\n    this.loadCounter = config.weapons.length * 2 + config.skins.length + 1\n\n    const weaponsTextures = []\n    for (let i = 0; i < config.weapons.length; i++) weaponsTextures[i] = config.weapons[i][1]\n    // SKINS\n\n    this.skinsBody = loadTextures(config.baseUrl + 'skins/', config.skins)\n    this.skinsWeapon = loadTextures(config.baseUrl + 'skins/', weaponsTextures)\n\n    // BODY\n\n    const loader = new MD2Loader()\n\n    loader.load(config.baseUrl + config.body, function (geo) {\n      const boundingBox = new Box3()\n      boundingBox.setFromBufferAttribute(geo.attributes.position)\n\n      scope.root.position.y = -scope.scale * boundingBox.min.y\n\n      const mesh = createPart(geo, scope.skinsBody[0])\n      mesh.scale.set(scope.scale, scope.scale, scope.scale)\n\n      scope.root.add(mesh)\n\n      scope.meshBody = mesh\n\n      scope.meshBody.clipOffset = 0\n      scope.activeAnimationClipName = mesh.geometry.animations[0].name\n\n      scope.mixer = new AnimationMixer(mesh)\n\n      checkLoadingComplete()\n    })\n\n    // WEAPONS\n\n    const generateCallback = function (index, name) {\n      return function (geo) {\n        const mesh = createPart(geo, scope.skinsWeapon[index])\n        mesh.scale.set(scope.scale, scope.scale, scope.scale)\n        mesh.visible = false\n\n        mesh.name = name\n\n        scope.root.add(mesh)\n\n        scope.weapons[index] = mesh\n        scope.meshWeapon = mesh\n\n        checkLoadingComplete()\n      }\n    }\n\n    for (let i = 0; i < config.weapons.length; i++) {\n      loader.load(config.baseUrl + config.weapons[i][0], generateCallback(i, config.weapons[i][0]))\n    }\n  }\n\n  setPlaybackRate(rate) {\n    if (rate !== 0) {\n      this.mixer.timeScale = 1 / rate\n    } else {\n      this.mixer.timeScale = 0\n    }\n  }\n\n  setWireframe(wireframeEnabled) {\n    if (wireframeEnabled) {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialWireframe\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialWireframe\n    } else {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialTexture\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialTexture\n    }\n  }\n\n  setSkin(index) {\n    if (this.meshBody && this.meshBody.material.wireframe === false) {\n      this.meshBody.material.map = this.skinsBody[index]\n    }\n  }\n\n  setWeapon(index) {\n    for (let i = 0; i < this.weapons.length; i++) this.weapons[i].visible = false\n\n    const activeWeapon = this.weapons[index]\n\n    if (activeWeapon) {\n      activeWeapon.visible = true\n      this.meshWeapon = activeWeapon\n\n      this.syncWeaponAnimation()\n    }\n  }\n\n  setAnimation(clipName) {\n    if (this.meshBody) {\n      if (this.meshBody.activeAction) {\n        this.meshBody.activeAction.stop()\n        this.meshBody.activeAction = null\n      }\n\n      const action = this.mixer.clipAction(clipName, this.meshBody)\n\n      if (action) {\n        this.meshBody.activeAction = action.play()\n      }\n    }\n\n    this.activeClipName = clipName\n\n    this.syncWeaponAnimation()\n  }\n\n  syncWeaponAnimation() {\n    const clipName = this.activeClipName\n\n    if (this.meshWeapon) {\n      if (this.meshWeapon.activeAction) {\n        this.meshWeapon.activeAction.stop()\n        this.meshWeapon.activeAction = null\n      }\n\n      const action = this.mixer.clipAction(clipName, this.meshWeapon)\n\n      if (action) {\n        this.meshWeapon.activeAction = action.syncWith(this.meshBody.activeAction).play()\n      }\n    }\n  }\n\n  update(delta) {\n    if (this.mixer) this.mixer.update(delta)\n  }\n}\n\nexport { MD2Character }\n"], "mappings": ";;AAGA,MAAMA,YAAA,CAAa;EACjBC,YAAA,EAAc;IACZ,KAAKC,KAAA,GAAQ;IACb,KAAKC,YAAA,GAAe;IAEpB,KAAKC,IAAA,GAAO,IAAIC,QAAA,CAAU;IAE1B,KAAKC,QAAA,GAAW;IAChB,KAAKC,UAAA,GAAa;IAElB,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKC,WAAA,GAAc,EAAE;IAErB,KAAKC,OAAA,GAAU,EAAE;IAEjB,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,KAAA,GAAQ;IAEb,KAAKC,cAAA,GAAiB,YAAY,CAAE;IAEpC,KAAKC,WAAA,GAAc;EACpB;EAEDC,UAAUC,MAAA,EAAQ;IAChB,MAAMC,KAAA,GAAQ;IAEd,SAASC,WAAWC,QAAA,EAAUC,OAAA,EAAS;MACrC,MAAMC,iBAAA,GAAoB,IAAIC,mBAAA,CAAoB;QAChDC,KAAA,EAAO;QACPC,SAAA,EAAW;QACXC,YAAA,EAAc;QACdC,YAAA,EAAc;MACtB,CAAO;MACD,MAAMC,eAAA,GAAkB,IAAIL,mBAAA,CAAoB;QAC9CC,KAAA,EAAO;QACPC,SAAA,EAAW;QACXI,GAAA,EAAKR,OAAA;QACLK,YAAA,EAAc;QACdC,YAAA,EAAc;MACtB,CAAO;MAID,MAAMG,IAAA,GAAO,IAAIC,IAAA,CAAKX,QAAA,EAAUQ,eAAe;MAC/CE,IAAA,CAAKE,QAAA,CAASC,CAAA,GAAI,CAACC,IAAA,CAAKC,EAAA,GAAK;MAE7BL,IAAA,CAAKM,UAAA,GAAa;MAClBN,IAAA,CAAKO,aAAA,GAAgB;MAIrBP,IAAA,CAAKF,eAAA,GAAkBA,eAAA;MACvBE,IAAA,CAAKR,iBAAA,GAAoBA,iBAAA;MAEzB,OAAOQ,IAAA;IACR;IAED,SAASQ,aAAaC,OAAA,EAASC,WAAA,EAAa;MAC1C,MAAMC,aAAA,GAAgB,IAAIC,aAAA,CAAe;MACzC,MAAMC,QAAA,GAAW,EAAE;MAEnB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,WAAA,CAAYK,MAAA,EAAQD,CAAA,IAAK;QAC3CD,QAAA,CAASC,CAAC,IAAIH,aAAA,CAAcK,IAAA,CAAKP,OAAA,GAAUC,WAAA,CAAYI,CAAC,GAAGG,oBAAoB;QAC/EJ,QAAA,CAASC,CAAC,EAAEI,OAAA,GAAUC,SAAA;QACtBN,QAAA,CAASC,CAAC,EAAEM,IAAA,GAAOV,WAAA,CAAYI,CAAC;QAChC,IAAI,gBAAgBD,QAAA,CAASC,CAAC,GAAGD,QAAA,CAASC,CAAC,EAAEO,UAAA,GAAa,YACrDR,QAAA,CAASC,CAAC,EAAEQ,QAAA,GAAW;MAC7B;MAED,OAAOT,QAAA;IACR;IAED,SAASI,qBAAA,EAAuB;MAC9B7B,KAAA,CAAMH,WAAA,IAAe;MAErB,IAAIG,KAAA,CAAMH,WAAA,KAAgB,GAAGG,KAAA,CAAMJ,cAAA,CAAgB;IACpD;IAED,KAAKC,WAAA,GAAcE,MAAA,CAAON,OAAA,CAAQkC,MAAA,GAAS,IAAI5B,MAAA,CAAOoC,KAAA,CAAMR,MAAA,GAAS;IAErE,MAAMS,eAAA,GAAkB,EAAE;IAC1B,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAI3B,MAAA,CAAON,OAAA,CAAQkC,MAAA,EAAQD,CAAA,IAAKU,eAAA,CAAgBV,CAAC,IAAI3B,MAAA,CAAON,OAAA,CAAQiC,CAAC,EAAE,CAAC;IAGxF,KAAKnC,SAAA,GAAY6B,YAAA,CAAarB,MAAA,CAAOsB,OAAA,GAAU,UAAUtB,MAAA,CAAOoC,KAAK;IACrE,KAAK3C,WAAA,GAAc4B,YAAA,CAAarB,MAAA,CAAOsB,OAAA,GAAU,UAAUe,eAAe;IAI1E,MAAMC,MAAA,GAAS,IAAIC,SAAA,CAAW;IAE9BD,MAAA,CAAOT,IAAA,CAAK7B,MAAA,CAAOsB,OAAA,GAAUtB,MAAA,CAAOwC,IAAA,EAAM,UAAUC,GAAA,EAAK;MACvD,MAAMC,WAAA,GAAc,IAAIC,IAAA,CAAM;MAC9BD,WAAA,CAAYE,sBAAA,CAAuBH,GAAA,CAAII,UAAA,CAAWC,QAAQ;MAE1D7C,KAAA,CAAMb,IAAA,CAAK0D,QAAA,CAAS9B,CAAA,GAAI,CAACf,KAAA,CAAMf,KAAA,GAAQwD,WAAA,CAAYK,GAAA,CAAI/B,CAAA;MAEvD,MAAMH,IAAA,GAAOX,UAAA,CAAWuC,GAAA,EAAKxC,KAAA,CAAMT,SAAA,CAAU,CAAC,CAAC;MAC/CqB,IAAA,CAAK3B,KAAA,CAAM8D,GAAA,CAAI/C,KAAA,CAAMf,KAAA,EAAOe,KAAA,CAAMf,KAAA,EAAOe,KAAA,CAAMf,KAAK;MAEpDe,KAAA,CAAMb,IAAA,CAAK6D,GAAA,CAAIpC,IAAI;MAEnBZ,KAAA,CAAMX,QAAA,GAAWuB,IAAA;MAEjBZ,KAAA,CAAMX,QAAA,CAAS4D,UAAA,GAAa;MAC5BjD,KAAA,CAAMkD,uBAAA,GAA0BtC,IAAA,CAAKV,QAAA,CAASiD,UAAA,CAAW,CAAC,EAAEnB,IAAA;MAE5DhC,KAAA,CAAML,KAAA,GAAQ,IAAIyD,cAAA,CAAexC,IAAI;MAErCiB,oBAAA,CAAsB;IAC5B,CAAK;IAID,MAAMwB,gBAAA,GAAmB,SAAAA,CAAUC,KAAA,EAAOtB,IAAA,EAAM;MAC9C,OAAO,UAAUQ,GAAA,EAAK;QACpB,MAAM5B,IAAA,GAAOX,UAAA,CAAWuC,GAAA,EAAKxC,KAAA,CAAMR,WAAA,CAAY8D,KAAK,CAAC;QACrD1C,IAAA,CAAK3B,KAAA,CAAM8D,GAAA,CAAI/C,KAAA,CAAMf,KAAA,EAAOe,KAAA,CAAMf,KAAA,EAAOe,KAAA,CAAMf,KAAK;QACpD2B,IAAA,CAAK2C,OAAA,GAAU;QAEf3C,IAAA,CAAKoB,IAAA,GAAOA,IAAA;QAEZhC,KAAA,CAAMb,IAAA,CAAK6D,GAAA,CAAIpC,IAAI;QAEnBZ,KAAA,CAAMP,OAAA,CAAQ6D,KAAK,IAAI1C,IAAA;QACvBZ,KAAA,CAAMV,UAAA,GAAasB,IAAA;QAEnBiB,oBAAA,CAAsB;MACvB;IACF;IAED,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAI3B,MAAA,CAAON,OAAA,CAAQkC,MAAA,EAAQD,CAAA,IAAK;MAC9CW,MAAA,CAAOT,IAAA,CAAK7B,MAAA,CAAOsB,OAAA,GAAUtB,MAAA,CAAON,OAAA,CAAQiC,CAAC,EAAE,CAAC,GAAG2B,gBAAA,CAAiB3B,CAAA,EAAG3B,MAAA,CAAON,OAAA,CAAQiC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7F;EACF;EAED8B,gBAAgBC,IAAA,EAAM;IACpB,IAAIA,IAAA,KAAS,GAAG;MACd,KAAK9D,KAAA,CAAM+D,SAAA,GAAY,IAAID,IAAA;IACjC,OAAW;MACL,KAAK9D,KAAA,CAAM+D,SAAA,GAAY;IACxB;EACF;EAEDC,aAAaC,gBAAA,EAAkB;IAC7B,IAAIA,gBAAA,EAAkB;MACpB,IAAI,KAAKvE,QAAA,EAAU,KAAKA,QAAA,CAASwE,QAAA,GAAW,KAAKxE,QAAA,CAASe,iBAAA;MAC1D,IAAI,KAAKd,UAAA,EAAY,KAAKA,UAAA,CAAWuE,QAAA,GAAW,KAAKvE,UAAA,CAAWc,iBAAA;IACtE,OAAW;MACL,IAAI,KAAKf,QAAA,EAAU,KAAKA,QAAA,CAASwE,QAAA,GAAW,KAAKxE,QAAA,CAASqB,eAAA;MAC1D,IAAI,KAAKpB,UAAA,EAAY,KAAKA,UAAA,CAAWuE,QAAA,GAAW,KAAKvE,UAAA,CAAWoB,eAAA;IACjE;EACF;EAEDoD,QAAQR,KAAA,EAAO;IACb,IAAI,KAAKjE,QAAA,IAAY,KAAKA,QAAA,CAASwE,QAAA,CAAStD,SAAA,KAAc,OAAO;MAC/D,KAAKlB,QAAA,CAASwE,QAAA,CAASlD,GAAA,GAAM,KAAKpB,SAAA,CAAU+D,KAAK;IAClD;EACF;EAEDS,UAAUT,KAAA,EAAO;IACf,SAAS5B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKjC,OAAA,CAAQkC,MAAA,EAAQD,CAAA,IAAK,KAAKjC,OAAA,CAAQiC,CAAC,EAAE6B,OAAA,GAAU;IAExE,MAAMS,YAAA,GAAe,KAAKvE,OAAA,CAAQ6D,KAAK;IAEvC,IAAIU,YAAA,EAAc;MAChBA,YAAA,CAAaT,OAAA,GAAU;MACvB,KAAKjE,UAAA,GAAa0E,YAAA;MAElB,KAAKC,mBAAA,CAAqB;IAC3B;EACF;EAEDC,aAAaC,QAAA,EAAU;IACrB,IAAI,KAAK9E,QAAA,EAAU;MACjB,IAAI,KAAKA,QAAA,CAAS+E,YAAA,EAAc;QAC9B,KAAK/E,QAAA,CAAS+E,YAAA,CAAaC,IAAA,CAAM;QACjC,KAAKhF,QAAA,CAAS+E,YAAA,GAAe;MAC9B;MAED,MAAME,MAAA,GAAS,KAAK3E,KAAA,CAAM4E,UAAA,CAAWJ,QAAA,EAAU,KAAK9E,QAAQ;MAE5D,IAAIiF,MAAA,EAAQ;QACV,KAAKjF,QAAA,CAAS+E,YAAA,GAAeE,MAAA,CAAOE,IAAA,CAAM;MAC3C;IACF;IAED,KAAKC,cAAA,GAAiBN,QAAA;IAEtB,KAAKF,mBAAA,CAAqB;EAC3B;EAEDA,oBAAA,EAAsB;IACpB,MAAME,QAAA,GAAW,KAAKM,cAAA;IAEtB,IAAI,KAAKnF,UAAA,EAAY;MACnB,IAAI,KAAKA,UAAA,CAAW8E,YAAA,EAAc;QAChC,KAAK9E,UAAA,CAAW8E,YAAA,CAAaC,IAAA,CAAM;QACnC,KAAK/E,UAAA,CAAW8E,YAAA,GAAe;MAChC;MAED,MAAME,MAAA,GAAS,KAAK3E,KAAA,CAAM4E,UAAA,CAAWJ,QAAA,EAAU,KAAK7E,UAAU;MAE9D,IAAIgF,MAAA,EAAQ;QACV,KAAKhF,UAAA,CAAW8E,YAAA,GAAeE,MAAA,CAAOI,QAAA,CAAS,KAAKrF,QAAA,CAAS+E,YAAY,EAAEI,IAAA,CAAM;MAClF;IACF;EACF;EAEDG,OAAOC,KAAA,EAAO;IACZ,IAAI,KAAKjF,KAAA,EAAO,KAAKA,KAAA,CAAMgF,MAAA,CAAOC,KAAK;EACxC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}