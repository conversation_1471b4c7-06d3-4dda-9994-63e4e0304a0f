{"ast": null, "code": "import { Mesh, IcosahedronGeometry, ShaderMaterial, DoubleSide } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nconst isCubeTexture = def => def && def.isCubeTexture;\nclass GroundProjectedEnv extends Mesh {\n  constructor(texture, options) {\n    var _a, _b;\n    const isCubeMap = isCubeTexture(texture);\n    const w = (_b = isCubeMap ? (_a = texture.image[0]) == null ? void 0 : _a.width : texture.image.width) != null ? _b : 1024;\n    const cubeSize = w / 4;\n    const _lodMax = Math.floor(Math.log2(cubeSize));\n    const _cubeSize = Math.pow(2, _lodMax);\n    const width = 3 * Math.max(_cubeSize, 16 * 7);\n    const height = 4 * _cubeSize;\n    const defines = [isCubeMap ? \"#define ENVMAP_TYPE_CUBE\" : \"\", `#define CUBEUV_TEXEL_WIDTH ${1 / width}`, `#define CUBEUV_TEXEL_HEIGHT ${1 / height}`, `#define CUBEUV_MAX_MIP ${_lodMax}.0`];\n    const vertexShader = /* glsl */\n    `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `;\n    const fragmentShader = defines.join(\"\\n\") + /* glsl */\n    `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n        }\n        `;\n    const uniforms = {\n      map: {\n        value: texture\n      },\n      height: {\n        value: (options == null ? void 0 : options.height) || 15\n      },\n      radius: {\n        value: (options == null ? void 0 : options.radius) || 100\n      }\n    };\n    const geometry = new IcosahedronGeometry(1, 16);\n    const material = new ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: DoubleSide\n    });\n    super(geometry, material);\n  }\n  set radius(radius) {\n    this.material.uniforms.radius.value = radius;\n  }\n  get radius() {\n    return this.material.uniforms.radius.value;\n  }\n  set height(height) {\n    this.material.uniforms.height.value = height;\n  }\n  get height() {\n    return this.material.uniforms.height.value;\n  }\n}\nexport { GroundProjectedEnv };", "map": {"version": 3, "names": ["isCubeTexture", "def", "GroundProjectedEnv", "<PERSON><PERSON>", "constructor", "texture", "options", "isCubeMap", "w", "_b", "_a", "image", "width", "cubeSize", "_lodMax", "Math", "floor", "log2", "_cubeSize", "pow", "max", "height", "defines", "vertexShader", "fragmentShader", "join", "version", "uniforms", "map", "value", "radius", "geometry", "IcosahedronGeometry", "material", "ShaderMaterial", "side", "DoubleSide"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/objects/GroundProjectedEnv.ts"], "sourcesContent": ["import { Mesh, IcosahedronGeometry, ShaderMaterial, DoubleSide, Texture, CubeTexture, BufferGeometry } from 'three'\nimport { version } from '../_polyfill/constants'\n\nexport interface GroundProjectedEnvParameters {\n  height?: number\n  radius?: number\n}\n\nconst isCubeTexture = (def: CubeTexture | Texture): def is CubeTexture => def && (def as CubeTexture).isCubeTexture\n\nexport class GroundProjectedEnv extends Mesh<BufferGeometry, ShaderMaterial> {\n  constructor(texture: CubeTexture | Texture, options?: GroundProjectedEnvParameters) {\n    const isCubeMap = isCubeTexture(texture)\n    const w = (isCubeMap ? texture.image[0]?.width : texture.image.width) ?? 1024\n    const cubeSize = w / 4\n    const _lodMax = Math.floor(Math.log2(cubeSize))\n    const _cubeSize = Math.pow(2, _lodMax)\n    const width = 3 * Math.max(_cubeSize, 16 * 7)\n    const height = 4 * _cubeSize\n\n    const defines = [\n      isCubeMap ? '#define ENVMAP_TYPE_CUBE' : '',\n      `#define CUBEUV_TEXEL_WIDTH ${1.0 / width}`,\n      `#define CUBEUV_TEXEL_HEIGHT ${1.0 / height}`,\n      `#define CUBEUV_MAX_MIP ${_lodMax}.0`,\n    ]\n\n    const vertexShader = /* glsl */ `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `\n    const fragmentShader =\n      defines.join('\\n') +\n      /* glsl */ `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n        `\n\n    const uniforms = {\n      map: { value: texture },\n      height: { value: options?.height || 15 },\n      radius: { value: options?.radius || 100 },\n    }\n\n    const geometry = new IcosahedronGeometry(1, 16)\n    const material = new ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: DoubleSide,\n    })\n\n    super(geometry, material)\n  }\n\n  set radius(radius: number) {\n    this.material.uniforms.radius.value = radius\n  }\n\n  get radius(): number {\n    return this.material.uniforms.radius.value\n  }\n\n  set height(height: number) {\n    this.material.uniforms.height.value = height\n  }\n\n  get height(): number {\n    return this.material.uniforms.height.value\n  }\n}\n"], "mappings": ";;AAQA,MAAMA,aAAA,GAAiBC,GAAA,IAAmDA,GAAA,IAAQA,GAAA,CAAoBD,aAAA;AAE/F,MAAME,kBAAA,SAA2BC,IAAA,CAAqC;EAC3EC,YAAYC,OAAA,EAAgCC,OAAA,EAAwC;;IAC5E,MAAAC,SAAA,GAAYP,aAAA,CAAcK,OAAO;IACjC,MAAAG,CAAA,IAAKC,EAAA,GAAAF,SAAA,IAAYG,EAAA,GAAAL,OAAA,CAAQM,KAAA,CAAM,CAAC,MAAf,gBAAAD,EAAA,CAAkBE,KAAA,GAAQP,OAAA,CAAQM,KAAA,CAAMC,KAAA,KAApD,OAAAH,EAAA,GAA8D;IACzE,MAAMI,QAAA,GAAWL,CAAA,GAAI;IACrB,MAAMM,OAAA,GAAUC,IAAA,CAAKC,KAAA,CAAMD,IAAA,CAAKE,IAAA,CAAKJ,QAAQ,CAAC;IAC9C,MAAMK,SAAA,GAAYH,IAAA,CAAKI,GAAA,CAAI,GAAGL,OAAO;IACrC,MAAMF,KAAA,GAAQ,IAAIG,IAAA,CAAKK,GAAA,CAAIF,SAAA,EAAW,KAAK,CAAC;IAC5C,MAAMG,MAAA,GAAS,IAAIH,SAAA;IAEnB,MAAMI,OAAA,GAAU,CACdf,SAAA,GAAY,6BAA6B,IACzC,8BAA8B,IAAMK,KAAA,IACpC,+BAA+B,IAAMS,MAAA,IACrC,0BAA0BP,OAAA;IAGtB,MAAAS,YAAA;IAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAU1B,MAAAC,cAAA,GACJF,OAAA,CAAQG,IAAA,CAAK,IAAI;IACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAqEOC,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;IAI7D,MAAMC,QAAA,GAAW;MACfC,GAAA,EAAK;QAAEC,KAAA,EAAOxB;MAAQ;MACtBgB,MAAA,EAAQ;QAAEQ,KAAA,GAAOvB,OAAA,oBAAAA,OAAA,CAASe,MAAA,KAAU;MAAG;MACvCS,MAAA,EAAQ;QAAED,KAAA,GAAOvB,OAAA,oBAAAA,OAAA,CAASwB,MAAA,KAAU;MAAI;IAAA;IAG1C,MAAMC,QAAA,GAAW,IAAIC,mBAAA,CAAoB,GAAG,EAAE;IACxC,MAAAC,QAAA,GAAW,IAAIC,cAAA,CAAe;MAClCP,QAAA;MACAH,cAAA;MACAD,YAAA;MACAY,IAAA,EAAMC;IAAA,CACP;IAED,MAAML,QAAA,EAAUE,QAAQ;EAC1B;EAEA,IAAIH,OAAOA,MAAA,EAAgB;IACpB,KAAAG,QAAA,CAASN,QAAA,CAASG,MAAA,CAAOD,KAAA,GAAQC,MAAA;EACxC;EAEA,IAAIA,OAAA,EAAiB;IACZ,YAAKG,QAAA,CAASN,QAAA,CAASG,MAAA,CAAOD,KAAA;EACvC;EAEA,IAAIR,OAAOA,MAAA,EAAgB;IACpB,KAAAY,QAAA,CAASN,QAAA,CAASN,MAAA,CAAOQ,KAAA,GAAQR,MAAA;EACxC;EAEA,IAAIA,OAAA,EAAiB;IACZ,YAAKY,QAAA,CAASN,QAAA,CAASN,MAAA,CAAOQ,KAAA;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}