{"ast": null, "code": "import { <PERSON><PERSON>, FileLoader, BufferGeometry, BufferAttribute, Vector3, Float32BufferAttribute } from \"three\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass STLLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    function isBinary(data2) {\n      const reader = new DataView(data2);\n      const face_size = 32 / 8 * 3 + 32 / 8 * 3 * 3 + 16 / 8;\n      const n_faces = reader.getUint32(80, true);\n      const expect = 80 + 32 / 8 + n_faces * face_size;\n      if (expect === reader.byteLength) {\n        return true;\n      }\n      const solid = [115, 111, 108, 105, 100];\n      for (let off = 0; off < 5; off++) {\n        if (matchDataViewAt(solid, reader, off)) return false;\n      }\n      return true;\n    }\n    function matchDataViewAt(query, reader, offset) {\n      for (let i = 0, il = query.length; i < il; i++) {\n        if (query[i] !== reader.getUint8(offset + i, false)) return false;\n      }\n      return true;\n    }\n    function parseBinary(data2) {\n      const reader = new DataView(data2);\n      const faces = reader.getUint32(80, true);\n      let r,\n        g,\n        b,\n        hasColors = false,\n        colors;\n      let defaultR, defaultG, defaultB, alpha;\n      for (let index = 0; index < 80 - 10; index++) {\n        if (reader.getUint32(index, false) == 1129270351 && reader.getUint8(index + 4) == 82 && reader.getUint8(index + 5) == 61) {\n          hasColors = true;\n          colors = new Float32Array(faces * 3 * 3);\n          defaultR = reader.getUint8(index + 6) / 255;\n          defaultG = reader.getUint8(index + 7) / 255;\n          defaultB = reader.getUint8(index + 8) / 255;\n          alpha = reader.getUint8(index + 9) / 255;\n        }\n      }\n      const dataOffset = 84;\n      const faceLength = 12 * 4 + 2;\n      const geometry = new BufferGeometry();\n      const vertices = new Float32Array(faces * 3 * 3);\n      const normals = new Float32Array(faces * 3 * 3);\n      for (let face = 0; face < faces; face++) {\n        const start = dataOffset + face * faceLength;\n        const normalX = reader.getFloat32(start, true);\n        const normalY = reader.getFloat32(start + 4, true);\n        const normalZ = reader.getFloat32(start + 8, true);\n        if (hasColors) {\n          const packedColor = reader.getUint16(start + 48, true);\n          if ((packedColor & 32768) === 0) {\n            r = (packedColor & 31) / 31;\n            g = (packedColor >> 5 & 31) / 31;\n            b = (packedColor >> 10 & 31) / 31;\n          } else {\n            r = defaultR;\n            g = defaultG;\n            b = defaultB;\n          }\n        }\n        for (let i = 1; i <= 3; i++) {\n          const vertexstart = start + i * 12;\n          const componentIdx = face * 3 * 3 + (i - 1) * 3;\n          vertices[componentIdx] = reader.getFloat32(vertexstart, true);\n          vertices[componentIdx + 1] = reader.getFloat32(vertexstart + 4, true);\n          vertices[componentIdx + 2] = reader.getFloat32(vertexstart + 8, true);\n          normals[componentIdx] = normalX;\n          normals[componentIdx + 1] = normalY;\n          normals[componentIdx + 2] = normalZ;\n          if (hasColors) {\n            colors[componentIdx] = r;\n            colors[componentIdx + 1] = g;\n            colors[componentIdx + 2] = b;\n          }\n        }\n      }\n      geometry.setAttribute(\"position\", new BufferAttribute(vertices, 3));\n      geometry.setAttribute(\"normal\", new BufferAttribute(normals, 3));\n      if (hasColors) {\n        geometry.setAttribute(\"color\", new BufferAttribute(colors, 3));\n        geometry.hasColors = true;\n        geometry.alpha = alpha;\n      }\n      return geometry;\n    }\n    function parseASCII(data2) {\n      const geometry = new BufferGeometry();\n      const patternSolid = /solid([\\s\\S]*?)endsolid/g;\n      const patternFace = /facet([\\s\\S]*?)endfacet/g;\n      let faceCounter = 0;\n      const patternFloat = /[\\s]+([+-]?(?:\\d*)(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)/.source;\n      const patternVertex = new RegExp(\"vertex\" + patternFloat + patternFloat + patternFloat, \"g\");\n      const patternNormal = new RegExp(\"normal\" + patternFloat + patternFloat + patternFloat, \"g\");\n      const vertices = [];\n      const normals = [];\n      const normal = new Vector3();\n      let result;\n      let groupCount = 0;\n      let startVertex = 0;\n      let endVertex = 0;\n      while ((result = patternSolid.exec(data2)) !== null) {\n        startVertex = endVertex;\n        const solid = result[0];\n        while ((result = patternFace.exec(solid)) !== null) {\n          let vertexCountPerFace = 0;\n          let normalCountPerFace = 0;\n          const text = result[0];\n          while ((result = patternNormal.exec(text)) !== null) {\n            normal.x = parseFloat(result[1]);\n            normal.y = parseFloat(result[2]);\n            normal.z = parseFloat(result[3]);\n            normalCountPerFace++;\n          }\n          while ((result = patternVertex.exec(text)) !== null) {\n            vertices.push(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3]));\n            normals.push(normal.x, normal.y, normal.z);\n            vertexCountPerFace++;\n            endVertex++;\n          }\n          if (normalCountPerFace !== 1) {\n            console.error(\"THREE.STLLoader: Something isn't right with the normal of face number \" + faceCounter);\n          }\n          if (vertexCountPerFace !== 3) {\n            console.error(\"THREE.STLLoader: Something isn't right with the vertices of face number \" + faceCounter);\n          }\n          faceCounter++;\n        }\n        const start = startVertex;\n        const count = endVertex - startVertex;\n        geometry.addGroup(start, count, groupCount);\n        groupCount++;\n      }\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n      geometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n      return geometry;\n    }\n    function ensureString(buffer) {\n      if (typeof buffer !== \"string\") {\n        return decodeText(new Uint8Array(buffer));\n      }\n      return buffer;\n    }\n    function ensureBinary(buffer) {\n      if (typeof buffer === \"string\") {\n        const array_buffer = new Uint8Array(buffer.length);\n        for (let i = 0; i < buffer.length; i++) {\n          array_buffer[i] = buffer.charCodeAt(i) & 255;\n        }\n        return array_buffer.buffer || array_buffer;\n      } else {\n        return buffer;\n      }\n    }\n    const binData = ensureBinary(data);\n    return isBinary(binData) ? parseBinary(binData) : parseASCII(ensureString(data));\n  }\n}\nexport { STLLoader };", "map": {"version": 3, "names": ["STLLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "isBinary", "data2", "reader", "DataView", "face_size", "n_faces", "getUint32", "expect", "byteLength", "solid", "off", "matchDataViewAt", "query", "offset", "i", "il", "length", "getUint8", "parseBinary", "faces", "r", "g", "b", "hasColors", "colors", "defaultR", "defaultG", "defaultB", "alpha", "index", "Float32Array", "dataOffset", "face<PERSON><PERSON><PERSON>", "geometry", "BufferGeometry", "vertices", "normals", "face", "start", "normalX", "getFloat32", "normalY", "normalZ", "packedColor", "getUint16", "vertexstart", "componentIdx", "setAttribute", "BufferAttribute", "parseASCII", "patternSolid", "patternFace", "faceCounter", "patternFloat", "source", "patternVertex", "RegExp", "patternNormal", "normal", "Vector3", "result", "groupCount", "startVertex", "endVertex", "exec", "vertexCountPerFace", "normalCountPerFace", "x", "parseFloat", "y", "z", "push", "count", "addGroup", "Float32BufferAttribute", "ensureString", "buffer", "decodeText", "Uint8Array", "ensureBinary", "array_buffer", "charCodeAt", "binData"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/STLLoader.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  Loader,\n  LoaderUtils,\n  Vector3,\n} from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n * Description: A THREE loader for STL ASCII files, as created by Solidworks and other CAD programs.\n *\n * Supports both binary and ASCII encoded files, with automatic detection of type.\n *\n * The loader returns a non-indexed buffer geometry.\n *\n * Limitations:\n *  Binary decoding supports \"Magics\" color format (http://en.wikipedia.org/wiki/STL_(file_format)#Color_in_binary_STL).\n *  There is perhaps some question as to how valid it is to always assume little-endian-ness.\n *  ASCII decoding assumes file is UTF-8.\n *\n * Usage:\n *  const loader = new STLLoader();\n *  loader.load( './models/stl/slotted_disk.stl', function ( geometry ) {\n *    scene.add( new THREE.Mesh( geometry ) );\n *  });\n *\n * For binary STLs geometry might contain colors for vertices. To use it:\n *  // use the same code to load STL as above\n *  if (geometry.hasColors) {\n *    material = new THREE.MeshPhongMaterial({ opacity: geometry.alpha, vertexColors: true });\n *  } else { .... }\n *  const mesh = new THREE.Mesh( geometry, material );\n *\n * For ASCII STLs containing multiple solids, each solid is assigned to a different group.\n * Groups can be used to assign a different color by defining an array of materials with the same length of\n * geometry.groups and passing it to the Mesh constructor:\n *\n * const mesh = new THREE.Mesh( geometry, material );\n *\n * For example:\n *\n *  const materials = [];\n *  const nGeometryGroups = geometry.groups.length;\n *\n *  const colorMap = ...; // Some logic to index colors.\n *\n *  for (let i = 0; i < nGeometryGroups; i++) {\n *\n *\t\tconst material = new THREE.MeshPhongMaterial({\n *\t\t\tcolor: colorMap[i],\n *\t\t\twireframe: false\n *\t\t});\n *\n *  }\n *\n *  materials.push(material);\n *  const mesh = new THREE.Mesh(geometry, materials);\n */\n\nclass STLLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function isBinary(data) {\n      const reader = new DataView(data)\n      const face_size = (32 / 8) * 3 + (32 / 8) * 3 * 3 + 16 / 8\n      const n_faces = reader.getUint32(80, true)\n      const expect = 80 + 32 / 8 + n_faces * face_size\n\n      if (expect === reader.byteLength) {\n        return true\n      }\n\n      // An ASCII STL data must begin with 'solid ' as the first six bytes.\n      // However, ASCII STLs lacking the SPACE after the 'd' are known to be\n      // plentiful.  So, check the first 5 bytes for 'solid'.\n\n      // Several encodings, such as UTF-8, precede the text with up to 5 bytes:\n      // https://en.wikipedia.org/wiki/Byte_order_mark#Byte_order_marks_by_encoding\n      // Search for \"solid\" to start anywhere after those prefixes.\n\n      // US-ASCII ordinal values for 's', 'o', 'l', 'i', 'd'\n\n      const solid = [115, 111, 108, 105, 100]\n\n      for (let off = 0; off < 5; off++) {\n        // If \"solid\" text is matched to the current offset, declare it to be an ASCII STL.\n\n        if (matchDataViewAt(solid, reader, off)) return false\n      }\n\n      // Couldn't find \"solid\" text at the beginning; it is binary STL.\n\n      return true\n    }\n\n    function matchDataViewAt(query, reader, offset) {\n      // Check if each byte in query matches the corresponding byte from the current offset\n\n      for (let i = 0, il = query.length; i < il; i++) {\n        if (query[i] !== reader.getUint8(offset + i, false)) return false\n      }\n\n      return true\n    }\n\n    function parseBinary(data) {\n      const reader = new DataView(data)\n      const faces = reader.getUint32(80, true)\n\n      let r,\n        g,\n        b,\n        hasColors = false,\n        colors\n      let defaultR, defaultG, defaultB, alpha\n\n      // process STL header\n      // check for default color in header (\"COLOR=rgba\" sequence).\n\n      for (let index = 0; index < 80 - 10; index++) {\n        if (\n          reader.getUint32(index, false) == 0x434f4c4f /*COLO*/ &&\n          reader.getUint8(index + 4) == 0x52 /*'R'*/ &&\n          reader.getUint8(index + 5) == 0x3d /*'='*/\n        ) {\n          hasColors = true\n          colors = new Float32Array(faces * 3 * 3)\n\n          defaultR = reader.getUint8(index + 6) / 255\n          defaultG = reader.getUint8(index + 7) / 255\n          defaultB = reader.getUint8(index + 8) / 255\n          alpha = reader.getUint8(index + 9) / 255\n        }\n      }\n\n      const dataOffset = 84\n      const faceLength = 12 * 4 + 2\n\n      const geometry = new BufferGeometry()\n\n      const vertices = new Float32Array(faces * 3 * 3)\n      const normals = new Float32Array(faces * 3 * 3)\n\n      for (let face = 0; face < faces; face++) {\n        const start = dataOffset + face * faceLength\n        const normalX = reader.getFloat32(start, true)\n        const normalY = reader.getFloat32(start + 4, true)\n        const normalZ = reader.getFloat32(start + 8, true)\n\n        if (hasColors) {\n          const packedColor = reader.getUint16(start + 48, true)\n\n          if ((packedColor & 0x8000) === 0) {\n            // facet has its own unique color\n\n            r = (packedColor & 0x1f) / 31\n            g = ((packedColor >> 5) & 0x1f) / 31\n            b = ((packedColor >> 10) & 0x1f) / 31\n          } else {\n            r = defaultR\n            g = defaultG\n            b = defaultB\n          }\n        }\n\n        for (let i = 1; i <= 3; i++) {\n          const vertexstart = start + i * 12\n          const componentIdx = face * 3 * 3 + (i - 1) * 3\n\n          vertices[componentIdx] = reader.getFloat32(vertexstart, true)\n          vertices[componentIdx + 1] = reader.getFloat32(vertexstart + 4, true)\n          vertices[componentIdx + 2] = reader.getFloat32(vertexstart + 8, true)\n\n          normals[componentIdx] = normalX\n          normals[componentIdx + 1] = normalY\n          normals[componentIdx + 2] = normalZ\n\n          if (hasColors) {\n            colors[componentIdx] = r\n            colors[componentIdx + 1] = g\n            colors[componentIdx + 2] = b\n          }\n        }\n      }\n\n      geometry.setAttribute('position', new BufferAttribute(vertices, 3))\n      geometry.setAttribute('normal', new BufferAttribute(normals, 3))\n\n      if (hasColors) {\n        geometry.setAttribute('color', new BufferAttribute(colors, 3))\n        geometry.hasColors = true\n        geometry.alpha = alpha\n      }\n\n      return geometry\n    }\n\n    function parseASCII(data) {\n      const geometry = new BufferGeometry()\n      const patternSolid = /solid([\\s\\S]*?)endsolid/g\n      const patternFace = /facet([\\s\\S]*?)endfacet/g\n      let faceCounter = 0\n\n      const patternFloat = /[\\s]+([+-]?(?:\\d*)(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)/.source\n      const patternVertex = new RegExp('vertex' + patternFloat + patternFloat + patternFloat, 'g')\n      const patternNormal = new RegExp('normal' + patternFloat + patternFloat + patternFloat, 'g')\n\n      const vertices = []\n      const normals = []\n\n      const normal = new Vector3()\n\n      let result\n\n      let groupCount = 0\n      let startVertex = 0\n      let endVertex = 0\n\n      while ((result = patternSolid.exec(data)) !== null) {\n        startVertex = endVertex\n\n        const solid = result[0]\n\n        while ((result = patternFace.exec(solid)) !== null) {\n          let vertexCountPerFace = 0\n          let normalCountPerFace = 0\n\n          const text = result[0]\n\n          while ((result = patternNormal.exec(text)) !== null) {\n            normal.x = parseFloat(result[1])\n            normal.y = parseFloat(result[2])\n            normal.z = parseFloat(result[3])\n            normalCountPerFace++\n          }\n\n          while ((result = patternVertex.exec(text)) !== null) {\n            vertices.push(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3]))\n            normals.push(normal.x, normal.y, normal.z)\n            vertexCountPerFace++\n            endVertex++\n          }\n\n          // every face have to own ONE valid normal\n\n          if (normalCountPerFace !== 1) {\n            console.error(\"THREE.STLLoader: Something isn't right with the normal of face number \" + faceCounter)\n          }\n\n          // each face have to own THREE valid vertices\n\n          if (vertexCountPerFace !== 3) {\n            console.error(\"THREE.STLLoader: Something isn't right with the vertices of face number \" + faceCounter)\n          }\n\n          faceCounter++\n        }\n\n        const start = startVertex\n        const count = endVertex - startVertex\n\n        geometry.addGroup(start, count, groupCount)\n        groupCount++\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n      geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n\n      return geometry\n    }\n\n    function ensureString(buffer) {\n      if (typeof buffer !== 'string') {\n        return decodeText(new Uint8Array(buffer))\n      }\n\n      return buffer\n    }\n\n    function ensureBinary(buffer) {\n      if (typeof buffer === 'string') {\n        const array_buffer = new Uint8Array(buffer.length)\n        for (let i = 0; i < buffer.length; i++) {\n          array_buffer[i] = buffer.charCodeAt(i) & 0xff // implicitly assumes little-endian\n        }\n\n        return array_buffer.buffer || array_buffer\n      } else {\n        return buffer\n      }\n    }\n\n    // start\n\n    const binData = ensureBinary(data)\n\n    return isBinary(binData) ? parseBinary(binData) : parseASCII(ensureString(data))\n  }\n}\n\nexport { STLLoader }\n"], "mappings": ";;AA8DA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKR,OAAO;IAC1CO,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;IACxBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiB,KAAKC,aAAa;IAC1CN,MAAA,CAAOO,kBAAA,CAAmB,KAAKC,eAAe;IAE9CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,IAAA,EAAM;IACV,SAASC,SAASC,KAAA,EAAM;MACtB,MAAMC,MAAA,GAAS,IAAIC,QAAA,CAASF,KAAI;MAChC,MAAMG,SAAA,GAAa,KAAK,IAAK,IAAK,KAAK,IAAK,IAAI,IAAI,KAAK;MACzD,MAAMC,OAAA,GAAUH,MAAA,CAAOI,SAAA,CAAU,IAAI,IAAI;MACzC,MAAMC,MAAA,GAAS,KAAK,KAAK,IAAIF,OAAA,GAAUD,SAAA;MAEvC,IAAIG,MAAA,KAAWL,MAAA,CAAOM,UAAA,EAAY;QAChC,OAAO;MACR;MAYD,MAAMC,KAAA,GAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;MAEtC,SAASC,GAAA,GAAM,GAAGA,GAAA,GAAM,GAAGA,GAAA,IAAO;QAGhC,IAAIC,eAAA,CAAgBF,KAAA,EAAOP,MAAA,EAAQQ,GAAG,GAAG,OAAO;MACjD;MAID,OAAO;IACR;IAED,SAASC,gBAAgBC,KAAA,EAAOV,MAAA,EAAQW,MAAA,EAAQ;MAG9C,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKH,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9C,IAAIF,KAAA,CAAME,CAAC,MAAMZ,MAAA,CAAOe,QAAA,CAASJ,MAAA,GAASC,CAAA,EAAG,KAAK,GAAG,OAAO;MAC7D;MAED,OAAO;IACR;IAED,SAASI,YAAYjB,KAAA,EAAM;MACzB,MAAMC,MAAA,GAAS,IAAIC,QAAA,CAASF,KAAI;MAChC,MAAMkB,KAAA,GAAQjB,MAAA,CAAOI,SAAA,CAAU,IAAI,IAAI;MAEvC,IAAIc,CAAA;QACFC,CAAA;QACAC,CAAA;QACAC,SAAA,GAAY;QACZC,MAAA;MACF,IAAIC,QAAA,EAAUC,QAAA,EAAUC,QAAA,EAAUC,KAAA;MAKlC,SAASC,KAAA,GAAQ,GAAGA,KAAA,GAAQ,KAAK,IAAIA,KAAA,IAAS;QAC5C,IACE3B,MAAA,CAAOI,SAAA,CAAUuB,KAAA,EAAO,KAAK,KAAK,cAClC3B,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,KAAK,MAC9B3B,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,KAAK,IAC9B;UACAN,SAAA,GAAY;UACZC,MAAA,GAAS,IAAIM,YAAA,CAAaX,KAAA,GAAQ,IAAI,CAAC;UAEvCM,QAAA,GAAWvB,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,IAAI;UACxCH,QAAA,GAAWxB,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,IAAI;UACxCF,QAAA,GAAWzB,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,IAAI;UACxCD,KAAA,GAAQ1B,MAAA,CAAOe,QAAA,CAASY,KAAA,GAAQ,CAAC,IAAI;QACtC;MACF;MAED,MAAME,UAAA,GAAa;MACnB,MAAMC,UAAA,GAAa,KAAK,IAAI;MAE5B,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;MAErC,MAAMC,QAAA,GAAW,IAAIL,YAAA,CAAaX,KAAA,GAAQ,IAAI,CAAC;MAC/C,MAAMiB,OAAA,GAAU,IAAIN,YAAA,CAAaX,KAAA,GAAQ,IAAI,CAAC;MAE9C,SAASkB,IAAA,GAAO,GAAGA,IAAA,GAAOlB,KAAA,EAAOkB,IAAA,IAAQ;QACvC,MAAMC,KAAA,GAAQP,UAAA,GAAaM,IAAA,GAAOL,UAAA;QAClC,MAAMO,OAAA,GAAUrC,MAAA,CAAOsC,UAAA,CAAWF,KAAA,EAAO,IAAI;QAC7C,MAAMG,OAAA,GAAUvC,MAAA,CAAOsC,UAAA,CAAWF,KAAA,GAAQ,GAAG,IAAI;QACjD,MAAMI,OAAA,GAAUxC,MAAA,CAAOsC,UAAA,CAAWF,KAAA,GAAQ,GAAG,IAAI;QAEjD,IAAIf,SAAA,EAAW;UACb,MAAMoB,WAAA,GAAczC,MAAA,CAAO0C,SAAA,CAAUN,KAAA,GAAQ,IAAI,IAAI;UAErD,KAAKK,WAAA,GAAc,WAAY,GAAG;YAGhCvB,CAAA,IAAKuB,WAAA,GAAc,MAAQ;YAC3BtB,CAAA,IAAMsB,WAAA,IAAe,IAAK,MAAQ;YAClCrB,CAAA,IAAMqB,WAAA,IAAe,KAAM,MAAQ;UAC/C,OAAiB;YACLvB,CAAA,GAAIK,QAAA;YACJJ,CAAA,GAAIK,QAAA;YACJJ,CAAA,GAAIK,QAAA;UACL;QACF;QAED,SAASb,CAAA,GAAI,GAAGA,CAAA,IAAK,GAAGA,CAAA,IAAK;UAC3B,MAAM+B,WAAA,GAAcP,KAAA,GAAQxB,CAAA,GAAI;UAChC,MAAMgC,YAAA,GAAeT,IAAA,GAAO,IAAI,KAAKvB,CAAA,GAAI,KAAK;UAE9CqB,QAAA,CAASW,YAAY,IAAI5C,MAAA,CAAOsC,UAAA,CAAWK,WAAA,EAAa,IAAI;UAC5DV,QAAA,CAASW,YAAA,GAAe,CAAC,IAAI5C,MAAA,CAAOsC,UAAA,CAAWK,WAAA,GAAc,GAAG,IAAI;UACpEV,QAAA,CAASW,YAAA,GAAe,CAAC,IAAI5C,MAAA,CAAOsC,UAAA,CAAWK,WAAA,GAAc,GAAG,IAAI;UAEpET,OAAA,CAAQU,YAAY,IAAIP,OAAA;UACxBH,OAAA,CAAQU,YAAA,GAAe,CAAC,IAAIL,OAAA;UAC5BL,OAAA,CAAQU,YAAA,GAAe,CAAC,IAAIJ,OAAA;UAE5B,IAAInB,SAAA,EAAW;YACbC,MAAA,CAAOsB,YAAY,IAAI1B,CAAA;YACvBI,MAAA,CAAOsB,YAAA,GAAe,CAAC,IAAIzB,CAAA;YAC3BG,MAAA,CAAOsB,YAAA,GAAe,CAAC,IAAIxB,CAAA;UAC5B;QACF;MACF;MAEDW,QAAA,CAASc,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBb,QAAA,EAAU,CAAC,CAAC;MAClEF,QAAA,CAASc,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBZ,OAAA,EAAS,CAAC,CAAC;MAE/D,IAAIb,SAAA,EAAW;QACbU,QAAA,CAASc,YAAA,CAAa,SAAS,IAAIC,eAAA,CAAgBxB,MAAA,EAAQ,CAAC,CAAC;QAC7DS,QAAA,CAASV,SAAA,GAAY;QACrBU,QAAA,CAASL,KAAA,GAAQA,KAAA;MAClB;MAED,OAAOK,QAAA;IACR;IAED,SAASgB,WAAWhD,KAAA,EAAM;MACxB,MAAMgC,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACrC,MAAMgB,YAAA,GAAe;MACrB,MAAMC,WAAA,GAAc;MACpB,IAAIC,WAAA,GAAc;MAElB,MAAMC,YAAA,GAAe,iDAAiDC,MAAA;MACtE,MAAMC,aAAA,GAAgB,IAAIC,MAAA,CAAO,WAAWH,YAAA,GAAeA,YAAA,GAAeA,YAAA,EAAc,GAAG;MAC3F,MAAMI,aAAA,GAAgB,IAAID,MAAA,CAAO,WAAWH,YAAA,GAAeA,YAAA,GAAeA,YAAA,EAAc,GAAG;MAE3F,MAAMlB,QAAA,GAAW,EAAE;MACnB,MAAMC,OAAA,GAAU,EAAE;MAElB,MAAMsB,MAAA,GAAS,IAAIC,OAAA,CAAS;MAE5B,IAAIC,MAAA;MAEJ,IAAIC,UAAA,GAAa;MACjB,IAAIC,WAAA,GAAc;MAClB,IAAIC,SAAA,GAAY;MAEhB,QAAQH,MAAA,GAASV,YAAA,CAAac,IAAA,CAAK/D,KAAI,OAAO,MAAM;QAClD6D,WAAA,GAAcC,SAAA;QAEd,MAAMtD,KAAA,GAAQmD,MAAA,CAAO,CAAC;QAEtB,QAAQA,MAAA,GAAST,WAAA,CAAYa,IAAA,CAAKvD,KAAK,OAAO,MAAM;UAClD,IAAIwD,kBAAA,GAAqB;UACzB,IAAIC,kBAAA,GAAqB;UAEzB,MAAMzE,IAAA,GAAOmE,MAAA,CAAO,CAAC;UAErB,QAAQA,MAAA,GAASH,aAAA,CAAcO,IAAA,CAAKvE,IAAI,OAAO,MAAM;YACnDiE,MAAA,CAAOS,CAAA,GAAIC,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC;YAC/BF,MAAA,CAAOW,CAAA,GAAID,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC;YAC/BF,MAAA,CAAOY,CAAA,GAAIF,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC;YAC/BM,kBAAA;UACD;UAED,QAAQN,MAAA,GAASL,aAAA,CAAcS,IAAA,CAAKvE,IAAI,OAAO,MAAM;YACnD0C,QAAA,CAASoC,IAAA,CAAKH,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC,GAAGQ,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC,GAAGQ,UAAA,CAAWR,MAAA,CAAO,CAAC,CAAC,CAAC;YACjFxB,OAAA,CAAQmC,IAAA,CAAKb,MAAA,CAAOS,CAAA,EAAGT,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAC;YACzCL,kBAAA;YACAF,SAAA;UACD;UAID,IAAIG,kBAAA,KAAuB,GAAG;YAC5BtE,OAAA,CAAQC,KAAA,CAAM,2EAA2EuD,WAAW;UACrG;UAID,IAAIa,kBAAA,KAAuB,GAAG;YAC5BrE,OAAA,CAAQC,KAAA,CAAM,6EAA6EuD,WAAW;UACvG;UAEDA,WAAA;QACD;QAED,MAAMd,KAAA,GAAQwB,WAAA;QACd,MAAMU,KAAA,GAAQT,SAAA,GAAYD,WAAA;QAE1B7B,QAAA,CAASwC,QAAA,CAASnC,KAAA,EAAOkC,KAAA,EAAOX,UAAU;QAC1CA,UAAA;MACD;MAED5B,QAAA,CAASc,YAAA,CAAa,YAAY,IAAI2B,sBAAA,CAAuBvC,QAAA,EAAU,CAAC,CAAC;MACzEF,QAAA,CAASc,YAAA,CAAa,UAAU,IAAI2B,sBAAA,CAAuBtC,OAAA,EAAS,CAAC,CAAC;MAEtE,OAAOH,QAAA;IACR;IAED,SAAS0C,aAAaC,MAAA,EAAQ;MAC5B,IAAI,OAAOA,MAAA,KAAW,UAAU;QAC9B,OAAOC,UAAA,CAAW,IAAIC,UAAA,CAAWF,MAAM,CAAC;MACzC;MAED,OAAOA,MAAA;IACR;IAED,SAASG,aAAaH,MAAA,EAAQ;MAC5B,IAAI,OAAOA,MAAA,KAAW,UAAU;QAC9B,MAAMI,YAAA,GAAe,IAAIF,UAAA,CAAWF,MAAA,CAAO5D,MAAM;QACjD,SAASF,CAAA,GAAI,GAAGA,CAAA,GAAI8D,MAAA,CAAO5D,MAAA,EAAQF,CAAA,IAAK;UACtCkE,YAAA,CAAalE,CAAC,IAAI8D,MAAA,CAAOK,UAAA,CAAWnE,CAAC,IAAI;QAC1C;QAED,OAAOkE,YAAA,CAAaJ,MAAA,IAAUI,YAAA;MACtC,OAAa;QACL,OAAOJ,MAAA;MACR;IACF;IAID,MAAMM,OAAA,GAAUH,YAAA,CAAahF,IAAI;IAEjC,OAAOC,QAAA,CAASkF,OAAO,IAAIhE,WAAA,CAAYgE,OAAO,IAAIjC,UAAA,CAAW0B,YAAA,CAAa5E,IAAI,CAAC;EAChF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}