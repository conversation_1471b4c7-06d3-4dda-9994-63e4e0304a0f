{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { MathUtils, WebGLRenderTarget, Vector2, Mesh, PlaneGeometry, OrthographicCamera, RawShaderMaterial, NoBlending } from \"three\";\nvar _mipmapMaterial = /* @__PURE__ */_getMipmapMaterial();\nvar _mesh = /* @__PURE__ */new Mesh(/* @__PURE__ */new PlaneGeometry(2, 2), _mipmapMaterial);\nvar _flatCamera = /* @__PURE__ */new OrthographicCamera(0, 1, 0, 1, 0, 1);\nvar _tempTarget = null;\nclass RoughnessMipmapper {\n  constructor(renderer) {\n    __publicField(this, \"generateMipmaps\", function (material) {\n      if (\"roughnessMap\" in material === false) return;\n      var {\n        roughnessMap,\n        normalMap\n      } = material;\n      if (roughnessMap === null || normalMap === null || !roughnessMap.generateMipmaps || material.userData.roughnessUpdated) {\n        return;\n      }\n      material.userData.roughnessUpdated = true;\n      var width = Math.max(roughnessMap.image.width, normalMap.image.width);\n      var height = Math.max(roughnessMap.image.height, normalMap.image.height);\n      if (!MathUtils.isPowerOfTwo(width) || !MathUtils.isPowerOfTwo(height)) return;\n      var oldTarget = this._renderer.getRenderTarget();\n      var autoClear = this._renderer.autoClear;\n      this._renderer.autoClear = false;\n      if (_tempTarget === null || _tempTarget.width !== width || _tempTarget.height !== height) {\n        if (_tempTarget !== null) _tempTarget.dispose();\n        _tempTarget = new WebGLRenderTarget(width, height, {\n          depthBuffer: false\n        });\n        _tempTarget.scissorTest = true;\n      }\n      if (width !== roughnessMap.image.width || height !== roughnessMap.image.height) {\n        var params = {\n          wrapS: roughnessMap.wrapS,\n          wrapT: roughnessMap.wrapT,\n          magFilter: roughnessMap.magFilter,\n          minFilter: roughnessMap.minFilter,\n          depthBuffer: false\n        };\n        var newRoughnessTarget = new WebGLRenderTarget(width, height, params);\n        newRoughnessTarget.texture.generateMipmaps = true;\n        this._renderer.setRenderTarget(newRoughnessTarget);\n        material.roughnessMap = newRoughnessTarget.texture;\n        if (material.metalnessMap == roughnessMap) material.metalnessMap = material.roughnessMap;\n        if (material.aoMap == roughnessMap) material.aoMap = material.roughnessMap;\n      }\n      _mipmapMaterial.uniforms.roughnessMap.value = roughnessMap;\n      _mipmapMaterial.uniforms.normalMap.value = normalMap;\n      var position = new Vector2(0, 0);\n      var texelSize = _mipmapMaterial.uniforms.texelSize.value;\n      for (let mip = 0; width >= 1 && height >= 1; ++mip, width /= 2, height /= 2) {\n        texelSize.set(1 / width, 1 / height);\n        if (mip == 0) texelSize.set(0, 0);\n        _tempTarget.viewport.set(position.x, position.y, width, height);\n        _tempTarget.scissor.set(position.x, position.y, width, height);\n        this._renderer.setRenderTarget(_tempTarget);\n        this._renderer.render(_mesh, _flatCamera);\n        this._renderer.copyFramebufferToTexture(position, material.roughnessMap, mip);\n        _mipmapMaterial.uniforms.roughnessMap.value = material.roughnessMap;\n      }\n      if (roughnessMap !== material.roughnessMap) roughnessMap.dispose();\n      this._renderer.setRenderTarget(oldTarget);\n      this._renderer.autoClear = autoClear;\n    });\n    __publicField(this, \"dispose\", function () {\n      _mipmapMaterial.dispose();\n      _mesh.geometry.dispose();\n      if (_tempTarget != null) _tempTarget.dispose();\n    });\n    this._renderer = renderer;\n    this._renderer.compile(_mesh, _flatCamera);\n  }\n}\nfunction _getMipmapMaterial() {\n  var shaderMaterial = new RawShaderMaterial({\n    uniforms: {\n      roughnessMap: {\n        value: null\n      },\n      normalMap: {\n        value: null\n      },\n      texelSize: {\n        value: new Vector2(1, 1)\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\t\t\tprecision mediump float;\n\t\t\tprecision mediump int;\n\n\t\t\tattribute vec3 position;\n\t\t\tattribute vec2 uv;\n\n\t\t\tvarying vec2 vUv;\n\n\t\t\tvoid main() {\n\n\t\t\t\tvUv = uv;\n\n\t\t\t\tgl_Position = vec4( position, 1.0 );\n\n\t\t\t}\n\t\t`),\n    fragmentShader: (/* glsl */\n    `\n\t\t\tprecision mediump float;\n\t\t\tprecision mediump int;\n\n\t\t\tvarying vec2 vUv;\n\n\t\t\tuniform sampler2D roughnessMap;\n\t\t\tuniform sampler2D normalMap;\n\t\t\tuniform vec2 texelSize;\n\n\t\t\t#define ENVMAP_TYPE_CUBE_UV\n\n\t\t\tvec4 envMapTexelToLinear( vec4 a ) { return a; }\n\n\t\t\t#include <cube_uv_reflection_fragment>\n\n\t\t\tfloat roughnessToVariance( float roughness ) {\n\n\t\t\t\tfloat variance = 0.0;\n\n\t\t\t\tif ( roughness >= r1 ) {\n\n\t\t\t\t\tvariance = ( r0 - roughness ) * ( v1 - v0 ) / ( r0 - r1 ) + v0;\n\n\t\t\t\t} else if ( roughness >= r4 ) {\n\n\t\t\t\t\tvariance = ( r1 - roughness ) * ( v4 - v1 ) / ( r1 - r4 ) + v1;\n\n\t\t\t\t} else if ( roughness >= r5 ) {\n\n\t\t\t\t\tvariance = ( r4 - roughness ) * ( v5 - v4 ) / ( r4 - r5 ) + v4;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tfloat roughness2 = roughness * roughness;\n\n\t\t\t\t\tvariance = 1.79 * roughness2 * roughness2;\n\n\t\t\t\t}\n\n\t\t\t\treturn variance;\n\n\t\t\t}\n\n\t\t\tfloat varianceToRoughness( float variance ) {\n\n\t\t\t\tfloat roughness = 0.0;\n\n\t\t\t\tif ( variance >= v1 ) {\n\n\t\t\t\t\troughness = ( v0 - variance ) * ( r1 - r0 ) / ( v0 - v1 ) + r0;\n\n\t\t\t\t} else if ( variance >= v4 ) {\n\n\t\t\t\t\troughness = ( v1 - variance ) * ( r4 - r1 ) / ( v1 - v4 ) + r1;\n\n\t\t\t\t} else if ( variance >= v5 ) {\n\n\t\t\t\t\troughness = ( v4 - variance ) * ( r5 - r4 ) / ( v4 - v5 ) + r4;\n\n\t\t\t\t} else {\n\n\t\t\t\t\troughness = pow( 0.559 * variance, 0.25 ); // 0.559 = 1.0 / 1.79\n\n\t\t\t\t}\n\n\t\t\t\treturn roughness;\n\n\t\t\t}\n\n\t\t\tvoid main() {\n\n\t\t\t\tgl_FragColor = texture2D( roughnessMap, vUv, - 1.0 );\n\n\t\t\t\tif ( texelSize.x == 0.0 ) return;\n\n\t\t\t\tfloat roughness = gl_FragColor.g;\n\n\t\t\t\tfloat variance = roughnessToVariance( roughness );\n\n\t\t\t\tvec3 avgNormal;\n\n\t\t\t\tfor ( float x = - 1.0; x < 2.0; x += 2.0 ) {\n\n\t\t\t\t\tfor ( float y = - 1.0; y < 2.0; y += 2.0 ) {\n\n\t\t\t\t\t\tvec2 uv = vUv + vec2( x, y ) * 0.25 * texelSize;\n\n\t\t\t\t\t\tavgNormal += normalize( texture2D( normalMap, uv, - 1.0 ).xyz - 0.5 );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tvariance += 1.0 - 0.25 * length( avgNormal );\n\n\t\t\t\tgl_FragColor.g = varianceToRoughness( variance );\n\n\t\t\t}\n\t\t`),\n    blending: NoBlending,\n    depthTest: false,\n    depthWrite: false\n  });\n  shaderMaterial.type = \"RoughnessMipmapper\";\n  return shaderMaterial;\n}\nexport { RoughnessMipmapper };", "map": {"version": 3, "names": ["_mipmapMaterial", "_getMipmapMaterial", "_mesh", "<PERSON><PERSON>", "PlaneGeometry", "_flatCamera", "OrthographicCamera", "_tempTarget", "RoughnessMipmapper", "constructor", "renderer", "__publicField", "material", "roughnessMap", "normalMap", "generateMipmaps", "userData", "roughnessUpdated", "width", "Math", "max", "image", "height", "MathUtils", "isPowerOfTwo", "old<PERSON><PERSON>get", "_renderer", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "autoClear", "dispose", "WebGLRenderTarget", "depthBuffer", "scissorTest", "params", "wrapS", "wrapT", "magFilter", "minFilter", "newRoughnessTarget", "texture", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalnessMap", "aoMap", "uniforms", "value", "position", "Vector2", "texelSize", "mip", "set", "viewport", "x", "y", "scissor", "render", "copyFramebufferToTexture", "geometry", "compile", "shaderMaterial", "RawShaderMaterial", "vertexShader", "fragmentShader", "blending", "NoBlending", "depthTest", "depthWrite", "type"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/utils/RoughnessMipmapper.js"], "sourcesContent": ["/**\n * This class generates custom mipmaps for a roughness map by encoding the lost variation in the\n * normal map mip levels as increased roughness in the corresponding roughness mip levels. This\n * helps with rendering accuracy for MeshStandardMaterial, and also helps with anti-aliasing when\n * using PMREM. If the normal map is larger than the roughness map, the roughness map will be\n * enlarged to match the dimensions of the normal map.\n */\n\nimport {\n  MathUtils,\n  Mesh,\n  NoBlending,\n  OrthographicCamera,\n  PlaneGeometry,\n  RawShaderMaterial,\n  Vector2,\n  WebGLRenderTarget,\n} from 'three'\n\nvar _mipmapMaterial = /* @__PURE__ */ _getMipmapMaterial()\n\nvar _mesh = /* @__PURE__ */ new Mesh(/* @__PURE__ */ new PlaneGeometry(2, 2), _mipmapMaterial)\n\nvar _flatCamera = /* @__PURE__ */ new OrthographicCamera(0, 1, 0, 1, 0, 1)\n\nvar _tempTarget = null\n\nclass RoughnessMipmapper {\n  constructor(renderer) {\n    this._renderer = renderer\n\n    this._renderer.compile(_mesh, _flatCamera)\n  }\n\n  generateMipmaps = function (material) {\n    if ('roughnessMap' in material === false) return\n\n    var { roughnessMap, normalMap } = material\n\n    if (\n      roughnessMap === null ||\n      normalMap === null ||\n      !roughnessMap.generateMipmaps ||\n      material.userData.roughnessUpdated\n    ) {\n      return\n    }\n\n    material.userData.roughnessUpdated = true\n\n    var width = Math.max(roughnessMap.image.width, normalMap.image.width)\n\n    var height = Math.max(roughnessMap.image.height, normalMap.image.height)\n\n    if (!MathUtils.isPowerOfTwo(width) || !MathUtils.isPowerOfTwo(height)) return\n\n    var oldTarget = this._renderer.getRenderTarget()\n\n    var autoClear = this._renderer.autoClear\n\n    this._renderer.autoClear = false\n\n    if (_tempTarget === null || _tempTarget.width !== width || _tempTarget.height !== height) {\n      if (_tempTarget !== null) _tempTarget.dispose()\n\n      _tempTarget = new WebGLRenderTarget(width, height, {\n        depthBuffer: false,\n      })\n\n      _tempTarget.scissorTest = true\n    }\n\n    if (width !== roughnessMap.image.width || height !== roughnessMap.image.height) {\n      var params = {\n        wrapS: roughnessMap.wrapS,\n        wrapT: roughnessMap.wrapT,\n        magFilter: roughnessMap.magFilter,\n        minFilter: roughnessMap.minFilter,\n        depthBuffer: false,\n      }\n\n      var newRoughnessTarget = new WebGLRenderTarget(width, height, params)\n\n      newRoughnessTarget.texture.generateMipmaps = true\n\n      // Setting the render target causes the memory to be allocated.\n\n      this._renderer.setRenderTarget(newRoughnessTarget)\n\n      material.roughnessMap = newRoughnessTarget.texture\n\n      if (material.metalnessMap == roughnessMap) material.metalnessMap = material.roughnessMap\n\n      if (material.aoMap == roughnessMap) material.aoMap = material.roughnessMap\n    }\n\n    _mipmapMaterial.uniforms.roughnessMap.value = roughnessMap\n\n    _mipmapMaterial.uniforms.normalMap.value = normalMap\n\n    var position = new Vector2(0, 0)\n\n    var texelSize = _mipmapMaterial.uniforms.texelSize.value\n\n    for (let mip = 0; width >= 1 && height >= 1; ++mip, width /= 2, height /= 2) {\n      // Rendering to a mip level is not allowed in webGL1. Instead we must set\n      // up a secondary texture to write the result to, then copy it back to the\n      // proper mipmap level.\n\n      texelSize.set(1.0 / width, 1.0 / height)\n\n      if (mip == 0) texelSize.set(0.0, 0.0)\n\n      _tempTarget.viewport.set(position.x, position.y, width, height)\n\n      _tempTarget.scissor.set(position.x, position.y, width, height)\n\n      this._renderer.setRenderTarget(_tempTarget)\n\n      this._renderer.render(_mesh, _flatCamera)\n\n      this._renderer.copyFramebufferToTexture(position, material.roughnessMap, mip)\n\n      _mipmapMaterial.uniforms.roughnessMap.value = material.roughnessMap\n    }\n\n    if (roughnessMap !== material.roughnessMap) roughnessMap.dispose()\n\n    this._renderer.setRenderTarget(oldTarget)\n\n    this._renderer.autoClear = autoClear\n  }\n\n  dispose = function () {\n    _mipmapMaterial.dispose()\n\n    _mesh.geometry.dispose()\n\n    if (_tempTarget != null) _tempTarget.dispose()\n  }\n}\n\nfunction _getMipmapMaterial() {\n  var shaderMaterial = new RawShaderMaterial({\n    uniforms: {\n      roughnessMap: { value: null },\n      normalMap: { value: null },\n      texelSize: { value: new Vector2(1, 1) },\n    },\n\n    vertexShader: /* glsl */ `\n\t\t\tprecision mediump float;\n\t\t\tprecision mediump int;\n\n\t\t\tattribute vec3 position;\n\t\t\tattribute vec2 uv;\n\n\t\t\tvarying vec2 vUv;\n\n\t\t\tvoid main() {\n\n\t\t\t\tvUv = uv;\n\n\t\t\t\tgl_Position = vec4( position, 1.0 );\n\n\t\t\t}\n\t\t`,\n\n    fragmentShader: /* glsl */ `\n\t\t\tprecision mediump float;\n\t\t\tprecision mediump int;\n\n\t\t\tvarying vec2 vUv;\n\n\t\t\tuniform sampler2D roughnessMap;\n\t\t\tuniform sampler2D normalMap;\n\t\t\tuniform vec2 texelSize;\n\n\t\t\t#define ENVMAP_TYPE_CUBE_UV\n\n\t\t\tvec4 envMapTexelToLinear( vec4 a ) { return a; }\n\n\t\t\t#include <cube_uv_reflection_fragment>\n\n\t\t\tfloat roughnessToVariance( float roughness ) {\n\n\t\t\t\tfloat variance = 0.0;\n\n\t\t\t\tif ( roughness >= r1 ) {\n\n\t\t\t\t\tvariance = ( r0 - roughness ) * ( v1 - v0 ) / ( r0 - r1 ) + v0;\n\n\t\t\t\t} else if ( roughness >= r4 ) {\n\n\t\t\t\t\tvariance = ( r1 - roughness ) * ( v4 - v1 ) / ( r1 - r4 ) + v1;\n\n\t\t\t\t} else if ( roughness >= r5 ) {\n\n\t\t\t\t\tvariance = ( r4 - roughness ) * ( v5 - v4 ) / ( r4 - r5 ) + v4;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tfloat roughness2 = roughness * roughness;\n\n\t\t\t\t\tvariance = 1.79 * roughness2 * roughness2;\n\n\t\t\t\t}\n\n\t\t\t\treturn variance;\n\n\t\t\t}\n\n\t\t\tfloat varianceToRoughness( float variance ) {\n\n\t\t\t\tfloat roughness = 0.0;\n\n\t\t\t\tif ( variance >= v1 ) {\n\n\t\t\t\t\troughness = ( v0 - variance ) * ( r1 - r0 ) / ( v0 - v1 ) + r0;\n\n\t\t\t\t} else if ( variance >= v4 ) {\n\n\t\t\t\t\troughness = ( v1 - variance ) * ( r4 - r1 ) / ( v1 - v4 ) + r1;\n\n\t\t\t\t} else if ( variance >= v5 ) {\n\n\t\t\t\t\troughness = ( v4 - variance ) * ( r5 - r4 ) / ( v4 - v5 ) + r4;\n\n\t\t\t\t} else {\n\n\t\t\t\t\troughness = pow( 0.559 * variance, 0.25 ); // 0.559 = 1.0 / 1.79\n\n\t\t\t\t}\n\n\t\t\t\treturn roughness;\n\n\t\t\t}\n\n\t\t\tvoid main() {\n\n\t\t\t\tgl_FragColor = texture2D( roughnessMap, vUv, - 1.0 );\n\n\t\t\t\tif ( texelSize.x == 0.0 ) return;\n\n\t\t\t\tfloat roughness = gl_FragColor.g;\n\n\t\t\t\tfloat variance = roughnessToVariance( roughness );\n\n\t\t\t\tvec3 avgNormal;\n\n\t\t\t\tfor ( float x = - 1.0; x < 2.0; x += 2.0 ) {\n\n\t\t\t\t\tfor ( float y = - 1.0; y < 2.0; y += 2.0 ) {\n\n\t\t\t\t\t\tvec2 uv = vUv + vec2( x, y ) * 0.25 * texelSize;\n\n\t\t\t\t\t\tavgNormal += normalize( texture2D( normalMap, uv, - 1.0 ).xyz - 0.5 );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tvariance += 1.0 - 0.25 * length( avgNormal );\n\n\t\t\t\tgl_FragColor.g = varianceToRoughness( variance );\n\n\t\t\t}\n\t\t`,\n\n    blending: NoBlending,\n    depthTest: false,\n    depthWrite: false,\n  })\n\n  shaderMaterial.type = 'RoughnessMipmapper'\n\n  return shaderMaterial\n}\n\nexport { RoughnessMipmapper }\n"], "mappings": ";;;;;;;;;;;;AAmBA,IAAIA,eAAA,GAAkC,eAAAC,kBAAA,CAAoB;AAE1D,IAAIC,KAAA,GAAwB,mBAAIC,IAAA,CAAqB,mBAAIC,aAAA,CAAc,GAAG,CAAC,GAAGJ,eAAe;AAE7F,IAAIK,WAAA,GAA8B,mBAAIC,kBAAA,CAAmB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEzE,IAAIC,WAAA,GAAc;AAElB,MAAMC,kBAAA,CAAmB;EACvBC,YAAYC,QAAA,EAAU;IAMtBC,aAAA,0BAAkB,UAAUC,QAAA,EAAU;MACpC,IAAI,kBAAkBA,QAAA,KAAa,OAAO;MAE1C,IAAI;QAAEC,YAAA;QAAcC;MAAS,IAAKF,QAAA;MAElC,IACEC,YAAA,KAAiB,QACjBC,SAAA,KAAc,QACd,CAACD,YAAA,CAAaE,eAAA,IACdH,QAAA,CAASI,QAAA,CAASC,gBAAA,EAClB;QACA;MACD;MAEDL,QAAA,CAASI,QAAA,CAASC,gBAAA,GAAmB;MAErC,IAAIC,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAIP,YAAA,CAAaQ,KAAA,CAAMH,KAAA,EAAOJ,SAAA,CAAUO,KAAA,CAAMH,KAAK;MAEpE,IAAII,MAAA,GAASH,IAAA,CAAKC,GAAA,CAAIP,YAAA,CAAaQ,KAAA,CAAMC,MAAA,EAAQR,SAAA,CAAUO,KAAA,CAAMC,MAAM;MAEvE,IAAI,CAACC,SAAA,CAAUC,YAAA,CAAaN,KAAK,KAAK,CAACK,SAAA,CAAUC,YAAA,CAAaF,MAAM,GAAG;MAEvE,IAAIG,SAAA,GAAY,KAAKC,SAAA,CAAUC,eAAA,CAAiB;MAEhD,IAAIC,SAAA,GAAY,KAAKF,SAAA,CAAUE,SAAA;MAE/B,KAAKF,SAAA,CAAUE,SAAA,GAAY;MAE3B,IAAIrB,WAAA,KAAgB,QAAQA,WAAA,CAAYW,KAAA,KAAUA,KAAA,IAASX,WAAA,CAAYe,MAAA,KAAWA,MAAA,EAAQ;QACxF,IAAIf,WAAA,KAAgB,MAAMA,WAAA,CAAYsB,OAAA,CAAS;QAE/CtB,WAAA,GAAc,IAAIuB,iBAAA,CAAkBZ,KAAA,EAAOI,MAAA,EAAQ;UACjDS,WAAA,EAAa;QACrB,CAAO;QAEDxB,WAAA,CAAYyB,WAAA,GAAc;MAC3B;MAED,IAAId,KAAA,KAAUL,YAAA,CAAaQ,KAAA,CAAMH,KAAA,IAASI,MAAA,KAAWT,YAAA,CAAaQ,KAAA,CAAMC,MAAA,EAAQ;QAC9E,IAAIW,MAAA,GAAS;UACXC,KAAA,EAAOrB,YAAA,CAAaqB,KAAA;UACpBC,KAAA,EAAOtB,YAAA,CAAasB,KAAA;UACpBC,SAAA,EAAWvB,YAAA,CAAauB,SAAA;UACxBC,SAAA,EAAWxB,YAAA,CAAawB,SAAA;UACxBN,WAAA,EAAa;QACd;QAED,IAAIO,kBAAA,GAAqB,IAAIR,iBAAA,CAAkBZ,KAAA,EAAOI,MAAA,EAAQW,MAAM;QAEpEK,kBAAA,CAAmBC,OAAA,CAAQxB,eAAA,GAAkB;QAI7C,KAAKW,SAAA,CAAUc,eAAA,CAAgBF,kBAAkB;QAEjD1B,QAAA,CAASC,YAAA,GAAeyB,kBAAA,CAAmBC,OAAA;QAE3C,IAAI3B,QAAA,CAAS6B,YAAA,IAAgB5B,YAAA,EAAcD,QAAA,CAAS6B,YAAA,GAAe7B,QAAA,CAASC,YAAA;QAE5E,IAAID,QAAA,CAAS8B,KAAA,IAAS7B,YAAA,EAAcD,QAAA,CAAS8B,KAAA,GAAQ9B,QAAA,CAASC,YAAA;MAC/D;MAEDb,eAAA,CAAgB2C,QAAA,CAAS9B,YAAA,CAAa+B,KAAA,GAAQ/B,YAAA;MAE9Cb,eAAA,CAAgB2C,QAAA,CAAS7B,SAAA,CAAU8B,KAAA,GAAQ9B,SAAA;MAE3C,IAAI+B,QAAA,GAAW,IAAIC,OAAA,CAAQ,GAAG,CAAC;MAE/B,IAAIC,SAAA,GAAY/C,eAAA,CAAgB2C,QAAA,CAASI,SAAA,CAAUH,KAAA;MAEnD,SAASI,GAAA,GAAM,GAAG9B,KAAA,IAAS,KAAKI,MAAA,IAAU,GAAG,EAAE0B,GAAA,EAAK9B,KAAA,IAAS,GAAGI,MAAA,IAAU,GAAG;QAK3EyB,SAAA,CAAUE,GAAA,CAAI,IAAM/B,KAAA,EAAO,IAAMI,MAAM;QAEvC,IAAI0B,GAAA,IAAO,GAAGD,SAAA,CAAUE,GAAA,CAAI,GAAK,CAAG;QAEpC1C,WAAA,CAAY2C,QAAA,CAASD,GAAA,CAAIJ,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAA,EAAGlC,KAAA,EAAOI,MAAM;QAE9Df,WAAA,CAAY8C,OAAA,CAAQJ,GAAA,CAAIJ,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAA,EAAGlC,KAAA,EAAOI,MAAM;QAE7D,KAAKI,SAAA,CAAUc,eAAA,CAAgBjC,WAAW;QAE1C,KAAKmB,SAAA,CAAU4B,MAAA,CAAOpD,KAAA,EAAOG,WAAW;QAExC,KAAKqB,SAAA,CAAU6B,wBAAA,CAAyBV,QAAA,EAAUjC,QAAA,CAASC,YAAA,EAAcmC,GAAG;QAE5EhD,eAAA,CAAgB2C,QAAA,CAAS9B,YAAA,CAAa+B,KAAA,GAAQhC,QAAA,CAASC,YAAA;MACxD;MAED,IAAIA,YAAA,KAAiBD,QAAA,CAASC,YAAA,EAAcA,YAAA,CAAagB,OAAA,CAAS;MAElE,KAAKH,SAAA,CAAUc,eAAA,CAAgBf,SAAS;MAExC,KAAKC,SAAA,CAAUE,SAAA,GAAYA,SAAA;IAC5B;IAEDjB,aAAA,kBAAU,YAAY;MACpBX,eAAA,CAAgB6B,OAAA,CAAS;MAEzB3B,KAAA,CAAMsD,QAAA,CAAS3B,OAAA,CAAS;MAExB,IAAItB,WAAA,IAAe,MAAMA,WAAA,CAAYsB,OAAA,CAAS;IAC/C;IA9GC,KAAKH,SAAA,GAAYhB,QAAA;IAEjB,KAAKgB,SAAA,CAAU+B,OAAA,CAAQvD,KAAA,EAAOG,WAAW;EAC1C;AA4GH;AAEA,SAASJ,mBAAA,EAAqB;EAC5B,IAAIyD,cAAA,GAAiB,IAAIC,iBAAA,CAAkB;IACzChB,QAAA,EAAU;MACR9B,YAAA,EAAc;QAAE+B,KAAA,EAAO;MAAM;MAC7B9B,SAAA,EAAW;QAAE8B,KAAA,EAAO;MAAM;MAC1BG,SAAA,EAAW;QAAEH,KAAA,EAAO,IAAIE,OAAA,CAAQ,GAAG,CAAC;MAAG;IACxC;IAEDc,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAkBzBC,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAqG3BC,QAAA,EAAUC,UAAA;IACVC,SAAA,EAAW;IACXC,UAAA,EAAY;EAChB,CAAG;EAEDP,cAAA,CAAeQ,IAAA,GAAO;EAEtB,OAAOR,cAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}