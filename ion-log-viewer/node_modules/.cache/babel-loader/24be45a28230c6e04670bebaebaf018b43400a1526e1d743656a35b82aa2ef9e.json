{"ast": null, "code": "import { BufferAttribute } from 'three';\nexport function getVertexCount(geo) {\n  return geo.index ? geo.index.count : geo.attributes.position.count;\n}\nexport function getTriCount(geo) {\n  return getVertexCount(geo) / 3;\n}\nexport function getIndexArray(vertexCount, BufferConstructor = ArrayBuffer) {\n  if (vertexCount > 65535) {\n    return new Uint32Array(new BufferConstructor(4 * vertexCount));\n  } else {\n    return new Uint16Array(new BufferConstructor(2 * vertexCount));\n  }\n}\n\n// ensures that an index is present on the geometry\nexport function ensureIndex(geo, options) {\n  if (!geo.index) {\n    const vertexCount = geo.attributes.position.count;\n    const BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n    const index = getIndexArray(vertexCount, BufferConstructor);\n    geo.setIndex(new BufferAttribute(index, 1));\n    for (let i = 0; i < vertexCount; i++) {\n      index[i] = i;\n    }\n  }\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nexport function getFullGeometryRange(geo, range) {\n  const triCount = getTriCount(geo);\n  const drawRange = range ? range : geo.drawRange;\n  const start = drawRange.start / 3;\n  const end = (drawRange.start + drawRange.count) / 3;\n  const offset = Math.max(0, start);\n  const count = Math.min(triCount, end) - offset;\n  return [{\n    offset: Math.floor(offset),\n    count: Math.floor(count)\n  }];\n}\nexport function getRootIndexRanges(geo, range) {\n  if (!geo.groups || !geo.groups.length) {\n    return getFullGeometryRange(geo, range);\n  }\n  const ranges = [];\n  const rangeBoundaries = new Set();\n  const drawRange = range ? range : geo.drawRange;\n  const drawRangeStart = drawRange.start / 3;\n  const drawRangeEnd = (drawRange.start + drawRange.count) / 3;\n  for (const group of geo.groups) {\n    const groupStart = group.start / 3;\n    const groupEnd = (group.start + group.count) / 3;\n    rangeBoundaries.add(Math.max(drawRangeStart, groupStart));\n    rangeBoundaries.add(Math.min(drawRangeEnd, groupEnd));\n  }\n\n  // note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n  const sortedBoundaries = Array.from(rangeBoundaries.values()).sort((a, b) => a - b);\n  for (let i = 0; i < sortedBoundaries.length - 1; i++) {\n    const start = sortedBoundaries[i];\n    const end = sortedBoundaries[i + 1];\n    ranges.push({\n      offset: Math.floor(start),\n      count: Math.floor(end - start)\n    });\n  }\n  return ranges;\n}\nexport function hasGroupGaps(geometry, range) {\n  const vertexCount = getTriCount(geometry);\n  const groups = getRootIndexRanges(geometry, range).sort((a, b) => a.offset - b.offset);\n  const finalGroup = groups[groups.length - 1];\n  finalGroup.count = Math.min(vertexCount - finalGroup.offset, finalGroup.count);\n  let total = 0;\n  groups.forEach(({\n    count\n  }) => total += count);\n  return vertexCount !== total;\n}", "map": {"version": 3, "names": ["BufferAttribute", "getVertexCount", "geo", "index", "count", "attributes", "position", "getTriCount", "getIndexArray", "vertexCount", "BufferConstructor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint32Array", "Uint16Array", "ensureIndex", "options", "useSharedArrayBuffer", "SharedArrayBuffer", "setIndex", "i", "getFullGeometryRange", "range", "triCount", "drawRange", "start", "end", "offset", "Math", "max", "min", "floor", "getRootIndexRanges", "groups", "length", "ranges", "rangeBoundaries", "Set", "drawRangeStart", "drawRangeEnd", "group", "groupStart", "groupEnd", "add", "sortedBoundaries", "Array", "from", "values", "sort", "a", "b", "push", "hasGroupGaps", "geometry", "finalGroup", "total", "for<PERSON>ach"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/build/geometryUtils.js"], "sourcesContent": ["import { BufferAttribute } from 'three';\n\nexport function getVertexCount( geo ) {\n\n\treturn geo.index ? geo.index.count : geo.attributes.position.count;\n\n}\n\nexport function getTriCount( geo ) {\n\n\treturn getVertexCount( geo ) / 3;\n\n}\n\nexport function getIndexArray( vertexCount, BufferConstructor = ArrayBuffer ) {\n\n\tif ( vertexCount > 65535 ) {\n\n\t\treturn new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t} else {\n\n\t\treturn new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t}\n\n}\n\n// ensures that an index is present on the geometry\nexport function ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tconst index = getIndexArray( vertexCount, BufferConstructor );\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nexport function getFullGeometryRange( geo, range ) {\n\n\tconst triCount = getTriCount( geo );\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst start = drawRange.start / 3;\n\tconst end = ( drawRange.start + drawRange.count ) / 3;\n\n\tconst offset = Math.max( 0, start );\n\tconst count = Math.min( triCount, end ) - offset;\n\treturn [ {\n\t\toffset: Math.floor( offset ),\n\t\tcount: Math.floor( count ),\n\t} ];\n\n}\n\nexport function getRootIndexRanges( geo, range ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn getFullGeometryRange( geo, range );\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst drawRangeStart = drawRange.start / 3;\n\tconst drawRangeEnd = ( drawRange.start + drawRange.count ) / 3;\n\tfor ( const group of geo.groups ) {\n\n\t\tconst groupStart = group.start / 3;\n\t\tconst groupEnd = ( group.start + group.count ) / 3;\n\t\trangeBoundaries.add( Math.max( drawRangeStart, groupStart ) );\n\t\trangeBoundaries.add( Math.min( drawRangeEnd, groupEnd ) );\n\n\t}\n\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ];\n\t\tconst end = sortedBoundaries[ i + 1 ];\n\n\t\tranges.push( {\n\t\t\toffset: Math.floor( start ),\n\t\t\tcount: Math.floor( end - start ),\n\t\t} );\n\n\t}\n\n\treturn ranges;\n\n}\n\nexport function hasGroupGaps( geometry, range ) {\n\n\tconst vertexCount = getTriCount( geometry );\n\tconst groups = getRootIndexRanges( geometry, range )\n\t\t.sort( ( a, b ) => a.offset - b.offset );\n\n\tconst finalGroup = groups[ groups.length - 1 ];\n\tfinalGroup.count = Math.min( vertexCount - finalGroup.offset, finalGroup.count );\n\n\tlet total = 0;\n\tgroups.forEach( ( { count } ) => total += count );\n\treturn vertexCount !== total;\n\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AAEvC,OAAO,SAASC,cAAcA,CAAEC,GAAG,EAAG;EAErC,OAAOA,GAAG,CAACC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAACC,KAAK,GAAGF,GAAG,CAACG,UAAU,CAACC,QAAQ,CAACF,KAAK;AAEnE;AAEA,OAAO,SAASG,WAAWA,CAAEL,GAAG,EAAG;EAElC,OAAOD,cAAc,CAAEC,GAAI,CAAC,GAAG,CAAC;AAEjC;AAEA,OAAO,SAASM,aAAaA,CAAEC,WAAW,EAAEC,iBAAiB,GAAGC,WAAW,EAAG;EAE7E,IAAKF,WAAW,GAAG,KAAK,EAAG;IAE1B,OAAO,IAAIG,WAAW,CAAE,IAAIF,iBAAiB,CAAE,CAAC,GAAGD,WAAY,CAAE,CAAC;EAEnE,CAAC,MAAM;IAEN,OAAO,IAAII,WAAW,CAAE,IAAIH,iBAAiB,CAAE,CAAC,GAAGD,WAAY,CAAE,CAAC;EAEnE;AAED;;AAEA;AACA,OAAO,SAASK,WAAWA,CAAEZ,GAAG,EAAEa,OAAO,EAAG;EAE3C,IAAK,CAAEb,GAAG,CAACC,KAAK,EAAG;IAElB,MAAMM,WAAW,GAAGP,GAAG,CAACG,UAAU,CAACC,QAAQ,CAACF,KAAK;IACjD,MAAMM,iBAAiB,GAAGK,OAAO,CAACC,oBAAoB,GAAGC,iBAAiB,GAAGN,WAAW;IACxF,MAAMR,KAAK,GAAGK,aAAa,CAAEC,WAAW,EAAEC,iBAAkB,CAAC;IAC7DR,GAAG,CAACgB,QAAQ,CAAE,IAAIlB,eAAe,CAAEG,KAAK,EAAE,CAAE,CAAE,CAAC;IAE/C,KAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,WAAW,EAAEU,CAAC,EAAG,EAAG;MAExChB,KAAK,CAAEgB,CAAC,CAAE,GAAGA,CAAC;IAEf;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAAElB,GAAG,EAAEmB,KAAK,EAAG;EAElD,MAAMC,QAAQ,GAAGf,WAAW,CAAEL,GAAI,CAAC;EACnC,MAAMqB,SAAS,GAAGF,KAAK,GAAGA,KAAK,GAAGnB,GAAG,CAACqB,SAAS;EAC/C,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAK,GAAG,CAAC;EACjC,MAAMC,GAAG,GAAG,CAAEF,SAAS,CAACC,KAAK,GAAGD,SAAS,CAACnB,KAAK,IAAK,CAAC;EAErD,MAAMsB,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAE,CAAC,EAAEJ,KAAM,CAAC;EACnC,MAAMpB,KAAK,GAAGuB,IAAI,CAACE,GAAG,CAAEP,QAAQ,EAAEG,GAAI,CAAC,GAAGC,MAAM;EAChD,OAAO,CAAE;IACRA,MAAM,EAAEC,IAAI,CAACG,KAAK,CAAEJ,MAAO,CAAC;IAC5BtB,KAAK,EAAEuB,IAAI,CAACG,KAAK,CAAE1B,KAAM;EAC1B,CAAC,CAAE;AAEJ;AAEA,OAAO,SAAS2B,kBAAkBA,CAAE7B,GAAG,EAAEmB,KAAK,EAAG;EAEhD,IAAK,CAAEnB,GAAG,CAAC8B,MAAM,IAAI,CAAE9B,GAAG,CAAC8B,MAAM,CAACC,MAAM,EAAG;IAE1C,OAAOb,oBAAoB,CAAElB,GAAG,EAAEmB,KAAM,CAAC;EAE1C;EAEA,MAAMa,MAAM,GAAG,EAAE;EACjB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEjC,MAAMb,SAAS,GAAGF,KAAK,GAAGA,KAAK,GAAGnB,GAAG,CAACqB,SAAS;EAC/C,MAAMc,cAAc,GAAGd,SAAS,CAACC,KAAK,GAAG,CAAC;EAC1C,MAAMc,YAAY,GAAG,CAAEf,SAAS,CAACC,KAAK,GAAGD,SAAS,CAACnB,KAAK,IAAK,CAAC;EAC9D,KAAM,MAAMmC,KAAK,IAAIrC,GAAG,CAAC8B,MAAM,EAAG;IAEjC,MAAMQ,UAAU,GAAGD,KAAK,CAACf,KAAK,GAAG,CAAC;IAClC,MAAMiB,QAAQ,GAAG,CAAEF,KAAK,CAACf,KAAK,GAAGe,KAAK,CAACnC,KAAK,IAAK,CAAC;IAClD+B,eAAe,CAACO,GAAG,CAAEf,IAAI,CAACC,GAAG,CAAES,cAAc,EAAEG,UAAW,CAAE,CAAC;IAC7DL,eAAe,CAACO,GAAG,CAAEf,IAAI,CAACE,GAAG,CAAES,YAAY,EAAEG,QAAS,CAAE,CAAC;EAE1D;;EAGA;EACA,MAAME,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAAEV,eAAe,CAACW,MAAM,CAAC,CAAE,CAAC,CAACC,IAAI,CAAE,CAAEC,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAE,CAAC;EACzF,KAAM,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,gBAAgB,CAACV,MAAM,GAAG,CAAC,EAAEd,CAAC,EAAG,EAAG;IAExD,MAAMK,KAAK,GAAGmB,gBAAgB,CAAExB,CAAC,CAAE;IACnC,MAAMM,GAAG,GAAGkB,gBAAgB,CAAExB,CAAC,GAAG,CAAC,CAAE;IAErCe,MAAM,CAACgB,IAAI,CAAE;MACZxB,MAAM,EAAEC,IAAI,CAACG,KAAK,CAAEN,KAAM,CAAC;MAC3BpB,KAAK,EAAEuB,IAAI,CAACG,KAAK,CAAEL,GAAG,GAAGD,KAAM;IAChC,CAAE,CAAC;EAEJ;EAEA,OAAOU,MAAM;AAEd;AAEA,OAAO,SAASiB,YAAYA,CAAEC,QAAQ,EAAE/B,KAAK,EAAG;EAE/C,MAAMZ,WAAW,GAAGF,WAAW,CAAE6C,QAAS,CAAC;EAC3C,MAAMpB,MAAM,GAAGD,kBAAkB,CAAEqB,QAAQ,EAAE/B,KAAM,CAAC,CAClD0B,IAAI,CAAE,CAAEC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACtB,MAAM,GAAGuB,CAAC,CAACvB,MAAO,CAAC;EAEzC,MAAM2B,UAAU,GAAGrB,MAAM,CAAEA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAE;EAC9CoB,UAAU,CAACjD,KAAK,GAAGuB,IAAI,CAACE,GAAG,CAAEpB,WAAW,GAAG4C,UAAU,CAAC3B,MAAM,EAAE2B,UAAU,CAACjD,KAAM,CAAC;EAEhF,IAAIkD,KAAK,GAAG,CAAC;EACbtB,MAAM,CAACuB,OAAO,CAAE,CAAE;IAAEnD;EAAM,CAAC,KAAMkD,KAAK,IAAIlD,KAAM,CAAC;EACjD,OAAOK,WAAW,KAAK6C,KAAK;AAE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}