{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { getSystemSymbolTable } from \"./IonSystemSymbolTable\";\nfunction byVersion(x, y) {\n  return x.version - y.version;\n}\nexport class Catalog {\n  constructor() {\n    this.symbolTables = {};\n    this.add(getSystemSymbolTable());\n  }\n  add(symbolTable) {\n    if (symbolTable.name === undefined || symbolTable.name === null) {\n      throw new Error(\"SymbolTable name must be defined.\");\n    }\n    const versions = this.symbolTables[symbolTable.name];\n    if (versions === undefined) {\n      this.symbolTables[symbolTable.name] = [];\n    }\n    this.symbolTables[symbolTable.name][symbolTable.version] = symbolTable;\n  }\n  getVersion(name, version) {\n    const tables = this.symbolTables[name];\n    if (!tables) {\n      return null;\n    }\n    let table = tables[version];\n    if (!table) {\n      table = tables[tables.length];\n    }\n    return table ? table : null;\n  }\n  getTable(name) {\n    const versions = this.symbolTables[name];\n    if (versions === undefined) {\n      return null;\n    }\n    return versions[versions.length - 1];\n  }\n}", "map": {"version": 3, "names": ["getSystemSymbolTable", "byVersion", "x", "y", "version", "Catalog", "constructor", "symbolTables", "add", "symbolTable", "name", "undefined", "Error", "versions", "getVersion", "tables", "table", "length", "getTable"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonCatalog.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { getSystemSymbolTable } from \"./IonSystemSymbolTable\";\nfunction byVersion(x, y) {\n    return x.version - y.version;\n}\nexport class Catalog {\n    constructor() {\n        this.symbolTables = {};\n        this.add(getSystemSymbolTable());\n    }\n    add(symbolTable) {\n        if (symbolTable.name === undefined || symbolTable.name === null) {\n            throw new Error(\"SymbolTable name must be defined.\");\n        }\n        const versions = this.symbolTables[symbolTable.name];\n        if (versions === undefined) {\n            this.symbolTables[symbolTable.name] = [];\n        }\n        this.symbolTables[symbolTable.name][symbolTable.version] = symbolTable;\n    }\n    getVersion(name, version) {\n        const tables = this.symbolTables[name];\n        if (!tables) {\n            return null;\n        }\n        let table = tables[version];\n        if (!table) {\n            table = tables[tables.length];\n        }\n        return table ? table : null;\n    }\n    getTable(name) {\n        const versions = this.symbolTables[name];\n        if (versions === undefined) {\n            return null;\n        }\n        return versions[versions.length - 1];\n    }\n}\n//# sourceMappingURL=IonCatalog.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,OAAOD,CAAC,CAACE,OAAO,GAAGD,CAAC,CAACC,OAAO;AAChC;AACA,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,GAAG,CAACR,oBAAoB,CAAC,CAAC,CAAC;EACpC;EACAQ,GAAGA,CAACC,WAAW,EAAE;IACb,IAAIA,WAAW,CAACC,IAAI,KAAKC,SAAS,IAAIF,WAAW,CAACC,IAAI,KAAK,IAAI,EAAE;MAC7D,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;IACxD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAACE,WAAW,CAACC,IAAI,CAAC;IACpD,IAAIG,QAAQ,KAAKF,SAAS,EAAE;MACxB,IAAI,CAACJ,YAAY,CAACE,WAAW,CAACC,IAAI,CAAC,GAAG,EAAE;IAC5C;IACA,IAAI,CAACH,YAAY,CAACE,WAAW,CAACC,IAAI,CAAC,CAACD,WAAW,CAACL,OAAO,CAAC,GAAGK,WAAW;EAC1E;EACAK,UAAUA,CAACJ,IAAI,EAAEN,OAAO,EAAE;IACtB,MAAMW,MAAM,GAAG,IAAI,CAACR,YAAY,CAACG,IAAI,CAAC;IACtC,IAAI,CAACK,MAAM,EAAE;MACT,OAAO,IAAI;IACf;IACA,IAAIC,KAAK,GAAGD,MAAM,CAACX,OAAO,CAAC;IAC3B,IAAI,CAACY,KAAK,EAAE;MACRA,KAAK,GAAGD,MAAM,CAACA,MAAM,CAACE,MAAM,CAAC;IACjC;IACA,OAAOD,KAAK,GAAGA,KAAK,GAAG,IAAI;EAC/B;EACAE,QAAQA,CAACR,IAAI,EAAE;IACX,MAAMG,QAAQ,GAAG,IAAI,CAACN,YAAY,CAACG,IAAI,CAAC;IACxC,IAAIG,QAAQ,KAAKF,SAAS,EAAE;MACxB,OAAO,IAAI;IACf;IACA,OAAOE,QAAQ,CAACA,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;EACxC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}