{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport default class SignAndMagnitudeInt {\n  constructor(_magnitude, _isNegative = _magnitude < 0n) {\n    this._magnitude = _magnitude;\n    this._isNegative = _isNegative;\n  }\n  get magnitude() {\n    return this._magnitude;\n  }\n  get isNegative() {\n    return this._isNegative;\n  }\n  static fromNumber(value) {\n    const isNegative = value < 0 || Object.is(value, -0);\n    const absoluteValue = Math.abs(value);\n    const magnitude = BigInt(absoluteValue);\n    return new SignAndMagnitudeInt(magnitude, isNegative);\n  }\n  equals(other) {\n    return this._magnitude === other._magnitude && this._isNegative === other._isNegative;\n  }\n}", "map": {"version": 3, "names": ["SignAndMagnitudeInt", "constructor", "_magnitude", "_isNegative", "magnitude", "isNegative", "fromNumber", "value", "Object", "is", "absoluteValue", "Math", "abs", "BigInt", "equals", "other"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/SignAndMagnitudeInt.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport default class SignAndMagnitudeInt {\n    constructor(_magnitude, _isNegative = _magnitude < 0n) {\n        this._magnitude = _magnitude;\n        this._isNegative = _isNegative;\n    }\n    get magnitude() {\n        return this._magnitude;\n    }\n    get isNegative() {\n        return this._isNegative;\n    }\n    static fromNumber(value) {\n        const isNegative = value < 0 || Object.is(value, -0);\n        const absoluteValue = Math.abs(value);\n        const magnitude = BigInt(absoluteValue);\n        return new SignAndMagnitudeInt(magnitude, isNegative);\n    }\n    equals(other) {\n        return (this._magnitude === other._magnitude &&\n            this._isNegative === other._isNegative);\n    }\n}\n//# sourceMappingURL=SignAndMagnitudeInt.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMA,mBAAmB,CAAC;EACrCC,WAAWA,CAACC,UAAU,EAAEC,WAAW,GAAGD,UAAU,GAAG,EAAE,EAAE;IACnD,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACF,UAAU;EAC1B;EACA,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,WAAW;EAC3B;EACA,OAAOG,UAAUA,CAACC,KAAK,EAAE;IACrB,MAAMF,UAAU,GAAGE,KAAK,GAAG,CAAC,IAAIC,MAAM,CAACC,EAAE,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;IACpD,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC;IACrC,MAAMH,SAAS,GAAGS,MAAM,CAACH,aAAa,CAAC;IACvC,OAAO,IAAIV,mBAAmB,CAACI,SAAS,EAAEC,UAAU,CAAC;EACzD;EACAS,MAAMA,CAACC,KAAK,EAAE;IACV,OAAQ,IAAI,CAACb,UAAU,KAAKa,KAAK,CAACb,UAAU,IACxC,IAAI,CAACC,WAAW,KAAKY,KAAK,CAACZ,WAAW;EAC9C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}