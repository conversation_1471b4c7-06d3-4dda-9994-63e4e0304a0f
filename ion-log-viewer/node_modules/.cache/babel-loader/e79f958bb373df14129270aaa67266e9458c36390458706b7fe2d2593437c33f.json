{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport const WHITESPACE_COMMENT1 = -2;\nexport const WHITESPACE_COMMENT2 = -3;\nexport const ESCAPED_NEWLINE = -4;\nconst DOUBLE_QUOTE = 34;\nconst SINGLE_QUOTE = 39;\nconst SLASH = 92;\nconst _escapeStrings = {\n  0: \"\\\\0\",\n  8: \"\\\\b\",\n  9: \"\\\\t\",\n  10: \"\\\\n\",\n  13: \"\\\\r\",\n  DOUBLE_QUOTE: '\\\\\"',\n  SINGLE_QUOTE: \"\\\\'\",\n  SLASH: \"\\\\\\\\\"\n};\nfunction _make_bool_array(str) {\n  let i = str.length;\n  const a = [];\n  a[128] = false;\n  while (i > 0) {\n    --i;\n    a[str.charCodeAt(i)] = true;\n  }\n  return a;\n}\nconst _is_base64_char = _make_bool_array(\"+/0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_hex_digit = _make_bool_array(\"0123456789abcdefABCDEF\");\nconst _is_letter = _make_bool_array(\"_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_letter_or_digit = _make_bool_array(\"_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_numeric_terminator = _make_bool_array(\"{}[](),\\\"' \\t\\n\\r\\v\\u000c\");\nconst _is_operator_char = _make_bool_array(\"!#%&*+-./;<=>?@^`|~\");\nconst _is_whitespace = _make_bool_array(\" \\t\\r\\n\\u000b\\u000c\");\nconst isIdentifierArray = _make_bool_array(\"_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nexport function is_digit(ch) {\n  if (ch < 48 || ch > 57) {\n    return false;\n  }\n  return true;\n}\nexport function is_keyword(str) {\n  return str === \"null\" || str === \"true\" || str === \"false\" || str === \"nan\" || str === \"+inf\" || str === \"-inf\";\n}\nexport function asAscii(s) {\n  if (typeof s === \"undefined\") {\n    s = \"undefined::null\";\n  } else if (typeof s == \"number\") {\n    s = \"\" + s;\n  } else if (typeof s != \"string\") {\n    const esc = nextEscape(s, s.length);\n    if (esc >= 0) {\n      s = escapeString(s, esc);\n    }\n  }\n  return s;\n}\nexport function nextEscape(s, prev) {\n  while (prev-- > 0) {\n    if (needsEscape(s.charCodeAt(prev))) {\n      break;\n    }\n  }\n  return prev;\n}\nexport function needsEscape(c) {\n  if (c < 32) {\n    return true;\n  }\n  if (c > 126) {\n    return true;\n  }\n  if (c === DOUBLE_QUOTE || c === SINGLE_QUOTE || c === SLASH) {\n    return true;\n  }\n  return false;\n}\nexport function escapeString(s, pos) {\n  const fixes = [];\n  let c, ii, s2;\n  while (pos >= 0) {\n    c = s.charCodeAt(pos);\n    if (!needsEscape(c)) {\n      break;\n    }\n    fixes.push([pos, c]);\n    pos = nextEscape(s, pos);\n  }\n  if (fixes.length > 0) {\n    s2 = \"\";\n    ii = fixes.length;\n    pos = s.length;\n    while (ii--) {\n      const fix = fixes[ii];\n      const tail_len = pos - fix[0] - 1;\n      if (tail_len > 0) {\n        s2 = escapeSequence(fix[1]) + s.substring(fix[0] + 1, pos) + s2;\n      } else {\n        s2 = s.substring(fix[0] + 1, pos) + s2;\n      }\n      pos = fix[0] - 1;\n    }\n    if (pos >= 0) {\n      s2 = s.substring(0, pos) + s2;\n    }\n    s = s2;\n  }\n  return s;\n}\nexport function escapeSequence(c) {\n  let s = _escapeStrings[c];\n  if (typeof s === \"undefined\") {\n    if (c < 256) {\n      s = \"\\\\x\" + toHex(c, 2);\n    } else if (c <= 0xffff) {\n      s = \"\\\\u\" + toHex(c, 4);\n    } else {\n      s = \"\\\\U\" + toHex(c, 8);\n    }\n  }\n  return s;\n}\nexport function toHex(c, len) {\n  let s = \"\";\n  while (c > 0) {\n    s += \"0123456789ABCDEF\".charAt(c && 0xf);\n    c = c / 16;\n  }\n  if (s.length < len) {\n    s = \"000000000\" + s;\n    s = s.substring(s.length - len, s.length);\n  }\n  return s;\n}\nexport function is_letter(ch) {\n  return _is_letter[ch];\n}\nexport function isNumericTerminator(ch) {\n  if (ch == -1) {\n    return true;\n  }\n  return _is_numeric_terminator[ch];\n}\nexport function is_letter_or_digit(ch) {\n  return _is_letter_or_digit[ch];\n}\nexport function is_operator_char(ch) {\n  return _is_operator_char[ch];\n}\nexport function is_whitespace(ch) {\n  if (ch > 32) {\n    return false;\n  }\n  if (ch == WHITESPACE_COMMENT1) {\n    return true;\n  }\n  if (ch == WHITESPACE_COMMENT2) {\n    return true;\n  }\n  if (ch == ESCAPED_NEWLINE) {\n    return true;\n  }\n  return _is_whitespace[ch];\n}\nexport function is_base64_char(ch) {\n  return _is_base64_char[ch];\n}\nexport function is_hex_digit(ch) {\n  return _is_hex_digit[ch];\n}\nconst base64chars = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\", \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\", \"w\", \"x\", \"y\", \"z\", \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"+\", \"/\"];\nconst base64inv = {\n  A: 0,\n  B: 1,\n  C: 2,\n  D: 3,\n  E: 4,\n  F: 5,\n  G: 6,\n  H: 7,\n  I: 8,\n  J: 9,\n  K: 10,\n  L: 11,\n  M: 12,\n  N: 13,\n  O: 14,\n  P: 15,\n  Q: 16,\n  R: 17,\n  S: 18,\n  T: 19,\n  U: 20,\n  V: 21,\n  W: 22,\n  X: 23,\n  Y: 24,\n  Z: 25,\n  a: 26,\n  b: 27,\n  c: 28,\n  d: 29,\n  e: 30,\n  f: 31,\n  g: 32,\n  h: 33,\n  i: 34,\n  j: 35,\n  k: 36,\n  l: 37,\n  m: 38,\n  n: 39,\n  o: 40,\n  p: 41,\n  q: 42,\n  r: 43,\n  s: 44,\n  t: 45,\n  u: 46,\n  v: 47,\n  w: 48,\n  x: 49,\n  y: 50,\n  z: 51,\n  \"0\": 52,\n  \"1\": 53,\n  \"2\": 54,\n  \"3\": 55,\n  \"4\": 56,\n  \"5\": 57,\n  \"6\": 58,\n  \"7\": 59,\n  \"8\": 60,\n  \"9\": 61,\n  \"+\": 62,\n  \"/\": 63\n};\nexport function fromBase64(str) {\n  let pad = 0;\n  for (let i = str.length - 1; str.charAt(i) == \"=\"; i--) {\n    pad++;\n  }\n  const buf = new Uint8Array(str.length * 3 / 4 - pad);\n  for (let i = 0; i < str.length - pad; i += 4) {\n    const c0 = base64inv[str.charAt(i)],\n      c1 = base64inv[str.charAt(i + 1)],\n      c2 = base64inv[str.charAt(i + 2)],\n      c3 = base64inv[str.charAt(i + 3)];\n    buf[i * 3 / 4] = c0 << 2 & 255 | c1 >>> 4;\n    if (i + 2 < str.length - pad) {\n      buf[i * 3 / 4 + 1] = c1 << 4 & 255 | c2 >>> 2;\n      if (i + 3 < str.length - pad) {\n        buf[i * 3 / 4 + 2] = c2 << 6 & 255 | c3;\n      }\n    }\n  }\n  return buf;\n}\nexport function toBase64(buf) {\n  const str = new Array(Math.ceil(buf.length * 4 / 3));\n  for (let i = 0; i < buf.length; i += 3) {\n    const b0 = buf[i],\n      b1 = buf[i + 1],\n      b2 = buf[i + 2],\n      b3 = buf[i + 3];\n    str[i * 4 / 3] = base64chars[b0 >>> 2];\n    str[i * 4 / 3 + 1] = base64chars[b0 << 4 & 63 | (b1 || 0) >>> 4];\n    if (i + 1 < buf.length) {\n      str[i * 4 / 3 + 2] = base64chars[b1 << 2 & 63 | (b2 || 0) >>> 6];\n      if (i + 2 < buf.length) {\n        str[i * 4 / 3 + 3] = base64chars[b2 & 63];\n      } else {\n        return str.join(\"\") + \"=\";\n      }\n    } else {\n      return str.join(\"\") + \"==\";\n    }\n  }\n  return str.join(\"\");\n}\nexport var CharCodes;\n(function (CharCodes) {\n  CharCodes[CharCodes[\"NULL\"] = 0] = \"NULL\";\n  CharCodes[CharCodes[\"BELL\"] = 7] = \"BELL\";\n  CharCodes[CharCodes[\"BACKSPACE\"] = 8] = \"BACKSPACE\";\n  CharCodes[CharCodes[\"HORIZONTAL_TAB\"] = 9] = \"HORIZONTAL_TAB\";\n  CharCodes[CharCodes[\"LINE_FEED\"] = 10] = \"LINE_FEED\";\n  CharCodes[CharCodes[\"VERTICAL_TAB\"] = 11] = \"VERTICAL_TAB\";\n  CharCodes[CharCodes[\"FORM_FEED\"] = 12] = \"FORM_FEED\";\n  CharCodes[CharCodes[\"CARRIAGE_RETURN\"] = 13] = \"CARRIAGE_RETURN\";\n  CharCodes[CharCodes[\"DOUBLE_QUOTE\"] = 34] = \"DOUBLE_QUOTE\";\n  CharCodes[CharCodes[\"SINGLE_QUOTE\"] = 39] = \"SINGLE_QUOTE\";\n  CharCodes[CharCodes[\"FORWARD_SLASH\"] = 47] = \"FORWARD_SLASH\";\n  CharCodes[CharCodes[\"QUESTION_MARK\"] = 63] = \"QUESTION_MARK\";\n  CharCodes[CharCodes[\"BACKSLASH\"] = 92] = \"BACKSLASH\";\n  CharCodes[CharCodes[\"LEFT_PARENTHESIS\"] = 40] = \"LEFT_PARENTHESIS\";\n  CharCodes[CharCodes[\"RIGHT_PARENTHESIS\"] = 41] = \"RIGHT_PARENTHESIS\";\n  CharCodes[CharCodes[\"LEFT_BRACE\"] = 123] = \"LEFT_BRACE\";\n  CharCodes[CharCodes[\"RIGHT_BRACE\"] = 125] = \"RIGHT_BRACE\";\n  CharCodes[CharCodes[\"LEFT_BRACKET\"] = 91] = \"LEFT_BRACKET\";\n  CharCodes[CharCodes[\"RIGHT_BRACKET\"] = 93] = \"RIGHT_BRACKET\";\n  CharCodes[CharCodes[\"COMMA\"] = 44] = \"COMMA\";\n  CharCodes[CharCodes[\"SPACE\"] = 32] = \"SPACE\";\n  CharCodes[CharCodes[\"LOWERCASE_X\"] = 120] = \"LOWERCASE_X\";\n  CharCodes[CharCodes[\"COLON\"] = 58] = \"COLON\";\n})(CharCodes || (CharCodes = {}));\nfunction backslashEscape(s) {\n  return [CharCodes.BACKSLASH, s.charCodeAt(0)];\n}\nfunction toCharCodes(s) {\n  const charCodes = new Array(s.length);\n  for (let i = 0; i < s.length; i++) {\n    charCodes[i] = s.charCodeAt(i);\n  }\n  return charCodes;\n}\nconst _HEX_ESCAPE_PREFIX = [CharCodes.BACKSLASH, CharCodes.LOWERCASE_X];\nfunction hexEscape(codePoint) {\n  let hexEscape = codePoint.toString(16);\n  while (hexEscape.length < 2) {\n    hexEscape = \"0\" + hexEscape;\n  }\n  return _HEX_ESCAPE_PREFIX.concat(toCharCodes(hexEscape));\n}\nfunction populateWithHexEscapes(escapes, start, end) {\n  if (end === undefined) {\n    escapes[start] = hexEscape(start);\n  } else {\n    for (let i = start; i < end; i++) {\n      escapes[i] = hexEscape(i);\n    }\n  }\n}\nconst CommonEscapes = {};\nCommonEscapes[CharCodes.NULL] = backslashEscape(\"0\");\npopulateWithHexEscapes(CommonEscapes, 1, 7);\nCommonEscapes[CharCodes.BELL] = backslashEscape(\"a\");\nCommonEscapes[CharCodes.BACKSPACE] = backslashEscape(\"b\");\nCommonEscapes[CharCodes.HORIZONTAL_TAB] = backslashEscape(\"t\");\nCommonEscapes[CharCodes.LINE_FEED] = backslashEscape(\"n\");\nCommonEscapes[CharCodes.VERTICAL_TAB] = backslashEscape(\"v\");\nCommonEscapes[CharCodes.FORM_FEED] = backslashEscape(\"f\");\nCommonEscapes[CharCodes.CARRIAGE_RETURN] = backslashEscape(\"r\");\npopulateWithHexEscapes(CommonEscapes, 14, 32);\nCommonEscapes[CharCodes.BACKSLASH] = backslashEscape(\"\\\\\");\npopulateWithHexEscapes(CommonEscapes, 0x7f, 0xa0);\nexport let ClobEscapes = Object[\"assign\"]({}, CommonEscapes);\nClobEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('\"');\nClobEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape(\"'\");\nClobEscapes[CharCodes.FORWARD_SLASH] = backslashEscape(\"/\");\nClobEscapes[CharCodes.QUESTION_MARK] = backslashEscape(\"?\");\nexport let StringEscapes = Object[\"assign\"]({}, CommonEscapes);\nStringEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('\"');\nexport let SymbolEscapes = Object[\"assign\"]({}, CommonEscapes);\nSymbolEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape(\"'\");\nexport function isIdentifier(s) {\n  if (is_digit(s.charCodeAt(0))) {\n    return false;\n  }\n  for (let i = 0; i < s.length; i++) {\n    const c = s.charCodeAt(i);\n    const b = isIdentifierArray[c];\n    if (!b) {\n      return false;\n    }\n  }\n  return true;\n}\nexport function isOperator(s) {\n  for (let i = 0; i < s.length; i++) {\n    const c = s.charCodeAt(i);\n    const b = _is_operator_char[c];\n    if (!b) {\n      return false;\n    }\n  }\n  return true;\n}\nexport function isDigit(charCode) {\n  return charCode < 58 && charCode > 47;\n}\nexport function escape(input, escapes) {\n  let escapedString = \"\";\n  let escapeSeq = \"\";\n  let charCode;\n  let escape;\n  let lastIndex = 0;\n  for (let i = 0; i < input.length; i++) {\n    charCode = input.charCodeAt(i);\n    escape = escapes[charCode];\n    if (escape !== undefined) {\n      for (let j = 0; j < escape.length; j++) {\n        escapeSeq += String.fromCharCode(escape[j]);\n      }\n      escapedString += input.slice(lastIndex, i) + escapeSeq;\n      lastIndex = i + 1;\n      escapeSeq = \"\";\n    }\n  }\n  return escapedString + input.slice(lastIndex, input.length);\n}", "map": {"version": 3, "names": ["WHITESPACE_COMMENT1", "WHITESPACE_COMMENT2", "ESCAPED_NEWLINE", "DOUBLE_QUOTE", "SINGLE_QUOTE", "SLASH", "_escapeStrings", "_make_bool_array", "str", "i", "length", "a", "charCodeAt", "_is_base64_char", "_is_hex_digit", "_is_letter", "_is_letter_or_digit", "_is_numeric_terminator", "_is_operator_char", "_is_whitespace", "isIdentifierArray", "is_digit", "ch", "is_keyword", "<PERSON><PERSON><PERSON><PERSON>", "s", "esc", "nextEscape", "escapeString", "prev", "needsEscape", "c", "pos", "fixes", "ii", "s2", "push", "fix", "tail_len", "escapeSequence", "substring", "toHex", "len", "char<PERSON>t", "is_letter", "isNumericTerminator", "is_letter_or_digit", "is_operator_char", "is_whitespace", "is_base64_char", "is_hex_digit", "base64chars", "base64inv", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "b", "d", "e", "f", "g", "h", "j", "k", "l", "m", "n", "o", "p", "q", "r", "t", "u", "v", "w", "x", "y", "z", "fromBase64", "pad", "buf", "Uint8Array", "c0", "c1", "c2", "c3", "toBase64", "Array", "Math", "ceil", "b0", "b1", "b2", "b3", "join", "CharCodes", "backslashEscape", "BACKSLASH", "toCharCodes", "charCodes", "_HEX_ESCAPE_PREFIX", "LOWERCASE_X", "hexEscape", "codePoint", "toString", "concat", "populateWithHexEscapes", "escapes", "start", "end", "undefined", "CommonEscapes", "NULL", "BELL", "BACKSPACE", "HORIZONTAL_TAB", "LINE_FEED", "VERTICAL_TAB", "FORM_FEED", "CARRIAGE_RETURN", "ClobEscapes", "Object", "FORWARD_SLASH", "QUESTION_MARK", "StringEscapes", "SymbolEscapes", "isIdentifier", "isOperator", "isDigit", "charCode", "escape", "input", "escapedString", "escapeSeq", "lastIndex", "String", "fromCharCode", "slice"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonText.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport const WHITESPACE_COMMENT1 = -2;\nexport const WHITESPACE_COMMENT2 = -3;\nexport const ESCAPED_NEWLINE = -4;\nconst DOUBLE_QUOTE = 34;\nconst SINGLE_QUOTE = 39;\nconst SLASH = 92;\nconst _escapeStrings = {\n    0: \"\\\\0\",\n    8: \"\\\\b\",\n    9: \"\\\\t\",\n    10: \"\\\\n\",\n    13: \"\\\\r\",\n    DOUBLE_QUOTE: '\\\\\"',\n    SINGLE_QUOTE: \"\\\\'\",\n    SLASH: \"\\\\\\\\\",\n};\nfunction _make_bool_array(str) {\n    let i = str.length;\n    const a = [];\n    a[128] = false;\n    while (i > 0) {\n        --i;\n        a[str.charCodeAt(i)] = true;\n    }\n    return a;\n}\nconst _is_base64_char = _make_bool_array(\"+/0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_hex_digit = _make_bool_array(\"0123456789abcdefABCDEF\");\nconst _is_letter = _make_bool_array(\"_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_letter_or_digit = _make_bool_array(\"_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nconst _is_numeric_terminator = _make_bool_array(\"{}[](),\\\"' \\t\\n\\r\\v\\u000c\");\nconst _is_operator_char = _make_bool_array(\"!#%&*+-./;<=>?@^`|~\");\nconst _is_whitespace = _make_bool_array(\" \\t\\r\\n\\u000b\\u000c\");\nconst isIdentifierArray = _make_bool_array(\"_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\");\nexport function is_digit(ch) {\n    if (ch < 48 || ch > 57) {\n        return false;\n    }\n    return true;\n}\nexport function is_keyword(str) {\n    return (str === \"null\" ||\n        str === \"true\" ||\n        str === \"false\" ||\n        str === \"nan\" ||\n        str === \"+inf\" ||\n        str === \"-inf\");\n}\nexport function asAscii(s) {\n    if (typeof s === \"undefined\") {\n        s = \"undefined::null\";\n    }\n    else if (typeof s == \"number\") {\n        s = \"\" + s;\n    }\n    else if (typeof s != \"string\") {\n        const esc = nextEscape(s, s.length);\n        if (esc >= 0) {\n            s = escapeString(s, esc);\n        }\n    }\n    return s;\n}\nexport function nextEscape(s, prev) {\n    while (prev-- > 0) {\n        if (needsEscape(s.charCodeAt(prev))) {\n            break;\n        }\n    }\n    return prev;\n}\nexport function needsEscape(c) {\n    if (c < 32) {\n        return true;\n    }\n    if (c > 126) {\n        return true;\n    }\n    if (c === DOUBLE_QUOTE || c === SINGLE_QUOTE || c === SLASH) {\n        return true;\n    }\n    return false;\n}\nexport function escapeString(s, pos) {\n    const fixes = [];\n    let c, ii, s2;\n    while (pos >= 0) {\n        c = s.charCodeAt(pos);\n        if (!needsEscape(c)) {\n            break;\n        }\n        fixes.push([pos, c]);\n        pos = nextEscape(s, pos);\n    }\n    if (fixes.length > 0) {\n        s2 = \"\";\n        ii = fixes.length;\n        pos = s.length;\n        while (ii--) {\n            const fix = fixes[ii];\n            const tail_len = pos - fix[0] - 1;\n            if (tail_len > 0) {\n                s2 = escapeSequence(fix[1]) + s.substring(fix[0] + 1, pos) + s2;\n            }\n            else {\n                s2 = s.substring(fix[0] + 1, pos) + s2;\n            }\n            pos = fix[0] - 1;\n        }\n        if (pos >= 0) {\n            s2 = s.substring(0, pos) + s2;\n        }\n        s = s2;\n    }\n    return s;\n}\nexport function escapeSequence(c) {\n    let s = _escapeStrings[c];\n    if (typeof s === \"undefined\") {\n        if (c < 256) {\n            s = \"\\\\x\" + toHex(c, 2);\n        }\n        else if (c <= 0xffff) {\n            s = \"\\\\u\" + toHex(c, 4);\n        }\n        else {\n            s = \"\\\\U\" + toHex(c, 8);\n        }\n    }\n    return s;\n}\nexport function toHex(c, len) {\n    let s = \"\";\n    while (c > 0) {\n        s += \"0123456789ABCDEF\".charAt(c && 0xf);\n        c = c / 16;\n    }\n    if (s.length < len) {\n        s = \"000000000\" + s;\n        s = s.substring(s.length - len, s.length);\n    }\n    return s;\n}\nexport function is_letter(ch) {\n    return _is_letter[ch];\n}\nexport function isNumericTerminator(ch) {\n    if (ch == -1) {\n        return true;\n    }\n    return _is_numeric_terminator[ch];\n}\nexport function is_letter_or_digit(ch) {\n    return _is_letter_or_digit[ch];\n}\nexport function is_operator_char(ch) {\n    return _is_operator_char[ch];\n}\nexport function is_whitespace(ch) {\n    if (ch > 32) {\n        return false;\n    }\n    if (ch == WHITESPACE_COMMENT1) {\n        return true;\n    }\n    if (ch == WHITESPACE_COMMENT2) {\n        return true;\n    }\n    if (ch == ESCAPED_NEWLINE) {\n        return true;\n    }\n    return _is_whitespace[ch];\n}\nexport function is_base64_char(ch) {\n    return _is_base64_char[ch];\n}\nexport function is_hex_digit(ch) {\n    return _is_hex_digit[ch];\n}\nconst base64chars = [\n    \"A\",\n    \"B\",\n    \"C\",\n    \"D\",\n    \"E\",\n    \"F\",\n    \"G\",\n    \"H\",\n    \"I\",\n    \"J\",\n    \"K\",\n    \"L\",\n    \"M\",\n    \"N\",\n    \"O\",\n    \"P\",\n    \"Q\",\n    \"R\",\n    \"S\",\n    \"T\",\n    \"U\",\n    \"V\",\n    \"W\",\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"a\",\n    \"b\",\n    \"c\",\n    \"d\",\n    \"e\",\n    \"f\",\n    \"g\",\n    \"h\",\n    \"i\",\n    \"j\",\n    \"k\",\n    \"l\",\n    \"m\",\n    \"n\",\n    \"o\",\n    \"p\",\n    \"q\",\n    \"r\",\n    \"s\",\n    \"t\",\n    \"u\",\n    \"v\",\n    \"w\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"0\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\",\n    \"8\",\n    \"9\",\n    \"+\",\n    \"/\",\n];\nconst base64inv = {\n    A: 0,\n    B: 1,\n    C: 2,\n    D: 3,\n    E: 4,\n    F: 5,\n    G: 6,\n    H: 7,\n    I: 8,\n    J: 9,\n    K: 10,\n    L: 11,\n    M: 12,\n    N: 13,\n    O: 14,\n    P: 15,\n    Q: 16,\n    R: 17,\n    S: 18,\n    T: 19,\n    U: 20,\n    V: 21,\n    W: 22,\n    X: 23,\n    Y: 24,\n    Z: 25,\n    a: 26,\n    b: 27,\n    c: 28,\n    d: 29,\n    e: 30,\n    f: 31,\n    g: 32,\n    h: 33,\n    i: 34,\n    j: 35,\n    k: 36,\n    l: 37,\n    m: 38,\n    n: 39,\n    o: 40,\n    p: 41,\n    q: 42,\n    r: 43,\n    s: 44,\n    t: 45,\n    u: 46,\n    v: 47,\n    w: 48,\n    x: 49,\n    y: 50,\n    z: 51,\n    \"0\": 52,\n    \"1\": 53,\n    \"2\": 54,\n    \"3\": 55,\n    \"4\": 56,\n    \"5\": 57,\n    \"6\": 58,\n    \"7\": 59,\n    \"8\": 60,\n    \"9\": 61,\n    \"+\": 62,\n    \"/\": 63,\n};\nexport function fromBase64(str) {\n    let pad = 0;\n    for (let i = str.length - 1; str.charAt(i) == \"=\"; i--) {\n        pad++;\n    }\n    const buf = new Uint8Array((str.length * 3) / 4 - pad);\n    for (let i = 0; i < str.length - pad; i += 4) {\n        const c0 = base64inv[str.charAt(i)], c1 = base64inv[str.charAt(i + 1)], c2 = base64inv[str.charAt(i + 2)], c3 = base64inv[str.charAt(i + 3)];\n        buf[(i * 3) / 4] = ((c0 << 2) & 255) | (c1 >>> 4);\n        if (i + 2 < str.length - pad) {\n            buf[(i * 3) / 4 + 1] = ((c1 << 4) & 255) | (c2 >>> 2);\n            if (i + 3 < str.length - pad) {\n                buf[(i * 3) / 4 + 2] = ((c2 << 6) & 255) | c3;\n            }\n        }\n    }\n    return buf;\n}\nexport function toBase64(buf) {\n    const str = new Array(Math.ceil((buf.length * 4) / 3));\n    for (let i = 0; i < buf.length; i += 3) {\n        const b0 = buf[i], b1 = buf[i + 1], b2 = buf[i + 2], b3 = buf[i + 3];\n        str[(i * 4) / 3] = base64chars[b0 >>> 2];\n        str[(i * 4) / 3 + 1] = base64chars[((b0 << 4) & 63) | ((b1 || 0) >>> 4)];\n        if (i + 1 < buf.length) {\n            str[(i * 4) / 3 + 2] = base64chars[((b1 << 2) & 63) | ((b2 || 0) >>> 6)];\n            if (i + 2 < buf.length) {\n                str[(i * 4) / 3 + 3] = base64chars[b2 & 63];\n            }\n            else {\n                return str.join(\"\") + \"=\";\n            }\n        }\n        else {\n            return str.join(\"\") + \"==\";\n        }\n    }\n    return str.join(\"\");\n}\nexport var CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"NULL\"] = 0] = \"NULL\";\n    CharCodes[CharCodes[\"BELL\"] = 7] = \"BELL\";\n    CharCodes[CharCodes[\"BACKSPACE\"] = 8] = \"BACKSPACE\";\n    CharCodes[CharCodes[\"HORIZONTAL_TAB\"] = 9] = \"HORIZONTAL_TAB\";\n    CharCodes[CharCodes[\"LINE_FEED\"] = 10] = \"LINE_FEED\";\n    CharCodes[CharCodes[\"VERTICAL_TAB\"] = 11] = \"VERTICAL_TAB\";\n    CharCodes[CharCodes[\"FORM_FEED\"] = 12] = \"FORM_FEED\";\n    CharCodes[CharCodes[\"CARRIAGE_RETURN\"] = 13] = \"CARRIAGE_RETURN\";\n    CharCodes[CharCodes[\"DOUBLE_QUOTE\"] = 34] = \"DOUBLE_QUOTE\";\n    CharCodes[CharCodes[\"SINGLE_QUOTE\"] = 39] = \"SINGLE_QUOTE\";\n    CharCodes[CharCodes[\"FORWARD_SLASH\"] = 47] = \"FORWARD_SLASH\";\n    CharCodes[CharCodes[\"QUESTION_MARK\"] = 63] = \"QUESTION_MARK\";\n    CharCodes[CharCodes[\"BACKSLASH\"] = 92] = \"BACKSLASH\";\n    CharCodes[CharCodes[\"LEFT_PARENTHESIS\"] = 40] = \"LEFT_PARENTHESIS\";\n    CharCodes[CharCodes[\"RIGHT_PARENTHESIS\"] = 41] = \"RIGHT_PARENTHESIS\";\n    CharCodes[CharCodes[\"LEFT_BRACE\"] = 123] = \"LEFT_BRACE\";\n    CharCodes[CharCodes[\"RIGHT_BRACE\"] = 125] = \"RIGHT_BRACE\";\n    CharCodes[CharCodes[\"LEFT_BRACKET\"] = 91] = \"LEFT_BRACKET\";\n    CharCodes[CharCodes[\"RIGHT_BRACKET\"] = 93] = \"RIGHT_BRACKET\";\n    CharCodes[CharCodes[\"COMMA\"] = 44] = \"COMMA\";\n    CharCodes[CharCodes[\"SPACE\"] = 32] = \"SPACE\";\n    CharCodes[CharCodes[\"LOWERCASE_X\"] = 120] = \"LOWERCASE_X\";\n    CharCodes[CharCodes[\"COLON\"] = 58] = \"COLON\";\n})(CharCodes || (CharCodes = {}));\nfunction backslashEscape(s) {\n    return [CharCodes.BACKSLASH, s.charCodeAt(0)];\n}\nfunction toCharCodes(s) {\n    const charCodes = new Array(s.length);\n    for (let i = 0; i < s.length; i++) {\n        charCodes[i] = s.charCodeAt(i);\n    }\n    return charCodes;\n}\nconst _HEX_ESCAPE_PREFIX = [CharCodes.BACKSLASH, CharCodes.LOWERCASE_X];\nfunction hexEscape(codePoint) {\n    let hexEscape = codePoint.toString(16);\n    while (hexEscape.length < 2) {\n        hexEscape = \"0\" + hexEscape;\n    }\n    return _HEX_ESCAPE_PREFIX.concat(toCharCodes(hexEscape));\n}\nfunction populateWithHexEscapes(escapes, start, end) {\n    if (end === undefined) {\n        escapes[start] = hexEscape(start);\n    }\n    else {\n        for (let i = start; i < end; i++) {\n            escapes[i] = hexEscape(i);\n        }\n    }\n}\nconst CommonEscapes = {};\nCommonEscapes[CharCodes.NULL] = backslashEscape(\"0\");\npopulateWithHexEscapes(CommonEscapes, 1, 7);\nCommonEscapes[CharCodes.BELL] = backslashEscape(\"a\");\nCommonEscapes[CharCodes.BACKSPACE] = backslashEscape(\"b\");\nCommonEscapes[CharCodes.HORIZONTAL_TAB] = backslashEscape(\"t\");\nCommonEscapes[CharCodes.LINE_FEED] = backslashEscape(\"n\");\nCommonEscapes[CharCodes.VERTICAL_TAB] = backslashEscape(\"v\");\nCommonEscapes[CharCodes.FORM_FEED] = backslashEscape(\"f\");\nCommonEscapes[CharCodes.CARRIAGE_RETURN] = backslashEscape(\"r\");\npopulateWithHexEscapes(CommonEscapes, 14, 32);\nCommonEscapes[CharCodes.BACKSLASH] = backslashEscape(\"\\\\\");\npopulateWithHexEscapes(CommonEscapes, 0x7f, 0xa0);\nexport let ClobEscapes = Object[\"assign\"]({}, CommonEscapes);\nClobEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('\"');\nClobEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape(\"'\");\nClobEscapes[CharCodes.FORWARD_SLASH] = backslashEscape(\"/\");\nClobEscapes[CharCodes.QUESTION_MARK] = backslashEscape(\"?\");\nexport let StringEscapes = Object[\"assign\"]({}, CommonEscapes);\nStringEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('\"');\nexport let SymbolEscapes = Object[\"assign\"]({}, CommonEscapes);\nSymbolEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape(\"'\");\nexport function isIdentifier(s) {\n    if (is_digit(s.charCodeAt(0))) {\n        return false;\n    }\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        const b = isIdentifierArray[c];\n        if (!b) {\n            return false;\n        }\n    }\n    return true;\n}\nexport function isOperator(s) {\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        const b = _is_operator_char[c];\n        if (!b) {\n            return false;\n        }\n    }\n    return true;\n}\nexport function isDigit(charCode) {\n    return charCode < 58 && charCode > 47;\n}\nexport function escape(input, escapes) {\n    let escapedString = \"\";\n    let escapeSeq = \"\";\n    let charCode;\n    let escape;\n    let lastIndex = 0;\n    for (let i = 0; i < input.length; i++) {\n        charCode = input.charCodeAt(i);\n        escape = escapes[charCode];\n        if (escape !== undefined) {\n            for (let j = 0; j < escape.length; j++) {\n                escapeSeq += String.fromCharCode(escape[j]);\n            }\n            escapedString += input.slice(lastIndex, i) + escapeSeq;\n            lastIndex = i + 1;\n            escapeSeq = \"\";\n        }\n    }\n    return escapedString + input.slice(lastIndex, input.length);\n}\n//# sourceMappingURL=IonText.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,mBAAmB,GAAG,CAAC,CAAC;AACrC,OAAO,MAAMC,mBAAmB,GAAG,CAAC,CAAC;AACrC,OAAO,MAAMC,eAAe,GAAG,CAAC,CAAC;AACjC,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,cAAc,GAAG;EACnB,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACTH,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE;AACX,CAAC;AACD,SAASE,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIC,CAAC,GAAGD,GAAG,CAACE,MAAM;EAClB,MAAMC,CAAC,GAAG,EAAE;EACZA,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;EACd,OAAOF,CAAC,GAAG,CAAC,EAAE;IACV,EAAEA,CAAC;IACHE,CAAC,CAACH,GAAG,CAACI,UAAU,CAACH,CAAC,CAAC,CAAC,GAAG,IAAI;EAC/B;EACA,OAAOE,CAAC;AACZ;AACA,MAAME,eAAe,GAAGN,gBAAgB,CAAC,kEAAkE,CAAC;AAC5G,MAAMO,aAAa,GAAGP,gBAAgB,CAAC,wBAAwB,CAAC;AAChE,MAAMQ,UAAU,GAAGR,gBAAgB,CAAC,wDAAwD,CAAC;AAC7F,MAAMS,mBAAmB,GAAGT,gBAAgB,CAAC,kEAAkE,CAAC;AAChH,MAAMU,sBAAsB,GAAGV,gBAAgB,CAAC,2BAA2B,CAAC;AAC5E,MAAMW,iBAAiB,GAAGX,gBAAgB,CAAC,qBAAqB,CAAC;AACjE,MAAMY,cAAc,GAAGZ,gBAAgB,CAAC,qBAAqB,CAAC;AAC9D,MAAMa,iBAAiB,GAAGb,gBAAgB,CAAC,kEAAkE,CAAC;AAC9G,OAAO,SAASc,QAAQA,CAACC,EAAE,EAAE;EACzB,IAAIA,EAAE,GAAG,EAAE,IAAIA,EAAE,GAAG,EAAE,EAAE;IACpB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,OAAO,SAASC,UAAUA,CAACf,GAAG,EAAE;EAC5B,OAAQA,GAAG,KAAK,MAAM,IAClBA,GAAG,KAAK,MAAM,IACdA,GAAG,KAAK,OAAO,IACfA,GAAG,KAAK,KAAK,IACbA,GAAG,KAAK,MAAM,IACdA,GAAG,KAAK,MAAM;AACtB;AACA,OAAO,SAASgB,OAAOA,CAACC,CAAC,EAAE;EACvB,IAAI,OAAOA,CAAC,KAAK,WAAW,EAAE;IAC1BA,CAAC,GAAG,iBAAiB;EACzB,CAAC,MACI,IAAI,OAAOA,CAAC,IAAI,QAAQ,EAAE;IAC3BA,CAAC,GAAG,EAAE,GAAGA,CAAC;EACd,CAAC,MACI,IAAI,OAAOA,CAAC,IAAI,QAAQ,EAAE;IAC3B,MAAMC,GAAG,GAAGC,UAAU,CAACF,CAAC,EAAEA,CAAC,CAACf,MAAM,CAAC;IACnC,IAAIgB,GAAG,IAAI,CAAC,EAAE;MACVD,CAAC,GAAGG,YAAY,CAACH,CAAC,EAAEC,GAAG,CAAC;IAC5B;EACJ;EACA,OAAOD,CAAC;AACZ;AACA,OAAO,SAASE,UAAUA,CAACF,CAAC,EAAEI,IAAI,EAAE;EAChC,OAAOA,IAAI,EAAE,GAAG,CAAC,EAAE;IACf,IAAIC,WAAW,CAACL,CAAC,CAACb,UAAU,CAACiB,IAAI,CAAC,CAAC,EAAE;MACjC;IACJ;EACJ;EACA,OAAOA,IAAI;AACf;AACA,OAAO,SAASC,WAAWA,CAACC,CAAC,EAAE;EAC3B,IAAIA,CAAC,GAAG,EAAE,EAAE;IACR,OAAO,IAAI;EACf;EACA,IAAIA,CAAC,GAAG,GAAG,EAAE;IACT,OAAO,IAAI;EACf;EACA,IAAIA,CAAC,KAAK5B,YAAY,IAAI4B,CAAC,KAAK3B,YAAY,IAAI2B,CAAC,KAAK1B,KAAK,EAAE;IACzD,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASuB,YAAYA,CAACH,CAAC,EAAEO,GAAG,EAAE;EACjC,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIF,CAAC,EAAEG,EAAE,EAAEC,EAAE;EACb,OAAOH,GAAG,IAAI,CAAC,EAAE;IACbD,CAAC,GAAGN,CAAC,CAACb,UAAU,CAACoB,GAAG,CAAC;IACrB,IAAI,CAACF,WAAW,CAACC,CAAC,CAAC,EAAE;MACjB;IACJ;IACAE,KAAK,CAACG,IAAI,CAAC,CAACJ,GAAG,EAAED,CAAC,CAAC,CAAC;IACpBC,GAAG,GAAGL,UAAU,CAACF,CAAC,EAAEO,GAAG,CAAC;EAC5B;EACA,IAAIC,KAAK,CAACvB,MAAM,GAAG,CAAC,EAAE;IAClByB,EAAE,GAAG,EAAE;IACPD,EAAE,GAAGD,KAAK,CAACvB,MAAM;IACjBsB,GAAG,GAAGP,CAAC,CAACf,MAAM;IACd,OAAOwB,EAAE,EAAE,EAAE;MACT,MAAMG,GAAG,GAAGJ,KAAK,CAACC,EAAE,CAAC;MACrB,MAAMI,QAAQ,GAAGN,GAAG,GAAGK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACjC,IAAIC,QAAQ,GAAG,CAAC,EAAE;QACdH,EAAE,GAAGI,cAAc,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGZ,CAAC,CAACe,SAAS,CAACH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEL,GAAG,CAAC,GAAGG,EAAE;MACnE,CAAC,MACI;QACDA,EAAE,GAAGV,CAAC,CAACe,SAAS,CAACH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEL,GAAG,CAAC,GAAGG,EAAE;MAC1C;MACAH,GAAG,GAAGK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACpB;IACA,IAAIL,GAAG,IAAI,CAAC,EAAE;MACVG,EAAE,GAAGV,CAAC,CAACe,SAAS,CAAC,CAAC,EAAER,GAAG,CAAC,GAAGG,EAAE;IACjC;IACAV,CAAC,GAAGU,EAAE;EACV;EACA,OAAOV,CAAC;AACZ;AACA,OAAO,SAASc,cAAcA,CAACR,CAAC,EAAE;EAC9B,IAAIN,CAAC,GAAGnB,cAAc,CAACyB,CAAC,CAAC;EACzB,IAAI,OAAON,CAAC,KAAK,WAAW,EAAE;IAC1B,IAAIM,CAAC,GAAG,GAAG,EAAE;MACTN,CAAC,GAAG,KAAK,GAAGgB,KAAK,CAACV,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,MACI,IAAIA,CAAC,IAAI,MAAM,EAAE;MAClBN,CAAC,GAAG,KAAK,GAAGgB,KAAK,CAACV,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,MACI;MACDN,CAAC,GAAG,KAAK,GAAGgB,KAAK,CAACV,CAAC,EAAE,CAAC,CAAC;IAC3B;EACJ;EACA,OAAON,CAAC;AACZ;AACA,OAAO,SAASgB,KAAKA,CAACV,CAAC,EAAEW,GAAG,EAAE;EAC1B,IAAIjB,CAAC,GAAG,EAAE;EACV,OAAOM,CAAC,GAAG,CAAC,EAAE;IACVN,CAAC,IAAI,kBAAkB,CAACkB,MAAM,CAACZ,CAAC,IAAI,GAAG,CAAC;IACxCA,CAAC,GAAGA,CAAC,GAAG,EAAE;EACd;EACA,IAAIN,CAAC,CAACf,MAAM,GAAGgC,GAAG,EAAE;IAChBjB,CAAC,GAAG,WAAW,GAAGA,CAAC;IACnBA,CAAC,GAAGA,CAAC,CAACe,SAAS,CAACf,CAAC,CAACf,MAAM,GAAGgC,GAAG,EAAEjB,CAAC,CAACf,MAAM,CAAC;EAC7C;EACA,OAAOe,CAAC;AACZ;AACA,OAAO,SAASmB,SAASA,CAACtB,EAAE,EAAE;EAC1B,OAAOP,UAAU,CAACO,EAAE,CAAC;AACzB;AACA,OAAO,SAASuB,mBAAmBA,CAACvB,EAAE,EAAE;EACpC,IAAIA,EAAE,IAAI,CAAC,CAAC,EAAE;IACV,OAAO,IAAI;EACf;EACA,OAAOL,sBAAsB,CAACK,EAAE,CAAC;AACrC;AACA,OAAO,SAASwB,kBAAkBA,CAACxB,EAAE,EAAE;EACnC,OAAON,mBAAmB,CAACM,EAAE,CAAC;AAClC;AACA,OAAO,SAASyB,gBAAgBA,CAACzB,EAAE,EAAE;EACjC,OAAOJ,iBAAiB,CAACI,EAAE,CAAC;AAChC;AACA,OAAO,SAAS0B,aAAaA,CAAC1B,EAAE,EAAE;EAC9B,IAAIA,EAAE,GAAG,EAAE,EAAE;IACT,OAAO,KAAK;EAChB;EACA,IAAIA,EAAE,IAAItB,mBAAmB,EAAE;IAC3B,OAAO,IAAI;EACf;EACA,IAAIsB,EAAE,IAAIrB,mBAAmB,EAAE;IAC3B,OAAO,IAAI;EACf;EACA,IAAIqB,EAAE,IAAIpB,eAAe,EAAE;IACvB,OAAO,IAAI;EACf;EACA,OAAOiB,cAAc,CAACG,EAAE,CAAC;AAC7B;AACA,OAAO,SAAS2B,cAAcA,CAAC3B,EAAE,EAAE;EAC/B,OAAOT,eAAe,CAACS,EAAE,CAAC;AAC9B;AACA,OAAO,SAAS4B,YAAYA,CAAC5B,EAAE,EAAE;EAC7B,OAAOR,aAAa,CAACQ,EAAE,CAAC;AAC5B;AACA,MAAM6B,WAAW,GAAG,CAChB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACN;AACD,MAAMC,SAAS,GAAG;EACdC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLnE,CAAC,EAAE,EAAE;EACLoE,CAAC,EAAE,EAAE;EACLhD,CAAC,EAAE,EAAE;EACLiD,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACL3E,CAAC,EAAE,EAAE;EACL4E,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLpE,CAAC,EAAE,EAAE;EACLqE,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACL,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE;AACT,CAAC;AACD,OAAO,SAASC,UAAUA,CAAC7F,GAAG,EAAE;EAC5B,IAAI8F,GAAG,GAAG,CAAC;EACX,KAAK,IAAI7F,CAAC,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC,EAAEF,GAAG,CAACmC,MAAM,CAAClC,CAAC,CAAC,IAAI,GAAG,EAAEA,CAAC,EAAE,EAAE;IACpD6F,GAAG,EAAE;EACT;EACA,MAAMC,GAAG,GAAG,IAAIC,UAAU,CAAEhG,GAAG,CAACE,MAAM,GAAG,CAAC,GAAI,CAAC,GAAG4F,GAAG,CAAC;EACtD,KAAK,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,MAAM,GAAG4F,GAAG,EAAE7F,CAAC,IAAI,CAAC,EAAE;IAC1C,MAAMgG,EAAE,GAAGrD,SAAS,CAAC5C,GAAG,CAACmC,MAAM,CAAClC,CAAC,CAAC,CAAC;MAAEiG,EAAE,GAAGtD,SAAS,CAAC5C,GAAG,CAACmC,MAAM,CAAClC,CAAC,GAAG,CAAC,CAAC,CAAC;MAAEkG,EAAE,GAAGvD,SAAS,CAAC5C,GAAG,CAACmC,MAAM,CAAClC,CAAC,GAAG,CAAC,CAAC,CAAC;MAAEmG,EAAE,GAAGxD,SAAS,CAAC5C,GAAG,CAACmC,MAAM,CAAClC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5I8F,GAAG,CAAE9F,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAKgG,EAAE,IAAI,CAAC,GAAI,GAAG,GAAKC,EAAE,KAAK,CAAE;IACjD,IAAIjG,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,MAAM,GAAG4F,GAAG,EAAE;MAC1BC,GAAG,CAAE9F,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,GAAKiG,EAAE,IAAI,CAAC,GAAI,GAAG,GAAKC,EAAE,KAAK,CAAE;MACrD,IAAIlG,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,MAAM,GAAG4F,GAAG,EAAE;QAC1BC,GAAG,CAAE9F,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,GAAKkG,EAAE,IAAI,CAAC,GAAI,GAAG,GAAIC,EAAE;MACjD;IACJ;EACJ;EACA,OAAOL,GAAG;AACd;AACA,OAAO,SAASM,QAAQA,CAACN,GAAG,EAAE;EAC1B,MAAM/F,GAAG,GAAG,IAAIsG,KAAK,CAACC,IAAI,CAACC,IAAI,CAAET,GAAG,CAAC7F,MAAM,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC;EACtD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,GAAG,CAAC7F,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACpC,MAAMwG,EAAE,GAAGV,GAAG,CAAC9F,CAAC,CAAC;MAAEyG,EAAE,GAAGX,GAAG,CAAC9F,CAAC,GAAG,CAAC,CAAC;MAAE0G,EAAE,GAAGZ,GAAG,CAAC9F,CAAC,GAAG,CAAC,CAAC;MAAE2G,EAAE,GAAGb,GAAG,CAAC9F,CAAC,GAAG,CAAC,CAAC;IACpED,GAAG,CAAEC,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG0C,WAAW,CAAC8D,EAAE,KAAK,CAAC,CAAC;IACxCzG,GAAG,CAAEC,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,GAAG0C,WAAW,CAAG8D,EAAE,IAAI,CAAC,GAAI,EAAE,GAAK,CAACC,EAAE,IAAI,CAAC,MAAM,CAAE,CAAC;IACxE,IAAIzG,CAAC,GAAG,CAAC,GAAG8F,GAAG,CAAC7F,MAAM,EAAE;MACpBF,GAAG,CAAEC,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,GAAG0C,WAAW,CAAG+D,EAAE,IAAI,CAAC,GAAI,EAAE,GAAK,CAACC,EAAE,IAAI,CAAC,MAAM,CAAE,CAAC;MACxE,IAAI1G,CAAC,GAAG,CAAC,GAAG8F,GAAG,CAAC7F,MAAM,EAAE;QACpBF,GAAG,CAAEC,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,GAAG0C,WAAW,CAACgE,EAAE,GAAG,EAAE,CAAC;MAC/C,CAAC,MACI;QACD,OAAO3G,GAAG,CAAC6G,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;MAC7B;IACJ,CAAC,MACI;MACD,OAAO7G,GAAG,CAAC6G,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;IAC9B;EACJ;EACA,OAAO7G,GAAG,CAAC6G,IAAI,CAAC,EAAE,CAAC;AACvB;AACA,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzCA,SAAS,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzCA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACnDA,SAAS,CAACA,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC7DA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;EACpDA,SAAS,CAACA,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EAC1DA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;EACpDA,SAAS,CAACA,SAAS,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,GAAG,iBAAiB;EAChEA,SAAS,CAACA,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EAC1DA,SAAS,CAACA,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EAC1DA,SAAS,CAACA,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,eAAe;EAC5DA,SAAS,CAACA,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,eAAe;EAC5DA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;EACpDA,SAAS,CAACA,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,GAAG,kBAAkB;EAClEA,SAAS,CAACA,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC,GAAG,mBAAmB;EACpEA,SAAS,CAACA,SAAS,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACvDA,SAAS,CAACA,SAAS,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACzDA,SAAS,CAACA,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EAC1DA,SAAS,CAACA,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,eAAe;EAC5DA,SAAS,CAACA,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EAC5CA,SAAS,CAACA,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EAC5CA,SAAS,CAACA,SAAS,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACzDA,SAAS,CAACA,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;AAChD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,SAASC,eAAeA,CAAC9F,CAAC,EAAE;EACxB,OAAO,CAAC6F,SAAS,CAACE,SAAS,EAAE/F,CAAC,CAACb,UAAU,CAAC,CAAC,CAAC,CAAC;AACjD;AACA,SAAS6G,WAAWA,CAAChG,CAAC,EAAE;EACpB,MAAMiG,SAAS,GAAG,IAAIZ,KAAK,CAACrF,CAAC,CAACf,MAAM,CAAC;EACrC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,CAAC,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/BiH,SAAS,CAACjH,CAAC,CAAC,GAAGgB,CAAC,CAACb,UAAU,CAACH,CAAC,CAAC;EAClC;EACA,OAAOiH,SAAS;AACpB;AACA,MAAMC,kBAAkB,GAAG,CAACL,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACM,WAAW,CAAC;AACvE,SAASC,SAASA,CAACC,SAAS,EAAE;EAC1B,IAAID,SAAS,GAAGC,SAAS,CAACC,QAAQ,CAAC,EAAE,CAAC;EACtC,OAAOF,SAAS,CAACnH,MAAM,GAAG,CAAC,EAAE;IACzBmH,SAAS,GAAG,GAAG,GAAGA,SAAS;EAC/B;EACA,OAAOF,kBAAkB,CAACK,MAAM,CAACP,WAAW,CAACI,SAAS,CAAC,CAAC;AAC5D;AACA,SAASI,sBAAsBA,CAACC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACjD,IAAIA,GAAG,KAAKC,SAAS,EAAE;IACnBH,OAAO,CAACC,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAAC;EACrC,CAAC,MACI;IACD,KAAK,IAAI1H,CAAC,GAAG0H,KAAK,EAAE1H,CAAC,GAAG2H,GAAG,EAAE3H,CAAC,EAAE,EAAE;MAC9ByH,OAAO,CAACzH,CAAC,CAAC,GAAGoH,SAAS,CAACpH,CAAC,CAAC;IAC7B;EACJ;AACJ;AACA,MAAM6H,aAAa,GAAG,CAAC,CAAC;AACxBA,aAAa,CAAChB,SAAS,CAACiB,IAAI,CAAC,GAAGhB,eAAe,CAAC,GAAG,CAAC;AACpDU,sBAAsB,CAACK,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3CA,aAAa,CAAChB,SAAS,CAACkB,IAAI,CAAC,GAAGjB,eAAe,CAAC,GAAG,CAAC;AACpDe,aAAa,CAAChB,SAAS,CAACmB,SAAS,CAAC,GAAGlB,eAAe,CAAC,GAAG,CAAC;AACzDe,aAAa,CAAChB,SAAS,CAACoB,cAAc,CAAC,GAAGnB,eAAe,CAAC,GAAG,CAAC;AAC9De,aAAa,CAAChB,SAAS,CAACqB,SAAS,CAAC,GAAGpB,eAAe,CAAC,GAAG,CAAC;AACzDe,aAAa,CAAChB,SAAS,CAACsB,YAAY,CAAC,GAAGrB,eAAe,CAAC,GAAG,CAAC;AAC5De,aAAa,CAAChB,SAAS,CAACuB,SAAS,CAAC,GAAGtB,eAAe,CAAC,GAAG,CAAC;AACzDe,aAAa,CAAChB,SAAS,CAACwB,eAAe,CAAC,GAAGvB,eAAe,CAAC,GAAG,CAAC;AAC/DU,sBAAsB,CAACK,aAAa,EAAE,EAAE,EAAE,EAAE,CAAC;AAC7CA,aAAa,CAAChB,SAAS,CAACE,SAAS,CAAC,GAAGD,eAAe,CAAC,IAAI,CAAC;AAC1DU,sBAAsB,CAACK,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;AACjD,OAAO,IAAIS,WAAW,GAAGC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEV,aAAa,CAAC;AAC5DS,WAAW,CAACzB,SAAS,CAACnH,YAAY,CAAC,GAAGoH,eAAe,CAAC,GAAG,CAAC;AAC1DwB,WAAW,CAACzB,SAAS,CAAClH,YAAY,CAAC,GAAGmH,eAAe,CAAC,GAAG,CAAC;AAC1DwB,WAAW,CAACzB,SAAS,CAAC2B,aAAa,CAAC,GAAG1B,eAAe,CAAC,GAAG,CAAC;AAC3DwB,WAAW,CAACzB,SAAS,CAAC4B,aAAa,CAAC,GAAG3B,eAAe,CAAC,GAAG,CAAC;AAC3D,OAAO,IAAI4B,aAAa,GAAGH,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEV,aAAa,CAAC;AAC9Da,aAAa,CAAC7B,SAAS,CAACnH,YAAY,CAAC,GAAGoH,eAAe,CAAC,GAAG,CAAC;AAC5D,OAAO,IAAI6B,aAAa,GAAGJ,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEV,aAAa,CAAC;AAC9Dc,aAAa,CAAC9B,SAAS,CAAClH,YAAY,CAAC,GAAGmH,eAAe,CAAC,GAAG,CAAC;AAC5D,OAAO,SAAS8B,YAAYA,CAAC5H,CAAC,EAAE;EAC5B,IAAIJ,QAAQ,CAACI,CAAC,CAACb,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,OAAO,KAAK;EAChB;EACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,CAAC,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/B,MAAMsB,CAAC,GAAGN,CAAC,CAACb,UAAU,CAACH,CAAC,CAAC;IACzB,MAAMsE,CAAC,GAAG3D,iBAAiB,CAACW,CAAC,CAAC;IAC9B,IAAI,CAACgD,CAAC,EAAE;MACJ,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,OAAO,SAASuE,UAAUA,CAAC7H,CAAC,EAAE;EAC1B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,CAAC,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/B,MAAMsB,CAAC,GAAGN,CAAC,CAACb,UAAU,CAACH,CAAC,CAAC;IACzB,MAAMsE,CAAC,GAAG7D,iBAAiB,CAACa,CAAC,CAAC;IAC9B,IAAI,CAACgD,CAAC,EAAE;MACJ,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,OAAO,SAASwE,OAAOA,CAACC,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,GAAG,EAAE,IAAIA,QAAQ,GAAG,EAAE;AACzC;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAExB,OAAO,EAAE;EACnC,IAAIyB,aAAa,GAAG,EAAE;EACtB,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIJ,QAAQ;EACZ,IAAIC,MAAM;EACV,IAAII,SAAS,GAAG,CAAC;EACjB,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiJ,KAAK,CAAChJ,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC+I,QAAQ,GAAGE,KAAK,CAAC9I,UAAU,CAACH,CAAC,CAAC;IAC9BgJ,MAAM,GAAGvB,OAAO,CAACsB,QAAQ,CAAC;IAC1B,IAAIC,MAAM,KAAKpB,SAAS,EAAE;MACtB,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,MAAM,CAAC/I,MAAM,EAAE2E,CAAC,EAAE,EAAE;QACpCuE,SAAS,IAAIE,MAAM,CAACC,YAAY,CAACN,MAAM,CAACpE,CAAC,CAAC,CAAC;MAC/C;MACAsE,aAAa,IAAID,KAAK,CAACM,KAAK,CAACH,SAAS,EAAEpJ,CAAC,CAAC,GAAGmJ,SAAS;MACtDC,SAAS,GAAGpJ,CAAC,GAAG,CAAC;MACjBmJ,SAAS,GAAG,EAAE;IAClB;EACJ;EACA,OAAOD,aAAa,GAAGD,KAAK,CAACM,KAAK,CAACH,SAAS,EAAEH,KAAK,CAAChJ,MAAM,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}