{"ast": null, "code": "import { _hasValue } from \"../util\";\nimport * as JsValueConversion from \"./JsValueConversion\";\nconst _DOM_VALUE_SIGNET = Symbol(\"ion.dom.Value\");\nexport function Value(BaseClass, ionType, fromJsConstructor) {\n  const newClass = class extends BaseClass {\n    constructor(...args) {\n      super(...args);\n      this._ionType = ionType;\n      this._ionAnnotations = [];\n      Object.defineProperty(this, \"_ionType\", {\n        enumerable: false\n      });\n      Object.defineProperty(this, \"_ionAnnotations\", {\n        enumerable: false\n      });\n    }\n    _unsupportedOperation(functionName) {\n      throw new Error(`Value#${functionName}() is not supported by Ion type ${this.getType().name}`);\n    }\n    getType() {\n      return this._ionType;\n    }\n    _setAnnotations(annotations) {\n      this._ionAnnotations = annotations;\n    }\n    getAnnotations() {\n      if (this._ionAnnotations === null) {\n        return [];\n      }\n      return this._ionAnnotations;\n    }\n    isNull() {\n      return false;\n    }\n    booleanValue() {\n      this._unsupportedOperation(\"booleanValue\");\n    }\n    numberValue() {\n      this._unsupportedOperation(\"numberValue\");\n    }\n    bigIntValue() {\n      this._unsupportedOperation(\"bigIntValue\");\n    }\n    decimalValue() {\n      this._unsupportedOperation(\"decimalValue\");\n    }\n    stringValue() {\n      this._unsupportedOperation(\"stringValue\");\n    }\n    dateValue() {\n      this._unsupportedOperation(\"dateValue\");\n    }\n    timestampValue() {\n      this._unsupportedOperation(\"timestampValue\");\n    }\n    uInt8ArrayValue() {\n      this._unsupportedOperation(\"uInt8ArrayValue\");\n    }\n    fieldNames() {\n      this._unsupportedOperation(\"fieldNames\");\n    }\n    fields() {\n      this._unsupportedOperation(\"fields\");\n    }\n    allFields() {\n      this._unsupportedOperation(\"allFields\");\n    }\n    elements() {\n      this._unsupportedOperation(\"elements\");\n    }\n    get(...pathElements) {\n      this._unsupportedOperation(\"get\");\n    }\n    getAll(...pathElements) {\n      this._unsupportedOperation(\"getAll\");\n    }\n    as(ionValueType) {\n      if (this instanceof ionValueType) {\n        return this;\n      }\n      throw new Error(`${this.constructor.name} is not an instance of ${ionValueType.name}`);\n    }\n    writeTo(writer) {\n      this._unsupportedOperation(\"writeTo\");\n    }\n    deleteField(name) {\n      this._unsupportedOperation(\"deleteField\");\n    }\n    _valueEquals(other, options = {\n      epsilon: null,\n      ignoreAnnotations: false,\n      ignoreTimestampPrecision: false,\n      onlyCompareIon: true,\n      coerceNumericType: false\n    }) {\n      this._unsupportedOperation(\"_valueEquals\");\n    }\n    equals(other, options = {\n      epsilon: null\n    }) {\n      let onlyCompareIon = false;\n      if (other instanceof Value) {\n        onlyCompareIon = true;\n      }\n      return this._valueEquals(other, {\n        onlyCompareIon: onlyCompareIon,\n        ignoreTimestampPrecision: true,\n        ignoreAnnotations: true,\n        epsilon: options.epsilon,\n        coerceNumericType: true\n      });\n    }\n    ionEquals(other, options = {\n      epsilon: null,\n      ignoreAnnotations: false,\n      ignoreTimestampPrecision: false\n    }) {\n      if (!options.ignoreAnnotations) {\n        if (!(other instanceof Value)) {\n          return false;\n        }\n        let actualAnnotations = this.getAnnotations();\n        let expectedAnnotations = other.getAnnotations();\n        if (actualAnnotations.length !== expectedAnnotations.length) {\n          return false;\n        }\n        for (let i = 0; i < actualAnnotations.length; i++) {\n          if (actualAnnotations[i].localeCompare(expectedAnnotations[i]) !== 0) {\n            return false;\n          }\n        }\n      }\n      let ion_options = {\n        onlyCompareIon: true,\n        ignoreTimestampPrecision: options.ignoreTimestampPrecision,\n        epsilon: options.epsilon,\n        coerceNumericType: false\n      };\n      return this._valueEquals(other, ion_options);\n    }\n    static _getIonType() {\n      return ionType;\n    }\n    static _fromJsValue(jsValue, annotations) {\n      return fromJsConstructor.construct(this, jsValue, annotations);\n    }\n  };\n  Object.defineProperty(newClass, _DOM_VALUE_SIGNET, {\n    writable: false,\n    enumerable: false,\n    value: _DOM_VALUE_SIGNET\n  });\n  return newClass;\n}\n(function (Value) {\n  function from(value, annotations) {\n    if (value instanceof Value) {\n      if (_hasValue(annotations)) {\n        throw new Error(\"Value.from() does not support overriding the annotations on a dom.Value\" + \" passed as an argument.\");\n      }\n      return value;\n    }\n    return JsValueConversion._ionValueFromJsValue(value, annotations);\n  }\n  Value.from = from;\n})(Value || (Value = {}));\nObject.defineProperty(Value, Symbol.hasInstance, {\n  get: () => instance => {\n    return _hasValue(instance) && _hasValue(instance.constructor) && _DOM_VALUE_SIGNET in instance.constructor && instance.constructor[_DOM_VALUE_SIGNET] === _DOM_VALUE_SIGNET;\n  }\n});", "map": {"version": 3, "names": ["_hasValue", "JsValueConversion", "_DOM_VALUE_SIGNET", "Symbol", "Value", "BaseClass", "ionType", "fromJsConstructor", "newClass", "constructor", "args", "_ionType", "_ionAnnotations", "Object", "defineProperty", "enumerable", "_unsupportedOperation", "functionName", "Error", "getType", "name", "_setAnnotations", "annotations", "getAnnotations", "isNull", "booleanValue", "numberValue", "bigIntValue", "decimalValue", "stringValue", "dateValue", "timestampValue", "uInt8ArrayValue", "fieldNames", "fields", "allFields", "elements", "get", "pathElements", "getAll", "as", "ionValueType", "writeTo", "writer", "deleteField", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "coerceNumericType", "equals", "ionEquals", "actualAnnotations", "expectedAnnotations", "length", "i", "localeCompare", "ion_options", "_getIonType", "_fromJsValue", "jsValue", "construct", "writable", "value", "from", "_ionValueFromJsValue", "hasInstance", "instance"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Value.js"], "sourcesContent": ["import { _hasValue } from \"../util\";\nimport * as JsValueConversion from \"./JsValueConversion\";\nconst _DOM_VALUE_SIGNET = Symbol(\"ion.dom.Value\");\nexport function Value(BaseClass, ionType, fromJsConstructor) {\n    const newClass = class extends BaseClass {\n        constructor(...args) {\n            super(...args);\n            this._ionType = ionType;\n            this._ionAnnotations = [];\n            Object.defineProperty(this, \"_ionType\", { enumerable: false });\n            Object.defineProperty(this, \"_ionAnnotations\", { enumerable: false });\n        }\n        _unsupportedOperation(functionName) {\n            throw new Error(`Value#${functionName}() is not supported by Ion type ${this.getType().name}`);\n        }\n        getType() {\n            return this._ionType;\n        }\n        _setAnnotations(annotations) {\n            this._ionAnnotations = annotations;\n        }\n        getAnnotations() {\n            if (this._ionAnnotations === null) {\n                return [];\n            }\n            return this._ionAnnotations;\n        }\n        isNull() {\n            return false;\n        }\n        booleanValue() {\n            this._unsupportedOperation(\"booleanValue\");\n        }\n        numberValue() {\n            this._unsupportedOperation(\"numberValue\");\n        }\n        bigIntValue() {\n            this._unsupportedOperation(\"bigIntValue\");\n        }\n        decimalValue() {\n            this._unsupportedOperation(\"decimalValue\");\n        }\n        stringValue() {\n            this._unsupportedOperation(\"stringValue\");\n        }\n        dateValue() {\n            this._unsupportedOperation(\"dateValue\");\n        }\n        timestampValue() {\n            this._unsupportedOperation(\"timestampValue\");\n        }\n        uInt8ArrayValue() {\n            this._unsupportedOperation(\"uInt8ArrayValue\");\n        }\n        fieldNames() {\n            this._unsupportedOperation(\"fieldNames\");\n        }\n        fields() {\n            this._unsupportedOperation(\"fields\");\n        }\n        allFields() {\n            this._unsupportedOperation(\"allFields\");\n        }\n        elements() {\n            this._unsupportedOperation(\"elements\");\n        }\n        get(...pathElements) {\n            this._unsupportedOperation(\"get\");\n        }\n        getAll(...pathElements) {\n            this._unsupportedOperation(\"getAll\");\n        }\n        as(ionValueType) {\n            if (this instanceof ionValueType) {\n                return this;\n            }\n            throw new Error(`${this.constructor.name} is not an instance of ${ionValueType.name}`);\n        }\n        writeTo(writer) {\n            this._unsupportedOperation(\"writeTo\");\n        }\n        deleteField(name) {\n            this._unsupportedOperation(\"deleteField\");\n        }\n        _valueEquals(other, options = {\n            epsilon: null,\n            ignoreAnnotations: false,\n            ignoreTimestampPrecision: false,\n            onlyCompareIon: true,\n            coerceNumericType: false,\n        }) {\n            this._unsupportedOperation(\"_valueEquals\");\n        }\n        equals(other, options = { epsilon: null }) {\n            let onlyCompareIon = false;\n            if (other instanceof Value) {\n                onlyCompareIon = true;\n            }\n            return this._valueEquals(other, {\n                onlyCompareIon: onlyCompareIon,\n                ignoreTimestampPrecision: true,\n                ignoreAnnotations: true,\n                epsilon: options.epsilon,\n                coerceNumericType: true,\n            });\n        }\n        ionEquals(other, options = {\n            epsilon: null,\n            ignoreAnnotations: false,\n            ignoreTimestampPrecision: false,\n        }) {\n            if (!options.ignoreAnnotations) {\n                if (!(other instanceof Value)) {\n                    return false;\n                }\n                let actualAnnotations = this.getAnnotations();\n                let expectedAnnotations = other.getAnnotations();\n                if (actualAnnotations.length !== expectedAnnotations.length) {\n                    return false;\n                }\n                for (let i = 0; i < actualAnnotations.length; i++) {\n                    if (actualAnnotations[i].localeCompare(expectedAnnotations[i]) !== 0) {\n                        return false;\n                    }\n                }\n            }\n            let ion_options = {\n                onlyCompareIon: true,\n                ignoreTimestampPrecision: options.ignoreTimestampPrecision,\n                epsilon: options.epsilon,\n                coerceNumericType: false,\n            };\n            return this._valueEquals(other, ion_options);\n        }\n        static _getIonType() {\n            return ionType;\n        }\n        static _fromJsValue(jsValue, annotations) {\n            return fromJsConstructor.construct(this, jsValue, annotations);\n        }\n    };\n    Object.defineProperty(newClass, _DOM_VALUE_SIGNET, {\n        writable: false,\n        enumerable: false,\n        value: _DOM_VALUE_SIGNET,\n    });\n    return newClass;\n}\n(function (Value) {\n    function from(value, annotations) {\n        if (value instanceof Value) {\n            if (_hasValue(annotations)) {\n                throw new Error(\"Value.from() does not support overriding the annotations on a dom.Value\" +\n                    \" passed as an argument.\");\n            }\n            return value;\n        }\n        return JsValueConversion._ionValueFromJsValue(value, annotations);\n    }\n    Value.from = from;\n})(Value || (Value = {}));\nObject.defineProperty(Value, Symbol.hasInstance, {\n    get: () => (instance) => {\n        return (_hasValue(instance) &&\n            _hasValue(instance.constructor) &&\n            _DOM_VALUE_SIGNET in instance.constructor &&\n            instance.constructor[_DOM_VALUE_SIGNET] === _DOM_VALUE_SIGNET);\n    },\n});\n//# sourceMappingURL=Value.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,SAAS;AACnC,OAAO,KAAKC,iBAAiB,MAAM,qBAAqB;AACxD,MAAMC,iBAAiB,GAAGC,MAAM,CAAC,eAAe,CAAC;AACjD,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;EACzD,MAAMC,QAAQ,GAAG,cAAcH,SAAS,CAAC;IACrCI,WAAWA,CAAC,GAAGC,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACC,QAAQ,GAAGL,OAAO;MACvB,IAAI,CAACM,eAAe,GAAG,EAAE;MACzBC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAC,CAAC;MAC9DF,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,iBAAiB,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAC,CAAC;IACzE;IACAC,qBAAqBA,CAACC,YAAY,EAAE;MAChC,MAAM,IAAIC,KAAK,CAAC,SAASD,YAAY,mCAAmC,IAAI,CAACE,OAAO,CAAC,CAAC,CAACC,IAAI,EAAE,CAAC;IAClG;IACAD,OAAOA,CAAA,EAAG;MACN,OAAO,IAAI,CAACR,QAAQ;IACxB;IACAU,eAAeA,CAACC,WAAW,EAAE;MACzB,IAAI,CAACV,eAAe,GAAGU,WAAW;IACtC;IACAC,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACX,eAAe,KAAK,IAAI,EAAE;QAC/B,OAAO,EAAE;MACb;MACA,OAAO,IAAI,CAACA,eAAe;IAC/B;IACAY,MAAMA,CAAA,EAAG;MACL,OAAO,KAAK;IAChB;IACAC,YAAYA,CAAA,EAAG;MACX,IAAI,CAACT,qBAAqB,CAAC,cAAc,CAAC;IAC9C;IACAU,WAAWA,CAAA,EAAG;MACV,IAAI,CAACV,qBAAqB,CAAC,aAAa,CAAC;IAC7C;IACAW,WAAWA,CAAA,EAAG;MACV,IAAI,CAACX,qBAAqB,CAAC,aAAa,CAAC;IAC7C;IACAY,YAAYA,CAAA,EAAG;MACX,IAAI,CAACZ,qBAAqB,CAAC,cAAc,CAAC;IAC9C;IACAa,WAAWA,CAAA,EAAG;MACV,IAAI,CAACb,qBAAqB,CAAC,aAAa,CAAC;IAC7C;IACAc,SAASA,CAAA,EAAG;MACR,IAAI,CAACd,qBAAqB,CAAC,WAAW,CAAC;IAC3C;IACAe,cAAcA,CAAA,EAAG;MACb,IAAI,CAACf,qBAAqB,CAAC,gBAAgB,CAAC;IAChD;IACAgB,eAAeA,CAAA,EAAG;MACd,IAAI,CAAChB,qBAAqB,CAAC,iBAAiB,CAAC;IACjD;IACAiB,UAAUA,CAAA,EAAG;MACT,IAAI,CAACjB,qBAAqB,CAAC,YAAY,CAAC;IAC5C;IACAkB,MAAMA,CAAA,EAAG;MACL,IAAI,CAAClB,qBAAqB,CAAC,QAAQ,CAAC;IACxC;IACAmB,SAASA,CAAA,EAAG;MACR,IAAI,CAACnB,qBAAqB,CAAC,WAAW,CAAC;IAC3C;IACAoB,QAAQA,CAAA,EAAG;MACP,IAAI,CAACpB,qBAAqB,CAAC,UAAU,CAAC;IAC1C;IACAqB,GAAGA,CAAC,GAAGC,YAAY,EAAE;MACjB,IAAI,CAACtB,qBAAqB,CAAC,KAAK,CAAC;IACrC;IACAuB,MAAMA,CAAC,GAAGD,YAAY,EAAE;MACpB,IAAI,CAACtB,qBAAqB,CAAC,QAAQ,CAAC;IACxC;IACAwB,EAAEA,CAACC,YAAY,EAAE;MACb,IAAI,IAAI,YAAYA,YAAY,EAAE;QAC9B,OAAO,IAAI;MACf;MACA,MAAM,IAAIvB,KAAK,CAAC,GAAG,IAAI,CAACT,WAAW,CAACW,IAAI,0BAA0BqB,YAAY,CAACrB,IAAI,EAAE,CAAC;IAC1F;IACAsB,OAAOA,CAACC,MAAM,EAAE;MACZ,IAAI,CAAC3B,qBAAqB,CAAC,SAAS,CAAC;IACzC;IACA4B,WAAWA,CAACxB,IAAI,EAAE;MACd,IAAI,CAACJ,qBAAqB,CAAC,aAAa,CAAC;IAC7C;IACA6B,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;MAC1BC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,KAAK;MACxBC,wBAAwB,EAAE,KAAK;MAC/BC,cAAc,EAAE,IAAI;MACpBC,iBAAiB,EAAE;IACvB,CAAC,EAAE;MACC,IAAI,CAACpC,qBAAqB,CAAC,cAAc,CAAC;IAC9C;IACAqC,MAAMA,CAACP,KAAK,EAAEC,OAAO,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAC,EAAE;MACvC,IAAIG,cAAc,GAAG,KAAK;MAC1B,IAAIL,KAAK,YAAY1C,KAAK,EAAE;QACxB+C,cAAc,GAAG,IAAI;MACzB;MACA,OAAO,IAAI,CAACN,YAAY,CAACC,KAAK,EAAE;QAC5BK,cAAc,EAAEA,cAAc;QAC9BD,wBAAwB,EAAE,IAAI;QAC9BD,iBAAiB,EAAE,IAAI;QACvBD,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBI,iBAAiB,EAAE;MACvB,CAAC,CAAC;IACN;IACAE,SAASA,CAACR,KAAK,EAAEC,OAAO,GAAG;MACvBC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,KAAK;MACxBC,wBAAwB,EAAE;IAC9B,CAAC,EAAE;MACC,IAAI,CAACH,OAAO,CAACE,iBAAiB,EAAE;QAC5B,IAAI,EAAEH,KAAK,YAAY1C,KAAK,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACA,IAAImD,iBAAiB,GAAG,IAAI,CAAChC,cAAc,CAAC,CAAC;QAC7C,IAAIiC,mBAAmB,GAAGV,KAAK,CAACvB,cAAc,CAAC,CAAC;QAChD,IAAIgC,iBAAiB,CAACE,MAAM,KAAKD,mBAAmB,CAACC,MAAM,EAAE;UACzD,OAAO,KAAK;QAChB;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,iBAAiB,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/C,IAAIH,iBAAiB,CAACG,CAAC,CAAC,CAACC,aAAa,CAACH,mBAAmB,CAACE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAClE,OAAO,KAAK;UAChB;QACJ;MACJ;MACA,IAAIE,WAAW,GAAG;QACdT,cAAc,EAAE,IAAI;QACpBD,wBAAwB,EAAEH,OAAO,CAACG,wBAAwB;QAC1DF,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBI,iBAAiB,EAAE;MACvB,CAAC;MACD,OAAO,IAAI,CAACP,YAAY,CAACC,KAAK,EAAEc,WAAW,CAAC;IAChD;IACA,OAAOC,WAAWA,CAAA,EAAG;MACjB,OAAOvD,OAAO;IAClB;IACA,OAAOwD,YAAYA,CAACC,OAAO,EAAEzC,WAAW,EAAE;MACtC,OAAOf,iBAAiB,CAACyD,SAAS,CAAC,IAAI,EAAED,OAAO,EAAEzC,WAAW,CAAC;IAClE;EACJ,CAAC;EACDT,MAAM,CAACC,cAAc,CAACN,QAAQ,EAAEN,iBAAiB,EAAE;IAC/C+D,QAAQ,EAAE,KAAK;IACflD,UAAU,EAAE,KAAK;IACjBmD,KAAK,EAAEhE;EACX,CAAC,CAAC;EACF,OAAOM,QAAQ;AACnB;AACA,CAAC,UAAUJ,KAAK,EAAE;EACd,SAAS+D,IAAIA,CAACD,KAAK,EAAE5C,WAAW,EAAE;IAC9B,IAAI4C,KAAK,YAAY9D,KAAK,EAAE;MACxB,IAAIJ,SAAS,CAACsB,WAAW,CAAC,EAAE;QACxB,MAAM,IAAIJ,KAAK,CAAC,yEAAyE,GACrF,yBAAyB,CAAC;MAClC;MACA,OAAOgD,KAAK;IAChB;IACA,OAAOjE,iBAAiB,CAACmE,oBAAoB,CAACF,KAAK,EAAE5C,WAAW,CAAC;EACrE;EACAlB,KAAK,CAAC+D,IAAI,GAAGA,IAAI;AACrB,CAAC,EAAE/D,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzBS,MAAM,CAACC,cAAc,CAACV,KAAK,EAAED,MAAM,CAACkE,WAAW,EAAE;EAC7ChC,GAAG,EAAEA,CAAA,KAAOiC,QAAQ,IAAK;IACrB,OAAQtE,SAAS,CAACsE,QAAQ,CAAC,IACvBtE,SAAS,CAACsE,QAAQ,CAAC7D,WAAW,CAAC,IAC/BP,iBAAiB,IAAIoE,QAAQ,CAAC7D,WAAW,IACzC6D,QAAQ,CAAC7D,WAAW,CAACP,iBAAiB,CAAC,KAAKA,iBAAiB;EACrE;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}