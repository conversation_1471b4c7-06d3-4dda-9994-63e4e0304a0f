{"ast": null, "code": "import { Object3D, Vector3, Quaternion } from \"three\";\nconst _translationObject = /* @__PURE__ */new Vector3();\nconst _quaternionObject = /* @__PURE__ */new Quaternion();\nconst _scaleObject = /* @__PURE__ */new Vector3();\nconst _translationWorld = /* @__PURE__ */new Vector3();\nconst _quaternionWorld = /* @__PURE__ */new Quaternion();\nconst _scaleWorld = /* @__PURE__ */new Vector3();\nclass Gyroscope extends Object3D {\n  constructor() {\n    super();\n  }\n  updateMatrixWorld(force) {\n    this.matrixAutoUpdate && this.updateMatrix();\n    if (this.matrixWorldNeedsUpdate || force) {\n      if (this.parent !== null) {\n        this.matrixWorld.multiplyMatrices(this.parent.matrixWorld, this.matrix);\n        this.matrixWorld.decompose(_translationWorld, _quaternionWorld, _scaleWorld);\n        this.matrix.decompose(_translationObject, _quaternionObject, _scaleObject);\n        this.matrixWorld.compose(_translationWorld, _quaternionObject, _scaleWorld);\n      } else {\n        this.matrixWorld.copy(this.matrix);\n      }\n      this.matrixWorldNeedsUpdate = false;\n      force = true;\n    }\n    for (let i = 0, l = this.children.length; i < l; i++) {\n      this.children[i].updateMatrixWorld(force);\n    }\n  }\n}\nexport { Gyroscope };", "map": {"version": 3, "names": ["_translationObject", "Vector3", "_quaternionObject", "Quaternion", "_scaleObject", "_translationWorld", "_quaternionWorld", "_scaleWorld", "Gyroscope", "Object3D", "constructor", "updateMatrixWorld", "force", "matrixAutoUpdate", "updateMatrix", "matrixWorldNeedsUpdate", "parent", "matrixWorld", "multiplyMatrices", "matrix", "decompose", "compose", "copy", "i", "l", "children", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/misc/Gyroscope.js"], "sourcesContent": ["import { Object3D, Quaternion, Vector3 } from 'three'\n\nconst _translationObject = /* @__PURE__ */ new Vector3()\nconst _quaternionObject = /* @__PURE__ */ new Quaternion()\nconst _scaleObject = /* @__PURE__ */ new Vector3()\n\nconst _translationWorld = /* @__PURE__ */ new Vector3()\nconst _quaternionWorld = /* @__PURE__ */ new Quaternion()\nconst _scaleWorld = /* @__PURE__ */ new Vector3()\n\nclass Gyroscope extends Object3D {\n  constructor() {\n    super()\n  }\n\n  updateMatrixWorld(force) {\n    this.matrixAutoUpdate && this.updateMatrix()\n\n    // update matrixWorld\n\n    if (this.matrixWorldNeedsUpdate || force) {\n      if (this.parent !== null) {\n        this.matrixWorld.multiplyMatrices(this.parent.matrixWorld, this.matrix)\n\n        this.matrixWorld.decompose(_translationWorld, _quaternionWorld, _scaleWorld)\n        this.matrix.decompose(_translationObject, _quaternionObject, _scaleObject)\n\n        this.matrixWorld.compose(_translationWorld, _quaternionObject, _scaleWorld)\n      } else {\n        this.matrixWorld.copy(this.matrix)\n      }\n\n      this.matrixWorldNeedsUpdate = false\n\n      force = true\n    }\n\n    // update children\n\n    for (let i = 0, l = this.children.length; i < l; i++) {\n      this.children[i].updateMatrixWorld(force)\n    }\n  }\n}\n\nexport { Gyroscope }\n"], "mappings": ";AAEA,MAAMA,kBAAA,GAAqC,mBAAIC,OAAA,CAAS;AACxD,MAAMC,iBAAA,GAAoC,mBAAIC,UAAA,CAAY;AAC1D,MAAMC,YAAA,GAA+B,mBAAIH,OAAA,CAAS;AAElD,MAAMI,iBAAA,GAAoC,mBAAIJ,OAAA,CAAS;AACvD,MAAMK,gBAAA,GAAmC,mBAAIH,UAAA,CAAY;AACzD,MAAMI,WAAA,GAA8B,mBAAIN,OAAA,CAAS;AAEjD,MAAMO,SAAA,SAAkBC,QAAA,CAAS;EAC/BC,YAAA,EAAc;IACZ,MAAO;EACR;EAEDC,kBAAkBC,KAAA,EAAO;IACvB,KAAKC,gBAAA,IAAoB,KAAKC,YAAA,CAAc;IAI5C,IAAI,KAAKC,sBAAA,IAA0BH,KAAA,EAAO;MACxC,IAAI,KAAKI,MAAA,KAAW,MAAM;QACxB,KAAKC,WAAA,CAAYC,gBAAA,CAAiB,KAAKF,MAAA,CAAOC,WAAA,EAAa,KAAKE,MAAM;QAEtE,KAAKF,WAAA,CAAYG,SAAA,CAAUf,iBAAA,EAAmBC,gBAAA,EAAkBC,WAAW;QAC3E,KAAKY,MAAA,CAAOC,SAAA,CAAUpB,kBAAA,EAAoBE,iBAAA,EAAmBE,YAAY;QAEzE,KAAKa,WAAA,CAAYI,OAAA,CAAQhB,iBAAA,EAAmBH,iBAAA,EAAmBK,WAAW;MAClF,OAAa;QACL,KAAKU,WAAA,CAAYK,IAAA,CAAK,KAAKH,MAAM;MAClC;MAED,KAAKJ,sBAAA,GAAyB;MAE9BH,KAAA,GAAQ;IACT;IAID,SAASW,CAAA,GAAI,GAAGC,CAAA,GAAI,KAAKC,QAAA,CAASC,MAAA,EAAQH,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MACpD,KAAKE,QAAA,CAASF,CAAC,EAAEZ,iBAAA,CAAkBC,KAAK;IACzC;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}