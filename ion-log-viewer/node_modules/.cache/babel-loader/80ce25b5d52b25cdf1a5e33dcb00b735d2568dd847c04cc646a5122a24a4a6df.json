{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives } from \"./FromJsConstructor\";\nimport { _NativeJsBoolean } from \"./JsValueConversion\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withPrimitives(Primitives.Boolean).withClassesToUnbox(_NativeJsBoolean).build();\nexport class Boolean extends Value(_NativeJsBoolean, IonTypes.BOOL, _fromJsConstructor) {\n  constructor(value, annotations = []) {\n    super(value);\n    this._setAnnotations(annotations);\n  }\n  booleanValue() {\n    return this.valueOf();\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeBoolean(this.booleanValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Boolean) {\n      isSupportedType = true;\n      valueToCompare = other.booleanValue();\n    } else if (!options.onlyCompareIon) {\n      if (typeof other === \"boolean\" || other instanceof _NativeJsBoolean) {\n        isSupportedType = true;\n        valueToCompare = other.valueOf();\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    if (this.booleanValue() !== valueToCompare) {\n      return false;\n    }\n    return true;\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Primitives", "_NativeJsBoolean", "Value", "_fromJsConstructor", "withPrimitives", "Boolean", "withClassesToUnbox", "build", "BOOL", "constructor", "value", "annotations", "_setAnnotations", "booleanValue", "valueOf", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeBoolean", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Boolean.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives, } from \"./FromJsConstructor\";\nimport { _NativeJsBoolean } from \"./JsValueConversion\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withPrimitives(Primitives.Boolean)\n    .withClassesToUnbox(_NativeJsBoolean)\n    .build();\nexport class Boolean extends Value(_NativeJsBoolean, IonTypes.BOOL, _fromJsConstructor) {\n    constructor(value, annotations = []) {\n        super(value);\n        this._setAnnotations(annotations);\n    }\n    booleanValue() {\n        return this.valueOf();\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeBoolean(this.booleanValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Boolean) {\n            isSupportedType = true;\n            valueToCompare = other.booleanValue();\n        }\n        else if (!options.onlyCompareIon) {\n            if (typeof other === \"boolean\" || other instanceof _NativeJsBoolean) {\n                isSupportedType = true;\n                valueToCompare = other.valueOf();\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        if (this.booleanValue() !== valueToCompare) {\n            return false;\n        }\n        return true;\n    }\n}\n//# sourceMappingURL=Boolean.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,EAAEC,UAAU,QAAS,qBAAqB;AAC3E,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIJ,wBAAwB,CAAC,CAAC,CACpDK,cAAc,CAACJ,UAAU,CAACK,OAAO,CAAC,CAClCC,kBAAkB,CAACL,gBAAgB,CAAC,CACpCM,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMF,OAAO,SAASH,KAAK,CAACD,gBAAgB,EAAEH,QAAQ,CAACU,IAAI,EAAEL,kBAAkB,CAAC,CAAC;EACpFM,WAAWA,CAACC,KAAK,EAAEC,WAAW,GAAG,EAAE,EAAE;IACjC,KAAK,CAACD,KAAK,CAAC;IACZ,IAAI,CAACE,eAAe,CAACD,WAAW,CAAC;EACrC;EACAE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC;EACzB;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACN,YAAY,CAAC,CAAC,CAAC;EAC5C;EACAO,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAYhB,OAAO,EAAE;MAC1BsB,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK,CAACR,YAAY,CAAC,CAAC;IACzC,CAAC,MACI,IAAI,CAACS,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAI,OAAOL,KAAK,KAAK,SAAS,IAAIA,KAAK,YAAYpB,gBAAgB,EAAE;QACjE0B,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGP,KAAK,CAACP,OAAO,CAAC,CAAC;MACpC;IACJ;IACA,IAAI,CAACa,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACd,YAAY,CAAC,CAAC,KAAKe,cAAc,EAAE;MACxC,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}