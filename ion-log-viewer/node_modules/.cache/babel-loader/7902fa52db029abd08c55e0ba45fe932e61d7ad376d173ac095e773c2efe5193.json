{"ast": null, "code": "import { Loader, FileLoader, MeshStandardMaterial, Color, TextureLoader, Object3D, Matrix4, BufferGeometryLoader, DirectionalLight, PointLight, RectAreaLight, Vector3, SpotLight, CanvasTexture, LinearFilter, ClampToEdgeWrapping, SpriteMaterial, Sprite, LineBasicMaterial, Line, Mesh, PointsMaterial, Points } from \"three\";\nconst _taskCache = /* @__PURE__ */new WeakMap();\nclass Rhino3dmLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.libraryPath = \"\";\n    this.libraryPending = null;\n    this.libraryBinary = null;\n    this.libraryConfig = {};\n    this.url = \"\";\n    this.workerLimit = 4;\n    this.workerPool = [];\n    this.workerNextTaskID = 1;\n    this.workerSourceURL = \"\";\n    this.workerConfig = {};\n    this.materials = [];\n  }\n  setLibraryPath(path) {\n    this.libraryPath = path;\n    return this;\n  }\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    this.url = url;\n    loader.load(url, buffer => {\n      if (_taskCache.has(buffer)) {\n        const cachedTask = _taskCache.get(buffer);\n        return cachedTask.promise.then(onLoad).catch(onError);\n      }\n      this.decodeObjects(buffer, url).then(onLoad).catch(onError);\n    }, onProgress, onError);\n  }\n  debug() {\n    console.log(\"Task load: \", this.workerPool.map(worker => worker._taskLoad));\n  }\n  decodeObjects(buffer, url) {\n    let worker;\n    let taskID;\n    const taskCost = buffer.byteLength;\n    const objectPending = this._getWorker(taskCost).then(_worker => {\n      worker = _worker;\n      taskID = this.workerNextTaskID++;\n      return new Promise((resolve, reject) => {\n        worker._callbacks[taskID] = {\n          resolve,\n          reject\n        };\n        worker.postMessage({\n          type: \"decode\",\n          id: taskID,\n          buffer\n        }, [buffer]);\n      });\n    }).then(message => this._createGeometry(message.data));\n    objectPending.catch(() => true).then(() => {\n      if (worker && taskID) {\n        this._releaseTask(worker, taskID);\n      }\n    });\n    _taskCache.set(buffer, {\n      url,\n      promise: objectPending\n    });\n    return objectPending;\n  }\n  parse(data, onLoad, onError) {\n    this.decodeObjects(data, \"\").then(onLoad).catch(onError);\n  }\n  _compareMaterials(material) {\n    const mat = {};\n    mat.name = material.name;\n    mat.color = {};\n    mat.color.r = material.color.r;\n    mat.color.g = material.color.g;\n    mat.color.b = material.color.b;\n    mat.type = material.type;\n    for (let i = 0; i < this.materials.length; i++) {\n      const m = this.materials[i];\n      const _mat = {};\n      _mat.name = m.name;\n      _mat.color = {};\n      _mat.color.r = m.color.r;\n      _mat.color.g = m.color.g;\n      _mat.color.b = m.color.b;\n      _mat.type = m.type;\n      if (JSON.stringify(mat) === JSON.stringify(_mat)) {\n        return m;\n      }\n    }\n    this.materials.push(material);\n    return material;\n  }\n  _createMaterial(material) {\n    if (material === void 0) {\n      return new MeshStandardMaterial({\n        color: new Color(1, 1, 1),\n        metalness: 0.8,\n        name: \"default\",\n        side: 2\n      });\n    }\n    const _diffuseColor = material.diffuseColor;\n    const diffusecolor = new Color(_diffuseColor.r / 255, _diffuseColor.g / 255, _diffuseColor.b / 255);\n    if (_diffuseColor.r === 0 && _diffuseColor.g === 0 && _diffuseColor.b === 0) {\n      diffusecolor.r = 1;\n      diffusecolor.g = 1;\n      diffusecolor.b = 1;\n    }\n    const mat = new MeshStandardMaterial({\n      color: diffusecolor,\n      name: material.name,\n      side: 2,\n      transparent: material.transparency > 0 ? true : false,\n      opacity: 1 - material.transparency\n    });\n    const textureLoader = new TextureLoader();\n    for (let i = 0; i < material.textures.length; i++) {\n      const texture = material.textures[i];\n      if (texture.image !== null) {\n        const map = textureLoader.load(texture.image);\n        switch (texture.type) {\n          case \"Diffuse\":\n            mat.map = map;\n            break;\n          case \"Bump\":\n            mat.bumpMap = map;\n            break;\n          case \"Transparency\":\n            mat.alphaMap = map;\n            mat.transparent = true;\n            break;\n          case \"Emap\":\n            mat.envMap = map;\n            break;\n        }\n      }\n    }\n    return mat;\n  }\n  _createGeometry(data) {\n    const object = new Object3D();\n    const instanceDefinitionObjects = [];\n    const instanceDefinitions = [];\n    const instanceReferences = [];\n    object.userData[\"layers\"] = data.layers;\n    object.userData[\"groups\"] = data.groups;\n    object.userData[\"settings\"] = data.settings;\n    object.userData[\"objectType\"] = \"File3dm\";\n    object.userData[\"materials\"] = null;\n    object.name = this.url;\n    let objects = data.objects;\n    const materials = data.materials;\n    for (let i = 0; i < objects.length; i++) {\n      const obj = objects[i];\n      const attributes = obj.attributes;\n      switch (obj.objectType) {\n        case \"InstanceDefinition\":\n          instanceDefinitions.push(obj);\n          break;\n        case \"InstanceReference\":\n          instanceReferences.push(obj);\n          break;\n        default:\n          let _object;\n          if (attributes.materialIndex >= 0) {\n            const rMaterial = materials[attributes.materialIndex];\n            let material = this._createMaterial(rMaterial);\n            material = this._compareMaterials(material);\n            _object = this._createObject(obj, material);\n          } else {\n            const material = this._createMaterial();\n            _object = this._createObject(obj, material);\n          }\n          if (_object === void 0) {\n            continue;\n          }\n          const layer = data.layers[attributes.layerIndex];\n          _object.visible = layer ? data.layers[attributes.layerIndex].visible : true;\n          if (attributes.isInstanceDefinitionObject) {\n            instanceDefinitionObjects.push(_object);\n          } else {\n            object.add(_object);\n          }\n          break;\n      }\n    }\n    for (let i = 0; i < instanceDefinitions.length; i++) {\n      const iDef = instanceDefinitions[i];\n      objects = [];\n      for (let j = 0; j < iDef.attributes.objectIds.length; j++) {\n        const objId = iDef.attributes.objectIds[j];\n        for (let p = 0; p < instanceDefinitionObjects.length; p++) {\n          const idoId = instanceDefinitionObjects[p].userData.attributes.id;\n          if (objId === idoId) {\n            objects.push(instanceDefinitionObjects[p]);\n          }\n        }\n      }\n      for (let j = 0; j < instanceReferences.length; j++) {\n        const iRef = instanceReferences[j];\n        if (iRef.geometry.parentIdefId === iDef.attributes.id) {\n          const iRefObject = new Object3D();\n          const xf = iRef.geometry.xform.array;\n          const matrix = new Matrix4();\n          matrix.set(xf[0], xf[1], xf[2], xf[3], xf[4], xf[5], xf[6], xf[7], xf[8], xf[9], xf[10], xf[11], xf[12], xf[13], xf[14], xf[15]);\n          iRefObject.applyMatrix4(matrix);\n          for (let p = 0; p < objects.length; p++) {\n            iRefObject.add(objects[p].clone(true));\n          }\n          object.add(iRefObject);\n        }\n      }\n    }\n    object.userData[\"materials\"] = this.materials;\n    return object;\n  }\n  _createObject(obj, mat) {\n    const loader = new BufferGeometryLoader();\n    const attributes = obj.attributes;\n    let geometry, material, _color, color;\n    switch (obj.objectType) {\n      case \"Point\":\n      case \"PointSet\":\n        geometry = loader.parse(obj.geometry);\n        if (geometry.attributes.hasOwnProperty(\"color\")) {\n          material = new PointsMaterial({\n            vertexColors: true,\n            sizeAttenuation: false,\n            size: 2\n          });\n        } else {\n          _color = attributes.drawColor;\n          color = new Color(_color.r / 255, _color.g / 255, _color.b / 255);\n          material = new PointsMaterial({\n            color,\n            sizeAttenuation: false,\n            size: 2\n          });\n        }\n        material = this._compareMaterials(material);\n        const points = new Points(geometry, material);\n        points.userData[\"attributes\"] = attributes;\n        points.userData[\"objectType\"] = obj.objectType;\n        if (attributes.name) {\n          points.name = attributes.name;\n        }\n        return points;\n      case \"Mesh\":\n      case \"Extrusion\":\n      case \"SubD\":\n      case \"Brep\":\n        if (obj.geometry === null) return;\n        geometry = loader.parse(obj.geometry);\n        if (geometry.attributes.hasOwnProperty(\"color\")) {\n          mat.vertexColors = true;\n        }\n        if (mat === null) {\n          mat = this._createMaterial();\n          mat = this._compareMaterials(mat);\n        }\n        const mesh = new Mesh(geometry, mat);\n        mesh.castShadow = attributes.castsShadows;\n        mesh.receiveShadow = attributes.receivesShadows;\n        mesh.userData[\"attributes\"] = attributes;\n        mesh.userData[\"objectType\"] = obj.objectType;\n        if (attributes.name) {\n          mesh.name = attributes.name;\n        }\n        return mesh;\n      case \"Curve\":\n        geometry = loader.parse(obj.geometry);\n        _color = attributes.drawColor;\n        color = new Color(_color.r / 255, _color.g / 255, _color.b / 255);\n        material = new LineBasicMaterial({\n          color\n        });\n        material = this._compareMaterials(material);\n        const lines = new Line(geometry, material);\n        lines.userData[\"attributes\"] = attributes;\n        lines.userData[\"objectType\"] = obj.objectType;\n        if (attributes.name) {\n          lines.name = attributes.name;\n        }\n        return lines;\n      case \"TextDot\":\n        geometry = obj.geometry;\n        const ctx = document.createElement(\"canvas\").getContext(\"2d\");\n        const font = `${geometry.fontHeight}px ${geometry.fontFace}`;\n        ctx.font = font;\n        const width = ctx.measureText(geometry.text).width + 10;\n        const height = geometry.fontHeight + 10;\n        const r = window.devicePixelRatio;\n        ctx.canvas.width = width * r;\n        ctx.canvas.height = height * r;\n        ctx.canvas.style.width = width + \"px\";\n        ctx.canvas.style.height = height + \"px\";\n        ctx.setTransform(r, 0, 0, r, 0, 0);\n        ctx.font = font;\n        ctx.textBaseline = \"middle\";\n        ctx.textAlign = \"center\";\n        color = attributes.drawColor;\n        ctx.fillStyle = `rgba(${color.r},${color.g},${color.b},${color.a})`;\n        ctx.fillRect(0, 0, width, height);\n        ctx.fillStyle = \"white\";\n        ctx.fillText(geometry.text, width / 2, height / 2);\n        const texture = new CanvasTexture(ctx.canvas);\n        texture.minFilter = LinearFilter;\n        texture.wrapS = ClampToEdgeWrapping;\n        texture.wrapT = ClampToEdgeWrapping;\n        material = new SpriteMaterial({\n          map: texture,\n          depthTest: false\n        });\n        const sprite = new Sprite(material);\n        sprite.position.set(geometry.point[0], geometry.point[1], geometry.point[2]);\n        sprite.scale.set(width / 10, height / 10, 1);\n        sprite.userData[\"attributes\"] = attributes;\n        sprite.userData[\"objectType\"] = obj.objectType;\n        if (attributes.name) {\n          sprite.name = attributes.name;\n        }\n        return sprite;\n      case \"Light\":\n        geometry = obj.geometry;\n        let light;\n        if (geometry.isDirectionalLight) {\n          light = new DirectionalLight();\n          light.castShadow = attributes.castsShadows;\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2]);\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2]);\n          light.shadow.normalBias = 0.1;\n        } else if (geometry.isPointLight) {\n          light = new PointLight();\n          light.castShadow = attributes.castsShadows;\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2]);\n          light.shadow.normalBias = 0.1;\n        } else if (geometry.isRectangularLight) {\n          light = new RectAreaLight();\n          const width2 = Math.abs(geometry.width[2]);\n          const height2 = Math.abs(geometry.length[0]);\n          light.position.set(geometry.location[0] - height2 / 2, geometry.location[1], geometry.location[2] - width2 / 2);\n          light.height = height2;\n          light.width = width2;\n          light.lookAt(new Vector3(geometry.direction[0], geometry.direction[1], geometry.direction[2]));\n        } else if (geometry.isSpotLight) {\n          light = new SpotLight();\n          light.castShadow = attributes.castsShadows;\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2]);\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2]);\n          light.angle = geometry.spotAngleRadians;\n          light.shadow.normalBias = 0.1;\n        } else if (geometry.isLinearLight) {\n          console.warn(\"THREE.3DMLoader:  No conversion exists for linear lights.\");\n          return;\n        }\n        if (light) {\n          light.intensity = geometry.intensity;\n          _color = geometry.diffuse;\n          color = new Color(_color.r / 255, _color.g / 255, _color.b / 255);\n          light.color = color;\n          light.userData[\"attributes\"] = attributes;\n          light.userData[\"objectType\"] = obj.objectType;\n        }\n        return light;\n    }\n  }\n  _initLibrary() {\n    if (!this.libraryPending) {\n      const jsLoader = new FileLoader(this.manager);\n      jsLoader.setPath(this.libraryPath);\n      const jsContent = new Promise((resolve, reject) => {\n        jsLoader.load(\"rhino3dm.js\", resolve, void 0, reject);\n      });\n      const binaryLoader = new FileLoader(this.manager);\n      binaryLoader.setPath(this.libraryPath);\n      binaryLoader.setResponseType(\"arraybuffer\");\n      const binaryContent = new Promise((resolve, reject) => {\n        binaryLoader.load(\"rhino3dm.wasm\", resolve, void 0, reject);\n      });\n      this.libraryPending = Promise.all([jsContent, binaryContent]).then(([jsContent2, binaryContent2]) => {\n        this.libraryConfig.wasmBinary = binaryContent2;\n        const fn = Rhino3dmWorker.toString();\n        const body = [\"/* rhino3dm.js */\", jsContent2, \"/* worker */\", fn.substring(fn.indexOf(\"{\") + 1, fn.lastIndexOf(\"}\"))].join(\"\\n\");\n        this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n      });\n    }\n    return this.libraryPending;\n  }\n  _getWorker(taskCost) {\n    return this._initLibrary().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker2 = new Worker(this.workerSourceURL);\n        worker2._callbacks = {};\n        worker2._taskCosts = {};\n        worker2._taskLoad = 0;\n        worker2.postMessage({\n          type: \"init\",\n          libraryConfig: this.libraryConfig\n        });\n        worker2.onmessage = function (e) {\n          const message = e.data;\n          switch (message.type) {\n            case \"decode\":\n              worker2._callbacks[message.id].resolve(message);\n              break;\n            case \"error\":\n              worker2._callbacks[message.id].reject(message);\n              break;\n            default:\n              console.error('THREE.Rhino3dmLoader: Unexpected message, \"' + message.type + '\"');\n          }\n        };\n        this.workerPool.push(worker2);\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1;\n        });\n      }\n      const worker = this.workerPool[this.workerPool.length - 1];\n      worker._taskLoad += taskCost;\n      return worker;\n    });\n  }\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID];\n    delete worker._callbacks[taskID];\n    delete worker._taskCosts[taskID];\n  }\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate();\n    }\n    this.workerPool.length = 0;\n    return this;\n  }\n}\nfunction Rhino3dmWorker() {\n  let libraryPending;\n  let libraryConfig;\n  let rhino;\n  onmessage = function (e) {\n    const message = e.data;\n    switch (message.type) {\n      case \"init\":\n        libraryConfig = message.libraryConfig;\n        const wasmBinary = libraryConfig.wasmBinary;\n        let RhinoModule;\n        libraryPending = new Promise(function (resolve) {\n          RhinoModule = {\n            wasmBinary,\n            onRuntimeInitialized: resolve\n          };\n          rhino3dm(RhinoModule);\n        }).then(() => {\n          rhino = RhinoModule;\n        });\n        break;\n      case \"decode\":\n        const buffer = message.buffer;\n        libraryPending.then(() => {\n          const data = decodeObjects(rhino, buffer);\n          self.postMessage({\n            type: \"decode\",\n            id: message.id,\n            data\n          });\n        });\n        break;\n    }\n  };\n  function decodeObjects(rhino2, buffer) {\n    const arr = new Uint8Array(buffer);\n    const doc = rhino2.File3dm.fromByteArray(arr);\n    const objects = [];\n    const materials = [];\n    const layers = [];\n    const views = [];\n    const namedViews = [];\n    const groups = [];\n    const objs = doc.objects();\n    const cnt = objs.count;\n    for (let i = 0; i < cnt; i++) {\n      const _object = objs.get(i);\n      const object = extractObjectData(_object, doc);\n      _object.delete();\n      if (object) {\n        objects.push(object);\n      }\n    }\n    for (let i = 0; i < doc.instanceDefinitions().count(); i++) {\n      const idef = doc.instanceDefinitions().get(i);\n      const idefAttributes = extractProperties(idef);\n      idefAttributes.objectIds = idef.getObjectIds();\n      objects.push({\n        geometry: null,\n        attributes: idefAttributes,\n        objectType: \"InstanceDefinition\"\n      });\n    }\n    const textureTypes = [\n    // rhino.TextureType.Bitmap,\n    rhino2.TextureType.Diffuse, rhino2.TextureType.Bump, rhino2.TextureType.Transparency, rhino2.TextureType.Opacity, rhino2.TextureType.Emap];\n    const pbrTextureTypes = [rhino2.TextureType.PBR_BaseColor, rhino2.TextureType.PBR_Subsurface, rhino2.TextureType.PBR_SubsurfaceScattering, rhino2.TextureType.PBR_SubsurfaceScatteringRadius, rhino2.TextureType.PBR_Metallic, rhino2.TextureType.PBR_Specular, rhino2.TextureType.PBR_SpecularTint, rhino2.TextureType.PBR_Roughness, rhino2.TextureType.PBR_Anisotropic, rhino2.TextureType.PBR_Anisotropic_Rotation, rhino2.TextureType.PBR_Sheen, rhino2.TextureType.PBR_SheenTint, rhino2.TextureType.PBR_Clearcoat, rhino2.TextureType.PBR_ClearcoatBump, rhino2.TextureType.PBR_ClearcoatRoughness, rhino2.TextureType.PBR_OpacityIor, rhino2.TextureType.PBR_OpacityRoughness, rhino2.TextureType.PBR_Emission, rhino2.TextureType.PBR_AmbientOcclusion, rhino2.TextureType.PBR_Displacement];\n    for (let i = 0; i < doc.materials().count(); i++) {\n      const _material = doc.materials().get(i);\n      const _pbrMaterial = _material.physicallyBased();\n      let material = extractProperties(_material);\n      const textures = [];\n      for (let j = 0; j < textureTypes.length; j++) {\n        const _texture = _material.getTexture(textureTypes[j]);\n        if (_texture) {\n          let textureType = textureTypes[j].constructor.name;\n          textureType = textureType.substring(12, textureType.length);\n          const texture = {\n            type: textureType\n          };\n          const image = doc.getEmbeddedFileAsBase64(_texture.fileName);\n          if (image) {\n            texture.image = \"data:image/png;base64,\" + image;\n          } else {\n            console.warn(`THREE.3DMLoader: Image for ${textureType} texture not embedded in file.`);\n            texture.image = null;\n          }\n          textures.push(texture);\n          _texture.delete();\n        }\n      }\n      material.textures = textures;\n      if (_pbrMaterial.supported) {\n        console.log(\"pbr true\");\n        for (let j = 0; j < pbrTextureTypes.length; j++) {\n          const _texture = _material.getTexture(textureTypes[j]);\n          if (_texture) {\n            const image = doc.getEmbeddedFileAsBase64(_texture.fileName);\n            let textureType = textureTypes[j].constructor.name;\n            textureType = textureType.substring(12, textureType.length);\n            const texture = {\n              type: textureType,\n              image: \"data:image/png;base64,\" + image\n            };\n            textures.push(texture);\n            _texture.delete();\n          }\n        }\n        const pbMaterialProperties = extractProperties(_material.physicallyBased());\n        material = Object.assign(pbMaterialProperties, material);\n      }\n      materials.push(material);\n      _material.delete();\n      _pbrMaterial.delete();\n    }\n    for (let i = 0; i < doc.layers().count(); i++) {\n      const _layer = doc.layers().get(i);\n      const layer = extractProperties(_layer);\n      layers.push(layer);\n      _layer.delete();\n    }\n    for (let i = 0; i < doc.views().count(); i++) {\n      const _view = doc.views().get(i);\n      const view = extractProperties(_view);\n      views.push(view);\n      _view.delete();\n    }\n    for (let i = 0; i < doc.namedViews().count(); i++) {\n      const _namedView = doc.namedViews().get(i);\n      const namedView = extractProperties(_namedView);\n      namedViews.push(namedView);\n      _namedView.delete();\n    }\n    for (let i = 0; i < doc.groups().count(); i++) {\n      const _group = doc.groups().get(i);\n      const group = extractProperties(_group);\n      groups.push(group);\n      _group.delete();\n    }\n    const settings = extractProperties(doc.settings());\n    doc.delete();\n    return {\n      objects,\n      materials,\n      layers,\n      views,\n      namedViews,\n      groups,\n      settings\n    };\n  }\n  function extractObjectData(object, doc) {\n    const _geometry = object.geometry();\n    const _attributes = object.attributes();\n    let objectType = _geometry.objectType;\n    let geometry, attributes, position, data, mesh;\n    switch (objectType) {\n      case rhino.ObjectType.Curve:\n        const pts = curveToPoints(_geometry, 100);\n        position = {};\n        attributes = {};\n        data = {};\n        position.itemSize = 3;\n        position.type = \"Float32Array\";\n        position.array = [];\n        for (let j = 0; j < pts.length; j++) {\n          position.array.push(pts[j][0]);\n          position.array.push(pts[j][1]);\n          position.array.push(pts[j][2]);\n        }\n        attributes.position = position;\n        data.attributes = attributes;\n        geometry = {\n          data\n        };\n        break;\n      case rhino.ObjectType.Point:\n        const pt = _geometry.location;\n        position = {};\n        const color = {};\n        attributes = {};\n        data = {};\n        position.itemSize = 3;\n        position.type = \"Float32Array\";\n        position.array = [pt[0], pt[1], pt[2]];\n        const _color = _attributes.drawColor(doc);\n        color.itemSize = 3;\n        color.type = \"Float32Array\";\n        color.array = [_color.r / 255, _color.g / 255, _color.b / 255];\n        attributes.position = position;\n        attributes.color = color;\n        data.attributes = attributes;\n        geometry = {\n          data\n        };\n        break;\n      case rhino.ObjectType.PointSet:\n      case rhino.ObjectType.Mesh:\n        geometry = _geometry.toThreejsJSON();\n        break;\n      case rhino.ObjectType.Brep:\n        const faces = _geometry.faces();\n        mesh = new rhino.Mesh();\n        for (let faceIndex = 0; faceIndex < faces.count; faceIndex++) {\n          const face = faces.get(faceIndex);\n          const _mesh = face.getMesh(rhino.MeshType.Any);\n          if (_mesh) {\n            mesh.append(_mesh);\n            _mesh.delete();\n          }\n          face.delete();\n        }\n        if (mesh.faces().count > 0) {\n          mesh.compact();\n          geometry = mesh.toThreejsJSON();\n          faces.delete();\n        }\n        mesh.delete();\n        break;\n      case rhino.ObjectType.Extrusion:\n        mesh = _geometry.getMesh(rhino.MeshType.Any);\n        if (mesh) {\n          geometry = mesh.toThreejsJSON();\n          mesh.delete();\n        }\n        break;\n      case rhino.ObjectType.TextDot:\n        geometry = extractProperties(_geometry);\n        break;\n      case rhino.ObjectType.Light:\n        geometry = extractProperties(_geometry);\n        break;\n      case rhino.ObjectType.InstanceReference:\n        geometry = extractProperties(_geometry);\n        geometry.xform = extractProperties(_geometry.xform);\n        geometry.xform.array = _geometry.xform.toFloatArray(true);\n        break;\n      case rhino.ObjectType.SubD:\n        _geometry.subdivide(3);\n        mesh = rhino.Mesh.createFromSubDControlNet(_geometry);\n        if (mesh) {\n          geometry = mesh.toThreejsJSON();\n          mesh.delete();\n        }\n        break;\n      default:\n        console.warn(`THREE.3DMLoader: TODO: Implement ${objectType.constructor.name}`);\n        break;\n    }\n    if (geometry) {\n      attributes = extractProperties(_attributes);\n      attributes.geometry = extractProperties(_geometry);\n      if (_attributes.groupCount > 0) {\n        attributes.groupIds = _attributes.getGroupList();\n      }\n      if (_attributes.userStringCount > 0) {\n        attributes.userStrings = _attributes.getUserStrings();\n      }\n      if (_geometry.userStringCount > 0) {\n        attributes.geometry.userStrings = _geometry.getUserStrings();\n      }\n      attributes.drawColor = _attributes.drawColor(doc);\n      objectType = objectType.constructor.name;\n      objectType = objectType.substring(11, objectType.length);\n      return {\n        geometry,\n        attributes,\n        objectType\n      };\n    } else {\n      console.warn(`THREE.3DMLoader: ${objectType.constructor.name} has no associated mesh geometry.`);\n    }\n  }\n  function extractProperties(object) {\n    const result = {};\n    for (const property in object) {\n      const value = object[property];\n      if (typeof value !== \"function\") {\n        if (typeof value === \"object\" && value !== null && value.hasOwnProperty(\"constructor\")) {\n          result[property] = {\n            name: value.constructor.name,\n            value: value.value\n          };\n        } else {\n          result[property] = value;\n        }\n      }\n    }\n    return result;\n  }\n  function curveToPoints(curve, pointLimit) {\n    let pointCount = pointLimit;\n    let rc = [];\n    const ts = [];\n    if (curve instanceof rhino.LineCurve) {\n      return [curve.pointAtStart, curve.pointAtEnd];\n    }\n    if (curve instanceof rhino.PolylineCurve) {\n      pointCount = curve.pointCount;\n      for (let i = 0; i < pointCount; i++) {\n        rc.push(curve.point(i));\n      }\n      return rc;\n    }\n    if (curve instanceof rhino.PolyCurve) {\n      const segmentCount = curve.segmentCount;\n      for (let i = 0; i < segmentCount; i++) {\n        const segment = curve.segmentCurve(i);\n        const segmentArray = curveToPoints(segment, pointCount);\n        rc = rc.concat(segmentArray);\n        segment.delete();\n      }\n      return rc;\n    }\n    if (curve instanceof rhino.ArcCurve) {\n      pointCount = Math.floor(curve.angleDegrees / 5);\n      pointCount = pointCount < 2 ? 2 : pointCount;\n    }\n    if (curve instanceof rhino.NurbsCurve && curve.degree === 1) {\n      const pLine = curve.tryGetPolyline();\n      for (let i = 0; i < pLine.count; i++) {\n        rc.push(pLine.get(i));\n      }\n      pLine.delete();\n      return rc;\n    }\n    const domain = curve.domain;\n    const divisions = pointCount - 1;\n    for (let j = 0; j < pointCount; j++) {\n      const t = domain[0] + j / divisions * (domain[1] - domain[0]);\n      if (t === domain[0] || t === domain[1]) {\n        ts.push(t);\n        continue;\n      }\n      const tan = curve.tangentAt(t);\n      const prevTan = curve.tangentAt(ts.slice(-1)[0]);\n      const tS = tan[0] * tan[0] + tan[1] * tan[1] + tan[2] * tan[2];\n      const ptS = prevTan[0] * prevTan[0] + prevTan[1] * prevTan[1] + prevTan[2] * prevTan[2];\n      const denominator = Math.sqrt(tS * ptS);\n      let angle;\n      if (denominator === 0) {\n        angle = Math.PI / 2;\n      } else {\n        const theta = (tan.x * prevTan.x + tan.y * prevTan.y + tan.z * prevTan.z) / denominator;\n        angle = Math.acos(Math.max(-1, Math.min(1, theta)));\n      }\n      if (angle < 0.1) continue;\n      ts.push(t);\n    }\n    rc = ts.map(t => curve.pointAt(t));\n    return rc;\n  }\n}\nexport { Rhino3dmLoader };", "map": {"version": 3, "names": ["_taskCache", "WeakMap", "Rhino3dmLoader", "Loader", "constructor", "manager", "libraryPath", "libraryPending", "libraryBinary", "libraryConfig", "url", "workerLimit", "workerPool", "workerNextTaskID", "workerSourceURL", "workerConfig", "materials", "setLibraryPath", "path", "setWorkerLimit", "load", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "buffer", "has", "cachedTask", "get", "promise", "then", "catch", "decodeObjects", "debug", "console", "log", "map", "worker", "_taskLoad", "taskID", "taskCost", "byteLength", "objectPending", "_get<PERSON><PERSON><PERSON>", "_worker", "Promise", "resolve", "reject", "_callbacks", "postMessage", "type", "id", "message", "_createGeometry", "data", "_releaseTask", "set", "parse", "_compareMaterials", "material", "mat", "name", "color", "r", "g", "b", "i", "length", "m", "_mat", "JSON", "stringify", "push", "_createMaterial", "MeshStandardMaterial", "Color", "metalness", "side", "_diffuseColor", "diffuseColor", "diffusecolor", "transparent", "transparency", "opacity", "textureLoader", "TextureLoader", "textures", "texture", "image", "bumpMap", "alphaMap", "envMap", "object", "Object3D", "instanceDefinitionObjects", "instanceDefinitions", "instanceReferences", "userData", "layers", "groups", "settings", "objects", "obj", "attributes", "objectType", "_object", "materialIndex", "rMaterial", "_createObject", "layer", "layerIndex", "visible", "isInstanceDefinitionObject", "add", "iDef", "j", "objectIds", "objId", "p", "idoId", "iRef", "geometry", "parentIdefId", "iRefObject", "xf", "xform", "array", "matrix", "Matrix4", "applyMatrix4", "clone", "BufferGeometry<PERSON><PERSON>der", "_color", "hasOwnProperty", "PointsMaterial", "vertexColors", "sizeAttenuation", "size", "drawColor", "points", "Points", "mesh", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "castsShadows", "receiveShadow", "receivesShadows", "LineBasicMaterial", "lines", "Line", "ctx", "document", "createElement", "getContext", "font", "fontHeight", "fontFace", "width", "measureText", "text", "height", "window", "devicePixelRatio", "canvas", "style", "setTransform", "textBaseline", "textAlign", "fillStyle", "a", "fillRect", "fillText", "CanvasTexture", "minFilter", "LinearFilter", "wrapS", "ClampToEdgeWrapping", "wrapT", "SpriteMaterial", "depthTest", "sprite", "Sprite", "position", "point", "scale", "light", "isDirectionalLight", "DirectionalLight", "location", "target", "direction", "shadow", "normalBias", "isPointLight", "PointLight", "isRectangularLight", "RectAreaLight", "width2", "Math", "abs", "height2", "lookAt", "Vector3", "isSpotLight", "SpotLight", "angle", "spotAngleRadians", "isLinearLight", "warn", "intensity", "diffuse", "_initLibrary", "j<PERSON><PERSON><PERSON><PERSON>", "js<PERSON><PERSON><PERSON>", "binaryLoader", "binaryContent", "all", "jsContent2", "binaryContent2", "wasmBinary", "fn", "Rhino3dmWorker", "toString", "body", "substring", "indexOf", "lastIndexOf", "join", "URL", "createObjectURL", "Blob", "worker2", "Worker", "_taskCosts", "onmessage", "e", "error", "sort", "dispose", "terminate", "rhino", "RhinoModule", "onRuntimeInitialized", "rhino3dm", "self", "rhino2", "arr", "Uint8Array", "doc", "File3dm", "fromByteArray", "views", "namedViews", "objs", "cnt", "count", "extractObjectData", "delete", "idef", "idefAttributes", "extractProperties", "getObjectIds", "textureTypes", "TextureType", "Diffuse", "Bump", "Transparency", "Opacity", "Emap", "pbrTextureTypes", "PBR_BaseColor", "PBR_Subsurface", "PBR_SubsurfaceScattering", "PBR_SubsurfaceScatteringRadius", "PBR_Metallic", "PBR_Specular", "PBR_SpecularTint", "PBR_Roughness", "PBR_Anisotropic", "PBR_Anisotropic_Rotation", "PBR_Sheen", "PBR_SheenTint", "PBR_Clearcoat", "PBR_ClearcoatBump", "PBR_ClearcoatRoughness", "PBR_OpacityIor", "PBR_OpacityRoughness", "PBR_Emission", "PBR_AmbientOcclusion", "PBR_Displacement", "_material", "_pbrMaterial", "physicallyBased", "_texture", "getTexture", "textureType", "getEmbeddedFileAsBase64", "fileName", "supported", "pbMaterialProperties", "Object", "assign", "_layer", "_view", "view", "_<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_group", "group", "_geometry", "_attributes", "ObjectType", "Curve", "pts", "curveToPoints", "itemSize", "Point", "pt", "PointSet", "toThreejsJSON", "Brep", "faces", "faceIndex", "face", "_mesh", "<PERSON><PERSON><PERSON>", "MeshType", "Any", "append", "compact", "Extrusion", "TextDot", "Light", "InstanceReference", "toFloatArray", "SubD", "subdivide", "createFromSubDControlNet", "groupCount", "groupIds", "getGroupList", "userStringCount", "userStrings", "getUserStrings", "result", "property", "value", "curve", "pointLimit", "pointCount", "rc", "ts", "LineCurve", "pointAtStart", "pointAtEnd", "PolylineCurve", "PolyCurve", "segmentCount", "segment", "segmentCurve", "segmentArray", "concat", "ArcCurve", "floor", "angleDegrees", "NurbsCurve", "degree", "pLine", "tryGetPolyline", "domain", "divisions", "t", "tan", "tangentAt", "prevTan", "slice", "tS", "ptS", "denominator", "sqrt", "PI", "theta", "x", "y", "z", "acos", "max", "min", "pointAt"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/3DMLoader.js"], "sourcesContent": ["import {\n  BufferGeometry<PERSON>oader,\n  FileLoader,\n  Loader,\n  Object3D,\n  MeshStandardMaterial,\n  Mesh,\n  Color,\n  Points,\n  PointsMaterial,\n  Line,\n  LineBasicMaterial,\n  Matrix4,\n  DirectionalLight,\n  PointLight,\n  SpotLight,\n  RectAreaLight,\n  Vector3,\n  Sprite,\n  SpriteMaterial,\n  CanvasTexture,\n  LinearFilter,\n  ClampToEdgeWrapping,\n  TextureLoader,\n} from 'three'\n\nconst _taskCache = new WeakMap()\n\nclass Rhino3dmLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.libraryPath = ''\n    this.libraryPending = null\n    this.libraryBinary = null\n    this.libraryConfig = {}\n\n    this.url = ''\n\n    this.workerLimit = 4\n    this.workerPool = []\n    this.workerNextTaskID = 1\n    this.workerSourceURL = ''\n    this.workerConfig = {}\n\n    this.materials = []\n  }\n\n  setLibraryPath(path) {\n    this.libraryPath = path\n\n    return this\n  }\n\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit\n\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n\n    this.url = url\n\n    loader.load(\n      url,\n      (buffer) => {\n        // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n        // again from this thread.\n        if (_taskCache.has(buffer)) {\n          const cachedTask = _taskCache.get(buffer)\n\n          return cachedTask.promise.then(onLoad).catch(onError)\n        }\n\n        this.decodeObjects(buffer, url).then(onLoad).catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  debug() {\n    console.log(\n      'Task load: ',\n      this.workerPool.map((worker) => worker._taskLoad),\n    )\n  }\n\n  decodeObjects(buffer, url) {\n    let worker\n    let taskID\n\n    const taskCost = buffer.byteLength\n\n    const objectPending = this._getWorker(taskCost)\n      .then((_worker) => {\n        worker = _worker\n        taskID = this.workerNextTaskID++ //hmmm\n\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = { resolve, reject }\n\n          worker.postMessage({ type: 'decode', id: taskID, buffer }, [buffer])\n\n          //this.debug();\n        })\n      })\n      .then((message) => this._createGeometry(message.data))\n\n    // Remove task from the task list.\n    // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n    objectPending\n      .catch(() => true)\n      .then(() => {\n        if (worker && taskID) {\n          this._releaseTask(worker, taskID)\n\n          //this.debug();\n        }\n      })\n\n    // Cache the task result.\n    _taskCache.set(buffer, {\n      url: url,\n      promise: objectPending,\n    })\n\n    return objectPending\n  }\n\n  parse(data, onLoad, onError) {\n    this.decodeObjects(data, '').then(onLoad).catch(onError)\n  }\n\n  _compareMaterials(material) {\n    const mat = {}\n    mat.name = material.name\n    mat.color = {}\n    mat.color.r = material.color.r\n    mat.color.g = material.color.g\n    mat.color.b = material.color.b\n    mat.type = material.type\n\n    for (let i = 0; i < this.materials.length; i++) {\n      const m = this.materials[i]\n      const _mat = {}\n      _mat.name = m.name\n      _mat.color = {}\n      _mat.color.r = m.color.r\n      _mat.color.g = m.color.g\n      _mat.color.b = m.color.b\n      _mat.type = m.type\n\n      if (JSON.stringify(mat) === JSON.stringify(_mat)) {\n        return m\n      }\n    }\n\n    this.materials.push(material)\n\n    return material\n  }\n\n  _createMaterial(material) {\n    if (material === undefined) {\n      return new MeshStandardMaterial({\n        color: new Color(1, 1, 1),\n        metalness: 0.8,\n        name: 'default',\n        side: 2,\n      })\n    }\n\n    const _diffuseColor = material.diffuseColor\n\n    const diffusecolor = new Color(_diffuseColor.r / 255.0, _diffuseColor.g / 255.0, _diffuseColor.b / 255.0)\n\n    if (_diffuseColor.r === 0 && _diffuseColor.g === 0 && _diffuseColor.b === 0) {\n      diffusecolor.r = 1\n      diffusecolor.g = 1\n      diffusecolor.b = 1\n    }\n\n    // console.log( material );\n\n    const mat = new MeshStandardMaterial({\n      color: diffusecolor,\n      name: material.name,\n      side: 2,\n      transparent: material.transparency > 0 ? true : false,\n      opacity: 1.0 - material.transparency,\n    })\n\n    const textureLoader = new TextureLoader()\n\n    for (let i = 0; i < material.textures.length; i++) {\n      const texture = material.textures[i]\n\n      if (texture.image !== null) {\n        const map = textureLoader.load(texture.image)\n\n        switch (texture.type) {\n          case 'Diffuse':\n            mat.map = map\n\n            break\n\n          case 'Bump':\n            mat.bumpMap = map\n\n            break\n\n          case 'Transparency':\n            mat.alphaMap = map\n            mat.transparent = true\n\n            break\n\n          case 'Emap':\n            mat.envMap = map\n\n            break\n        }\n      }\n    }\n\n    return mat\n  }\n\n  _createGeometry(data) {\n    // console.log(data);\n\n    const object = new Object3D()\n    const instanceDefinitionObjects = []\n    const instanceDefinitions = []\n    const instanceReferences = []\n\n    object.userData['layers'] = data.layers\n    object.userData['groups'] = data.groups\n    object.userData['settings'] = data.settings\n    object.userData['objectType'] = 'File3dm'\n    object.userData['materials'] = null\n    object.name = this.url\n\n    let objects = data.objects\n    const materials = data.materials\n\n    for (let i = 0; i < objects.length; i++) {\n      const obj = objects[i]\n      const attributes = obj.attributes\n\n      switch (obj.objectType) {\n        case 'InstanceDefinition':\n          instanceDefinitions.push(obj)\n\n          break\n\n        case 'InstanceReference':\n          instanceReferences.push(obj)\n\n          break\n\n        default:\n          let _object\n\n          if (attributes.materialIndex >= 0) {\n            const rMaterial = materials[attributes.materialIndex]\n            let material = this._createMaterial(rMaterial)\n            material = this._compareMaterials(material)\n            _object = this._createObject(obj, material)\n          } else {\n            const material = this._createMaterial()\n            _object = this._createObject(obj, material)\n          }\n\n          if (_object === undefined) {\n            continue\n          }\n\n          const layer = data.layers[attributes.layerIndex]\n\n          _object.visible = layer ? data.layers[attributes.layerIndex].visible : true\n\n          if (attributes.isInstanceDefinitionObject) {\n            instanceDefinitionObjects.push(_object)\n          } else {\n            object.add(_object)\n          }\n\n          break\n      }\n    }\n\n    for (let i = 0; i < instanceDefinitions.length; i++) {\n      const iDef = instanceDefinitions[i]\n\n      objects = []\n\n      for (let j = 0; j < iDef.attributes.objectIds.length; j++) {\n        const objId = iDef.attributes.objectIds[j]\n\n        for (let p = 0; p < instanceDefinitionObjects.length; p++) {\n          const idoId = instanceDefinitionObjects[p].userData.attributes.id\n\n          if (objId === idoId) {\n            objects.push(instanceDefinitionObjects[p])\n          }\n        }\n      }\n\n      // Currently clones geometry and does not take advantage of instancing\n\n      for (let j = 0; j < instanceReferences.length; j++) {\n        const iRef = instanceReferences[j]\n\n        if (iRef.geometry.parentIdefId === iDef.attributes.id) {\n          const iRefObject = new Object3D()\n          const xf = iRef.geometry.xform.array\n\n          const matrix = new Matrix4()\n          matrix.set(\n            xf[0],\n            xf[1],\n            xf[2],\n            xf[3],\n            xf[4],\n            xf[5],\n            xf[6],\n            xf[7],\n            xf[8],\n            xf[9],\n            xf[10],\n            xf[11],\n            xf[12],\n            xf[13],\n            xf[14],\n            xf[15],\n          )\n\n          iRefObject.applyMatrix4(matrix)\n\n          for (let p = 0; p < objects.length; p++) {\n            iRefObject.add(objects[p].clone(true))\n          }\n\n          object.add(iRefObject)\n        }\n      }\n    }\n\n    object.userData['materials'] = this.materials\n    return object\n  }\n\n  _createObject(obj, mat) {\n    const loader = new BufferGeometryLoader()\n\n    const attributes = obj.attributes\n\n    let geometry, material, _color, color\n\n    switch (obj.objectType) {\n      case 'Point':\n      case 'PointSet':\n        geometry = loader.parse(obj.geometry)\n\n        if (geometry.attributes.hasOwnProperty('color')) {\n          material = new PointsMaterial({ vertexColors: true, sizeAttenuation: false, size: 2 })\n        } else {\n          _color = attributes.drawColor\n          color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n          material = new PointsMaterial({ color: color, sizeAttenuation: false, size: 2 })\n        }\n\n        material = this._compareMaterials(material)\n\n        const points = new Points(geometry, material)\n        points.userData['attributes'] = attributes\n        points.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          points.name = attributes.name\n        }\n\n        return points\n\n      case 'Mesh':\n      case 'Extrusion':\n      case 'SubD':\n      case 'Brep':\n        if (obj.geometry === null) return\n\n        geometry = loader.parse(obj.geometry)\n\n        if (geometry.attributes.hasOwnProperty('color')) {\n          mat.vertexColors = true\n        }\n\n        if (mat === null) {\n          mat = this._createMaterial()\n          mat = this._compareMaterials(mat)\n        }\n\n        const mesh = new Mesh(geometry, mat)\n        mesh.castShadow = attributes.castsShadows\n        mesh.receiveShadow = attributes.receivesShadows\n        mesh.userData['attributes'] = attributes\n        mesh.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          mesh.name = attributes.name\n        }\n\n        return mesh\n\n      case 'Curve':\n        geometry = loader.parse(obj.geometry)\n\n        _color = attributes.drawColor\n        color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n\n        material = new LineBasicMaterial({ color: color })\n        material = this._compareMaterials(material)\n\n        const lines = new Line(geometry, material)\n        lines.userData['attributes'] = attributes\n        lines.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          lines.name = attributes.name\n        }\n\n        return lines\n\n      case 'TextDot':\n        geometry = obj.geometry\n\n        const ctx = document.createElement('canvas').getContext('2d')\n        const font = `${geometry.fontHeight}px ${geometry.fontFace}`\n        ctx.font = font\n        const width = ctx.measureText(geometry.text).width + 10\n        const height = geometry.fontHeight + 10\n\n        const r = window.devicePixelRatio\n\n        ctx.canvas.width = width * r\n        ctx.canvas.height = height * r\n        ctx.canvas.style.width = width + 'px'\n        ctx.canvas.style.height = height + 'px'\n        ctx.setTransform(r, 0, 0, r, 0, 0)\n\n        ctx.font = font\n        ctx.textBaseline = 'middle'\n        ctx.textAlign = 'center'\n        color = attributes.drawColor\n        ctx.fillStyle = `rgba(${color.r},${color.g},${color.b},${color.a})`\n        ctx.fillRect(0, 0, width, height)\n        ctx.fillStyle = 'white'\n        ctx.fillText(geometry.text, width / 2, height / 2)\n\n        const texture = new CanvasTexture(ctx.canvas)\n        texture.minFilter = LinearFilter\n        texture.wrapS = ClampToEdgeWrapping\n        texture.wrapT = ClampToEdgeWrapping\n\n        material = new SpriteMaterial({ map: texture, depthTest: false })\n        const sprite = new Sprite(material)\n        sprite.position.set(geometry.point[0], geometry.point[1], geometry.point[2])\n        sprite.scale.set(width / 10, height / 10, 1.0)\n\n        sprite.userData['attributes'] = attributes\n        sprite.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          sprite.name = attributes.name\n        }\n\n        return sprite\n\n      case 'Light':\n        geometry = obj.geometry\n\n        let light\n\n        if (geometry.isDirectionalLight) {\n          light = new DirectionalLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2])\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isPointLight) {\n          light = new PointLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isRectangularLight) {\n          light = new RectAreaLight()\n\n          const width = Math.abs(geometry.width[2])\n          const height = Math.abs(geometry.length[0])\n\n          light.position.set(geometry.location[0] - height / 2, geometry.location[1], geometry.location[2] - width / 2)\n\n          light.height = height\n          light.width = width\n\n          light.lookAt(new Vector3(geometry.direction[0], geometry.direction[1], geometry.direction[2]))\n        } else if (geometry.isSpotLight) {\n          light = new SpotLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2])\n          light.angle = geometry.spotAngleRadians\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isLinearLight) {\n          console.warn('THREE.3DMLoader:  No conversion exists for linear lights.')\n\n          return\n        }\n\n        if (light) {\n          light.intensity = geometry.intensity\n          _color = geometry.diffuse\n          color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n          light.color = color\n          light.userData['attributes'] = attributes\n          light.userData['objectType'] = obj.objectType\n        }\n\n        return light\n    }\n  }\n\n  _initLibrary() {\n    if (!this.libraryPending) {\n      // Load rhino3dm wrapper.\n      const jsLoader = new FileLoader(this.manager)\n      jsLoader.setPath(this.libraryPath)\n      const jsContent = new Promise((resolve, reject) => {\n        jsLoader.load('rhino3dm.js', resolve, undefined, reject)\n      })\n\n      // Load rhino3dm WASM binary.\n      const binaryLoader = new FileLoader(this.manager)\n      binaryLoader.setPath(this.libraryPath)\n      binaryLoader.setResponseType('arraybuffer')\n      const binaryContent = new Promise((resolve, reject) => {\n        binaryLoader.load('rhino3dm.wasm', resolve, undefined, reject)\n      })\n\n      this.libraryPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n        //this.libraryBinary = binaryContent;\n        this.libraryConfig.wasmBinary = binaryContent\n\n        const fn = Rhino3dmWorker.toString()\n\n        const body = [\n          '/* rhino3dm.js */',\n          jsContent,\n          '/* worker */',\n          fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n        ].join('\\n')\n\n        this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n      })\n    }\n\n    return this.libraryPending\n  }\n\n  _getWorker(taskCost) {\n    return this._initLibrary().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker = new Worker(this.workerSourceURL)\n\n        worker._callbacks = {}\n        worker._taskCosts = {}\n        worker._taskLoad = 0\n\n        worker.postMessage({\n          type: 'init',\n          libraryConfig: this.libraryConfig,\n        })\n\n        worker.onmessage = function (e) {\n          const message = e.data\n\n          switch (message.type) {\n            case 'decode':\n              worker._callbacks[message.id].resolve(message)\n              break\n\n            case 'error':\n              worker._callbacks[message.id].reject(message)\n              break\n\n            default:\n              console.error('THREE.Rhino3dmLoader: Unexpected message, \"' + message.type + '\"')\n          }\n        }\n\n        this.workerPool.push(worker)\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1\n        })\n      }\n\n      const worker = this.workerPool[this.workerPool.length - 1]\n\n      worker._taskLoad += taskCost\n\n      return worker\n    })\n  }\n\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID]\n    delete worker._callbacks[taskID]\n    delete worker._taskCosts[taskID]\n  }\n\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate()\n    }\n\n    this.workerPool.length = 0\n\n    return this\n  }\n}\n\n/* WEB WORKER */\n\nfunction Rhino3dmWorker() {\n  let libraryPending\n  let libraryConfig\n  let rhino\n\n  onmessage = function (e) {\n    const message = e.data\n\n    switch (message.type) {\n      case 'init':\n        libraryConfig = message.libraryConfig\n        const wasmBinary = libraryConfig.wasmBinary\n        let RhinoModule\n        libraryPending = new Promise(function (resolve) {\n          /* Like Basis Loader */\n          RhinoModule = { wasmBinary, onRuntimeInitialized: resolve }\n\n          rhino3dm(RhinoModule)\n        }).then(() => {\n          rhino = RhinoModule\n        })\n\n        break\n\n      case 'decode':\n        const buffer = message.buffer\n        libraryPending.then(() => {\n          const data = decodeObjects(rhino, buffer)\n\n          self.postMessage({ type: 'decode', id: message.id, data })\n        })\n\n        break\n    }\n  }\n\n  function decodeObjects(rhino, buffer) {\n    const arr = new Uint8Array(buffer)\n    const doc = rhino.File3dm.fromByteArray(arr)\n\n    const objects = []\n    const materials = []\n    const layers = []\n    const views = []\n    const namedViews = []\n    const groups = []\n\n    //Handle objects\n\n    const objs = doc.objects()\n    const cnt = objs.count\n\n    for (let i = 0; i < cnt; i++) {\n      const _object = objs.get(i)\n\n      const object = extractObjectData(_object, doc)\n\n      _object.delete()\n\n      if (object) {\n        objects.push(object)\n      }\n    }\n\n    // Handle instance definitions\n    // console.log( `Instance Definitions Count: ${doc.instanceDefinitions().count()}` );\n\n    for (let i = 0; i < doc.instanceDefinitions().count(); i++) {\n      const idef = doc.instanceDefinitions().get(i)\n      const idefAttributes = extractProperties(idef)\n      idefAttributes.objectIds = idef.getObjectIds()\n\n      objects.push({ geometry: null, attributes: idefAttributes, objectType: 'InstanceDefinition' })\n    }\n\n    // Handle materials\n\n    const textureTypes = [\n      // rhino.TextureType.Bitmap,\n      rhino.TextureType.Diffuse,\n      rhino.TextureType.Bump,\n      rhino.TextureType.Transparency,\n      rhino.TextureType.Opacity,\n      rhino.TextureType.Emap,\n    ]\n\n    const pbrTextureTypes = [\n      rhino.TextureType.PBR_BaseColor,\n      rhino.TextureType.PBR_Subsurface,\n      rhino.TextureType.PBR_SubsurfaceScattering,\n      rhino.TextureType.PBR_SubsurfaceScatteringRadius,\n      rhino.TextureType.PBR_Metallic,\n      rhino.TextureType.PBR_Specular,\n      rhino.TextureType.PBR_SpecularTint,\n      rhino.TextureType.PBR_Roughness,\n      rhino.TextureType.PBR_Anisotropic,\n      rhino.TextureType.PBR_Anisotropic_Rotation,\n      rhino.TextureType.PBR_Sheen,\n      rhino.TextureType.PBR_SheenTint,\n      rhino.TextureType.PBR_Clearcoat,\n      rhino.TextureType.PBR_ClearcoatBump,\n      rhino.TextureType.PBR_ClearcoatRoughness,\n      rhino.TextureType.PBR_OpacityIor,\n      rhino.TextureType.PBR_OpacityRoughness,\n      rhino.TextureType.PBR_Emission,\n      rhino.TextureType.PBR_AmbientOcclusion,\n      rhino.TextureType.PBR_Displacement,\n    ]\n\n    for (let i = 0; i < doc.materials().count(); i++) {\n      const _material = doc.materials().get(i)\n      const _pbrMaterial = _material.physicallyBased()\n\n      let material = extractProperties(_material)\n\n      const textures = []\n\n      for (let j = 0; j < textureTypes.length; j++) {\n        const _texture = _material.getTexture(textureTypes[j])\n        if (_texture) {\n          let textureType = textureTypes[j].constructor.name\n          textureType = textureType.substring(12, textureType.length)\n          const texture = { type: textureType }\n\n          const image = doc.getEmbeddedFileAsBase64(_texture.fileName)\n\n          if (image) {\n            texture.image = 'data:image/png;base64,' + image\n          } else {\n            console.warn(`THREE.3DMLoader: Image for ${textureType} texture not embedded in file.`)\n            texture.image = null\n          }\n\n          textures.push(texture)\n\n          _texture.delete()\n        }\n      }\n\n      material.textures = textures\n\n      if (_pbrMaterial.supported) {\n        console.log('pbr true')\n\n        for (let j = 0; j < pbrTextureTypes.length; j++) {\n          const _texture = _material.getTexture(textureTypes[j])\n          if (_texture) {\n            const image = doc.getEmbeddedFileAsBase64(_texture.fileName)\n            let textureType = textureTypes[j].constructor.name\n            textureType = textureType.substring(12, textureType.length)\n            const texture = { type: textureType, image: 'data:image/png;base64,' + image }\n            textures.push(texture)\n\n            _texture.delete()\n          }\n        }\n\n        const pbMaterialProperties = extractProperties(_material.physicallyBased())\n\n        material = Object.assign(pbMaterialProperties, material)\n      }\n\n      materials.push(material)\n\n      _material.delete()\n      _pbrMaterial.delete()\n    }\n\n    // Handle layers\n\n    for (let i = 0; i < doc.layers().count(); i++) {\n      const _layer = doc.layers().get(i)\n      const layer = extractProperties(_layer)\n\n      layers.push(layer)\n\n      _layer.delete()\n    }\n\n    // Handle views\n\n    for (let i = 0; i < doc.views().count(); i++) {\n      const _view = doc.views().get(i)\n      const view = extractProperties(_view)\n\n      views.push(view)\n\n      _view.delete()\n    }\n\n    // Handle named views\n\n    for (let i = 0; i < doc.namedViews().count(); i++) {\n      const _namedView = doc.namedViews().get(i)\n      const namedView = extractProperties(_namedView)\n\n      namedViews.push(namedView)\n\n      _namedView.delete()\n    }\n\n    // Handle groups\n\n    for (let i = 0; i < doc.groups().count(); i++) {\n      const _group = doc.groups().get(i)\n      const group = extractProperties(_group)\n\n      groups.push(group)\n\n      _group.delete()\n    }\n\n    // Handle settings\n\n    const settings = extractProperties(doc.settings())\n\n    //TODO: Handle other document stuff like dimstyles, instance definitions, bitmaps etc.\n\n    // Handle dimstyles\n    // console.log( `Dimstyle Count: ${doc.dimstyles().count()}` );\n\n    // Handle bitmaps\n    // console.log( `Bitmap Count: ${doc.bitmaps().count()}` );\n\n    // Handle strings -- this seems to be broken at the moment in rhino3dm\n    // console.log( `Document Strings Count: ${doc.strings().count()}` );\n\n    /*\n\t\tfor( var i = 0; i < doc.strings().count(); i++ ){\n\n\t\t\tvar _string= doc.strings().get( i );\n\n\t\t\tconsole.log(_string);\n\t\t\tvar string = extractProperties( _group );\n\n\t\t\tstrings.push( string );\n\n\t\t\t_string.delete();\n\n\t\t}\n\t\t*/\n\n    doc.delete()\n\n    return { objects, materials, layers, views, namedViews, groups, settings }\n  }\n\n  function extractObjectData(object, doc) {\n    const _geometry = object.geometry()\n    const _attributes = object.attributes()\n    let objectType = _geometry.objectType\n    let geometry, attributes, position, data, mesh\n\n    // skip instance definition objects\n    //if( _attributes.isInstanceDefinitionObject ) { continue; }\n\n    // TODO: handle other geometry types\n    switch (objectType) {\n      case rhino.ObjectType.Curve:\n        const pts = curveToPoints(_geometry, 100)\n\n        position = {}\n        attributes = {}\n        data = {}\n\n        position.itemSize = 3\n        position.type = 'Float32Array'\n        position.array = []\n\n        for (let j = 0; j < pts.length; j++) {\n          position.array.push(pts[j][0])\n          position.array.push(pts[j][1])\n          position.array.push(pts[j][2])\n        }\n\n        attributes.position = position\n        data.attributes = attributes\n\n        geometry = { data }\n\n        break\n\n      case rhino.ObjectType.Point:\n        const pt = _geometry.location\n\n        position = {}\n        const color = {}\n        attributes = {}\n        data = {}\n\n        position.itemSize = 3\n        position.type = 'Float32Array'\n        position.array = [pt[0], pt[1], pt[2]]\n\n        const _color = _attributes.drawColor(doc)\n\n        color.itemSize = 3\n        color.type = 'Float32Array'\n        color.array = [_color.r / 255.0, _color.g / 255.0, _color.b / 255.0]\n\n        attributes.position = position\n        attributes.color = color\n        data.attributes = attributes\n\n        geometry = { data }\n\n        break\n\n      case rhino.ObjectType.PointSet:\n      case rhino.ObjectType.Mesh:\n        geometry = _geometry.toThreejsJSON()\n\n        break\n\n      case rhino.ObjectType.Brep:\n        const faces = _geometry.faces()\n        mesh = new rhino.Mesh()\n\n        for (let faceIndex = 0; faceIndex < faces.count; faceIndex++) {\n          const face = faces.get(faceIndex)\n          const _mesh = face.getMesh(rhino.MeshType.Any)\n\n          if (_mesh) {\n            mesh.append(_mesh)\n            _mesh.delete()\n          }\n\n          face.delete()\n        }\n\n        if (mesh.faces().count > 0) {\n          mesh.compact()\n          geometry = mesh.toThreejsJSON()\n          faces.delete()\n        }\n\n        mesh.delete()\n\n        break\n\n      case rhino.ObjectType.Extrusion:\n        mesh = _geometry.getMesh(rhino.MeshType.Any)\n\n        if (mesh) {\n          geometry = mesh.toThreejsJSON()\n          mesh.delete()\n        }\n\n        break\n\n      case rhino.ObjectType.TextDot:\n        geometry = extractProperties(_geometry)\n\n        break\n\n      case rhino.ObjectType.Light:\n        geometry = extractProperties(_geometry)\n\n        break\n\n      case rhino.ObjectType.InstanceReference:\n        geometry = extractProperties(_geometry)\n        geometry.xform = extractProperties(_geometry.xform)\n        geometry.xform.array = _geometry.xform.toFloatArray(true)\n\n        break\n\n      case rhino.ObjectType.SubD:\n        // TODO: precalculate resulting vertices and faces and warn on excessive results\n        _geometry.subdivide(3)\n        mesh = rhino.Mesh.createFromSubDControlNet(_geometry)\n        if (mesh) {\n          geometry = mesh.toThreejsJSON()\n          mesh.delete()\n        }\n\n        break\n\n      /*\n\t\t\t\tcase rhino.ObjectType.Annotation:\n\t\t\t\tcase rhino.ObjectType.Hatch:\n\t\t\t\tcase rhino.ObjectType.ClipPlane:\n\t\t\t\t*/\n\n      default:\n        console.warn(`THREE.3DMLoader: TODO: Implement ${objectType.constructor.name}`)\n        break\n    }\n\n    if (geometry) {\n      attributes = extractProperties(_attributes)\n      attributes.geometry = extractProperties(_geometry)\n\n      if (_attributes.groupCount > 0) {\n        attributes.groupIds = _attributes.getGroupList()\n      }\n\n      if (_attributes.userStringCount > 0) {\n        attributes.userStrings = _attributes.getUserStrings()\n      }\n\n      if (_geometry.userStringCount > 0) {\n        attributes.geometry.userStrings = _geometry.getUserStrings()\n      }\n\n      attributes.drawColor = _attributes.drawColor(doc)\n\n      objectType = objectType.constructor.name\n      objectType = objectType.substring(11, objectType.length)\n\n      return { geometry, attributes, objectType }\n    } else {\n      console.warn(`THREE.3DMLoader: ${objectType.constructor.name} has no associated mesh geometry.`)\n    }\n  }\n\n  function extractProperties(object) {\n    const result = {}\n\n    for (const property in object) {\n      const value = object[property]\n\n      if (typeof value !== 'function') {\n        if (typeof value === 'object' && value !== null && value.hasOwnProperty('constructor')) {\n          result[property] = { name: value.constructor.name, value: value.value }\n        } else {\n          result[property] = value\n        }\n      } else {\n        // these are functions that could be called to extract more data.\n        //console.log( `${property}: ${object[ property ].constructor.name}` );\n      }\n    }\n\n    return result\n  }\n\n  function curveToPoints(curve, pointLimit) {\n    let pointCount = pointLimit\n    let rc = []\n    const ts = []\n\n    if (curve instanceof rhino.LineCurve) {\n      return [curve.pointAtStart, curve.pointAtEnd]\n    }\n\n    if (curve instanceof rhino.PolylineCurve) {\n      pointCount = curve.pointCount\n      for (let i = 0; i < pointCount; i++) {\n        rc.push(curve.point(i))\n      }\n\n      return rc\n    }\n\n    if (curve instanceof rhino.PolyCurve) {\n      const segmentCount = curve.segmentCount\n\n      for (let i = 0; i < segmentCount; i++) {\n        const segment = curve.segmentCurve(i)\n        const segmentArray = curveToPoints(segment, pointCount)\n        rc = rc.concat(segmentArray)\n        segment.delete()\n      }\n\n      return rc\n    }\n\n    if (curve instanceof rhino.ArcCurve) {\n      pointCount = Math.floor(curve.angleDegrees / 5)\n      pointCount = pointCount < 2 ? 2 : pointCount\n      // alternative to this hardcoded version: https://stackoverflow.com/a/18499923/2179399\n    }\n\n    if (curve instanceof rhino.NurbsCurve && curve.degree === 1) {\n      const pLine = curve.tryGetPolyline()\n\n      for (let i = 0; i < pLine.count; i++) {\n        rc.push(pLine.get(i))\n      }\n\n      pLine.delete()\n\n      return rc\n    }\n\n    const domain = curve.domain\n    const divisions = pointCount - 1.0\n\n    for (let j = 0; j < pointCount; j++) {\n      const t = domain[0] + (j / divisions) * (domain[1] - domain[0])\n\n      if (t === domain[0] || t === domain[1]) {\n        ts.push(t)\n        continue\n      }\n\n      const tan = curve.tangentAt(t)\n      const prevTan = curve.tangentAt(ts.slice(-1)[0])\n\n      // Duplicated from THREE.Vector3\n      // How to pass imports to worker?\n\n      const tS = tan[0] * tan[0] + tan[1] * tan[1] + tan[2] * tan[2]\n      const ptS = prevTan[0] * prevTan[0] + prevTan[1] * prevTan[1] + prevTan[2] * prevTan[2]\n\n      const denominator = Math.sqrt(tS * ptS)\n\n      let angle\n\n      if (denominator === 0) {\n        angle = Math.PI / 2\n      } else {\n        const theta = (tan.x * prevTan.x + tan.y * prevTan.y + tan.z * prevTan.z) / denominator\n        angle = Math.acos(Math.max(-1, Math.min(1, theta)))\n      }\n\n      if (angle < 0.1) continue\n\n      ts.push(t)\n    }\n\n    rc = ts.map((t) => curve.pointAt(t))\n    return rc\n  }\n}\n\nexport { Rhino3dmLoader }\n"], "mappings": ";AA0BA,MAAMA,UAAA,GAAa,mBAAIC,OAAA,CAAS;AAEhC,MAAMC,cAAA,SAAuBC,MAAA,CAAO;EAClCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,WAAA,GAAc;IACnB,KAAKC,cAAA,GAAiB;IACtB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,aAAA,GAAgB,CAAE;IAEvB,KAAKC,GAAA,GAAM;IAEX,KAAKC,WAAA,GAAc;IACnB,KAAKC,UAAA,GAAa,EAAE;IACpB,KAAKC,gBAAA,GAAmB;IACxB,KAAKC,eAAA,GAAkB;IACvB,KAAKC,YAAA,GAAe,CAAE;IAEtB,KAAKC,SAAA,GAAY,EAAE;EACpB;EAEDC,eAAeC,IAAA,EAAM;IACnB,KAAKZ,WAAA,GAAcY,IAAA;IAEnB,OAAO;EACR;EAEDC,eAAeR,WAAA,EAAa;IAC1B,KAAKA,WAAA,GAAcA,WAAA;IAEnB,OAAO;EACR;EAEDS,KAAKV,GAAA,EAAKW,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKpB,OAAO;IAE1CmB,MAAA,CAAOE,OAAA,CAAQ,KAAKR,IAAI;IACxBM,MAAA,CAAOG,eAAA,CAAgB,aAAa;IACpCH,MAAA,CAAOI,gBAAA,CAAiB,KAAKC,aAAa;IAE1C,KAAKnB,GAAA,GAAMA,GAAA;IAEXc,MAAA,CAAOJ,IAAA,CACLV,GAAA,EACCoB,MAAA,IAAW;MAGV,IAAI9B,UAAA,CAAW+B,GAAA,CAAID,MAAM,GAAG;QAC1B,MAAME,UAAA,GAAahC,UAAA,CAAWiC,GAAA,CAAIH,MAAM;QAExC,OAAOE,UAAA,CAAWE,OAAA,CAAQC,IAAA,CAAKd,MAAM,EAAEe,KAAA,CAAMb,OAAO;MACrD;MAED,KAAKc,aAAA,CAAcP,MAAA,EAAQpB,GAAG,EAAEyB,IAAA,CAAKd,MAAM,EAAEe,KAAA,CAAMb,OAAO;IAC3D,GACDD,UAAA,EACAC,OACD;EACF;EAEDe,MAAA,EAAQ;IACNC,OAAA,CAAQC,GAAA,CACN,eACA,KAAK5B,UAAA,CAAW6B,GAAA,CAAKC,MAAA,IAAWA,MAAA,CAAOC,SAAS,CACjD;EACF;EAEDN,cAAcP,MAAA,EAAQpB,GAAA,EAAK;IACzB,IAAIgC,MAAA;IACJ,IAAIE,MAAA;IAEJ,MAAMC,QAAA,GAAWf,MAAA,CAAOgB,UAAA;IAExB,MAAMC,aAAA,GAAgB,KAAKC,UAAA,CAAWH,QAAQ,EAC3CV,IAAA,CAAMc,OAAA,IAAY;MACjBP,MAAA,GAASO,OAAA;MACTL,MAAA,GAAS,KAAK/B,gBAAA;MAEd,OAAO,IAAIqC,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;QACtCV,MAAA,CAAOW,UAAA,CAAWT,MAAM,IAAI;UAAEO,OAAA;UAASC;QAAQ;QAE/CV,MAAA,CAAOY,WAAA,CAAY;UAAEC,IAAA,EAAM;UAAUC,EAAA,EAAIZ,MAAA;UAAQd;QAAM,GAAI,CAACA,MAAM,CAAC;MAG7E,CAAS;IACT,CAAO,EACAK,IAAA,CAAMsB,OAAA,IAAY,KAAKC,eAAA,CAAgBD,OAAA,CAAQE,IAAI,CAAC;IAIvDZ,aAAA,CACGX,KAAA,CAAM,MAAM,IAAI,EAChBD,IAAA,CAAK,MAAM;MACV,IAAIO,MAAA,IAAUE,MAAA,EAAQ;QACpB,KAAKgB,YAAA,CAAalB,MAAA,EAAQE,MAAM;MAGjC;IACT,CAAO;IAGH5C,UAAA,CAAW6D,GAAA,CAAI/B,MAAA,EAAQ;MACrBpB,GAAA;MACAwB,OAAA,EAASa;IACf,CAAK;IAED,OAAOA,aAAA;EACR;EAEDe,MAAMH,IAAA,EAAMtC,MAAA,EAAQE,OAAA,EAAS;IAC3B,KAAKc,aAAA,CAAcsB,IAAA,EAAM,EAAE,EAAExB,IAAA,CAAKd,MAAM,EAAEe,KAAA,CAAMb,OAAO;EACxD;EAEDwC,kBAAkBC,QAAA,EAAU;IAC1B,MAAMC,GAAA,GAAM,CAAE;IACdA,GAAA,CAAIC,IAAA,GAAOF,QAAA,CAASE,IAAA;IACpBD,GAAA,CAAIE,KAAA,GAAQ,CAAE;IACdF,GAAA,CAAIE,KAAA,CAAMC,CAAA,GAAIJ,QAAA,CAASG,KAAA,CAAMC,CAAA;IAC7BH,GAAA,CAAIE,KAAA,CAAME,CAAA,GAAIL,QAAA,CAASG,KAAA,CAAME,CAAA;IAC7BJ,GAAA,CAAIE,KAAA,CAAMG,CAAA,GAAIN,QAAA,CAASG,KAAA,CAAMG,CAAA;IAC7BL,GAAA,CAAIV,IAAA,GAAOS,QAAA,CAAST,IAAA;IAEpB,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKvD,SAAA,CAAUwD,MAAA,EAAQD,CAAA,IAAK;MAC9C,MAAME,CAAA,GAAI,KAAKzD,SAAA,CAAUuD,CAAC;MAC1B,MAAMG,IAAA,GAAO,CAAE;MACfA,IAAA,CAAKR,IAAA,GAAOO,CAAA,CAAEP,IAAA;MACdQ,IAAA,CAAKP,KAAA,GAAQ,CAAE;MACfO,IAAA,CAAKP,KAAA,CAAMC,CAAA,GAAIK,CAAA,CAAEN,KAAA,CAAMC,CAAA;MACvBM,IAAA,CAAKP,KAAA,CAAME,CAAA,GAAII,CAAA,CAAEN,KAAA,CAAME,CAAA;MACvBK,IAAA,CAAKP,KAAA,CAAMG,CAAA,GAAIG,CAAA,CAAEN,KAAA,CAAMG,CAAA;MACvBI,IAAA,CAAKnB,IAAA,GAAOkB,CAAA,CAAElB,IAAA;MAEd,IAAIoB,IAAA,CAAKC,SAAA,CAAUX,GAAG,MAAMU,IAAA,CAAKC,SAAA,CAAUF,IAAI,GAAG;QAChD,OAAOD,CAAA;MACR;IACF;IAED,KAAKzD,SAAA,CAAU6D,IAAA,CAAKb,QAAQ;IAE5B,OAAOA,QAAA;EACR;EAEDc,gBAAgBd,QAAA,EAAU;IACxB,IAAIA,QAAA,KAAa,QAAW;MAC1B,OAAO,IAAIe,oBAAA,CAAqB;QAC9BZ,KAAA,EAAO,IAAIa,KAAA,CAAM,GAAG,GAAG,CAAC;QACxBC,SAAA,EAAW;QACXf,IAAA,EAAM;QACNgB,IAAA,EAAM;MACd,CAAO;IACF;IAED,MAAMC,aAAA,GAAgBnB,QAAA,CAASoB,YAAA;IAE/B,MAAMC,YAAA,GAAe,IAAIL,KAAA,CAAMG,aAAA,CAAcf,CAAA,GAAI,KAAOe,aAAA,CAAcd,CAAA,GAAI,KAAOc,aAAA,CAAcb,CAAA,GAAI,GAAK;IAExG,IAAIa,aAAA,CAAcf,CAAA,KAAM,KAAKe,aAAA,CAAcd,CAAA,KAAM,KAAKc,aAAA,CAAcb,CAAA,KAAM,GAAG;MAC3Ee,YAAA,CAAajB,CAAA,GAAI;MACjBiB,YAAA,CAAahB,CAAA,GAAI;MACjBgB,YAAA,CAAaf,CAAA,GAAI;IAClB;IAID,MAAML,GAAA,GAAM,IAAIc,oBAAA,CAAqB;MACnCZ,KAAA,EAAOkB,YAAA;MACPnB,IAAA,EAAMF,QAAA,CAASE,IAAA;MACfgB,IAAA,EAAM;MACNI,WAAA,EAAatB,QAAA,CAASuB,YAAA,GAAe,IAAI,OAAO;MAChDC,OAAA,EAAS,IAAMxB,QAAA,CAASuB;IAC9B,CAAK;IAED,MAAME,aAAA,GAAgB,IAAIC,aAAA,CAAe;IAEzC,SAASnB,CAAA,GAAI,GAAGA,CAAA,GAAIP,QAAA,CAAS2B,QAAA,CAASnB,MAAA,EAAQD,CAAA,IAAK;MACjD,MAAMqB,OAAA,GAAU5B,QAAA,CAAS2B,QAAA,CAASpB,CAAC;MAEnC,IAAIqB,OAAA,CAAQC,KAAA,KAAU,MAAM;QAC1B,MAAMpD,GAAA,GAAMgD,aAAA,CAAcrE,IAAA,CAAKwE,OAAA,CAAQC,KAAK;QAE5C,QAAQD,OAAA,CAAQrC,IAAA;UACd,KAAK;YACHU,GAAA,CAAIxB,GAAA,GAAMA,GAAA;YAEV;UAEF,KAAK;YACHwB,GAAA,CAAI6B,OAAA,GAAUrD,GAAA;YAEd;UAEF,KAAK;YACHwB,GAAA,CAAI8B,QAAA,GAAWtD,GAAA;YACfwB,GAAA,CAAIqB,WAAA,GAAc;YAElB;UAEF,KAAK;YACHrB,GAAA,CAAI+B,MAAA,GAASvD,GAAA;YAEb;QACH;MACF;IACF;IAED,OAAOwB,GAAA;EACR;EAEDP,gBAAgBC,IAAA,EAAM;IAGpB,MAAMsC,MAAA,GAAS,IAAIC,QAAA,CAAU;IAC7B,MAAMC,yBAAA,GAA4B,EAAE;IACpC,MAAMC,mBAAA,GAAsB,EAAE;IAC9B,MAAMC,kBAAA,GAAqB,EAAE;IAE7BJ,MAAA,CAAOK,QAAA,CAAS,QAAQ,IAAI3C,IAAA,CAAK4C,MAAA;IACjCN,MAAA,CAAOK,QAAA,CAAS,QAAQ,IAAI3C,IAAA,CAAK6C,MAAA;IACjCP,MAAA,CAAOK,QAAA,CAAS,UAAU,IAAI3C,IAAA,CAAK8C,QAAA;IACnCR,MAAA,CAAOK,QAAA,CAAS,YAAY,IAAI;IAChCL,MAAA,CAAOK,QAAA,CAAS,WAAW,IAAI;IAC/BL,MAAA,CAAO/B,IAAA,GAAO,KAAKxD,GAAA;IAEnB,IAAIgG,OAAA,GAAU/C,IAAA,CAAK+C,OAAA;IACnB,MAAM1F,SAAA,GAAY2C,IAAA,CAAK3C,SAAA;IAEvB,SAASuD,CAAA,GAAI,GAAGA,CAAA,GAAImC,OAAA,CAAQlC,MAAA,EAAQD,CAAA,IAAK;MACvC,MAAMoC,GAAA,GAAMD,OAAA,CAAQnC,CAAC;MACrB,MAAMqC,UAAA,GAAaD,GAAA,CAAIC,UAAA;MAEvB,QAAQD,GAAA,CAAIE,UAAA;QACV,KAAK;UACHT,mBAAA,CAAoBvB,IAAA,CAAK8B,GAAG;UAE5B;QAEF,KAAK;UACHN,kBAAA,CAAmBxB,IAAA,CAAK8B,GAAG;UAE3B;QAEF;UACE,IAAIG,OAAA;UAEJ,IAAIF,UAAA,CAAWG,aAAA,IAAiB,GAAG;YACjC,MAAMC,SAAA,GAAYhG,SAAA,CAAU4F,UAAA,CAAWG,aAAa;YACpD,IAAI/C,QAAA,GAAW,KAAKc,eAAA,CAAgBkC,SAAS;YAC7ChD,QAAA,GAAW,KAAKD,iBAAA,CAAkBC,QAAQ;YAC1C8C,OAAA,GAAU,KAAKG,aAAA,CAAcN,GAAA,EAAK3C,QAAQ;UACtD,OAAiB;YACL,MAAMA,QAAA,GAAW,KAAKc,eAAA,CAAiB;YACvCgC,OAAA,GAAU,KAAKG,aAAA,CAAcN,GAAA,EAAK3C,QAAQ;UAC3C;UAED,IAAI8C,OAAA,KAAY,QAAW;YACzB;UACD;UAED,MAAMI,KAAA,GAAQvD,IAAA,CAAK4C,MAAA,CAAOK,UAAA,CAAWO,UAAU;UAE/CL,OAAA,CAAQM,OAAA,GAAUF,KAAA,GAAQvD,IAAA,CAAK4C,MAAA,CAAOK,UAAA,CAAWO,UAAU,EAAEC,OAAA,GAAU;UAEvE,IAAIR,UAAA,CAAWS,0BAAA,EAA4B;YACzClB,yBAAA,CAA0BtB,IAAA,CAAKiC,OAAO;UAClD,OAAiB;YACLb,MAAA,CAAOqB,GAAA,CAAIR,OAAO;UACnB;UAED;MACH;IACF;IAED,SAASvC,CAAA,GAAI,GAAGA,CAAA,GAAI6B,mBAAA,CAAoB5B,MAAA,EAAQD,CAAA,IAAK;MACnD,MAAMgD,IAAA,GAAOnB,mBAAA,CAAoB7B,CAAC;MAElCmC,OAAA,GAAU,EAAE;MAEZ,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAKX,UAAA,CAAWa,SAAA,CAAUjD,MAAA,EAAQgD,CAAA,IAAK;QACzD,MAAME,KAAA,GAAQH,IAAA,CAAKX,UAAA,CAAWa,SAAA,CAAUD,CAAC;QAEzC,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIxB,yBAAA,CAA0B3B,MAAA,EAAQmD,CAAA,IAAK;UACzD,MAAMC,KAAA,GAAQzB,yBAAA,CAA0BwB,CAAC,EAAErB,QAAA,CAASM,UAAA,CAAWpD,EAAA;UAE/D,IAAIkE,KAAA,KAAUE,KAAA,EAAO;YACnBlB,OAAA,CAAQ7B,IAAA,CAAKsB,yBAAA,CAA0BwB,CAAC,CAAC;UAC1C;QACF;MACF;MAID,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAInB,kBAAA,CAAmB7B,MAAA,EAAQgD,CAAA,IAAK;QAClD,MAAMK,IAAA,GAAOxB,kBAAA,CAAmBmB,CAAC;QAEjC,IAAIK,IAAA,CAAKC,QAAA,CAASC,YAAA,KAAiBR,IAAA,CAAKX,UAAA,CAAWpD,EAAA,EAAI;UACrD,MAAMwE,UAAA,GAAa,IAAI9B,QAAA,CAAU;UACjC,MAAM+B,EAAA,GAAKJ,IAAA,CAAKC,QAAA,CAASI,KAAA,CAAMC,KAAA;UAE/B,MAAMC,MAAA,GAAS,IAAIC,OAAA,CAAS;UAC5BD,MAAA,CAAOvE,GAAA,CACLoE,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,CAAC,GACJA,EAAA,CAAG,EAAE,GACLA,EAAA,CAAG,EAAE,GACLA,EAAA,CAAG,EAAE,GACLA,EAAA,CAAG,EAAE,GACLA,EAAA,CAAG,EAAE,GACLA,EAAA,CAAG,EAAE,CACN;UAEDD,UAAA,CAAWM,YAAA,CAAaF,MAAM;UAE9B,SAAST,CAAA,GAAI,GAAGA,CAAA,GAAIjB,OAAA,CAAQlC,MAAA,EAAQmD,CAAA,IAAK;YACvCK,UAAA,CAAWV,GAAA,CAAIZ,OAAA,CAAQiB,CAAC,EAAEY,KAAA,CAAM,IAAI,CAAC;UACtC;UAEDtC,MAAA,CAAOqB,GAAA,CAAIU,UAAU;QACtB;MACF;IACF;IAED/B,MAAA,CAAOK,QAAA,CAAS,WAAW,IAAI,KAAKtF,SAAA;IACpC,OAAOiF,MAAA;EACR;EAEDgB,cAAcN,GAAA,EAAK1C,GAAA,EAAK;IACtB,MAAMzC,MAAA,GAAS,IAAIgH,oBAAA,CAAsB;IAEzC,MAAM5B,UAAA,GAAaD,GAAA,CAAIC,UAAA;IAEvB,IAAIkB,QAAA,EAAU9D,QAAA,EAAUyE,MAAA,EAAQtE,KAAA;IAEhC,QAAQwC,GAAA,CAAIE,UAAA;MACV,KAAK;MACL,KAAK;QACHiB,QAAA,GAAWtG,MAAA,CAAOsC,KAAA,CAAM6C,GAAA,CAAImB,QAAQ;QAEpC,IAAIA,QAAA,CAASlB,UAAA,CAAW8B,cAAA,CAAe,OAAO,GAAG;UAC/C1E,QAAA,GAAW,IAAI2E,cAAA,CAAe;YAAEC,YAAA,EAAc;YAAMC,eAAA,EAAiB;YAAOC,IAAA,EAAM;UAAA,CAAG;QAC/F,OAAe;UACLL,MAAA,GAAS7B,UAAA,CAAWmC,SAAA;UACpB5E,KAAA,GAAQ,IAAIa,KAAA,CAAMyD,MAAA,CAAOrE,CAAA,GAAI,KAAOqE,MAAA,CAAOpE,CAAA,GAAI,KAAOoE,MAAA,CAAOnE,CAAA,GAAI,GAAK;UACtEN,QAAA,GAAW,IAAI2E,cAAA,CAAe;YAAExE,KAAA;YAAc0E,eAAA,EAAiB;YAAOC,IAAA,EAAM;UAAA,CAAG;QAChF;QAED9E,QAAA,GAAW,KAAKD,iBAAA,CAAkBC,QAAQ;QAE1C,MAAMgF,MAAA,GAAS,IAAIC,MAAA,CAAOnB,QAAA,EAAU9D,QAAQ;QAC5CgF,MAAA,CAAO1C,QAAA,CAAS,YAAY,IAAIM,UAAA;QAChCoC,MAAA,CAAO1C,QAAA,CAAS,YAAY,IAAIK,GAAA,CAAIE,UAAA;QAEpC,IAAID,UAAA,CAAW1C,IAAA,EAAM;UACnB8E,MAAA,CAAO9E,IAAA,GAAO0C,UAAA,CAAW1C,IAAA;QAC1B;QAED,OAAO8E,MAAA;MAET,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,IAAIrC,GAAA,CAAImB,QAAA,KAAa,MAAM;QAE3BA,QAAA,GAAWtG,MAAA,CAAOsC,KAAA,CAAM6C,GAAA,CAAImB,QAAQ;QAEpC,IAAIA,QAAA,CAASlB,UAAA,CAAW8B,cAAA,CAAe,OAAO,GAAG;UAC/CzE,GAAA,CAAI2E,YAAA,GAAe;QACpB;QAED,IAAI3E,GAAA,KAAQ,MAAM;UAChBA,GAAA,GAAM,KAAKa,eAAA,CAAiB;UAC5Bb,GAAA,GAAM,KAAKF,iBAAA,CAAkBE,GAAG;QACjC;QAED,MAAMiF,IAAA,GAAO,IAAIC,IAAA,CAAKrB,QAAA,EAAU7D,GAAG;QACnCiF,IAAA,CAAKE,UAAA,GAAaxC,UAAA,CAAWyC,YAAA;QAC7BH,IAAA,CAAKI,aAAA,GAAgB1C,UAAA,CAAW2C,eAAA;QAChCL,IAAA,CAAK5C,QAAA,CAAS,YAAY,IAAIM,UAAA;QAC9BsC,IAAA,CAAK5C,QAAA,CAAS,YAAY,IAAIK,GAAA,CAAIE,UAAA;QAElC,IAAID,UAAA,CAAW1C,IAAA,EAAM;UACnBgF,IAAA,CAAKhF,IAAA,GAAO0C,UAAA,CAAW1C,IAAA;QACxB;QAED,OAAOgF,IAAA;MAET,KAAK;QACHpB,QAAA,GAAWtG,MAAA,CAAOsC,KAAA,CAAM6C,GAAA,CAAImB,QAAQ;QAEpCW,MAAA,GAAS7B,UAAA,CAAWmC,SAAA;QACpB5E,KAAA,GAAQ,IAAIa,KAAA,CAAMyD,MAAA,CAAOrE,CAAA,GAAI,KAAOqE,MAAA,CAAOpE,CAAA,GAAI,KAAOoE,MAAA,CAAOnE,CAAA,GAAI,GAAK;QAEtEN,QAAA,GAAW,IAAIwF,iBAAA,CAAkB;UAAErF;QAAY,CAAE;QACjDH,QAAA,GAAW,KAAKD,iBAAA,CAAkBC,QAAQ;QAE1C,MAAMyF,KAAA,GAAQ,IAAIC,IAAA,CAAK5B,QAAA,EAAU9D,QAAQ;QACzCyF,KAAA,CAAMnD,QAAA,CAAS,YAAY,IAAIM,UAAA;QAC/B6C,KAAA,CAAMnD,QAAA,CAAS,YAAY,IAAIK,GAAA,CAAIE,UAAA;QAEnC,IAAID,UAAA,CAAW1C,IAAA,EAAM;UACnBuF,KAAA,CAAMvF,IAAA,GAAO0C,UAAA,CAAW1C,IAAA;QACzB;QAED,OAAOuF,KAAA;MAET,KAAK;QACH3B,QAAA,GAAWnB,GAAA,CAAImB,QAAA;QAEf,MAAM6B,GAAA,GAAMC,QAAA,CAASC,aAAA,CAAc,QAAQ,EAAEC,UAAA,CAAW,IAAI;QAC5D,MAAMC,IAAA,GAAO,GAAGjC,QAAA,CAASkC,UAAA,MAAgBlC,QAAA,CAASmC,QAAA;QAClDN,GAAA,CAAII,IAAA,GAAOA,IAAA;QACX,MAAMG,KAAA,GAAQP,GAAA,CAAIQ,WAAA,CAAYrC,QAAA,CAASsC,IAAI,EAAEF,KAAA,GAAQ;QACrD,MAAMG,MAAA,GAASvC,QAAA,CAASkC,UAAA,GAAa;QAErC,MAAM5F,CAAA,GAAIkG,MAAA,CAAOC,gBAAA;QAEjBZ,GAAA,CAAIa,MAAA,CAAON,KAAA,GAAQA,KAAA,GAAQ9F,CAAA;QAC3BuF,GAAA,CAAIa,MAAA,CAAOH,MAAA,GAASA,MAAA,GAASjG,CAAA;QAC7BuF,GAAA,CAAIa,MAAA,CAAOC,KAAA,CAAMP,KAAA,GAAQA,KAAA,GAAQ;QACjCP,GAAA,CAAIa,MAAA,CAAOC,KAAA,CAAMJ,MAAA,GAASA,MAAA,GAAS;QACnCV,GAAA,CAAIe,YAAA,CAAatG,CAAA,EAAG,GAAG,GAAGA,CAAA,EAAG,GAAG,CAAC;QAEjCuF,GAAA,CAAII,IAAA,GAAOA,IAAA;QACXJ,GAAA,CAAIgB,YAAA,GAAe;QACnBhB,GAAA,CAAIiB,SAAA,GAAY;QAChBzG,KAAA,GAAQyC,UAAA,CAAWmC,SAAA;QACnBY,GAAA,CAAIkB,SAAA,GAAY,QAAQ1G,KAAA,CAAMC,CAAA,IAAKD,KAAA,CAAME,CAAA,IAAKF,KAAA,CAAMG,CAAA,IAAKH,KAAA,CAAM2G,CAAA;QAC/DnB,GAAA,CAAIoB,QAAA,CAAS,GAAG,GAAGb,KAAA,EAAOG,MAAM;QAChCV,GAAA,CAAIkB,SAAA,GAAY;QAChBlB,GAAA,CAAIqB,QAAA,CAASlD,QAAA,CAASsC,IAAA,EAAMF,KAAA,GAAQ,GAAGG,MAAA,GAAS,CAAC;QAEjD,MAAMzE,OAAA,GAAU,IAAIqF,aAAA,CAActB,GAAA,CAAIa,MAAM;QAC5C5E,OAAA,CAAQsF,SAAA,GAAYC,YAAA;QACpBvF,OAAA,CAAQwF,KAAA,GAAQC,mBAAA;QAChBzF,OAAA,CAAQ0F,KAAA,GAAQD,mBAAA;QAEhBrH,QAAA,GAAW,IAAIuH,cAAA,CAAe;UAAE9I,GAAA,EAAKmD,OAAA;UAAS4F,SAAA,EAAW;QAAA,CAAO;QAChE,MAAMC,MAAA,GAAS,IAAIC,MAAA,CAAO1H,QAAQ;QAClCyH,MAAA,CAAOE,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAAS8D,KAAA,CAAM,CAAC,GAAG9D,QAAA,CAAS8D,KAAA,CAAM,CAAC,GAAG9D,QAAA,CAAS8D,KAAA,CAAM,CAAC,CAAC;QAC3EH,MAAA,CAAOI,KAAA,CAAMhI,GAAA,CAAIqG,KAAA,GAAQ,IAAIG,MAAA,GAAS,IAAI,CAAG;QAE7CoB,MAAA,CAAOnF,QAAA,CAAS,YAAY,IAAIM,UAAA;QAChC6E,MAAA,CAAOnF,QAAA,CAAS,YAAY,IAAIK,GAAA,CAAIE,UAAA;QAEpC,IAAID,UAAA,CAAW1C,IAAA,EAAM;UACnBuH,MAAA,CAAOvH,IAAA,GAAO0C,UAAA,CAAW1C,IAAA;QAC1B;QAED,OAAOuH,MAAA;MAET,KAAK;QACH3D,QAAA,GAAWnB,GAAA,CAAImB,QAAA;QAEf,IAAIgE,KAAA;QAEJ,IAAIhE,QAAA,CAASiE,kBAAA,EAAoB;UAC/BD,KAAA,GAAQ,IAAIE,gBAAA,CAAkB;UAC9BF,KAAA,CAAM1C,UAAA,GAAaxC,UAAA,CAAWyC,YAAA;UAC9ByC,KAAA,CAAMH,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,CAAC;UACnFH,KAAA,CAAMI,MAAA,CAAOP,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,CAAC;UAC7FL,KAAA,CAAMM,MAAA,CAAOC,UAAA,GAAa;QACpC,WAAmBvE,QAAA,CAASwE,YAAA,EAAc;UAChCR,KAAA,GAAQ,IAAIS,UAAA,CAAY;UACxBT,KAAA,CAAM1C,UAAA,GAAaxC,UAAA,CAAWyC,YAAA;UAC9ByC,KAAA,CAAMH,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,CAAC;UACnFH,KAAA,CAAMM,MAAA,CAAOC,UAAA,GAAa;QACpC,WAAmBvE,QAAA,CAAS0E,kBAAA,EAAoB;UACtCV,KAAA,GAAQ,IAAIW,aAAA,CAAe;UAE3B,MAAMC,MAAA,GAAQC,IAAA,CAAKC,GAAA,CAAI9E,QAAA,CAASoC,KAAA,CAAM,CAAC,CAAC;UACxC,MAAM2C,OAAA,GAASF,IAAA,CAAKC,GAAA,CAAI9E,QAAA,CAAStD,MAAA,CAAO,CAAC,CAAC;UAE1CsH,KAAA,CAAMH,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASmE,QAAA,CAAS,CAAC,IAAIY,OAAA,GAAS,GAAG/E,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,IAAIS,MAAA,GAAQ,CAAC;UAE5GZ,KAAA,CAAMzB,MAAA,GAASwC,OAAA;UACff,KAAA,CAAM5B,KAAA,GAAQwC,MAAA;UAEdZ,KAAA,CAAMgB,MAAA,CAAO,IAAIC,OAAA,CAAQjF,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,CAAC,CAAC;QACvG,WAAmBrE,QAAA,CAASkF,WAAA,EAAa;UAC/BlB,KAAA,GAAQ,IAAImB,SAAA,CAAW;UACvBnB,KAAA,CAAM1C,UAAA,GAAaxC,UAAA,CAAWyC,YAAA;UAC9ByC,KAAA,CAAMH,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,GAAGnE,QAAA,CAASmE,QAAA,CAAS,CAAC,CAAC;UACnFH,KAAA,CAAMI,MAAA,CAAOP,QAAA,CAAS9H,GAAA,CAAIiE,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,GAAGrE,QAAA,CAASqE,SAAA,CAAU,CAAC,CAAC;UAC7FL,KAAA,CAAMoB,KAAA,GAAQpF,QAAA,CAASqF,gBAAA;UACvBrB,KAAA,CAAMM,MAAA,CAAOC,UAAA,GAAa;QACpC,WAAmBvE,QAAA,CAASsF,aAAA,EAAe;UACjC7K,OAAA,CAAQ8K,IAAA,CAAK,2DAA2D;UAExE;QACD;QAED,IAAIvB,KAAA,EAAO;UACTA,KAAA,CAAMwB,SAAA,GAAYxF,QAAA,CAASwF,SAAA;UAC3B7E,MAAA,GAASX,QAAA,CAASyF,OAAA;UAClBpJ,KAAA,GAAQ,IAAIa,KAAA,CAAMyD,MAAA,CAAOrE,CAAA,GAAI,KAAOqE,MAAA,CAAOpE,CAAA,GAAI,KAAOoE,MAAA,CAAOnE,CAAA,GAAI,GAAK;UACtEwH,KAAA,CAAM3H,KAAA,GAAQA,KAAA;UACd2H,KAAA,CAAMxF,QAAA,CAAS,YAAY,IAAIM,UAAA;UAC/BkF,KAAA,CAAMxF,QAAA,CAAS,YAAY,IAAIK,GAAA,CAAIE,UAAA;QACpC;QAED,OAAOiF,KAAA;IACV;EACF;EAED0B,aAAA,EAAe;IACb,IAAI,CAAC,KAAKjN,cAAA,EAAgB;MAExB,MAAMkN,QAAA,GAAW,IAAIhM,UAAA,CAAW,KAAKpB,OAAO;MAC5CoN,QAAA,CAAS/L,OAAA,CAAQ,KAAKpB,WAAW;MACjC,MAAMoN,SAAA,GAAY,IAAIxK,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;QACjDqK,QAAA,CAASrM,IAAA,CAAK,eAAe+B,OAAA,EAAS,QAAWC,MAAM;MAC/D,CAAO;MAGD,MAAMuK,YAAA,GAAe,IAAIlM,UAAA,CAAW,KAAKpB,OAAO;MAChDsN,YAAA,CAAajM,OAAA,CAAQ,KAAKpB,WAAW;MACrCqN,YAAA,CAAahM,eAAA,CAAgB,aAAa;MAC1C,MAAMiM,aAAA,GAAgB,IAAI1K,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;QACrDuK,YAAA,CAAavM,IAAA,CAAK,iBAAiB+B,OAAA,EAAS,QAAWC,MAAM;MACrE,CAAO;MAED,KAAK7C,cAAA,GAAiB2C,OAAA,CAAQ2K,GAAA,CAAI,CAACH,SAAA,EAAWE,aAAa,CAAC,EAAEzL,IAAA,CAAK,CAAC,CAAC2L,UAAA,EAAWC,cAAa,MAAM;QAEjG,KAAKtN,aAAA,CAAcuN,UAAA,GAAaD,cAAA;QAEhC,MAAME,EAAA,GAAKC,cAAA,CAAeC,QAAA,CAAU;QAEpC,MAAMC,IAAA,GAAO,CACX,qBACAN,UAAA,EACA,gBACAG,EAAA,CAAGI,SAAA,CAAUJ,EAAA,CAAGK,OAAA,CAAQ,GAAG,IAAI,GAAGL,EAAA,CAAGM,WAAA,CAAY,GAAG,CAAC,EAC/D,CAAUC,IAAA,CAAK,IAAI;QAEX,KAAK1N,eAAA,GAAkB2N,GAAA,CAAIC,eAAA,CAAgB,IAAIC,IAAA,CAAK,CAACP,IAAI,CAAC,CAAC;MACnE,CAAO;IACF;IAED,OAAO,KAAK7N,cAAA;EACb;EAEDyC,WAAWH,QAAA,EAAU;IACnB,OAAO,KAAK2K,YAAA,GAAerL,IAAA,CAAK,MAAM;MACpC,IAAI,KAAKvB,UAAA,CAAW4D,MAAA,GAAS,KAAK7D,WAAA,EAAa;QAC7C,MAAMiO,OAAA,GAAS,IAAIC,MAAA,CAAO,KAAK/N,eAAe;QAE9C8N,OAAA,CAAOvL,UAAA,GAAa,CAAE;QACtBuL,OAAA,CAAOE,UAAA,GAAa,CAAE;QACtBF,OAAA,CAAOjM,SAAA,GAAY;QAEnBiM,OAAA,CAAOtL,WAAA,CAAY;UACjBC,IAAA,EAAM;UACN9C,aAAA,EAAe,KAAKA;QAC9B,CAAS;QAEDmO,OAAA,CAAOG,SAAA,GAAY,UAAUC,CAAA,EAAG;UAC9B,MAAMvL,OAAA,GAAUuL,CAAA,CAAErL,IAAA;UAElB,QAAQF,OAAA,CAAQF,IAAA;YACd,KAAK;cACHqL,OAAA,CAAOvL,UAAA,CAAWI,OAAA,CAAQD,EAAE,EAAEL,OAAA,CAAQM,OAAO;cAC7C;YAEF,KAAK;cACHmL,OAAA,CAAOvL,UAAA,CAAWI,OAAA,CAAQD,EAAE,EAAEJ,MAAA,CAAOK,OAAO;cAC5C;YAEF;cACElB,OAAA,CAAQ0M,KAAA,CAAM,gDAAgDxL,OAAA,CAAQF,IAAA,GAAO,GAAG;UACnF;QACF;QAED,KAAK3C,UAAA,CAAWiE,IAAA,CAAK+J,OAAM;MACnC,OAAa;QACL,KAAKhO,UAAA,CAAWsO,IAAA,CAAK,UAAUpE,CAAA,EAAGxG,CAAA,EAAG;UACnC,OAAOwG,CAAA,CAAEnI,SAAA,GAAY2B,CAAA,CAAE3B,SAAA,GAAY,KAAK;QAClD,CAAS;MACF;MAED,MAAMD,MAAA,GAAS,KAAK9B,UAAA,CAAW,KAAKA,UAAA,CAAW4D,MAAA,GAAS,CAAC;MAEzD9B,MAAA,CAAOC,SAAA,IAAaE,QAAA;MAEpB,OAAOH,MAAA;IACb,CAAK;EACF;EAEDkB,aAAalB,MAAA,EAAQE,MAAA,EAAQ;IAC3BF,MAAA,CAAOC,SAAA,IAAaD,MAAA,CAAOoM,UAAA,CAAWlM,MAAM;IAC5C,OAAOF,MAAA,CAAOW,UAAA,CAAWT,MAAM;IAC/B,OAAOF,MAAA,CAAOoM,UAAA,CAAWlM,MAAM;EAChC;EAEDuM,QAAA,EAAU;IACR,SAAS5K,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK3D,UAAA,CAAW4D,MAAA,EAAQ,EAAED,CAAA,EAAG;MAC/C,KAAK3D,UAAA,CAAW2D,CAAC,EAAE6K,SAAA,CAAW;IAC/B;IAED,KAAKxO,UAAA,CAAW4D,MAAA,GAAS;IAEzB,OAAO;EACR;AACH;AAIA,SAAS0J,eAAA,EAAiB;EACxB,IAAI3N,cAAA;EACJ,IAAIE,aAAA;EACJ,IAAI4O,KAAA;EAEJN,SAAA,GAAY,SAAAA,CAAUC,CAAA,EAAG;IACvB,MAAMvL,OAAA,GAAUuL,CAAA,CAAErL,IAAA;IAElB,QAAQF,OAAA,CAAQF,IAAA;MACd,KAAK;QACH9C,aAAA,GAAgBgD,OAAA,CAAQhD,aAAA;QACxB,MAAMuN,UAAA,GAAavN,aAAA,CAAcuN,UAAA;QACjC,IAAIsB,WAAA;QACJ/O,cAAA,GAAiB,IAAI2C,OAAA,CAAQ,UAAUC,OAAA,EAAS;UAE9CmM,WAAA,GAAc;YAAEtB,UAAA;YAAYuB,oBAAA,EAAsBpM;UAAS;UAE3DqM,QAAA,CAASF,WAAW;QAC9B,CAAS,EAAEnN,IAAA,CAAK,MAAM;UACZkN,KAAA,GAAQC,WAAA;QAClB,CAAS;QAED;MAEF,KAAK;QACH,MAAMxN,MAAA,GAAS2B,OAAA,CAAQ3B,MAAA;QACvBvB,cAAA,CAAe4B,IAAA,CAAK,MAAM;UACxB,MAAMwB,IAAA,GAAOtB,aAAA,CAAcgN,KAAA,EAAOvN,MAAM;UAExC2N,IAAA,CAAKnM,WAAA,CAAY;YAAEC,IAAA,EAAM;YAAUC,EAAA,EAAIC,OAAA,CAAQD,EAAA;YAAIG;UAAA,CAAM;QACnE,CAAS;QAED;IACH;EACF;EAED,SAAStB,cAAcqN,MAAA,EAAO5N,MAAA,EAAQ;IACpC,MAAM6N,GAAA,GAAM,IAAIC,UAAA,CAAW9N,MAAM;IACjC,MAAM+N,GAAA,GAAMH,MAAA,CAAMI,OAAA,CAAQC,aAAA,CAAcJ,GAAG;IAE3C,MAAMjJ,OAAA,GAAU,EAAE;IAClB,MAAM1F,SAAA,GAAY,EAAE;IACpB,MAAMuF,MAAA,GAAS,EAAE;IACjB,MAAMyJ,KAAA,GAAQ,EAAE;IAChB,MAAMC,UAAA,GAAa,EAAE;IACrB,MAAMzJ,MAAA,GAAS,EAAE;IAIjB,MAAM0J,IAAA,GAAOL,GAAA,CAAInJ,OAAA,CAAS;IAC1B,MAAMyJ,GAAA,GAAMD,IAAA,CAAKE,KAAA;IAEjB,SAAS7L,CAAA,GAAI,GAAGA,CAAA,GAAI4L,GAAA,EAAK5L,CAAA,IAAK;MAC5B,MAAMuC,OAAA,GAAUoJ,IAAA,CAAKjO,GAAA,CAAIsC,CAAC;MAE1B,MAAM0B,MAAA,GAASoK,iBAAA,CAAkBvJ,OAAA,EAAS+I,GAAG;MAE7C/I,OAAA,CAAQwJ,MAAA,CAAQ;MAEhB,IAAIrK,MAAA,EAAQ;QACVS,OAAA,CAAQ7B,IAAA,CAAKoB,MAAM;MACpB;IACF;IAKD,SAAS1B,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAIzJ,mBAAA,GAAsBgK,KAAA,IAAS7L,CAAA,IAAK;MAC1D,MAAMgM,IAAA,GAAOV,GAAA,CAAIzJ,mBAAA,CAAmB,EAAGnE,GAAA,CAAIsC,CAAC;MAC5C,MAAMiM,cAAA,GAAiBC,iBAAA,CAAkBF,IAAI;MAC7CC,cAAA,CAAe/I,SAAA,GAAY8I,IAAA,CAAKG,YAAA,CAAc;MAE9ChK,OAAA,CAAQ7B,IAAA,CAAK;QAAEiD,QAAA,EAAU;QAAMlB,UAAA,EAAY4J,cAAA;QAAgB3J,UAAA,EAAY;MAAA,CAAsB;IAC9F;IAID,MAAM8J,YAAA,GAAe;IAAA;IAEnBjB,MAAA,CAAMkB,WAAA,CAAYC,OAAA,EAClBnB,MAAA,CAAMkB,WAAA,CAAYE,IAAA,EAClBpB,MAAA,CAAMkB,WAAA,CAAYG,YAAA,EAClBrB,MAAA,CAAMkB,WAAA,CAAYI,OAAA,EAClBtB,MAAA,CAAMkB,WAAA,CAAYK,IAAA,CACnB;IAED,MAAMC,eAAA,GAAkB,CACtBxB,MAAA,CAAMkB,WAAA,CAAYO,aAAA,EAClBzB,MAAA,CAAMkB,WAAA,CAAYQ,cAAA,EAClB1B,MAAA,CAAMkB,WAAA,CAAYS,wBAAA,EAClB3B,MAAA,CAAMkB,WAAA,CAAYU,8BAAA,EAClB5B,MAAA,CAAMkB,WAAA,CAAYW,YAAA,EAClB7B,MAAA,CAAMkB,WAAA,CAAYY,YAAA,EAClB9B,MAAA,CAAMkB,WAAA,CAAYa,gBAAA,EAClB/B,MAAA,CAAMkB,WAAA,CAAYc,aAAA,EAClBhC,MAAA,CAAMkB,WAAA,CAAYe,eAAA,EAClBjC,MAAA,CAAMkB,WAAA,CAAYgB,wBAAA,EAClBlC,MAAA,CAAMkB,WAAA,CAAYiB,SAAA,EAClBnC,MAAA,CAAMkB,WAAA,CAAYkB,aAAA,EAClBpC,MAAA,CAAMkB,WAAA,CAAYmB,aAAA,EAClBrC,MAAA,CAAMkB,WAAA,CAAYoB,iBAAA,EAClBtC,MAAA,CAAMkB,WAAA,CAAYqB,sBAAA,EAClBvC,MAAA,CAAMkB,WAAA,CAAYsB,cAAA,EAClBxC,MAAA,CAAMkB,WAAA,CAAYuB,oBAAA,EAClBzC,MAAA,CAAMkB,WAAA,CAAYwB,YAAA,EAClB1C,MAAA,CAAMkB,WAAA,CAAYyB,oBAAA,EAClB3C,MAAA,CAAMkB,WAAA,CAAY0B,gBAAA,CACnB;IAED,SAAS/N,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAI7O,SAAA,GAAYoP,KAAA,IAAS7L,CAAA,IAAK;MAChD,MAAMgO,SAAA,GAAY1C,GAAA,CAAI7O,SAAA,CAAS,EAAGiB,GAAA,CAAIsC,CAAC;MACvC,MAAMiO,YAAA,GAAeD,SAAA,CAAUE,eAAA,CAAiB;MAEhD,IAAIzO,QAAA,GAAWyM,iBAAA,CAAkB8B,SAAS;MAE1C,MAAM5M,QAAA,GAAW,EAAE;MAEnB,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAImJ,YAAA,CAAanM,MAAA,EAAQgD,CAAA,IAAK;QAC5C,MAAMkL,QAAA,GAAWH,SAAA,CAAUI,UAAA,CAAWhC,YAAA,CAAanJ,CAAC,CAAC;QACrD,IAAIkL,QAAA,EAAU;UACZ,IAAIE,WAAA,GAAcjC,YAAA,CAAanJ,CAAC,EAAEpH,WAAA,CAAY8D,IAAA;UAC9C0O,WAAA,GAAcA,WAAA,CAAYvE,SAAA,CAAU,IAAIuE,WAAA,CAAYpO,MAAM;UAC1D,MAAMoB,OAAA,GAAU;YAAErC,IAAA,EAAMqP;UAAa;UAErC,MAAM/M,KAAA,GAAQgK,GAAA,CAAIgD,uBAAA,CAAwBH,QAAA,CAASI,QAAQ;UAE3D,IAAIjN,KAAA,EAAO;YACTD,OAAA,CAAQC,KAAA,GAAQ,2BAA2BA,KAAA;UACvD,OAAiB;YACLtD,OAAA,CAAQ8K,IAAA,CAAK,8BAA8BuF,WAAA,gCAA2C;YACtFhN,OAAA,CAAQC,KAAA,GAAQ;UACjB;UAEDF,QAAA,CAASd,IAAA,CAAKe,OAAO;UAErB8M,QAAA,CAASpC,MAAA,CAAQ;QAClB;MACF;MAEDtM,QAAA,CAAS2B,QAAA,GAAWA,QAAA;MAEpB,IAAI6M,YAAA,CAAaO,SAAA,EAAW;QAC1BxQ,OAAA,CAAQC,GAAA,CAAI,UAAU;QAEtB,SAASgF,CAAA,GAAI,GAAGA,CAAA,GAAI0J,eAAA,CAAgB1M,MAAA,EAAQgD,CAAA,IAAK;UAC/C,MAAMkL,QAAA,GAAWH,SAAA,CAAUI,UAAA,CAAWhC,YAAA,CAAanJ,CAAC,CAAC;UACrD,IAAIkL,QAAA,EAAU;YACZ,MAAM7M,KAAA,GAAQgK,GAAA,CAAIgD,uBAAA,CAAwBH,QAAA,CAASI,QAAQ;YAC3D,IAAIF,WAAA,GAAcjC,YAAA,CAAanJ,CAAC,EAAEpH,WAAA,CAAY8D,IAAA;YAC9C0O,WAAA,GAAcA,WAAA,CAAYvE,SAAA,CAAU,IAAIuE,WAAA,CAAYpO,MAAM;YAC1D,MAAMoB,OAAA,GAAU;cAAErC,IAAA,EAAMqP,WAAA;cAAa/M,KAAA,EAAO,2BAA2BA;YAAO;YAC9EF,QAAA,CAASd,IAAA,CAAKe,OAAO;YAErB8M,QAAA,CAASpC,MAAA,CAAQ;UAClB;QACF;QAED,MAAM0C,oBAAA,GAAuBvC,iBAAA,CAAkB8B,SAAA,CAAUE,eAAA,CAAe,CAAE;QAE1EzO,QAAA,GAAWiP,MAAA,CAAOC,MAAA,CAAOF,oBAAA,EAAsBhP,QAAQ;MACxD;MAEDhD,SAAA,CAAU6D,IAAA,CAAKb,QAAQ;MAEvBuO,SAAA,CAAUjC,MAAA,CAAQ;MAClBkC,YAAA,CAAalC,MAAA,CAAQ;IACtB;IAID,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAItJ,MAAA,GAAS6J,KAAA,IAAS7L,CAAA,IAAK;MAC7C,MAAM4O,MAAA,GAAStD,GAAA,CAAItJ,MAAA,CAAM,EAAGtE,GAAA,CAAIsC,CAAC;MACjC,MAAM2C,KAAA,GAAQuJ,iBAAA,CAAkB0C,MAAM;MAEtC5M,MAAA,CAAO1B,IAAA,CAAKqC,KAAK;MAEjBiM,MAAA,CAAO7C,MAAA,CAAQ;IAChB;IAID,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAIG,KAAA,GAAQI,KAAA,IAAS7L,CAAA,IAAK;MAC5C,MAAM6O,KAAA,GAAQvD,GAAA,CAAIG,KAAA,CAAK,EAAG/N,GAAA,CAAIsC,CAAC;MAC/B,MAAM8O,IAAA,GAAO5C,iBAAA,CAAkB2C,KAAK;MAEpCpD,KAAA,CAAMnL,IAAA,CAAKwO,IAAI;MAEfD,KAAA,CAAM9C,MAAA,CAAQ;IACf;IAID,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAII,UAAA,GAAaG,KAAA,IAAS7L,CAAA,IAAK;MACjD,MAAM+O,UAAA,GAAazD,GAAA,CAAII,UAAA,CAAU,EAAGhO,GAAA,CAAIsC,CAAC;MACzC,MAAMgP,SAAA,GAAY9C,iBAAA,CAAkB6C,UAAU;MAE9CrD,UAAA,CAAWpL,IAAA,CAAK0O,SAAS;MAEzBD,UAAA,CAAWhD,MAAA,CAAQ;IACpB;IAID,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAIsL,GAAA,CAAIrJ,MAAA,GAAS4J,KAAA,IAAS7L,CAAA,IAAK;MAC7C,MAAMiP,MAAA,GAAS3D,GAAA,CAAIrJ,MAAA,CAAM,EAAGvE,GAAA,CAAIsC,CAAC;MACjC,MAAMkP,KAAA,GAAQhD,iBAAA,CAAkB+C,MAAM;MAEtChN,MAAA,CAAO3B,IAAA,CAAK4O,KAAK;MAEjBD,MAAA,CAAOlD,MAAA,CAAQ;IAChB;IAID,MAAM7J,QAAA,GAAWgK,iBAAA,CAAkBZ,GAAA,CAAIpJ,QAAA,CAAQ,CAAE;IA4BjDoJ,GAAA,CAAIS,MAAA,CAAQ;IAEZ,OAAO;MAAE5J,OAAA;MAAS1F,SAAA;MAAWuF,MAAA;MAAQyJ,KAAA;MAAOC,UAAA;MAAYzJ,MAAA;MAAQC;IAAU;EAC3E;EAED,SAAS4J,kBAAkBpK,MAAA,EAAQ4J,GAAA,EAAK;IACtC,MAAM6D,SAAA,GAAYzN,MAAA,CAAO6B,QAAA,CAAU;IACnC,MAAM6L,WAAA,GAAc1N,MAAA,CAAOW,UAAA,CAAY;IACvC,IAAIC,UAAA,GAAa6M,SAAA,CAAU7M,UAAA;IAC3B,IAAIiB,QAAA,EAAUlB,UAAA,EAAY+E,QAAA,EAAUhI,IAAA,EAAMuF,IAAA;IAM1C,QAAQrC,UAAA;MACN,KAAKwI,KAAA,CAAMuE,UAAA,CAAWC,KAAA;QACpB,MAAMC,GAAA,GAAMC,aAAA,CAAcL,SAAA,EAAW,GAAG;QAExC/H,QAAA,GAAW,CAAE;QACb/E,UAAA,GAAa,CAAE;QACfjD,IAAA,GAAO,CAAE;QAETgI,QAAA,CAASqI,QAAA,GAAW;QACpBrI,QAAA,CAASpI,IAAA,GAAO;QAChBoI,QAAA,CAASxD,KAAA,GAAQ,EAAE;QAEnB,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAIsM,GAAA,CAAItP,MAAA,EAAQgD,CAAA,IAAK;UACnCmE,QAAA,CAASxD,KAAA,CAAMtD,IAAA,CAAKiP,GAAA,CAAItM,CAAC,EAAE,CAAC,CAAC;UAC7BmE,QAAA,CAASxD,KAAA,CAAMtD,IAAA,CAAKiP,GAAA,CAAItM,CAAC,EAAE,CAAC,CAAC;UAC7BmE,QAAA,CAASxD,KAAA,CAAMtD,IAAA,CAAKiP,GAAA,CAAItM,CAAC,EAAE,CAAC,CAAC;QAC9B;QAEDZ,UAAA,CAAW+E,QAAA,GAAWA,QAAA;QACtBhI,IAAA,CAAKiD,UAAA,GAAaA,UAAA;QAElBkB,QAAA,GAAW;UAAEnE;QAAM;QAEnB;MAEF,KAAK0L,KAAA,CAAMuE,UAAA,CAAWK,KAAA;QACpB,MAAMC,EAAA,GAAKR,SAAA,CAAUzH,QAAA;QAErBN,QAAA,GAAW,CAAE;QACb,MAAMxH,KAAA,GAAQ,CAAE;QAChByC,UAAA,GAAa,CAAE;QACfjD,IAAA,GAAO,CAAE;QAETgI,QAAA,CAASqI,QAAA,GAAW;QACpBrI,QAAA,CAASpI,IAAA,GAAO;QAChBoI,QAAA,CAASxD,KAAA,GAAQ,CAAC+L,EAAA,CAAG,CAAC,GAAGA,EAAA,CAAG,CAAC,GAAGA,EAAA,CAAG,CAAC,CAAC;QAErC,MAAMzL,MAAA,GAASkL,WAAA,CAAY5K,SAAA,CAAU8G,GAAG;QAExC1L,KAAA,CAAM6P,QAAA,GAAW;QACjB7P,KAAA,CAAMZ,IAAA,GAAO;QACbY,KAAA,CAAMgE,KAAA,GAAQ,CAACM,MAAA,CAAOrE,CAAA,GAAI,KAAOqE,MAAA,CAAOpE,CAAA,GAAI,KAAOoE,MAAA,CAAOnE,CAAA,GAAI,GAAK;QAEnEsC,UAAA,CAAW+E,QAAA,GAAWA,QAAA;QACtB/E,UAAA,CAAWzC,KAAA,GAAQA,KAAA;QACnBR,IAAA,CAAKiD,UAAA,GAAaA,UAAA;QAElBkB,QAAA,GAAW;UAAEnE;QAAM;QAEnB;MAEF,KAAK0L,KAAA,CAAMuE,UAAA,CAAWO,QAAA;MACtB,KAAK9E,KAAA,CAAMuE,UAAA,CAAWzK,IAAA;QACpBrB,QAAA,GAAW4L,SAAA,CAAUU,aAAA,CAAe;QAEpC;MAEF,KAAK/E,KAAA,CAAMuE,UAAA,CAAWS,IAAA;QACpB,MAAMC,KAAA,GAAQZ,SAAA,CAAUY,KAAA,CAAO;QAC/BpL,IAAA,GAAO,IAAImG,KAAA,CAAMlG,IAAA,CAAM;QAEvB,SAASoL,SAAA,GAAY,GAAGA,SAAA,GAAYD,KAAA,CAAMlE,KAAA,EAAOmE,SAAA,IAAa;UAC5D,MAAMC,IAAA,GAAOF,KAAA,CAAMrS,GAAA,CAAIsS,SAAS;UAChC,MAAME,KAAA,GAAQD,IAAA,CAAKE,OAAA,CAAQrF,KAAA,CAAMsF,QAAA,CAASC,GAAG;UAE7C,IAAIH,KAAA,EAAO;YACTvL,IAAA,CAAK2L,MAAA,CAAOJ,KAAK;YACjBA,KAAA,CAAMnE,MAAA,CAAQ;UACf;UAEDkE,IAAA,CAAKlE,MAAA,CAAQ;QACd;QAED,IAAIpH,IAAA,CAAKoL,KAAA,GAAQlE,KAAA,GAAQ,GAAG;UAC1BlH,IAAA,CAAK4L,OAAA,CAAS;UACdhN,QAAA,GAAWoB,IAAA,CAAKkL,aAAA,CAAe;UAC/BE,KAAA,CAAMhE,MAAA,CAAQ;QACf;QAEDpH,IAAA,CAAKoH,MAAA,CAAQ;QAEb;MAEF,KAAKjB,KAAA,CAAMuE,UAAA,CAAWmB,SAAA;QACpB7L,IAAA,GAAOwK,SAAA,CAAUgB,OAAA,CAAQrF,KAAA,CAAMsF,QAAA,CAASC,GAAG;QAE3C,IAAI1L,IAAA,EAAM;UACRpB,QAAA,GAAWoB,IAAA,CAAKkL,aAAA,CAAe;UAC/BlL,IAAA,CAAKoH,MAAA,CAAQ;QACd;QAED;MAEF,KAAKjB,KAAA,CAAMuE,UAAA,CAAWoB,OAAA;QACpBlN,QAAA,GAAW2I,iBAAA,CAAkBiD,SAAS;QAEtC;MAEF,KAAKrE,KAAA,CAAMuE,UAAA,CAAWqB,KAAA;QACpBnN,QAAA,GAAW2I,iBAAA,CAAkBiD,SAAS;QAEtC;MAEF,KAAKrE,KAAA,CAAMuE,UAAA,CAAWsB,iBAAA;QACpBpN,QAAA,GAAW2I,iBAAA,CAAkBiD,SAAS;QACtC5L,QAAA,CAASI,KAAA,GAAQuI,iBAAA,CAAkBiD,SAAA,CAAUxL,KAAK;QAClDJ,QAAA,CAASI,KAAA,CAAMC,KAAA,GAAQuL,SAAA,CAAUxL,KAAA,CAAMiN,YAAA,CAAa,IAAI;QAExD;MAEF,KAAK9F,KAAA,CAAMuE,UAAA,CAAWwB,IAAA;QAEpB1B,SAAA,CAAU2B,SAAA,CAAU,CAAC;QACrBnM,IAAA,GAAOmG,KAAA,CAAMlG,IAAA,CAAKmM,wBAAA,CAAyB5B,SAAS;QACpD,IAAIxK,IAAA,EAAM;UACRpB,QAAA,GAAWoB,IAAA,CAAKkL,aAAA,CAAe;UAC/BlL,IAAA,CAAKoH,MAAA,CAAQ;QACd;QAED;MAQF;QACE/N,OAAA,CAAQ8K,IAAA,CAAK,oCAAoCxG,UAAA,CAAWzG,WAAA,CAAY8D,IAAA,EAAM;QAC9E;IACH;IAED,IAAI4D,QAAA,EAAU;MACZlB,UAAA,GAAa6J,iBAAA,CAAkBkD,WAAW;MAC1C/M,UAAA,CAAWkB,QAAA,GAAW2I,iBAAA,CAAkBiD,SAAS;MAEjD,IAAIC,WAAA,CAAY4B,UAAA,GAAa,GAAG;QAC9B3O,UAAA,CAAW4O,QAAA,GAAW7B,WAAA,CAAY8B,YAAA,CAAc;MACjD;MAED,IAAI9B,WAAA,CAAY+B,eAAA,GAAkB,GAAG;QACnC9O,UAAA,CAAW+O,WAAA,GAAchC,WAAA,CAAYiC,cAAA,CAAgB;MACtD;MAED,IAAIlC,SAAA,CAAUgC,eAAA,GAAkB,GAAG;QACjC9O,UAAA,CAAWkB,QAAA,CAAS6N,WAAA,GAAcjC,SAAA,CAAUkC,cAAA,CAAgB;MAC7D;MAEDhP,UAAA,CAAWmC,SAAA,GAAY4K,WAAA,CAAY5K,SAAA,CAAU8G,GAAG;MAEhDhJ,UAAA,GAAaA,UAAA,CAAWzG,WAAA,CAAY8D,IAAA;MACpC2C,UAAA,GAAaA,UAAA,CAAWwH,SAAA,CAAU,IAAIxH,UAAA,CAAWrC,MAAM;MAEvD,OAAO;QAAEsD,QAAA;QAAUlB,UAAA;QAAYC;MAAY;IACjD,OAAW;MACLtE,OAAA,CAAQ8K,IAAA,CAAK,oBAAoBxG,UAAA,CAAWzG,WAAA,CAAY8D,IAAA,mCAAuC;IAChG;EACF;EAED,SAASuM,kBAAkBxK,MAAA,EAAQ;IACjC,MAAM4P,MAAA,GAAS,CAAE;IAEjB,WAAWC,QAAA,IAAY7P,MAAA,EAAQ;MAC7B,MAAM8P,KAAA,GAAQ9P,MAAA,CAAO6P,QAAQ;MAE7B,IAAI,OAAOC,KAAA,KAAU,YAAY;QAC/B,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,QAAQA,KAAA,CAAMrN,cAAA,CAAe,aAAa,GAAG;UACtFmN,MAAA,CAAOC,QAAQ,IAAI;YAAE5R,IAAA,EAAM6R,KAAA,CAAM3V,WAAA,CAAY8D,IAAA;YAAM6R,KAAA,EAAOA,KAAA,CAAMA;UAAO;QACjF,OAAe;UACLF,MAAA,CAAOC,QAAQ,IAAIC,KAAA;QACpB;MAIF;IACF;IAED,OAAOF,MAAA;EACR;EAED,SAAS9B,cAAciC,KAAA,EAAOC,UAAA,EAAY;IACxC,IAAIC,UAAA,GAAaD,UAAA;IACjB,IAAIE,EAAA,GAAK,EAAE;IACX,MAAMC,EAAA,GAAK,EAAE;IAEb,IAAIJ,KAAA,YAAiB3G,KAAA,CAAMgH,SAAA,EAAW;MACpC,OAAO,CAACL,KAAA,CAAMM,YAAA,EAAcN,KAAA,CAAMO,UAAU;IAC7C;IAED,IAAIP,KAAA,YAAiB3G,KAAA,CAAMmH,aAAA,EAAe;MACxCN,UAAA,GAAaF,KAAA,CAAME,UAAA;MACnB,SAAS3R,CAAA,GAAI,GAAGA,CAAA,GAAI2R,UAAA,EAAY3R,CAAA,IAAK;QACnC4R,EAAA,CAAGtR,IAAA,CAAKmR,KAAA,CAAMpK,KAAA,CAAMrH,CAAC,CAAC;MACvB;MAED,OAAO4R,EAAA;IACR;IAED,IAAIH,KAAA,YAAiB3G,KAAA,CAAMoH,SAAA,EAAW;MACpC,MAAMC,YAAA,GAAeV,KAAA,CAAMU,YAAA;MAE3B,SAASnS,CAAA,GAAI,GAAGA,CAAA,GAAImS,YAAA,EAAcnS,CAAA,IAAK;QACrC,MAAMoS,OAAA,GAAUX,KAAA,CAAMY,YAAA,CAAarS,CAAC;QACpC,MAAMsS,YAAA,GAAe9C,aAAA,CAAc4C,OAAA,EAAST,UAAU;QACtDC,EAAA,GAAKA,EAAA,CAAGW,MAAA,CAAOD,YAAY;QAC3BF,OAAA,CAAQrG,MAAA,CAAQ;MACjB;MAED,OAAO6F,EAAA;IACR;IAED,IAAIH,KAAA,YAAiB3G,KAAA,CAAM0H,QAAA,EAAU;MACnCb,UAAA,GAAavJ,IAAA,CAAKqK,KAAA,CAAMhB,KAAA,CAAMiB,YAAA,GAAe,CAAC;MAC9Cf,UAAA,GAAaA,UAAA,GAAa,IAAI,IAAIA,UAAA;IAEnC;IAED,IAAIF,KAAA,YAAiB3G,KAAA,CAAM6H,UAAA,IAAclB,KAAA,CAAMmB,MAAA,KAAW,GAAG;MAC3D,MAAMC,KAAA,GAAQpB,KAAA,CAAMqB,cAAA,CAAgB;MAEpC,SAAS9S,CAAA,GAAI,GAAGA,CAAA,GAAI6S,KAAA,CAAMhH,KAAA,EAAO7L,CAAA,IAAK;QACpC4R,EAAA,CAAGtR,IAAA,CAAKuS,KAAA,CAAMnV,GAAA,CAAIsC,CAAC,CAAC;MACrB;MAED6S,KAAA,CAAM9G,MAAA,CAAQ;MAEd,OAAO6F,EAAA;IACR;IAED,MAAMmB,MAAA,GAAStB,KAAA,CAAMsB,MAAA;IACrB,MAAMC,SAAA,GAAYrB,UAAA,GAAa;IAE/B,SAAS1O,CAAA,GAAI,GAAGA,CAAA,GAAI0O,UAAA,EAAY1O,CAAA,IAAK;MACnC,MAAMgQ,CAAA,GAAIF,MAAA,CAAO,CAAC,IAAK9P,CAAA,GAAI+P,SAAA,IAAcD,MAAA,CAAO,CAAC,IAAIA,MAAA,CAAO,CAAC;MAE7D,IAAIE,CAAA,KAAMF,MAAA,CAAO,CAAC,KAAKE,CAAA,KAAMF,MAAA,CAAO,CAAC,GAAG;QACtClB,EAAA,CAAGvR,IAAA,CAAK2S,CAAC;QACT;MACD;MAED,MAAMC,GAAA,GAAMzB,KAAA,CAAM0B,SAAA,CAAUF,CAAC;MAC7B,MAAMG,OAAA,GAAU3B,KAAA,CAAM0B,SAAA,CAAUtB,EAAA,CAAGwB,KAAA,CAAM,EAAE,EAAE,CAAC,CAAC;MAK/C,MAAMC,EAAA,GAAKJ,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC,IAAIA,GAAA,CAAI,CAAC;MAC7D,MAAMK,GAAA,GAAMH,OAAA,CAAQ,CAAC,IAAIA,OAAA,CAAQ,CAAC,IAAIA,OAAA,CAAQ,CAAC,IAAIA,OAAA,CAAQ,CAAC,IAAIA,OAAA,CAAQ,CAAC,IAAIA,OAAA,CAAQ,CAAC;MAEtF,MAAMI,WAAA,GAAcpL,IAAA,CAAKqL,IAAA,CAAKH,EAAA,GAAKC,GAAG;MAEtC,IAAI5K,KAAA;MAEJ,IAAI6K,WAAA,KAAgB,GAAG;QACrB7K,KAAA,GAAQP,IAAA,CAAKsL,EAAA,GAAK;MAC1B,OAAa;QACL,MAAMC,KAAA,IAAST,GAAA,CAAIU,CAAA,GAAIR,OAAA,CAAQQ,CAAA,GAAIV,GAAA,CAAIW,CAAA,GAAIT,OAAA,CAAQS,CAAA,GAAIX,GAAA,CAAIY,CAAA,GAAIV,OAAA,CAAQU,CAAA,IAAKN,WAAA;QAC5E7K,KAAA,GAAQP,IAAA,CAAK2L,IAAA,CAAK3L,IAAA,CAAK4L,GAAA,CAAI,IAAI5L,IAAA,CAAK6L,GAAA,CAAI,GAAGN,KAAK,CAAC,CAAC;MACnD;MAED,IAAIhL,KAAA,GAAQ,KAAK;MAEjBkJ,EAAA,CAAGvR,IAAA,CAAK2S,CAAC;IACV;IAEDrB,EAAA,GAAKC,EAAA,CAAG3T,GAAA,CAAK+U,CAAA,IAAMxB,KAAA,CAAMyC,OAAA,CAAQjB,CAAC,CAAC;IACnC,OAAOrB,EAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}