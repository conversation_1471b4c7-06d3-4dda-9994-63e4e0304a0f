{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { CharCodes } from \"./IonText\";\nimport { State, TextWriter } from \"./IonTextWriter\";\nimport { IonTypes } from \"./IonTypes\";\nexport class PrettyTextWriter extends TextWriter {\n  constructor(writeable, indentSize = 2) {\n    super(writeable);\n    this.indentSize = indentSize;\n    this.indentCount = 0;\n  }\n  writeFieldName(fieldName) {\n    if (this.currentContainer.containerType !== IonTypes.STRUCT) {\n      throw new Error(\"Cannot write field name outside of a struct\");\n    }\n    if (this.currentContainer.state !== State.STRUCT_FIELD) {\n      throw new Error(\"Expecting a struct value\");\n    }\n    if (!this.currentContainer.clean) {\n      this.writeable.writeByte(CharCodes.COMMA);\n      this.writePrettyNewLine(0);\n    }\n    this.writePrettyIndent(0);\n    this.writeSymbolToken(fieldName);\n    this.writeable.writeByte(CharCodes.COLON);\n    this.writeable.writeByte(CharCodes.SPACE);\n    this.currentContainer.state = State.VALUE;\n  }\n  writeNull(type) {\n    if (type === undefined || type === null) {\n      type = IonTypes.NULL;\n    }\n    this.handleSeparator();\n    this.writePrettyValue();\n    this.writeAnnotations();\n    this._writeNull(type);\n    if (this.currentContainer.containerType === IonTypes.STRUCT) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n  }\n  stepOut() {\n    const currentContainer = this.containerContext.pop();\n    if (!currentContainer || !currentContainer.containerType) {\n      throw new Error(\"Can't step out when not in a container\");\n    } else if (currentContainer.containerType === IonTypes.STRUCT && currentContainer.state === State.VALUE) {\n      throw new Error(\"Expecting a struct value\");\n    }\n    if (!currentContainer.clean) {\n      this.writePrettyNewLine(0);\n    }\n    this.writePrettyIndent(-1);\n    switch (currentContainer.containerType) {\n      case IonTypes.LIST:\n        this.writeable.writeByte(CharCodes.RIGHT_BRACKET);\n        break;\n      case IonTypes.SEXP:\n        this.writeable.writeByte(CharCodes.RIGHT_PARENTHESIS);\n        break;\n      case IonTypes.STRUCT:\n        this.writeable.writeByte(CharCodes.RIGHT_BRACE);\n        break;\n      default:\n        throw new Error(\"Unexpected container type\");\n    }\n  }\n  _serializeValue(type, value, serialize) {\n    if (this.currentContainer.state === State.STRUCT_FIELD) {\n      throw new Error(\"Expecting a struct field\");\n    }\n    if (value === null) {\n      this.writeNull(type);\n      return;\n    }\n    this.handleSeparator();\n    this.writePrettyValue();\n    this.writeAnnotations();\n    serialize(value);\n    if (this.currentContainer.containerType === IonTypes.STRUCT) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n  }\n  writeContainer(type, openingCharacter) {\n    if (this.currentContainer.containerType === IonTypes.STRUCT && this.currentContainer.state === State.VALUE) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n    this.handleSeparator();\n    this.writePrettyValue();\n    this.writeAnnotations();\n    this.writeable.writeByte(openingCharacter);\n    this.writePrettyNewLine(1);\n    this._stepIn(type);\n  }\n  handleSeparator() {\n    if (this.depth() === 0) {\n      if (this.currentContainer.clean) {\n        this.currentContainer.clean = false;\n      } else {\n        this.writeable.writeByte(CharCodes.LINE_FEED);\n      }\n    } else {\n      if (this.currentContainer.clean) {\n        this.currentContainer.clean = false;\n      } else {\n        switch (this.currentContainer.containerType) {\n          case IonTypes.LIST:\n            this.writeable.writeByte(CharCodes.COMMA);\n            this.writePrettyNewLine(0);\n            break;\n          case IonTypes.SEXP:\n            this.writeable.writeByte(CharCodes.SPACE);\n            this.writePrettyNewLine(0);\n            break;\n          default:\n        }\n      }\n    }\n  }\n  writePrettyValue() {\n    if (this.depth() > 0 && this.currentContainer.containerType && this.currentContainer.containerType !== IonTypes.STRUCT) {\n      this.writePrettyIndent(0);\n    }\n  }\n  writePrettyNewLine(incrementValue) {\n    this.indentCount = this.indentCount + incrementValue;\n    if (this.indentSize && this.indentSize > 0) {\n      this.writeable.writeByte(CharCodes.LINE_FEED);\n    }\n  }\n  writePrettyIndent(incrementValue) {\n    this.indentCount = this.indentCount + incrementValue;\n    if (this.indentSize && this.indentSize > 0) {\n      for (let i = 0; i < this.indentCount * this.indentSize; i++) {\n        this.writeable.writeByte(CharCodes.SPACE);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["CharCodes", "State", "TextWriter", "IonTypes", "PrettyTextWriter", "constructor", "writeable", "indentSize", "indentCount", "writeFieldName", "fieldName", "currentC<PERSON><PERSON>", "containerType", "STRUCT", "Error", "state", "STRUCT_FIELD", "clean", "writeByte", "COMMA", "writePrettyNewLine", "writePrettyIndent", "writeSymbolToken", "COLON", "SPACE", "VALUE", "writeNull", "type", "undefined", "NULL", "handleSeparator", "writePrettyValue", "writeAnnotations", "_writeNull", "stepOut", "containerContext", "pop", "LIST", "RIGHT_BRACKET", "SEXP", "RIGHT_PARENTHESIS", "RIGHT_BRACE", "_serializeValue", "value", "serialize", "writeContainer", "openingCharacter", "_stepIn", "depth", "LINE_FEED", "incrementValue", "i"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonPrettyTextWriter.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { CharCodes } from \"./IonText\";\nimport { State, TextWriter } from \"./IonTextWriter\";\nimport { IonTypes } from \"./IonTypes\";\nexport class PrettyTextWriter extends TextWriter {\n    constructor(writeable, indentSize = 2) {\n        super(writeable);\n        this.indentSize = indentSize;\n        this.indentCount = 0;\n    }\n    writeFieldName(fieldName) {\n        if (this.currentContainer.containerType !== IonTypes.STRUCT) {\n            throw new Error(\"Cannot write field name outside of a struct\");\n        }\n        if (this.currentContainer.state !== State.STRUCT_FIELD) {\n            throw new Error(\"Expecting a struct value\");\n        }\n        if (!this.currentContainer.clean) {\n            this.writeable.writeByte(CharCodes.COMMA);\n            this.writePrettyNewLine(0);\n        }\n        this.writePrettyIndent(0);\n        this.writeSymbolToken(fieldName);\n        this.writeable.writeByte(CharCodes.COLON);\n        this.writeable.writeByte(CharCodes.SPACE);\n        this.currentContainer.state = State.VALUE;\n    }\n    writeNull(type) {\n        if (type === undefined || type === null) {\n            type = IonTypes.NULL;\n        }\n        this.handleSeparator();\n        this.writePrettyValue();\n        this.writeAnnotations();\n        this._writeNull(type);\n        if (this.currentContainer.containerType === IonTypes.STRUCT) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n    }\n    stepOut() {\n        const currentContainer = this.containerContext.pop();\n        if (!currentContainer || !currentContainer.containerType) {\n            throw new Error(\"Can't step out when not in a container\");\n        }\n        else if (currentContainer.containerType === IonTypes.STRUCT &&\n            currentContainer.state === State.VALUE) {\n            throw new Error(\"Expecting a struct value\");\n        }\n        if (!currentContainer.clean) {\n            this.writePrettyNewLine(0);\n        }\n        this.writePrettyIndent(-1);\n        switch (currentContainer.containerType) {\n            case IonTypes.LIST:\n                this.writeable.writeByte(CharCodes.RIGHT_BRACKET);\n                break;\n            case IonTypes.SEXP:\n                this.writeable.writeByte(CharCodes.RIGHT_PARENTHESIS);\n                break;\n            case IonTypes.STRUCT:\n                this.writeable.writeByte(CharCodes.RIGHT_BRACE);\n                break;\n            default:\n                throw new Error(\"Unexpected container type\");\n        }\n    }\n    _serializeValue(type, value, serialize) {\n        if (this.currentContainer.state === State.STRUCT_FIELD) {\n            throw new Error(\"Expecting a struct field\");\n        }\n        if (value === null) {\n            this.writeNull(type);\n            return;\n        }\n        this.handleSeparator();\n        this.writePrettyValue();\n        this.writeAnnotations();\n        serialize(value);\n        if (this.currentContainer.containerType === IonTypes.STRUCT) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n    }\n    writeContainer(type, openingCharacter) {\n        if (this.currentContainer.containerType === IonTypes.STRUCT &&\n            this.currentContainer.state === State.VALUE) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n        this.handleSeparator();\n        this.writePrettyValue();\n        this.writeAnnotations();\n        this.writeable.writeByte(openingCharacter);\n        this.writePrettyNewLine(1);\n        this._stepIn(type);\n    }\n    handleSeparator() {\n        if (this.depth() === 0) {\n            if (this.currentContainer.clean) {\n                this.currentContainer.clean = false;\n            }\n            else {\n                this.writeable.writeByte(CharCodes.LINE_FEED);\n            }\n        }\n        else {\n            if (this.currentContainer.clean) {\n                this.currentContainer.clean = false;\n            }\n            else {\n                switch (this.currentContainer.containerType) {\n                    case IonTypes.LIST:\n                        this.writeable.writeByte(CharCodes.COMMA);\n                        this.writePrettyNewLine(0);\n                        break;\n                    case IonTypes.SEXP:\n                        this.writeable.writeByte(CharCodes.SPACE);\n                        this.writePrettyNewLine(0);\n                        break;\n                    default:\n                }\n            }\n        }\n    }\n    writePrettyValue() {\n        if (this.depth() > 0 &&\n            this.currentContainer.containerType &&\n            this.currentContainer.containerType !== IonTypes.STRUCT) {\n            this.writePrettyIndent(0);\n        }\n    }\n    writePrettyNewLine(incrementValue) {\n        this.indentCount = this.indentCount + incrementValue;\n        if (this.indentSize && this.indentSize > 0) {\n            this.writeable.writeByte(CharCodes.LINE_FEED);\n        }\n    }\n    writePrettyIndent(incrementValue) {\n        this.indentCount = this.indentCount + incrementValue;\n        if (this.indentSize && this.indentSize > 0) {\n            for (let i = 0; i < this.indentCount * this.indentSize; i++) {\n                this.writeable.writeByte(CharCodes.SPACE);\n            }\n        }\n    }\n}\n//# sourceMappingURL=IonPrettyTextWriter.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,WAAW;AACrC,SAASC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AACnD,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,gBAAgB,SAASF,UAAU,CAAC;EAC7CG,WAAWA,CAACC,SAAS,EAAEC,UAAU,GAAG,CAAC,EAAE;IACnC,KAAK,CAACD,SAAS,CAAC;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAG,CAAC;EACxB;EACAC,cAAcA,CAACC,SAAS,EAAE;IACtB,IAAI,IAAI,CAACC,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,EAAE;MACzD,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;IAClE;IACA,IAAI,IAAI,CAACH,gBAAgB,CAACI,KAAK,KAAKd,KAAK,CAACe,YAAY,EAAE;MACpD,MAAM,IAAIF,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAI,CAAC,IAAI,CAACH,gBAAgB,CAACM,KAAK,EAAE;MAC9B,IAAI,CAACX,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACmB,KAAK,CAAC;MACzC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;IACzB,IAAI,CAACC,gBAAgB,CAACZ,SAAS,CAAC;IAChC,IAAI,CAACJ,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACuB,KAAK,CAAC;IACzC,IAAI,CAACjB,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACwB,KAAK,CAAC;IACzC,IAAI,CAACb,gBAAgB,CAACI,KAAK,GAAGd,KAAK,CAACwB,KAAK;EAC7C;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,IAAIA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,IAAI,EAAE;MACrCA,IAAI,GAAGxB,QAAQ,CAAC0B,IAAI;IACxB;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,UAAU,CAACN,IAAI,CAAC;IACrB,IAAI,IAAI,CAAChB,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,EAAE;MACzD,IAAI,CAACF,gBAAgB,CAACI,KAAK,GAAGd,KAAK,CAACe,YAAY;IACpD;EACJ;EACAkB,OAAOA,CAAA,EAAG;IACN,MAAMvB,gBAAgB,GAAG,IAAI,CAACwB,gBAAgB,CAACC,GAAG,CAAC,CAAC;IACpD,IAAI,CAACzB,gBAAgB,IAAI,CAACA,gBAAgB,CAACC,aAAa,EAAE;MACtD,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;IAC7D,CAAC,MACI,IAAIH,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,IACvDF,gBAAgB,CAACI,KAAK,KAAKd,KAAK,CAACwB,KAAK,EAAE;MACxC,MAAM,IAAIX,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAI,CAACH,gBAAgB,CAACM,KAAK,EAAE;MACzB,IAAI,CAACG,kBAAkB,CAAC,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC1B,QAAQV,gBAAgB,CAACC,aAAa;MAClC,KAAKT,QAAQ,CAACkC,IAAI;QACd,IAAI,CAAC/B,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACsC,aAAa,CAAC;QACjD;MACJ,KAAKnC,QAAQ,CAACoC,IAAI;QACd,IAAI,CAACjC,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACwC,iBAAiB,CAAC;QACrD;MACJ,KAAKrC,QAAQ,CAACU,MAAM;QAChB,IAAI,CAACP,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACyC,WAAW,CAAC;QAC/C;MACJ;QACI,MAAM,IAAI3B,KAAK,CAAC,2BAA2B,CAAC;IACpD;EACJ;EACA4B,eAAeA,CAACf,IAAI,EAAEgB,KAAK,EAAEC,SAAS,EAAE;IACpC,IAAI,IAAI,CAACjC,gBAAgB,CAACI,KAAK,KAAKd,KAAK,CAACe,YAAY,EAAE;MACpD,MAAM,IAAIF,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAI6B,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAACjB,SAAS,CAACC,IAAI,CAAC;MACpB;IACJ;IACA,IAAI,CAACG,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvBY,SAAS,CAACD,KAAK,CAAC;IAChB,IAAI,IAAI,CAAChC,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,EAAE;MACzD,IAAI,CAACF,gBAAgB,CAACI,KAAK,GAAGd,KAAK,CAACe,YAAY;IACpD;EACJ;EACA6B,cAAcA,CAAClB,IAAI,EAAEmB,gBAAgB,EAAE;IACnC,IAAI,IAAI,CAACnC,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,IACvD,IAAI,CAACF,gBAAgB,CAACI,KAAK,KAAKd,KAAK,CAACwB,KAAK,EAAE;MAC7C,IAAI,CAACd,gBAAgB,CAACI,KAAK,GAAGd,KAAK,CAACe,YAAY;IACpD;IACA,IAAI,CAACc,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC1B,SAAS,CAACY,SAAS,CAAC4B,gBAAgB,CAAC;IAC1C,IAAI,CAAC1B,kBAAkB,CAAC,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACpB,IAAI,CAAC;EACtB;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACkB,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;MACpB,IAAI,IAAI,CAACrC,gBAAgB,CAACM,KAAK,EAAE;QAC7B,IAAI,CAACN,gBAAgB,CAACM,KAAK,GAAG,KAAK;MACvC,CAAC,MACI;QACD,IAAI,CAACX,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACiD,SAAS,CAAC;MACjD;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACtC,gBAAgB,CAACM,KAAK,EAAE;QAC7B,IAAI,CAACN,gBAAgB,CAACM,KAAK,GAAG,KAAK;MACvC,CAAC,MACI;QACD,QAAQ,IAAI,CAACN,gBAAgB,CAACC,aAAa;UACvC,KAAKT,QAAQ,CAACkC,IAAI;YACd,IAAI,CAAC/B,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACmB,KAAK,CAAC;YACzC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;YAC1B;UACJ,KAAKjB,QAAQ,CAACoC,IAAI;YACd,IAAI,CAACjC,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACwB,KAAK,CAAC;YACzC,IAAI,CAACJ,kBAAkB,CAAC,CAAC,CAAC;YAC1B;UACJ;QACJ;MACJ;IACJ;EACJ;EACAW,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACiB,KAAK,CAAC,CAAC,GAAG,CAAC,IAChB,IAAI,CAACrC,gBAAgB,CAACC,aAAa,IACnC,IAAI,CAACD,gBAAgB,CAACC,aAAa,KAAKT,QAAQ,CAACU,MAAM,EAAE;MACzD,IAAI,CAACQ,iBAAiB,CAAC,CAAC,CAAC;IAC7B;EACJ;EACAD,kBAAkBA,CAAC8B,cAAc,EAAE;IAC/B,IAAI,CAAC1C,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG0C,cAAc;IACpD,IAAI,IAAI,CAAC3C,UAAU,IAAI,IAAI,CAACA,UAAU,GAAG,CAAC,EAAE;MACxC,IAAI,CAACD,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACiD,SAAS,CAAC;IACjD;EACJ;EACA5B,iBAAiBA,CAAC6B,cAAc,EAAE;IAC9B,IAAI,CAAC1C,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG0C,cAAc;IACpD,IAAI,IAAI,CAAC3C,UAAU,IAAI,IAAI,CAACA,UAAU,GAAG,CAAC,EAAE;MACxC,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3C,WAAW,GAAG,IAAI,CAACD,UAAU,EAAE4C,CAAC,EAAE,EAAE;QACzD,IAAI,CAAC7C,SAAS,CAACY,SAAS,CAAClB,SAAS,CAACwB,KAAK,CAAC;MAC7C;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}