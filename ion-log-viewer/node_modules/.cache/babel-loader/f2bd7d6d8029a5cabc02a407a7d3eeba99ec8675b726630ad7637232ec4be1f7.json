{"ast": null, "code": "import { WireframeGeometry } from \"three\";\nimport { LineSegmentsGeometry } from \"./LineSegmentsGeometry.js\";\nclass WireframeGeometry2 extends LineSegmentsGeometry {\n  constructor(geometry) {\n    super();\n    this.isWireframeGeometry2 = true;\n    this.type = \"WireframeGeometry2\";\n    this.fromWireframeGeometry(new WireframeGeometry(geometry));\n  }\n}\nexport { WireframeGeometry2 };", "map": {"version": 3, "names": ["WireframeGeometry2", "LineSegmentsGeometry", "constructor", "geometry", "isWireframeGeometry2", "type", "fromWireframeGeometry", "WireframeGeometry"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/lines/WireframeGeometry2.js"], "sourcesContent": ["import { WireframeGeometry } from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass WireframeGeometry2 extends LineSegmentsGeometry {\n  constructor(geometry) {\n    super()\n\n    this.isWireframeGeometry2 = true\n\n    this.type = 'WireframeGeometry2'\n\n    this.fromWireframeGeometry(new WireframeGeometry(geometry))\n\n    // set colors, maybe\n  }\n}\n\nexport { WireframeGeometry2 }\n"], "mappings": ";;AAGA,MAAMA,kBAAA,SAA2BC,oBAAA,CAAqB;EACpDC,YAAYC,QAAA,EAAU;IACpB,MAAO;IAEP,KAAKC,oBAAA,GAAuB;IAE5B,KAAKC,IAAA,GAAO;IAEZ,KAAKC,qBAAA,CAAsB,IAAIC,iBAAA,CAAkBJ,QAAQ,CAAC;EAG3D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}