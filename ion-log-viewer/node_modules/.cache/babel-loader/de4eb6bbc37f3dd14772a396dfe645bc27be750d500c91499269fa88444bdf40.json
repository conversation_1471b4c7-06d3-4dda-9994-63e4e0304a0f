{"ast": null, "code": "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n    overflow = _getComputedStyle.overflow,\n    overflowX = _getComputedStyle.overflowX,\n    overflowY = _getComputedStyle.overflowY;\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "map": {"version": 3, "names": ["getComputedStyle", "isScrollParent", "element", "_getComputedStyle", "overflow", "overflowX", "overflowY", "test"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js"], "sourcesContent": ["import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC9C;EACA,IAAIC,iBAAiB,GAAGH,gBAAgB,CAACE,OAAO,CAAC;IAC7CE,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;IACrCC,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,OAAO,4BAA4B,CAACC,IAAI,CAACH,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}