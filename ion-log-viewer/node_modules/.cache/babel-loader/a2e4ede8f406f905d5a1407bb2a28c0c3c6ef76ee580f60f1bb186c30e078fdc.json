{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class Import {\n  constructor(parent, symbolTable, length) {\n    this._parent = parent;\n    this._symbolTable = symbolTable;\n    this._offset = this.parent ? this.parent.offset + this.parent.length : 1;\n    this._length = length || this.symbolTable.numberOfSymbols;\n  }\n  get parent() {\n    return this._parent;\n  }\n  get offset() {\n    return this._offset;\n  }\n  get length() {\n    return this._length;\n  }\n  get symbolTable() {\n    return this._symbolTable;\n  }\n  getSymbolText(symbolId) {\n    if (this.parent === undefined) {\n      throw new Error(\"Illegal parent state.\");\n    }\n    if (this.parent !== null) {\n      const parentSymbol = this.parent.getSymbolText(symbolId);\n      if (parentSymbol) {\n        return parentSymbol;\n      }\n    }\n    const index = symbolId - this.offset;\n    if (index >= 0 && index < this.length) {\n      return this.symbolTable.getSymbolText(index);\n    }\n    return undefined;\n  }\n  getSymbolId(symbolText) {\n    let symbolId;\n    if (this.parent !== null) {\n      symbolId = this.parent.getSymbolId(symbolText);\n      if (symbolId) {\n        return symbolId;\n      }\n    }\n    symbolId = this.symbolTable.getSymbolId(symbolText);\n    if (symbolId !== null && symbolId !== undefined && symbolId < this.length) {\n      return symbolId + this.offset;\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["Import", "constructor", "parent", "symbolTable", "length", "_parent", "_symbolTable", "_offset", "offset", "_length", "numberOfSymbols", "getSymbolText", "symbolId", "undefined", "Error", "parentSymbol", "index", "getSymbolId", "symbolText"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonImport.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class Import {\n    constructor(parent, symbolTable, length) {\n        this._parent = parent;\n        this._symbolTable = symbolTable;\n        this._offset = this.parent ? this.parent.offset + this.parent.length : 1;\n        this._length = length || this.symbolTable.numberOfSymbols;\n    }\n    get parent() {\n        return this._parent;\n    }\n    get offset() {\n        return this._offset;\n    }\n    get length() {\n        return this._length;\n    }\n    get symbolTable() {\n        return this._symbolTable;\n    }\n    getSymbolText(symbolId) {\n        if (this.parent === undefined) {\n            throw new Error(\"Illegal parent state.\");\n        }\n        if (this.parent !== null) {\n            const parentSymbol = this.parent.getSymbolText(symbolId);\n            if (parentSymbol) {\n                return parentSymbol;\n            }\n        }\n        const index = symbolId - this.offset;\n        if (index >= 0 && index < this.length) {\n            return this.symbolTable.getSymbolText(index);\n        }\n        return undefined;\n    }\n    getSymbolId(symbolText) {\n        let symbolId;\n        if (this.parent !== null) {\n            symbolId = this.parent.getSymbolId(symbolText);\n            if (symbolId) {\n                return symbolId;\n            }\n        }\n        symbolId = this.symbolTable.getSymbolId(symbolText);\n        if (symbolId !== null && symbolId !== undefined && symbolId < this.length) {\n            return symbolId + this.offset;\n        }\n        return undefined;\n    }\n}\n//# sourceMappingURL=IonImport.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,MAAM,CAAC;EAChBC,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAE;IACrC,IAAI,CAACC,OAAO,GAAGH,MAAM;IACrB,IAAI,CAACI,YAAY,GAAGH,WAAW;IAC/B,IAAI,CAACI,OAAO,GAAG,IAAI,CAACL,MAAM,GAAG,IAAI,CAACA,MAAM,CAACM,MAAM,GAAG,IAAI,CAACN,MAAM,CAACE,MAAM,GAAG,CAAC;IACxE,IAAI,CAACK,OAAO,GAAGL,MAAM,IAAI,IAAI,CAACD,WAAW,CAACO,eAAe;EAC7D;EACA,IAAIR,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACG,OAAO;EACvB;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,OAAO;EACvB;EACA,IAAIH,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACK,OAAO;EACvB;EACA,IAAIN,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACG,YAAY;EAC5B;EACAK,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAI,IAAI,CAACV,MAAM,KAAKW,SAAS,EAAE;MAC3B,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,IAAI,IAAI,CAACZ,MAAM,KAAK,IAAI,EAAE;MACtB,MAAMa,YAAY,GAAG,IAAI,CAACb,MAAM,CAACS,aAAa,CAACC,QAAQ,CAAC;MACxD,IAAIG,YAAY,EAAE;QACd,OAAOA,YAAY;MACvB;IACJ;IACA,MAAMC,KAAK,GAAGJ,QAAQ,GAAG,IAAI,CAACJ,MAAM;IACpC,IAAIQ,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACZ,MAAM,EAAE;MACnC,OAAO,IAAI,CAACD,WAAW,CAACQ,aAAa,CAACK,KAAK,CAAC;IAChD;IACA,OAAOH,SAAS;EACpB;EACAI,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIN,QAAQ;IACZ,IAAI,IAAI,CAACV,MAAM,KAAK,IAAI,EAAE;MACtBU,QAAQ,GAAG,IAAI,CAACV,MAAM,CAACe,WAAW,CAACC,UAAU,CAAC;MAC9C,IAAIN,QAAQ,EAAE;QACV,OAAOA,QAAQ;MACnB;IACJ;IACAA,QAAQ,GAAG,IAAI,CAACT,WAAW,CAACc,WAAW,CAACC,UAAU,CAAC;IACnD,IAAIN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKC,SAAS,IAAID,QAAQ,GAAG,IAAI,CAACR,MAAM,EAAE;MACvE,OAAOQ,QAAQ,GAAG,IAAI,CAACJ,MAAM;IACjC;IACA,OAAOK,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}