{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Loader, RGBAFormat, RGBA_ASTC_4x4_Format, RGBA_BPTC_Format, RGBA_ETC2_EAC_Format, RGBA_PVRTC_4BPPV1_Format, RGBA_S3TC_DXT5_Format, RGB_ETC1_Format, RGB_ETC2_Format, RGB_PVRTC_4BPPV1_Format, RGB_S3TC_DXT1_Format, FileLoader, CompressedTexture, UnsignedByteType, LinearFilter, LinearMipmapLinearFilter } from \"three\";\nconst _taskCache = /* @__PURE__ */new WeakMap();\nconst BasisTextureLoader = /* @__PURE__ */(() => {\n  const _BasisTextureLoader = class extends Loader {\n    constructor(manager) {\n      super(manager);\n      this.transcoderPath = \"\";\n      this.transcoderBinary = null;\n      this.transcoderPending = null;\n      this.workerLimit = 4;\n      this.workerPool = [];\n      this.workerNextTaskID = 1;\n      this.workerSourceURL = \"\";\n      this.workerConfig = null;\n    }\n    setTranscoderPath(path) {\n      this.transcoderPath = path;\n      return this;\n    }\n    setWorkerLimit(workerLimit) {\n      this.workerLimit = workerLimit;\n      return this;\n    }\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has(\"WEBGL_compressed_texture_astc\"),\n        etc1Supported: renderer.extensions.has(\"WEBGL_compressed_texture_etc1\"),\n        etc2Supported: renderer.extensions.has(\"WEBGL_compressed_texture_etc\"),\n        dxtSupported: renderer.extensions.has(\"WEBGL_compressed_texture_s3tc\"),\n        bptcSupported: renderer.extensions.has(\"EXT_texture_compression_bptc\"),\n        pvrtcSupported: renderer.extensions.has(\"WEBGL_compressed_texture_pvrtc\") || renderer.extensions.has(\"WEBKIT_WEBGL_compressed_texture_pvrtc\")\n      };\n      return this;\n    }\n    load(url, onLoad, onProgress, onError) {\n      const loader = new FileLoader(this.manager);\n      loader.setResponseType(\"arraybuffer\");\n      loader.setWithCredentials(this.withCredentials);\n      const texture = new CompressedTexture();\n      loader.load(url, buffer => {\n        if (_taskCache.has(buffer)) {\n          const cachedTask = _taskCache.get(buffer);\n          return cachedTask.promise.then(onLoad).catch(onError);\n        }\n        this._createTexture([buffer]).then(function (_texture) {\n          texture.copy(_texture);\n          texture.needsUpdate = true;\n          if (onLoad) onLoad(texture);\n        }).catch(onError);\n      }, onProgress, onError);\n      return texture;\n    }\n    /** Low-level transcoding API, exposed for use by KTX2Loader. */\n    parseInternalAsync(options) {\n      const {\n        levels\n      } = options;\n      const buffers = /* @__PURE__ */new Set();\n      for (let i = 0; i < levels.length; i++) {\n        buffers.add(levels[i].data.buffer);\n      }\n      return this._createTexture(Array.from(buffers), {\n        ...options,\n        lowLevel: true\n      });\n    }\n    /**\n     * @param {ArrayBuffer[]} buffers\n     * @param {object?} config\n     * @return {Promise<CompressedTexture>}\n     */\n    _createTexture(buffers, config = {}) {\n      let worker;\n      let taskID;\n      const taskConfig = config;\n      let taskCost = 0;\n      for (let i = 0; i < buffers.length; i++) {\n        taskCost += buffers[i].byteLength;\n      }\n      const texturePending = this._allocateWorker(taskCost).then(_worker => {\n        worker = _worker;\n        taskID = this.workerNextTaskID++;\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = {\n            resolve,\n            reject\n          };\n          worker.postMessage({\n            type: \"transcode\",\n            id: taskID,\n            buffers,\n            taskConfig\n          }, buffers);\n        });\n      }).then(message => {\n        const {\n          mipmaps,\n          width,\n          height,\n          format\n        } = message;\n        const texture = new CompressedTexture(mipmaps, width, height, format, UnsignedByteType);\n        texture.minFilter = mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter;\n        texture.magFilter = LinearFilter;\n        texture.generateMipmaps = false;\n        texture.needsUpdate = true;\n        return texture;\n      });\n      texturePending.catch(() => true).then(() => {\n        if (worker && taskID) {\n          worker._taskLoad -= taskCost;\n          delete worker._callbacks[taskID];\n        }\n      });\n      _taskCache.set(buffers[0], {\n        promise: texturePending\n      });\n      return texturePending;\n    }\n    _initTranscoder() {\n      if (!this.transcoderPending) {\n        const jsLoader = new FileLoader(this.manager);\n        jsLoader.setPath(this.transcoderPath);\n        jsLoader.setWithCredentials(this.withCredentials);\n        const jsContent = new Promise((resolve, reject) => {\n          jsLoader.load(\"basis_transcoder.js\", resolve, void 0, reject);\n        });\n        const binaryLoader = new FileLoader(this.manager);\n        binaryLoader.setPath(this.transcoderPath);\n        binaryLoader.setResponseType(\"arraybuffer\");\n        binaryLoader.setWithCredentials(this.withCredentials);\n        const binaryContent = new Promise((resolve, reject) => {\n          binaryLoader.load(\"basis_transcoder.wasm\", resolve, void 0, reject);\n        });\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent2, binaryContent2]) => {\n          const fn = _BasisTextureLoader.BasisWorker.toString();\n          const body = [\"/* constants */\", \"let _EngineFormat = \" + JSON.stringify(_BasisTextureLoader.EngineFormat), \"let _TranscoderFormat = \" + JSON.stringify(_BasisTextureLoader.TranscoderFormat), \"let _BasisFormat = \" + JSON.stringify(_BasisTextureLoader.BasisFormat), \"/* basis_transcoder.js */\", jsContent2, \"/* worker */\", fn.substring(fn.indexOf(\"{\") + 1, fn.lastIndexOf(\"}\"))].join(\"\\n\");\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n          this.transcoderBinary = binaryContent2;\n        });\n      }\n      return this.transcoderPending;\n    }\n    _allocateWorker(taskCost) {\n      return this._initTranscoder().then(() => {\n        if (this.workerPool.length < this.workerLimit) {\n          const worker2 = new Worker(this.workerSourceURL);\n          worker2._callbacks = {};\n          worker2._taskLoad = 0;\n          worker2.postMessage({\n            type: \"init\",\n            config: this.workerConfig,\n            transcoderBinary: this.transcoderBinary\n          });\n          worker2.onmessage = function (e) {\n            const message = e.data;\n            switch (message.type) {\n              case \"transcode\":\n                worker2._callbacks[message.id].resolve(message);\n                break;\n              case \"error\":\n                worker2._callbacks[message.id].reject(message);\n                break;\n              default:\n                console.error('THREE.BasisTextureLoader: Unexpected message, \"' + message.type + '\"');\n            }\n          };\n          this.workerPool.push(worker2);\n        } else {\n          this.workerPool.sort(function (a, b) {\n            return a._taskLoad > b._taskLoad ? -1 : 1;\n          });\n        }\n        const worker = this.workerPool[this.workerPool.length - 1];\n        worker._taskLoad += taskCost;\n        return worker;\n      });\n    }\n    dispose() {\n      for (let i = 0; i < this.workerPool.length; i++) {\n        this.workerPool[i].terminate();\n      }\n      this.workerPool.length = 0;\n      return this;\n    }\n  };\n  let BasisTextureLoader2 = _BasisTextureLoader;\n  /* CONSTANTS */\n  __publicField(BasisTextureLoader2, \"BasisFormat\", {\n    ETC1S: 0,\n    UASTC_4x4: 1\n  });\n  __publicField(BasisTextureLoader2, \"TranscoderFormat\", {\n    ETC1: 0,\n    ETC2: 1,\n    BC1: 2,\n    BC3: 3,\n    BC4: 4,\n    BC5: 5,\n    BC7_M6_OPAQUE_ONLY: 6,\n    BC7_M5: 7,\n    PVRTC1_4_RGB: 8,\n    PVRTC1_4_RGBA: 9,\n    ASTC_4x4: 10,\n    ATC_RGB: 11,\n    ATC_RGBA_INTERPOLATED_ALPHA: 12,\n    RGBA32: 13,\n    RGB565: 14,\n    BGR565: 15,\n    RGBA4444: 16\n  });\n  __publicField(BasisTextureLoader2, \"EngineFormat\", {\n    RGBAFormat,\n    RGBA_ASTC_4x4_Format,\n    RGBA_BPTC_Format,\n    RGBA_ETC2_EAC_Format,\n    RGBA_PVRTC_4BPPV1_Format,\n    RGBA_S3TC_DXT5_Format,\n    RGB_ETC1_Format,\n    RGB_ETC2_Format,\n    RGB_PVRTC_4BPPV1_Format,\n    RGB_S3TC_DXT1_Format\n  });\n  /* WEB WORKER */\n  __publicField(BasisTextureLoader2, \"BasisWorker\", function () {\n    let config;\n    let transcoderPending;\n    let BasisModule;\n    const EngineFormat = _EngineFormat;\n    const TranscoderFormat = _TranscoderFormat;\n    const BasisFormat = _BasisFormat;\n    onmessage = function (e) {\n      const message = e.data;\n      switch (message.type) {\n        case \"init\":\n          config = message.config;\n          init(message.transcoderBinary);\n          break;\n        case \"transcode\":\n          transcoderPending.then(() => {\n            try {\n              const {\n                width,\n                height,\n                hasAlpha,\n                mipmaps,\n                format\n              } = message.taskConfig.lowLevel ? transcodeLowLevel(message.taskConfig) : transcode(message.buffers[0]);\n              const buffers = [];\n              for (let i = 0; i < mipmaps.length; ++i) {\n                buffers.push(mipmaps[i].data.buffer);\n              }\n              self.postMessage({\n                type: \"transcode\",\n                id: message.id,\n                width,\n                height,\n                hasAlpha,\n                mipmaps,\n                format\n              }, buffers);\n            } catch (error) {\n              console.error(error);\n              self.postMessage({\n                type: \"error\",\n                id: message.id,\n                error: error.message\n              });\n            }\n          });\n          break;\n      }\n    };\n    function init(wasmBinary) {\n      transcoderPending = new Promise(resolve => {\n        BasisModule = {\n          wasmBinary,\n          onRuntimeInitialized: resolve\n        };\n        BASIS(BasisModule);\n      }).then(() => {\n        BasisModule.initializeBasis();\n      });\n    }\n    function transcodeLowLevel(taskConfig) {\n      const {\n        basisFormat,\n        width,\n        height,\n        hasAlpha\n      } = taskConfig;\n      const {\n        transcoderFormat,\n        engineFormat\n      } = getTranscoderFormat(basisFormat, width, height, hasAlpha);\n      const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat);\n      assert(BasisModule.isFormatSupported(transcoderFormat), \"THREE.BasisTextureLoader: Unsupported format.\");\n      const mipmaps = [];\n      if (basisFormat === BasisFormat.ETC1S) {\n        const transcoder = new BasisModule.LowLevelETC1SImageTranscoder();\n        const {\n          endpointCount,\n          endpointsData,\n          selectorCount,\n          selectorsData,\n          tablesData\n        } = taskConfig.globalData;\n        try {\n          let ok;\n          ok = transcoder.decodePalettes(endpointCount, endpointsData, selectorCount, selectorsData);\n          assert(ok, \"THREE.BasisTextureLoader: decodePalettes() failed.\");\n          ok = transcoder.decodeTables(tablesData);\n          assert(ok, \"THREE.BasisTextureLoader: decodeTables() failed.\");\n          for (let i = 0; i < taskConfig.levels.length; i++) {\n            const level = taskConfig.levels[i];\n            const imageDesc = taskConfig.globalData.imageDescs[i];\n            const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height);\n            const dst = new Uint8Array(dstByteLength);\n            ok = transcoder.transcodeImage(transcoderFormat, dst, dstByteLength / blockByteLength, level.data, getWidthInBlocks(transcoderFormat, level.width), getHeightInBlocks(transcoderFormat, level.height), level.width, level.height, level.index, imageDesc.rgbSliceByteOffset, imageDesc.rgbSliceByteLength, imageDesc.alphaSliceByteOffset, imageDesc.alphaSliceByteLength, imageDesc.imageFlags, hasAlpha, false, 0, 0);\n            assert(ok, \"THREE.BasisTextureLoader: transcodeImage() failed for level \" + level.index + \".\");\n            mipmaps.push({\n              data: dst,\n              width: level.width,\n              height: level.height\n            });\n          }\n        } finally {\n          transcoder.delete();\n        }\n      } else {\n        for (let i = 0; i < taskConfig.levels.length; i++) {\n          const level = taskConfig.levels[i];\n          const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height);\n          const dst = new Uint8Array(dstByteLength);\n          const ok = BasisModule.transcodeUASTCImage(transcoderFormat, dst, dstByteLength / blockByteLength, level.data, getWidthInBlocks(transcoderFormat, level.width), getHeightInBlocks(transcoderFormat, level.height), level.width, level.height, level.index, 0, level.data.byteLength, 0, hasAlpha, false, 0, 0, -1, -1);\n          assert(ok, \"THREE.BasisTextureLoader: transcodeUASTCImage() failed for level \" + level.index + \".\");\n          mipmaps.push({\n            data: dst,\n            width: level.width,\n            height: level.height\n          });\n        }\n      }\n      return {\n        width,\n        height,\n        hasAlpha,\n        mipmaps,\n        format: engineFormat\n      };\n    }\n    function transcode(buffer) {\n      const basisFile = new BasisModule.BasisFile(new Uint8Array(buffer));\n      const basisFormat = basisFile.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S;\n      const width = basisFile.getImageWidth(0, 0);\n      const height = basisFile.getImageHeight(0, 0);\n      const levels = basisFile.getNumLevels(0);\n      const hasAlpha = basisFile.getHasAlpha();\n      function cleanup() {\n        basisFile.close();\n        basisFile.delete();\n      }\n      const {\n        transcoderFormat,\n        engineFormat\n      } = getTranscoderFormat(basisFormat, width, height, hasAlpha);\n      if (!width || !height || !levels) {\n        cleanup();\n        throw new Error(\"THREE.BasisTextureLoader:\tInvalid texture\");\n      }\n      if (!basisFile.startTranscoding()) {\n        cleanup();\n        throw new Error(\"THREE.BasisTextureLoader: .startTranscoding failed\");\n      }\n      const mipmaps = [];\n      for (let mip = 0; mip < levels; mip++) {\n        const mipWidth = basisFile.getImageWidth(0, mip);\n        const mipHeight = basisFile.getImageHeight(0, mip);\n        const dst = new Uint8Array(basisFile.getImageTranscodedSizeInBytes(0, mip, transcoderFormat));\n        const status = basisFile.transcodeImage(dst, 0, mip, transcoderFormat, 0, hasAlpha);\n        if (!status) {\n          cleanup();\n          throw new Error(\"THREE.BasisTextureLoader: .transcodeImage failed.\");\n        }\n        mipmaps.push({\n          data: dst,\n          width: mipWidth,\n          height: mipHeight\n        });\n      }\n      cleanup();\n      return {\n        width,\n        height,\n        hasAlpha,\n        mipmaps,\n        format: engineFormat\n      };\n    }\n    const FORMAT_OPTIONS = [{\n      if: \"astcSupported\",\n      basisFormat: [BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n      engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n      priorityETC1S: Infinity,\n      priorityUASTC: 1,\n      needsPowerOfTwo: false\n    }, {\n      if: \"bptcSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n      engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n      priorityETC1S: 3,\n      priorityUASTC: 2,\n      needsPowerOfTwo: false\n    }, {\n      if: \"dxtSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n      engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n      priorityETC1S: 4,\n      priorityUASTC: 5,\n      needsPowerOfTwo: false\n    }, {\n      if: \"etc2Supported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n      engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n      priorityETC1S: 1,\n      priorityUASTC: 3,\n      needsPowerOfTwo: false\n    }, {\n      if: \"etc1Supported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC1],\n      engineFormat: [EngineFormat.RGB_ETC1_Format, EngineFormat.RGB_ETC1_Format],\n      priorityETC1S: 2,\n      priorityUASTC: 4,\n      needsPowerOfTwo: false\n    }, {\n      if: \"pvrtcSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n      engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n      priorityETC1S: 5,\n      priorityUASTC: 6,\n      needsPowerOfTwo: true\n    }];\n    const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n      return a.priorityETC1S - b.priorityETC1S;\n    });\n    const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n      return a.priorityUASTC - b.priorityUASTC;\n    });\n    function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n      let transcoderFormat;\n      let engineFormat;\n      const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS;\n      for (let i = 0; i < options.length; i++) {\n        const opt = options[i];\n        if (!config[opt.if]) continue;\n        if (!opt.basisFormat.includes(basisFormat)) continue;\n        if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue;\n        transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0];\n        engineFormat = opt.engineFormat[hasAlpha ? 1 : 0];\n        return {\n          transcoderFormat,\n          engineFormat\n        };\n      }\n      console.warn(\"THREE.BasisTextureLoader: No suitable compressed texture format found. Decoding to RGBA32.\");\n      transcoderFormat = TranscoderFormat.RGBA32;\n      engineFormat = EngineFormat.RGBAFormat;\n      return {\n        transcoderFormat,\n        engineFormat\n      };\n    }\n    function assert(ok, message) {\n      if (!ok) throw new Error(message);\n    }\n    function getWidthInBlocks(transcoderFormat, width) {\n      return Math.ceil(width / BasisModule.getFormatBlockWidth(transcoderFormat));\n    }\n    function getHeightInBlocks(transcoderFormat, height) {\n      return Math.ceil(height / BasisModule.getFormatBlockHeight(transcoderFormat));\n    }\n    function getTranscodedImageByteLength(transcoderFormat, width, height) {\n      const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat);\n      if (BasisModule.formatIsUncompressed(transcoderFormat)) {\n        return width * height * blockByteLength;\n      }\n      if (transcoderFormat === TranscoderFormat.PVRTC1_4_RGB || transcoderFormat === TranscoderFormat.PVRTC1_4_RGBA) {\n        const paddedWidth = width + 3 & ~3;\n        const paddedHeight = height + 3 & ~3;\n        return (Math.max(8, paddedWidth) * Math.max(8, paddedHeight) * 4 + 7) / 8;\n      }\n      return getWidthInBlocks(transcoderFormat, width) * getHeightInBlocks(transcoderFormat, height) * blockByteLength;\n    }\n    function isPowerOfTwo(value) {\n      if (value <= 2) return true;\n      return (value & value - 1) === 0 && value !== 0;\n    }\n  });\n  return BasisTextureLoader2;\n})();\nexport { BasisTextureLoader };", "map": {"version": 3, "names": ["_taskCache", "WeakMap", "BasisTextureLoader", "_BasisTexture<PERSON>oader", "Loader", "constructor", "manager", "transcoderPath", "transcoderBinary", "transcoderPending", "workerLimit", "workerPool", "workerNextTaskID", "workerSourceURL", "workerConfig", "setTranscoderPath", "path", "setWorkerLimit", "detectSupport", "renderer", "astcSupported", "extensions", "has", "etc1Supported", "etc2Supported", "dxtSupported", "bptcSupported", "pvrtcSupported", "load", "url", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "setResponseType", "setWithCredentials", "withCredentials", "texture", "CompressedTexture", "buffer", "cachedTask", "get", "promise", "then", "catch", "_createTexture", "_texture", "copy", "needsUpdate", "parseInternalAsync", "options", "levels", "buffers", "Set", "i", "length", "add", "data", "Array", "from", "lowLevel", "config", "worker", "taskID", "taskConfig", "taskCost", "byteLength", "texturePending", "_allocateWorker", "_worker", "Promise", "resolve", "reject", "_callbacks", "postMessage", "type", "id", "message", "mipmaps", "width", "height", "format", "UnsignedByteType", "minFilter", "LinearFilter", "LinearMipmapLinearFilter", "magFilter", "generateMipmaps", "_taskLoad", "set", "_initTranscoder", "j<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "js<PERSON><PERSON><PERSON>", "binaryLoader", "binaryContent", "all", "jsContent2", "binaryContent2", "fn", "BasisWorker", "toString", "body", "JSON", "stringify", "EngineFormat", "TranscoderFormat", "BasisFormat", "substring", "indexOf", "lastIndexOf", "join", "URL", "createObjectURL", "Blob", "worker2", "Worker", "onmessage", "e", "console", "error", "push", "sort", "a", "b", "dispose", "terminate", "BasisTextureLoader2", "__publicField", "ETC1S", "UASTC_4x4", "ETC1", "ETC2", "BC1", "BC3", "BC4", "BC5", "BC7_M6_OPAQUE_ONLY", "BC7_M5", "PVRTC1_4_RGB", "PVRTC1_4_RGBA", "ASTC_4x4", "ATC_RGB", "ATC_RGBA_INTERPOLATED_ALPHA", "RGBA32", "RGB565", "BGR565", "RGBA4444", "RGBAFormat", "RGBA_ASTC_4x4_Format", "RGBA_BPTC_Format", "RGBA_ETC2_EAC_Format", "RGBA_PVRTC_4BPPV1_Format", "RGBA_S3TC_DXT5_Format", "RGB_ETC1_Format", "RGB_ETC2_Format", "RGB_PVRTC_4BPPV1_Format", "RGB_S3TC_DXT1_Format", "BasisModule", "_EngineFormat", "_TranscoderFormat", "_BasisFormat", "init", "has<PERSON><PERSON><PERSON>", "transcodeLowLevel", "transcode", "self", "wasmBinary", "onRuntimeInitialized", "BASIS", "initializeBasis", "basisFormat", "transcoderFormat", "engineFormat", "getTranscoderFormat", "blockByteLength", "getBytesPerBlockOrPixel", "assert", "isFormatSupported", "transcoder", "LowLevelETC1SImageTranscoder", "endpointCount", "endpointsData", "selectorCount", "selectorsData", "tablesData", "globalData", "ok", "decodePalettes", "decodeTables", "level", "imageDesc", "imageDescs", "dstByteLength", "getTranscodedImageByteLength", "dst", "Uint8Array", "transcodeImage", "getWidthInBlocks", "getHeightInBlocks", "index", "rgbSliceByteOffset", "rgbSliceByteLength", "alphaSliceByteOffset", "alphaSliceByteLength", "imageFlags", "delete", "transcodeUASTCImage", "basisFile", "BasisFile", "isUASTC", "getImageWidth", "getImageHeight", "getNumLevels", "getHasAlpha", "cleanup", "close", "Error", "startTranscoding", "mip", "mip<PERSON><PERSON><PERSON>", "mipHeight", "getImageTranscodedSizeInBytes", "status", "FORMAT_OPTIONS", "if", "priorityETC1S", "Infinity", "priorityUASTC", "needsPowerOfTwo", "ETC1S_OPTIONS", "UASTC_OPTIONS", "opt", "includes", "isPowerOfTwo", "warn", "Math", "ceil", "getFormatBlockWidth", "getFormatBlockHeight", "formatIsUncompressed", "<PERSON><PERSON><PERSON><PERSON>", "paddedHeight", "max", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/BasisTextureLoader.js"], "sourcesContent": ["import {\n  CompressedTexture,\n  FileLoader,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  RGBAFormat,\n  RGBA_ASTC_4x4_Format,\n  RGBA_BPTC_Format,\n  RGBA_ETC2_EAC_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGB_ETC1_Format,\n  RGB_ETC2_Format,\n  RGB_PVRTC_4BPPV1_Format,\n  RGB_S3TC_DXT1_Format,\n  UnsignedByteType,\n} from 'three'\n\n/**\n * Loader for Basis Universal GPU Texture Codec.\n *\n * Basis Universal is a \"supercompressed\" GPU texture and texture video\n * compression system that outputs a highly compressed intermediate file format\n * (.basis) that can be quickly transcoded to a wide variety of GPU texture\n * compression formats.\n *\n * This loader parallelizes the transcoding process across a configurable number\n * of web workers, before transferring the transcoded compressed texture back\n * to the main thread.\n */\n\nconst _taskCache = new WeakMap()\n\nconst BasisTextureLoader = /* @__PURE__ */ (() => {\n  class BasisTextureLoader extends Loader {\n    /* CONSTANTS */\n\n    static BasisFormat = {\n      ETC1S: 0,\n      UASTC_4x4: 1,\n    }\n\n    static TranscoderFormat = {\n      ETC1: 0,\n      ETC2: 1,\n      BC1: 2,\n      BC3: 3,\n      BC4: 4,\n      BC5: 5,\n      BC7_M6_OPAQUE_ONLY: 6,\n      BC7_M5: 7,\n      PVRTC1_4_RGB: 8,\n      PVRTC1_4_RGBA: 9,\n      ASTC_4x4: 10,\n      ATC_RGB: 11,\n      ATC_RGBA_INTERPOLATED_ALPHA: 12,\n      RGBA32: 13,\n      RGB565: 14,\n      BGR565: 15,\n      RGBA4444: 16,\n    }\n\n    static EngineFormat = {\n      RGBAFormat: RGBAFormat,\n      RGBA_ASTC_4x4_Format: RGBA_ASTC_4x4_Format,\n      RGBA_BPTC_Format: RGBA_BPTC_Format,\n      RGBA_ETC2_EAC_Format: RGBA_ETC2_EAC_Format,\n      RGBA_PVRTC_4BPPV1_Format: RGBA_PVRTC_4BPPV1_Format,\n      RGBA_S3TC_DXT5_Format: RGBA_S3TC_DXT5_Format,\n      RGB_ETC1_Format: RGB_ETC1_Format,\n      RGB_ETC2_Format: RGB_ETC2_Format,\n      RGB_PVRTC_4BPPV1_Format: RGB_PVRTC_4BPPV1_Format,\n      RGB_S3TC_DXT1_Format: RGB_S3TC_DXT1_Format,\n    }\n\n    /* WEB WORKER */\n\n    static BasisWorker = function () {\n      let config\n      let transcoderPending\n      let BasisModule\n\n      const EngineFormat = _EngineFormat\n      const TranscoderFormat = _TranscoderFormat\n      const BasisFormat = _BasisFormat\n\n      onmessage = function (e) {\n        const message = e.data\n\n        switch (message.type) {\n          case 'init':\n            config = message.config\n            init(message.transcoderBinary)\n            break\n\n          case 'transcode':\n            transcoderPending.then(() => {\n              try {\n                const { width, height, hasAlpha, mipmaps, format } = message.taskConfig.lowLevel\n                  ? transcodeLowLevel(message.taskConfig)\n                  : transcode(message.buffers[0])\n\n                const buffers = []\n\n                for (let i = 0; i < mipmaps.length; ++i) {\n                  buffers.push(mipmaps[i].data.buffer)\n                }\n\n                self.postMessage(\n                  { type: 'transcode', id: message.id, width, height, hasAlpha, mipmaps, format },\n                  buffers,\n                )\n              } catch (error) {\n                console.error(error)\n\n                self.postMessage({ type: 'error', id: message.id, error: error.message })\n              }\n            })\n            break\n        }\n      }\n\n      function init(wasmBinary) {\n        transcoderPending = new Promise((resolve) => {\n          BasisModule = { wasmBinary, onRuntimeInitialized: resolve }\n          BASIS(BasisModule)\n        }).then(() => {\n          BasisModule.initializeBasis()\n        })\n      }\n\n      function transcodeLowLevel(taskConfig) {\n        const { basisFormat, width, height, hasAlpha } = taskConfig\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat)\n\n        assert(BasisModule.isFormatSupported(transcoderFormat), 'THREE.BasisTextureLoader: Unsupported format.')\n\n        const mipmaps = []\n\n        if (basisFormat === BasisFormat.ETC1S) {\n          const transcoder = new BasisModule.LowLevelETC1SImageTranscoder()\n\n          const { endpointCount, endpointsData, selectorCount, selectorsData, tablesData } = taskConfig.globalData\n\n          try {\n            let ok\n\n            ok = transcoder.decodePalettes(endpointCount, endpointsData, selectorCount, selectorsData)\n\n            assert(ok, 'THREE.BasisTextureLoader: decodePalettes() failed.')\n\n            ok = transcoder.decodeTables(tablesData)\n\n            assert(ok, 'THREE.BasisTextureLoader: decodeTables() failed.')\n\n            for (let i = 0; i < taskConfig.levels.length; i++) {\n              const level = taskConfig.levels[i]\n              const imageDesc = taskConfig.globalData.imageDescs[i]\n\n              const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height)\n              const dst = new Uint8Array(dstByteLength)\n\n              ok = transcoder.transcodeImage(\n                transcoderFormat,\n                dst,\n                dstByteLength / blockByteLength,\n                level.data,\n                getWidthInBlocks(transcoderFormat, level.width),\n                getHeightInBlocks(transcoderFormat, level.height),\n                level.width,\n                level.height,\n                level.index,\n                imageDesc.rgbSliceByteOffset,\n                imageDesc.rgbSliceByteLength,\n                imageDesc.alphaSliceByteOffset,\n                imageDesc.alphaSliceByteLength,\n                imageDesc.imageFlags,\n                hasAlpha,\n                false,\n                0,\n                0,\n              )\n\n              assert(ok, 'THREE.BasisTextureLoader: transcodeImage() failed for level ' + level.index + '.')\n\n              mipmaps.push({ data: dst, width: level.width, height: level.height })\n            }\n          } finally {\n            transcoder.delete()\n          }\n        } else {\n          for (let i = 0; i < taskConfig.levels.length; i++) {\n            const level = taskConfig.levels[i]\n\n            const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height)\n            const dst = new Uint8Array(dstByteLength)\n\n            const ok = BasisModule.transcodeUASTCImage(\n              transcoderFormat,\n              dst,\n              dstByteLength / blockByteLength,\n              level.data,\n              getWidthInBlocks(transcoderFormat, level.width),\n              getHeightInBlocks(transcoderFormat, level.height),\n              level.width,\n              level.height,\n              level.index,\n              0,\n              level.data.byteLength,\n              0,\n              hasAlpha,\n              false,\n              0,\n              0,\n              -1,\n              -1,\n            )\n\n            assert(ok, 'THREE.BasisTextureLoader: transcodeUASTCImage() failed for level ' + level.index + '.')\n\n            mipmaps.push({ data: dst, width: level.width, height: level.height })\n          }\n        }\n\n        return { width, height, hasAlpha, mipmaps, format: engineFormat }\n      }\n\n      function transcode(buffer) {\n        const basisFile = new BasisModule.BasisFile(new Uint8Array(buffer))\n\n        const basisFormat = basisFile.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S\n        const width = basisFile.getImageWidth(0, 0)\n        const height = basisFile.getImageHeight(0, 0)\n        const levels = basisFile.getNumLevels(0)\n        const hasAlpha = basisFile.getHasAlpha()\n\n        function cleanup() {\n          basisFile.close()\n          basisFile.delete()\n        }\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        if (!width || !height || !levels) {\n          cleanup()\n          throw new Error('THREE.BasisTextureLoader:\tInvalid texture')\n        }\n\n        if (!basisFile.startTranscoding()) {\n          cleanup()\n          throw new Error('THREE.BasisTextureLoader: .startTranscoding failed')\n        }\n\n        const mipmaps = []\n\n        for (let mip = 0; mip < levels; mip++) {\n          const mipWidth = basisFile.getImageWidth(0, mip)\n          const mipHeight = basisFile.getImageHeight(0, mip)\n          const dst = new Uint8Array(basisFile.getImageTranscodedSizeInBytes(0, mip, transcoderFormat))\n\n          const status = basisFile.transcodeImage(dst, 0, mip, transcoderFormat, 0, hasAlpha)\n\n          if (!status) {\n            cleanup()\n            throw new Error('THREE.BasisTextureLoader: .transcodeImage failed.')\n          }\n\n          mipmaps.push({ data: dst, width: mipWidth, height: mipHeight })\n        }\n\n        cleanup()\n\n        return { width, height, hasAlpha, mipmaps, format: engineFormat }\n      }\n\n      //\n\n      // Optimal choice of a transcoder target format depends on the Basis format (ETC1S or UASTC),\n      // device capabilities, and texture dimensions. The list below ranks the formats separately\n      // for ETC1S and UASTC.\n      //\n      // In some cases, transcoding UASTC to RGBA32 might be preferred for higher quality (at\n      // significant memory cost) compared to ETC1/2, BC1/3, and PVRTC. The transcoder currently\n      // chooses RGBA32 only as a last resort and does not expose that option to the caller.\n      const FORMAT_OPTIONS = [\n        {\n          if: 'astcSupported',\n          basisFormat: [BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n          engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n          priorityETC1S: Infinity,\n          priorityUASTC: 1,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'bptcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n          engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n          priorityETC1S: 3,\n          priorityUASTC: 2,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'dxtSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n          engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n          priorityETC1S: 4,\n          priorityUASTC: 5,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc2Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n          engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n          priorityETC1S: 1,\n          priorityUASTC: 3,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc1Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC1],\n          engineFormat: [EngineFormat.RGB_ETC1_Format, EngineFormat.RGB_ETC1_Format],\n          priorityETC1S: 2,\n          priorityUASTC: 4,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'pvrtcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n          engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n          priorityETC1S: 5,\n          priorityUASTC: 6,\n          needsPowerOfTwo: true,\n        },\n      ]\n\n      const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityETC1S - b.priorityETC1S\n      })\n      const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityUASTC - b.priorityUASTC\n      })\n\n      function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n        let transcoderFormat\n        let engineFormat\n\n        const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS\n\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i]\n\n          if (!config[opt.if]) continue\n          if (!opt.basisFormat.includes(basisFormat)) continue\n          if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue\n\n          transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0]\n          engineFormat = opt.engineFormat[hasAlpha ? 1 : 0]\n\n          return { transcoderFormat, engineFormat }\n        }\n\n        console.warn('THREE.BasisTextureLoader: No suitable compressed texture format found. Decoding to RGBA32.')\n\n        transcoderFormat = TranscoderFormat.RGBA32\n        engineFormat = EngineFormat.RGBAFormat\n\n        return { transcoderFormat, engineFormat }\n      }\n\n      function assert(ok, message) {\n        if (!ok) throw new Error(message)\n      }\n\n      function getWidthInBlocks(transcoderFormat, width) {\n        return Math.ceil(width / BasisModule.getFormatBlockWidth(transcoderFormat))\n      }\n\n      function getHeightInBlocks(transcoderFormat, height) {\n        return Math.ceil(height / BasisModule.getFormatBlockHeight(transcoderFormat))\n      }\n\n      function getTranscodedImageByteLength(transcoderFormat, width, height) {\n        const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat)\n\n        if (BasisModule.formatIsUncompressed(transcoderFormat)) {\n          return width * height * blockByteLength\n        }\n\n        if (transcoderFormat === TranscoderFormat.PVRTC1_4_RGB || transcoderFormat === TranscoderFormat.PVRTC1_4_RGBA) {\n          // GL requires extra padding for very small textures:\n          // https://www.khronos.org/registry/OpenGL/extensions/IMG/IMG_texture_compression_pvrtc.txt\n          const paddedWidth = (width + 3) & ~3\n          const paddedHeight = (height + 3) & ~3\n\n          return (Math.max(8, paddedWidth) * Math.max(8, paddedHeight) * 4 + 7) / 8\n        }\n\n        return getWidthInBlocks(transcoderFormat, width) * getHeightInBlocks(transcoderFormat, height) * blockByteLength\n      }\n\n      function isPowerOfTwo(value) {\n        if (value <= 2) return true\n\n        return (value & (value - 1)) === 0 && value !== 0\n      }\n    }\n\n    constructor(manager) {\n      super(manager)\n\n      this.transcoderPath = ''\n      this.transcoderBinary = null\n      this.transcoderPending = null\n\n      this.workerLimit = 4\n      this.workerPool = []\n      this.workerNextTaskID = 1\n      this.workerSourceURL = ''\n      this.workerConfig = null\n    }\n\n    setTranscoderPath(path) {\n      this.transcoderPath = path\n\n      return this\n    }\n\n    setWorkerLimit(workerLimit) {\n      this.workerLimit = workerLimit\n\n      return this\n    }\n\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has('WEBGL_compressed_texture_astc'),\n        etc1Supported: renderer.extensions.has('WEBGL_compressed_texture_etc1'),\n        etc2Supported: renderer.extensions.has('WEBGL_compressed_texture_etc'),\n        dxtSupported: renderer.extensions.has('WEBGL_compressed_texture_s3tc'),\n        bptcSupported: renderer.extensions.has('EXT_texture_compression_bptc'),\n        pvrtcSupported:\n          renderer.extensions.has('WEBGL_compressed_texture_pvrtc') ||\n          renderer.extensions.has('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n      }\n\n      return this\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const loader = new FileLoader(this.manager)\n\n      loader.setResponseType('arraybuffer')\n      loader.setWithCredentials(this.withCredentials)\n\n      const texture = new CompressedTexture()\n\n      loader.load(\n        url,\n        (buffer) => {\n          // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n          // again from this thread.\n          if (_taskCache.has(buffer)) {\n            const cachedTask = _taskCache.get(buffer)\n\n            return cachedTask.promise.then(onLoad).catch(onError)\n          }\n\n          this._createTexture([buffer])\n            .then(function (_texture) {\n              texture.copy(_texture)\n              texture.needsUpdate = true\n\n              if (onLoad) onLoad(texture)\n            })\n            .catch(onError)\n        },\n        onProgress,\n        onError,\n      )\n\n      return texture\n    }\n\n    /** Low-level transcoding API, exposed for use by KTX2Loader. */\n    parseInternalAsync(options) {\n      const { levels } = options\n\n      const buffers = new Set()\n\n      for (let i = 0; i < levels.length; i++) {\n        buffers.add(levels[i].data.buffer)\n      }\n\n      return this._createTexture(Array.from(buffers), { ...options, lowLevel: true })\n    }\n\n    /**\n     * @param {ArrayBuffer[]} buffers\n     * @param {object?} config\n     * @return {Promise<CompressedTexture>}\n     */\n    _createTexture(buffers, config = {}) {\n      let worker\n      let taskID\n\n      const taskConfig = config\n      let taskCost = 0\n\n      for (let i = 0; i < buffers.length; i++) {\n        taskCost += buffers[i].byteLength\n      }\n\n      const texturePending = this._allocateWorker(taskCost)\n        .then((_worker) => {\n          worker = _worker\n          taskID = this.workerNextTaskID++\n\n          return new Promise((resolve, reject) => {\n            worker._callbacks[taskID] = { resolve, reject }\n\n            worker.postMessage({ type: 'transcode', id: taskID, buffers: buffers, taskConfig: taskConfig }, buffers)\n          })\n        })\n        .then((message) => {\n          const { mipmaps, width, height, format } = message\n\n          const texture = new CompressedTexture(mipmaps, width, height, format, UnsignedByteType)\n          texture.minFilter = mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter\n          texture.magFilter = LinearFilter\n          texture.generateMipmaps = false\n          texture.needsUpdate = true\n\n          return texture\n        })\n\n      // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n      texturePending\n        .catch(() => true)\n        .then(() => {\n          if (worker && taskID) {\n            worker._taskLoad -= taskCost\n            delete worker._callbacks[taskID]\n          }\n        })\n\n      // Cache the task result.\n      _taskCache.set(buffers[0], { promise: texturePending })\n\n      return texturePending\n    }\n\n    _initTranscoder() {\n      if (!this.transcoderPending) {\n        // Load transcoder wrapper.\n        const jsLoader = new FileLoader(this.manager)\n        jsLoader.setPath(this.transcoderPath)\n        jsLoader.setWithCredentials(this.withCredentials)\n        const jsContent = new Promise((resolve, reject) => {\n          jsLoader.load('basis_transcoder.js', resolve, undefined, reject)\n        })\n\n        // Load transcoder WASM binary.\n        const binaryLoader = new FileLoader(this.manager)\n        binaryLoader.setPath(this.transcoderPath)\n        binaryLoader.setResponseType('arraybuffer')\n        binaryLoader.setWithCredentials(this.withCredentials)\n        const binaryContent = new Promise((resolve, reject) => {\n          binaryLoader.load('basis_transcoder.wasm', resolve, undefined, reject)\n        })\n\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n          const fn = BasisTextureLoader.BasisWorker.toString()\n\n          const body = [\n            '/* constants */',\n            'let _EngineFormat = ' + JSON.stringify(BasisTextureLoader.EngineFormat),\n            'let _TranscoderFormat = ' + JSON.stringify(BasisTextureLoader.TranscoderFormat),\n            'let _BasisFormat = ' + JSON.stringify(BasisTextureLoader.BasisFormat),\n            '/* basis_transcoder.js */',\n            jsContent,\n            '/* worker */',\n            fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n          ].join('\\n')\n\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n          this.transcoderBinary = binaryContent\n        })\n      }\n\n      return this.transcoderPending\n    }\n\n    _allocateWorker(taskCost) {\n      return this._initTranscoder().then(() => {\n        if (this.workerPool.length < this.workerLimit) {\n          const worker = new Worker(this.workerSourceURL)\n\n          worker._callbacks = {}\n          worker._taskLoad = 0\n\n          worker.postMessage({\n            type: 'init',\n            config: this.workerConfig,\n            transcoderBinary: this.transcoderBinary,\n          })\n\n          worker.onmessage = function (e) {\n            const message = e.data\n\n            switch (message.type) {\n              case 'transcode':\n                worker._callbacks[message.id].resolve(message)\n                break\n\n              case 'error':\n                worker._callbacks[message.id].reject(message)\n                break\n\n              default:\n                console.error('THREE.BasisTextureLoader: Unexpected message, \"' + message.type + '\"')\n            }\n          }\n\n          this.workerPool.push(worker)\n        } else {\n          this.workerPool.sort(function (a, b) {\n            return a._taskLoad > b._taskLoad ? -1 : 1\n          })\n        }\n\n        const worker = this.workerPool[this.workerPool.length - 1]\n\n        worker._taskLoad += taskCost\n\n        return worker\n      })\n    }\n\n    dispose() {\n      for (let i = 0; i < this.workerPool.length; i++) {\n        this.workerPool[i].terminate()\n      }\n\n      this.workerPool.length = 0\n\n      return this\n    }\n  }\n\n  return BasisTextureLoader\n})()\n\nexport { BasisTextureLoader }\n"], "mappings": ";;;;;;;;;;;;AAgCA,MAAMA,UAAA,GAAa,mBAAIC,OAAA,CAAS;AAE3B,MAACC,kBAAA,GAAsC,sBAAM;EAChD,MAAMC,mBAAA,GAAN,cAAiCC,MAAA,CAAO;IA8XtCC,YAAYC,OAAA,EAAS;MACnB,MAAMA,OAAO;MAEb,KAAKC,cAAA,GAAiB;MACtB,KAAKC,gBAAA,GAAmB;MACxB,KAAKC,iBAAA,GAAoB;MAEzB,KAAKC,WAAA,GAAc;MACnB,KAAKC,UAAA,GAAa,EAAE;MACpB,KAAKC,gBAAA,GAAmB;MACxB,KAAKC,eAAA,GAAkB;MACvB,KAAKC,YAAA,GAAe;IACrB;IAEDC,kBAAkBC,IAAA,EAAM;MACtB,KAAKT,cAAA,GAAiBS,IAAA;MAEtB,OAAO;IACR;IAEDC,eAAeP,WAAA,EAAa;MAC1B,KAAKA,WAAA,GAAcA,WAAA;MAEnB,OAAO;IACR;IAEDQ,cAAcC,QAAA,EAAU;MACtB,KAAKL,YAAA,GAAe;QAClBM,aAAA,EAAeD,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACtEC,aAAA,EAAeJ,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACtEE,aAAA,EAAeL,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,8BAA8B;QACrEG,YAAA,EAAcN,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACrEI,aAAA,EAAeP,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,8BAA8B;QACrEK,cAAA,EACER,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,gCAAgC,KACxDH,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,uCAAuC;MAClE;MAED,OAAO;IACR;IAEDM,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;MACrC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAK5B,OAAO;MAE1C2B,MAAA,CAAOE,eAAA,CAAgB,aAAa;MACpCF,MAAA,CAAOG,kBAAA,CAAmB,KAAKC,eAAe;MAE9C,MAAMC,OAAA,GAAU,IAAIC,iBAAA,CAAmB;MAEvCN,MAAA,CAAOL,IAAA,CACLC,GAAA,EACCW,MAAA,IAAW;QAGV,IAAIxC,UAAA,CAAWsB,GAAA,CAAIkB,MAAM,GAAG;UAC1B,MAAMC,UAAA,GAAazC,UAAA,CAAW0C,GAAA,CAAIF,MAAM;UAExC,OAAOC,UAAA,CAAWE,OAAA,CAAQC,IAAA,CAAKd,MAAM,EAAEe,KAAA,CAAMb,OAAO;QACrD;QAED,KAAKc,cAAA,CAAe,CAACN,MAAM,CAAC,EACzBI,IAAA,CAAK,UAAUG,QAAA,EAAU;UACxBT,OAAA,CAAQU,IAAA,CAAKD,QAAQ;UACrBT,OAAA,CAAQW,WAAA,GAAc;UAEtB,IAAInB,MAAA,EAAQA,MAAA,CAAOQ,OAAO;QACxC,CAAa,EACAO,KAAA,CAAMb,OAAO;MACjB,GACDD,UAAA,EACAC,OACD;MAED,OAAOM,OAAA;IACR;IAAA;IAGDY,mBAAmBC,OAAA,EAAS;MAC1B,MAAM;QAAEC;MAAM,IAAKD,OAAA;MAEnB,MAAME,OAAA,GAAU,mBAAIC,GAAA,CAAK;MAEzB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,MAAA,CAAOI,MAAA,EAAQD,CAAA,IAAK;QACtCF,OAAA,CAAQI,GAAA,CAAIL,MAAA,CAAOG,CAAC,EAAEG,IAAA,CAAKlB,MAAM;MAClC;MAED,OAAO,KAAKM,cAAA,CAAea,KAAA,CAAMC,IAAA,CAAKP,OAAO,GAAG;QAAE,GAAGF,OAAA;QAASU,QAAA,EAAU;MAAA,CAAM;IAC/E;IAAA;AAAA;AAAA;AAAA;AAAA;IAODf,eAAeO,OAAA,EAASS,MAAA,GAAS,IAAI;MACnC,IAAIC,MAAA;MACJ,IAAIC,MAAA;MAEJ,MAAMC,UAAA,GAAaH,MAAA;MACnB,IAAII,QAAA,GAAW;MAEf,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAIF,OAAA,CAAQG,MAAA,EAAQD,CAAA,IAAK;QACvCW,QAAA,IAAYb,OAAA,CAAQE,CAAC,EAAEY,UAAA;MACxB;MAED,MAAMC,cAAA,GAAiB,KAAKC,eAAA,CAAgBH,QAAQ,EACjDtB,IAAA,CAAM0B,OAAA,IAAY;QACjBP,MAAA,GAASO,OAAA;QACTN,MAAA,GAAS,KAAKpD,gBAAA;QAEd,OAAO,IAAI2D,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;UACtCV,MAAA,CAAOW,UAAA,CAAWV,MAAM,IAAI;YAAEQ,OAAA;YAASC;UAAQ;UAE/CV,MAAA,CAAOY,WAAA,CAAY;YAAEC,IAAA,EAAM;YAAaC,EAAA,EAAIb,MAAA;YAAQX,OAAA;YAAkBY;UAAsB,GAAIZ,OAAO;QACnH,CAAW;MACX,CAAS,EACAT,IAAA,CAAMkC,OAAA,IAAY;QACjB,MAAM;UAAEC,OAAA;UAASC,KAAA;UAAOC,MAAA;UAAQC;QAAQ,IAAGJ,OAAA;QAE3C,MAAMxC,OAAA,GAAU,IAAIC,iBAAA,CAAkBwC,OAAA,EAASC,KAAA,EAAOC,MAAA,EAAQC,MAAA,EAAQC,gBAAgB;QACtF7C,OAAA,CAAQ8C,SAAA,GAAYL,OAAA,CAAQvB,MAAA,KAAW,IAAI6B,YAAA,GAAeC,wBAAA;QAC1DhD,OAAA,CAAQiD,SAAA,GAAYF,YAAA;QACpB/C,OAAA,CAAQkD,eAAA,GAAkB;QAC1BlD,OAAA,CAAQW,WAAA,GAAc;QAEtB,OAAOX,OAAA;MACjB,CAAS;MAGH8B,cAAA,CACGvB,KAAA,CAAM,MAAM,IAAI,EAChBD,IAAA,CAAK,MAAM;QACV,IAAImB,MAAA,IAAUC,MAAA,EAAQ;UACpBD,MAAA,CAAO0B,SAAA,IAAavB,QAAA;UACpB,OAAOH,MAAA,CAAOW,UAAA,CAAWV,MAAM;QAChC;MACX,CAAS;MAGHhE,UAAA,CAAW0F,GAAA,CAAIrC,OAAA,CAAQ,CAAC,GAAG;QAAEV,OAAA,EAASyB;MAAA,CAAgB;MAEtD,OAAOA,cAAA;IACR;IAEDuB,gBAAA,EAAkB;MAChB,IAAI,CAAC,KAAKlF,iBAAA,EAAmB;QAE3B,MAAMmF,QAAA,GAAW,IAAI1D,UAAA,CAAW,KAAK5B,OAAO;QAC5CsF,QAAA,CAASC,OAAA,CAAQ,KAAKtF,cAAc;QACpCqF,QAAA,CAASxD,kBAAA,CAAmB,KAAKC,eAAe;QAChD,MAAMyD,SAAA,GAAY,IAAIvB,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;UACjDmB,QAAA,CAAShE,IAAA,CAAK,uBAAuB4C,OAAA,EAAS,QAAWC,MAAM;QACzE,CAAS;QAGD,MAAMsB,YAAA,GAAe,IAAI7D,UAAA,CAAW,KAAK5B,OAAO;QAChDyF,YAAA,CAAaF,OAAA,CAAQ,KAAKtF,cAAc;QACxCwF,YAAA,CAAa5D,eAAA,CAAgB,aAAa;QAC1C4D,YAAA,CAAa3D,kBAAA,CAAmB,KAAKC,eAAe;QACpD,MAAM2D,aAAA,GAAgB,IAAIzB,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;UACrDsB,YAAA,CAAanE,IAAA,CAAK,yBAAyB4C,OAAA,EAAS,QAAWC,MAAM;QAC/E,CAAS;QAED,KAAKhE,iBAAA,GAAoB8D,OAAA,CAAQ0B,GAAA,CAAI,CAACH,SAAA,EAAWE,aAAa,CAAC,EAAEpD,IAAA,CAAK,CAAC,CAACsD,UAAA,EAAWC,cAAa,MAAM;UACpG,MAAMC,EAAA,GAAKjG,mBAAA,CAAmBkG,WAAA,CAAYC,QAAA,CAAU;UAEpD,MAAMC,IAAA,GAAO,CACX,mBACA,yBAAyBC,IAAA,CAAKC,SAAA,CAAUtG,mBAAA,CAAmBuG,YAAY,GACvE,6BAA6BF,IAAA,CAAKC,SAAA,CAAUtG,mBAAA,CAAmBwG,gBAAgB,GAC/E,wBAAwBH,IAAA,CAAKC,SAAA,CAAUtG,mBAAA,CAAmByG,WAAW,GACrE,6BACAV,UAAA,EACA,gBACAE,EAAA,CAAGS,SAAA,CAAUT,EAAA,CAAGU,OAAA,CAAQ,GAAG,IAAI,GAAGV,EAAA,CAAGW,WAAA,CAAY,GAAG,CAAC,EACjE,CAAYC,IAAA,CAAK,IAAI;UAEX,KAAKnG,eAAA,GAAkBoG,GAAA,CAAIC,eAAA,CAAgB,IAAIC,IAAA,CAAK,CAACZ,IAAI,CAAC,CAAC;UAC3D,KAAK/F,gBAAA,GAAmB2F,cAAA;QAClC,CAAS;MACF;MAED,OAAO,KAAK1F,iBAAA;IACb;IAED4D,gBAAgBH,QAAA,EAAU;MACxB,OAAO,KAAKyB,eAAA,GAAkB/C,IAAA,CAAK,MAAM;QACvC,IAAI,KAAKjC,UAAA,CAAW6C,MAAA,GAAS,KAAK9C,WAAA,EAAa;UAC7C,MAAM0G,OAAA,GAAS,IAAIC,MAAA,CAAO,KAAKxG,eAAe;UAE9CuG,OAAA,CAAO1C,UAAA,GAAa,CAAE;UACtB0C,OAAA,CAAO3B,SAAA,GAAY;UAEnB2B,OAAA,CAAOzC,WAAA,CAAY;YACjBC,IAAA,EAAM;YACNd,MAAA,EAAQ,KAAKhD,YAAA;YACbN,gBAAA,EAAkB,KAAKA;UACnC,CAAW;UAED4G,OAAA,CAAOE,SAAA,GAAY,UAAUC,CAAA,EAAG;YAC9B,MAAMzC,OAAA,GAAUyC,CAAA,CAAE7D,IAAA;YAElB,QAAQoB,OAAA,CAAQF,IAAA;cACd,KAAK;gBACHwC,OAAA,CAAO1C,UAAA,CAAWI,OAAA,CAAQD,EAAE,EAAEL,OAAA,CAAQM,OAAO;gBAC7C;cAEF,KAAK;gBACHsC,OAAA,CAAO1C,UAAA,CAAWI,OAAA,CAAQD,EAAE,EAAEJ,MAAA,CAAOK,OAAO;gBAC5C;cAEF;gBACE0C,OAAA,CAAQC,KAAA,CAAM,oDAAoD3C,OAAA,CAAQF,IAAA,GAAO,GAAG;YACvF;UACF;UAED,KAAKjE,UAAA,CAAW+G,IAAA,CAAKN,OAAM;QACrC,OAAe;UACL,KAAKzG,UAAA,CAAWgH,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;YACnC,OAAOD,CAAA,CAAEnC,SAAA,GAAYoC,CAAA,CAAEpC,SAAA,GAAY,KAAK;UACpD,CAAW;QACF;QAED,MAAM1B,MAAA,GAAS,KAAKpD,UAAA,CAAW,KAAKA,UAAA,CAAW6C,MAAA,GAAS,CAAC;QAEzDO,MAAA,CAAO0B,SAAA,IAAavB,QAAA;QAEpB,OAAOH,MAAA;MACf,CAAO;IACF;IAED+D,QAAA,EAAU;MACR,SAASvE,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5C,UAAA,CAAW6C,MAAA,EAAQD,CAAA,IAAK;QAC/C,KAAK5C,UAAA,CAAW4C,CAAC,EAAEwE,SAAA,CAAW;MAC/B;MAED,KAAKpH,UAAA,CAAW6C,MAAA,GAAS;MAEzB,OAAO;IACR;EACF;EA9mBD,IAAMwE,mBAAA,GAAN7H,mBAAA;EAGE;EAAA8H,aAAA,CAHID,mBAAA,EAGG,eAAc;IACnBE,KAAA,EAAO;IACPC,SAAA,EAAW;EACZ;EAEDF,aAAA,CARID,mBAAA,EAQG,oBAAmB;IACxBI,IAAA,EAAM;IACNC,IAAA,EAAM;IACNC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,kBAAA,EAAoB;IACpBC,MAAA,EAAQ;IACRC,YAAA,EAAc;IACdC,aAAA,EAAe;IACfC,QAAA,EAAU;IACVC,OAAA,EAAS;IACTC,2BAAA,EAA6B;IAC7BC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,QAAA,EAAU;EACX;EAEDnB,aAAA,CA5BID,mBAAA,EA4BG,gBAAe;IACpBqB,UAAA;IACAC,oBAAA;IACAC,gBAAA;IACAC,oBAAA;IACAC,wBAAA;IACAC,qBAAA;IACAC,eAAA;IACAC,eAAA;IACAC,uBAAA;IACAC;EACD;EAID;EAAA7B,aAAA,CA3CID,mBAAA,EA2CG,eAAc,YAAY;IAC/B,IAAIlE,MAAA;IACJ,IAAIrD,iBAAA;IACJ,IAAIsJ,WAAA;IAEJ,MAAMrD,YAAA,GAAesD,aAAA;IACrB,MAAMrD,gBAAA,GAAmBsD,iBAAA;IACzB,MAAMrD,WAAA,GAAcsD,YAAA;IAEpB5C,SAAA,GAAY,SAAAA,CAAUC,CAAA,EAAG;MACvB,MAAMzC,OAAA,GAAUyC,CAAA,CAAE7D,IAAA;MAElB,QAAQoB,OAAA,CAAQF,IAAA;QACd,KAAK;UACHd,MAAA,GAASgB,OAAA,CAAQhB,MAAA;UACjBqG,IAAA,CAAKrF,OAAA,CAAQtE,gBAAgB;UAC7B;QAEF,KAAK;UACHC,iBAAA,CAAkBmC,IAAA,CAAK,MAAM;YAC3B,IAAI;cACF,MAAM;gBAAEoC,KAAA;gBAAOC,MAAA;gBAAQmF,QAAA;gBAAUrF,OAAA;gBAASG;cAAQ,IAAGJ,OAAA,CAAQb,UAAA,CAAWJ,QAAA,GACpEwG,iBAAA,CAAkBvF,OAAA,CAAQb,UAAU,IACpCqG,SAAA,CAAUxF,OAAA,CAAQzB,OAAA,CAAQ,CAAC,CAAC;cAEhC,MAAMA,OAAA,GAAU,EAAE;cAElB,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIwB,OAAA,CAAQvB,MAAA,EAAQ,EAAED,CAAA,EAAG;gBACvCF,OAAA,CAAQqE,IAAA,CAAK3C,OAAA,CAAQxB,CAAC,EAAEG,IAAA,CAAKlB,MAAM;cACpC;cAED+H,IAAA,CAAK5F,WAAA,CACH;gBAAEC,IAAA,EAAM;gBAAaC,EAAA,EAAIC,OAAA,CAAQD,EAAA;gBAAIG,KAAA;gBAAOC,MAAA;gBAAQmF,QAAA;gBAAUrF,OAAA;gBAASG;cAAQ,GAC/E7B,OACD;YACF,SAAQoE,KAAA,EAAP;cACAD,OAAA,CAAQC,KAAA,CAAMA,KAAK;cAEnB8C,IAAA,CAAK5F,WAAA,CAAY;gBAAEC,IAAA,EAAM;gBAASC,EAAA,EAAIC,OAAA,CAAQD,EAAA;gBAAI4C,KAAA,EAAOA,KAAA,CAAM3C;cAAO,CAAE;YACzE;UACf,CAAa;UACD;MACH;IACF;IAED,SAASqF,KAAKK,UAAA,EAAY;MACxB/J,iBAAA,GAAoB,IAAI8D,OAAA,CAASC,OAAA,IAAY;QAC3CuF,WAAA,GAAc;UAAES,UAAA;UAAYC,oBAAA,EAAsBjG;QAAS;QAC3DkG,KAAA,CAAMX,WAAW;MAC3B,CAAS,EAAEnH,IAAA,CAAK,MAAM;QACZmH,WAAA,CAAYY,eAAA,CAAiB;MACvC,CAAS;IACF;IAED,SAASN,kBAAkBpG,UAAA,EAAY;MACrC,MAAM;QAAE2G,WAAA;QAAa5F,KAAA;QAAOC,MAAA;QAAQmF;MAAU,IAAGnG,UAAA;MAEjD,MAAM;QAAE4G,gBAAA;QAAkBC;MAAA,IAAiBC,mBAAA,CAAoBH,WAAA,EAAa5F,KAAA,EAAOC,MAAA,EAAQmF,QAAQ;MAEnG,MAAMY,eAAA,GAAkBjB,WAAA,CAAYkB,uBAAA,CAAwBJ,gBAAgB;MAE5EK,MAAA,CAAOnB,WAAA,CAAYoB,iBAAA,CAAkBN,gBAAgB,GAAG,+CAA+C;MAEvG,MAAM9F,OAAA,GAAU,EAAE;MAElB,IAAI6F,WAAA,KAAgBhE,WAAA,CAAYsB,KAAA,EAAO;QACrC,MAAMkD,UAAA,GAAa,IAAIrB,WAAA,CAAYsB,4BAAA,CAA8B;QAEjE,MAAM;UAAEC,aAAA;UAAeC,aAAA;UAAeC,aAAA;UAAeC,aAAA;UAAeC;QAAU,IAAKzH,UAAA,CAAW0H,UAAA;QAE9F,IAAI;UACF,IAAIC,EAAA;UAEJA,EAAA,GAAKR,UAAA,CAAWS,cAAA,CAAeP,aAAA,EAAeC,aAAA,EAAeC,aAAA,EAAeC,aAAa;UAEzFP,MAAA,CAAOU,EAAA,EAAI,oDAAoD;UAE/DA,EAAA,GAAKR,UAAA,CAAWU,YAAA,CAAaJ,UAAU;UAEvCR,MAAA,CAAOU,EAAA,EAAI,kDAAkD;UAE7D,SAASrI,CAAA,GAAI,GAAGA,CAAA,GAAIU,UAAA,CAAWb,MAAA,CAAOI,MAAA,EAAQD,CAAA,IAAK;YACjD,MAAMwI,KAAA,GAAQ9H,UAAA,CAAWb,MAAA,CAAOG,CAAC;YACjC,MAAMyI,SAAA,GAAY/H,UAAA,CAAW0H,UAAA,CAAWM,UAAA,CAAW1I,CAAC;YAEpD,MAAM2I,aAAA,GAAgBC,4BAAA,CAA6BtB,gBAAA,EAAkBkB,KAAA,CAAM/G,KAAA,EAAO+G,KAAA,CAAM9G,MAAM;YAC9F,MAAMmH,GAAA,GAAM,IAAIC,UAAA,CAAWH,aAAa;YAExCN,EAAA,GAAKR,UAAA,CAAWkB,cAAA,CACdzB,gBAAA,EACAuB,GAAA,EACAF,aAAA,GAAgBlB,eAAA,EAChBe,KAAA,CAAMrI,IAAA,EACN6I,gBAAA,CAAiB1B,gBAAA,EAAkBkB,KAAA,CAAM/G,KAAK,GAC9CwH,iBAAA,CAAkB3B,gBAAA,EAAkBkB,KAAA,CAAM9G,MAAM,GAChD8G,KAAA,CAAM/G,KAAA,EACN+G,KAAA,CAAM9G,MAAA,EACN8G,KAAA,CAAMU,KAAA,EACNT,SAAA,CAAUU,kBAAA,EACVV,SAAA,CAAUW,kBAAA,EACVX,SAAA,CAAUY,oBAAA,EACVZ,SAAA,CAAUa,oBAAA,EACVb,SAAA,CAAUc,UAAA,EACV1C,QAAA,EACA,OACA,GACA,CACD;YAEDc,MAAA,CAAOU,EAAA,EAAI,iEAAiEG,KAAA,CAAMU,KAAA,GAAQ,GAAG;YAE7F1H,OAAA,CAAQ2C,IAAA,CAAK;cAAEhE,IAAA,EAAM0I,GAAA;cAAKpH,KAAA,EAAO+G,KAAA,CAAM/G,KAAA;cAAOC,MAAA,EAAQ8G,KAAA,CAAM9G;YAAM,CAAE;UACrE;QACb,UAAoB;UACRmG,UAAA,CAAW2B,MAAA,CAAQ;QACpB;MACX,OAAe;QACL,SAASxJ,CAAA,GAAI,GAAGA,CAAA,GAAIU,UAAA,CAAWb,MAAA,CAAOI,MAAA,EAAQD,CAAA,IAAK;UACjD,MAAMwI,KAAA,GAAQ9H,UAAA,CAAWb,MAAA,CAAOG,CAAC;UAEjC,MAAM2I,aAAA,GAAgBC,4BAAA,CAA6BtB,gBAAA,EAAkBkB,KAAA,CAAM/G,KAAA,EAAO+G,KAAA,CAAM9G,MAAM;UAC9F,MAAMmH,GAAA,GAAM,IAAIC,UAAA,CAAWH,aAAa;UAExC,MAAMN,EAAA,GAAK7B,WAAA,CAAYiD,mBAAA,CACrBnC,gBAAA,EACAuB,GAAA,EACAF,aAAA,GAAgBlB,eAAA,EAChBe,KAAA,CAAMrI,IAAA,EACN6I,gBAAA,CAAiB1B,gBAAA,EAAkBkB,KAAA,CAAM/G,KAAK,GAC9CwH,iBAAA,CAAkB3B,gBAAA,EAAkBkB,KAAA,CAAM9G,MAAM,GAChD8G,KAAA,CAAM/G,KAAA,EACN+G,KAAA,CAAM9G,MAAA,EACN8G,KAAA,CAAMU,KAAA,EACN,GACAV,KAAA,CAAMrI,IAAA,CAAKS,UAAA,EACX,GACAiG,QAAA,EACA,OACA,GACA,GACA,IACA,EACD;UAEDc,MAAA,CAAOU,EAAA,EAAI,sEAAsEG,KAAA,CAAMU,KAAA,GAAQ,GAAG;UAElG1H,OAAA,CAAQ2C,IAAA,CAAK;YAAEhE,IAAA,EAAM0I,GAAA;YAAKpH,KAAA,EAAO+G,KAAA,CAAM/G,KAAA;YAAOC,MAAA,EAAQ8G,KAAA,CAAM9G;UAAM,CAAE;QACrE;MACF;MAED,OAAO;QAAED,KAAA;QAAOC,MAAA;QAAQmF,QAAA;QAAUrF,OAAA;QAASG,MAAA,EAAQ4F;MAAc;IAClE;IAED,SAASR,UAAU9H,MAAA,EAAQ;MACzB,MAAMyK,SAAA,GAAY,IAAIlD,WAAA,CAAYmD,SAAA,CAAU,IAAIb,UAAA,CAAW7J,MAAM,CAAC;MAElE,MAAMoI,WAAA,GAAcqC,SAAA,CAAUE,OAAA,CAAO,IAAKvG,WAAA,CAAYuB,SAAA,GAAYvB,WAAA,CAAYsB,KAAA;MAC9E,MAAMlD,KAAA,GAAQiI,SAAA,CAAUG,aAAA,CAAc,GAAG,CAAC;MAC1C,MAAMnI,MAAA,GAASgI,SAAA,CAAUI,cAAA,CAAe,GAAG,CAAC;MAC5C,MAAMjK,MAAA,GAAS6J,SAAA,CAAUK,YAAA,CAAa,CAAC;MACvC,MAAMlD,QAAA,GAAW6C,SAAA,CAAUM,WAAA,CAAa;MAExC,SAASC,QAAA,EAAU;QACjBP,SAAA,CAAUQ,KAAA,CAAO;QACjBR,SAAA,CAAUF,MAAA,CAAQ;MACnB;MAED,MAAM;QAAElC,gBAAA;QAAkBC;MAAA,IAAiBC,mBAAA,CAAoBH,WAAA,EAAa5F,KAAA,EAAOC,MAAA,EAAQmF,QAAQ;MAEnG,IAAI,CAACpF,KAAA,IAAS,CAACC,MAAA,IAAU,CAAC7B,MAAA,EAAQ;QAChCoK,OAAA,CAAS;QACT,MAAM,IAAIE,KAAA,CAAM,2CAA2C;MAC5D;MAED,IAAI,CAACT,SAAA,CAAUU,gBAAA,IAAoB;QACjCH,OAAA,CAAS;QACT,MAAM,IAAIE,KAAA,CAAM,oDAAoD;MACrE;MAED,MAAM3I,OAAA,GAAU,EAAE;MAElB,SAAS6I,GAAA,GAAM,GAAGA,GAAA,GAAMxK,MAAA,EAAQwK,GAAA,IAAO;QACrC,MAAMC,QAAA,GAAWZ,SAAA,CAAUG,aAAA,CAAc,GAAGQ,GAAG;QAC/C,MAAME,SAAA,GAAYb,SAAA,CAAUI,cAAA,CAAe,GAAGO,GAAG;QACjD,MAAMxB,GAAA,GAAM,IAAIC,UAAA,CAAWY,SAAA,CAAUc,6BAAA,CAA8B,GAAGH,GAAA,EAAK/C,gBAAgB,CAAC;QAE5F,MAAMmD,MAAA,GAASf,SAAA,CAAUX,cAAA,CAAeF,GAAA,EAAK,GAAGwB,GAAA,EAAK/C,gBAAA,EAAkB,GAAGT,QAAQ;QAElF,IAAI,CAAC4D,MAAA,EAAQ;UACXR,OAAA,CAAS;UACT,MAAM,IAAIE,KAAA,CAAM,mDAAmD;QACpE;QAED3I,OAAA,CAAQ2C,IAAA,CAAK;UAAEhE,IAAA,EAAM0I,GAAA;UAAKpH,KAAA,EAAO6I,QAAA;UAAU5I,MAAA,EAAQ6I;QAAA,CAAW;MAC/D;MAEDN,OAAA,CAAS;MAET,OAAO;QAAExI,KAAA;QAAOC,MAAA;QAAQmF,QAAA;QAAUrF,OAAA;QAASG,MAAA,EAAQ4F;MAAc;IAClE;IAWD,MAAMmD,cAAA,GAAiB,CACrB;MACEC,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYuB,SAAS;MACnC0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiBmC,QAAA,EAAUnC,gBAAA,CAAiBmC,QAAQ;MACvEgC,YAAA,EAAc,CAACpE,YAAA,CAAa4C,oBAAA,EAAsB5C,YAAA,CAAa4C,oBAAoB;MACnF6E,aAAA,EAAeC,QAAA;MACfC,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYsB,KAAA,EAAOtB,WAAA,CAAYuB,SAAS;MACtD0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiBgC,MAAA,EAAQhC,gBAAA,CAAiBgC,MAAM;MACnEmC,YAAA,EAAc,CAACpE,YAAA,CAAa6C,gBAAA,EAAkB7C,YAAA,CAAa6C,gBAAgB;MAC3E4E,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYsB,KAAA,EAAOtB,WAAA,CAAYuB,SAAS;MACtD0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiB2B,GAAA,EAAK3B,gBAAA,CAAiB4B,GAAG;MAC7DuC,YAAA,EAAc,CAACpE,YAAA,CAAaoD,oBAAA,EAAsBpD,YAAA,CAAagD,qBAAqB;MACpFyE,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYsB,KAAA,EAAOtB,WAAA,CAAYuB,SAAS;MACtD0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiByB,IAAA,EAAMzB,gBAAA,CAAiB0B,IAAI;MAC/DyC,YAAA,EAAc,CAACpE,YAAA,CAAakD,eAAA,EAAiBlD,YAAA,CAAa8C,oBAAoB;MAC9E2E,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYsB,KAAA,EAAOtB,WAAA,CAAYuB,SAAS;MACtD0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiByB,IAAA,EAAMzB,gBAAA,CAAiByB,IAAI;MAC/D0C,YAAA,EAAc,CAACpE,YAAA,CAAaiD,eAAA,EAAiBjD,YAAA,CAAaiD,eAAe;MACzEwE,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJtD,WAAA,EAAa,CAAChE,WAAA,CAAYsB,KAAA,EAAOtB,WAAA,CAAYuB,SAAS;MACtD0C,gBAAA,EAAkB,CAAClE,gBAAA,CAAiBiC,YAAA,EAAcjC,gBAAA,CAAiBkC,aAAa;MAChFiC,YAAA,EAAc,CAACpE,YAAA,CAAamD,uBAAA,EAAyBnD,YAAA,CAAa+C,wBAAwB;MAC1F0E,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,EACF;IAED,MAAMC,aAAA,GAAgBN,cAAA,CAAetG,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MACxD,OAAOD,CAAA,CAAEuG,aAAA,GAAgBtG,CAAA,CAAEsG,aAAA;IACnC,CAAO;IACD,MAAMK,aAAA,GAAgBP,cAAA,CAAetG,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MACxD,OAAOD,CAAA,CAAEyG,aAAA,GAAgBxG,CAAA,CAAEwG,aAAA;IACnC,CAAO;IAED,SAAStD,oBAAoBH,WAAA,EAAa5F,KAAA,EAAOC,MAAA,EAAQmF,QAAA,EAAU;MACjE,IAAIS,gBAAA;MACJ,IAAIC,YAAA;MAEJ,MAAM3H,OAAA,GAAUyH,WAAA,KAAgBhE,WAAA,CAAYsB,KAAA,GAAQqG,aAAA,GAAgBC,aAAA;MAEpE,SAASjL,CAAA,GAAI,GAAGA,CAAA,GAAIJ,OAAA,CAAQK,MAAA,EAAQD,CAAA,IAAK;QACvC,MAAMkL,GAAA,GAAMtL,OAAA,CAAQI,CAAC;QAErB,IAAI,CAACO,MAAA,CAAO2K,GAAA,CAAIP,EAAE,GAAG;QACrB,IAAI,CAACO,GAAA,CAAI7D,WAAA,CAAY8D,QAAA,CAAS9D,WAAW,GAAG;QAC5C,IAAI6D,GAAA,CAAIH,eAAA,IAAmB,EAAEK,YAAA,CAAa3J,KAAK,KAAK2J,YAAA,CAAa1J,MAAM,IAAI;QAE3E4F,gBAAA,GAAmB4D,GAAA,CAAI5D,gBAAA,CAAiBT,QAAA,GAAW,IAAI,CAAC;QACxDU,YAAA,GAAe2D,GAAA,CAAI3D,YAAA,CAAaV,QAAA,GAAW,IAAI,CAAC;QAEhD,OAAO;UAAES,gBAAA;UAAkBC;QAAc;MAC1C;MAEDtD,OAAA,CAAQoH,IAAA,CAAK,4FAA4F;MAEzG/D,gBAAA,GAAmBlE,gBAAA,CAAiBsC,MAAA;MACpC6B,YAAA,GAAepE,YAAA,CAAa2C,UAAA;MAE5B,OAAO;QAAEwB,gBAAA;QAAkBC;MAAc;IAC1C;IAED,SAASI,OAAOU,EAAA,EAAI9G,OAAA,EAAS;MAC3B,IAAI,CAAC8G,EAAA,EAAI,MAAM,IAAI8B,KAAA,CAAM5I,OAAO;IACjC;IAED,SAASyH,iBAAiB1B,gBAAA,EAAkB7F,KAAA,EAAO;MACjD,OAAO6J,IAAA,CAAKC,IAAA,CAAK9J,KAAA,GAAQ+E,WAAA,CAAYgF,mBAAA,CAAoBlE,gBAAgB,CAAC;IAC3E;IAED,SAAS2B,kBAAkB3B,gBAAA,EAAkB5F,MAAA,EAAQ;MACnD,OAAO4J,IAAA,CAAKC,IAAA,CAAK7J,MAAA,GAAS8E,WAAA,CAAYiF,oBAAA,CAAqBnE,gBAAgB,CAAC;IAC7E;IAED,SAASsB,6BAA6BtB,gBAAA,EAAkB7F,KAAA,EAAOC,MAAA,EAAQ;MACrE,MAAM+F,eAAA,GAAkBjB,WAAA,CAAYkB,uBAAA,CAAwBJ,gBAAgB;MAE5E,IAAId,WAAA,CAAYkF,oBAAA,CAAqBpE,gBAAgB,GAAG;QACtD,OAAO7F,KAAA,GAAQC,MAAA,GAAS+F,eAAA;MACzB;MAED,IAAIH,gBAAA,KAAqBlE,gBAAA,CAAiBiC,YAAA,IAAgBiC,gBAAA,KAAqBlE,gBAAA,CAAiBkC,aAAA,EAAe;QAG7G,MAAMqG,WAAA,GAAelK,KAAA,GAAQ,IAAK,CAAC;QACnC,MAAMmK,YAAA,GAAgBlK,MAAA,GAAS,IAAK,CAAC;QAErC,QAAQ4J,IAAA,CAAKO,GAAA,CAAI,GAAGF,WAAW,IAAIL,IAAA,CAAKO,GAAA,CAAI,GAAGD,YAAY,IAAI,IAAI,KAAK;MACzE;MAED,OAAO5C,gBAAA,CAAiB1B,gBAAA,EAAkB7F,KAAK,IAAIwH,iBAAA,CAAkB3B,gBAAA,EAAkB5F,MAAM,IAAI+F,eAAA;IAClG;IAED,SAAS2D,aAAaU,KAAA,EAAO;MAC3B,IAAIA,KAAA,IAAS,GAAG,OAAO;MAEvB,QAAQA,KAAA,GAASA,KAAA,GAAQ,OAAQ,KAAKA,KAAA,KAAU;IACjD;EACF;EAoPH,OAAOrH,mBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}