{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Box3, FrontSide } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG, SKIP_GENERATION } from './Constants.js';\nimport { buildPackedTree } from './build/buildTree.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { ExtendedTrianglePool } from '../utils/ExtendedTrianglePool.js';\nimport { shapecast } from './cast/shapecast.js';\nimport { closestPointToPoint } from './cast/closestPointToPoint.js';\nimport { iterateOverTriangles } from './utils/iterationUtils.generated.js';\nimport { refit } from './cast/refit.generated.js';\nimport { raycast } from './cast/raycast.generated.js';\nimport { raycastFirst } from './cast/raycastFirst.generated.js';\nimport { intersectsGeometry } from './cast/intersectsGeometry.generated.js';\nimport { closestPointToGeometry } from './cast/closestPointToGeometry.generated.js';\nimport { iterateOverTriangles_indirect } from './utils/iterationUtils_indirect.generated.js';\nimport { refit_indirect } from './cast/refit_indirect.generated.js';\nimport { raycast_indirect } from './cast/raycast_indirect.generated.js';\nimport { raycastFirst_indirect } from './cast/raycastFirst_indirect.generated.js';\nimport { intersectsGeometry_indirect } from './cast/intersectsGeometry_indirect.generated.js';\nimport { closestPointToGeometry_indirect } from './cast/closestPointToGeometry_indirect.generated.js';\nimport { isSharedArrayBufferSupported } from '../utils/BufferUtils.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { bvhcast } from './cast/bvhcast.js';\nconst obb = /* @__PURE__ */new OrientedBox();\nconst tempBox = /* @__PURE__ */new Box3();\nexport const DEFAULT_OPTIONS = {\n  strategy: CENTER,\n  maxDepth: 40,\n  maxLeafTris: 10,\n  useSharedArrayBuffer: false,\n  setBoundingBox: true,\n  onProgress: null,\n  indirect: false,\n  verbose: true,\n  range: null\n};\nexport class MeshBVH {\n  static serialize(bvh, options = {}) {\n    options = {\n      cloneBuffers: true,\n      ...options\n    };\n    const geometry = bvh.geometry;\n    const rootData = bvh._roots;\n    const indirectBuffer = bvh._indirectBuffer;\n    const indexAttribute = geometry.getIndex();\n    let result;\n    if (options.cloneBuffers) {\n      result = {\n        roots: rootData.map(root => root.slice()),\n        index: indexAttribute ? indexAttribute.array.slice() : null,\n        indirectBuffer: indirectBuffer ? indirectBuffer.slice() : null\n      };\n    } else {\n      result = {\n        roots: rootData,\n        index: indexAttribute ? indexAttribute.array : null,\n        indirectBuffer: indirectBuffer\n      };\n    }\n    return result;\n  }\n  static deserialize(data, geometry, options = {}) {\n    options = {\n      setIndex: true,\n      indirect: Boolean(data.indirectBuffer),\n      ...options\n    };\n    const {\n      index,\n      roots,\n      indirectBuffer\n    } = data;\n    const bvh = new MeshBVH(geometry, {\n      ...options,\n      [SKIP_GENERATION]: true\n    });\n    bvh._roots = roots;\n    bvh._indirectBuffer = indirectBuffer || null;\n    if (options.setIndex) {\n      const indexAttribute = geometry.getIndex();\n      if (indexAttribute === null) {\n        const newIndex = new BufferAttribute(data.index, 1, false);\n        geometry.setIndex(newIndex);\n      } else if (indexAttribute.array !== index) {\n        indexAttribute.array.set(index);\n        indexAttribute.needsUpdate = true;\n      }\n    }\n    return bvh;\n  }\n  get indirect() {\n    return !!this._indirectBuffer;\n  }\n  constructor(geometry, options = {}) {\n    if (!geometry.isBufferGeometry) {\n      throw new Error('MeshBVH: Only BufferGeometries are supported.');\n    } else if (geometry.index && geometry.index.isInterleavedBufferAttribute) {\n      throw new Error('MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.');\n    }\n\n    // default options\n    options = Object.assign({\n      ...DEFAULT_OPTIONS,\n      // undocumented options\n\n      // Whether to skip generating the tree. Used for deserialization.\n      [SKIP_GENERATION]: false\n    }, options);\n    if (options.useSharedArrayBuffer && !isSharedArrayBufferSupported()) {\n      throw new Error('MeshBVH: SharedArrayBuffer is not available.');\n    }\n\n    // retain references to the geometry so we can use them it without having to\n    // take a geometry reference in every function.\n    this.geometry = geometry;\n    this._roots = null;\n    this._indirectBuffer = null;\n    if (!options[SKIP_GENERATION]) {\n      buildPackedTree(this, options);\n      if (!geometry.boundingBox && options.setBoundingBox) {\n        geometry.boundingBox = this.getBoundingBox(new Box3());\n      }\n    }\n    this.resolveTriangleIndex = options.indirect ? i => this._indirectBuffer[i] : i => i;\n  }\n  refit(nodeIndices = null) {\n    const refitFunc = this.indirect ? refit_indirect : refit;\n    return refitFunc(this, nodeIndices);\n  }\n  traverse(callback, rootIndex = 0) {\n    const buffer = this._roots[rootIndex];\n    const uint32Array = new Uint32Array(buffer);\n    const uint16Array = new Uint16Array(buffer);\n    _traverse(0);\n    function _traverse(node32Index, depth = 0) {\n      const node16Index = node32Index * 2;\n      const isLeaf = uint16Array[node16Index + 15] === IS_LEAFNODE_FLAG;\n      if (isLeaf) {\n        const offset = uint32Array[node32Index + 6];\n        const count = uint16Array[node16Index + 14];\n        callback(depth, isLeaf, new Float32Array(buffer, node32Index * 4, 6), offset, count);\n      } else {\n        // TODO: use node functions here\n        const left = node32Index + BYTES_PER_NODE / 4;\n        const right = uint32Array[node32Index + 6];\n        const splitAxis = uint32Array[node32Index + 7];\n        const stopTraversal = callback(depth, isLeaf, new Float32Array(buffer, node32Index * 4, 6), splitAxis);\n        if (!stopTraversal) {\n          _traverse(left, depth + 1);\n          _traverse(right, depth + 1);\n        }\n      }\n    }\n  }\n\n  /* Core Cast Functions */\n  raycast(ray, materialOrSide = FrontSide, near = 0, far = Infinity) {\n    const roots = this._roots;\n    const geometry = this.geometry;\n    const intersects = [];\n    const isMaterial = materialOrSide.isMaterial;\n    const isArrayMaterial = Array.isArray(materialOrSide);\n    const groups = geometry.groups;\n    const side = isMaterial ? materialOrSide.side : materialOrSide;\n    const raycastFunc = this.indirect ? raycast_indirect : raycast;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      const materialSide = isArrayMaterial ? materialOrSide[groups[i].materialIndex].side : side;\n      const startCount = intersects.length;\n      raycastFunc(this, i, materialSide, ray, intersects, near, far);\n      if (isArrayMaterial) {\n        const materialIndex = groups[i].materialIndex;\n        for (let j = startCount, jl = intersects.length; j < jl; j++) {\n          intersects[j].face.materialIndex = materialIndex;\n        }\n      }\n    }\n    return intersects;\n  }\n  raycastFirst(ray, materialOrSide = FrontSide, near = 0, far = Infinity) {\n    const roots = this._roots;\n    const geometry = this.geometry;\n    const isMaterial = materialOrSide.isMaterial;\n    const isArrayMaterial = Array.isArray(materialOrSide);\n    let closestResult = null;\n    const groups = geometry.groups;\n    const side = isMaterial ? materialOrSide.side : materialOrSide;\n    const raycastFirstFunc = this.indirect ? raycastFirst_indirect : raycastFirst;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      const materialSide = isArrayMaterial ? materialOrSide[groups[i].materialIndex].side : side;\n      const result = raycastFirstFunc(this, i, materialSide, ray, near, far);\n      if (result != null && (closestResult == null || result.distance < closestResult.distance)) {\n        closestResult = result;\n        if (isArrayMaterial) {\n          result.face.materialIndex = groups[i].materialIndex;\n        }\n      }\n    }\n    return closestResult;\n  }\n  intersectsGeometry(otherGeometry, geomToMesh) {\n    let result = false;\n    const roots = this._roots;\n    const intersectsGeometryFunc = this.indirect ? intersectsGeometry_indirect : intersectsGeometry;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      result = intersectsGeometryFunc(this, i, otherGeometry, geomToMesh);\n      if (result) {\n        break;\n      }\n    }\n    return result;\n  }\n  shapecast(callbacks) {\n    const triangle = ExtendedTrianglePool.getPrimitive();\n    const iterateFunc = this.indirect ? iterateOverTriangles_indirect : iterateOverTriangles;\n    let {\n      boundsTraverseOrder,\n      intersectsBounds,\n      intersectsRange,\n      intersectsTriangle\n    } = callbacks;\n\n    // wrap the intersectsRange function\n    if (intersectsRange && intersectsTriangle) {\n      const originalIntersectsRange = intersectsRange;\n      intersectsRange = (offset, count, contained, depth, nodeIndex) => {\n        if (!originalIntersectsRange(offset, count, contained, depth, nodeIndex)) {\n          return iterateFunc(offset, count, this, intersectsTriangle, contained, depth, triangle);\n        }\n        return true;\n      };\n    } else if (!intersectsRange) {\n      if (intersectsTriangle) {\n        intersectsRange = (offset, count, contained, depth) => {\n          return iterateFunc(offset, count, this, intersectsTriangle, contained, depth, triangle);\n        };\n      } else {\n        intersectsRange = (offset, count, contained) => {\n          return contained;\n        };\n      }\n    }\n\n    // run shapecast\n    let result = false;\n    let byteOffset = 0;\n    const roots = this._roots;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      const root = roots[i];\n      result = shapecast(this, i, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset);\n      if (result) {\n        break;\n      }\n      byteOffset += root.byteLength;\n    }\n    ExtendedTrianglePool.releasePrimitive(triangle);\n    return result;\n  }\n  bvhcast(otherBvh, matrixToLocal, callbacks) {\n    let {\n      intersectsRanges,\n      intersectsTriangles\n    } = callbacks;\n    const triangle1 = ExtendedTrianglePool.getPrimitive();\n    const indexAttr1 = this.geometry.index;\n    const positionAttr1 = this.geometry.attributes.position;\n    const assignTriangle1 = this.indirect ? i1 => {\n      const ti = this.resolveTriangleIndex(i1);\n      setTriangle(triangle1, ti * 3, indexAttr1, positionAttr1);\n    } : i1 => {\n      setTriangle(triangle1, i1 * 3, indexAttr1, positionAttr1);\n    };\n    const triangle2 = ExtendedTrianglePool.getPrimitive();\n    const indexAttr2 = otherBvh.geometry.index;\n    const positionAttr2 = otherBvh.geometry.attributes.position;\n    const assignTriangle2 = otherBvh.indirect ? i2 => {\n      const ti2 = otherBvh.resolveTriangleIndex(i2);\n      setTriangle(triangle2, ti2 * 3, indexAttr2, positionAttr2);\n    } : i2 => {\n      setTriangle(triangle2, i2 * 3, indexAttr2, positionAttr2);\n    };\n\n    // generate triangle callback if needed\n    if (intersectsTriangles) {\n      const iterateOverDoubleTriangles = (offset1, count1, offset2, count2, depth1, index1, depth2, index2) => {\n        for (let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2++) {\n          assignTriangle2(i2);\n          triangle2.a.applyMatrix4(matrixToLocal);\n          triangle2.b.applyMatrix4(matrixToLocal);\n          triangle2.c.applyMatrix4(matrixToLocal);\n          triangle2.needsUpdate = true;\n          for (let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1++) {\n            assignTriangle1(i1);\n            triangle1.needsUpdate = true;\n            if (intersectsTriangles(triangle1, triangle2, i1, i2, depth1, index1, depth2, index2)) {\n              return true;\n            }\n          }\n        }\n        return false;\n      };\n      if (intersectsRanges) {\n        const originalIntersectsRanges = intersectsRanges;\n        intersectsRanges = function (offset1, count1, offset2, count2, depth1, index1, depth2, index2) {\n          if (!originalIntersectsRanges(offset1, count1, offset2, count2, depth1, index1, depth2, index2)) {\n            return iterateOverDoubleTriangles(offset1, count1, offset2, count2, depth1, index1, depth2, index2);\n          }\n          return true;\n        };\n      } else {\n        intersectsRanges = iterateOverDoubleTriangles;\n      }\n    }\n    return bvhcast(this, otherBvh, matrixToLocal, intersectsRanges);\n  }\n\n  /* Derived Cast Functions */\n  intersectsBox(box, boxToMesh) {\n    obb.set(box.min, box.max, boxToMesh);\n    obb.needsUpdate = true;\n    return this.shapecast({\n      intersectsBounds: box => obb.intersectsBox(box),\n      intersectsTriangle: tri => obb.intersectsTriangle(tri)\n    });\n  }\n  intersectsSphere(sphere) {\n    return this.shapecast({\n      intersectsBounds: box => sphere.intersectsBox(box),\n      intersectsTriangle: tri => tri.intersectsSphere(sphere)\n    });\n  }\n  closestPointToGeometry(otherGeometry, geometryToBvh, target1 = {}, target2 = {}, minThreshold = 0, maxThreshold = Infinity) {\n    const closestPointToGeometryFunc = this.indirect ? closestPointToGeometry_indirect : closestPointToGeometry;\n    return closestPointToGeometryFunc(this, otherGeometry, geometryToBvh, target1, target2, minThreshold, maxThreshold);\n  }\n  closestPointToPoint(point, target = {}, minThreshold = 0, maxThreshold = Infinity) {\n    return closestPointToPoint(this, point, target, minThreshold, maxThreshold);\n  }\n  getBoundingBox(target) {\n    target.makeEmpty();\n    const roots = this._roots;\n    roots.forEach(buffer => {\n      arrayToBox(0, new Float32Array(buffer), tempBox);\n      target.union(tempBox);\n    });\n    return target;\n  }\n}", "map": {"version": 3, "names": ["BufferAttribute", "Box3", "FrontSide", "CENTER", "BYTES_PER_NODE", "IS_LEAFNODE_FLAG", "SKIP_GENERATION", "buildPackedTree", "OrientedBox", "arrayToBox", "ExtendedTrianglePool", "shapecast", "closestPointToPoint", "iterateOverTriangles", "refit", "raycast", "raycastFirst", "intersectsGeometry", "closestPointToGeometry", "iterateOverTriangles_indirect", "refit_indirect", "raycast_indirect", "raycastFirst_indirect", "intersectsGeometry_indirect", "closestPointToGeometry_indirect", "isSharedArrayBufferSupported", "set<PERSON>riangle", "bvhcast", "obb", "tempBox", "DEFAULT_OPTIONS", "strategy", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "useSharedArrayBuffer", "setBoundingBox", "onProgress", "indirect", "verbose", "range", "MeshBVH", "serialize", "bvh", "options", "cloneBuffers", "geometry", "rootData", "_roots", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON>er", "indexAttribute", "getIndex", "result", "roots", "map", "root", "slice", "index", "array", "deserialize", "data", "setIndex", "Boolean", "newIndex", "set", "needsUpdate", "constructor", "isBufferGeometry", "Error", "isInterleavedBufferAttribute", "Object", "assign", "boundingBox", "getBoundingBox", "resolveTriangleIndex", "i", "nodeIndices", "refitFunc", "traverse", "callback", "rootIndex", "buffer", "uint32Array", "Uint32Array", "uint16Array", "Uint16Array", "_traverse", "node32Index", "depth", "node16Index", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "Float32Array", "left", "right", "splitAxis", "stopTraversal", "ray", "materialOrSide", "near", "far", "Infinity", "intersects", "isMaterial", "isArrayMaterial", "Array", "isArray", "groups", "side", "raycastFunc", "l", "length", "materialSide", "materialIndex", "startCount", "j", "jl", "face", "closestResult", "raycastFirstFunc", "distance", "otherGeometry", "geomToMesh", "intersectsGeometryFunc", "callbacks", "triangle", "getPrimitive", "iterateFunc", "boundsTraverseOrder", "intersectsBounds", "intersectsRange", "intersectsTriangle", "originalIntersectsRange", "contained", "nodeIndex", "byteOffset", "byteLength", "releasePrimitive", "otherBvh", "matrixToLocal", "intersectsRanges", "intersects<PERSON><PERSON><PERSON>", "triangle1", "indexAttr1", "positionAttr1", "attributes", "position", "assignTriangle1", "i1", "ti", "triangle2", "indexAttr2", "positionAttr2", "assignTriangle2", "i2", "ti2", "iterateOverDoubleTriangles", "offset1", "count1", "offset2", "count2", "depth1", "index1", "depth2", "index2", "l2", "a", "applyMatrix4", "b", "c", "l1", "originalIntersectsRanges", "intersectsBox", "box", "boxToMesh", "min", "max", "tri", "intersectsSphere", "sphere", "geometryToBvh", "target1", "target2", "min<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "closestPointToGeometryFunc", "point", "target", "makeEmpty", "for<PERSON>ach", "union"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/MeshBVH.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Box3, FrontSide } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG, SKIP_GENERATION } from './Constants.js';\nimport { buildPackedTree } from './build/buildTree.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { ExtendedTrianglePool } from '../utils/ExtendedTrianglePool.js';\nimport { shapecast } from './cast/shapecast.js';\nimport { closestPointToPoint } from './cast/closestPointToPoint.js';\n\nimport { iterateOverTriangles } from './utils/iterationUtils.generated.js';\nimport { refit } from './cast/refit.generated.js';\nimport { raycast } from './cast/raycast.generated.js';\nimport { raycastFirst } from './cast/raycastFirst.generated.js';\nimport { intersectsGeometry } from './cast/intersectsGeometry.generated.js';\nimport { closestPointToGeometry } from './cast/closestPointToGeometry.generated.js';\n\nimport { iterateOverTriangles_indirect } from './utils/iterationUtils_indirect.generated.js';\nimport { refit_indirect } from './cast/refit_indirect.generated.js';\nimport { raycast_indirect } from './cast/raycast_indirect.generated.js';\nimport { raycastFirst_indirect } from './cast/raycastFirst_indirect.generated.js';\nimport { intersectsGeometry_indirect } from './cast/intersectsGeometry_indirect.generated.js';\nimport { closestPointToGeometry_indirect } from './cast/closestPointToGeometry_indirect.generated.js';\nimport { isSharedArrayBufferSupported } from '../utils/BufferUtils.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { bvhcast } from './cast/bvhcast.js';\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst tempBox = /* @__PURE__ */ new Box3();\nexport const DEFAULT_OPTIONS = {\n\tstrategy: CENTER,\n\tmaxDepth: 40,\n\tmaxLeafTris: 10,\n\tuseSharedArrayBuffer: false,\n\tsetBoundingBox: true,\n\tonProgress: null,\n\tindirect: false,\n\tverbose: true,\n\trange: null\n};\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute ? indexAttribute.array.slice() : null,\n\t\t\t\tindirectBuffer: indirectBuffer ? indirectBuffer.slice() : null,\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute ? indexAttribute.array : null,\n\t\t\t\tindirectBuffer: indirectBuffer,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\tindirect: Boolean( data.indirectBuffer ),\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots, indirectBuffer } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\t\tbvh._indirectBuffer = indirectBuffer || null;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tget indirect() {\n\n\t\treturn ! ! this._indirectBuffer;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\t...DEFAULT_OPTIONS,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && ! isSharedArrayBufferSupported() ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\t\tthis._roots = null;\n\t\tthis._indirectBuffer = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tbuildPackedTree( this, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.resolveTriangleIndex = options.indirect ? i => this._indirectBuffer[ i ] : i => i;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tconst refitFunc = this.indirect ? refit_indirect : refit;\n\t\treturn refitFunc( this, nodeIndices );\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFunc = this.indirect ? raycast_indirect : raycast;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\traycastFunc( this, i, materialSide, ray, intersects, near, far );\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFirstFunc = this.indirect ? raycastFirst_indirect : raycastFirst;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst result = raycastFirstFunc( this, i, materialSide, ray, near, far );\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tlet result = false;\n\t\tconst roots = this._roots;\n\t\tconst intersectsGeometryFunc = this.indirect ? intersectsGeometry_indirect : intersectsGeometry;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tresult = intersectsGeometryFunc( this, i, otherGeometry, geomToMesh );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks ) {\n\n\t\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\t\tconst iterateFunc = this.indirect ? iterateOverTriangles_indirect : iterateOverTriangles;\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\t// wrap the intersectsRange function\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\t// run shapecast\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst root = roots[ i ];\n\t\t\tresult = shapecast( this, i, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\tExtendedTrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst triangle1 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr1 = this.geometry.index;\n\t\tconst positionAttr1 = this.geometry.attributes.position;\n\t\tconst assignTriangle1 = this.indirect ?\n\t\t\ti1 => {\n\n\n\t\t\t\tconst ti = this.resolveTriangleIndex( i1 );\n\t\t\t\tsetTriangle( triangle1, ti * 3, indexAttr1, positionAttr1 );\n\n\t\t\t} :\n\t\t\ti1 => {\n\n\t\t\t\tsetTriangle( triangle1, i1 * 3, indexAttr1, positionAttr1 );\n\n\t\t\t};\n\n\t\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr2 = otherBvh.geometry.index;\n\t\tconst positionAttr2 = otherBvh.geometry.attributes.position;\n\t\tconst assignTriangle2 = otherBvh.indirect ?\n\t\t\ti2 => {\n\n\t\t\t\tconst ti2 = otherBvh.resolveTriangleIndex( i2 );\n\t\t\t\tsetTriangle( triangle2, ti2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t} :\n\t\t\ti2 => {\n\n\t\t\t\tsetTriangle( triangle2, i2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t};\n\n\t\t// generate triangle callback if needed\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tconst iterateOverDoubleTriangles = ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) => {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tassignTriangle2( i2 );\n\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tassignTriangle1( i1 );\n\n\t\t\t\t\t\ttriangle1.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle1, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t};\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvhcast( this, otherBvh, matrixToLocal, intersectsRanges );\n\n\t}\n\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tconst closestPointToGeometryFunc = this.indirect ? closestPointToGeometry_indirect : closestPointToGeometry;\n\t\treturn closestPointToGeometryFunc(\n\t\t\tthis,\n\t\t\totherGeometry,\n\t\t\tgeometryToBvh,\n\t\t\ttarget1,\n\t\t\ttarget2,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\treturn closestPointToPoint(\n\t\t\tthis,\n\t\t\tpoint,\n\t\t\ttarget,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,IAAI,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,MAAM,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,gBAAgB;AAC1F,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,mBAAmB,QAAQ,+BAA+B;AAEnE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,sBAAsB,QAAQ,4CAA4C;AAEnF,SAASC,6BAA6B,QAAQ,8CAA8C;AAC5F,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,2BAA2B,QAAQ,iDAAiD;AAC7F,SAASC,+BAA+B,QAAQ,qDAAqD;AACrG,SAASC,4BAA4B,QAAQ,yBAAyB;AACtE,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,OAAO,QAAQ,mBAAmB;AAE3C,MAAMC,GAAG,GAAG,eAAgB,IAAIpB,WAAW,CAAC,CAAC;AAC7C,MAAMqB,OAAO,GAAG,eAAgB,IAAI5B,IAAI,CAAC,CAAC;AAC1C,OAAO,MAAM6B,eAAe,GAAG;EAC9BC,QAAQ,EAAE5B,MAAM;EAChB6B,QAAQ,EAAE,EAAE;EACZC,WAAW,EAAE,EAAE;EACfC,oBAAoB,EAAE,KAAK;EAC3BC,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACR,CAAC;AAED,OAAO,MAAMC,OAAO,CAAC;EAEpB,OAAOC,SAASA,CAAEC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAG;IAErCA,OAAO,GAAG;MACTC,YAAY,EAAE,IAAI;MAClB,GAAGD;IACJ,CAAC;IAED,MAAME,QAAQ,GAAGH,GAAG,CAACG,QAAQ;IAC7B,MAAMC,QAAQ,GAAGJ,GAAG,CAACK,MAAM;IAC3B,MAAMC,cAAc,GAAGN,GAAG,CAACO,eAAe;IAC1C,MAAMC,cAAc,GAAGL,QAAQ,CAACM,QAAQ,CAAC,CAAC;IAC1C,IAAIC,MAAM;IACV,IAAKT,OAAO,CAACC,YAAY,EAAG;MAE3BQ,MAAM,GAAG;QACRC,KAAK,EAAEP,QAAQ,CAACQ,GAAG,CAAEC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAAE,CAAC;QAC3CC,KAAK,EAAEP,cAAc,GAAGA,cAAc,CAACQ,KAAK,CAACF,KAAK,CAAC,CAAC,GAAG,IAAI;QAC3DR,cAAc,EAAEA,cAAc,GAAGA,cAAc,CAACQ,KAAK,CAAC,CAAC,GAAG;MAC3D,CAAC;IAEF,CAAC,MAAM;MAENJ,MAAM,GAAG;QACRC,KAAK,EAAEP,QAAQ;QACfW,KAAK,EAAEP,cAAc,GAAGA,cAAc,CAACQ,KAAK,GAAG,IAAI;QACnDV,cAAc,EAAEA;MACjB,CAAC;IAEF;IAEA,OAAOI,MAAM;EAEd;EAEA,OAAOO,WAAWA,CAAEC,IAAI,EAAEf,QAAQ,EAAEF,OAAO,GAAG,CAAC,CAAC,EAAG;IAElDA,OAAO,GAAG;MACTkB,QAAQ,EAAE,IAAI;MACdxB,QAAQ,EAAEyB,OAAO,CAAEF,IAAI,CAACZ,cAAe,CAAC;MACxC,GAAGL;IACJ,CAAC;IAED,MAAM;MAAEc,KAAK;MAAEJ,KAAK;MAAEL;IAAe,CAAC,GAAGY,IAAI;IAC7C,MAAMlB,GAAG,GAAG,IAAIF,OAAO,CAAEK,QAAQ,EAAE;MAAE,GAAGF,OAAO;MAAE,CAAErC,eAAe,GAAI;IAAK,CAAE,CAAC;IAC9EoC,GAAG,CAACK,MAAM,GAAGM,KAAK;IAClBX,GAAG,CAACO,eAAe,GAAGD,cAAc,IAAI,IAAI;IAE5C,IAAKL,OAAO,CAACkB,QAAQ,EAAG;MAEvB,MAAMX,cAAc,GAAGL,QAAQ,CAACM,QAAQ,CAAC,CAAC;MAC1C,IAAKD,cAAc,KAAK,IAAI,EAAG;QAE9B,MAAMa,QAAQ,GAAG,IAAI/D,eAAe,CAAE4D,IAAI,CAACH,KAAK,EAAE,CAAC,EAAE,KAAM,CAAC;QAC5DZ,QAAQ,CAACgB,QAAQ,CAAEE,QAAS,CAAC;MAE9B,CAAC,MAAM,IAAKb,cAAc,CAACQ,KAAK,KAAKD,KAAK,EAAG;QAE5CP,cAAc,CAACQ,KAAK,CAACM,GAAG,CAAEP,KAAM,CAAC;QACjCP,cAAc,CAACe,WAAW,GAAG,IAAI;MAElC;IAED;IAEA,OAAOvB,GAAG;EAEX;EAEA,IAAIL,QAAQA,CAAA,EAAG;IAEd,OAAO,CAAE,CAAE,IAAI,CAACY,eAAe;EAEhC;EAEAiB,WAAWA,CAAErB,QAAQ,EAAEF,OAAO,GAAG,CAAC,CAAC,EAAG;IAErC,IAAK,CAAEE,QAAQ,CAACsB,gBAAgB,EAAG;MAElC,MAAM,IAAIC,KAAK,CAAE,+CAAgD,CAAC;IAEnE,CAAC,MAAM,IAAKvB,QAAQ,CAACY,KAAK,IAAIZ,QAAQ,CAACY,KAAK,CAACY,4BAA4B,EAAG;MAE3E,MAAM,IAAID,KAAK,CAAE,+EAAgF,CAAC;IAEnG;;IAEA;IACAzB,OAAO,GAAG2B,MAAM,CAACC,MAAM,CAAE;MAExB,GAAGzC,eAAe;MAElB;;MAEA;MACA,CAAExB,eAAe,GAAI;IAEtB,CAAC,EAAEqC,OAAQ,CAAC;IAEZ,IAAKA,OAAO,CAACT,oBAAoB,IAAI,CAAET,4BAA4B,CAAC,CAAC,EAAG;MAEvE,MAAM,IAAI2C,KAAK,CAAE,8CAA+C,CAAC;IAElE;;IAEA;IACA;IACA,IAAI,CAACvB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,MAAM,GAAG,IAAI;IAClB,IAAI,CAACE,eAAe,GAAG,IAAI;IAC3B,IAAK,CAAEN,OAAO,CAAErC,eAAe,CAAE,EAAG;MAEnCC,eAAe,CAAE,IAAI,EAAEoC,OAAQ,CAAC;MAEhC,IAAK,CAAEE,QAAQ,CAAC2B,WAAW,IAAI7B,OAAO,CAACR,cAAc,EAAG;QAEvDU,QAAQ,CAAC2B,WAAW,GAAG,IAAI,CAACC,cAAc,CAAE,IAAIxE,IAAI,CAAC,CAAE,CAAC;MAEzD;IAED;IAEA,IAAI,CAACyE,oBAAoB,GAAG/B,OAAO,CAACN,QAAQ,GAAGsC,CAAC,IAAI,IAAI,CAAC1B,eAAe,CAAE0B,CAAC,CAAE,GAAGA,CAAC,IAAIA,CAAC;EAEvF;EAEA7D,KAAKA,CAAE8D,WAAW,GAAG,IAAI,EAAG;IAE3B,MAAMC,SAAS,GAAG,IAAI,CAACxC,QAAQ,GAAGjB,cAAc,GAAGN,KAAK;IACxD,OAAO+D,SAAS,CAAE,IAAI,EAAED,WAAY,CAAC;EAEtC;EAEAE,QAAQA,CAAEC,QAAQ,EAAEC,SAAS,GAAG,CAAC,EAAG;IAEnC,MAAMC,MAAM,GAAG,IAAI,CAAClC,MAAM,CAAEiC,SAAS,CAAE;IACvC,MAAME,WAAW,GAAG,IAAIC,WAAW,CAAEF,MAAO,CAAC;IAC7C,MAAMG,WAAW,GAAG,IAAIC,WAAW,CAAEJ,MAAO,CAAC;IAC7CK,SAAS,CAAE,CAAE,CAAC;IAEd,SAASA,SAASA,CAAEC,WAAW,EAAEC,KAAK,GAAG,CAAC,EAAG;MAE5C,MAAMC,WAAW,GAAGF,WAAW,GAAG,CAAC;MACnC,MAAMG,MAAM,GAAGN,WAAW,CAAEK,WAAW,GAAG,EAAE,CAAE,KAAKpF,gBAAgB;MACnE,IAAKqF,MAAM,EAAG;QAEb,MAAMC,MAAM,GAAGT,WAAW,CAAEK,WAAW,GAAG,CAAC,CAAE;QAC7C,MAAMK,KAAK,GAAGR,WAAW,CAAEK,WAAW,GAAG,EAAE,CAAE;QAC7CV,QAAQ,CAAES,KAAK,EAAEE,MAAM,EAAE,IAAIG,YAAY,CAAEZ,MAAM,EAAEM,WAAW,GAAG,CAAC,EAAE,CAAE,CAAC,EAAEI,MAAM,EAAEC,KAAM,CAAC;MAEzF,CAAC,MAAM;QAEN;QACA,MAAME,IAAI,GAAGP,WAAW,GAAGnF,cAAc,GAAG,CAAC;QAC7C,MAAM2F,KAAK,GAAGb,WAAW,CAAEK,WAAW,GAAG,CAAC,CAAE;QAC5C,MAAMS,SAAS,GAAGd,WAAW,CAAEK,WAAW,GAAG,CAAC,CAAE;QAChD,MAAMU,aAAa,GAAGlB,QAAQ,CAAES,KAAK,EAAEE,MAAM,EAAE,IAAIG,YAAY,CAAEZ,MAAM,EAAEM,WAAW,GAAG,CAAC,EAAE,CAAE,CAAC,EAAES,SAAU,CAAC;QAE1G,IAAK,CAAEC,aAAa,EAAG;UAEtBX,SAAS,CAAEQ,IAAI,EAAEN,KAAK,GAAG,CAAE,CAAC;UAC5BF,SAAS,CAAES,KAAK,EAAEP,KAAK,GAAG,CAAE,CAAC;QAE9B;MAED;IAED;EAED;;EAEA;EACAzE,OAAOA,CAAEmF,GAAG,EAAEC,cAAc,GAAGjG,SAAS,EAAEkG,IAAI,GAAG,CAAC,EAAEC,GAAG,GAAGC,QAAQ,EAAG;IAEpE,MAAMjD,KAAK,GAAG,IAAI,CAACN,MAAM;IACzB,MAAMF,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAM0D,UAAU,GAAG,EAAE;IACrB,MAAMC,UAAU,GAAGL,cAAc,CAACK,UAAU;IAC5C,MAAMC,eAAe,GAAGC,KAAK,CAACC,OAAO,CAAER,cAAe,CAAC;IAEvD,MAAMS,MAAM,GAAG/D,QAAQ,CAAC+D,MAAM;IAC9B,MAAMC,IAAI,GAAGL,UAAU,GAAGL,cAAc,CAACU,IAAI,GAAGV,cAAc;IAC9D,MAAMW,WAAW,GAAG,IAAI,CAACzE,QAAQ,GAAGhB,gBAAgB,GAAGN,OAAO;IAC9D,KAAM,IAAI4D,CAAC,GAAG,CAAC,EAAEoC,CAAC,GAAG1D,KAAK,CAAC2D,MAAM,EAAErC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAG,EAAG;MAEhD,MAAMsC,YAAY,GAAGR,eAAe,GAAGN,cAAc,CAAES,MAAM,CAAEjC,CAAC,CAAE,CAACuC,aAAa,CAAE,CAACL,IAAI,GAAGA,IAAI;MAC9F,MAAMM,UAAU,GAAGZ,UAAU,CAACS,MAAM;MAEpCF,WAAW,CAAE,IAAI,EAAEnC,CAAC,EAAEsC,YAAY,EAAEf,GAAG,EAAEK,UAAU,EAAEH,IAAI,EAAEC,GAAI,CAAC;MAEhE,IAAKI,eAAe,EAAG;QAEtB,MAAMS,aAAa,GAAGN,MAAM,CAAEjC,CAAC,CAAE,CAACuC,aAAa;QAC/C,KAAM,IAAIE,CAAC,GAAGD,UAAU,EAAEE,EAAE,GAAGd,UAAU,CAACS,MAAM,EAAEI,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;UAEhEb,UAAU,CAAEa,CAAC,CAAE,CAACE,IAAI,CAACJ,aAAa,GAAGA,aAAa;QAEnD;MAED;IAED;IAEA,OAAOX,UAAU;EAElB;EAEAvF,YAAYA,CAAEkF,GAAG,EAAEC,cAAc,GAAGjG,SAAS,EAAEkG,IAAI,GAAG,CAAC,EAAEC,GAAG,GAAGC,QAAQ,EAAG;IAEzE,MAAMjD,KAAK,GAAG,IAAI,CAACN,MAAM;IACzB,MAAMF,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAM2D,UAAU,GAAGL,cAAc,CAACK,UAAU;IAC5C,MAAMC,eAAe,GAAGC,KAAK,CAACC,OAAO,CAAER,cAAe,CAAC;IAEvD,IAAIoB,aAAa,GAAG,IAAI;IAExB,MAAMX,MAAM,GAAG/D,QAAQ,CAAC+D,MAAM;IAC9B,MAAMC,IAAI,GAAGL,UAAU,GAAGL,cAAc,CAACU,IAAI,GAAGV,cAAc;IAC9D,MAAMqB,gBAAgB,GAAG,IAAI,CAACnF,QAAQ,GAAGf,qBAAqB,GAAGN,YAAY;IAC7E,KAAM,IAAI2D,CAAC,GAAG,CAAC,EAAEoC,CAAC,GAAG1D,KAAK,CAAC2D,MAAM,EAAErC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAG,EAAG;MAEhD,MAAMsC,YAAY,GAAGR,eAAe,GAAGN,cAAc,CAAES,MAAM,CAAEjC,CAAC,CAAE,CAACuC,aAAa,CAAE,CAACL,IAAI,GAAGA,IAAI;MAC9F,MAAMzD,MAAM,GAAGoE,gBAAgB,CAAE,IAAI,EAAE7C,CAAC,EAAEsC,YAAY,EAAEf,GAAG,EAAEE,IAAI,EAAEC,GAAI,CAAC;MACxE,IAAKjD,MAAM,IAAI,IAAI,KAAMmE,aAAa,IAAI,IAAI,IAAInE,MAAM,CAACqE,QAAQ,GAAGF,aAAa,CAACE,QAAQ,CAAE,EAAG;QAE9FF,aAAa,GAAGnE,MAAM;QACtB,IAAKqD,eAAe,EAAG;UAEtBrD,MAAM,CAACkE,IAAI,CAACJ,aAAa,GAAGN,MAAM,CAAEjC,CAAC,CAAE,CAACuC,aAAa;QAEtD;MAED;IAED;IAEA,OAAOK,aAAa;EAErB;EAEAtG,kBAAkBA,CAAEyG,aAAa,EAAEC,UAAU,EAAG;IAE/C,IAAIvE,MAAM,GAAG,KAAK;IAClB,MAAMC,KAAK,GAAG,IAAI,CAACN,MAAM;IACzB,MAAM6E,sBAAsB,GAAG,IAAI,CAACvF,QAAQ,GAAGd,2BAA2B,GAAGN,kBAAkB;IAC/F,KAAM,IAAI0D,CAAC,GAAG,CAAC,EAAEoC,CAAC,GAAG1D,KAAK,CAAC2D,MAAM,EAAErC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAG,EAAG;MAEhDvB,MAAM,GAAGwE,sBAAsB,CAAE,IAAI,EAAEjD,CAAC,EAAE+C,aAAa,EAAEC,UAAW,CAAC;MAErE,IAAKvE,MAAM,EAAG;QAEb;MAED;IAED;IAEA,OAAOA,MAAM;EAEd;EAEAzC,SAASA,CAAEkH,SAAS,EAAG;IAEtB,MAAMC,QAAQ,GAAGpH,oBAAoB,CAACqH,YAAY,CAAC,CAAC;IACpD,MAAMC,WAAW,GAAG,IAAI,CAAC3F,QAAQ,GAAGlB,6BAA6B,GAAGN,oBAAoB;IACxF,IAAI;MACHoH,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfC;IACD,CAAC,GAAGP,SAAS;;IAEb;IACA,IAAKM,eAAe,IAAIC,kBAAkB,EAAG;MAE5C,MAAMC,uBAAuB,GAAGF,eAAe;MAC/CA,eAAe,GAAGA,CAAExC,MAAM,EAAEC,KAAK,EAAE0C,SAAS,EAAE9C,KAAK,EAAE+C,SAAS,KAAM;QAEnE,IAAK,CAAEF,uBAAuB,CAAE1C,MAAM,EAAEC,KAAK,EAAE0C,SAAS,EAAE9C,KAAK,EAAE+C,SAAU,CAAC,EAAG;UAE9E,OAAOP,WAAW,CAAErC,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEwC,kBAAkB,EAAEE,SAAS,EAAE9C,KAAK,EAAEsC,QAAS,CAAC;QAE1F;QAEA,OAAO,IAAI;MAEZ,CAAC;IAEF,CAAC,MAAM,IAAK,CAAEK,eAAe,EAAG;MAE/B,IAAKC,kBAAkB,EAAG;QAEzBD,eAAe,GAAGA,CAAExC,MAAM,EAAEC,KAAK,EAAE0C,SAAS,EAAE9C,KAAK,KAAM;UAExD,OAAOwC,WAAW,CAAErC,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEwC,kBAAkB,EAAEE,SAAS,EAAE9C,KAAK,EAAEsC,QAAS,CAAC;QAE1F,CAAC;MAEF,CAAC,MAAM;QAENK,eAAe,GAAGA,CAAExC,MAAM,EAAEC,KAAK,EAAE0C,SAAS,KAAM;UAEjD,OAAOA,SAAS;QAEjB,CAAC;MAEF;IAED;;IAEA;IACA,IAAIlF,MAAM,GAAG,KAAK;IAClB,IAAIoF,UAAU,GAAG,CAAC;IAClB,MAAMnF,KAAK,GAAG,IAAI,CAACN,MAAM;IACzB,KAAM,IAAI4B,CAAC,GAAG,CAAC,EAAEoC,CAAC,GAAG1D,KAAK,CAAC2D,MAAM,EAAErC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAG,EAAG;MAEhD,MAAMpB,IAAI,GAAGF,KAAK,CAAEsB,CAAC,CAAE;MACvBvB,MAAM,GAAGzC,SAAS,CAAE,IAAI,EAAEgE,CAAC,EAAEuD,gBAAgB,EAAEC,eAAe,EAAEF,mBAAmB,EAAEO,UAAW,CAAC;MAEjG,IAAKpF,MAAM,EAAG;QAEb;MAED;MAEAoF,UAAU,IAAIjF,IAAI,CAACkF,UAAU;IAE9B;IAEA/H,oBAAoB,CAACgI,gBAAgB,CAAEZ,QAAS,CAAC;IAEjD,OAAO1E,MAAM;EAEd;EAEAzB,OAAOA,CAAEgH,QAAQ,EAAEC,aAAa,EAAEf,SAAS,EAAG;IAE7C,IAAI;MACHgB,gBAAgB;MAChBC;IACD,CAAC,GAAGjB,SAAS;IAEb,MAAMkB,SAAS,GAAGrI,oBAAoB,CAACqH,YAAY,CAAC,CAAC;IACrD,MAAMiB,UAAU,GAAG,IAAI,CAACnG,QAAQ,CAACY,KAAK;IACtC,MAAMwF,aAAa,GAAG,IAAI,CAACpG,QAAQ,CAACqG,UAAU,CAACC,QAAQ;IACvD,MAAMC,eAAe,GAAG,IAAI,CAAC/G,QAAQ,GACpCgH,EAAE,IAAI;MAGL,MAAMC,EAAE,GAAG,IAAI,CAAC5E,oBAAoB,CAAE2E,EAAG,CAAC;MAC1C3H,WAAW,CAAEqH,SAAS,EAAEO,EAAE,GAAG,CAAC,EAAEN,UAAU,EAAEC,aAAc,CAAC;IAE5D,CAAC,GACDI,EAAE,IAAI;MAEL3H,WAAW,CAAEqH,SAAS,EAAEM,EAAE,GAAG,CAAC,EAAEL,UAAU,EAAEC,aAAc,CAAC;IAE5D,CAAC;IAEF,MAAMM,SAAS,GAAG7I,oBAAoB,CAACqH,YAAY,CAAC,CAAC;IACrD,MAAMyB,UAAU,GAAGb,QAAQ,CAAC9F,QAAQ,CAACY,KAAK;IAC1C,MAAMgG,aAAa,GAAGd,QAAQ,CAAC9F,QAAQ,CAACqG,UAAU,CAACC,QAAQ;IAC3D,MAAMO,eAAe,GAAGf,QAAQ,CAACtG,QAAQ,GACxCsH,EAAE,IAAI;MAEL,MAAMC,GAAG,GAAGjB,QAAQ,CAACjE,oBAAoB,CAAEiF,EAAG,CAAC;MAC/CjI,WAAW,CAAE6H,SAAS,EAAEK,GAAG,GAAG,CAAC,EAAEJ,UAAU,EAAEC,aAAc,CAAC;IAE7D,CAAC,GACDE,EAAE,IAAI;MAELjI,WAAW,CAAE6H,SAAS,EAAEI,EAAE,GAAG,CAAC,EAAEH,UAAU,EAAEC,aAAc,CAAC;IAE5D,CAAC;;IAEF;IACA,IAAKX,mBAAmB,EAAG;MAE1B,MAAMe,0BAA0B,GAAGA,CAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,KAAM;QAE1G,KAAM,IAAIV,EAAE,GAAGK,OAAO,EAAEM,EAAE,GAAGN,OAAO,GAAGC,MAAM,EAAEN,EAAE,GAAGW,EAAE,EAAEX,EAAE,EAAG,EAAG;UAE/DD,eAAe,CAAEC,EAAG,CAAC;UAErBJ,SAAS,CAACgB,CAAC,CAACC,YAAY,CAAE5B,aAAc,CAAC;UACzCW,SAAS,CAACkB,CAAC,CAACD,YAAY,CAAE5B,aAAc,CAAC;UACzCW,SAAS,CAACmB,CAAC,CAACF,YAAY,CAAE5B,aAAc,CAAC;UACzCW,SAAS,CAACtF,WAAW,GAAG,IAAI;UAE5B,KAAM,IAAIoF,EAAE,GAAGS,OAAO,EAAEa,EAAE,GAAGb,OAAO,GAAGC,MAAM,EAAEV,EAAE,GAAGsB,EAAE,EAAEtB,EAAE,EAAG,EAAG;YAE/DD,eAAe,CAAEC,EAAG,CAAC;YAErBN,SAAS,CAAC9E,WAAW,GAAG,IAAI;YAE5B,IAAK6E,mBAAmB,CAAEC,SAAS,EAAEQ,SAAS,EAAEF,EAAE,EAAEM,EAAE,EAAEO,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC,EAAG;cAE1F,OAAO,IAAI;YAEZ;UAED;QAED;QAEA,OAAO,KAAK;MAEb,CAAC;MAED,IAAKxB,gBAAgB,EAAG;QAEvB,MAAM+B,wBAAwB,GAAG/B,gBAAgB;QACjDA,gBAAgB,GAAG,SAAAA,CAAWiB,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAG;UAEhG,IAAK,CAAEO,wBAAwB,CAAEd,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC,EAAG;YAErG,OAAOR,0BAA0B,CAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC;UAEtG;UAEA,OAAO,IAAI;QAEZ,CAAC;MAEF,CAAC,MAAM;QAENxB,gBAAgB,GAAGgB,0BAA0B;MAE9C;IAED;IAEA,OAAOlI,OAAO,CAAE,IAAI,EAAEgH,QAAQ,EAAEC,aAAa,EAAEC,gBAAiB,CAAC;EAElE;;EAGA;EACAgC,aAAaA,CAAEC,GAAG,EAAEC,SAAS,EAAG;IAE/BnJ,GAAG,CAACoC,GAAG,CAAE8G,GAAG,CAACE,GAAG,EAAEF,GAAG,CAACG,GAAG,EAAEF,SAAU,CAAC;IACtCnJ,GAAG,CAACqC,WAAW,GAAG,IAAI;IAEtB,OAAO,IAAI,CAACtD,SAAS,CACpB;MACCuH,gBAAgB,EAAE4C,GAAG,IAAIlJ,GAAG,CAACiJ,aAAa,CAAEC,GAAI,CAAC;MACjD1C,kBAAkB,EAAE8C,GAAG,IAAItJ,GAAG,CAACwG,kBAAkB,CAAE8C,GAAI;IACxD,CACD,CAAC;EAEF;EAEAC,gBAAgBA,CAAEC,MAAM,EAAG;IAE1B,OAAO,IAAI,CAACzK,SAAS,CACpB;MACCuH,gBAAgB,EAAE4C,GAAG,IAAIM,MAAM,CAACP,aAAa,CAAEC,GAAI,CAAC;MACpD1C,kBAAkB,EAAE8C,GAAG,IAAIA,GAAG,CAACC,gBAAgB,CAAEC,MAAO;IACzD,CACD,CAAC;EAEF;EAEAlK,sBAAsBA,CAAEwG,aAAa,EAAE2D,aAAa,EAAEC,OAAO,GAAG,CAAE,CAAC,EAAEC,OAAO,GAAG,CAAE,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAEC,YAAY,GAAGnF,QAAQ,EAAG;IAE/H,MAAMoF,0BAA0B,GAAG,IAAI,CAACrJ,QAAQ,GAAGb,+BAA+B,GAAGN,sBAAsB;IAC3G,OAAOwK,0BAA0B,CAChC,IAAI,EACJhE,aAAa,EACb2D,aAAa,EACbC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,YACD,CAAC;EAEF;EAEA7K,mBAAmBA,CAAE+K,KAAK,EAAEC,MAAM,GAAG,CAAE,CAAC,EAAEJ,YAAY,GAAG,CAAC,EAAEC,YAAY,GAAGnF,QAAQ,EAAG;IAErF,OAAO1F,mBAAmB,CACzB,IAAI,EACJ+K,KAAK,EACLC,MAAM,EACNJ,YAAY,EACZC,YACD,CAAC;EAEF;EAEAhH,cAAcA,CAAEmH,MAAM,EAAG;IAExBA,MAAM,CAACC,SAAS,CAAC,CAAC;IAElB,MAAMxI,KAAK,GAAG,IAAI,CAACN,MAAM;IACzBM,KAAK,CAACyI,OAAO,CAAE7G,MAAM,IAAI;MAExBxE,UAAU,CAAE,CAAC,EAAE,IAAIoF,YAAY,CAAEZ,MAAO,CAAC,EAAEpD,OAAQ,CAAC;MACpD+J,MAAM,CAACG,KAAK,CAAElK,OAAQ,CAAC;IAExB,CAAE,CAAC;IAEH,OAAO+J,MAAM;EAEd;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}