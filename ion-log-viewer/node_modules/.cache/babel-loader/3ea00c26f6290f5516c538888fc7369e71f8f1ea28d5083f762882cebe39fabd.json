{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/TopicReader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Paper, Typography, Box, FormControl, InputLabel, Select, MenuItem, Card, CardContent, Chip, Alert } from '@mui/material';\nimport { findMessageAtTime } from '../utils/ionParser';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TopicReader = ({\n  data,\n  currentTime\n}) => {\n  _s();\n  const [selectedTopicName, setSelectedTopicName] = useState('');\n\n  // Filter to only human-readable topics\n  const humanReadableTopics = useMemo(() => {\n    return data.topics.filter(topic => topic.isHumanReadable);\n  }, [data.topics]);\n\n  // Get the selected topic\n  const selectedTopic = useMemo(() => {\n    return humanReadableTopics.find(topic => topic.name === selectedTopicName);\n  }, [humanReadableTopics, selectedTopicName]);\n\n  // Find the message closest to the current time\n  const currentMessage = useMemo(() => {\n    if (!selectedTopic || selectedTopic.messages.length === 0) {\n      return null;\n    }\n    return findMessageAtTime(selectedTopic.messages, currentTime);\n  }, [selectedTopic, currentTime]);\n\n  // Auto-select first topic if none selected\n  React.useEffect(() => {\n    if (!selectedTopicName && humanReadableTopics.length > 0) {\n      setSelectedTopicName(humanReadableTopics[0].name);\n    }\n  }, [selectedTopicName, humanReadableTopics]);\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleString();\n  };\n  const renderMessageContent = (content, depth = 0) => {\n    // Prevent infinite recursion\n    if (depth > 5) {\n      return /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"[Max depth reached]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 14\n      }, this);\n    }\n    try {\n      if (content === null || content === undefined) {\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"null\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 16\n        }, this);\n      }\n\n      // Handle primitive types\n      if (typeof content === 'string' || typeof content === 'number' || typeof content === 'boolean') {\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: String(content)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      }\n\n      // Handle Uint8Array and other binary data\n      if (content instanceof Uint8Array) {\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Binary data (\", content.length, \" bytes)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this);\n      }\n\n      // Handle arrays\n      if (Array.isArray(content)) {\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Array (\", content.length, \" items):\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              ml: 2,\n              mt: 1\n            },\n            children: [content.slice(0, 5).map((item, index) => {\n              try {\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [\"[\", index, \"]:\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 23\n                  }, this), ' ', renderMessageContent(item, depth + 1)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 21\n                }, this);\n              } catch (error) {\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [\"[\", index, \"]:\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this), ' ', /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"error\",\n                    children: \"Error rendering item\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this);\n              }\n            }), content.length > 5 && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [\"... and \", content.length - 5, \" more items\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this);\n      }\n\n      // Handle objects\n      if (typeof content === 'object') {\n        // Check if it's a special object type that might cause issues\n        if (content.constructor && content.constructor.name !== 'Object') {\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [content.constructor.name, \" object\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this);\n        }\n        const entries = Object.entries(content);\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Object (\", entries.length, \" properties):\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              ml: 2,\n              mt: 1\n            },\n            children: [entries.slice(0, 5).map(([key, value]) => {\n              try {\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [key, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 23\n                  }, this), ' ', renderMessageContent(value, depth + 1)]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this);\n              } catch (error) {\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [key, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this), ' ', /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"error\",\n                    children: \"Error rendering value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this);\n              }\n            }), entries.length > 5 && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [\"... and \", entries.length - 5, \" more properties\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this);\n      }\n\n      // Fallback for any other type\n      return /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: String(content)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 14\n      }, this);\n    } catch (error) {\n      console.error('Error rendering message content:', error, content);\n      return /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"error\",\n        children: [\"Error rendering content: \", error instanceof Error ? error.message : 'Unknown error']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"h2\",\n        gutterBottom: true,\n        children: \"Topic Reader\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: \"Question 2\",\n        color: \"primary\",\n        size: \"small\",\n        sx: {\n          mb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        id: \"topic-select-label\",\n        children: \"Select Topic\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        labelId: \"topic-select-label\",\n        value: selectedTopicName,\n        label: \"Select Topic\",\n        onChange: e => setSelectedTopicName(e.target.value),\n        children: humanReadableTopics.map(topic => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: topic.name,\n          children: [topic.name, \" (\", topic.messages.length, \" messages)\"]\n        }, topic.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), selectedTopic && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Topic Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `Type: ${selectedTopic.type}`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Messages: ${selectedTopic.messages.length}`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), selectedTopic.frequency && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Freq: ${selectedTopic.frequency}Hz`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this), selectedTopic ? currentMessage ? /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: [\"Message at \", formatTimestamp(currentMessage.timestamp)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this), currentMessage.publishTime && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          display: \"block\",\n          gutterBottom: true,\n          children: [\"Published: \", formatTimestamp(currentMessage.publishTime)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 1,\n            maxHeight: 300,\n            overflow: 'auto'\n          },\n          children: renderMessageContent(currentMessage.content)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"No messages found for the current timestamp\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      children: \"No human-readable topics available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this), selectedTopic && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: [\"Total human-readable topics: \", humanReadableTopics.length, \" / \", data.topics.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s(TopicReader, \"IV/SjbgxyZZGSb/+fxkG08CecMY=\");\n_c = TopicReader;\nexport default TopicReader;\nvar _c;\n$RefreshReg$(_c, \"TopicReader\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Paper", "Typography", "Box", "FormControl", "InputLabel", "Select", "MenuItem", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "findMessageAtTime", "jsxDEV", "_jsxDEV", "TopicReader", "data", "currentTime", "_s", "selectedTopicName", "setSelectedTopicName", "humanReadableTopics", "topics", "filter", "topic", "isHumanReadable", "selectedTopic", "find", "name", "currentMessage", "messages", "length", "useEffect", "formatTimestamp", "timestamp", "Date", "toLocaleString", "renderMessageContent", "content", "depth", "variant", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "undefined", "String", "Uint8Array", "Array", "isArray", "sx", "ml", "mt", "slice", "map", "item", "index", "mb", "error", "constructor", "entries", "Object", "key", "value", "console", "Error", "message", "p", "height", "component", "gutterBottom", "label", "size", "fullWidth", "id", "labelId", "onChange", "e", "target", "display", "gap", "type", "frequency", "publishTime", "maxHeight", "overflow", "severity", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/TopicReader.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport {\n  Paper,\n  Typography,\n  Box,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Card,\n  CardContent,\n  Chip,\n  Alert\n} from '@mui/material';\nimport { IonLogData, findMessageAtTime } from '../utils/ionParser';\n\ninterface TopicReaderProps {\n  data: IonLogData;\n  currentTime: number;\n}\n\nconst TopicReader: React.FC<TopicReaderProps> = ({ data, currentTime }) => {\n  const [selectedTopicName, setSelectedTopicName] = useState<string>('');\n\n  // Filter to only human-readable topics\n  const humanReadableTopics = useMemo(() => {\n    return data.topics.filter(topic => topic.isHumanReadable);\n  }, [data.topics]);\n\n  // Get the selected topic\n  const selectedTopic = useMemo(() => {\n    return humanReadableTopics.find(topic => topic.name === selectedTopicName);\n  }, [humanReadableTopics, selectedTopicName]);\n\n  // Find the message closest to the current time\n  const currentMessage = useMemo(() => {\n    if (!selectedTopic || selectedTopic.messages.length === 0) {\n      return null;\n    }\n    return findMessageAtTime(selectedTopic.messages, currentTime);\n  }, [selectedTopic, currentTime]);\n\n  // Auto-select first topic if none selected\n  React.useEffect(() => {\n    if (!selectedTopicName && humanReadableTopics.length > 0) {\n      setSelectedTopicName(humanReadableTopics[0].name);\n    }\n  }, [selectedTopicName, humanReadableTopics]);\n\n  const formatTimestamp = (timestamp: number): string => {\n    return new Date(timestamp).toLocaleString();\n  };\n\n  const renderMessageContent = (content: any, depth: number = 0): React.ReactNode => {\n    // Prevent infinite recursion\n    if (depth > 5) {\n      return <Typography variant=\"body2\" color=\"text.secondary\">[Max depth reached]</Typography>;\n    }\n\n    try {\n      if (content === null || content === undefined) {\n        return <Typography variant=\"body2\" color=\"text.secondary\">null</Typography>;\n      }\n\n      // Handle primitive types\n      if (typeof content === 'string' || typeof content === 'number' || typeof content === 'boolean') {\n        return <Typography variant=\"body2\">{String(content)}</Typography>;\n      }\n\n      // Handle Uint8Array and other binary data\n      if (content instanceof Uint8Array) {\n        return (\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Binary data ({content.length} bytes)\n          </Typography>\n        );\n      }\n\n      // Handle arrays\n      if (Array.isArray(content)) {\n        return (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Array ({content.length} items):</Typography>\n            <Box sx={{ ml: 2, mt: 1 }}>\n              {content.slice(0, 5).map((item, index) => {\n                try {\n                  return (\n                    <Box key={index} sx={{ mb: 0.5 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">[{index}]:</Typography>\n                      {' '}\n                      {renderMessageContent(item, depth + 1)}\n                    </Box>\n                  );\n                } catch (error) {\n                  return (\n                    <Box key={index} sx={{ mb: 0.5 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">[{index}]:</Typography>\n                      {' '}\n                      <Typography variant=\"body2\" color=\"error\">Error rendering item</Typography>\n                    </Box>\n                  );\n                }\n              })}\n              {content.length > 5 && (\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  ... and {content.length - 5} more items\n                </Typography>\n              )}\n            </Box>\n          </Box>\n        );\n      }\n\n      // Handle objects\n      if (typeof content === 'object') {\n        // Check if it's a special object type that might cause issues\n        if (content.constructor && content.constructor.name !== 'Object') {\n          return (\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              {content.constructor.name} object\n            </Typography>\n          );\n        }\n\n        const entries = Object.entries(content);\n        return (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">Object ({entries.length} properties):</Typography>\n            <Box sx={{ ml: 2, mt: 1 }}>\n              {entries.slice(0, 5).map(([key, value]) => {\n                try {\n                  return (\n                    <Box key={key} sx={{ mb: 0.5 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">{key}:</Typography>\n                      {' '}\n                      {renderMessageContent(value, depth + 1)}\n                    </Box>\n                  );\n                } catch (error) {\n                  return (\n                    <Box key={key} sx={{ mb: 0.5 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">{key}:</Typography>\n                      {' '}\n                      <Typography variant=\"body2\" color=\"error\">Error rendering value</Typography>\n                    </Box>\n                  );\n                }\n              })}\n              {entries.length > 5 && (\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  ... and {entries.length - 5} more properties\n                </Typography>\n              )}\n            </Box>\n          </Box>\n        );\n      }\n\n      // Fallback for any other type\n      return <Typography variant=\"body2\">{String(content)}</Typography>;\n\n    } catch (error) {\n      console.error('Error rendering message content:', error, content);\n      return (\n        <Typography variant=\"body2\" color=\"error\">\n          Error rendering content: {error instanceof Error ? error.message : 'Unknown error'}\n        </Typography>\n      );\n    }\n  };\n\n  return (\n    <Paper sx={{ p: 2, height: '100%' }}>\n      <Box sx={{ mb: 2 }}>\n        <Typography variant=\"h6\" component=\"h2\" gutterBottom>\n          Topic Reader\n        </Typography>\n        <Chip\n          label=\"Question 2\"\n          color=\"primary\"\n          size=\"small\"\n          sx={{ mb: 1 }}\n        />\n      </Box>\n\n      {/* Topic Selection */}\n      <FormControl fullWidth sx={{ mb: 2 }}>\n        <InputLabel id=\"topic-select-label\">Select Topic</InputLabel>\n        <Select\n          labelId=\"topic-select-label\"\n          value={selectedTopicName}\n          label=\"Select Topic\"\n          onChange={(e) => setSelectedTopicName(e.target.value)}\n        >\n          {humanReadableTopics.map((topic) => (\n            <MenuItem key={topic.name} value={topic.name}>\n              {topic.name} ({topic.messages.length} messages)\n            </MenuItem>\n          ))}\n        </Select>\n      </FormControl>\n\n      {/* Topic Information */}\n      {selectedTopic && (\n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Topic Information\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>\n            <Chip label={`Type: ${selectedTopic.type}`} size=\"small\" variant=\"outlined\" />\n            <Chip label={`Messages: ${selectedTopic.messages.length}`} size=\"small\" variant=\"outlined\" />\n            {selectedTopic.frequency && (\n              <Chip label={`Freq: ${selectedTopic.frequency}Hz`} size=\"small\" variant=\"outlined\" />\n            )}\n          </Box>\n        </Box>\n      )}\n\n      {/* Current Message Display */}\n      {selectedTopic ? (\n        currentMessage ? (\n          <Card variant=\"outlined\">\n            <CardContent>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Message at {formatTimestamp(currentMessage.timestamp)}\n              </Typography>\n              {currentMessage.publishTime && (\n                <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" gutterBottom>\n                  Published: {formatTimestamp(currentMessage.publishTime)}\n                </Typography>\n              )}\n              <Box sx={{ mt: 1, maxHeight: 300, overflow: 'auto' }}>\n                {renderMessageContent(currentMessage.content)}\n              </Box>\n            </CardContent>\n          </Card>\n        ) : (\n          <Alert severity=\"info\">\n            No messages found for the current timestamp\n          </Alert>\n        )\n      ) : (\n        <Alert severity=\"warning\">\n          No human-readable topics available\n        </Alert>\n      )}\n\n      {/* Statistics */}\n      {selectedTopic && (\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Total human-readable topics: {humanReadableTopics.length} / {data.topics.length}\n          </Typography>\n        </Box>\n      )}\n    </Paper>\n  );\n};\n\nexport default TopicReader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SAAqBC,iBAAiB,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOnE,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAMsB,mBAAmB,GAAGrB,OAAO,CAAC,MAAM;IACxC,OAAOgB,IAAI,CAACM,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,eAAe,CAAC;EAC3D,CAAC,EAAE,CAACT,IAAI,CAACM,MAAM,CAAC,CAAC;;EAEjB;EACA,MAAMI,aAAa,GAAG1B,OAAO,CAAC,MAAM;IAClC,OAAOqB,mBAAmB,CAACM,IAAI,CAACH,KAAK,IAAIA,KAAK,CAACI,IAAI,KAAKT,iBAAiB,CAAC;EAC5E,CAAC,EAAE,CAACE,mBAAmB,EAAEF,iBAAiB,CAAC,CAAC;;EAE5C;EACA,MAAMU,cAAc,GAAG7B,OAAO,CAAC,MAAM;IACnC,IAAI,CAAC0B,aAAa,IAAIA,aAAa,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IACA,OAAOnB,iBAAiB,CAACc,aAAa,CAACI,QAAQ,EAAEb,WAAW,CAAC;EAC/D,CAAC,EAAE,CAACS,aAAa,EAAET,WAAW,CAAC,CAAC;;EAEhC;EACAnB,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACb,iBAAiB,IAAIE,mBAAmB,CAACU,MAAM,GAAG,CAAC,EAAE;MACxDX,oBAAoB,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC;IACnD;EACF,CAAC,EAAE,CAACT,iBAAiB,EAAEE,mBAAmB,CAAC,CAAC;EAE5C,MAAMY,eAAe,GAAIC,SAAiB,IAAa;IACrD,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACC,OAAY,EAAEC,KAAa,GAAG,CAAC,KAAsB;IACjF;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,oBAAOzB,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAC5F;IAEA,IAAI;MACF,IAAIR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKS,SAAS,EAAE;QAC7C,oBAAOjC,OAAA,CAACZ,UAAU;UAACsC,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAC7E;;MAEA;MACA,IAAI,OAAOR,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9F,oBAAOxB,OAAA,CAACZ,UAAU;UAACsC,OAAO,EAAC,OAAO;UAAAE,QAAA,EAAEM,MAAM,CAACV,OAAO;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MACnE;;MAEA;MACA,IAAIR,OAAO,YAAYW,UAAU,EAAE;QACjC,oBACEnC,OAAA,CAACZ,UAAU;UAACsC,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAC,QAAA,GAAC,eACpC,EAACJ,OAAO,CAACP,MAAM,EAAC,SAC/B;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAEjB;;MAEA;MACA,IAAII,KAAK,CAACC,OAAO,CAACb,OAAO,CAAC,EAAE;QAC1B,oBACExB,OAAA,CAACX,GAAG;UAAAuC,QAAA,gBACF5B,OAAA,CAACZ,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAC,QAAA,GAAC,SAAO,EAACJ,OAAO,CAACP,MAAM,EAAC,UAAQ;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/FhC,OAAA,CAACX,GAAG;YAACiD,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,GACvBJ,OAAO,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;cACxC,IAAI;gBACF,oBACE5C,OAAA,CAACX,GAAG;kBAAaiD,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAI,CAAE;kBAAAjB,QAAA,gBAC/B5B,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,GAAC,EAACgB,KAAK,EAAC,IAAE;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAC3E,GAAG,EACHT,oBAAoB,CAACoB,IAAI,EAAElB,KAAK,GAAG,CAAC,CAAC;gBAAA,GAH9BmB,KAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIV,CAAC;cAEV,CAAC,CAAC,OAAOc,KAAK,EAAE;gBACd,oBACE9C,OAAA,CAACX,GAAG;kBAAaiD,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAI,CAAE;kBAAAjB,QAAA,gBAC/B5B,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,GAAC,EAACgB,KAAK,EAAC,IAAE;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAC3E,GAAG,eACJhC,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAHnEY,KAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIV,CAAC;cAEV;YACF,CAAC,CAAC,EACDR,OAAO,CAACP,MAAM,GAAG,CAAC,iBACjBjB,OAAA,CAACZ,UAAU;cAACsC,OAAO,EAAC,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAAAC,QAAA,GAAC,UAC3C,EAACJ,OAAO,CAACP,MAAM,GAAG,CAAC,EAAC,aAC9B;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;;MAEA;MACA,IAAI,OAAOR,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA,IAAIA,OAAO,CAACuB,WAAW,IAAIvB,OAAO,CAACuB,WAAW,CAACjC,IAAI,KAAK,QAAQ,EAAE;UAChE,oBACEd,OAAA,CAACZ,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAC,QAAA,GAC/CJ,OAAO,CAACuB,WAAW,CAACjC,IAAI,EAAC,SAC5B;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAEjB;QAEA,MAAMgB,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACxB,OAAO,CAAC;QACvC,oBACExB,OAAA,CAACX,GAAG;UAAAuC,QAAA,gBACF5B,OAAA,CAACZ,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAC,QAAA,GAAC,UAAQ,EAACoB,OAAO,CAAC/B,MAAM,EAAC,eAAa;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrGhC,OAAA,CAACX,GAAG;YAACiD,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,GACvBoB,OAAO,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACQ,GAAG,EAAEC,KAAK,CAAC,KAAK;cACzC,IAAI;gBACF,oBACEnD,OAAA,CAACX,GAAG;kBAAWiD,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAI,CAAE;kBAAAjB,QAAA,gBAC7B5B,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAC,QAAA,GAAEsB,GAAG,EAAC,GAAC;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACvE,GAAG,EACHT,oBAAoB,CAAC4B,KAAK,EAAE1B,KAAK,GAAG,CAAC,CAAC;gBAAA,GAH/ByB,GAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIR,CAAC;cAEV,CAAC,CAAC,OAAOc,KAAK,EAAE;gBACd,oBACE9C,OAAA,CAACX,GAAG;kBAAWiD,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAI,CAAE;kBAAAjB,QAAA,gBAC7B5B,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAC,QAAA,GAAEsB,GAAG,EAAC,GAAC;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACvE,GAAG,eACJhC,OAAA,CAACZ,UAAU;oBAACsC,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAHpEkB,GAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIR,CAAC;cAEV;YACF,CAAC,CAAC,EACDgB,OAAO,CAAC/B,MAAM,GAAG,CAAC,iBACjBjB,OAAA,CAACZ,UAAU;cAACsC,OAAO,EAAC,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAAAC,QAAA,GAAC,UAC3C,EAACoB,OAAO,CAAC/B,MAAM,GAAG,CAAC,EAAC,kBAC9B;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;;MAEA;MACA,oBAAOhC,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,OAAO;QAAAE,QAAA,EAAEM,MAAM,CAACV,OAAO;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAEnE,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,kCAAkC,EAAEA,KAAK,EAAEtB,OAAO,CAAC;MACjE,oBACExB,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,OAAO;QAAAC,QAAA,GAAC,2BACf,EAACkB,KAAK,YAAYO,KAAK,GAAGP,KAAK,CAACQ,OAAO,GAAG,eAAe;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC;IAEjB;EACF,CAAC;EAED,oBACEhC,OAAA,CAACb,KAAK;IAACmD,EAAE,EAAE;MAAEiB,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAA5B,QAAA,gBAClC5B,OAAA,CAACX,GAAG;MAACiD,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBACjB5B,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,IAAI;QAAC+B,SAAS,EAAC,IAAI;QAACC,YAAY;QAAA9B,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhC,OAAA,CAACJ,IAAI;QACH+D,KAAK,EAAC,YAAY;QAClBhC,KAAK,EAAC,SAAS;QACfiC,IAAI,EAAC,OAAO;QACZtB,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhC,OAAA,CAACV,WAAW;MAACuE,SAAS;MAACvB,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBACnC5B,OAAA,CAACT,UAAU;QAACuE,EAAE,EAAC,oBAAoB;QAAAlC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DhC,OAAA,CAACR,MAAM;QACLuE,OAAO,EAAC,oBAAoB;QAC5BZ,KAAK,EAAE9C,iBAAkB;QACzBsD,KAAK,EAAC,cAAc;QACpBK,QAAQ,EAAGC,CAAC,IAAK3D,oBAAoB,CAAC2D,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;QAAAvB,QAAA,EAErDrB,mBAAmB,CAACmC,GAAG,CAAEhC,KAAK,iBAC7BV,OAAA,CAACP,QAAQ;UAAkB0D,KAAK,EAAEzC,KAAK,CAACI,IAAK;UAAAc,QAAA,GAC1ClB,KAAK,CAACI,IAAI,EAAC,IAAE,EAACJ,KAAK,CAACM,QAAQ,CAACC,MAAM,EAAC,YACvC;QAAA,GAFeP,KAAK,CAACI,IAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEf,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGbpB,aAAa,iBACZZ,OAAA,CAACX,GAAG;MAACiD,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBACjB5B,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,WAAW;QAACgC,YAAY;QAAA9B,QAAA,EAAC;MAE7C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhC,OAAA,CAACX,GAAG;QAACiD,EAAE,EAAE;UAAE6B,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEvB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBAC1C5B,OAAA,CAACJ,IAAI;UAAC+D,KAAK,EAAE,SAAS/C,aAAa,CAACyD,IAAI,EAAG;UAACT,IAAI,EAAC,OAAO;UAAClC,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EhC,OAAA,CAACJ,IAAI;UAAC+D,KAAK,EAAE,aAAa/C,aAAa,CAACI,QAAQ,CAACC,MAAM,EAAG;UAAC2C,IAAI,EAAC,OAAO;UAAClC,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5FpB,aAAa,CAAC0D,SAAS,iBACtBtE,OAAA,CAACJ,IAAI;UAAC+D,KAAK,EAAE,SAAS/C,aAAa,CAAC0D,SAAS,IAAK;UAACV,IAAI,EAAC,OAAO;UAAClC,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACrF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApB,aAAa,GACZG,cAAc,gBACZf,OAAA,CAACN,IAAI;MAACgC,OAAO,EAAC,UAAU;MAAAE,QAAA,eACtB5B,OAAA,CAACL,WAAW;QAAAiC,QAAA,gBACV5B,OAAA,CAACZ,UAAU;UAACsC,OAAO,EAAC,WAAW;UAACgC,YAAY;UAAA9B,QAAA,GAAC,aAChC,EAACT,eAAe,CAACJ,cAAc,CAACK,SAAS,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,EACZjB,cAAc,CAACwD,WAAW,iBACzBvE,OAAA,CAACZ,UAAU;UAACsC,OAAO,EAAC,SAAS;UAACC,KAAK,EAAC,gBAAgB;UAACwC,OAAO,EAAC,OAAO;UAACT,YAAY;UAAA9B,QAAA,GAAC,aACrE,EAACT,eAAe,CAACJ,cAAc,CAACwD,WAAW,CAAC;QAAA;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACb,eACDhC,OAAA,CAACX,GAAG;UAACiD,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEgC,SAAS,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7C,QAAA,EAClDL,oBAAoB,CAACR,cAAc,CAACS,OAAO;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPhC,OAAA,CAACH,KAAK;MAAC6E,QAAQ,EAAC,MAAM;MAAA9C,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,gBAEDhC,OAAA,CAACH,KAAK;MAAC6E,QAAQ,EAAC,SAAS;MAAA9C,QAAA,EAAC;IAE1B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGApB,aAAa,iBACZZ,OAAA,CAACX,GAAG;MAACiD,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAZ,QAAA,eACjB5B,OAAA,CAACZ,UAAU;QAACsC,OAAO,EAAC,SAAS;QAACC,KAAK,EAAC,gBAAgB;QAAAC,QAAA,GAAC,+BACtB,EAACrB,mBAAmB,CAACU,MAAM,EAAC,KAAG,EAACf,IAAI,CAACM,MAAM,CAACS,MAAM;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAAC5B,EAAA,CA5OIH,WAAuC;AAAA0E,EAAA,GAAvC1E,WAAuC;AA8O7C,eAAeA,WAAW;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}