{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Plane, Raycaster, Vector2, Vector3, Matrix4 } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nclass DragControls extends EventDispatcher {\n  constructor(_objects, _camera, _domElement) {\n    super();\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"transformGroup\", false);\n    __publicField(this, \"_objects\");\n    __publicField(this, \"_camera\");\n    __publicField(this, \"_domElement\");\n    __publicField(this, \"_plane\", new Plane());\n    __publicField(this, \"_raycaster\", new Raycaster());\n    __publicField(this, \"_mouse\", new Vector2());\n    __publicField(this, \"_offset\", new Vector3());\n    __publicField(this, \"_intersection\", new Vector3());\n    __publicField(this, \"_worldPosition\", new Vector3());\n    __publicField(this, \"_inverseMatrix\", new Matrix4());\n    __publicField(this, \"_intersections\", []);\n    __publicField(this, \"_selected\", null);\n    __publicField(this, \"_hovered\", null);\n    __publicField(this, \"activate\", () => {\n      this._domElement.addEventListener(\"pointermove\", this.onPointerMove);\n      this._domElement.addEventListener(\"pointerdown\", this.onPointerDown);\n      this._domElement.addEventListener(\"pointerup\", this.onPointerCancel);\n      this._domElement.addEventListener(\"pointerleave\", this.onPointerCancel);\n      this._domElement.addEventListener(\"touchmove\", this.onTouchMove);\n      this._domElement.addEventListener(\"touchstart\", this.onTouchStart);\n      this._domElement.addEventListener(\"touchend\", this.onTouchEnd);\n    });\n    __publicField(this, \"deactivate\", () => {\n      this._domElement.removeEventListener(\"pointermove\", this.onPointerMove);\n      this._domElement.removeEventListener(\"pointerdown\", this.onPointerDown);\n      this._domElement.removeEventListener(\"pointerup\", this.onPointerCancel);\n      this._domElement.removeEventListener(\"pointerleave\", this.onPointerCancel);\n      this._domElement.removeEventListener(\"touchmove\", this.onTouchMove);\n      this._domElement.removeEventListener(\"touchstart\", this.onTouchStart);\n      this._domElement.removeEventListener(\"touchend\", this.onTouchEnd);\n      this._domElement.style.cursor = \"\";\n    });\n    // TODO: confirm if this can be removed?\n    __publicField(this, \"dispose\", () => this.deactivate());\n    __publicField(this, \"getObjects\", () => this._objects);\n    __publicField(this, \"getRaycaster\", () => this._raycaster);\n    __publicField(this, \"onMouseMove\", event => {\n      const rect = this._domElement.getBoundingClientRect();\n      this._mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      this._mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      this._raycaster.setFromCamera(this._mouse, this._camera);\n      if (this._selected && this.enabled) {\n        if (this._raycaster.ray.intersectPlane(this._plane, this._intersection)) {\n          this._selected.position.copy(this._intersection.sub(this._offset).applyMatrix4(this._inverseMatrix));\n        }\n        this.dispatchEvent({\n          type: \"drag\",\n          object: this._selected\n        });\n        return;\n      }\n      this._intersections.length = 0;\n      this._raycaster.setFromCamera(this._mouse, this._camera);\n      this._raycaster.intersectObjects(this._objects, true, this._intersections);\n      if (this._intersections.length > 0) {\n        const object = this._intersections[0].object;\n        this._plane.setFromNormalAndCoplanarPoint(this._camera.getWorldDirection(this._plane.normal), this._worldPosition.setFromMatrixPosition(object.matrixWorld));\n        if (this._hovered !== object) {\n          this.dispatchEvent({\n            type: \"hoveron\",\n            object\n          });\n          this._domElement.style.cursor = \"pointer\";\n          this._hovered = object;\n        }\n      } else {\n        if (this._hovered !== null) {\n          this.dispatchEvent({\n            type: \"hoveroff\",\n            object: this._hovered\n          });\n          this._domElement.style.cursor = \"auto\";\n          this._hovered = null;\n        }\n      }\n    });\n    __publicField(this, \"onMouseDown\", () => {\n      this._intersections.length = 0;\n      this._raycaster.setFromCamera(this._mouse, this._camera);\n      this._raycaster.intersectObjects(this._objects, true, this._intersections);\n      if (this._intersections.length > 0) {\n        this._selected = this.transformGroup === true ? this._objects[0] : this._intersections[0].object;\n        if (this._raycaster.ray.intersectPlane(this._plane, this._intersection) && this._selected.parent) {\n          this._inverseMatrix.copy(this._selected.parent.matrixWorld).invert();\n          this._offset.copy(this._intersection).sub(this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld));\n        }\n        this._domElement.style.cursor = \"move\";\n        this.dispatchEvent({\n          type: \"dragstart\",\n          object: this._selected\n        });\n      }\n    });\n    __publicField(this, \"onMouseCancel\", () => {\n      if (this._selected) {\n        this.dispatchEvent({\n          type: \"dragend\",\n          object: this._selected\n        });\n        this._selected = null;\n      }\n      this._domElement.style.cursor = this._hovered ? \"pointer\" : \"auto\";\n    });\n    __publicField(this, \"onPointerMove\", event => {\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseMove(event);\n          break;\n      }\n    });\n    __publicField(this, \"onPointerDown\", event => {\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseDown();\n          break;\n      }\n    });\n    __publicField(this, \"onPointerCancel\", event => {\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseCancel();\n          break;\n      }\n    });\n    __publicField(this, \"onTouchMove\", event => {\n      event.preventDefault();\n      const newEvent = event.changedTouches[0];\n      const rect = this._domElement.getBoundingClientRect();\n      this._mouse.x = (newEvent.clientX - rect.left) / rect.width * 2 - 1;\n      this._mouse.y = -((newEvent.clientY - rect.top) / rect.height) * 2 + 1;\n      this._raycaster.setFromCamera(this._mouse, this._camera);\n      if (this._selected && this.enabled) {\n        if (this._raycaster.ray.intersectPlane(this._plane, this._intersection)) {\n          this._selected.position.copy(this._intersection.sub(this._offset).applyMatrix4(this._inverseMatrix));\n        }\n        this.dispatchEvent({\n          type: \"drag\",\n          object: this._selected\n        });\n        return;\n      }\n    });\n    __publicField(this, \"onTouchStart\", event => {\n      event.preventDefault();\n      const newEvent = event.changedTouches[0];\n      const rect = this._domElement.getBoundingClientRect();\n      this._mouse.x = (newEvent.clientX - rect.left) / rect.width * 2 - 1;\n      this._mouse.y = -((newEvent.clientY - rect.top) / rect.height) * 2 + 1;\n      this._intersections.length = 0;\n      this._raycaster.setFromCamera(this._mouse, this._camera);\n      this._raycaster.intersectObjects(this._objects, true, this._intersections);\n      if (this._intersections.length > 0) {\n        this._selected = this.transformGroup === true ? this._objects[0] : this._intersections[0].object;\n        this._plane.setFromNormalAndCoplanarPoint(this._camera.getWorldDirection(this._plane.normal), this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld));\n        if (this._raycaster.ray.intersectPlane(this._plane, this._intersection) && this._selected.parent) {\n          this._inverseMatrix.copy(this._selected.parent.matrixWorld).invert();\n          this._offset.copy(this._intersection).sub(this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld));\n        }\n        this._domElement.style.cursor = \"move\";\n        this.dispatchEvent({\n          type: \"dragstart\",\n          object: this._selected\n        });\n      }\n    });\n    __publicField(this, \"onTouchEnd\", event => {\n      event.preventDefault();\n      if (this._selected) {\n        this.dispatchEvent({\n          type: \"dragend\",\n          object: this._selected\n        });\n        this._selected = null;\n      }\n      this._domElement.style.cursor = \"auto\";\n    });\n    this._objects = _objects;\n    this._camera = _camera;\n    this._domElement = _domElement;\n    this.activate();\n  }\n}\nexport { DragControls };", "map": {"version": 3, "names": ["DragControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_objects", "_camera", "_domElement", "__publicField", "Plane", "Raycaster", "Vector2", "Vector3", "Matrix4", "addEventListener", "onPointerMove", "onPointerDown", "onPointerCancel", "onTouchMove", "onTouchStart", "onTouchEnd", "removeEventListener", "style", "cursor", "deactivate", "_raycaster", "event", "rect", "getBoundingClientRect", "_mouse", "x", "clientX", "left", "width", "y", "clientY", "top", "height", "setFromCamera", "_selected", "enabled", "ray", "intersectPlane", "_plane", "_intersection", "position", "copy", "sub", "_offset", "applyMatrix4", "_inverseMatrix", "dispatchEvent", "type", "object", "_intersections", "length", "intersectObjects", "setFromNormalAndCoplanarPoint", "getWorldDirection", "normal", "_worldPosition", "setFromMatrixPosition", "matrixWorld", "_hovered", "transformGroup", "parent", "invert", "pointerType", "onMouseMove", "onMouseDown", "onMouseCancel", "preventDefault", "newEvent", "changedTouches", "activate"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/controls/DragControls.ts"], "sourcesContent": ["import { <PERSON>, Intersection, Matrix4, Object3<PERSON>, Plane, Raycaster, Vector2, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nexport interface DragControlsEventMap {\n  /**\n   * Fires when the pointer is moved onto a 3D object, or onto one of its children.\n   */\n  hoveron: { object: Object3D };\n\n  /**\n   * Fires when the pointer is moved out of a 3D object.\n   */\n  hoveroff: { object: Object3D };\n\n  /**\n   * Fires when the user starts to drag a 3D object.\n   */\n  dragstart: { object: Object3D };\n\n  /**\n   * Fires when the user drags a 3D object.\n   */\n  drag: { object: Object3D };\n\n  /**\n   * Fires when the user has finished dragging a 3D object.\n   */\n  dragend: { object: Object3D };\n}\n\nclass DragControls extends EventDispatcher<DragControlsEventMap> {\n  public enabled = true\n  public transformGroup = false\n\n  private _objects: Object3D[]\n  private _camera: Camera\n  private _domElement: HTMLElement\n\n  private _plane = new Plane()\n  private _raycaster = new Raycaster()\n\n  private _mouse = new Vector2()\n  private _offset = new Vector3()\n  private _intersection = new Vector3()\n  private _worldPosition = new Vector3()\n  private _inverseMatrix = new Matrix4()\n  private _intersections: Intersection[] = []\n  private _selected: Object3D | null = null\n  private _hovered: Object3D | null = null\n\n  constructor(_objects: Object3D[], _camera: Camera, _domElement: HTMLElement) {\n    super()\n\n    this._objects = _objects\n    this._camera = _camera\n    this._domElement = _domElement\n\n    this.activate()\n  }\n\n  public activate = (): void => {\n    this._domElement.addEventListener('pointermove', this.onPointerMove)\n    this._domElement.addEventListener('pointerdown', this.onPointerDown)\n    this._domElement.addEventListener('pointerup', this.onPointerCancel)\n    this._domElement.addEventListener('pointerleave', this.onPointerCancel)\n    this._domElement.addEventListener('touchmove', this.onTouchMove)\n    this._domElement.addEventListener('touchstart', this.onTouchStart)\n    this._domElement.addEventListener('touchend', this.onTouchEnd)\n  }\n\n  public deactivate = (): void => {\n    this._domElement.removeEventListener('pointermove', this.onPointerMove)\n    this._domElement.removeEventListener('pointerdown', this.onPointerDown)\n    this._domElement.removeEventListener('pointerup', this.onPointerCancel)\n    this._domElement.removeEventListener('pointerleave', this.onPointerCancel)\n    this._domElement.removeEventListener('touchmove', this.onTouchMove)\n    this._domElement.removeEventListener('touchstart', this.onTouchStart)\n    this._domElement.removeEventListener('touchend', this.onTouchEnd)\n\n    this._domElement.style.cursor = ''\n  }\n\n  // TODO: confirm if this can be removed?\n  public dispose = (): void => this.deactivate()\n\n  public getObjects = (): Object3D[] => this._objects\n\n  public getRaycaster = (): Raycaster => this._raycaster\n\n  private onMouseMove = (event: MouseEvent): void => {\n    const rect = this._domElement.getBoundingClientRect()\n\n    this._mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n    this._mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n    this._raycaster.setFromCamera(this._mouse, this._camera)\n\n    if (this._selected && this.enabled) {\n      if (this._raycaster.ray.intersectPlane(this._plane, this._intersection)) {\n        this._selected.position.copy(this._intersection.sub(this._offset).applyMatrix4(this._inverseMatrix))\n      }\n\n      // @ts-ignore\n      this.dispatchEvent({ type: 'drag', object: this._selected })\n\n      return\n    }\n\n    this._intersections.length = 0\n\n    this._raycaster.setFromCamera(this._mouse, this._camera)\n    this._raycaster.intersectObjects(this._objects, true, this._intersections)\n\n    if (this._intersections.length > 0) {\n      const object = this._intersections[0].object\n\n      this._plane.setFromNormalAndCoplanarPoint(\n        this._camera.getWorldDirection(this._plane.normal),\n        this._worldPosition.setFromMatrixPosition(object.matrixWorld),\n      )\n\n      if (this._hovered !== object) {\n        // @ts-ignore\n        this.dispatchEvent({ type: 'hoveron', object })\n\n        this._domElement.style.cursor = 'pointer'\n        this._hovered = object\n      }\n    } else {\n      if (this._hovered !== null) {\n        // @ts-ignore\n        this.dispatchEvent({ type: 'hoveroff', object: this._hovered })\n\n        this._domElement.style.cursor = 'auto'\n        this._hovered = null\n      }\n    }\n  }\n\n  private onMouseDown = (): void => {\n    this._intersections.length = 0\n\n    this._raycaster.setFromCamera(this._mouse, this._camera)\n    this._raycaster.intersectObjects(this._objects, true, this._intersections)\n\n    if (this._intersections.length > 0) {\n      this._selected = this.transformGroup === true ? this._objects[0] : this._intersections[0].object\n\n      if (this._raycaster.ray.intersectPlane(this._plane, this._intersection) && this._selected.parent) {\n        this._inverseMatrix.copy(this._selected.parent.matrixWorld).invert()\n        this._offset.copy(this._intersection).sub(this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld))\n      }\n\n      this._domElement.style.cursor = 'move'\n\n      // @ts-ignore\n      this.dispatchEvent({ type: 'dragstart', object: this._selected })\n    }\n  }\n\n  private onMouseCancel = (): void => {\n    if (this._selected) {\n      // @ts-ignore\n      this.dispatchEvent({ type: 'dragend', object: this._selected })\n\n      this._selected = null\n    }\n\n    this._domElement.style.cursor = this._hovered ? 'pointer' : 'auto'\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseMove(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseDown()\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerCancel = (event: PointerEvent): void => {\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseCancel()\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onTouchMove = (event: TouchEvent): void => {\n    event.preventDefault()\n    const newEvent = event.changedTouches[0]\n\n    const rect = this._domElement.getBoundingClientRect()\n\n    this._mouse.x = ((newEvent.clientX - rect.left) / rect.width) * 2 - 1\n    this._mouse.y = -((newEvent.clientY - rect.top) / rect.height) * 2 + 1\n\n    this._raycaster.setFromCamera(this._mouse, this._camera)\n\n    if (this._selected && this.enabled) {\n      if (this._raycaster.ray.intersectPlane(this._plane, this._intersection)) {\n        this._selected.position.copy(this._intersection.sub(this._offset).applyMatrix4(this._inverseMatrix))\n      }\n\n      // @ts-ignore\n      this.dispatchEvent({ type: 'drag', object: this._selected })\n\n      return\n    }\n  }\n\n  private onTouchStart = (event: TouchEvent): void => {\n    event.preventDefault()\n    const newEvent = event.changedTouches[0]\n\n    const rect = this._domElement.getBoundingClientRect()\n\n    this._mouse.x = ((newEvent.clientX - rect.left) / rect.width) * 2 - 1\n    this._mouse.y = -((newEvent.clientY - rect.top) / rect.height) * 2 + 1\n\n    this._intersections.length = 0\n\n    this._raycaster.setFromCamera(this._mouse, this._camera)\n    this._raycaster.intersectObjects(this._objects, true, this._intersections)\n\n    if (this._intersections.length > 0) {\n      this._selected = this.transformGroup === true ? this._objects[0] : this._intersections[0].object\n\n      this._plane.setFromNormalAndCoplanarPoint(\n        this._camera.getWorldDirection(this._plane.normal),\n        this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld),\n      )\n\n      if (this._raycaster.ray.intersectPlane(this._plane, this._intersection) && this._selected.parent) {\n        this._inverseMatrix.copy(this._selected.parent.matrixWorld).invert()\n        this._offset.copy(this._intersection).sub(this._worldPosition.setFromMatrixPosition(this._selected.matrixWorld))\n      }\n\n      this._domElement.style.cursor = 'move'\n\n      // @ts-ignore\n      this.dispatchEvent({ type: 'dragstart', object: this._selected })\n    }\n  }\n\n  private onTouchEnd = (event: TouchEvent): void => {\n    event.preventDefault()\n\n    if (this._selected) {\n      // @ts-ignore\n      this.dispatchEvent({ type: 'dragend', object: this._selected })\n\n      this._selected = null\n    }\n\n    this._domElement.style.cursor = 'auto'\n  }\n}\n\nexport { DragControls }\n"], "mappings": ";;;;;;;;;;;;;AA8BA,MAAMA,YAAA,SAAqBC,eAAA,CAAsC;EAoB/DC,YAAYC,QAAA,EAAsBC,OAAA,EAAiBC,WAAA,EAA0B;IACrE;IApBDC,aAAA,kBAAU;IACVA,aAAA,yBAAiB;IAEhBA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA,iBAAS,IAAIC,KAAA;IACbD,aAAA,qBAAa,IAAIE,SAAA;IAEjBF,aAAA,iBAAS,IAAIG,OAAA;IACbH,aAAA,kBAAU,IAAII,OAAA;IACdJ,aAAA,wBAAgB,IAAII,OAAA;IACpBJ,aAAA,yBAAiB,IAAII,OAAA;IACrBJ,aAAA,yBAAiB,IAAIK,OAAA;IACrBL,aAAA,yBAAiC;IACjCA,aAAA,oBAA6B;IAC7BA,aAAA,mBAA4B;IAY7BA,aAAA,mBAAW,MAAY;MAC5B,KAAKD,WAAA,CAAYO,gBAAA,CAAiB,eAAe,KAAKC,aAAa;MACnE,KAAKR,WAAA,CAAYO,gBAAA,CAAiB,eAAe,KAAKE,aAAa;MACnE,KAAKT,WAAA,CAAYO,gBAAA,CAAiB,aAAa,KAAKG,eAAe;MACnE,KAAKV,WAAA,CAAYO,gBAAA,CAAiB,gBAAgB,KAAKG,eAAe;MACtE,KAAKV,WAAA,CAAYO,gBAAA,CAAiB,aAAa,KAAKI,WAAW;MAC/D,KAAKX,WAAA,CAAYO,gBAAA,CAAiB,cAAc,KAAKK,YAAY;MACjE,KAAKZ,WAAA,CAAYO,gBAAA,CAAiB,YAAY,KAAKM,UAAU;IAAA;IAGxDZ,aAAA,qBAAa,MAAY;MAC9B,KAAKD,WAAA,CAAYc,mBAAA,CAAoB,eAAe,KAAKN,aAAa;MACtE,KAAKR,WAAA,CAAYc,mBAAA,CAAoB,eAAe,KAAKL,aAAa;MACtE,KAAKT,WAAA,CAAYc,mBAAA,CAAoB,aAAa,KAAKJ,eAAe;MACtE,KAAKV,WAAA,CAAYc,mBAAA,CAAoB,gBAAgB,KAAKJ,eAAe;MACzE,KAAKV,WAAA,CAAYc,mBAAA,CAAoB,aAAa,KAAKH,WAAW;MAClE,KAAKX,WAAA,CAAYc,mBAAA,CAAoB,cAAc,KAAKF,YAAY;MACpE,KAAKZ,WAAA,CAAYc,mBAAA,CAAoB,YAAY,KAAKD,UAAU;MAE3D,KAAAb,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;IAAA;IAI3B;IAAAf,aAAA,kBAAU,MAAY,KAAKgB,UAAA;IAE3BhB,aAAA,qBAAa,MAAkB,KAAKH,QAAA;IAEpCG,aAAA,uBAAe,MAAiB,KAAKiB,UAAA;IAEpCjB,aAAA,sBAAekB,KAAA,IAA4B;MAC3C,MAAAC,IAAA,GAAO,KAAKpB,WAAA,CAAYqB,qBAAA,CAAsB;MAE/C,KAAAC,MAAA,CAAOC,CAAA,IAAMJ,KAAA,CAAMK,OAAA,GAAUJ,IAAA,CAAKK,IAAA,IAAQL,IAAA,CAAKM,KAAA,GAAS,IAAI;MAC5D,KAAAJ,MAAA,CAAOK,CAAA,GAAI,GAAGR,KAAA,CAAMS,OAAA,GAAUR,IAAA,CAAKS,GAAA,IAAOT,IAAA,CAAKU,MAAA,IAAU,IAAI;MAElE,KAAKZ,UAAA,CAAWa,aAAA,CAAc,KAAKT,MAAA,EAAQ,KAAKvB,OAAO;MAEnD,SAAKiC,SAAA,IAAa,KAAKC,OAAA,EAAS;QAC9B,SAAKf,UAAA,CAAWgB,GAAA,CAAIC,cAAA,CAAe,KAAKC,MAAA,EAAQ,KAAKC,aAAa,GAAG;UACvE,KAAKL,SAAA,CAAUM,QAAA,CAASC,IAAA,CAAK,KAAKF,aAAA,CAAcG,GAAA,CAAI,KAAKC,OAAO,EAAEC,YAAA,CAAa,KAAKC,cAAc,CAAC;QACrG;QAGA,KAAKC,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAQC,MAAA,EAAQ,KAAKd;QAAA,CAAW;QAE3D;MACF;MAEA,KAAKe,cAAA,CAAeC,MAAA,GAAS;MAE7B,KAAK9B,UAAA,CAAWa,aAAA,CAAc,KAAKT,MAAA,EAAQ,KAAKvB,OAAO;MACvD,KAAKmB,UAAA,CAAW+B,gBAAA,CAAiB,KAAKnD,QAAA,EAAU,MAAM,KAAKiD,cAAc;MAErE,SAAKA,cAAA,CAAeC,MAAA,GAAS,GAAG;QAClC,MAAMF,MAAA,GAAS,KAAKC,cAAA,CAAe,CAAC,EAAED,MAAA;QAEtC,KAAKV,MAAA,CAAOc,6BAAA,CACV,KAAKnD,OAAA,CAAQoD,iBAAA,CAAkB,KAAKf,MAAA,CAAOgB,MAAM,GACjD,KAAKC,cAAA,CAAeC,qBAAA,CAAsBR,MAAA,CAAOS,WAAW;QAG1D,SAAKC,QAAA,KAAaV,MAAA,EAAQ;UAE5B,KAAKF,aAAA,CAAc;YAAEC,IAAA,EAAM;YAAWC;UAAQ;UAEzC,KAAA9C,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;UAChC,KAAKwC,QAAA,GAAWV,MAAA;QAClB;MAAA,OACK;QACD,SAAKU,QAAA,KAAa,MAAM;UAE1B,KAAKZ,aAAA,CAAc;YAAEC,IAAA,EAAM;YAAYC,MAAA,EAAQ,KAAKU;UAAA,CAAU;UAEzD,KAAAxD,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;UAChC,KAAKwC,QAAA,GAAW;QAClB;MACF;IAAA;IAGMvD,aAAA,sBAAc,MAAY;MAChC,KAAK8C,cAAA,CAAeC,MAAA,GAAS;MAE7B,KAAK9B,UAAA,CAAWa,aAAA,CAAc,KAAKT,MAAA,EAAQ,KAAKvB,OAAO;MACvD,KAAKmB,UAAA,CAAW+B,gBAAA,CAAiB,KAAKnD,QAAA,EAAU,MAAM,KAAKiD,cAAc;MAErE,SAAKA,cAAA,CAAeC,MAAA,GAAS,GAAG;QAC7B,KAAAhB,SAAA,GAAY,KAAKyB,cAAA,KAAmB,OAAO,KAAK3D,QAAA,CAAS,CAAC,IAAI,KAAKiD,cAAA,CAAe,CAAC,EAAED,MAAA;QAEtF,SAAK5B,UAAA,CAAWgB,GAAA,CAAIC,cAAA,CAAe,KAAKC,MAAA,EAAQ,KAAKC,aAAa,KAAK,KAAKL,SAAA,CAAU0B,MAAA,EAAQ;UAChG,KAAKf,cAAA,CAAeJ,IAAA,CAAK,KAAKP,SAAA,CAAU0B,MAAA,CAAOH,WAAW,EAAEI,MAAA;UAC5D,KAAKlB,OAAA,CAAQF,IAAA,CAAK,KAAKF,aAAa,EAAEG,GAAA,CAAI,KAAKa,cAAA,CAAeC,qBAAA,CAAsB,KAAKtB,SAAA,CAAUuB,WAAW,CAAC;QACjH;QAEK,KAAAvD,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;QAGhC,KAAK4B,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAaC,MAAA,EAAQ,KAAKd;QAAA,CAAW;MAClE;IAAA;IAGM/B,aAAA,wBAAgB,MAAY;MAClC,IAAI,KAAK+B,SAAA,EAAW;QAElB,KAAKY,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAWC,MAAA,EAAQ,KAAKd;QAAA,CAAW;QAE9D,KAAKA,SAAA,GAAY;MACnB;MAEA,KAAKhC,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS,KAAKwC,QAAA,GAAW,YAAY;IAAA;IAGtDvD,aAAA,wBAAiBkB,KAAA,IAA8B;MACrD,QAAQA,KAAA,CAAMyC,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKC,WAAA,CAAY1C,KAAK;UACtB;MAGJ;IAAA;IAGMlB,aAAA,wBAAiBkB,KAAA,IAA8B;MACrD,QAAQA,KAAA,CAAMyC,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKE,WAAA,CAAY;UACjB;MAGJ;IAAA;IAGM7D,aAAA,0BAAmBkB,KAAA,IAA8B;MACvD,QAAQA,KAAA,CAAMyC,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKG,aAAA,CAAc;UACnB;MAGJ;IAAA;IAGM9D,aAAA,sBAAekB,KAAA,IAA4B;MACjDA,KAAA,CAAM6C,cAAA,CAAe;MACf,MAAAC,QAAA,GAAW9C,KAAA,CAAM+C,cAAA,CAAe,CAAC;MAEjC,MAAA9C,IAAA,GAAO,KAAKpB,WAAA,CAAYqB,qBAAA,CAAsB;MAE/C,KAAAC,MAAA,CAAOC,CAAA,IAAM0C,QAAA,CAASzC,OAAA,GAAUJ,IAAA,CAAKK,IAAA,IAAQL,IAAA,CAAKM,KAAA,GAAS,IAAI;MAC/D,KAAAJ,MAAA,CAAOK,CAAA,GAAI,GAAGsC,QAAA,CAASrC,OAAA,GAAUR,IAAA,CAAKS,GAAA,IAAOT,IAAA,CAAKU,MAAA,IAAU,IAAI;MAErE,KAAKZ,UAAA,CAAWa,aAAA,CAAc,KAAKT,MAAA,EAAQ,KAAKvB,OAAO;MAEnD,SAAKiC,SAAA,IAAa,KAAKC,OAAA,EAAS;QAC9B,SAAKf,UAAA,CAAWgB,GAAA,CAAIC,cAAA,CAAe,KAAKC,MAAA,EAAQ,KAAKC,aAAa,GAAG;UACvE,KAAKL,SAAA,CAAUM,QAAA,CAASC,IAAA,CAAK,KAAKF,aAAA,CAAcG,GAAA,CAAI,KAAKC,OAAO,EAAEC,YAAA,CAAa,KAAKC,cAAc,CAAC;QACrG;QAGA,KAAKC,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAQC,MAAA,EAAQ,KAAKd;QAAA,CAAW;QAE3D;MACF;IAAA;IAGM/B,aAAA,uBAAgBkB,KAAA,IAA4B;MAClDA,KAAA,CAAM6C,cAAA,CAAe;MACf,MAAAC,QAAA,GAAW9C,KAAA,CAAM+C,cAAA,CAAe,CAAC;MAEjC,MAAA9C,IAAA,GAAO,KAAKpB,WAAA,CAAYqB,qBAAA,CAAsB;MAE/C,KAAAC,MAAA,CAAOC,CAAA,IAAM0C,QAAA,CAASzC,OAAA,GAAUJ,IAAA,CAAKK,IAAA,IAAQL,IAAA,CAAKM,KAAA,GAAS,IAAI;MAC/D,KAAAJ,MAAA,CAAOK,CAAA,GAAI,GAAGsC,QAAA,CAASrC,OAAA,GAAUR,IAAA,CAAKS,GAAA,IAAOT,IAAA,CAAKU,MAAA,IAAU,IAAI;MAErE,KAAKiB,cAAA,CAAeC,MAAA,GAAS;MAE7B,KAAK9B,UAAA,CAAWa,aAAA,CAAc,KAAKT,MAAA,EAAQ,KAAKvB,OAAO;MACvD,KAAKmB,UAAA,CAAW+B,gBAAA,CAAiB,KAAKnD,QAAA,EAAU,MAAM,KAAKiD,cAAc;MAErE,SAAKA,cAAA,CAAeC,MAAA,GAAS,GAAG;QAC7B,KAAAhB,SAAA,GAAY,KAAKyB,cAAA,KAAmB,OAAO,KAAK3D,QAAA,CAAS,CAAC,IAAI,KAAKiD,cAAA,CAAe,CAAC,EAAED,MAAA;QAE1F,KAAKV,MAAA,CAAOc,6BAAA,CACV,KAAKnD,OAAA,CAAQoD,iBAAA,CAAkB,KAAKf,MAAA,CAAOgB,MAAM,GACjD,KAAKC,cAAA,CAAeC,qBAAA,CAAsB,KAAKtB,SAAA,CAAUuB,WAAW;QAGlE,SAAKrC,UAAA,CAAWgB,GAAA,CAAIC,cAAA,CAAe,KAAKC,MAAA,EAAQ,KAAKC,aAAa,KAAK,KAAKL,SAAA,CAAU0B,MAAA,EAAQ;UAChG,KAAKf,cAAA,CAAeJ,IAAA,CAAK,KAAKP,SAAA,CAAU0B,MAAA,CAAOH,WAAW,EAAEI,MAAA;UAC5D,KAAKlB,OAAA,CAAQF,IAAA,CAAK,KAAKF,aAAa,EAAEG,GAAA,CAAI,KAAKa,cAAA,CAAeC,qBAAA,CAAsB,KAAKtB,SAAA,CAAUuB,WAAW,CAAC;QACjH;QAEK,KAAAvD,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;QAGhC,KAAK4B,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAaC,MAAA,EAAQ,KAAKd;QAAA,CAAW;MAClE;IAAA;IAGM/B,aAAA,qBAAckB,KAAA,IAA4B;MAChDA,KAAA,CAAM6C,cAAA,CAAe;MAErB,IAAI,KAAKhC,SAAA,EAAW;QAElB,KAAKY,aAAA,CAAc;UAAEC,IAAA,EAAM;UAAWC,MAAA,EAAQ,KAAKd;QAAA,CAAW;QAE9D,KAAKA,SAAA,GAAY;MACnB;MAEK,KAAAhC,WAAA,CAAYe,KAAA,CAAMC,MAAA,GAAS;IAAA;IA1NhC,KAAKlB,QAAA,GAAWA,QAAA;IAChB,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKC,WAAA,GAAcA,WAAA;IAEnB,KAAKmE,QAAA,CAAS;EAChB;AAuNF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}