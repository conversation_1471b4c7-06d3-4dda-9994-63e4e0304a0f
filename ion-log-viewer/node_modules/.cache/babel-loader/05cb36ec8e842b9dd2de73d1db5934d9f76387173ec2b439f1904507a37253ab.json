{"ast": null, "code": "import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\n\n// Simple value handlers for JSON data\nexport class IonValueHandler {\n  static handleValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n    if (typeof value === 'object') {\n      const result = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n    return value;\n  }\n  static isHumanReadable(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data) {\n  try {\n    let parsedData;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    const processedData = IonValueHandler.handleValue(parsedData);\n    console.log('Processed data:', processedData);\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo = extractSessionInfo(processedData);\n\n    // Extract robot information\n    const robotInfo = extractRobotInfo(processedData);\n\n    // Extract topics\n    const topics = extractTopics(processedData);\n\n    // Calculate time bounds\n    const {\n      startTime,\n      endTime,\n      totalDuration\n    } = calculateTimeBounds(topics);\n    console.log('Extraction complete:', {\n      sessionInfo,\n      robotInfo,\n      topicCount: topics.length\n    });\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\nfunction extractSessionInfo(data) {\n  const sessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n  return sessionInfo;\n}\nfunction extractRobotInfo(data) {\n  const robotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n  return robotInfo;\n}\nfunction extractTopics(data) {\n  const topics = [];\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n    const messages = [];\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 || messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n  return topics;\n}\nfunction calculateTimeBounds(topics) {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n  const totalDuration = endTime - startTime;\n  return {\n    startTime,\n    endTime,\n    totalDuration\n  };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages, targetTime) {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages, targetTime) {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}", "map": {"version": 3, "names": ["ion", "IonValueHandler", "handleValue", "value", "undefined", "Array", "isArray", "map", "item", "result", "key", "val", "Object", "entries", "isHumanReadable", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "every", "values", "parseIonLog", "data", "parsedData", "console", "log", "load", "ionError", "textData", "TextDecoder", "decode", "ionTextError", "JSON", "parse", "jsonError", "Error", "message", "processedData", "sessionInfo", "extractSessionInfo", "robotInfo", "extractRobotInfo", "topics", "extractTopics", "startTime", "endTime", "totalDuration", "calculateTimeBounds", "topicCount", "length", "metadata", "error", "session", "assign", "duration", "recordingDate", "version", "robot", "robotModel", "model", "robotName", "name", "robotVersion", "botModel", "topicData", "topic", "messages", "msgData", "push", "timestamp", "publishTime", "content", "sort", "a", "b", "msg", "frequency", "Infinity", "findMessageAtTime", "targetTime", "left", "right", "closest", "mid", "Math", "floor", "abs", "getMessagesUpToTime", "filter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts"], "sourcesContent": ["import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\nexport interface IonMessage {\n  timestamp: number;\n  publishTime?: number;\n  content: any;\n}\n\nexport interface IonTopic {\n  name: string;\n  type: string;\n  frequency?: number;\n  messages: IonMessage[];\n  isHumanReadable: boolean;\n}\n\nexport interface IonSessionInfo {\n  startTime?: number;\n  endTime?: number;\n  duration?: number;\n  recordingDate?: string;\n  version?: string;\n  [key: string]: any;\n}\n\nexport interface IonRobotInfo {\n  model?: string;\n  name?: string;\n  version?: string;\n  botModel?: Uint8Array; // 3D model data\n  [key: string]: any;\n}\n\nexport interface IonLogData {\n  sessionInfo: IonSessionInfo;\n  robotInfo: IonRobotInfo;\n  topics: IonTopic[];\n  metadata: any;\n  totalDuration: number;\n  startTime: number;\n  endTime: number;\n}\n\n// Simple value handlers for JSON data\nexport class IonValueHandler {\n  static handleValue(value: any): any {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n\n    if (typeof value === 'object') {\n      const result: any = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n\n    return value;\n  }\n\n  static isHumanReadable(value: any): boolean {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data: Uint8Array): Promise<IonLogData> {\n  try {\n    let parsedData: any;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    const processedData = IonValueHandler.handleValue(parsedData);\n    console.log('Processed data:', processedData);\n\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo: IonSessionInfo = extractSessionInfo(processedData);\n\n    // Extract robot information\n    const robotInfo: IonRobotInfo = extractRobotInfo(processedData);\n\n    // Extract topics\n    const topics: IonTopic[] = extractTopics(processedData);\n\n    // Calculate time bounds\n    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);\n\n    console.log('Extraction complete:', { sessionInfo, robotInfo, topicCount: topics.length });\n\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\nfunction extractSessionInfo(data: any): IonSessionInfo {\n  const sessionInfo: IonSessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  return sessionInfo;\n}\n\nfunction extractRobotInfo(data: any): IonRobotInfo {\n  const robotInfo: IonRobotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n\n  return robotInfo;\n}\n\nfunction extractTopics(data: any): IonTopic[] {\n  const topics: IonTopic[] = [];\n\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n\n    const messages: IonMessage[] = [];\n\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 ||\n      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n\n  return topics;\n}\n\nfunction calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n\n  const totalDuration = endTime - startTime;\n\n  return { startTime, endTime, totalDuration };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;;AAE7B;;AA0CA;AACA,OAAO,MAAMC,eAAe,CAAC;EAC3B,OAAOC,WAAWA,CAACC,KAAU,EAAO;IAClC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;IAEA,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxF,OAAOA,KAAK;IACd;IAEA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACI,GAAG,CAACC,IAAI,IAAI,IAAI,CAACN,WAAW,CAACM,IAAI,CAAC,CAAC;IAClD;IAEA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMM,MAAW,GAAG,CAAC,CAAC;MACtB,KAAK,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACV,KAAK,CAAC,EAAE;QAC9CM,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,CAACR,WAAW,CAACS,GAAG,CAAC;MACrC;MACA,OAAOF,MAAM;IACf;IAEA,OAAON,KAAK;EACd;EAEA,OAAOW,eAAeA,CAACX,KAAU,EAAW;IAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE,OAAO,IAAI;IACtD,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErG;IACA,IAAIA,KAAK,YAAYY,UAAU,IAAIZ,KAAK,YAAYa,WAAW,EAAE,OAAO,KAAK;IAC7E,IAAI,OAAOb,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACc,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;IAExE,IAAIZ,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACe,KAAK,CAACV,IAAI,IAAI,IAAI,CAACM,eAAe,CAACN,IAAI,CAAC,CAAC;IACxD;IAEA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOS,MAAM,CAACO,MAAM,CAAChB,KAAK,CAAC,CAACe,KAAK,CAACP,GAAG,IAAI,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC,CAAC;IACrE;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,eAAeS,WAAWA,CAACC,IAAgB,EAAuB;EACvE,IAAI;IACF,IAAIC,UAAe;;IAEnB;IACA,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DF,UAAU,GAAGtB,GAAG,CAACyB,IAAI,CAACJ,IAAI,CAAC;MAC3BE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,OAAOE,QAAQ,EAAE;MACjBH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEE,QAAQ,CAAC;;MAEzE;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC;QAC/CC,UAAU,GAAGtB,GAAG,CAACyB,IAAI,CAACE,QAAQ,CAAC;QAC/BJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAChD,CAAC,CAAC,OAAOM,YAAY,EAAE;QACrBP,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEM,YAAY,CAAC;;QAEpE;QACA,IAAI;UACF,MAAMH,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC;UAC/CC,UAAU,GAAGS,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;UACjCJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,CAAC,OAAOS,SAAS,EAAE;UAClB,MAAM,IAAIC,KAAK,CAAC,mFAAmFD,SAAS,YAAYC,KAAK,GAAGD,SAAS,CAACE,OAAO,GAAG,eAAe,EAAE,CAAC;QACxK;MACF;IACF;IAEA,IAAI,CAACb,UAAU,EAAE;MACf,MAAM,IAAIY,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEAX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,UAAU,CAAC;;IAEjD;IACA,MAAMc,aAAa,GAAGnC,eAAe,CAACC,WAAW,CAACoB,UAAU,CAAC;IAC7DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEY,aAAa,CAAC;IAE7C,IAAI,CAACA,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACvD,MAAM,IAAIF,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACA,MAAMG,WAA2B,GAAGC,kBAAkB,CAACF,aAAa,CAAC;;IAErE;IACA,MAAMG,SAAuB,GAAGC,gBAAgB,CAACJ,aAAa,CAAC;;IAE/D;IACA,MAAMK,MAAkB,GAAGC,aAAa,CAACN,aAAa,CAAC;;IAEvD;IACA,MAAM;MAAEO,SAAS;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGC,mBAAmB,CAACL,MAAM,CAAC;IAEzElB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAEa,WAAW;MAAEE,SAAS;MAAEQ,UAAU,EAAEN,MAAM,CAACO;IAAO,CAAC,CAAC;IAE1F,OAAO;MACLX,WAAW;MACXE,SAAS;MACTE,MAAM;MACNQ,QAAQ,EAAEb,aAAa;MACvBS,aAAa;MACbF,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd3B,OAAO,CAAC2B,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACtC,MAAM,IAAIhB,KAAK,CAAC,wBAAwBgB,KAAK,YAAYhB,KAAK,GAAGgB,KAAK,CAACf,OAAO,GAAG,eAAe,EAAE,CAAC;EACrG;AACF;AAEA,SAASG,kBAAkBA,CAACjB,IAAS,EAAkB;EACrD,MAAMgB,WAA2B,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAIhB,IAAI,CAAC8B,OAAO,EAAE;IAChBvC,MAAM,CAACwC,MAAM,CAACf,WAAW,EAAEpC,eAAe,CAACC,WAAW,CAACmB,IAAI,CAAC8B,OAAO,CAAC,CAAC;EACvE;EAEA,IAAI9B,IAAI,CAAC4B,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGhD,eAAe,CAACC,WAAW,CAACmB,IAAI,CAAC4B,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACN,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGM,QAAQ,CAACN,SAAS;IAClE,IAAIM,QAAQ,CAACL,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGK,QAAQ,CAACL,OAAO;IAC5D,IAAIK,QAAQ,CAACI,QAAQ,EAAEhB,WAAW,CAACgB,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;IAC/D,IAAIJ,QAAQ,CAACK,aAAa,EAAEjB,WAAW,CAACiB,aAAa,GAAGL,QAAQ,CAACK,aAAa;IAC9E,IAAIL,QAAQ,CAACM,OAAO,EAAElB,WAAW,CAACkB,OAAO,GAAGN,QAAQ,CAACM,OAAO;EAC9D;EAEA,OAAOlB,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAACnB,IAAS,EAAgB;EACjD,MAAMkB,SAAuB,GAAG,CAAC,CAAC;;EAElC;EACA,IAAIlB,IAAI,CAACmC,KAAK,EAAE;IACd5C,MAAM,CAACwC,MAAM,CAACb,SAAS,EAAEtC,eAAe,CAACC,WAAW,CAACmB,IAAI,CAACmC,KAAK,CAAC,CAAC;EACnE;EAEA,IAAInC,IAAI,CAAC4B,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGhD,eAAe,CAACC,WAAW,CAACmB,IAAI,CAAC4B,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACQ,UAAU,EAAElB,SAAS,CAACmB,KAAK,GAAGT,QAAQ,CAACQ,UAAU;IAC9D,IAAIR,QAAQ,CAACU,SAAS,EAAEpB,SAAS,CAACqB,IAAI,GAAGX,QAAQ,CAACU,SAAS;IAC3D,IAAIV,QAAQ,CAACY,YAAY,EAAEtB,SAAS,CAACgB,OAAO,GAAGN,QAAQ,CAACY,YAAY;IACpE,IAAIZ,QAAQ,CAACa,QAAQ,EAAEvB,SAAS,CAACuB,QAAQ,GAAGb,QAAQ,CAACa,QAAQ;EAC/D;EAEA,OAAOvB,SAAS;AAClB;AAEA,SAASG,aAAaA,CAACrB,IAAS,EAAc;EAC5C,MAAMoB,MAAkB,GAAG,EAAE;EAE7B,IAAI,CAACpB,IAAI,CAACoB,MAAM,IAAI,CAACpC,KAAK,CAACC,OAAO,CAACe,IAAI,CAACoB,MAAM,CAAC,EAAE;IAC/C,OAAOA,MAAM;EACf;EAEA,KAAK,MAAMsB,SAAS,IAAI1C,IAAI,CAACoB,MAAM,EAAE;IACnC,MAAMuB,KAAK,GAAG/D,eAAe,CAACC,WAAW,CAAC6D,SAAS,CAAC;IAEpD,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,CAACI,KAAK,CAAC/C,IAAI,EAAE;MAC9B,SAAS,CAAC;IACZ;IAEA,MAAMgD,QAAsB,GAAG,EAAE;IAEjC,IAAID,KAAK,CAACC,QAAQ,IAAI5D,KAAK,CAACC,OAAO,CAAC0D,KAAK,CAACC,QAAQ,CAAC,EAAE;MACnD,KAAK,MAAMC,OAAO,IAAIF,KAAK,CAACC,QAAQ,EAAE;QACpC,MAAM9B,OAAO,GAAGlC,eAAe,CAACC,WAAW,CAACgE,OAAO,CAAC;QACpDD,QAAQ,CAACE,IAAI,CAAC;UACZC,SAAS,EAAEjC,OAAO,CAACiC,SAAS,IAAI,CAAC;UACjCC,WAAW,EAAElC,OAAO,CAACkC,WAAW;UAChCC,OAAO,EAAEnC,OAAO,CAACmC,OAAO,IAAInC;QAC9B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA8B,QAAQ,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,SAAS,GAAGK,CAAC,CAACL,SAAS,CAAC;;IAElD;IACA,MAAMtD,eAAe,GAAGmD,QAAQ,CAACjB,MAAM,KAAK,CAAC,IAC3CiB,QAAQ,CAAC/C,KAAK,CAACwD,GAAG,IAAIzE,eAAe,CAACa,eAAe,CAAC4D,GAAG,CAACJ,OAAO,CAAC,CAAC;IAErE7B,MAAM,CAAC0B,IAAI,CAAC;MACVP,IAAI,EAAEI,KAAK,CAACJ,IAAI;MAChB3C,IAAI,EAAE+C,KAAK,CAAC/C,IAAI;MAChB0D,SAAS,EAAEX,KAAK,CAACW,SAAS;MAC1BV,QAAQ;MACRnD;IACF,CAAC,CAAC;EACJ;EAEA,OAAO2B,MAAM;AACf;AAEA,SAASK,mBAAmBA,CAACL,MAAkB,EAAiE;EAC9G,IAAIE,SAAS,GAAGiC,QAAQ;EACxB,IAAIhC,OAAO,GAAG,CAACgC,QAAQ;EAEvB,KAAK,MAAMZ,KAAK,IAAIvB,MAAM,EAAE;IAC1B,KAAK,MAAMN,OAAO,IAAI6B,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI9B,OAAO,CAACiC,SAAS,GAAGzB,SAAS,EAAE;QACjCA,SAAS,GAAGR,OAAO,CAACiC,SAAS;MAC/B;MACA,IAAIjC,OAAO,CAACiC,SAAS,GAAGxB,OAAO,EAAE;QAC/BA,OAAO,GAAGT,OAAO,CAACiC,SAAS;MAC7B;IACF;EACF;EAEA,IAAIzB,SAAS,KAAKiC,QAAQ,EAAE;IAC1BjC,SAAS,GAAG,CAAC;IACbC,OAAO,GAAG,CAAC;EACb;EAEA,MAAMC,aAAa,GAAGD,OAAO,GAAGD,SAAS;EAEzC,OAAO;IAAEA,SAAS;IAAEC,OAAO;IAAEC;EAAc,CAAC;AAC9C;;AAEA;AACA,OAAO,SAASgC,iBAAiBA,CAACZ,QAAsB,EAAEa,UAAkB,EAAqB;EAC/F,IAAIb,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEtC;EACA,IAAI+B,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAGf,QAAQ,CAACjB,MAAM,GAAG,CAAC;EAC/B,IAAIiC,OAAO,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EAEzB,OAAOc,IAAI,IAAIC,KAAK,EAAE;IACpB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC1C,MAAM7C,OAAO,GAAG8B,QAAQ,CAACiB,GAAG,CAAC;IAE7B,IAAIC,IAAI,CAACE,GAAG,CAAClD,OAAO,CAACiC,SAAS,GAAGU,UAAU,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACJ,OAAO,CAACb,SAAS,GAAGU,UAAU,CAAC,EAAE;MACvFG,OAAO,GAAG9C,OAAO;IACnB;IAEA,IAAIA,OAAO,CAACiC,SAAS,GAAGU,UAAU,EAAE;MAClCC,IAAI,GAAGG,GAAG,GAAG,CAAC;IAChB,CAAC,MAAM,IAAI/C,OAAO,CAACiC,SAAS,GAAGU,UAAU,EAAE;MACzCE,KAAK,GAAGE,GAAG,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,OAAO/C,OAAO;IAChB;EACF;EAEA,OAAO8C,OAAO;AAChB;;AAEA;AACA,OAAO,SAASK,mBAAmBA,CAACrB,QAAsB,EAAEa,UAAkB,EAAgB;EAC5F,OAAOb,QAAQ,CAACsB,MAAM,CAACb,GAAG,IAAIA,GAAG,CAACN,SAAS,IAAIU,UAAU,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}