{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class SharedSymbolTable {\n  constructor(_name, _version, _symbols) {\n    this._name = _name;\n    this._version = _version;\n    this._symbols = _symbols;\n    this._idsByText = new Map();\n    this._numberOfSymbols = this._symbols.length;\n    for (let m = _symbols.length - 1; m >= 0; m--) {\n      this._idsByText.set(_symbols[m], m);\n    }\n  }\n  get numberOfSymbols() {\n    return this._numberOfSymbols;\n  }\n  get name() {\n    return this._name;\n  }\n  get version() {\n    return this._version;\n  }\n  getSymbolText(symbolId) {\n    if (symbolId < 0) {\n      throw new Error(`Index ${symbolId} is out of bounds for the SharedSymbolTable name=${this.name}, version=${this.version}`);\n    }\n    if (symbolId >= this.numberOfSymbols) {\n      return undefined;\n    }\n    return this._symbols[symbolId];\n  }\n  getSymbolId(text) {\n    return this._idsByText.get(text);\n  }\n}", "map": {"version": 3, "names": ["SharedSymbolTable", "constructor", "_name", "_version", "_symbols", "_idsByText", "Map", "_numberOfSymbols", "length", "m", "set", "numberOfSymbols", "name", "version", "getSymbolText", "symbolId", "Error", "undefined", "getSymbolId", "text", "get"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSharedSymbolTable.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class SharedSymbolTable {\n    constructor(_name, _version, _symbols) {\n        this._name = _name;\n        this._version = _version;\n        this._symbols = _symbols;\n        this._idsByText = new Map();\n        this._numberOfSymbols = this._symbols.length;\n        for (let m = _symbols.length - 1; m >= 0; m--) {\n            this._idsByText.set(_symbols[m], m);\n        }\n    }\n    get numberOfSymbols() {\n        return this._numberOfSymbols;\n    }\n    get name() {\n        return this._name;\n    }\n    get version() {\n        return this._version;\n    }\n    getSymbolText(symbolId) {\n        if (symbolId < 0) {\n            throw new Error(`Index ${symbolId} is out of bounds for the SharedSymbolTable name=${this.name}, version=${this.version}`);\n        }\n        if (symbolId >= this.numberOfSymbols) {\n            return undefined;\n        }\n        return this._symbols[symbolId];\n    }\n    getSymbolId(text) {\n        return this._idsByText.get(text);\n    }\n}\n//# sourceMappingURL=IonSharedSymbolTable.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,CAAC;EAC3BC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACnC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACH,QAAQ,CAACI,MAAM;IAC5C,KAAK,IAAIC,CAAC,GAAGL,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAI,CAACJ,UAAU,CAACK,GAAG,CAACN,QAAQ,CAACK,CAAC,CAAC,EAAEA,CAAC,CAAC;IACvC;EACJ;EACA,IAAIE,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACJ,gBAAgB;EAChC;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACV,KAAK;EACrB;EACA,IAAIW,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACV,QAAQ;EACxB;EACAW,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACd,MAAM,IAAIC,KAAK,CAAC,SAASD,QAAQ,oDAAoD,IAAI,CAACH,IAAI,aAAa,IAAI,CAACC,OAAO,EAAE,CAAC;IAC9H;IACA,IAAIE,QAAQ,IAAI,IAAI,CAACJ,eAAe,EAAE;MAClC,OAAOM,SAAS;IACpB;IACA,OAAO,IAAI,CAACb,QAAQ,CAACW,QAAQ,CAAC;EAClC;EACAG,WAAWA,CAACC,IAAI,EAAE;IACd,OAAO,IAAI,CAACd,UAAU,CAACe,GAAG,CAACD,IAAI,CAAC;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}