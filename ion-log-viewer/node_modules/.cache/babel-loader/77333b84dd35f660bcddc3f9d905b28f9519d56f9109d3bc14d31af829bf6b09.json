{"ast": null, "code": "import { dom, IonTypes } from \"../Ion\";\nimport { _hasValue } from \"../util\";\nfunction _newSet(values) {\n  if (_hasValue(values)) {\n    return new Set(values);\n  }\n  return new Set();\n}\nexport class FromJsConstructorBuilder {\n  constructor() {\n    this._primitives = _newSet();\n    this._classesToUnbox = _newSet();\n    this._classes = _newSet();\n  }\n  withPrimitives(...primitives) {\n    this._primitives = _newSet(primitives);\n    return this;\n  }\n  withClasses(...classes) {\n    this._classes = _newSet(classes);\n    return this;\n  }\n  withClassesToUnbox(...classes) {\n    this._classesToUnbox = _newSet(classes);\n    return this;\n  }\n  build() {\n    return new FromJsConstructor(this._primitives, this._classesToUnbox, this._classes);\n  }\n}\nexport class FromJsConstructor {\n  constructor(_primitives, _classesToUnbox, _classes) {\n    this._primitives = _primitives;\n    this._classesToUnbox = _classesToUnbox;\n    this._classes = _classes;\n  }\n  construct(constructor, jsValue, annotations = []) {\n    if (jsValue === null) {\n      return new dom.Null(IonTypes.NULL, annotations);\n    }\n    const jsValueType = typeof jsValue;\n    if (jsValueType === \"object\") {\n      if (this._classesToUnbox.has(jsValue.constructor)) {\n        return new constructor(jsValue.valueOf(), annotations);\n      }\n      if (this._classes.has(jsValue.constructor)) {\n        return new constructor(jsValue, annotations);\n      }\n      throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValue.constructor.name}.`);\n    }\n    if (this._primitives.has(jsValueType)) {\n      return new constructor(jsValue, annotations);\n    }\n    throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValueType}.`);\n  }\n}\n(function (FromJsConstructor) {\n  FromJsConstructor.NONE = new FromJsConstructorBuilder().build();\n})(FromJsConstructor || (FromJsConstructor = {}));\nexport const Primitives = {\n  Boolean: \"boolean\",\n  Number: \"number\",\n  String: \"string\",\n  BigInt: \"bigint\"\n};", "map": {"version": 3, "names": ["dom", "IonTypes", "_hasValue", "_newSet", "values", "Set", "FromJsConstructorBuilder", "constructor", "_primitives", "_classesToUnbox", "_classes", "withPrimitives", "primitives", "withClasses", "classes", "withClassesToUnbox", "build", "FromJsConstructor", "construct", "jsValue", "annotations", "<PERSON><PERSON>", "NULL", "jsValueType", "has", "valueOf", "Error", "name", "NONE", "Primitives", "Boolean", "Number", "String", "BigInt"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/FromJsConstructor.js"], "sourcesContent": ["import { dom, IonTypes } from \"../Ion\";\nimport { _hasValue } from \"../util\";\nfunction _newSet(values) {\n    if (_hasValue(values)) {\n        return new Set(values);\n    }\n    return new Set();\n}\nexport class FromJsConstructorBuilder {\n    constructor() {\n        this._primitives = _newSet();\n        this._classesToUnbox = _newSet();\n        this._classes = _newSet();\n    }\n    withPrimitives(...primitives) {\n        this._primitives = _newSet(primitives);\n        return this;\n    }\n    withClasses(...classes) {\n        this._classes = _newSet(classes);\n        return this;\n    }\n    withClassesToUnbox(...classes) {\n        this._classesToUnbox = _newSet(classes);\n        return this;\n    }\n    build() {\n        return new FromJsConstructor(this._primitives, this._classesToUnbox, this._classes);\n    }\n}\nexport class FromJsConstructor {\n    constructor(_primitives, _classesToUnbox, _classes) {\n        this._primitives = _primitives;\n        this._classesToUnbox = _classesToUnbox;\n        this._classes = _classes;\n    }\n    construct(constructor, jsValue, annotations = []) {\n        if (jsValue === null) {\n            return new dom.Null(IonTypes.NULL, annotations);\n        }\n        const jsValueType = typeof jsValue;\n        if (jsValueType === \"object\") {\n            if (this._classesToUnbox.has(jsValue.constructor)) {\n                return new constructor(jsValue.valueOf(), annotations);\n            }\n            if (this._classes.has(jsValue.constructor)) {\n                return new constructor(jsValue, annotations);\n            }\n            throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValue.constructor.name}.`);\n        }\n        if (this._primitives.has(jsValueType)) {\n            return new constructor(jsValue, annotations);\n        }\n        throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValueType}.`);\n    }\n}\n(function (FromJsConstructor) {\n    FromJsConstructor.NONE = new FromJsConstructorBuilder().build();\n})(FromJsConstructor || (FromJsConstructor = {}));\nexport const Primitives = {\n    Boolean: \"boolean\",\n    Number: \"number\",\n    String: \"string\",\n    BigInt: \"bigint\",\n};\n//# sourceMappingURL=FromJsConstructor.js.map"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,QAAQ,QAAQ;AACtC,SAASC,SAAS,QAAQ,SAAS;AACnC,SAASC,OAAOA,CAACC,MAAM,EAAE;EACrB,IAAIF,SAAS,CAACE,MAAM,CAAC,EAAE;IACnB,OAAO,IAAIC,GAAG,CAACD,MAAM,CAAC;EAC1B;EACA,OAAO,IAAIC,GAAG,CAAC,CAAC;AACpB;AACA,OAAO,MAAMC,wBAAwB,CAAC;EAClCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAGL,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACM,eAAe,GAAGN,OAAO,CAAC,CAAC;IAChC,IAAI,CAACO,QAAQ,GAAGP,OAAO,CAAC,CAAC;EAC7B;EACAQ,cAAcA,CAAC,GAAGC,UAAU,EAAE;IAC1B,IAAI,CAACJ,WAAW,GAAGL,OAAO,CAACS,UAAU,CAAC;IACtC,OAAO,IAAI;EACf;EACAC,WAAWA,CAAC,GAAGC,OAAO,EAAE;IACpB,IAAI,CAACJ,QAAQ,GAAGP,OAAO,CAACW,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACAC,kBAAkBA,CAAC,GAAGD,OAAO,EAAE;IAC3B,IAAI,CAACL,eAAe,GAAGN,OAAO,CAACW,OAAO,CAAC;IACvC,OAAO,IAAI;EACf;EACAE,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,iBAAiB,CAAC,IAAI,CAACT,WAAW,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,QAAQ,CAAC;EACvF;AACJ;AACA,OAAO,MAAMO,iBAAiB,CAAC;EAC3BV,WAAWA,CAACC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,EAAE;IAChD,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAQ,SAASA,CAACX,WAAW,EAAEY,OAAO,EAAEC,WAAW,GAAG,EAAE,EAAE;IAC9C,IAAID,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAInB,GAAG,CAACqB,IAAI,CAACpB,QAAQ,CAACqB,IAAI,EAAEF,WAAW,CAAC;IACnD;IACA,MAAMG,WAAW,GAAG,OAAOJ,OAAO;IAClC,IAAII,WAAW,KAAK,QAAQ,EAAE;MAC1B,IAAI,IAAI,CAACd,eAAe,CAACe,GAAG,CAACL,OAAO,CAACZ,WAAW,CAAC,EAAE;QAC/C,OAAO,IAAIA,WAAW,CAACY,OAAO,CAACM,OAAO,CAAC,CAAC,EAAEL,WAAW,CAAC;MAC1D;MACA,IAAI,IAAI,CAACV,QAAQ,CAACc,GAAG,CAACL,OAAO,CAACZ,WAAW,CAAC,EAAE;QACxC,OAAO,IAAIA,WAAW,CAACY,OAAO,EAAEC,WAAW,CAAC;MAChD;MACA,MAAM,IAAIM,KAAK,CAAC,4BAA4BnB,WAAW,CAACoB,IAAI,WAAWR,OAAO,CAACZ,WAAW,CAACoB,IAAI,GAAG,CAAC;IACvG;IACA,IAAI,IAAI,CAACnB,WAAW,CAACgB,GAAG,CAACD,WAAW,CAAC,EAAE;MACnC,OAAO,IAAIhB,WAAW,CAACY,OAAO,EAAEC,WAAW,CAAC;IAChD;IACA,MAAM,IAAIM,KAAK,CAAC,4BAA4BnB,WAAW,CAACoB,IAAI,WAAWJ,WAAW,GAAG,CAAC;EAC1F;AACJ;AACA,CAAC,UAAUN,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACW,IAAI,GAAG,IAAItB,wBAAwB,CAAC,CAAC,CAACU,KAAK,CAAC,CAAC;AACnE,CAAC,EAAEC,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,OAAO,MAAMY,UAAU,GAAG;EACtBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}