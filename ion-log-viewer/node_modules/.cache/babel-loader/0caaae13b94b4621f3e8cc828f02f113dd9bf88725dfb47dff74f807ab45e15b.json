{"ast": null, "code": "// Define interfaces for the ION log structure\n\n// Simple value handlers for JSON data\nexport class IonValueHandler {\n  static handleValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n    if (typeof value === 'object') {\n      const result = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n    return value;\n  }\n  static isHumanReadable(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data) {\n  try {\n    // For now, try to parse as JSON first, then fall back to ION\n    let parsedData;\n    try {\n      // Try JSON first\n      const textData = new TextDecoder().decode(data);\n      parsedData = JSON.parse(textData);\n    } catch {\n      throw new Error('Unable to parse as JSON format');\n    }\n    if (!parsedData || typeof parsedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo = extractSessionInfo(parsedData);\n\n    // Extract robot information\n    const robotInfo = extractRobotInfo(parsedData);\n\n    // Extract topics\n    const topics = extractTopics(parsedData);\n\n    // Calculate time bounds\n    const {\n      startTime,\n      endTime,\n      totalDuration\n    } = calculateTimeBounds(topics);\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: parsedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\nfunction extractSessionInfo(data) {\n  const sessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n  return sessionInfo;\n}\nfunction extractRobotInfo(data) {\n  const robotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n  return robotInfo;\n}\nfunction extractTopics(data) {\n  const topics = [];\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n    const messages = [];\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 || messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n  return topics;\n}\nfunction calculateTimeBounds(topics) {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n  const totalDuration = endTime - startTime;\n  return {\n    startTime,\n    endTime,\n    totalDuration\n  };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages, targetTime) {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages, targetTime) {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}", "map": {"version": 3, "names": ["IonValueHandler", "handleValue", "value", "undefined", "Array", "isArray", "map", "item", "result", "key", "val", "Object", "entries", "isHumanReadable", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "every", "values", "parseIonLog", "data", "parsedData", "textData", "TextDecoder", "decode", "JSON", "parse", "Error", "sessionInfo", "extractSessionInfo", "robotInfo", "extractRobotInfo", "topics", "extractTopics", "startTime", "endTime", "totalDuration", "calculateTimeBounds", "metadata", "error", "message", "session", "assign", "duration", "recordingDate", "version", "robot", "robotModel", "model", "robotName", "name", "robotVersion", "botModel", "topicData", "topic", "messages", "msgData", "push", "timestamp", "publishTime", "content", "sort", "a", "b", "length", "msg", "frequency", "Infinity", "findMessageAtTime", "targetTime", "left", "right", "closest", "mid", "Math", "floor", "abs", "getMessagesUpToTime", "filter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts"], "sourcesContent": ["import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\nexport interface IonMessage {\n  timestamp: number;\n  publishTime?: number;\n  content: any;\n}\n\nexport interface IonTopic {\n  name: string;\n  type: string;\n  frequency?: number;\n  messages: IonMessage[];\n  isHumanReadable: boolean;\n}\n\nexport interface IonSessionInfo {\n  startTime?: number;\n  endTime?: number;\n  duration?: number;\n  recordingDate?: string;\n  version?: string;\n  [key: string]: any;\n}\n\nexport interface IonRobotInfo {\n  model?: string;\n  name?: string;\n  version?: string;\n  botModel?: Uint8Array; // 3D model data\n  [key: string]: any;\n}\n\nexport interface IonLogData {\n  sessionInfo: IonSessionInfo;\n  robotInfo: IonRobotInfo;\n  topics: IonTopic[];\n  metadata: any;\n  totalDuration: number;\n  startTime: number;\n  endTime: number;\n}\n\n// Simple value handlers for JSON data\nexport class IonValueHandler {\n  static handleValue(value: any): any {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n\n    if (typeof value === 'object') {\n      const result: any = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n\n    return value;\n  }\n\n  static isHumanReadable(value: any): boolean {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data: Uint8Array): Promise<IonLogData> {\n  try {\n    // For now, try to parse as JSON first, then fall back to ION\n    let parsedData: any;\n\n    try {\n      // Try JSON first\n      const textData = new TextDecoder().decode(data);\n      parsedData = JSON.parse(textData);\n    } catch {\n      throw new Error('Unable to parse as JSON format');\n    }\n\n    if (!parsedData || typeof parsedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo: IonSessionInfo = extractSessionInfo(parsedData);\n\n    // Extract robot information\n    const robotInfo: IonRobotInfo = extractRobotInfo(parsedData);\n\n    // Extract topics\n    const topics: IonTopic[] = extractTopics(parsedData);\n\n    // Calculate time bounds\n    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);\n\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: parsedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\nfunction extractSessionInfo(data: any): IonSessionInfo {\n  const sessionInfo: IonSessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  return sessionInfo;\n}\n\nfunction extractRobotInfo(data: any): IonRobotInfo {\n  const robotInfo: IonRobotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n\n  return robotInfo;\n}\n\nfunction extractTopics(data: any): IonTopic[] {\n  const topics: IonTopic[] = [];\n\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n\n    const messages: IonMessage[] = [];\n\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 ||\n      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n\n  return topics;\n}\n\nfunction calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n\n  const totalDuration = endTime - startTime;\n\n  return { startTime, endTime, totalDuration };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}\n"], "mappings": "AAEA;;AA0CA;AACA,OAAO,MAAMA,eAAe,CAAC;EAC3B,OAAOC,WAAWA,CAACC,KAAU,EAAO;IAClC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;IAEA,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxF,OAAOA,KAAK;IACd;IAEA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACI,GAAG,CAACC,IAAI,IAAI,IAAI,CAACN,WAAW,CAACM,IAAI,CAAC,CAAC;IAClD;IAEA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMM,MAAW,GAAG,CAAC,CAAC;MACtB,KAAK,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACV,KAAK,CAAC,EAAE;QAC9CM,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,CAACR,WAAW,CAACS,GAAG,CAAC;MACrC;MACA,OAAOF,MAAM;IACf;IAEA,OAAON,KAAK;EACd;EAEA,OAAOW,eAAeA,CAACX,KAAU,EAAW;IAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE,OAAO,IAAI;IACtD,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErG;IACA,IAAIA,KAAK,YAAYY,UAAU,IAAIZ,KAAK,YAAYa,WAAW,EAAE,OAAO,KAAK;IAC7E,IAAI,OAAOb,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACc,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;IAExE,IAAIZ,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACe,KAAK,CAACV,IAAI,IAAI,IAAI,CAACM,eAAe,CAACN,IAAI,CAAC,CAAC;IACxD;IAEA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOS,MAAM,CAACO,MAAM,CAAChB,KAAK,CAAC,CAACe,KAAK,CAACP,GAAG,IAAI,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC,CAAC;IACrE;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,eAAeS,WAAWA,CAACC,IAAgB,EAAuB;EACvE,IAAI;IACF;IACA,IAAIC,UAAe;IAEnB,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,IAAI,CAAC;MAC/CC,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;IACnC,CAAC,CAAC,MAAM;MACN,MAAM,IAAIK,KAAK,CAAC,gCAAgC,CAAC;IACnD;IAEA,IAAI,CAACN,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjD,MAAM,IAAIM,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACA,MAAMC,WAA2B,GAAGC,kBAAkB,CAACR,UAAU,CAAC;;IAElE;IACA,MAAMS,SAAuB,GAAGC,gBAAgB,CAACV,UAAU,CAAC;;IAE5D;IACA,MAAMW,MAAkB,GAAGC,aAAa,CAACZ,UAAU,CAAC;;IAEpD;IACA,MAAM;MAAEa,SAAS;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGC,mBAAmB,CAACL,MAAM,CAAC;IAEzE,OAAO;MACLJ,WAAW;MACXE,SAAS;MACTE,MAAM;MACNM,QAAQ,EAAEjB,UAAU;MACpBe,aAAa;MACbF,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC,OAAOI,KAAK,EAAE;IACd,MAAM,IAAIZ,KAAK,CAAC,wBAAwBY,KAAK,YAAYZ,KAAK,GAAGY,KAAK,CAACC,OAAO,GAAG,eAAe,EAAE,CAAC;EACrG;AACF;AAEA,SAASX,kBAAkBA,CAACT,IAAS,EAAkB;EACrD,MAAMQ,WAA2B,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAIR,IAAI,CAACqB,OAAO,EAAE;IAChB9B,MAAM,CAAC+B,MAAM,CAACd,WAAW,EAAE5B,eAAe,CAACC,WAAW,CAACmB,IAAI,CAACqB,OAAO,CAAC,CAAC;EACvE;EAEA,IAAIrB,IAAI,CAACkB,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGtC,eAAe,CAACC,WAAW,CAACmB,IAAI,CAACkB,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACJ,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGI,QAAQ,CAACJ,SAAS;IAClE,IAAII,QAAQ,CAACH,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGG,QAAQ,CAACH,OAAO;IAC5D,IAAIG,QAAQ,CAACK,QAAQ,EAAEf,WAAW,CAACe,QAAQ,GAAGL,QAAQ,CAACK,QAAQ;IAC/D,IAAIL,QAAQ,CAACM,aAAa,EAAEhB,WAAW,CAACgB,aAAa,GAAGN,QAAQ,CAACM,aAAa;IAC9E,IAAIN,QAAQ,CAACO,OAAO,EAAEjB,WAAW,CAACiB,OAAO,GAAGP,QAAQ,CAACO,OAAO;EAC9D;EAEA,OAAOjB,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAACX,IAAS,EAAgB;EACjD,MAAMU,SAAuB,GAAG,CAAC,CAAC;;EAElC;EACA,IAAIV,IAAI,CAAC0B,KAAK,EAAE;IACdnC,MAAM,CAAC+B,MAAM,CAACZ,SAAS,EAAE9B,eAAe,CAACC,WAAW,CAACmB,IAAI,CAAC0B,KAAK,CAAC,CAAC;EACnE;EAEA,IAAI1B,IAAI,CAACkB,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGtC,eAAe,CAACC,WAAW,CAACmB,IAAI,CAACkB,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACS,UAAU,EAAEjB,SAAS,CAACkB,KAAK,GAAGV,QAAQ,CAACS,UAAU;IAC9D,IAAIT,QAAQ,CAACW,SAAS,EAAEnB,SAAS,CAACoB,IAAI,GAAGZ,QAAQ,CAACW,SAAS;IAC3D,IAAIX,QAAQ,CAACa,YAAY,EAAErB,SAAS,CAACe,OAAO,GAAGP,QAAQ,CAACa,YAAY;IACpE,IAAIb,QAAQ,CAACc,QAAQ,EAAEtB,SAAS,CAACsB,QAAQ,GAAGd,QAAQ,CAACc,QAAQ;EAC/D;EAEA,OAAOtB,SAAS;AAClB;AAEA,SAASG,aAAaA,CAACb,IAAS,EAAc;EAC5C,MAAMY,MAAkB,GAAG,EAAE;EAE7B,IAAI,CAACZ,IAAI,CAACY,MAAM,IAAI,CAAC5B,KAAK,CAACC,OAAO,CAACe,IAAI,CAACY,MAAM,CAAC,EAAE;IAC/C,OAAOA,MAAM;EACf;EAEA,KAAK,MAAMqB,SAAS,IAAIjC,IAAI,CAACY,MAAM,EAAE;IACnC,MAAMsB,KAAK,GAAGtD,eAAe,CAACC,WAAW,CAACoD,SAAS,CAAC;IAEpD,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,CAACI,KAAK,CAACtC,IAAI,EAAE;MAC9B,SAAS,CAAC;IACZ;IAEA,MAAMuC,QAAsB,GAAG,EAAE;IAEjC,IAAID,KAAK,CAACC,QAAQ,IAAInD,KAAK,CAACC,OAAO,CAACiD,KAAK,CAACC,QAAQ,CAAC,EAAE;MACnD,KAAK,MAAMC,OAAO,IAAIF,KAAK,CAACC,QAAQ,EAAE;QACpC,MAAMf,OAAO,GAAGxC,eAAe,CAACC,WAAW,CAACuD,OAAO,CAAC;QACpDD,QAAQ,CAACE,IAAI,CAAC;UACZC,SAAS,EAAElB,OAAO,CAACkB,SAAS,IAAI,CAAC;UACjCC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;UAChCC,OAAO,EAAEpB,OAAO,CAACoB,OAAO,IAAIpB;QAC9B,CAAC,CAAC;MACJ;IACF;;IAEA;IACAe,QAAQ,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,SAAS,GAAGK,CAAC,CAACL,SAAS,CAAC;;IAElD;IACA,MAAM7C,eAAe,GAAG0C,QAAQ,CAACS,MAAM,KAAK,CAAC,IAC3CT,QAAQ,CAACtC,KAAK,CAACgD,GAAG,IAAIjE,eAAe,CAACa,eAAe,CAACoD,GAAG,CAACL,OAAO,CAAC,CAAC;IAErE5B,MAAM,CAACyB,IAAI,CAAC;MACVP,IAAI,EAAEI,KAAK,CAACJ,IAAI;MAChBlC,IAAI,EAAEsC,KAAK,CAACtC,IAAI;MAChBkD,SAAS,EAAEZ,KAAK,CAACY,SAAS;MAC1BX,QAAQ;MACR1C;IACF,CAAC,CAAC;EACJ;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASK,mBAAmBA,CAACL,MAAkB,EAAiE;EAC9G,IAAIE,SAAS,GAAGiC,QAAQ;EACxB,IAAIhC,OAAO,GAAG,CAACgC,QAAQ;EAEvB,KAAK,MAAMb,KAAK,IAAItB,MAAM,EAAE;IAC1B,KAAK,MAAMQ,OAAO,IAAIc,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAIf,OAAO,CAACkB,SAAS,GAAGxB,SAAS,EAAE;QACjCA,SAAS,GAAGM,OAAO,CAACkB,SAAS;MAC/B;MACA,IAAIlB,OAAO,CAACkB,SAAS,GAAGvB,OAAO,EAAE;QAC/BA,OAAO,GAAGK,OAAO,CAACkB,SAAS;MAC7B;IACF;EACF;EAEA,IAAIxB,SAAS,KAAKiC,QAAQ,EAAE;IAC1BjC,SAAS,GAAG,CAAC;IACbC,OAAO,GAAG,CAAC;EACb;EAEA,MAAMC,aAAa,GAAGD,OAAO,GAAGD,SAAS;EAEzC,OAAO;IAAEA,SAAS;IAAEC,OAAO;IAAEC;EAAc,CAAC;AAC9C;;AAEA;AACA,OAAO,SAASgC,iBAAiBA,CAACb,QAAsB,EAAEc,UAAkB,EAAqB;EAC/F,IAAId,QAAQ,CAACS,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEtC;EACA,IAAIM,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAGhB,QAAQ,CAACS,MAAM,GAAG,CAAC;EAC/B,IAAIQ,OAAO,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAEzB,OAAOe,IAAI,IAAIC,KAAK,EAAE;IACpB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC1C,MAAM/B,OAAO,GAAGe,QAAQ,CAACkB,GAAG,CAAC;IAE7B,IAAIC,IAAI,CAACE,GAAG,CAACpC,OAAO,CAACkB,SAAS,GAAGW,UAAU,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACJ,OAAO,CAACd,SAAS,GAAGW,UAAU,CAAC,EAAE;MACvFG,OAAO,GAAGhC,OAAO;IACnB;IAEA,IAAIA,OAAO,CAACkB,SAAS,GAAGW,UAAU,EAAE;MAClCC,IAAI,GAAGG,GAAG,GAAG,CAAC;IAChB,CAAC,MAAM,IAAIjC,OAAO,CAACkB,SAAS,GAAGW,UAAU,EAAE;MACzCE,KAAK,GAAGE,GAAG,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,OAAOjC,OAAO;IAChB;EACF;EAEA,OAAOgC,OAAO;AAChB;;AAEA;AACA,OAAO,SAASK,mBAAmBA,CAACtB,QAAsB,EAAEc,UAAkB,EAAgB;EAC5F,OAAOd,QAAQ,CAACuB,MAAM,CAACb,GAAG,IAAIA,GAAG,CAACP,SAAS,IAAIW,UAAU,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}