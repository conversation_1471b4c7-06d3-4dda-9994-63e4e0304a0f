{"ast": null, "code": "export class EventStreamError extends Error {\n  constructor(type, message, index, eventstream) {\n    super();\n    this.type = type;\n    this.index = index;\n    this.message = message;\n    this.eventstream = eventstream;\n  }\n}", "map": {"version": 3, "names": ["EventStreamError", "Error", "constructor", "type", "message", "index", "eventstream"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/events/EventStreamError.js"], "sourcesContent": ["export class EventStreamError extends Error {\n    constructor(type, message, index, eventstream) {\n        super();\n        this.type = type;\n        this.index = index;\n        this.message = message;\n        this.eventstream = eventstream;\n    }\n}\n//# sourceMappingURL=EventStreamError.js.map"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,SAASC,KAAK,CAAC;EACxCC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,WAAW,EAAE;IAC3C,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,WAAW,GAAGA,WAAW;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}