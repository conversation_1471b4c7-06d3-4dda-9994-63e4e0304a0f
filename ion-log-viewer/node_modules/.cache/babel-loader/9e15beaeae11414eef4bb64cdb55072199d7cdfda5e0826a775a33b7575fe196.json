{"ast": null, "code": "import { Panel } from \"./panel.js\";\nconst _Stats = class _Stats2 {\n  constructor({\n    trackGPU = false,\n    logsPerSecond = 30,\n    samplesLog = 60,\n    samplesGraph = 10,\n    precision = 2,\n    minimal = false,\n    horizontal = true,\n    mode = 0\n  } = {}) {\n    this.gl = null;\n    this.ext = null;\n    this.activeQuery = null;\n    this.gpuQueries = [];\n    this.threeRendererPatched = false;\n    this.frames = 0;\n    this.renderCount = 0;\n    this.isRunningCPUProfiling = false;\n    this.totalCpuDuration = 0;\n    this.totalGpuDuration = 0;\n    this.totalGpuDurationCompute = 0;\n    this.totalFps = 0;\n    this.gpuPanel = null;\n    this.gpuPanelCompute = null;\n    this.averageFps = {\n      logs: [],\n      graph: []\n    };\n    this.averageCpu = {\n      logs: [],\n      graph: []\n    };\n    this.averageGpu = {\n      logs: [],\n      graph: []\n    };\n    this.averageGpuCompute = {\n      logs: [],\n      graph: []\n    };\n    this.handleClick = event => {\n      event.preventDefault();\n      this.showPanel(++this.mode % this.dom.children.length);\n    };\n    this.handleResize = () => {\n      this.resizePanel(this.fpsPanel, 0);\n      this.resizePanel(this.msPanel, 1);\n      if (this.gpuPanel) this.resizePanel(this.gpuPanel, 2);\n      if (this.gpuPanelCompute) this.resizePanel(this.gpuPanelCompute, 3);\n    };\n    this.mode = mode;\n    this.horizontal = horizontal;\n    this.minimal = minimal;\n    this.trackGPU = trackGPU;\n    this.samplesLog = samplesLog;\n    this.samplesGraph = samplesGraph;\n    this.precision = precision;\n    this.logsPerSecond = logsPerSecond;\n    this.dom = document.createElement(\"div\");\n    this.initializeDOM();\n    this.beginTime = performance.now();\n    this.prevTime = this.beginTime;\n    this.prevCpuTime = this.beginTime;\n    this.fpsPanel = this.addPanel(new _Stats2.Panel(\"FPS\", \"#0ff\", \"#002\"), 0);\n    this.msPanel = this.addPanel(new _Stats2.Panel(\"CPU\", \"#0f0\", \"#020\"), 1);\n    this.setupEventListeners();\n  }\n  initializeDOM() {\n    this.dom.style.cssText = `\n      position: fixed;\n      top: 0;\n      left: 0;\n      opacity: 0.9;\n      z-index: 10000;\n      ${this.minimal ? \"cursor: pointer;\" : \"\"}\n    `;\n  }\n  setupEventListeners() {\n    if (this.minimal) {\n      this.dom.addEventListener(\"click\", this.handleClick);\n      this.showPanel(this.mode);\n    } else {\n      window.addEventListener(\"resize\", this.handleResize);\n    }\n  }\n  async init(canvasOrGL) {\n    if (!canvasOrGL) {\n      console.error('Stats: The \"canvas\" parameter is undefined.');\n      return;\n    }\n    if (this.handleThreeRenderer(canvasOrGL)) return;\n    if (await this.handleWebGPURenderer(canvasOrGL)) return;\n    if (!this.initializeWebGL(canvasOrGL)) return;\n  }\n  handleThreeRenderer(renderer) {\n    if (renderer.isWebGLRenderer && !this.threeRendererPatched) {\n      this.patchThreeRenderer(renderer);\n      this.gl = renderer.getContext();\n      if (this.trackGPU) {\n        this.initializeGPUTracking();\n      }\n      return true;\n    }\n    return false;\n  }\n  async handleWebGPURenderer(renderer) {\n    if (renderer.isWebGPURenderer) {\n      if (this.trackGPU) {\n        renderer.backend.trackTimestamp = true;\n        if (await renderer.hasFeatureAsync(\"timestamp-query\")) {\n          this.initializeWebGPUPanels();\n        }\n      }\n      this.info = renderer.info;\n      return true;\n    }\n    return false;\n  }\n  initializeWebGPUPanels() {\n    this.gpuPanel = this.addPanel(new _Stats2.Panel(\"GPU\", \"#ff0\", \"#220\"), 2);\n    this.gpuPanelCompute = this.addPanel(new _Stats2.Panel(\"CPT\", \"#e1e1e1\", \"#212121\"), 3);\n  }\n  initializeWebGL(canvasOrGL) {\n    if (canvasOrGL instanceof WebGL2RenderingContext) {\n      this.gl = canvasOrGL;\n    } else if (canvasOrGL instanceof HTMLCanvasElement || canvasOrGL instanceof OffscreenCanvas) {\n      this.gl = canvasOrGL.getContext(\"webgl2\");\n      if (!this.gl) {\n        console.error(\"Stats: Unable to obtain WebGL2 context.\");\n        return false;\n      }\n    } else {\n      console.error(\"Stats: Invalid input type. Expected WebGL2RenderingContext, HTMLCanvasElement, or OffscreenCanvas.\");\n      return false;\n    }\n    return true;\n  }\n  initializeGPUTracking() {\n    if (this.gl) {\n      this.ext = this.gl.getExtension(\"EXT_disjoint_timer_query_webgl2\");\n      if (this.ext) {\n        this.gpuPanel = this.addPanel(new _Stats2.Panel(\"GPU\", \"#ff0\", \"#220\"), 2);\n      }\n    }\n  }\n  begin() {\n    if (!this.isRunningCPUProfiling) {\n      this.beginProfiling(\"cpu-started\");\n    }\n    if (!this.gl || !this.ext) return;\n    if (this.activeQuery) {\n      this.gl.endQuery(this.ext.TIME_ELAPSED_EXT);\n    }\n    this.activeQuery = this.gl.createQuery();\n    if (this.activeQuery) {\n      this.gl.beginQuery(this.ext.TIME_ELAPSED_EXT, this.activeQuery);\n    }\n  }\n  end() {\n    this.renderCount++;\n    if (this.gl && this.ext && this.activeQuery) {\n      this.gl.endQuery(this.ext.TIME_ELAPSED_EXT);\n      this.gpuQueries.push({\n        query: this.activeQuery\n      });\n      this.activeQuery = null;\n    }\n  }\n  update() {\n    if (!this.info) {\n      this.processGpuQueries();\n    } else {\n      this.processWebGPUTimestamps();\n    }\n    this.endProfiling(\"cpu-started\", \"cpu-finished\", \"cpu-duration\");\n    this.updateAverages();\n    this.resetCounters();\n  }\n  processWebGPUTimestamps() {\n    this.totalGpuDuration = this.info.render.timestamp;\n    this.totalGpuDurationCompute = this.info.compute.timestamp;\n    this.addToAverage(this.totalGpuDurationCompute, this.averageGpuCompute);\n  }\n  updateAverages() {\n    this.addToAverage(this.totalCpuDuration, this.averageCpu);\n    this.addToAverage(this.totalGpuDuration, this.averageGpu);\n  }\n  resetCounters() {\n    this.renderCount = 0;\n    if (this.totalCpuDuration === 0) {\n      this.beginProfiling(\"cpu-started\");\n    }\n    this.totalCpuDuration = 0;\n    this.totalFps = 0;\n    this.beginTime = this.endInternal();\n  }\n  resizePanel(panel, offset) {\n    panel.canvas.style.position = \"absolute\";\n    if (this.minimal) {\n      panel.canvas.style.display = \"none\";\n    } else {\n      panel.canvas.style.display = \"block\";\n      if (this.horizontal) {\n        panel.canvas.style.top = \"0px\";\n        panel.canvas.style.left = offset * panel.WIDTH / panel.PR + \"px\";\n      } else {\n        panel.canvas.style.left = \"0px\";\n        panel.canvas.style.top = offset * panel.HEIGHT / panel.PR + \"px\";\n      }\n    }\n  }\n  addPanel(panel, offset) {\n    if (panel.canvas) {\n      this.dom.appendChild(panel.canvas);\n      this.resizePanel(panel, offset);\n    }\n    return panel;\n  }\n  showPanel(id) {\n    for (let i = 0; i < this.dom.children.length; i++) {\n      const child = this.dom.children[i];\n      child.style.display = i === id ? \"block\" : \"none\";\n    }\n    this.mode = id;\n  }\n  processGpuQueries() {\n    if (!this.gl || !this.ext) return;\n    this.totalGpuDuration = 0;\n    this.gpuQueries.forEach((queryInfo, index) => {\n      if (this.gl) {\n        const available = this.gl.getQueryParameter(queryInfo.query, this.gl.QUERY_RESULT_AVAILABLE);\n        const disjoint = this.gl.getParameter(this.ext.GPU_DISJOINT_EXT);\n        if (available && !disjoint) {\n          const elapsed = this.gl.getQueryParameter(queryInfo.query, this.gl.QUERY_RESULT);\n          const duration = elapsed * 1e-6;\n          this.totalGpuDuration += duration;\n          this.gl.deleteQuery(queryInfo.query);\n          this.gpuQueries.splice(index, 1);\n        }\n      }\n    });\n  }\n  endInternal() {\n    this.frames++;\n    const time = (performance || Date).now();\n    const elapsed = time - this.prevTime;\n    if (time >= this.prevCpuTime + 1e3 / this.logsPerSecond) {\n      const fps = Math.round(this.frames * 1e3 / elapsed);\n      this.addToAverage(fps, this.averageFps);\n      this.updatePanel(this.fpsPanel, this.averageFps, 0);\n      this.updatePanel(this.msPanel, this.averageCpu, this.precision);\n      this.updatePanel(this.gpuPanel, this.averageGpu, this.precision);\n      if (this.gpuPanelCompute) {\n        this.updatePanel(this.gpuPanelCompute, this.averageGpuCompute);\n      }\n      this.frames = 0;\n      this.prevCpuTime = time;\n      this.prevTime = time;\n    }\n    return time;\n  }\n  addToAverage(value, averageArray) {\n    averageArray.logs.push(value);\n    if (averageArray.logs.length > this.samplesLog) {\n      averageArray.logs.shift();\n    }\n    averageArray.graph.push(value);\n    if (averageArray.graph.length > this.samplesGraph) {\n      averageArray.graph.shift();\n    }\n  }\n  beginProfiling(marker) {\n    if (window.performance) {\n      window.performance.mark(marker);\n      this.isRunningCPUProfiling = true;\n    }\n  }\n  endProfiling(startMarker, endMarker, measureName) {\n    if (window.performance && endMarker && this.isRunningCPUProfiling) {\n      window.performance.mark(endMarker);\n      const cpuMeasure = performance.measure(measureName, startMarker, endMarker);\n      this.totalCpuDuration += cpuMeasure.duration;\n      this.isRunningCPUProfiling = false;\n    }\n  }\n  updatePanel(panel, averageArray, precision = 2) {\n    if (averageArray.logs.length > 0) {\n      let sumLog = 0;\n      let max = 0.01;\n      for (let i = 0; i < averageArray.logs.length; i++) {\n        sumLog += averageArray.logs[i];\n        if (averageArray.logs[i] > max) {\n          max = averageArray.logs[i];\n        }\n      }\n      let sumGraph = 0;\n      let maxGraph = 0.01;\n      for (let i = 0; i < averageArray.graph.length; i++) {\n        sumGraph += averageArray.graph[i];\n        if (averageArray.graph[i] > maxGraph) {\n          maxGraph = averageArray.graph[i];\n        }\n      }\n      if (panel) {\n        panel.update(sumLog / Math.min(averageArray.logs.length, this.samplesLog), sumGraph / Math.min(averageArray.graph.length, this.samplesGraph), max, maxGraph, precision);\n      }\n    }\n  }\n  get domElement() {\n    return this.dom;\n  }\n  patchThreeRenderer(renderer) {\n    const originalRenderMethod = renderer.render;\n    const statsInstance = this;\n    renderer.render = function (scene, camera) {\n      statsInstance.begin();\n      originalRenderMethod.call(this, scene, camera);\n      statsInstance.end();\n    };\n    this.threeRendererPatched = true;\n  }\n};\n_Stats.Panel = Panel;\nlet Stats = _Stats;\nexport { Stats as default };", "map": {"version": 3, "names": ["_Stats", "_Stats2", "constructor", "trackGPU", "logsPerSecond", "samplesLog", "samplesGraph", "precision", "minimal", "horizontal", "mode", "gl", "ext", "activeQuery", "gpuQueries", "threeRendererPatched", "frames", "renderCount", "isRunningCPUProfiling", "totalCpuDuration", "totalGpuDuration", "totalGpuDurationCompute", "totalFps", "gpuPanel", "gpuPanelCompute", "averageFps", "logs", "graph", "averageCpu", "averageGpu", "averageGpuCompute", "handleClick", "event", "preventDefault", "showPanel", "dom", "children", "length", "handleResize", "resizePanel", "fpsPanel", "msPanel", "document", "createElement", "initializeDOM", "beginTime", "performance", "now", "prevTime", "prevCpuTime", "addPanel", "Panel", "setupEventListeners", "style", "cssText", "addEventListener", "window", "init", "canvasOrGL", "console", "error", "handle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleWebGPURenderer", "initializeWebGL", "renderer", "isWebGLRenderer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getContext", "initializeGPUTracking", "isWebGP<PERSON><PERSON><PERSON>", "backend", "trackTimestamp", "hasFeatureAsync", "initializeWebGPUPanels", "info", "WebGL2RenderingContext", "HTMLCanvasElement", "OffscreenCanvas", "getExtension", "begin", "beginProfiling", "<PERSON><PERSON><PERSON><PERSON>", "TIME_ELAPSED_EXT", "createQuery", "begin<PERSON><PERSON>y", "end", "push", "query", "update", "processGpuQueries", "processWebGPUTimestamps", "endProfiling", "updateAverages", "resetCounters", "render", "timestamp", "compute", "addToAverage", "endInternal", "panel", "offset", "canvas", "position", "display", "top", "left", "WIDTH", "PR", "HEIGHT", "append<PERSON><PERSON><PERSON>", "id", "i", "child", "for<PERSON>ach", "queryInfo", "index", "available", "getQueryParameter", "QUERY_RESULT_AVAILABLE", "disjoint", "getParameter", "GPU_DISJOINT_EXT", "elapsed", "QUERY_RESULT", "duration", "deleteQuery", "splice", "time", "Date", "fps", "Math", "round", "updatePanel", "value", "averageArray", "shift", "marker", "mark", "startMarker", "<PERSON><PERSON><PERSON><PERSON>", "measureName", "cpuMeasure", "measure", "sumLog", "max", "sumGraph", "maxGraph", "min", "dom<PERSON>lement", "originalRenderMethod", "statsInstance", "scene", "camera", "call", "Stats"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/stats-gl/lib/main.ts"], "sourcesContent": ["import * as THREE from 'three';\nimport { Panel } from './panel';\n\ninterface StatsOptions {\n  trackGPU?: boolean;\n  logsPerSecond?: number;\n  samplesLog?: number;\n  samplesGraph?: number;\n  precision?: number;\n  minimal?: boolean;\n  horizontal?: boolean;\n  mode?: number;\n}\n\ninterface QueryInfo {\n  query: WebGLQuery;\n}\n\ninterface AverageData {\n  logs: number[];\n  graph: number[];\n}\n\ninterface InfoData {\n  render: {\n    timestamp: number;\n  };\n  compute: {\n    timestamp: number;\n  };\n}\n\nclass Stats {\n  private dom: HTMLDivElement;\n  private mode: number;\n  private horizontal: boolean;\n  private minimal: boolean;\n  private trackGPU: boolean;\n  private samplesLog: number;\n  private samplesGraph: number;\n  private precision: number;\n  private logsPerSecond: number;\n\n  private gl: WebGL2RenderingContext | null = null;\n  private ext: any | null = null;\n  private info?: InfoData;\n  private activeQuery: WebGLQuery | null = null;\n  private gpuQueries: QueryInfo[] = [];\n  private threeRendererPatched = false;\n\n  private beginTime: number;\n  private prevTime: number;\n  private prevCpuTime: number;\n  private frames = 0;\n  private renderCount = 0;\n  private isRunningCPUProfiling = false;\n\n  private totalCpuDuration = 0;\n  private totalGpuDuration = 0;\n  private totalGpuDurationCompute = 0;\n  private totalFps = 0;\n\n  private fpsPanel: Panel;\n  private msPanel: Panel;\n  private gpuPanel: Panel | null = null;\n  private gpuPanelCompute: Panel | null = null;\n\n  private averageFps: AverageData = { logs: [], graph: [] };\n  private averageCpu: AverageData = { logs: [], graph: [] };\n  private averageGpu: AverageData = { logs: [], graph: [] };\n  private averageGpuCompute: AverageData = { logs: [], graph: [] };\n\n  static Panel = Panel;\n\n  constructor({\n    trackGPU = false,\n    logsPerSecond = 30,\n    samplesLog = 60,\n    samplesGraph = 10,\n    precision = 2,\n    minimal = false,\n    horizontal = true,\n    mode = 0\n  }: StatsOptions = {}) {\n    this.mode = mode;\n    this.horizontal = horizontal;\n    this.minimal = minimal;\n    this.trackGPU = trackGPU;\n    this.samplesLog = samplesLog;\n    this.samplesGraph = samplesGraph;\n    this.precision = precision;\n    this.logsPerSecond = logsPerSecond;\n\n    // Initialize DOM\n    this.dom = document.createElement('div');\n    this.initializeDOM();\n\n    // Initialize timing\n    this.beginTime = performance.now();\n    this.prevTime = this.beginTime;\n    this.prevCpuTime = this.beginTime;\n\n    // Create panels\n    this.fpsPanel = this.addPanel(new Stats.Panel('FPS', '#0ff', '#002'), 0);\n    this.msPanel = this.addPanel(new Stats.Panel('CPU', '#0f0', '#020'), 1);\n\n    this.setupEventListeners();\n  }\n\n\n  private initializeDOM(): void {\n    this.dom.style.cssText = `\n      position: fixed;\n      top: 0;\n      left: 0;\n      opacity: 0.9;\n      z-index: 10000;\n      ${this.minimal ? 'cursor: pointer;' : ''}\n    `;\n  }\n\n  private setupEventListeners(): void {\n    if (this.minimal) {\n      this.dom.addEventListener('click', this.handleClick);\n      this.showPanel(this.mode);\n    } else {\n      window.addEventListener('resize', this.handleResize);\n    }\n  }\n\n  private handleClick = (event: MouseEvent): void => {\n    event.preventDefault();\n    this.showPanel(++this.mode % this.dom.children.length);\n  };\n\n  private handleResize = (): void => {\n    this.resizePanel(this.fpsPanel, 0);\n    this.resizePanel(this.msPanel, 1);\n    if (this.gpuPanel) this.resizePanel(this.gpuPanel, 2);\n    if (this.gpuPanelCompute) this.resizePanel(this.gpuPanelCompute, 3);\n  };\n\n  public async init(\n    canvasOrGL: WebGL2RenderingContext | HTMLCanvasElement | OffscreenCanvas | any\n  ): Promise<void> {\n    if (!canvasOrGL) {\n      console.error('Stats: The \"canvas\" parameter is undefined.');\n      return;\n    }\n\n    if (this.handleThreeRenderer(canvasOrGL)) return;\n    if (await this.handleWebGPURenderer(canvasOrGL)) return;\n    if (!this.initializeWebGL(canvasOrGL)) return;\n\n  }\n\n  private handleThreeRenderer(renderer: any): boolean {\n    if (renderer.isWebGLRenderer && !this.threeRendererPatched) {\n      this.patchThreeRenderer(renderer);\n      this.gl = renderer.getContext();\n\n      if (this.trackGPU) {\n        this.initializeGPUTracking();\n      }\n      return true;\n    }\n    return false;\n  }\n\n  private async handleWebGPURenderer(renderer: any): Promise<boolean> {\n    if (renderer.isWebGPURenderer) {\n      if (this.trackGPU) {\n        renderer.backend.trackTimestamp = true;\n        if (await renderer.hasFeatureAsync('timestamp-query')) {\n          this.initializeWebGPUPanels();\n        }\n      }\n      this.info = renderer.info;\n      return true;\n    }\n    return false;\n  }\n\n  private initializeWebGPUPanels(): void {\n    this.gpuPanel = this.addPanel(new Stats.Panel('GPU', '#ff0', '#220'), 2);\n    this.gpuPanelCompute = this.addPanel(\n      new Stats.Panel('CPT', '#e1e1e1', '#212121'),\n      3\n    );\n  }\n\n  private initializeWebGL(\n    canvasOrGL: WebGL2RenderingContext | HTMLCanvasElement | OffscreenCanvas\n  ): boolean {\n    if (canvasOrGL instanceof WebGL2RenderingContext) {\n      this.gl = canvasOrGL;\n    } else if (\n      canvasOrGL instanceof HTMLCanvasElement ||\n      canvasOrGL instanceof OffscreenCanvas\n    ) {\n      this.gl = canvasOrGL.getContext('webgl2');\n      if (!this.gl) {\n        console.error('Stats: Unable to obtain WebGL2 context.');\n        return false;\n      }\n    } else {\n      console.error(\n        'Stats: Invalid input type. Expected WebGL2RenderingContext, HTMLCanvasElement, or OffscreenCanvas.'\n      );\n      return false;\n    }\n    return true;\n  }\n\n  private initializeGPUTracking(): void {\n    if (this.gl) {\n      this.ext = this.gl.getExtension('EXT_disjoint_timer_query_webgl2');\n      if (this.ext) {\n        this.gpuPanel = this.addPanel(new Stats.Panel('GPU', '#ff0', '#220'), 2);\n      }\n    }\n  }\n\n  public begin(): void {\n    if (!this.isRunningCPUProfiling) {\n      this.beginProfiling('cpu-started');\n    }\n\n    if (!this.gl || !this.ext) return;\n\n    if (this.activeQuery) {\n      this.gl.endQuery(this.ext.TIME_ELAPSED_EXT);\n    }\n\n    this.activeQuery = this.gl.createQuery();\n    if (this.activeQuery) {\n      this.gl.beginQuery(this.ext.TIME_ELAPSED_EXT, this.activeQuery);\n    }\n  }\n\n  public end(): void {\n    this.renderCount++;\n    if (this.gl && this.ext && this.activeQuery) {\n      this.gl.endQuery(this.ext.TIME_ELAPSED_EXT);\n      this.gpuQueries.push({ query: this.activeQuery });\n      this.activeQuery = null;\n    }\n  }\n\n  public update(): void {\n    if (!this.info) {\n      this.processGpuQueries();\n    } else {\n      this.processWebGPUTimestamps();\n    }\n\n    this.endProfiling('cpu-started', 'cpu-finished', 'cpu-duration');\n    this.updateAverages();\n    this.resetCounters();\n  }\n\n  private processWebGPUTimestamps(): void {\n    this.totalGpuDuration = this.info!.render.timestamp;\n    this.totalGpuDurationCompute = this.info!.compute.timestamp;\n    this.addToAverage(this.totalGpuDurationCompute, this.averageGpuCompute);\n  }\n\n  private updateAverages(): void {\n    this.addToAverage(this.totalCpuDuration, this.averageCpu);\n    this.addToAverage(this.totalGpuDuration, this.averageGpu);\n  }\n\n  private resetCounters(): void {\n    this.renderCount = 0;\n    if (this.totalCpuDuration === 0) {\n      this.beginProfiling('cpu-started');\n    }\n    this.totalCpuDuration = 0;\n    this.totalFps = 0;\n    this.beginTime = this.endInternal();\n  }\n\n\n  resizePanel(panel: Panel, offset: number) {\n\n    panel.canvas.style.position = 'absolute';\n\n    if (this.minimal) {\n\n      panel.canvas.style.display = 'none';\n\n    } else {\n\n      panel.canvas.style.display = 'block';\n      if (this.horizontal) {\n        panel.canvas.style.top = '0px';\n        panel.canvas.style.left = offset * panel.WIDTH / panel.PR + 'px';\n      } else {\n        panel.canvas.style.left = '0px';\n        panel.canvas.style.top = offset * panel.HEIGHT / panel.PR + 'px';\n\n      }\n    }\n\n  }\n  addPanel(panel: Panel, offset: number) {\n\n    if (panel.canvas) {\n\n      this.dom.appendChild(panel.canvas);\n\n      this.resizePanel(panel, offset);\n\n    }\n\n    return panel;\n\n  }\n\n  showPanel(id: number) {\n\n    for (let i = 0; i < this.dom.children.length; i++) {\n      const child = this.dom.children[i] as HTMLElement;\n\n      child.style.display = i === id ? 'block' : 'none';\n\n    }\n\n    this.mode = id;\n\n  }\n\n  processGpuQueries() {\n\n\n    if (!this.gl || !this.ext) return;\n\n    this.totalGpuDuration = 0;\n\n    this.gpuQueries.forEach((queryInfo, index) => {\n      if (this.gl) {\n        const available = this.gl.getQueryParameter(queryInfo.query, this.gl.QUERY_RESULT_AVAILABLE);\n        const disjoint = this.gl.getParameter(this.ext.GPU_DISJOINT_EXT);\n\n        if (available && !disjoint) {\n          const elapsed = this.gl.getQueryParameter(queryInfo.query, this.gl.QUERY_RESULT);\n          const duration = elapsed * 1e-6;  // Convert nanoseconds to milliseconds\n          this.totalGpuDuration += duration;\n          this.gl.deleteQuery(queryInfo.query);\n          this.gpuQueries.splice(index, 1);  // Remove the processed query\n        }\n      }\n    });\n\n  }\n\n  endInternal() {\n\n    this.frames++;\n    const time = (performance || Date).now();\n    const elapsed = time - this.prevTime;\n\n    // Calculate FPS more frequently based on logsPerSecond\n    if (time >= this.prevCpuTime + 1000 / this.logsPerSecond) {\n      // Calculate FPS and round to nearest integer\n      const fps = Math.round((this.frames * 1000) / elapsed);\n\n      // Add to FPS averages\n      this.addToAverage(fps, this.averageFps);\n\n      // Update all panels\n      this.updatePanel(this.fpsPanel, this.averageFps, 0);\n      this.updatePanel(this.msPanel, this.averageCpu, this.precision);\n      this.updatePanel(this.gpuPanel, this.averageGpu, this.precision);\n\n      if (this.gpuPanelCompute) {\n        this.updatePanel(this.gpuPanelCompute, this.averageGpuCompute);\n      }\n\n      // Reset frame counter for next interval\n      this.frames = 0;\n      this.prevCpuTime = time;\n      this.prevTime = time;\n    }\n\n    return time;\n\n  }\n\n  addToAverage(value: number, averageArray: { logs: any; graph: any; }) {\n\n    averageArray.logs.push(value);\n    if (averageArray.logs.length > this.samplesLog) {\n\n      averageArray.logs.shift();\n\n    }\n\n    averageArray.graph.push(value);\n    if (averageArray.graph.length > this.samplesGraph) {\n\n      averageArray.graph.shift();\n\n    }\n\n  }\n\n  beginProfiling(marker: string) {\n\n    if (window.performance) {\n\n      window.performance.mark(marker);\n      this.isRunningCPUProfiling = true\n\n    }\n\n  }\n\n  endProfiling(startMarker: string | PerformanceMeasureOptions | undefined, endMarker: string | undefined, measureName: string) {\n\n    if (window.performance && endMarker && this.isRunningCPUProfiling) {\n\n      window.performance.mark(endMarker);\n      const cpuMeasure = performance.measure(measureName, startMarker, endMarker);\n      this.totalCpuDuration += cpuMeasure.duration;\n      this.isRunningCPUProfiling = false\n\n    }\n\n  }\n\n  updatePanel(panel: { update: any; } | null, averageArray: { logs: number[], graph: number[] }, precision = 2) {\n\n    if (averageArray.logs.length > 0) {\n\n      let sumLog = 0;\n      let max = 0.01;\n\n      for (let i = 0; i < averageArray.logs.length; i++) {\n\n        sumLog += averageArray.logs[i];\n\n        if (averageArray.logs[i] > max) {\n          max = averageArray.logs[i];\n        }\n\n      }\n\n      let sumGraph = 0;\n      let maxGraph = 0.01;\n      for (let i = 0; i < averageArray.graph.length; i++) {\n\n        sumGraph += averageArray.graph[i];\n\n        if (averageArray.graph[i] > maxGraph) {\n          maxGraph = averageArray.graph[i];\n        }\n\n      }\n\n      if (panel) {\n        panel.update(sumLog / Math.min(averageArray.logs.length, this.samplesLog), sumGraph / Math.min(averageArray.graph.length, this.samplesGraph), max, maxGraph, precision);\n      }\n\n    }\n  }\n\n  get domElement() {\n    // patch for some use case in threejs\n    return this.dom;\n\n  }\n\n  patchThreeRenderer(renderer: any) {\n\n    // Store the original render method\n    const originalRenderMethod = renderer.render;\n\n    // Reference to the stats instance\n    const statsInstance = this;\n\n    // Override the render method on the prototype\n    renderer.render = function (scene: THREE.Scene, camera: THREE.Camera) {\n\n\n      statsInstance.begin(); // Start tracking for this render call\n\n      // Call the original render method\n      originalRenderMethod.call(this, scene, camera);\n\n      statsInstance.end(); // End tracking for this render call\n    };\n\n\n    this.threeRendererPatched = true;\n\n  }\n}\n\n\nexport default Stats;"], "mappings": ";AAgCA,MAAMA,MAAA,GAAN,MAAMC,OAAA,CAAM;EA0CVC,YAAY;IACVC,QAAA,GAAW;IACXC,aAAA,GAAgB;IAChBC,UAAA,GAAa;IACbC,YAAA,GAAe;IACfC,SAAA,GAAY;IACZC,OAAA,GAAU;IACVC,UAAA,GAAa;IACbC,IAAA,GAAO;EACT,IAAkB,IAAI;IAxCtB,KAAQC,EAAA,GAAoC;IAC5C,KAAQC,GAAA,GAAkB;IAE1B,KAAQC,WAAA,GAAiC;IACzC,KAAQC,UAAA,GAA0B;IAClC,KAAQC,oBAAA,GAAuB;IAK/B,KAAQC,MAAA,GAAS;IACjB,KAAQC,WAAA,GAAc;IACtB,KAAQC,qBAAA,GAAwB;IAEhC,KAAQC,gBAAA,GAAmB;IAC3B,KAAQC,gBAAA,GAAmB;IAC3B,KAAQC,uBAAA,GAA0B;IAClC,KAAQC,QAAA,GAAW;IAInB,KAAQC,QAAA,GAAyB;IACjC,KAAQC,eAAA,GAAgC;IAExC,KAAQC,UAAA,GAA0B;MAAEC,IAAA,EAAM;MAAIC,KAAA,EAAO;IAAA;IACrD,KAAQC,UAAA,GAA0B;MAAEF,IAAA,EAAM;MAAIC,KAAA,EAAO;IAAA;IACrD,KAAQE,UAAA,GAA0B;MAAEH,IAAA,EAAM;MAAIC,KAAA,EAAO;IAAA;IACrD,KAAQG,iBAAA,GAAiC;MAAEJ,IAAA,EAAM;MAAIC,KAAA,EAAO;IAAA;IA4DpD,KAAAI,WAAA,GAAeC,KAAA,IAA4B;MACjDA,KAAA,CAAMC,cAAA,CAAe;MACrB,KAAKC,SAAA,CAAU,EAAE,KAAKxB,IAAA,GAAO,KAAKyB,GAAA,CAAIC,QAAA,CAASC,MAAM;IAAA;IAGvD,KAAQC,YAAA,GAAe,MAAY;MAC5B,KAAAC,WAAA,CAAY,KAAKC,QAAA,EAAU,CAAC;MAC5B,KAAAD,WAAA,CAAY,KAAKE,OAAA,EAAS,CAAC;MAChC,IAAI,KAAKlB,QAAA,EAAe,KAAAgB,WAAA,CAAY,KAAKhB,QAAA,EAAU,CAAC;MACpD,IAAI,KAAKC,eAAA,EAAsB,KAAAe,WAAA,CAAY,KAAKf,eAAA,EAAiB,CAAC;IAAA;IAvDlE,KAAKd,IAAA,GAAOA,IAAA;IACZ,KAAKD,UAAA,GAAaA,UAAA;IAClB,KAAKD,OAAA,GAAUA,OAAA;IACf,KAAKL,QAAA,GAAWA,QAAA;IAChB,KAAKE,UAAA,GAAaA,UAAA;IAClB,KAAKC,YAAA,GAAeA,YAAA;IACpB,KAAKC,SAAA,GAAYA,SAAA;IACjB,KAAKH,aAAA,GAAgBA,aAAA;IAGhB,KAAA+B,GAAA,GAAMO,QAAA,CAASC,aAAA,CAAc,KAAK;IACvC,KAAKC,aAAA,CAAc;IAGd,KAAAC,SAAA,GAAYC,WAAA,CAAYC,GAAA;IAC7B,KAAKC,QAAA,GAAW,KAAKH,SAAA;IACrB,KAAKI,WAAA,GAAc,KAAKJ,SAAA;IAGnB,KAAAL,QAAA,GAAW,KAAKU,QAAA,CAAS,IAAIjD,OAAA,CAAMkD,KAAA,CAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;IAClE,KAAAV,OAAA,GAAU,KAAKS,QAAA,CAAS,IAAIjD,OAAA,CAAMkD,KAAA,CAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;IAEtE,KAAKC,mBAAA,CAAoB;EAC3B;EAGQR,cAAA,EAAsB;IACvB,KAAAT,GAAA,CAAIkB,KAAA,CAAMC,OAAA,GAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMrB,KAAK9C,OAAA,GAAU,qBAAqB,EAAE;AAAA;EAE5C;EAEQ4C,oBAAA,EAA4B;IAClC,IAAI,KAAK5C,OAAA,EAAS;MAChB,KAAK2B,GAAA,CAAIoB,gBAAA,CAAiB,SAAS,KAAKxB,WAAW;MAC9C,KAAAG,SAAA,CAAU,KAAKxB,IAAI;IAAA,OACnB;MACE8C,MAAA,CAAAD,gBAAA,CAAiB,UAAU,KAAKjB,YAAY;IACrD;EACF;EAcA,MAAamB,KACXC,UAAA,EACe;IACf,IAAI,CAACA,UAAA,EAAY;MACfC,OAAA,CAAQC,KAAA,CAAM,6CAA6C;MAC3D;IACF;IAEI,SAAKC,mBAAA,CAAoBH,UAAU,GAAG;IACtC,UAAM,KAAKI,oBAAA,CAAqBJ,UAAU,GAAG;IAC7C,KAAC,KAAKK,eAAA,CAAgBL,UAAU,GAAG;EAEzC;EAEQG,oBAAoBG,QAAA,EAAwB;IAClD,IAAIA,QAAA,CAASC,eAAA,IAAmB,CAAC,KAAKlD,oBAAA,EAAsB;MAC1D,KAAKmD,kBAAA,CAAmBF,QAAQ;MAC3B,KAAArD,EAAA,GAAKqD,QAAA,CAASG,UAAA;MAEnB,IAAI,KAAKhE,QAAA,EAAU;QACjB,KAAKiE,qBAAA,CAAsB;MAC7B;MACO;IACT;IACO;EACT;EAEA,MAAcN,qBAAqBE,QAAA,EAAiC;IAClE,IAAIA,QAAA,CAASK,gBAAA,EAAkB;MAC7B,IAAI,KAAKlE,QAAA,EAAU;QACjB6D,QAAA,CAASM,OAAA,CAAQC,cAAA,GAAiB;QAClC,IAAI,MAAMP,QAAA,CAASQ,eAAA,CAAgB,iBAAiB,GAAG;UACrD,KAAKC,sBAAA,CAAuB;QAC9B;MACF;MACA,KAAKC,IAAA,GAAOV,QAAA,CAASU,IAAA;MACd;IACT;IACO;EACT;EAEQD,uBAAA,EAA+B;IAChC,KAAAlD,QAAA,GAAW,KAAK2B,QAAA,CAAS,IAAIjD,OAAA,CAAMkD,KAAA,CAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;IACvE,KAAK3B,eAAA,GAAkB,KAAK0B,QAAA,CAC1B,IAAIjD,OAAA,CAAMkD,KAAA,CAAM,OAAO,WAAW,SAAS,GAC3C;EAEJ;EAEQY,gBACNL,UAAA,EACS;IACT,IAAIA,UAAA,YAAsBiB,sBAAA,EAAwB;MAChD,KAAKhE,EAAA,GAAK+C,UAAA;IAEV,WAAAA,UAAA,YAAsBkB,iBAAA,IACtBlB,UAAA,YAAsBmB,eAAA,EACtB;MACK,KAAAlE,EAAA,GAAK+C,UAAA,CAAWS,UAAA,CAAW,QAAQ;MACpC,KAAC,KAAKxD,EAAA,EAAI;QACZgD,OAAA,CAAQC,KAAA,CAAM,yCAAyC;QAChD;MACT;IAAA,OACK;MACGD,OAAA,CAAAC,KAAA,CACN;MAEK;IACT;IACO;EACT;EAEQQ,sBAAA,EAA8B;IACpC,IAAI,KAAKzD,EAAA,EAAI;MACX,KAAKC,GAAA,GAAM,KAAKD,EAAA,CAAGmE,YAAA,CAAa,iCAAiC;MACjE,IAAI,KAAKlE,GAAA,EAAK;QACP,KAAAW,QAAA,GAAW,KAAK2B,QAAA,CAAS,IAAIjD,OAAA,CAAMkD,KAAA,CAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;MACzE;IACF;EACF;EAEO4B,MAAA,EAAc;IACf,KAAC,KAAK7D,qBAAA,EAAuB;MAC/B,KAAK8D,cAAA,CAAe,aAAa;IACnC;IAEA,IAAI,CAAC,KAAKrE,EAAA,IAAM,CAAC,KAAKC,GAAA,EAAK;IAE3B,IAAI,KAAKC,WAAA,EAAa;MACpB,KAAKF,EAAA,CAAGsE,QAAA,CAAS,KAAKrE,GAAA,CAAIsE,gBAAgB;IAC5C;IAEK,KAAArE,WAAA,GAAc,KAAKF,EAAA,CAAGwE,WAAA,CAAY;IACvC,IAAI,KAAKtE,WAAA,EAAa;MACpB,KAAKF,EAAA,CAAGyE,UAAA,CAAW,KAAKxE,GAAA,CAAIsE,gBAAA,EAAkB,KAAKrE,WAAW;IAChE;EACF;EAEOwE,IAAA,EAAY;IACZ,KAAApE,WAAA;IACL,IAAI,KAAKN,EAAA,IAAM,KAAKC,GAAA,IAAO,KAAKC,WAAA,EAAa;MAC3C,KAAKF,EAAA,CAAGsE,QAAA,CAAS,KAAKrE,GAAA,CAAIsE,gBAAgB;MAC1C,KAAKpE,UAAA,CAAWwE,IAAA,CAAK;QAAEC,KAAA,EAAO,KAAK1E;MAAA,CAAa;MAChD,KAAKA,WAAA,GAAc;IACrB;EACF;EAEO2E,OAAA,EAAe;IAChB,KAAC,KAAKd,IAAA,EAAM;MACd,KAAKe,iBAAA,CAAkB;IAAA,OAClB;MACL,KAAKC,uBAAA,CAAwB;IAC/B;IAEK,KAAAC,YAAA,CAAa,eAAe,gBAAgB,cAAc;IAC/D,KAAKC,cAAA,CAAe;IACpB,KAAKC,aAAA,CAAc;EACrB;EAEQH,wBAAA,EAAgC;IACjC,KAAAtE,gBAAA,GAAmB,KAAKsD,IAAA,CAAMoB,MAAA,CAAOC,SAAA;IACrC,KAAA1E,uBAAA,GAA0B,KAAKqD,IAAA,CAAMsB,OAAA,CAAQD,SAAA;IAClD,KAAKE,YAAA,CAAa,KAAK5E,uBAAA,EAAyB,KAAKS,iBAAiB;EACxE;EAEQ8D,eAAA,EAAuB;IAC7B,KAAKK,YAAA,CAAa,KAAK9E,gBAAA,EAAkB,KAAKS,UAAU;IACxD,KAAKqE,YAAA,CAAa,KAAK7E,gBAAA,EAAkB,KAAKS,UAAU;EAC1D;EAEQgE,cAAA,EAAsB;IAC5B,KAAK5E,WAAA,GAAc;IACf,SAAKE,gBAAA,KAAqB,GAAG;MAC/B,KAAK6D,cAAA,CAAe,aAAa;IACnC;IACA,KAAK7D,gBAAA,GAAmB;IACxB,KAAKG,QAAA,GAAW;IACX,KAAAuB,SAAA,GAAY,KAAKqD,WAAA;EACxB;EAGA3D,YAAY4D,KAAA,EAAcC,MAAA,EAAgB;IAElCD,KAAA,CAAAE,MAAA,CAAOhD,KAAA,CAAMiD,QAAA,GAAW;IAE9B,IAAI,KAAK9F,OAAA,EAAS;MAEV2F,KAAA,CAAAE,MAAA,CAAOhD,KAAA,CAAMkD,OAAA,GAAU;IAAA,OAExB;MAECJ,KAAA,CAAAE,MAAA,CAAOhD,KAAA,CAAMkD,OAAA,GAAU;MAC7B,IAAI,KAAK9F,UAAA,EAAY;QACb0F,KAAA,CAAAE,MAAA,CAAOhD,KAAA,CAAMmD,GAAA,GAAM;QACzBL,KAAA,CAAME,MAAA,CAAOhD,KAAA,CAAMoD,IAAA,GAAOL,MAAA,GAASD,KAAA,CAAMO,KAAA,GAAQP,KAAA,CAAMQ,EAAA,GAAK;MAAA,OACvD;QACCR,KAAA,CAAAE,MAAA,CAAOhD,KAAA,CAAMoD,IAAA,GAAO;QAC1BN,KAAA,CAAME,MAAA,CAAOhD,KAAA,CAAMmD,GAAA,GAAMJ,MAAA,GAASD,KAAA,CAAMS,MAAA,GAAST,KAAA,CAAMQ,EAAA,GAAK;MAE9D;IACF;EAEF;EACAzD,SAASiD,KAAA,EAAcC,MAAA,EAAgB;IAErC,IAAID,KAAA,CAAME,MAAA,EAAQ;MAEX,KAAAlE,GAAA,CAAI0E,WAAA,CAAYV,KAAA,CAAME,MAAM;MAE5B,KAAA9D,WAAA,CAAY4D,KAAA,EAAOC,MAAM;IAEhC;IAEO,OAAAD,KAAA;EAET;EAEAjE,UAAU4E,EAAA,EAAY;IAEpB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5E,GAAA,CAAIC,QAAA,CAASC,MAAA,EAAQ0E,CAAA,IAAK;MACjD,MAAMC,KAAA,GAAQ,KAAK7E,GAAA,CAAIC,QAAA,CAAS2E,CAAC;MAEjCC,KAAA,CAAM3D,KAAA,CAAMkD,OAAA,GAAUQ,CAAA,KAAMD,EAAA,GAAK,UAAU;IAE7C;IAEA,KAAKpG,IAAA,GAAOoG,EAAA;EAEd;EAEArB,kBAAA,EAAoB;IAGlB,IAAI,CAAC,KAAK9E,EAAA,IAAM,CAAC,KAAKC,GAAA,EAAK;IAE3B,KAAKQ,gBAAA,GAAmB;IAExB,KAAKN,UAAA,CAAWmG,OAAA,CAAQ,CAACC,SAAA,EAAWC,KAAA,KAAU;MAC5C,IAAI,KAAKxG,EAAA,EAAI;QACL,MAAAyG,SAAA,GAAY,KAAKzG,EAAA,CAAG0G,iBAAA,CAAkBH,SAAA,CAAU3B,KAAA,EAAO,KAAK5E,EAAA,CAAG2G,sBAAsB;QAC3F,MAAMC,QAAA,GAAW,KAAK5G,EAAA,CAAG6G,YAAA,CAAa,KAAK5G,GAAA,CAAI6G,gBAAgB;QAE3D,IAAAL,SAAA,IAAa,CAACG,QAAA,EAAU;UACpB,MAAAG,OAAA,GAAU,KAAK/G,EAAA,CAAG0G,iBAAA,CAAkBH,SAAA,CAAU3B,KAAA,EAAO,KAAK5E,EAAA,CAAGgH,YAAY;UAC/E,MAAMC,QAAA,GAAWF,OAAA,GAAU;UAC3B,KAAKtG,gBAAA,IAAoBwG,QAAA;UACpB,KAAAjH,EAAA,CAAGkH,WAAA,CAAYX,SAAA,CAAU3B,KAAK;UAC9B,KAAAzE,UAAA,CAAWgH,MAAA,CAAOX,KAAA,EAAO,CAAC;QACjC;MACF;IAAA,CACD;EAEH;EAEAjB,YAAA,EAAc;IAEP,KAAAlF,MAAA;IACC,MAAA+G,IAAA,IAAQjF,WAAA,IAAekF,IAAA,EAAMjF,GAAA,CAAI;IACjC,MAAA2E,OAAA,GAAUK,IAAA,GAAO,KAAK/E,QAAA;IAG5B,IAAI+E,IAAA,IAAQ,KAAK9E,WAAA,GAAc,MAAO,KAAK7C,aAAA,EAAe;MAExD,MAAM6H,GAAA,GAAMC,IAAA,CAAKC,KAAA,CAAO,KAAKnH,MAAA,GAAS,MAAQ0G,OAAO;MAGhD,KAAAzB,YAAA,CAAagC,GAAA,EAAK,KAAKxG,UAAU;MAGtC,KAAK2G,WAAA,CAAY,KAAK5F,QAAA,EAAU,KAAKf,UAAA,EAAY,CAAC;MAClD,KAAK2G,WAAA,CAAY,KAAK3F,OAAA,EAAS,KAAKb,UAAA,EAAY,KAAKrB,SAAS;MAC9D,KAAK6H,WAAA,CAAY,KAAK7G,QAAA,EAAU,KAAKM,UAAA,EAAY,KAAKtB,SAAS;MAE/D,IAAI,KAAKiB,eAAA,EAAiB;QACxB,KAAK4G,WAAA,CAAY,KAAK5G,eAAA,EAAiB,KAAKM,iBAAiB;MAC/D;MAGA,KAAKd,MAAA,GAAS;MACd,KAAKiC,WAAA,GAAc8E,IAAA;MACnB,KAAK/E,QAAA,GAAW+E,IAAA;IAClB;IAEO,OAAAA,IAAA;EAET;EAEA9B,aAAaoC,KAAA,EAAeC,YAAA,EAA0C;IAEvDA,YAAA,CAAA5G,IAAA,CAAK4D,IAAA,CAAK+C,KAAK;IAC5B,IAAIC,YAAA,CAAa5G,IAAA,CAAKW,MAAA,GAAS,KAAKhC,UAAA,EAAY;MAE9CiI,YAAA,CAAa5G,IAAA,CAAK6G,KAAA;IAEpB;IAEaD,YAAA,CAAA3G,KAAA,CAAM2D,IAAA,CAAK+C,KAAK;IAC7B,IAAIC,YAAA,CAAa3G,KAAA,CAAMU,MAAA,GAAS,KAAK/B,YAAA,EAAc;MAEjDgI,YAAA,CAAa3G,KAAA,CAAM4G,KAAA;IAErB;EAEF;EAEAvD,eAAewD,MAAA,EAAgB;IAE7B,IAAIhF,MAAA,CAAOV,WAAA,EAAa;MAEfU,MAAA,CAAAV,WAAA,CAAY2F,IAAA,CAAKD,MAAM;MAC9B,KAAKtH,qBAAA,GAAwB;IAE/B;EAEF;EAEAyE,aAAa+C,WAAA,EAA6DC,SAAA,EAA+BC,WAAA,EAAqB;IAE5H,IAAIpF,MAAA,CAAOV,WAAA,IAAe6F,SAAA,IAAa,KAAKzH,qBAAA,EAAuB;MAE1DsC,MAAA,CAAAV,WAAA,CAAY2F,IAAA,CAAKE,SAAS;MACjC,MAAME,UAAA,GAAa/F,WAAA,CAAYgG,OAAA,CAAQF,WAAA,EAAaF,WAAA,EAAaC,SAAS;MAC1E,KAAKxH,gBAAA,IAAoB0H,UAAA,CAAWjB,QAAA;MACpC,KAAK1G,qBAAA,GAAwB;IAE/B;EAEF;EAEAkH,YAAYjC,KAAA,EAAgCmC,YAAA,EAAmD/H,SAAA,GAAY,GAAG;IAExG,IAAA+H,YAAA,CAAa5G,IAAA,CAAKW,MAAA,GAAS,GAAG;MAEhC,IAAI0G,MAAA,GAAS;MACb,IAAIC,GAAA,GAAM;MAEV,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIuB,YAAA,CAAa5G,IAAA,CAAKW,MAAA,EAAQ0E,CAAA,IAAK;QAEvCgC,MAAA,IAAAT,YAAA,CAAa5G,IAAA,CAAKqF,CAAC;QAE7B,IAAIuB,YAAA,CAAa5G,IAAA,CAAKqF,CAAC,IAAIiC,GAAA,EAAK;UACxBA,GAAA,GAAAV,YAAA,CAAa5G,IAAA,CAAKqF,CAAC;QAC3B;MAEF;MAEA,IAAIkC,QAAA,GAAW;MACf,IAAIC,QAAA,GAAW;MACf,SAASnC,CAAA,GAAI,GAAGA,CAAA,GAAIuB,YAAA,CAAa3G,KAAA,CAAMU,MAAA,EAAQ0E,CAAA,IAAK;QAEtCkC,QAAA,IAAAX,YAAA,CAAa3G,KAAA,CAAMoF,CAAC;QAEhC,IAAIuB,YAAA,CAAa3G,KAAA,CAAMoF,CAAC,IAAImC,QAAA,EAAU;UACzBA,QAAA,GAAAZ,YAAA,CAAa3G,KAAA,CAAMoF,CAAC;QACjC;MAEF;MAEA,IAAIZ,KAAA,EAAO;QACHA,KAAA,CAAAX,MAAA,CAAOuD,MAAA,GAASb,IAAA,CAAKiB,GAAA,CAAIb,YAAA,CAAa5G,IAAA,CAAKW,MAAA,EAAQ,KAAKhC,UAAU,GAAG4I,QAAA,GAAWf,IAAA,CAAKiB,GAAA,CAAIb,YAAA,CAAa3G,KAAA,CAAMU,MAAA,EAAQ,KAAK/B,YAAY,GAAG0I,GAAA,EAAKE,QAAA,EAAU3I,SAAS;MACxK;IAEF;EACF;EAEA,IAAI6I,WAAA,EAAa;IAEf,OAAO,KAAKjH,GAAA;EAEd;EAEA+B,mBAAmBF,QAAA,EAAe;IAGhC,MAAMqF,oBAAA,GAAuBrF,QAAA,CAAS8B,MAAA;IAGtC,MAAMwD,aAAA,GAAgB;IAGbtF,QAAA,CAAA8B,MAAA,GAAS,UAAUyD,KAAA,EAAoBC,MAAA,EAAsB;MAGpEF,aAAA,CAAcvE,KAAA,CAAM;MAGCsE,oBAAA,CAAAI,IAAA,CAAK,MAAMF,KAAA,EAAOC,MAAM;MAE7CF,aAAA,CAAcjE,GAAA,CAAI;IAAA;IAIpB,KAAKtE,oBAAA,GAAuB;EAE9B;AACF;AAjdMf,MAAA,CAwCGmD,KAAA,GAAQA,KAAA;AAxCjB,IAAMuG,KAAA,GAAN1J,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}