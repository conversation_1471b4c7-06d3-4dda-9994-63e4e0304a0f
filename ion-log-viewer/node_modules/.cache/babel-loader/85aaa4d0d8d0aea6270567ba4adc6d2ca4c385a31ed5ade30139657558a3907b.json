{"ast": null, "code": "import { Group, Mesh } from \"three\";\nconst SceneUtils = {\n  createMeshesFromInstancedMesh: function (instancedMesh) {\n    const group = new Group();\n    const count = instancedMesh.count;\n    const geometry = instancedMesh.geometry;\n    const material = instancedMesh.material;\n    for (let i = 0; i < count; i++) {\n      const mesh = new Mesh(geometry, material);\n      instancedMesh.getMatrixAt(i, mesh.matrix);\n      mesh.matrix.decompose(mesh.position, mesh.quaternion, mesh.scale);\n      group.add(mesh);\n    }\n    group.copy(instancedMesh);\n    group.updateMatrixWorld();\n    return group;\n  },\n  createMultiMaterialObject: function (geometry, materials) {\n    const group = new Group();\n    for (let i = 0, l = materials.length; i < l; i++) {\n      group.add(new Mesh(geometry, materials[i]));\n    }\n    return group;\n  },\n  detach: function (child, parent, scene) {\n    console.warn(\"THREE.SceneUtils: detach() has been deprecated. Use scene.attach( child ) instead.\");\n    scene.attach(child);\n  },\n  attach: function (child, scene, parent) {\n    console.warn(\"THREE.SceneUtils: attach() has been deprecated. Use parent.attach( child ) instead.\");\n    parent.attach(child);\n  }\n};\nexport { SceneUtils };", "map": {"version": 3, "names": ["SceneUtils", "createMeshesFromInstancedMesh", "instanced<PERSON><PERSON>", "group", "Group", "count", "geometry", "material", "i", "mesh", "<PERSON><PERSON>", "getMatrixAt", "matrix", "decompose", "position", "quaternion", "scale", "add", "copy", "updateMatrixWorld", "createMultiMaterialObject", "materials", "l", "length", "detach", "child", "parent", "scene", "console", "warn", "attach"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/utils/SceneUtils.ts"], "sourcesContent": ["import { Group, Mesh } from 'three'\nimport type { BufferGeometry, InstancedMesh, Material, Object3D, Scene } from 'three'\n\nconst SceneUtils = {\n  createMeshesFromInstancedMesh: function (instancedMesh: InstancedMesh): Group {\n    const group = new Group()\n\n    const count = instancedMesh.count\n    const geometry = instancedMesh.geometry\n    const material = instancedMesh.material\n\n    for (let i = 0; i < count; i++) {\n      const mesh = new Mesh(geometry, material)\n\n      instancedMesh.getMatrixAt(i, mesh.matrix)\n      mesh.matrix.decompose(mesh.position, mesh.quaternion, mesh.scale)\n\n      group.add(mesh)\n    }\n\n    group.copy((instancedMesh as unknown) as Group)\n    group.updateMatrixWorld() // ensure correct world matrices of meshes\n\n    return group\n  },\n\n  createMultiMaterialObject: function (geometry: BufferGeometry, materials: Material[]): Group {\n    const group = new Group()\n\n    for (let i = 0, l = materials.length; i < l; i++) {\n      group.add(new Mesh(geometry, materials[i]))\n    }\n\n    return group\n  },\n\n  detach: function (child: Object3D, parent: Object3D, scene: Scene): void {\n    console.warn('THREE.SceneUtils: detach() has been deprecated. Use scene.attach( child ) instead.')\n\n    scene.attach(child)\n  },\n\n  attach: function (child: Object3D, scene: Scene, parent: Object3D): void {\n    console.warn('THREE.SceneUtils: attach() has been deprecated. Use parent.attach( child ) instead.')\n\n    parent.attach(child)\n  },\n}\n\nexport { SceneUtils }\n"], "mappings": ";AAGA,MAAMA,UAAA,GAAa;EACjBC,6BAAA,EAA+B,SAAAA,CAAUC,aAAA,EAAqC;IACtE,MAAAC,KAAA,GAAQ,IAAIC,KAAA;IAElB,MAAMC,KAAA,GAAQH,aAAA,CAAcG,KAAA;IAC5B,MAAMC,QAAA,GAAWJ,aAAA,CAAcI,QAAA;IAC/B,MAAMC,QAAA,GAAWL,aAAA,CAAcK,QAAA;IAE/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,KAAA,EAAOG,CAAA,IAAK;MAC9B,MAAMC,IAAA,GAAO,IAAIC,IAAA,CAAKJ,QAAA,EAAUC,QAAQ;MAE1BL,aAAA,CAAAS,WAAA,CAAYH,CAAA,EAAGC,IAAA,CAAKG,MAAM;MACxCH,IAAA,CAAKG,MAAA,CAAOC,SAAA,CAAUJ,IAAA,CAAKK,QAAA,EAAUL,IAAA,CAAKM,UAAA,EAAYN,IAAA,CAAKO,KAAK;MAEhEb,KAAA,CAAMc,GAAA,CAAIR,IAAI;IAChB;IAEAN,KAAA,CAAMe,IAAA,CAAMhB,aAAkC;IAC9CC,KAAA,CAAMgB,iBAAA,CAAkB;IAEjB,OAAAhB,KAAA;EACT;EAEAiB,yBAAA,EAA2B,SAAAA,CAAUd,QAAA,EAA0Be,SAAA,EAA8B;IACrF,MAAAlB,KAAA,GAAQ,IAAIC,KAAA;IAElB,SAASI,CAAA,GAAI,GAAGc,CAAA,GAAID,SAAA,CAAUE,MAAA,EAAQf,CAAA,GAAIc,CAAA,EAAGd,CAAA,IAAK;MAChDL,KAAA,CAAMc,GAAA,CAAI,IAAIP,IAAA,CAAKJ,QAAA,EAAUe,SAAA,CAAUb,CAAC,CAAC,CAAC;IAC5C;IAEO,OAAAL,KAAA;EACT;EAEAqB,MAAA,EAAQ,SAAAA,CAAUC,KAAA,EAAiBC,MAAA,EAAkBC,KAAA,EAAoB;IACvEC,OAAA,CAAQC,IAAA,CAAK,oFAAoF;IAEjGF,KAAA,CAAMG,MAAA,CAAOL,KAAK;EACpB;EAEAK,MAAA,EAAQ,SAAAA,CAAUL,KAAA,EAAiBE,KAAA,EAAcD,MAAA,EAAwB;IACvEE,OAAA,CAAQC,IAAA,CAAK,qFAAqF;IAElGH,MAAA,CAAOI,MAAA,CAAOL,KAAK;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}