{"ast": null, "code": "import { Vector3, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bone, Object3D, MeshBasicMaterial, Color, Mesh, BoxGeometry, SphereGeometry } from \"three\";\nimport { CapsuleGeometry } from \"../_polyfill/CapsuleGeometry.js\";\nclass MMDPhysics {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} rigidBodyParams\n   * @param {Array<Object>} (optional) constraintParams\n   * @param {Object} params - (optional)\n   * @param {Number} params.unitStep - Default is 1 / 65.\n   * @param {Integer} params.maxStepNum - Default is 3.\n   * @param {Vector3} params.gravity - Default is ( 0, - 9.8 * 10, 0 )\n   */\n  constructor(mesh, rigidBodyParams, constraintParams = [], params = {}) {\n    if (typeof Ammo === \"undefined\") {\n      throw new Error(\"THREE.MMDPhysics: Import ammo.js https://github.com/kripken/ammo.js\");\n    }\n    this.manager = new ResourceManager();\n    this.mesh = mesh;\n    this.unitStep = params.unitStep !== void 0 ? params.unitStep : 1 / 65;\n    this.maxStepNum = params.maxStepNum !== void 0 ? params.maxStepNum : 3;\n    this.gravity = new Vector3(0, -9.8 * 10, 0);\n    if (params.gravity !== void 0) this.gravity.copy(params.gravity);\n    this.world = params.world !== void 0 ? params.world : null;\n    this.bodies = [];\n    this.constraints = [];\n    this._init(mesh, rigidBodyParams, constraintParams);\n  }\n  /**\n   * Advances Physics calculation and updates bones.\n   *\n   * @param {Number} delta - time in second\n   * @return {MMDPhysics}\n   */\n  update(delta) {\n    const manager = this.manager;\n    const mesh = this.mesh;\n    let isNonDefaultScale = false;\n    const position = manager.allocThreeVector3();\n    const quaternion = manager.allocThreeQuaternion();\n    const scale = manager.allocThreeVector3();\n    mesh.matrixWorld.decompose(position, quaternion, scale);\n    if (scale.x !== 1 || scale.y !== 1 || scale.z !== 1) {\n      isNonDefaultScale = true;\n    }\n    let parent;\n    if (isNonDefaultScale) {\n      parent = mesh.parent;\n      if (parent !== null) mesh.parent = null;\n      scale.copy(this.mesh.scale);\n      mesh.scale.set(1, 1, 1);\n      mesh.updateMatrixWorld(true);\n    }\n    this._updateRigidBodies();\n    this._stepSimulation(delta);\n    this._updateBones();\n    if (isNonDefaultScale) {\n      if (parent !== null) mesh.parent = parent;\n      mesh.scale.copy(scale);\n    }\n    manager.freeThreeVector3(scale);\n    manager.freeThreeQuaternion(quaternion);\n    manager.freeThreeVector3(position);\n    return this;\n  }\n  /**\n   * Resets rigid bodies transorm to current bone's.\n   *\n   * @return {MMDPhysics}\n   */\n  reset() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].reset();\n    }\n    return this;\n  }\n  /**\n   * Warm ups Rigid bodies. Calculates cycles steps.\n   *\n   * @param {Integer} cycles\n   * @return {MMDPhysics}\n   */\n  warmup(cycles) {\n    for (let i = 0; i < cycles; i++) {\n      this.update(1 / 60);\n    }\n    return this;\n  }\n  /**\n   * Sets gravity.\n   *\n   * @param {Vector3} gravity\n   * @return {MMDPhysicsHelper}\n   */\n  setGravity(gravity) {\n    this.world.setGravity(new Ammo.btVector3(gravity.x, gravity.y, gravity.z));\n    this.gravity.copy(gravity);\n    return this;\n  }\n  /**\n   * Creates MMDPhysicsHelper\n   *\n   * @return {MMDPhysicsHelper}\n   */\n  createHelper() {\n    return new MMDPhysicsHelper(this.mesh, this);\n  }\n  // private methods\n  _init(mesh, rigidBodyParams, constraintParams) {\n    const manager = this.manager;\n    const parent = mesh.parent;\n    if (parent !== null) mesh.parent = null;\n    const currentPosition = manager.allocThreeVector3();\n    const currentQuaternion = manager.allocThreeQuaternion();\n    const currentScale = manager.allocThreeVector3();\n    currentPosition.copy(mesh.position);\n    currentQuaternion.copy(mesh.quaternion);\n    currentScale.copy(mesh.scale);\n    mesh.position.set(0, 0, 0);\n    mesh.quaternion.set(0, 0, 0, 1);\n    mesh.scale.set(1, 1, 1);\n    mesh.updateMatrixWorld(true);\n    if (this.world === null) {\n      this.world = this._createWorld();\n      this.setGravity(this.gravity);\n    }\n    this._initRigidBodies(rigidBodyParams);\n    this._initConstraints(constraintParams);\n    if (parent !== null) mesh.parent = parent;\n    mesh.position.copy(currentPosition);\n    mesh.quaternion.copy(currentQuaternion);\n    mesh.scale.copy(currentScale);\n    mesh.updateMatrixWorld(true);\n    this.reset();\n    manager.freeThreeVector3(currentPosition);\n    manager.freeThreeQuaternion(currentQuaternion);\n    manager.freeThreeVector3(currentScale);\n  }\n  _createWorld() {\n    const config = new Ammo.btDefaultCollisionConfiguration();\n    const dispatcher = new Ammo.btCollisionDispatcher(config);\n    const cache = new Ammo.btDbvtBroadphase();\n    const solver = new Ammo.btSequentialImpulseConstraintSolver();\n    const world = new Ammo.btDiscreteDynamicsWorld(dispatcher, cache, solver, config);\n    return world;\n  }\n  _initRigidBodies(rigidBodies) {\n    for (let i = 0, il = rigidBodies.length; i < il; i++) {\n      this.bodies.push(new RigidBody(this.mesh, this.world, rigidBodies[i], this.manager));\n    }\n  }\n  _initConstraints(constraints) {\n    for (let i = 0, il = constraints.length; i < il; i++) {\n      const params = constraints[i];\n      const bodyA = this.bodies[params.rigidBodyIndex1];\n      const bodyB = this.bodies[params.rigidBodyIndex2];\n      this.constraints.push(new Constraint(this.mesh, this.world, bodyA, bodyB, params, this.manager));\n    }\n  }\n  _stepSimulation(delta) {\n    const unitStep = this.unitStep;\n    let stepTime = delta;\n    let maxStepNum = (delta / unitStep | 0) + 1;\n    if (stepTime < unitStep) {\n      stepTime = unitStep;\n      maxStepNum = 1;\n    }\n    if (maxStepNum > this.maxStepNum) {\n      maxStepNum = this.maxStepNum;\n    }\n    this.world.stepSimulation(stepTime, maxStepNum, unitStep);\n  }\n  _updateRigidBodies() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateFromBone();\n    }\n  }\n  _updateBones() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateBone();\n    }\n  }\n}\nclass ResourceManager {\n  constructor() {\n    this.threeVector3s = [];\n    this.threeMatrix4s = [];\n    this.threeQuaternions = [];\n    this.threeEulers = [];\n    this.transforms = [];\n    this.quaternions = [];\n    this.vector3s = [];\n  }\n  allocThreeVector3() {\n    return this.threeVector3s.length > 0 ? this.threeVector3s.pop() : new Vector3();\n  }\n  freeThreeVector3(v) {\n    this.threeVector3s.push(v);\n  }\n  allocThreeMatrix4() {\n    return this.threeMatrix4s.length > 0 ? this.threeMatrix4s.pop() : new Matrix4();\n  }\n  freeThreeMatrix4(m) {\n    this.threeMatrix4s.push(m);\n  }\n  allocThreeQuaternion() {\n    return this.threeQuaternions.length > 0 ? this.threeQuaternions.pop() : new Quaternion();\n  }\n  freeThreeQuaternion(q) {\n    this.threeQuaternions.push(q);\n  }\n  allocThreeEuler() {\n    return this.threeEulers.length > 0 ? this.threeEulers.pop() : new Euler();\n  }\n  freeThreeEuler(e) {\n    this.threeEulers.push(e);\n  }\n  allocTransform() {\n    return this.transforms.length > 0 ? this.transforms.pop() : new Ammo.btTransform();\n  }\n  freeTransform(t) {\n    this.transforms.push(t);\n  }\n  allocQuaternion() {\n    return this.quaternions.length > 0 ? this.quaternions.pop() : new Ammo.btQuaternion();\n  }\n  freeQuaternion(q) {\n    this.quaternions.push(q);\n  }\n  allocVector3() {\n    return this.vector3s.length > 0 ? this.vector3s.pop() : new Ammo.btVector3();\n  }\n  freeVector3(v) {\n    this.vector3s.push(v);\n  }\n  setIdentity(t) {\n    t.setIdentity();\n  }\n  getBasis(t) {\n    var q = this.allocQuaternion();\n    t.getBasis().getRotation(q);\n    return q;\n  }\n  getBasisAsMatrix3(t) {\n    var q = this.getBasis(t);\n    var m = this.quaternionToMatrix3(q);\n    this.freeQuaternion(q);\n    return m;\n  }\n  getOrigin(t) {\n    return t.getOrigin();\n  }\n  setOrigin(t, v) {\n    t.getOrigin().setValue(v.x(), v.y(), v.z());\n  }\n  copyOrigin(t1, t2) {\n    var o = t2.getOrigin();\n    this.setOrigin(t1, o);\n  }\n  setBasis(t, q) {\n    t.setRotation(q);\n  }\n  setBasisFromMatrix3(t, m) {\n    var q = this.matrix3ToQuaternion(m);\n    this.setBasis(t, q);\n    this.freeQuaternion(q);\n  }\n  setOriginFromArray3(t, a) {\n    t.getOrigin().setValue(a[0], a[1], a[2]);\n  }\n  setOriginFromThreeVector3(t, v) {\n    t.getOrigin().setValue(v.x, v.y, v.z);\n  }\n  setBasisFromArray3(t, a) {\n    var thQ = this.allocThreeQuaternion();\n    var thE = this.allocThreeEuler();\n    thE.set(a[0], a[1], a[2]);\n    this.setBasisFromThreeQuaternion(t, thQ.setFromEuler(thE));\n    this.freeThreeEuler(thE);\n    this.freeThreeQuaternion(thQ);\n  }\n  setBasisFromThreeQuaternion(t, a) {\n    var q = this.allocQuaternion();\n    q.setX(a.x);\n    q.setY(a.y);\n    q.setZ(a.z);\n    q.setW(a.w);\n    this.setBasis(t, q);\n    this.freeQuaternion(q);\n  }\n  multiplyTransforms(t1, t2) {\n    var t = this.allocTransform();\n    this.setIdentity(t);\n    var m1 = this.getBasisAsMatrix3(t1);\n    var m2 = this.getBasisAsMatrix3(t2);\n    var o1 = this.getOrigin(t1);\n    var o2 = this.getOrigin(t2);\n    var v1 = this.multiplyMatrix3ByVector3(m1, o2);\n    var v2 = this.addVector3(v1, o1);\n    this.setOrigin(t, v2);\n    var m3 = this.multiplyMatrices3(m1, m2);\n    this.setBasisFromMatrix3(t, m3);\n    this.freeVector3(v1);\n    this.freeVector3(v2);\n    return t;\n  }\n  inverseTransform(t) {\n    var t2 = this.allocTransform();\n    var m1 = this.getBasisAsMatrix3(t);\n    var o = this.getOrigin(t);\n    var m2 = this.transposeMatrix3(m1);\n    var v1 = this.negativeVector3(o);\n    var v2 = this.multiplyMatrix3ByVector3(m2, v1);\n    this.setOrigin(t2, v2);\n    this.setBasisFromMatrix3(t2, m2);\n    this.freeVector3(v1);\n    this.freeVector3(v2);\n    return t2;\n  }\n  multiplyMatrices3(m1, m2) {\n    var m3 = [];\n    var v10 = this.rowOfMatrix3(m1, 0);\n    var v11 = this.rowOfMatrix3(m1, 1);\n    var v12 = this.rowOfMatrix3(m1, 2);\n    var v20 = this.columnOfMatrix3(m2, 0);\n    var v21 = this.columnOfMatrix3(m2, 1);\n    var v22 = this.columnOfMatrix3(m2, 2);\n    m3[0] = this.dotVectors3(v10, v20);\n    m3[1] = this.dotVectors3(v10, v21);\n    m3[2] = this.dotVectors3(v10, v22);\n    m3[3] = this.dotVectors3(v11, v20);\n    m3[4] = this.dotVectors3(v11, v21);\n    m3[5] = this.dotVectors3(v11, v22);\n    m3[6] = this.dotVectors3(v12, v20);\n    m3[7] = this.dotVectors3(v12, v21);\n    m3[8] = this.dotVectors3(v12, v22);\n    this.freeVector3(v10);\n    this.freeVector3(v11);\n    this.freeVector3(v12);\n    this.freeVector3(v20);\n    this.freeVector3(v21);\n    this.freeVector3(v22);\n    return m3;\n  }\n  addVector3(v1, v2) {\n    var v = this.allocVector3();\n    v.setValue(v1.x() + v2.x(), v1.y() + v2.y(), v1.z() + v2.z());\n    return v;\n  }\n  dotVectors3(v1, v2) {\n    return v1.x() * v2.x() + v1.y() * v2.y() + v1.z() * v2.z();\n  }\n  rowOfMatrix3(m, i) {\n    var v = this.allocVector3();\n    v.setValue(m[i * 3 + 0], m[i * 3 + 1], m[i * 3 + 2]);\n    return v;\n  }\n  columnOfMatrix3(m, i) {\n    var v = this.allocVector3();\n    v.setValue(m[i + 0], m[i + 3], m[i + 6]);\n    return v;\n  }\n  negativeVector3(v) {\n    var v2 = this.allocVector3();\n    v2.setValue(-v.x(), -v.y(), -v.z());\n    return v2;\n  }\n  multiplyMatrix3ByVector3(m, v) {\n    var v4 = this.allocVector3();\n    var v0 = this.rowOfMatrix3(m, 0);\n    var v1 = this.rowOfMatrix3(m, 1);\n    var v2 = this.rowOfMatrix3(m, 2);\n    var x = this.dotVectors3(v0, v);\n    var y = this.dotVectors3(v1, v);\n    var z = this.dotVectors3(v2, v);\n    v4.setValue(x, y, z);\n    this.freeVector3(v0);\n    this.freeVector3(v1);\n    this.freeVector3(v2);\n    return v4;\n  }\n  transposeMatrix3(m) {\n    var m2 = [];\n    m2[0] = m[0];\n    m2[1] = m[3];\n    m2[2] = m[6];\n    m2[3] = m[1];\n    m2[4] = m[4];\n    m2[5] = m[7];\n    m2[6] = m[2];\n    m2[7] = m[5];\n    m2[8] = m[8];\n    return m2;\n  }\n  quaternionToMatrix3(q) {\n    var m = [];\n    var x = q.x();\n    var y = q.y();\n    var z = q.z();\n    var w = q.w();\n    var xx = x * x;\n    var yy = y * y;\n    var zz = z * z;\n    var xy = x * y;\n    var yz = y * z;\n    var zx = z * x;\n    var xw = x * w;\n    var yw = y * w;\n    var zw = z * w;\n    m[0] = 1 - 2 * (yy + zz);\n    m[1] = 2 * (xy - zw);\n    m[2] = 2 * (zx + yw);\n    m[3] = 2 * (xy + zw);\n    m[4] = 1 - 2 * (zz + xx);\n    m[5] = 2 * (yz - xw);\n    m[6] = 2 * (zx - yw);\n    m[7] = 2 * (yz + xw);\n    m[8] = 1 - 2 * (xx + yy);\n    return m;\n  }\n  matrix3ToQuaternion(m) {\n    var t = m[0] + m[4] + m[8];\n    var s, x, y, z, w;\n    if (t > 0) {\n      s = Math.sqrt(t + 1) * 2;\n      w = 0.25 * s;\n      x = (m[7] - m[5]) / s;\n      y = (m[2] - m[6]) / s;\n      z = (m[3] - m[1]) / s;\n    } else if (m[0] > m[4] && m[0] > m[8]) {\n      s = Math.sqrt(1 + m[0] - m[4] - m[8]) * 2;\n      w = (m[7] - m[5]) / s;\n      x = 0.25 * s;\n      y = (m[1] + m[3]) / s;\n      z = (m[2] + m[6]) / s;\n    } else if (m[4] > m[8]) {\n      s = Math.sqrt(1 + m[4] - m[0] - m[8]) * 2;\n      w = (m[2] - m[6]) / s;\n      x = (m[1] + m[3]) / s;\n      y = 0.25 * s;\n      z = (m[5] + m[7]) / s;\n    } else {\n      s = Math.sqrt(1 + m[8] - m[0] - m[4]) * 2;\n      w = (m[3] - m[1]) / s;\n      x = (m[2] + m[6]) / s;\n      y = (m[5] + m[7]) / s;\n      z = 0.25 * s;\n    }\n    var q = this.allocQuaternion();\n    q.setX(x);\n    q.setY(y);\n    q.setZ(z);\n    q.setW(w);\n    return q;\n  }\n}\nclass RigidBody {\n  constructor(mesh, world, params, manager) {\n    this.mesh = mesh;\n    this.world = world;\n    this.params = params;\n    this.manager = manager;\n    this.body = null;\n    this.bone = null;\n    this.boneOffsetForm = null;\n    this.boneOffsetFormInverse = null;\n    this._init();\n  }\n  /**\n   * Resets rigid body transform to the current bone's.\n   *\n   * @return {RigidBody}\n   */\n  reset() {\n    this._setTransformFromBone();\n    return this;\n  }\n  /**\n   * Updates rigid body's transform from the current bone.\n   *\n   * @return {RidigBody}\n   */\n  updateFromBone() {\n    if (this.params.boneIndex !== -1 && this.params.type === 0) {\n      this._setTransformFromBone();\n    }\n    return this;\n  }\n  /**\n   * Updates bone from the current ridid body's transform.\n   *\n   * @return {RidigBody}\n   */\n  updateBone() {\n    if (this.params.type === 0 || this.params.boneIndex === -1) {\n      return this;\n    }\n    this._updateBoneRotation();\n    if (this.params.type === 1) {\n      this._updateBonePosition();\n    }\n    this.bone.updateMatrixWorld(true);\n    if (this.params.type === 2) {\n      this._setPositionFromBone();\n    }\n    return this;\n  }\n  // private methods\n  _init() {\n    function generateShape(p) {\n      switch (p.shapeType) {\n        case 0:\n          return new Ammo.btSphereShape(p.width);\n        case 1:\n          return new Ammo.btBoxShape(new Ammo.btVector3(p.width, p.height, p.depth));\n        case 2:\n          return new Ammo.btCapsuleShape(p.width, p.height);\n        default:\n          throw new Error(\"unknown shape type \" + p.shapeType);\n      }\n    }\n    const manager = this.manager;\n    const params = this.params;\n    const bones = this.mesh.skeleton.bones;\n    const bone = params.boneIndex === -1 ? new Bone() : bones[params.boneIndex];\n    const shape = generateShape(params);\n    const weight = params.type === 0 ? 0 : params.weight;\n    const localInertia = manager.allocVector3();\n    localInertia.setValue(0, 0, 0);\n    if (weight !== 0) {\n      shape.calculateLocalInertia(weight, localInertia);\n    }\n    const boneOffsetForm = manager.allocTransform();\n    manager.setIdentity(boneOffsetForm);\n    manager.setOriginFromArray3(boneOffsetForm, params.position);\n    manager.setBasisFromArray3(boneOffsetForm, params.rotation);\n    const vector = manager.allocThreeVector3();\n    const boneForm = manager.allocTransform();\n    manager.setIdentity(boneForm);\n    manager.setOriginFromThreeVector3(boneForm, bone.getWorldPosition(vector));\n    const form = manager.multiplyTransforms(boneForm, boneOffsetForm);\n    const state = new Ammo.btDefaultMotionState(form);\n    const info = new Ammo.btRigidBodyConstructionInfo(weight, state, shape, localInertia);\n    info.set_m_friction(params.friction);\n    info.set_m_restitution(params.restitution);\n    const body = new Ammo.btRigidBody(info);\n    if (params.type === 0) {\n      body.setCollisionFlags(body.getCollisionFlags() | 2);\n      body.setActivationState(4);\n    }\n    body.setDamping(params.positionDamping, params.rotationDamping);\n    body.setSleepingThresholds(0, 0);\n    this.world.addRigidBody(body, 1 << params.groupIndex, params.groupTarget);\n    this.body = body;\n    this.bone = bone;\n    this.boneOffsetForm = boneOffsetForm;\n    this.boneOffsetFormInverse = manager.inverseTransform(boneOffsetForm);\n    manager.freeVector3(localInertia);\n    manager.freeTransform(form);\n    manager.freeTransform(boneForm);\n    manager.freeThreeVector3(vector);\n  }\n  _getBoneTransform() {\n    const manager = this.manager;\n    const p = manager.allocThreeVector3();\n    const q = manager.allocThreeQuaternion();\n    const s = manager.allocThreeVector3();\n    this.bone.matrixWorld.decompose(p, q, s);\n    const tr = manager.allocTransform();\n    manager.setOriginFromThreeVector3(tr, p);\n    manager.setBasisFromThreeQuaternion(tr, q);\n    const form = manager.multiplyTransforms(tr, this.boneOffsetForm);\n    manager.freeTransform(tr);\n    manager.freeThreeVector3(s);\n    manager.freeThreeQuaternion(q);\n    manager.freeThreeVector3(p);\n    return form;\n  }\n  _getWorldTransformForBone() {\n    const manager = this.manager;\n    const tr = this.body.getCenterOfMassTransform();\n    return manager.multiplyTransforms(tr, this.boneOffsetFormInverse);\n  }\n  _setTransformFromBone() {\n    const manager = this.manager;\n    const form = this._getBoneTransform();\n    this.body.setCenterOfMassTransform(form);\n    this.body.getMotionState().setWorldTransform(form);\n    manager.freeTransform(form);\n  }\n  _setPositionFromBone() {\n    const manager = this.manager;\n    const form = this._getBoneTransform();\n    const tr = manager.allocTransform();\n    this.body.getMotionState().getWorldTransform(tr);\n    manager.copyOrigin(tr, form);\n    this.body.setCenterOfMassTransform(tr);\n    this.body.getMotionState().setWorldTransform(tr);\n    manager.freeTransform(tr);\n    manager.freeTransform(form);\n  }\n  _updateBoneRotation() {\n    const manager = this.manager;\n    const tr = this._getWorldTransformForBone();\n    const q = manager.getBasis(tr);\n    const thQ = manager.allocThreeQuaternion();\n    const thQ2 = manager.allocThreeQuaternion();\n    const thQ3 = manager.allocThreeQuaternion();\n    thQ.set(q.x(), q.y(), q.z(), q.w());\n    thQ2.setFromRotationMatrix(this.bone.matrixWorld);\n    thQ2.conjugate();\n    thQ2.multiply(thQ);\n    thQ3.setFromRotationMatrix(this.bone.matrix);\n    this.bone.quaternion.copy(thQ2.multiply(thQ3).normalize());\n    manager.freeThreeQuaternion(thQ);\n    manager.freeThreeQuaternion(thQ2);\n    manager.freeThreeQuaternion(thQ3);\n    manager.freeQuaternion(q);\n    manager.freeTransform(tr);\n  }\n  _updateBonePosition() {\n    const manager = this.manager;\n    const tr = this._getWorldTransformForBone();\n    const thV = manager.allocThreeVector3();\n    const o = manager.getOrigin(tr);\n    thV.set(o.x(), o.y(), o.z());\n    if (this.bone.parent) {\n      this.bone.parent.worldToLocal(thV);\n    }\n    this.bone.position.copy(thV);\n    manager.freeThreeVector3(thV);\n    manager.freeTransform(tr);\n  }\n}\nclass Constraint {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Ammo.btDiscreteDynamicsWorld} world\n   * @param {RigidBody} bodyA\n   * @param {RigidBody} bodyB\n   * @param {Object} params\n   * @param {ResourceManager} manager\n   */\n  constructor(mesh, world, bodyA, bodyB, params, manager) {\n    this.mesh = mesh;\n    this.world = world;\n    this.bodyA = bodyA;\n    this.bodyB = bodyB;\n    this.params = params;\n    this.manager = manager;\n    this.constraint = null;\n    this._init();\n  }\n  // private method\n  _init() {\n    const manager = this.manager;\n    const params = this.params;\n    const bodyA = this.bodyA;\n    const bodyB = this.bodyB;\n    const form = manager.allocTransform();\n    manager.setIdentity(form);\n    manager.setOriginFromArray3(form, params.position);\n    manager.setBasisFromArray3(form, params.rotation);\n    const formA = manager.allocTransform();\n    const formB = manager.allocTransform();\n    bodyA.body.getMotionState().getWorldTransform(formA);\n    bodyB.body.getMotionState().getWorldTransform(formB);\n    const formInverseA = manager.inverseTransform(formA);\n    const formInverseB = manager.inverseTransform(formB);\n    const formA2 = manager.multiplyTransforms(formInverseA, form);\n    const formB2 = manager.multiplyTransforms(formInverseB, form);\n    const constraint = new Ammo.btGeneric6DofSpringConstraint(bodyA.body, bodyB.body, formA2, formB2, true);\n    const lll = manager.allocVector3();\n    const lul = manager.allocVector3();\n    const all = manager.allocVector3();\n    const aul = manager.allocVector3();\n    lll.setValue(params.translationLimitation1[0], params.translationLimitation1[1], params.translationLimitation1[2]);\n    lul.setValue(params.translationLimitation2[0], params.translationLimitation2[1], params.translationLimitation2[2]);\n    all.setValue(params.rotationLimitation1[0], params.rotationLimitation1[1], params.rotationLimitation1[2]);\n    aul.setValue(params.rotationLimitation2[0], params.rotationLimitation2[1], params.rotationLimitation2[2]);\n    constraint.setLinearLowerLimit(lll);\n    constraint.setLinearUpperLimit(lul);\n    constraint.setAngularLowerLimit(all);\n    constraint.setAngularUpperLimit(aul);\n    for (let i = 0; i < 3; i++) {\n      if (params.springPosition[i] !== 0) {\n        constraint.enableSpring(i, true);\n        constraint.setStiffness(i, params.springPosition[i]);\n      }\n    }\n    for (let i = 0; i < 3; i++) {\n      if (params.springRotation[i] !== 0) {\n        constraint.enableSpring(i + 3, true);\n        constraint.setStiffness(i + 3, params.springRotation[i]);\n      }\n    }\n    if (constraint.setParam !== void 0) {\n      for (let i = 0; i < 6; i++) {\n        constraint.setParam(2, 0.475, i);\n      }\n    }\n    this.world.addConstraint(constraint, true);\n    this.constraint = constraint;\n    manager.freeTransform(form);\n    manager.freeTransform(formA);\n    manager.freeTransform(formB);\n    manager.freeTransform(formInverseA);\n    manager.freeTransform(formInverseB);\n    manager.freeTransform(formA2);\n    manager.freeTransform(formB2);\n    manager.freeVector3(lll);\n    manager.freeVector3(lul);\n    manager.freeVector3(all);\n    manager.freeVector3(aul);\n  }\n}\nconst _position = /* @__PURE__ */new Vector3();\nconst _quaternion = /* @__PURE__ */new Quaternion();\nconst _scale = /* @__PURE__ */new Vector3();\nconst _matrixWorldInv = /* @__PURE__ */new Matrix4();\nclass MMDPhysicsHelper extends Object3D {\n  /**\n   * Visualize Rigid bodies\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Physics} physics\n   */\n  constructor(mesh, physics) {\n    super();\n    this.root = mesh;\n    this.physics = physics;\n    this.matrix.copy(mesh.matrixWorld);\n    this.matrixAutoUpdate = false;\n    this.materials = [];\n    this.materials.push(new MeshBasicMaterial({\n      color: new Color(16746632),\n      wireframe: true,\n      depthTest: false,\n      depthWrite: false,\n      opacity: 0.25,\n      transparent: true\n    }));\n    this.materials.push(new MeshBasicMaterial({\n      color: new Color(8978312),\n      wireframe: true,\n      depthTest: false,\n      depthWrite: false,\n      opacity: 0.25,\n      transparent: true\n    }));\n    this.materials.push(new MeshBasicMaterial({\n      color: new Color(8947967),\n      wireframe: true,\n      depthTest: false,\n      depthWrite: false,\n      opacity: 0.25,\n      transparent: true\n    }));\n    this._init();\n  }\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    const materials = this.materials;\n    const children = this.children;\n    for (let i = 0; i < materials.length; i++) {\n      materials[i].dispose();\n    }\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child.isMesh) child.geometry.dispose();\n    }\n  }\n  /**\n   * Updates Rigid Bodies visualization.\n   */\n  updateMatrixWorld(force) {\n    var mesh = this.root;\n    if (this.visible) {\n      var bodies = this.physics.bodies;\n      _matrixWorldInv.copy(mesh.matrixWorld).decompose(_position, _quaternion, _scale).compose(_position, _quaternion, _scale.set(1, 1, 1)).invert();\n      for (var i = 0, il = bodies.length; i < il; i++) {\n        var body = bodies[i].body;\n        var child = this.children[i];\n        var tr = body.getCenterOfMassTransform();\n        var origin = tr.getOrigin();\n        var rotation = tr.getRotation();\n        child.position.set(origin.x(), origin.y(), origin.z()).applyMatrix4(_matrixWorldInv);\n        child.quaternion.setFromRotationMatrix(_matrixWorldInv).multiply(_quaternion.set(rotation.x(), rotation.y(), rotation.z(), rotation.w()));\n      }\n    }\n    this.matrix.copy(mesh.matrixWorld).decompose(_position, _quaternion, _scale).compose(_position, _quaternion, _scale.set(1, 1, 1));\n    super.updateMatrixWorld(force);\n  }\n  // private method\n  _init() {\n    var bodies = this.physics.bodies;\n    function createGeometry(param2) {\n      switch (param2.shapeType) {\n        case 0:\n          return new SphereGeometry(param2.width, 16, 8);\n        case 1:\n          return new BoxGeometry(param2.width * 2, param2.height * 2, param2.depth * 2, 8, 8, 8);\n        case 2:\n          return new CapsuleGeometry(param2.width, param2.height, 8, 16);\n        default:\n          return null;\n      }\n    }\n    for (var i = 0, il = bodies.length; i < il; i++) {\n      var param = bodies[i].params;\n      this.add(new Mesh(createGeometry(param), this.materials[param.type]));\n    }\n  }\n}\nexport { MMDPhysics };", "map": {"version": 3, "names": ["MMDPhysics", "constructor", "mesh", "rigidBodyParams", "constraintParams", "params", "Ammo", "Error", "manager", "ResourceManager", "unitStep", "maxStepNum", "gravity", "Vector3", "copy", "world", "bodies", "constraints", "_init", "update", "delta", "isNonDefaultScale", "position", "allocThreeVector3", "quaternion", "allocThreeQuaternion", "scale", "matrixWorld", "decompose", "x", "y", "z", "parent", "set", "updateMatrixWorld", "_updateRigidBodies", "_stepSimulation", "_updateBones", "freeThreeVector3", "freeThreeQuaternion", "reset", "i", "il", "length", "warmup", "cycles", "setGravity", "btVector3", "createHelper", "MMDPhysicsHelper", "currentPosition", "currentQuaternion", "currentScale", "_createWorld", "_initRigidBodies", "_initConstraints", "config", "btDefaultCollisionConfiguration", "dispatcher", "btCollisionDispatcher", "cache", "btDbvtBroadphase", "solver", "btSequentialImpulseConstraintSolver", "btDiscreteDynamicsWorld", "rigidBodies", "push", "RigidBody", "bodyA", "rigidBodyIndex1", "bodyB", "rigidBodyIndex2", "Constraint", "stepTime", "stepSimulation", "updateFromBone", "updateBone", "threeVector3s", "threeMatrix4s", "threeQuaternions", "threeEulers", "transforms", "quaternions", "vector3s", "pop", "v", "allocThreeMatrix4", "Matrix4", "freeThreeMatrix4", "m", "Quaternion", "q", "allocThreeEuler", "<PERSON>uler", "freeThreeEuler", "e", "allocTransform", "btTransform", "freeTransform", "t", "allocQuaternion", "btQuaternion", "freeQuaternion", "allocVector3", "freeVector3", "setIdentity", "getBasis", "getRotation", "getBasisAsMatrix3", "quaternionToMatrix3", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "copy<PERSON><PERSON>in", "t1", "t2", "o", "setBasis", "setRotation", "setBasisFromMatrix3", "matrix3ToQuaternion", "setOriginFromArray3", "a", "setOriginFromThreeVector3", "setBasisFromArray3", "thQ", "thE", "setBasisFromThreeQuaternion", "setFromEuler", "setX", "setY", "setZ", "setW", "w", "multiplyTransforms", "m1", "m2", "o1", "o2", "v1", "multiplyMatrix3ByVector3", "v2", "addVector3", "m3", "multiplyMatrices3", "inverseTransform", "transposeMatrix3", "negativeVector3", "v10", "rowOfMatrix3", "v11", "v12", "v20", "columnOfMatrix3", "v21", "v22", "dotVectors3", "v4", "v0", "xx", "yy", "zz", "xy", "yz", "zx", "xw", "yw", "zw", "s", "Math", "sqrt", "body", "bone", "boneOffsetForm", "boneOffsetFormInverse", "_setTransformFromBone", "boneIndex", "type", "_updateBoneRotation", "_updateBonePosition", "_setPositionFromBone", "generateShape", "p", "shapeType", "btSphereShape", "width", "btBoxShape", "height", "depth", "btCapsuleShape", "bones", "skeleton", "Bone", "shape", "weight", "localInertia", "calculateLocalInertia", "rotation", "vector", "boneForm", "getWorldPosition", "form", "state", "btDefaultMotionState", "info", "btRigidBodyConstructionInfo", "set_m_friction", "friction", "set_m_restitution", "restitution", "btRigidBody", "setCollisionFlags", "getCollisionFlags", "setActivationState", "setDamping", "positionDamping", "rotationDamping", "setSleepingThresholds", "addRigidBody", "groupIndex", "groupTarget", "_getBoneTransform", "tr", "_getWorldTransformForBone", "getCenterOfMassTransform", "setCenterOfMassTransform", "getMotionState", "setWorldTransform", "getWorldTransform", "thQ2", "thQ3", "setFromRotationMatrix", "conjugate", "multiply", "matrix", "normalize", "thV", "worldToLocal", "constraint", "formA", "formB", "formInverseA", "formInverseB", "formA2", "formB2", "btGeneric6DofSpringConstraint", "lll", "lul", "all", "aul", "translationLimitation1", "translationLimitation2", "rotationLimitation1", "rotationLimitation2", "setLinearLowerLimit", "setLinearUpperLimit", "setAngularLowerLimit", "setAngularUpperLimit", "springPosition", "enableSpring", "setStiffness", "springRotation", "set<PERSON>ara<PERSON>", "addConstraint", "_position", "_quaternion", "_scale", "_matrixWorldInv", "Object3D", "physics", "root", "matrixAutoUpdate", "materials", "MeshBasicMaterial", "color", "Color", "wireframe", "depthTest", "depthWrite", "opacity", "transparent", "dispose", "children", "child", "<PERSON><PERSON><PERSON>", "geometry", "force", "visible", "compose", "invert", "origin", "applyMatrix4", "createGeometry", "param2", "SphereGeometry", "BoxGeometry", "CapsuleGeometry", "param", "add", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/animation/MMDPhysics.js"], "sourcesContent": ["import {\n  Bone,\n  BoxGeometry,\n  Color,\n  Euler,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Quaternion,\n  SphereGeometry,\n  Vector3,\n} from 'three'\nimport { CapsuleGeometry } from '../_polyfill/CapsuleGeometry'\n\n/**\n * Dependencies\n *  - Ammo.js https://github.com/kripken/ammo.js\n *\n * MMDPhysics calculates physics with Ammo(Bullet based JavaScript Physics engine)\n * for MMD model loaded by MMDLoader.\n *\n * TODO\n *  - Physics in Worker\n */\n\n/* global Ammo */\n\nclass MMDPhysics {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} rigidBodyParams\n   * @param {Array<Object>} (optional) constraintParams\n   * @param {Object} params - (optional)\n   * @param {Number} params.unitStep - Default is 1 / 65.\n   * @param {Integer} params.maxStepNum - Default is 3.\n   * @param {Vector3} params.gravity - Default is ( 0, - 9.8 * 10, 0 )\n   */\n  constructor(mesh, rigidBodyParams, constraintParams = [], params = {}) {\n    if (typeof Ammo === 'undefined') {\n      throw new Error('THREE.MMDPhysics: Import ammo.js https://github.com/kripken/ammo.js')\n    }\n\n    this.manager = new ResourceManager()\n\n    this.mesh = mesh\n\n    /*\n     * I don't know why but 1/60 unitStep easily breaks models\n     * so I set it 1/65 so far.\n     * Don't set too small unitStep because\n     * the smaller unitStep can make the performance worse.\n     */\n    this.unitStep = params.unitStep !== undefined ? params.unitStep : 1 / 65\n    this.maxStepNum = params.maxStepNum !== undefined ? params.maxStepNum : 3\n    this.gravity = new Vector3(0, -9.8 * 10, 0)\n\n    if (params.gravity !== undefined) this.gravity.copy(params.gravity)\n\n    this.world = params.world !== undefined ? params.world : null // experimental\n\n    this.bodies = []\n    this.constraints = []\n\n    this._init(mesh, rigidBodyParams, constraintParams)\n  }\n\n  /**\n   * Advances Physics calculation and updates bones.\n   *\n   * @param {Number} delta - time in second\n   * @return {MMDPhysics}\n   */\n  update(delta) {\n    const manager = this.manager\n    const mesh = this.mesh\n\n    // rigid bodies and constrains are for\n    // mesh's world scale (1, 1, 1).\n    // Convert to (1, 1, 1) if it isn't.\n\n    let isNonDefaultScale = false\n\n    const position = manager.allocThreeVector3()\n    const quaternion = manager.allocThreeQuaternion()\n    const scale = manager.allocThreeVector3()\n\n    mesh.matrixWorld.decompose(position, quaternion, scale)\n\n    if (scale.x !== 1 || scale.y !== 1 || scale.z !== 1) {\n      isNonDefaultScale = true\n    }\n\n    let parent\n\n    if (isNonDefaultScale) {\n      parent = mesh.parent\n\n      if (parent !== null) mesh.parent = null\n\n      scale.copy(this.mesh.scale)\n\n      mesh.scale.set(1, 1, 1)\n      mesh.updateMatrixWorld(true)\n    }\n\n    // calculate physics and update bones\n\n    this._updateRigidBodies()\n    this._stepSimulation(delta)\n    this._updateBones()\n\n    // restore mesh if converted above\n\n    if (isNonDefaultScale) {\n      if (parent !== null) mesh.parent = parent\n\n      mesh.scale.copy(scale)\n    }\n\n    manager.freeThreeVector3(scale)\n    manager.freeThreeQuaternion(quaternion)\n    manager.freeThreeVector3(position)\n\n    return this\n  }\n\n  /**\n   * Resets rigid bodies transorm to current bone's.\n   *\n   * @return {MMDPhysics}\n   */\n  reset() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].reset()\n    }\n\n    return this\n  }\n\n  /**\n   * Warm ups Rigid bodies. Calculates cycles steps.\n   *\n   * @param {Integer} cycles\n   * @return {MMDPhysics}\n   */\n  warmup(cycles) {\n    for (let i = 0; i < cycles; i++) {\n      this.update(1 / 60)\n    }\n\n    return this\n  }\n\n  /**\n   * Sets gravity.\n   *\n   * @param {Vector3} gravity\n   * @return {MMDPhysicsHelper}\n   */\n  setGravity(gravity) {\n    this.world.setGravity(new Ammo.btVector3(gravity.x, gravity.y, gravity.z))\n    this.gravity.copy(gravity)\n\n    return this\n  }\n\n  /**\n   * Creates MMDPhysicsHelper\n   *\n   * @return {MMDPhysicsHelper}\n   */\n  createHelper() {\n    return new MMDPhysicsHelper(this.mesh, this)\n  }\n\n  // private methods\n\n  _init(mesh, rigidBodyParams, constraintParams) {\n    const manager = this.manager\n\n    // rigid body/constraint parameters are for\n    // mesh's default world transform as position(0, 0, 0),\n    // quaternion(0, 0, 0, 1) and scale(0, 0, 0)\n\n    const parent = mesh.parent\n\n    if (parent !== null) mesh.parent = null\n\n    const currentPosition = manager.allocThreeVector3()\n    const currentQuaternion = manager.allocThreeQuaternion()\n    const currentScale = manager.allocThreeVector3()\n\n    currentPosition.copy(mesh.position)\n    currentQuaternion.copy(mesh.quaternion)\n    currentScale.copy(mesh.scale)\n\n    mesh.position.set(0, 0, 0)\n    mesh.quaternion.set(0, 0, 0, 1)\n    mesh.scale.set(1, 1, 1)\n\n    mesh.updateMatrixWorld(true)\n\n    if (this.world === null) {\n      this.world = this._createWorld()\n      this.setGravity(this.gravity)\n    }\n\n    this._initRigidBodies(rigidBodyParams)\n    this._initConstraints(constraintParams)\n\n    if (parent !== null) mesh.parent = parent\n\n    mesh.position.copy(currentPosition)\n    mesh.quaternion.copy(currentQuaternion)\n    mesh.scale.copy(currentScale)\n\n    mesh.updateMatrixWorld(true)\n\n    this.reset()\n\n    manager.freeThreeVector3(currentPosition)\n    manager.freeThreeQuaternion(currentQuaternion)\n    manager.freeThreeVector3(currentScale)\n  }\n\n  _createWorld() {\n    const config = new Ammo.btDefaultCollisionConfiguration()\n    const dispatcher = new Ammo.btCollisionDispatcher(config)\n    const cache = new Ammo.btDbvtBroadphase()\n    const solver = new Ammo.btSequentialImpulseConstraintSolver()\n    const world = new Ammo.btDiscreteDynamicsWorld(dispatcher, cache, solver, config)\n    return world\n  }\n\n  _initRigidBodies(rigidBodies) {\n    for (let i = 0, il = rigidBodies.length; i < il; i++) {\n      this.bodies.push(new RigidBody(this.mesh, this.world, rigidBodies[i], this.manager))\n    }\n  }\n\n  _initConstraints(constraints) {\n    for (let i = 0, il = constraints.length; i < il; i++) {\n      const params = constraints[i]\n      const bodyA = this.bodies[params.rigidBodyIndex1]\n      const bodyB = this.bodies[params.rigidBodyIndex2]\n      this.constraints.push(new Constraint(this.mesh, this.world, bodyA, bodyB, params, this.manager))\n    }\n  }\n\n  _stepSimulation(delta) {\n    const unitStep = this.unitStep\n    let stepTime = delta\n    let maxStepNum = ((delta / unitStep) | 0) + 1\n\n    if (stepTime < unitStep) {\n      stepTime = unitStep\n      maxStepNum = 1\n    }\n\n    if (maxStepNum > this.maxStepNum) {\n      maxStepNum = this.maxStepNum\n    }\n\n    this.world.stepSimulation(stepTime, maxStepNum, unitStep)\n  }\n\n  _updateRigidBodies() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateFromBone()\n    }\n  }\n\n  _updateBones() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateBone()\n    }\n  }\n}\n\n/**\n * This manager's responsibilies are\n *\n * 1. manage Ammo.js and Three.js object resources and\n *    improve the performance and the memory consumption by\n *    reusing objects.\n *\n * 2. provide simple Ammo object operations.\n */\nclass ResourceManager {\n  constructor() {\n    // for Three.js\n    this.threeVector3s = []\n    this.threeMatrix4s = []\n    this.threeQuaternions = []\n    this.threeEulers = []\n\n    // for Ammo.js\n    this.transforms = []\n    this.quaternions = []\n    this.vector3s = []\n  }\n\n  allocThreeVector3() {\n    return this.threeVector3s.length > 0 ? this.threeVector3s.pop() : new Vector3()\n  }\n\n  freeThreeVector3(v) {\n    this.threeVector3s.push(v)\n  }\n\n  allocThreeMatrix4() {\n    return this.threeMatrix4s.length > 0 ? this.threeMatrix4s.pop() : new Matrix4()\n  }\n\n  freeThreeMatrix4(m) {\n    this.threeMatrix4s.push(m)\n  }\n\n  allocThreeQuaternion() {\n    return this.threeQuaternions.length > 0 ? this.threeQuaternions.pop() : new Quaternion()\n  }\n\n  freeThreeQuaternion(q) {\n    this.threeQuaternions.push(q)\n  }\n\n  allocThreeEuler() {\n    return this.threeEulers.length > 0 ? this.threeEulers.pop() : new Euler()\n  }\n\n  freeThreeEuler(e) {\n    this.threeEulers.push(e)\n  }\n\n  allocTransform() {\n    return this.transforms.length > 0 ? this.transforms.pop() : new Ammo.btTransform()\n  }\n\n  freeTransform(t) {\n    this.transforms.push(t)\n  }\n\n  allocQuaternion() {\n    return this.quaternions.length > 0 ? this.quaternions.pop() : new Ammo.btQuaternion()\n  }\n\n  freeQuaternion(q) {\n    this.quaternions.push(q)\n  }\n\n  allocVector3() {\n    return this.vector3s.length > 0 ? this.vector3s.pop() : new Ammo.btVector3()\n  }\n\n  freeVector3(v) {\n    this.vector3s.push(v)\n  }\n\n  setIdentity(t) {\n    t.setIdentity()\n  }\n\n  getBasis(t) {\n    var q = this.allocQuaternion()\n    t.getBasis().getRotation(q)\n    return q\n  }\n\n  getBasisAsMatrix3(t) {\n    var q = this.getBasis(t)\n    var m = this.quaternionToMatrix3(q)\n    this.freeQuaternion(q)\n    return m\n  }\n\n  getOrigin(t) {\n    return t.getOrigin()\n  }\n\n  setOrigin(t, v) {\n    t.getOrigin().setValue(v.x(), v.y(), v.z())\n  }\n\n  copyOrigin(t1, t2) {\n    var o = t2.getOrigin()\n    this.setOrigin(t1, o)\n  }\n\n  setBasis(t, q) {\n    t.setRotation(q)\n  }\n\n  setBasisFromMatrix3(t, m) {\n    var q = this.matrix3ToQuaternion(m)\n    this.setBasis(t, q)\n    this.freeQuaternion(q)\n  }\n\n  setOriginFromArray3(t, a) {\n    t.getOrigin().setValue(a[0], a[1], a[2])\n  }\n\n  setOriginFromThreeVector3(t, v) {\n    t.getOrigin().setValue(v.x, v.y, v.z)\n  }\n\n  setBasisFromArray3(t, a) {\n    var thQ = this.allocThreeQuaternion()\n    var thE = this.allocThreeEuler()\n    thE.set(a[0], a[1], a[2])\n    this.setBasisFromThreeQuaternion(t, thQ.setFromEuler(thE))\n\n    this.freeThreeEuler(thE)\n    this.freeThreeQuaternion(thQ)\n  }\n\n  setBasisFromThreeQuaternion(t, a) {\n    var q = this.allocQuaternion()\n\n    q.setX(a.x)\n    q.setY(a.y)\n    q.setZ(a.z)\n    q.setW(a.w)\n    this.setBasis(t, q)\n\n    this.freeQuaternion(q)\n  }\n\n  multiplyTransforms(t1, t2) {\n    var t = this.allocTransform()\n    this.setIdentity(t)\n\n    var m1 = this.getBasisAsMatrix3(t1)\n    var m2 = this.getBasisAsMatrix3(t2)\n\n    var o1 = this.getOrigin(t1)\n    var o2 = this.getOrigin(t2)\n\n    var v1 = this.multiplyMatrix3ByVector3(m1, o2)\n    var v2 = this.addVector3(v1, o1)\n    this.setOrigin(t, v2)\n\n    var m3 = this.multiplyMatrices3(m1, m2)\n    this.setBasisFromMatrix3(t, m3)\n\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return t\n  }\n\n  inverseTransform(t) {\n    var t2 = this.allocTransform()\n\n    var m1 = this.getBasisAsMatrix3(t)\n    var o = this.getOrigin(t)\n\n    var m2 = this.transposeMatrix3(m1)\n    var v1 = this.negativeVector3(o)\n    var v2 = this.multiplyMatrix3ByVector3(m2, v1)\n\n    this.setOrigin(t2, v2)\n    this.setBasisFromMatrix3(t2, m2)\n\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return t2\n  }\n\n  multiplyMatrices3(m1, m2) {\n    var m3 = []\n\n    var v10 = this.rowOfMatrix3(m1, 0)\n    var v11 = this.rowOfMatrix3(m1, 1)\n    var v12 = this.rowOfMatrix3(m1, 2)\n\n    var v20 = this.columnOfMatrix3(m2, 0)\n    var v21 = this.columnOfMatrix3(m2, 1)\n    var v22 = this.columnOfMatrix3(m2, 2)\n\n    m3[0] = this.dotVectors3(v10, v20)\n    m3[1] = this.dotVectors3(v10, v21)\n    m3[2] = this.dotVectors3(v10, v22)\n    m3[3] = this.dotVectors3(v11, v20)\n    m3[4] = this.dotVectors3(v11, v21)\n    m3[5] = this.dotVectors3(v11, v22)\n    m3[6] = this.dotVectors3(v12, v20)\n    m3[7] = this.dotVectors3(v12, v21)\n    m3[8] = this.dotVectors3(v12, v22)\n\n    this.freeVector3(v10)\n    this.freeVector3(v11)\n    this.freeVector3(v12)\n    this.freeVector3(v20)\n    this.freeVector3(v21)\n    this.freeVector3(v22)\n\n    return m3\n  }\n\n  addVector3(v1, v2) {\n    var v = this.allocVector3()\n    v.setValue(v1.x() + v2.x(), v1.y() + v2.y(), v1.z() + v2.z())\n    return v\n  }\n\n  dotVectors3(v1, v2) {\n    return v1.x() * v2.x() + v1.y() * v2.y() + v1.z() * v2.z()\n  }\n\n  rowOfMatrix3(m, i) {\n    var v = this.allocVector3()\n    v.setValue(m[i * 3 + 0], m[i * 3 + 1], m[i * 3 + 2])\n    return v\n  }\n\n  columnOfMatrix3(m, i) {\n    var v = this.allocVector3()\n    v.setValue(m[i + 0], m[i + 3], m[i + 6])\n    return v\n  }\n\n  negativeVector3(v) {\n    var v2 = this.allocVector3()\n    v2.setValue(-v.x(), -v.y(), -v.z())\n    return v2\n  }\n\n  multiplyMatrix3ByVector3(m, v) {\n    var v4 = this.allocVector3()\n\n    var v0 = this.rowOfMatrix3(m, 0)\n    var v1 = this.rowOfMatrix3(m, 1)\n    var v2 = this.rowOfMatrix3(m, 2)\n    var x = this.dotVectors3(v0, v)\n    var y = this.dotVectors3(v1, v)\n    var z = this.dotVectors3(v2, v)\n\n    v4.setValue(x, y, z)\n\n    this.freeVector3(v0)\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return v4\n  }\n\n  transposeMatrix3(m) {\n    var m2 = []\n    m2[0] = m[0]\n    m2[1] = m[3]\n    m2[2] = m[6]\n    m2[3] = m[1]\n    m2[4] = m[4]\n    m2[5] = m[7]\n    m2[6] = m[2]\n    m2[7] = m[5]\n    m2[8] = m[8]\n    return m2\n  }\n\n  quaternionToMatrix3(q) {\n    var m = []\n\n    var x = q.x()\n    var y = q.y()\n    var z = q.z()\n    var w = q.w()\n\n    var xx = x * x\n    var yy = y * y\n    var zz = z * z\n\n    var xy = x * y\n    var yz = y * z\n    var zx = z * x\n\n    var xw = x * w\n    var yw = y * w\n    var zw = z * w\n\n    m[0] = 1 - 2 * (yy + zz)\n    m[1] = 2 * (xy - zw)\n    m[2] = 2 * (zx + yw)\n    m[3] = 2 * (xy + zw)\n    m[4] = 1 - 2 * (zz + xx)\n    m[5] = 2 * (yz - xw)\n    m[6] = 2 * (zx - yw)\n    m[7] = 2 * (yz + xw)\n    m[8] = 1 - 2 * (xx + yy)\n\n    return m\n  }\n\n  matrix3ToQuaternion(m) {\n    var t = m[0] + m[4] + m[8]\n    var s, x, y, z, w\n\n    if (t > 0) {\n      s = Math.sqrt(t + 1.0) * 2\n      w = 0.25 * s\n      x = (m[7] - m[5]) / s\n      y = (m[2] - m[6]) / s\n      z = (m[3] - m[1]) / s\n    } else if (m[0] > m[4] && m[0] > m[8]) {\n      s = Math.sqrt(1.0 + m[0] - m[4] - m[8]) * 2\n      w = (m[7] - m[5]) / s\n      x = 0.25 * s\n      y = (m[1] + m[3]) / s\n      z = (m[2] + m[6]) / s\n    } else if (m[4] > m[8]) {\n      s = Math.sqrt(1.0 + m[4] - m[0] - m[8]) * 2\n      w = (m[2] - m[6]) / s\n      x = (m[1] + m[3]) / s\n      y = 0.25 * s\n      z = (m[5] + m[7]) / s\n    } else {\n      s = Math.sqrt(1.0 + m[8] - m[0] - m[4]) * 2\n      w = (m[3] - m[1]) / s\n      x = (m[2] + m[6]) / s\n      y = (m[5] + m[7]) / s\n      z = 0.25 * s\n    }\n\n    var q = this.allocQuaternion()\n    q.setX(x)\n    q.setY(y)\n    q.setZ(z)\n    q.setW(w)\n    return q\n  }\n}\n\n/**\n * @param {THREE.SkinnedMesh} mesh\n * @param {Ammo.btDiscreteDynamicsWorld} world\n * @param {Object} params\n * @param {ResourceManager} manager\n */\nclass RigidBody {\n  constructor(mesh, world, params, manager) {\n    this.mesh = mesh\n    this.world = world\n    this.params = params\n    this.manager = manager\n\n    this.body = null\n    this.bone = null\n    this.boneOffsetForm = null\n    this.boneOffsetFormInverse = null\n\n    this._init()\n  }\n\n  /**\n   * Resets rigid body transform to the current bone's.\n   *\n   * @return {RigidBody}\n   */\n  reset() {\n    this._setTransformFromBone()\n    return this\n  }\n\n  /**\n   * Updates rigid body's transform from the current bone.\n   *\n   * @return {RidigBody}\n   */\n  updateFromBone() {\n    if (this.params.boneIndex !== -1 && this.params.type === 0) {\n      this._setTransformFromBone()\n    }\n\n    return this\n  }\n\n  /**\n   * Updates bone from the current ridid body's transform.\n   *\n   * @return {RidigBody}\n   */\n  updateBone() {\n    if (this.params.type === 0 || this.params.boneIndex === -1) {\n      return this\n    }\n\n    this._updateBoneRotation()\n\n    if (this.params.type === 1) {\n      this._updateBonePosition()\n    }\n\n    this.bone.updateMatrixWorld(true)\n\n    if (this.params.type === 2) {\n      this._setPositionFromBone()\n    }\n\n    return this\n  }\n\n  // private methods\n\n  _init() {\n    function generateShape(p) {\n      switch (p.shapeType) {\n        case 0:\n          return new Ammo.btSphereShape(p.width)\n\n        case 1:\n          return new Ammo.btBoxShape(new Ammo.btVector3(p.width, p.height, p.depth))\n\n        case 2:\n          return new Ammo.btCapsuleShape(p.width, p.height)\n\n        default:\n          throw new Error('unknown shape type ' + p.shapeType)\n      }\n    }\n\n    const manager = this.manager\n    const params = this.params\n    const bones = this.mesh.skeleton.bones\n    const bone = params.boneIndex === -1 ? new Bone() : bones[params.boneIndex]\n\n    const shape = generateShape(params)\n    const weight = params.type === 0 ? 0 : params.weight\n    const localInertia = manager.allocVector3()\n    localInertia.setValue(0, 0, 0)\n\n    if (weight !== 0) {\n      shape.calculateLocalInertia(weight, localInertia)\n    }\n\n    const boneOffsetForm = manager.allocTransform()\n    manager.setIdentity(boneOffsetForm)\n    manager.setOriginFromArray3(boneOffsetForm, params.position)\n    manager.setBasisFromArray3(boneOffsetForm, params.rotation)\n\n    const vector = manager.allocThreeVector3()\n    const boneForm = manager.allocTransform()\n    manager.setIdentity(boneForm)\n    manager.setOriginFromThreeVector3(boneForm, bone.getWorldPosition(vector))\n\n    const form = manager.multiplyTransforms(boneForm, boneOffsetForm)\n    const state = new Ammo.btDefaultMotionState(form)\n\n    const info = new Ammo.btRigidBodyConstructionInfo(weight, state, shape, localInertia)\n    info.set_m_friction(params.friction)\n    info.set_m_restitution(params.restitution)\n\n    const body = new Ammo.btRigidBody(info)\n\n    if (params.type === 0) {\n      body.setCollisionFlags(body.getCollisionFlags() | 2)\n\n      /*\n       * It'd be better to comment out this line though in general I should call this method\n       * because I'm not sure why but physics will be more like MMD's\n       * if I comment out.\n       */\n      body.setActivationState(4)\n    }\n\n    body.setDamping(params.positionDamping, params.rotationDamping)\n    body.setSleepingThresholds(0, 0)\n\n    this.world.addRigidBody(body, 1 << params.groupIndex, params.groupTarget)\n\n    this.body = body\n    this.bone = bone\n    this.boneOffsetForm = boneOffsetForm\n    this.boneOffsetFormInverse = manager.inverseTransform(boneOffsetForm)\n\n    manager.freeVector3(localInertia)\n    manager.freeTransform(form)\n    manager.freeTransform(boneForm)\n    manager.freeThreeVector3(vector)\n  }\n\n  _getBoneTransform() {\n    const manager = this.manager\n    const p = manager.allocThreeVector3()\n    const q = manager.allocThreeQuaternion()\n    const s = manager.allocThreeVector3()\n\n    this.bone.matrixWorld.decompose(p, q, s)\n\n    const tr = manager.allocTransform()\n    manager.setOriginFromThreeVector3(tr, p)\n    manager.setBasisFromThreeQuaternion(tr, q)\n\n    const form = manager.multiplyTransforms(tr, this.boneOffsetForm)\n\n    manager.freeTransform(tr)\n    manager.freeThreeVector3(s)\n    manager.freeThreeQuaternion(q)\n    manager.freeThreeVector3(p)\n\n    return form\n  }\n\n  _getWorldTransformForBone() {\n    const manager = this.manager\n    const tr = this.body.getCenterOfMassTransform()\n    return manager.multiplyTransforms(tr, this.boneOffsetFormInverse)\n  }\n\n  _setTransformFromBone() {\n    const manager = this.manager\n    const form = this._getBoneTransform()\n\n    // TODO: check the most appropriate way to set\n    //this.body.setWorldTransform( form );\n    this.body.setCenterOfMassTransform(form)\n    this.body.getMotionState().setWorldTransform(form)\n\n    manager.freeTransform(form)\n  }\n\n  _setPositionFromBone() {\n    const manager = this.manager\n    const form = this._getBoneTransform()\n\n    const tr = manager.allocTransform()\n    this.body.getMotionState().getWorldTransform(tr)\n    manager.copyOrigin(tr, form)\n\n    // TODO: check the most appropriate way to set\n    //this.body.setWorldTransform( tr );\n    this.body.setCenterOfMassTransform(tr)\n    this.body.getMotionState().setWorldTransform(tr)\n\n    manager.freeTransform(tr)\n    manager.freeTransform(form)\n  }\n\n  _updateBoneRotation() {\n    const manager = this.manager\n\n    const tr = this._getWorldTransformForBone()\n    const q = manager.getBasis(tr)\n\n    const thQ = manager.allocThreeQuaternion()\n    const thQ2 = manager.allocThreeQuaternion()\n    const thQ3 = manager.allocThreeQuaternion()\n\n    thQ.set(q.x(), q.y(), q.z(), q.w())\n    thQ2.setFromRotationMatrix(this.bone.matrixWorld)\n    thQ2.conjugate()\n    thQ2.multiply(thQ)\n\n    //this.bone.quaternion.multiply( thQ2 );\n\n    thQ3.setFromRotationMatrix(this.bone.matrix)\n\n    // Renormalizing quaternion here because repeatedly transforming\n    // quaternion continuously accumulates floating point error and\n    // can end up being overflow. See #15335\n    this.bone.quaternion.copy(thQ2.multiply(thQ3).normalize())\n\n    manager.freeThreeQuaternion(thQ)\n    manager.freeThreeQuaternion(thQ2)\n    manager.freeThreeQuaternion(thQ3)\n\n    manager.freeQuaternion(q)\n    manager.freeTransform(tr)\n  }\n\n  _updateBonePosition() {\n    const manager = this.manager\n\n    const tr = this._getWorldTransformForBone()\n\n    const thV = manager.allocThreeVector3()\n\n    const o = manager.getOrigin(tr)\n    thV.set(o.x(), o.y(), o.z())\n\n    if (this.bone.parent) {\n      this.bone.parent.worldToLocal(thV)\n    }\n\n    this.bone.position.copy(thV)\n\n    manager.freeThreeVector3(thV)\n\n    manager.freeTransform(tr)\n  }\n}\n\n//\n\nclass Constraint {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Ammo.btDiscreteDynamicsWorld} world\n   * @param {RigidBody} bodyA\n   * @param {RigidBody} bodyB\n   * @param {Object} params\n   * @param {ResourceManager} manager\n   */\n  constructor(mesh, world, bodyA, bodyB, params, manager) {\n    this.mesh = mesh\n    this.world = world\n    this.bodyA = bodyA\n    this.bodyB = bodyB\n    this.params = params\n    this.manager = manager\n\n    this.constraint = null\n\n    this._init()\n  }\n\n  // private method\n\n  _init() {\n    const manager = this.manager\n    const params = this.params\n    const bodyA = this.bodyA\n    const bodyB = this.bodyB\n\n    const form = manager.allocTransform()\n    manager.setIdentity(form)\n    manager.setOriginFromArray3(form, params.position)\n    manager.setBasisFromArray3(form, params.rotation)\n\n    const formA = manager.allocTransform()\n    const formB = manager.allocTransform()\n\n    bodyA.body.getMotionState().getWorldTransform(formA)\n    bodyB.body.getMotionState().getWorldTransform(formB)\n\n    const formInverseA = manager.inverseTransform(formA)\n    const formInverseB = manager.inverseTransform(formB)\n\n    const formA2 = manager.multiplyTransforms(formInverseA, form)\n    const formB2 = manager.multiplyTransforms(formInverseB, form)\n\n    const constraint = new Ammo.btGeneric6DofSpringConstraint(bodyA.body, bodyB.body, formA2, formB2, true)\n\n    const lll = manager.allocVector3()\n    const lul = manager.allocVector3()\n    const all = manager.allocVector3()\n    const aul = manager.allocVector3()\n\n    lll.setValue(params.translationLimitation1[0], params.translationLimitation1[1], params.translationLimitation1[2])\n    lul.setValue(params.translationLimitation2[0], params.translationLimitation2[1], params.translationLimitation2[2])\n    all.setValue(params.rotationLimitation1[0], params.rotationLimitation1[1], params.rotationLimitation1[2])\n    aul.setValue(params.rotationLimitation2[0], params.rotationLimitation2[1], params.rotationLimitation2[2])\n\n    constraint.setLinearLowerLimit(lll)\n    constraint.setLinearUpperLimit(lul)\n    constraint.setAngularLowerLimit(all)\n    constraint.setAngularUpperLimit(aul)\n\n    for (let i = 0; i < 3; i++) {\n      if (params.springPosition[i] !== 0) {\n        constraint.enableSpring(i, true)\n        constraint.setStiffness(i, params.springPosition[i])\n      }\n    }\n\n    for (let i = 0; i < 3; i++) {\n      if (params.springRotation[i] !== 0) {\n        constraint.enableSpring(i + 3, true)\n        constraint.setStiffness(i + 3, params.springRotation[i])\n      }\n    }\n\n    /*\n     * Currently(10/31/2016) official ammo.js doesn't support\n     * btGeneric6DofSpringConstraint.setParam method.\n     * You need custom ammo.js (add the method into idl) if you wanna use.\n     * By setting this parameter, physics will be more like MMD's\n     */\n    if (constraint.setParam !== undefined) {\n      for (let i = 0; i < 6; i++) {\n        constraint.setParam(2, 0.475, i)\n      }\n    }\n\n    this.world.addConstraint(constraint, true)\n    this.constraint = constraint\n\n    manager.freeTransform(form)\n    manager.freeTransform(formA)\n    manager.freeTransform(formB)\n    manager.freeTransform(formInverseA)\n    manager.freeTransform(formInverseB)\n    manager.freeTransform(formA2)\n    manager.freeTransform(formB2)\n    manager.freeVector3(lll)\n    manager.freeVector3(lul)\n    manager.freeVector3(all)\n    manager.freeVector3(aul)\n  }\n}\n\nconst _position = /* @__PURE__ */ new Vector3()\nconst _quaternion = /* @__PURE__ */ new Quaternion()\nconst _scale = /* @__PURE__ */ new Vector3()\nconst _matrixWorldInv = /* @__PURE__ */ new Matrix4()\n\nclass MMDPhysicsHelper extends Object3D {\n  /**\n   * Visualize Rigid bodies\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Physics} physics\n   */\n  constructor(mesh, physics) {\n    super()\n\n    this.root = mesh\n    this.physics = physics\n\n    this.matrix.copy(mesh.matrixWorld)\n    this.matrixAutoUpdate = false\n\n    this.materials = []\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0xff8888),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0x88ff88),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0x8888ff),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this._init()\n  }\n\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    const materials = this.materials\n    const children = this.children\n\n    for (let i = 0; i < materials.length; i++) {\n      materials[i].dispose()\n    }\n\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i]\n\n      if (child.isMesh) child.geometry.dispose()\n    }\n  }\n\n  /**\n   * Updates Rigid Bodies visualization.\n   */\n  updateMatrixWorld(force) {\n    var mesh = this.root\n\n    if (this.visible) {\n      var bodies = this.physics.bodies\n\n      _matrixWorldInv\n        .copy(mesh.matrixWorld)\n        .decompose(_position, _quaternion, _scale)\n        .compose(_position, _quaternion, _scale.set(1, 1, 1))\n        .invert()\n\n      for (var i = 0, il = bodies.length; i < il; i++) {\n        var body = bodies[i].body\n        var child = this.children[i]\n\n        var tr = body.getCenterOfMassTransform()\n        var origin = tr.getOrigin()\n        var rotation = tr.getRotation()\n\n        child.position.set(origin.x(), origin.y(), origin.z()).applyMatrix4(_matrixWorldInv)\n\n        child.quaternion\n          .setFromRotationMatrix(_matrixWorldInv)\n          .multiply(_quaternion.set(rotation.x(), rotation.y(), rotation.z(), rotation.w()))\n      }\n    }\n\n    this.matrix\n      .copy(mesh.matrixWorld)\n      .decompose(_position, _quaternion, _scale)\n      .compose(_position, _quaternion, _scale.set(1, 1, 1))\n\n    super.updateMatrixWorld(force)\n  }\n\n  // private method\n\n  _init() {\n    var bodies = this.physics.bodies\n\n    function createGeometry(param) {\n      switch (param.shapeType) {\n        case 0:\n          return new SphereGeometry(param.width, 16, 8)\n\n        case 1:\n          return new BoxGeometry(param.width * 2, param.height * 2, param.depth * 2, 8, 8, 8)\n\n        case 2:\n          return new CapsuleGeometry(param.width, param.height, 8, 16)\n\n        default:\n          return null\n      }\n    }\n\n    for (var i = 0, il = bodies.length; i < il; i++) {\n      var param = bodies[i].params\n      this.add(new Mesh(createGeometry(param), this.materials[param.type]))\n    }\n  }\n}\n\nexport { MMDPhysics }\n"], "mappings": ";;AA4BA,MAAMA,UAAA,CAAW;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUfC,YAAYC,IAAA,EAAMC,eAAA,EAAiBC,gBAAA,GAAmB,EAAE,EAAEC,MAAA,GAAS,IAAI;IACrE,IAAI,OAAOC,IAAA,KAAS,aAAa;MAC/B,MAAM,IAAIC,KAAA,CAAM,qEAAqE;IACtF;IAED,KAAKC,OAAA,GAAU,IAAIC,eAAA,CAAiB;IAEpC,KAAKP,IAAA,GAAOA,IAAA;IAQZ,KAAKQ,QAAA,GAAWL,MAAA,CAAOK,QAAA,KAAa,SAAYL,MAAA,CAAOK,QAAA,GAAW,IAAI;IACtE,KAAKC,UAAA,GAAaN,MAAA,CAAOM,UAAA,KAAe,SAAYN,MAAA,CAAOM,UAAA,GAAa;IACxE,KAAKC,OAAA,GAAU,IAAIC,OAAA,CAAQ,GAAG,OAAO,IAAI,CAAC;IAE1C,IAAIR,MAAA,CAAOO,OAAA,KAAY,QAAW,KAAKA,OAAA,CAAQE,IAAA,CAAKT,MAAA,CAAOO,OAAO;IAElE,KAAKG,KAAA,GAAQV,MAAA,CAAOU,KAAA,KAAU,SAAYV,MAAA,CAAOU,KAAA,GAAQ;IAEzD,KAAKC,MAAA,GAAS,EAAE;IAChB,KAAKC,WAAA,GAAc,EAAE;IAErB,KAAKC,KAAA,CAAMhB,IAAA,EAAMC,eAAA,EAAiBC,gBAAgB;EACnD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDe,OAAOC,KAAA,EAAO;IACZ,MAAMZ,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMN,IAAA,GAAO,KAAKA,IAAA;IAMlB,IAAImB,iBAAA,GAAoB;IAExB,MAAMC,QAAA,GAAWd,OAAA,CAAQe,iBAAA,CAAmB;IAC5C,MAAMC,UAAA,GAAahB,OAAA,CAAQiB,oBAAA,CAAsB;IACjD,MAAMC,KAAA,GAAQlB,OAAA,CAAQe,iBAAA,CAAmB;IAEzCrB,IAAA,CAAKyB,WAAA,CAAYC,SAAA,CAAUN,QAAA,EAAUE,UAAA,EAAYE,KAAK;IAEtD,IAAIA,KAAA,CAAMG,CAAA,KAAM,KAAKH,KAAA,CAAMI,CAAA,KAAM,KAAKJ,KAAA,CAAMK,CAAA,KAAM,GAAG;MACnDV,iBAAA,GAAoB;IACrB;IAED,IAAIW,MAAA;IAEJ,IAAIX,iBAAA,EAAmB;MACrBW,MAAA,GAAS9B,IAAA,CAAK8B,MAAA;MAEd,IAAIA,MAAA,KAAW,MAAM9B,IAAA,CAAK8B,MAAA,GAAS;MAEnCN,KAAA,CAAMZ,IAAA,CAAK,KAAKZ,IAAA,CAAKwB,KAAK;MAE1BxB,IAAA,CAAKwB,KAAA,CAAMO,GAAA,CAAI,GAAG,GAAG,CAAC;MACtB/B,IAAA,CAAKgC,iBAAA,CAAkB,IAAI;IAC5B;IAID,KAAKC,kBAAA,CAAoB;IACzB,KAAKC,eAAA,CAAgBhB,KAAK;IAC1B,KAAKiB,YAAA,CAAc;IAInB,IAAIhB,iBAAA,EAAmB;MACrB,IAAIW,MAAA,KAAW,MAAM9B,IAAA,CAAK8B,MAAA,GAASA,MAAA;MAEnC9B,IAAA,CAAKwB,KAAA,CAAMZ,IAAA,CAAKY,KAAK;IACtB;IAEDlB,OAAA,CAAQ8B,gBAAA,CAAiBZ,KAAK;IAC9BlB,OAAA,CAAQ+B,mBAAA,CAAoBf,UAAU;IACtChB,OAAA,CAAQ8B,gBAAA,CAAiBhB,QAAQ;IAEjC,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODkB,MAAA,EAAQ;IACN,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAK1B,MAAA,CAAO2B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,KAAKzB,MAAA,CAAOyB,CAAC,EAAED,KAAA,CAAO;IACvB;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDI,OAAOC,MAAA,EAAQ;IACb,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAII,MAAA,EAAQJ,CAAA,IAAK;MAC/B,KAAKtB,MAAA,CAAO,IAAI,EAAE;IACnB;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQD2B,WAAWlC,OAAA,EAAS;IAClB,KAAKG,KAAA,CAAM+B,UAAA,CAAW,IAAIxC,IAAA,CAAKyC,SAAA,CAAUnC,OAAA,CAAQiB,CAAA,EAAGjB,OAAA,CAAQkB,CAAA,EAAGlB,OAAA,CAAQmB,CAAC,CAAC;IACzE,KAAKnB,OAAA,CAAQE,IAAA,CAAKF,OAAO;IAEzB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODoC,aAAA,EAAe;IACb,OAAO,IAAIC,gBAAA,CAAiB,KAAK/C,IAAA,EAAM,IAAI;EAC5C;EAAA;EAIDgB,MAAMhB,IAAA,EAAMC,eAAA,EAAiBC,gBAAA,EAAkB;IAC7C,MAAMI,OAAA,GAAU,KAAKA,OAAA;IAMrB,MAAMwB,MAAA,GAAS9B,IAAA,CAAK8B,MAAA;IAEpB,IAAIA,MAAA,KAAW,MAAM9B,IAAA,CAAK8B,MAAA,GAAS;IAEnC,MAAMkB,eAAA,GAAkB1C,OAAA,CAAQe,iBAAA,CAAmB;IACnD,MAAM4B,iBAAA,GAAoB3C,OAAA,CAAQiB,oBAAA,CAAsB;IACxD,MAAM2B,YAAA,GAAe5C,OAAA,CAAQe,iBAAA,CAAmB;IAEhD2B,eAAA,CAAgBpC,IAAA,CAAKZ,IAAA,CAAKoB,QAAQ;IAClC6B,iBAAA,CAAkBrC,IAAA,CAAKZ,IAAA,CAAKsB,UAAU;IACtC4B,YAAA,CAAatC,IAAA,CAAKZ,IAAA,CAAKwB,KAAK;IAE5BxB,IAAA,CAAKoB,QAAA,CAASW,GAAA,CAAI,GAAG,GAAG,CAAC;IACzB/B,IAAA,CAAKsB,UAAA,CAAWS,GAAA,CAAI,GAAG,GAAG,GAAG,CAAC;IAC9B/B,IAAA,CAAKwB,KAAA,CAAMO,GAAA,CAAI,GAAG,GAAG,CAAC;IAEtB/B,IAAA,CAAKgC,iBAAA,CAAkB,IAAI;IAE3B,IAAI,KAAKnB,KAAA,KAAU,MAAM;MACvB,KAAKA,KAAA,GAAQ,KAAKsC,YAAA,CAAc;MAChC,KAAKP,UAAA,CAAW,KAAKlC,OAAO;IAC7B;IAED,KAAK0C,gBAAA,CAAiBnD,eAAe;IACrC,KAAKoD,gBAAA,CAAiBnD,gBAAgB;IAEtC,IAAI4B,MAAA,KAAW,MAAM9B,IAAA,CAAK8B,MAAA,GAASA,MAAA;IAEnC9B,IAAA,CAAKoB,QAAA,CAASR,IAAA,CAAKoC,eAAe;IAClChD,IAAA,CAAKsB,UAAA,CAAWV,IAAA,CAAKqC,iBAAiB;IACtCjD,IAAA,CAAKwB,KAAA,CAAMZ,IAAA,CAAKsC,YAAY;IAE5BlD,IAAA,CAAKgC,iBAAA,CAAkB,IAAI;IAE3B,KAAKM,KAAA,CAAO;IAEZhC,OAAA,CAAQ8B,gBAAA,CAAiBY,eAAe;IACxC1C,OAAA,CAAQ+B,mBAAA,CAAoBY,iBAAiB;IAC7C3C,OAAA,CAAQ8B,gBAAA,CAAiBc,YAAY;EACtC;EAEDC,aAAA,EAAe;IACb,MAAMG,MAAA,GAAS,IAAIlD,IAAA,CAAKmD,+BAAA,CAAiC;IACzD,MAAMC,UAAA,GAAa,IAAIpD,IAAA,CAAKqD,qBAAA,CAAsBH,MAAM;IACxD,MAAMI,KAAA,GAAQ,IAAItD,IAAA,CAAKuD,gBAAA,CAAkB;IACzC,MAAMC,MAAA,GAAS,IAAIxD,IAAA,CAAKyD,mCAAA,CAAqC;IAC7D,MAAMhD,KAAA,GAAQ,IAAIT,IAAA,CAAK0D,uBAAA,CAAwBN,UAAA,EAAYE,KAAA,EAAOE,MAAA,EAAQN,MAAM;IAChF,OAAOzC,KAAA;EACR;EAEDuC,iBAAiBW,WAAA,EAAa;IAC5B,SAASxB,CAAA,GAAI,GAAGC,EAAA,GAAKuB,WAAA,CAAYtB,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,KAAKzB,MAAA,CAAOkD,IAAA,CAAK,IAAIC,SAAA,CAAU,KAAKjE,IAAA,EAAM,KAAKa,KAAA,EAAOkD,WAAA,CAAYxB,CAAC,GAAG,KAAKjC,OAAO,CAAC;IACpF;EACF;EAED+C,iBAAiBtC,WAAA,EAAa;IAC5B,SAASwB,CAAA,GAAI,GAAGC,EAAA,GAAKzB,WAAA,CAAY0B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,MAAMpC,MAAA,GAASY,WAAA,CAAYwB,CAAC;MAC5B,MAAM2B,KAAA,GAAQ,KAAKpD,MAAA,CAAOX,MAAA,CAAOgE,eAAe;MAChD,MAAMC,KAAA,GAAQ,KAAKtD,MAAA,CAAOX,MAAA,CAAOkE,eAAe;MAChD,KAAKtD,WAAA,CAAYiD,IAAA,CAAK,IAAIM,UAAA,CAAW,KAAKtE,IAAA,EAAM,KAAKa,KAAA,EAAOqD,KAAA,EAAOE,KAAA,EAAOjE,MAAA,EAAQ,KAAKG,OAAO,CAAC;IAChG;EACF;EAED4B,gBAAgBhB,KAAA,EAAO;IACrB,MAAMV,QAAA,GAAW,KAAKA,QAAA;IACtB,IAAI+D,QAAA,GAAWrD,KAAA;IACf,IAAIT,UAAA,IAAeS,KAAA,GAAQV,QAAA,GAAY,KAAK;IAE5C,IAAI+D,QAAA,GAAW/D,QAAA,EAAU;MACvB+D,QAAA,GAAW/D,QAAA;MACXC,UAAA,GAAa;IACd;IAED,IAAIA,UAAA,GAAa,KAAKA,UAAA,EAAY;MAChCA,UAAA,GAAa,KAAKA,UAAA;IACnB;IAED,KAAKI,KAAA,CAAM2D,cAAA,CAAeD,QAAA,EAAU9D,UAAA,EAAYD,QAAQ;EACzD;EAEDyB,mBAAA,EAAqB;IACnB,SAASM,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAK1B,MAAA,CAAO2B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,KAAKzB,MAAA,CAAOyB,CAAC,EAAEkC,cAAA,CAAgB;IAChC;EACF;EAEDtC,aAAA,EAAe;IACb,SAASI,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAK1B,MAAA,CAAO2B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,KAAKzB,MAAA,CAAOyB,CAAC,EAAEmC,UAAA,CAAY;IAC5B;EACF;AACH;AAWA,MAAMnE,eAAA,CAAgB;EACpBR,YAAA,EAAc;IAEZ,KAAK4E,aAAA,GAAgB,EAAE;IACvB,KAAKC,aAAA,GAAgB,EAAE;IACvB,KAAKC,gBAAA,GAAmB,EAAE;IAC1B,KAAKC,WAAA,GAAc,EAAE;IAGrB,KAAKC,UAAA,GAAa,EAAE;IACpB,KAAKC,WAAA,GAAc,EAAE;IACrB,KAAKC,QAAA,GAAW,EAAE;EACnB;EAED5D,kBAAA,EAAoB;IAClB,OAAO,KAAKsD,aAAA,CAAclC,MAAA,GAAS,IAAI,KAAKkC,aAAA,CAAcO,GAAA,KAAQ,IAAIvE,OAAA,CAAS;EAChF;EAEDyB,iBAAiB+C,CAAA,EAAG;IAClB,KAAKR,aAAA,CAAcX,IAAA,CAAKmB,CAAC;EAC1B;EAEDC,kBAAA,EAAoB;IAClB,OAAO,KAAKR,aAAA,CAAcnC,MAAA,GAAS,IAAI,KAAKmC,aAAA,CAAcM,GAAA,KAAQ,IAAIG,OAAA,CAAS;EAChF;EAEDC,iBAAiBC,CAAA,EAAG;IAClB,KAAKX,aAAA,CAAcZ,IAAA,CAAKuB,CAAC;EAC1B;EAEDhE,qBAAA,EAAuB;IACrB,OAAO,KAAKsD,gBAAA,CAAiBpC,MAAA,GAAS,IAAI,KAAKoC,gBAAA,CAAiBK,GAAA,KAAQ,IAAIM,UAAA,CAAY;EACzF;EAEDnD,oBAAoBoD,CAAA,EAAG;IACrB,KAAKZ,gBAAA,CAAiBb,IAAA,CAAKyB,CAAC;EAC7B;EAEDC,gBAAA,EAAkB;IAChB,OAAO,KAAKZ,WAAA,CAAYrC,MAAA,GAAS,IAAI,KAAKqC,WAAA,CAAYI,GAAA,KAAQ,IAAIS,KAAA,CAAO;EAC1E;EAEDC,eAAeC,CAAA,EAAG;IAChB,KAAKf,WAAA,CAAYd,IAAA,CAAK6B,CAAC;EACxB;EAEDC,eAAA,EAAiB;IACf,OAAO,KAAKf,UAAA,CAAWtC,MAAA,GAAS,IAAI,KAAKsC,UAAA,CAAWG,GAAA,CAAK,IAAG,IAAI9E,IAAA,CAAK2F,WAAA,CAAa;EACnF;EAEDC,cAAcC,CAAA,EAAG;IACf,KAAKlB,UAAA,CAAWf,IAAA,CAAKiC,CAAC;EACvB;EAEDC,gBAAA,EAAkB;IAChB,OAAO,KAAKlB,WAAA,CAAYvC,MAAA,GAAS,IAAI,KAAKuC,WAAA,CAAYE,GAAA,CAAK,IAAG,IAAI9E,IAAA,CAAK+F,YAAA,CAAc;EACtF;EAEDC,eAAeX,CAAA,EAAG;IAChB,KAAKT,WAAA,CAAYhB,IAAA,CAAKyB,CAAC;EACxB;EAEDY,aAAA,EAAe;IACb,OAAO,KAAKpB,QAAA,CAASxC,MAAA,GAAS,IAAI,KAAKwC,QAAA,CAASC,GAAA,CAAK,IAAG,IAAI9E,IAAA,CAAKyC,SAAA,CAAW;EAC7E;EAEDyD,YAAYnB,CAAA,EAAG;IACb,KAAKF,QAAA,CAASjB,IAAA,CAAKmB,CAAC;EACrB;EAEDoB,YAAYN,CAAA,EAAG;IACbA,CAAA,CAAEM,WAAA,CAAa;EAChB;EAEDC,SAASP,CAAA,EAAG;IACV,IAAIR,CAAA,GAAI,KAAKS,eAAA,CAAiB;IAC9BD,CAAA,CAAEO,QAAA,CAAQ,EAAGC,WAAA,CAAYhB,CAAC;IAC1B,OAAOA,CAAA;EACR;EAEDiB,kBAAkBT,CAAA,EAAG;IACnB,IAAIR,CAAA,GAAI,KAAKe,QAAA,CAASP,CAAC;IACvB,IAAIV,CAAA,GAAI,KAAKoB,mBAAA,CAAoBlB,CAAC;IAClC,KAAKW,cAAA,CAAeX,CAAC;IACrB,OAAOF,CAAA;EACR;EAEDqB,UAAUX,CAAA,EAAG;IACX,OAAOA,CAAA,CAAEW,SAAA,CAAW;EACrB;EAEDC,UAAUZ,CAAA,EAAGd,CAAA,EAAG;IACdc,CAAA,CAAEW,SAAA,CAAS,EAAGE,QAAA,CAAS3B,CAAA,CAAExD,CAAA,IAAKwD,CAAA,CAAEvD,CAAA,CAAC,GAAIuD,CAAA,CAAEtD,CAAA,CAAC,CAAE;EAC3C;EAEDkF,WAAWC,EAAA,EAAIC,EAAA,EAAI;IACjB,IAAIC,CAAA,GAAID,EAAA,CAAGL,SAAA,CAAW;IACtB,KAAKC,SAAA,CAAUG,EAAA,EAAIE,CAAC;EACrB;EAEDC,SAASlB,CAAA,EAAGR,CAAA,EAAG;IACbQ,CAAA,CAAEmB,WAAA,CAAY3B,CAAC;EAChB;EAED4B,oBAAoBpB,CAAA,EAAGV,CAAA,EAAG;IACxB,IAAIE,CAAA,GAAI,KAAK6B,mBAAA,CAAoB/B,CAAC;IAClC,KAAK4B,QAAA,CAASlB,CAAA,EAAGR,CAAC;IAClB,KAAKW,cAAA,CAAeX,CAAC;EACtB;EAED8B,oBAAoBtB,CAAA,EAAGuB,CAAA,EAAG;IACxBvB,CAAA,CAAEW,SAAA,CAAS,EAAGE,QAAA,CAASU,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,CAAC;EACxC;EAEDC,0BAA0BxB,CAAA,EAAGd,CAAA,EAAG;IAC9Bc,CAAA,CAAEW,SAAA,GAAYE,QAAA,CAAS3B,CAAA,CAAExD,CAAA,EAAGwD,CAAA,CAAEvD,CAAA,EAAGuD,CAAA,CAAEtD,CAAC;EACrC;EAED6F,mBAAmBzB,CAAA,EAAGuB,CAAA,EAAG;IACvB,IAAIG,GAAA,GAAM,KAAKpG,oBAAA,CAAsB;IACrC,IAAIqG,GAAA,GAAM,KAAKlC,eAAA,CAAiB;IAChCkC,GAAA,CAAI7F,GAAA,CAAIyF,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,CAAC;IACxB,KAAKK,2BAAA,CAA4B5B,CAAA,EAAG0B,GAAA,CAAIG,YAAA,CAAaF,GAAG,CAAC;IAEzD,KAAKhC,cAAA,CAAegC,GAAG;IACvB,KAAKvF,mBAAA,CAAoBsF,GAAG;EAC7B;EAEDE,4BAA4B5B,CAAA,EAAGuB,CAAA,EAAG;IAChC,IAAI/B,CAAA,GAAI,KAAKS,eAAA,CAAiB;IAE9BT,CAAA,CAAEsC,IAAA,CAAKP,CAAA,CAAE7F,CAAC;IACV8D,CAAA,CAAEuC,IAAA,CAAKR,CAAA,CAAE5F,CAAC;IACV6D,CAAA,CAAEwC,IAAA,CAAKT,CAAA,CAAE3F,CAAC;IACV4D,CAAA,CAAEyC,IAAA,CAAKV,CAAA,CAAEW,CAAC;IACV,KAAKhB,QAAA,CAASlB,CAAA,EAAGR,CAAC;IAElB,KAAKW,cAAA,CAAeX,CAAC;EACtB;EAED2C,mBAAmBpB,EAAA,EAAIC,EAAA,EAAI;IACzB,IAAIhB,CAAA,GAAI,KAAKH,cAAA,CAAgB;IAC7B,KAAKS,WAAA,CAAYN,CAAC;IAElB,IAAIoC,EAAA,GAAK,KAAK3B,iBAAA,CAAkBM,EAAE;IAClC,IAAIsB,EAAA,GAAK,KAAK5B,iBAAA,CAAkBO,EAAE;IAElC,IAAIsB,EAAA,GAAK,KAAK3B,SAAA,CAAUI,EAAE;IAC1B,IAAIwB,EAAA,GAAK,KAAK5B,SAAA,CAAUK,EAAE;IAE1B,IAAIwB,EAAA,GAAK,KAAKC,wBAAA,CAAyBL,EAAA,EAAIG,EAAE;IAC7C,IAAIG,EAAA,GAAK,KAAKC,UAAA,CAAWH,EAAA,EAAIF,EAAE;IAC/B,KAAK1B,SAAA,CAAUZ,CAAA,EAAG0C,EAAE;IAEpB,IAAIE,EAAA,GAAK,KAAKC,iBAAA,CAAkBT,EAAA,EAAIC,EAAE;IACtC,KAAKjB,mBAAA,CAAoBpB,CAAA,EAAG4C,EAAE;IAE9B,KAAKvC,WAAA,CAAYmC,EAAE;IACnB,KAAKnC,WAAA,CAAYqC,EAAE;IAEnB,OAAO1C,CAAA;EACR;EAED8C,iBAAiB9C,CAAA,EAAG;IAClB,IAAIgB,EAAA,GAAK,KAAKnB,cAAA,CAAgB;IAE9B,IAAIuC,EAAA,GAAK,KAAK3B,iBAAA,CAAkBT,CAAC;IACjC,IAAIiB,CAAA,GAAI,KAAKN,SAAA,CAAUX,CAAC;IAExB,IAAIqC,EAAA,GAAK,KAAKU,gBAAA,CAAiBX,EAAE;IACjC,IAAII,EAAA,GAAK,KAAKQ,eAAA,CAAgB/B,CAAC;IAC/B,IAAIyB,EAAA,GAAK,KAAKD,wBAAA,CAAyBJ,EAAA,EAAIG,EAAE;IAE7C,KAAK5B,SAAA,CAAUI,EAAA,EAAI0B,EAAE;IACrB,KAAKtB,mBAAA,CAAoBJ,EAAA,EAAIqB,EAAE;IAE/B,KAAKhC,WAAA,CAAYmC,EAAE;IACnB,KAAKnC,WAAA,CAAYqC,EAAE;IAEnB,OAAO1B,EAAA;EACR;EAED6B,kBAAkBT,EAAA,EAAIC,EAAA,EAAI;IACxB,IAAIO,EAAA,GAAK,EAAE;IAEX,IAAIK,GAAA,GAAM,KAAKC,YAAA,CAAad,EAAA,EAAI,CAAC;IACjC,IAAIe,GAAA,GAAM,KAAKD,YAAA,CAAad,EAAA,EAAI,CAAC;IACjC,IAAIgB,GAAA,GAAM,KAAKF,YAAA,CAAad,EAAA,EAAI,CAAC;IAEjC,IAAIiB,GAAA,GAAM,KAAKC,eAAA,CAAgBjB,EAAA,EAAI,CAAC;IACpC,IAAIkB,GAAA,GAAM,KAAKD,eAAA,CAAgBjB,EAAA,EAAI,CAAC;IACpC,IAAImB,GAAA,GAAM,KAAKF,eAAA,CAAgBjB,EAAA,EAAI,CAAC;IAEpCO,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYR,GAAA,EAAKI,GAAG;IACjCT,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYR,GAAA,EAAKM,GAAG;IACjCX,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYR,GAAA,EAAKO,GAAG;IACjCZ,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYN,GAAA,EAAKE,GAAG;IACjCT,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYN,GAAA,EAAKI,GAAG;IACjCX,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYN,GAAA,EAAKK,GAAG;IACjCZ,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYL,GAAA,EAAKC,GAAG;IACjCT,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYL,GAAA,EAAKG,GAAG;IACjCX,EAAA,CAAG,CAAC,IAAI,KAAKa,WAAA,CAAYL,GAAA,EAAKI,GAAG;IAEjC,KAAKnD,WAAA,CAAY4C,GAAG;IACpB,KAAK5C,WAAA,CAAY8C,GAAG;IACpB,KAAK9C,WAAA,CAAY+C,GAAG;IACpB,KAAK/C,WAAA,CAAYgD,GAAG;IACpB,KAAKhD,WAAA,CAAYkD,GAAG;IACpB,KAAKlD,WAAA,CAAYmD,GAAG;IAEpB,OAAOZ,EAAA;EACR;EAEDD,WAAWH,EAAA,EAAIE,EAAA,EAAI;IACjB,IAAIxD,CAAA,GAAI,KAAKkB,YAAA,CAAc;IAC3BlB,CAAA,CAAE2B,QAAA,CAAS2B,EAAA,CAAG9G,CAAA,CAAG,IAAGgH,EAAA,CAAGhH,CAAA,CAAC,GAAI8G,EAAA,CAAG7G,CAAA,CAAC,IAAK+G,EAAA,CAAG/G,CAAA,IAAK6G,EAAA,CAAG5G,CAAA,KAAM8G,EAAA,CAAG9G,CAAA,EAAG;IAC5D,OAAOsD,CAAA;EACR;EAEDuE,YAAYjB,EAAA,EAAIE,EAAA,EAAI;IAClB,OAAOF,EAAA,CAAG9G,CAAA,CAAG,IAAGgH,EAAA,CAAGhH,CAAA,CAAC,IAAK8G,EAAA,CAAG7G,CAAA,CAAG,IAAG+G,EAAA,CAAG/G,CAAA,CAAC,IAAK6G,EAAA,CAAG5G,CAAA,CAAG,IAAG8G,EAAA,CAAG9G,CAAA,CAAG;EAC3D;EAEDsH,aAAa5D,CAAA,EAAGhD,CAAA,EAAG;IACjB,IAAI4C,CAAA,GAAI,KAAKkB,YAAA,CAAc;IAC3BlB,CAAA,CAAE2B,QAAA,CAASvB,CAAA,CAAEhD,CAAA,GAAI,IAAI,CAAC,GAAGgD,CAAA,CAAEhD,CAAA,GAAI,IAAI,CAAC,GAAGgD,CAAA,CAAEhD,CAAA,GAAI,IAAI,CAAC,CAAC;IACnD,OAAO4C,CAAA;EACR;EAEDoE,gBAAgBhE,CAAA,EAAGhD,CAAA,EAAG;IACpB,IAAI4C,CAAA,GAAI,KAAKkB,YAAA,CAAc;IAC3BlB,CAAA,CAAE2B,QAAA,CAASvB,CAAA,CAAEhD,CAAA,GAAI,CAAC,GAAGgD,CAAA,CAAEhD,CAAA,GAAI,CAAC,GAAGgD,CAAA,CAAEhD,CAAA,GAAI,CAAC,CAAC;IACvC,OAAO4C,CAAA;EACR;EAED8D,gBAAgB9D,CAAA,EAAG;IACjB,IAAIwD,EAAA,GAAK,KAAKtC,YAAA,CAAc;IAC5BsC,EAAA,CAAG7B,QAAA,CAAS,CAAC3B,CAAA,CAAExD,CAAA,CAAG,GAAE,CAACwD,CAAA,CAAEvD,CAAA,CAAG,GAAE,CAACuD,CAAA,CAAEtD,CAAA,CAAC,CAAE;IAClC,OAAO8G,EAAA;EACR;EAEDD,yBAAyBnD,CAAA,EAAGJ,CAAA,EAAG;IAC7B,IAAIwE,EAAA,GAAK,KAAKtD,YAAA,CAAc;IAE5B,IAAIuD,EAAA,GAAK,KAAKT,YAAA,CAAa5D,CAAA,EAAG,CAAC;IAC/B,IAAIkD,EAAA,GAAK,KAAKU,YAAA,CAAa5D,CAAA,EAAG,CAAC;IAC/B,IAAIoD,EAAA,GAAK,KAAKQ,YAAA,CAAa5D,CAAA,EAAG,CAAC;IAC/B,IAAI5D,CAAA,GAAI,KAAK+H,WAAA,CAAYE,EAAA,EAAIzE,CAAC;IAC9B,IAAIvD,CAAA,GAAI,KAAK8H,WAAA,CAAYjB,EAAA,EAAItD,CAAC;IAC9B,IAAItD,CAAA,GAAI,KAAK6H,WAAA,CAAYf,EAAA,EAAIxD,CAAC;IAE9BwE,EAAA,CAAG7C,QAAA,CAASnF,CAAA,EAAGC,CAAA,EAAGC,CAAC;IAEnB,KAAKyE,WAAA,CAAYsD,EAAE;IACnB,KAAKtD,WAAA,CAAYmC,EAAE;IACnB,KAAKnC,WAAA,CAAYqC,EAAE;IAEnB,OAAOgB,EAAA;EACR;EAEDX,iBAAiBzD,CAAA,EAAG;IAClB,IAAI+C,EAAA,GAAK,EAAE;IACXA,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX+C,EAAA,CAAG,CAAC,IAAI/C,CAAA,CAAE,CAAC;IACX,OAAO+C,EAAA;EACR;EAED3B,oBAAoBlB,CAAA,EAAG;IACrB,IAAIF,CAAA,GAAI,EAAE;IAEV,IAAI5D,CAAA,GAAI8D,CAAA,CAAE9D,CAAA,CAAG;IACb,IAAIC,CAAA,GAAI6D,CAAA,CAAE7D,CAAA,CAAG;IACb,IAAIC,CAAA,GAAI4D,CAAA,CAAE5D,CAAA,CAAG;IACb,IAAIsG,CAAA,GAAI1C,CAAA,CAAE0C,CAAA,CAAG;IAEb,IAAI0B,EAAA,GAAKlI,CAAA,GAAIA,CAAA;IACb,IAAImI,EAAA,GAAKlI,CAAA,GAAIA,CAAA;IACb,IAAImI,EAAA,GAAKlI,CAAA,GAAIA,CAAA;IAEb,IAAImI,EAAA,GAAKrI,CAAA,GAAIC,CAAA;IACb,IAAIqI,EAAA,GAAKrI,CAAA,GAAIC,CAAA;IACb,IAAIqI,EAAA,GAAKrI,CAAA,GAAIF,CAAA;IAEb,IAAIwI,EAAA,GAAKxI,CAAA,GAAIwG,CAAA;IACb,IAAIiC,EAAA,GAAKxI,CAAA,GAAIuG,CAAA;IACb,IAAIkC,EAAA,GAAKxI,CAAA,GAAIsG,CAAA;IAEb5C,CAAA,CAAE,CAAC,IAAI,IAAI,KAAKuE,EAAA,GAAKC,EAAA;IACrBxE,CAAA,CAAE,CAAC,IAAI,KAAKyE,EAAA,GAAKK,EAAA;IACjB9E,CAAA,CAAE,CAAC,IAAI,KAAK2E,EAAA,GAAKE,EAAA;IACjB7E,CAAA,CAAE,CAAC,IAAI,KAAKyE,EAAA,GAAKK,EAAA;IACjB9E,CAAA,CAAE,CAAC,IAAI,IAAI,KAAKwE,EAAA,GAAKF,EAAA;IACrBtE,CAAA,CAAE,CAAC,IAAI,KAAK0E,EAAA,GAAKE,EAAA;IACjB5E,CAAA,CAAE,CAAC,IAAI,KAAK2E,EAAA,GAAKE,EAAA;IACjB7E,CAAA,CAAE,CAAC,IAAI,KAAK0E,EAAA,GAAKE,EAAA;IACjB5E,CAAA,CAAE,CAAC,IAAI,IAAI,KAAKsE,EAAA,GAAKC,EAAA;IAErB,OAAOvE,CAAA;EACR;EAED+B,oBAAoB/B,CAAA,EAAG;IACrB,IAAIU,CAAA,GAAIV,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC;IACzB,IAAI+E,CAAA,EAAG3I,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGsG,CAAA;IAEhB,IAAIlC,CAAA,GAAI,GAAG;MACTqE,CAAA,GAAIC,IAAA,CAAKC,IAAA,CAAKvE,CAAA,GAAI,CAAG,IAAI;MACzBkC,CAAA,GAAI,OAAOmC,CAAA;MACX3I,CAAA,IAAK4D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB1I,CAAA,IAAK2D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpBzI,CAAA,IAAK0D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;IACrB,WAAU/E,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAKA,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,GAAG;MACrC+E,CAAA,GAAIC,IAAA,CAAKC,IAAA,CAAK,IAAMjF,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,CAAC,IAAI;MAC1C4C,CAAA,IAAK5C,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB3I,CAAA,GAAI,OAAO2I,CAAA;MACX1I,CAAA,IAAK2D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpBzI,CAAA,IAAK0D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;IACrB,WAAU/E,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,GAAG;MACtB+E,CAAA,GAAIC,IAAA,CAAKC,IAAA,CAAK,IAAMjF,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,CAAC,IAAI;MAC1C4C,CAAA,IAAK5C,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB3I,CAAA,IAAK4D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB1I,CAAA,GAAI,OAAO0I,CAAA;MACXzI,CAAA,IAAK0D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;IAC1B,OAAW;MACLA,CAAA,GAAIC,IAAA,CAAKC,IAAA,CAAK,IAAMjF,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,CAAC,IAAI;MAC1C4C,CAAA,IAAK5C,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB3I,CAAA,IAAK4D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpB1I,CAAA,IAAK2D,CAAA,CAAE,CAAC,IAAIA,CAAA,CAAE,CAAC,KAAK+E,CAAA;MACpBzI,CAAA,GAAI,OAAOyI,CAAA;IACZ;IAED,IAAI7E,CAAA,GAAI,KAAKS,eAAA,CAAiB;IAC9BT,CAAA,CAAEsC,IAAA,CAAKpG,CAAC;IACR8D,CAAA,CAAEuC,IAAA,CAAKpG,CAAC;IACR6D,CAAA,CAAEwC,IAAA,CAAKpG,CAAC;IACR4D,CAAA,CAAEyC,IAAA,CAAKC,CAAC;IACR,OAAO1C,CAAA;EACR;AACH;AAQA,MAAMxB,SAAA,CAAU;EACdlE,YAAYC,IAAA,EAAMa,KAAA,EAAOV,MAAA,EAAQG,OAAA,EAAS;IACxC,KAAKN,IAAA,GAAOA,IAAA;IACZ,KAAKa,KAAA,GAAQA,KAAA;IACb,KAAKV,MAAA,GAASA,MAAA;IACd,KAAKG,OAAA,GAAUA,OAAA;IAEf,KAAKmK,IAAA,GAAO;IACZ,KAAKC,IAAA,GAAO;IACZ,KAAKC,cAAA,GAAiB;IACtB,KAAKC,qBAAA,GAAwB;IAE7B,KAAK5J,KAAA,CAAO;EACb;EAAA;AAAA;AAAA;AAAA;AAAA;EAODsB,MAAA,EAAQ;IACN,KAAKuI,qBAAA,CAAuB;IAC5B,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODpG,eAAA,EAAiB;IACf,IAAI,KAAKtE,MAAA,CAAO2K,SAAA,KAAc,MAAM,KAAK3K,MAAA,CAAO4K,IAAA,KAAS,GAAG;MAC1D,KAAKF,qBAAA,CAAuB;IAC7B;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODnG,WAAA,EAAa;IACX,IAAI,KAAKvE,MAAA,CAAO4K,IAAA,KAAS,KAAK,KAAK5K,MAAA,CAAO2K,SAAA,KAAc,IAAI;MAC1D,OAAO;IACR;IAED,KAAKE,mBAAA,CAAqB;IAE1B,IAAI,KAAK7K,MAAA,CAAO4K,IAAA,KAAS,GAAG;MAC1B,KAAKE,mBAAA,CAAqB;IAC3B;IAED,KAAKP,IAAA,CAAK1I,iBAAA,CAAkB,IAAI;IAEhC,IAAI,KAAK7B,MAAA,CAAO4K,IAAA,KAAS,GAAG;MAC1B,KAAKG,oBAAA,CAAsB;IAC5B;IAED,OAAO;EACR;EAAA;EAIDlK,MAAA,EAAQ;IACN,SAASmK,cAAcC,CAAA,EAAG;MACxB,QAAQA,CAAA,CAAEC,SAAA;QACR,KAAK;UACH,OAAO,IAAIjL,IAAA,CAAKkL,aAAA,CAAcF,CAAA,CAAEG,KAAK;QAEvC,KAAK;UACH,OAAO,IAAInL,IAAA,CAAKoL,UAAA,CAAW,IAAIpL,IAAA,CAAKyC,SAAA,CAAUuI,CAAA,CAAEG,KAAA,EAAOH,CAAA,CAAEK,MAAA,EAAQL,CAAA,CAAEM,KAAK,CAAC;QAE3E,KAAK;UACH,OAAO,IAAItL,IAAA,CAAKuL,cAAA,CAAeP,CAAA,CAAEG,KAAA,EAAOH,CAAA,CAAEK,MAAM;QAElD;UACE,MAAM,IAAIpL,KAAA,CAAM,wBAAwB+K,CAAA,CAAEC,SAAS;MACtD;IACF;IAED,MAAM/K,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMH,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMyL,KAAA,GAAQ,KAAK5L,IAAA,CAAK6L,QAAA,CAASD,KAAA;IACjC,MAAMlB,IAAA,GAAOvK,MAAA,CAAO2K,SAAA,KAAc,KAAK,IAAIgB,IAAA,KAASF,KAAA,CAAMzL,MAAA,CAAO2K,SAAS;IAE1E,MAAMiB,KAAA,GAAQZ,aAAA,CAAchL,MAAM;IAClC,MAAM6L,MAAA,GAAS7L,MAAA,CAAO4K,IAAA,KAAS,IAAI,IAAI5K,MAAA,CAAO6L,MAAA;IAC9C,MAAMC,YAAA,GAAe3L,OAAA,CAAQ+F,YAAA,CAAc;IAC3C4F,YAAA,CAAanF,QAAA,CAAS,GAAG,GAAG,CAAC;IAE7B,IAAIkF,MAAA,KAAW,GAAG;MAChBD,KAAA,CAAMG,qBAAA,CAAsBF,MAAA,EAAQC,YAAY;IACjD;IAED,MAAMtB,cAAA,GAAiBrK,OAAA,CAAQwF,cAAA,CAAgB;IAC/CxF,OAAA,CAAQiG,WAAA,CAAYoE,cAAc;IAClCrK,OAAA,CAAQiH,mBAAA,CAAoBoD,cAAA,EAAgBxK,MAAA,CAAOiB,QAAQ;IAC3Dd,OAAA,CAAQoH,kBAAA,CAAmBiD,cAAA,EAAgBxK,MAAA,CAAOgM,QAAQ;IAE1D,MAAMC,MAAA,GAAS9L,OAAA,CAAQe,iBAAA,CAAmB;IAC1C,MAAMgL,QAAA,GAAW/L,OAAA,CAAQwF,cAAA,CAAgB;IACzCxF,OAAA,CAAQiG,WAAA,CAAY8F,QAAQ;IAC5B/L,OAAA,CAAQmH,yBAAA,CAA0B4E,QAAA,EAAU3B,IAAA,CAAK4B,gBAAA,CAAiBF,MAAM,CAAC;IAEzE,MAAMG,IAAA,GAAOjM,OAAA,CAAQ8H,kBAAA,CAAmBiE,QAAA,EAAU1B,cAAc;IAChE,MAAM6B,KAAA,GAAQ,IAAIpM,IAAA,CAAKqM,oBAAA,CAAqBF,IAAI;IAEhD,MAAMG,IAAA,GAAO,IAAItM,IAAA,CAAKuM,2BAAA,CAA4BX,MAAA,EAAQQ,KAAA,EAAOT,KAAA,EAAOE,YAAY;IACpFS,IAAA,CAAKE,cAAA,CAAezM,MAAA,CAAO0M,QAAQ;IACnCH,IAAA,CAAKI,iBAAA,CAAkB3M,MAAA,CAAO4M,WAAW;IAEzC,MAAMtC,IAAA,GAAO,IAAIrK,IAAA,CAAK4M,WAAA,CAAYN,IAAI;IAEtC,IAAIvM,MAAA,CAAO4K,IAAA,KAAS,GAAG;MACrBN,IAAA,CAAKwC,iBAAA,CAAkBxC,IAAA,CAAKyC,iBAAA,CAAiB,IAAK,CAAC;MAOnDzC,IAAA,CAAK0C,kBAAA,CAAmB,CAAC;IAC1B;IAED1C,IAAA,CAAK2C,UAAA,CAAWjN,MAAA,CAAOkN,eAAA,EAAiBlN,MAAA,CAAOmN,eAAe;IAC9D7C,IAAA,CAAK8C,qBAAA,CAAsB,GAAG,CAAC;IAE/B,KAAK1M,KAAA,CAAM2M,YAAA,CAAa/C,IAAA,EAAM,KAAKtK,MAAA,CAAOsN,UAAA,EAAYtN,MAAA,CAAOuN,WAAW;IAExE,KAAKjD,IAAA,GAAOA,IAAA;IACZ,KAAKC,IAAA,GAAOA,IAAA;IACZ,KAAKC,cAAA,GAAiBA,cAAA;IACtB,KAAKC,qBAAA,GAAwBtK,OAAA,CAAQyI,gBAAA,CAAiB4B,cAAc;IAEpErK,OAAA,CAAQgG,WAAA,CAAY2F,YAAY;IAChC3L,OAAA,CAAQ0F,aAAA,CAAcuG,IAAI;IAC1BjM,OAAA,CAAQ0F,aAAA,CAAcqG,QAAQ;IAC9B/L,OAAA,CAAQ8B,gBAAA,CAAiBgK,MAAM;EAChC;EAEDuB,kBAAA,EAAoB;IAClB,MAAMrN,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAM8K,CAAA,GAAI9K,OAAA,CAAQe,iBAAA,CAAmB;IACrC,MAAMoE,CAAA,GAAInF,OAAA,CAAQiB,oBAAA,CAAsB;IACxC,MAAM+I,CAAA,GAAIhK,OAAA,CAAQe,iBAAA,CAAmB;IAErC,KAAKqJ,IAAA,CAAKjJ,WAAA,CAAYC,SAAA,CAAU0J,CAAA,EAAG3F,CAAA,EAAG6E,CAAC;IAEvC,MAAMsD,EAAA,GAAKtN,OAAA,CAAQwF,cAAA,CAAgB;IACnCxF,OAAA,CAAQmH,yBAAA,CAA0BmG,EAAA,EAAIxC,CAAC;IACvC9K,OAAA,CAAQuH,2BAAA,CAA4B+F,EAAA,EAAInI,CAAC;IAEzC,MAAM8G,IAAA,GAAOjM,OAAA,CAAQ8H,kBAAA,CAAmBwF,EAAA,EAAI,KAAKjD,cAAc;IAE/DrK,OAAA,CAAQ0F,aAAA,CAAc4H,EAAE;IACxBtN,OAAA,CAAQ8B,gBAAA,CAAiBkI,CAAC;IAC1BhK,OAAA,CAAQ+B,mBAAA,CAAoBoD,CAAC;IAC7BnF,OAAA,CAAQ8B,gBAAA,CAAiBgJ,CAAC;IAE1B,OAAOmB,IAAA;EACR;EAEDsB,0BAAA,EAA4B;IAC1B,MAAMvN,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMsN,EAAA,GAAK,KAAKnD,IAAA,CAAKqD,wBAAA,CAA0B;IAC/C,OAAOxN,OAAA,CAAQ8H,kBAAA,CAAmBwF,EAAA,EAAI,KAAKhD,qBAAqB;EACjE;EAEDC,sBAAA,EAAwB;IACtB,MAAMvK,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMiM,IAAA,GAAO,KAAKoB,iBAAA,CAAmB;IAIrC,KAAKlD,IAAA,CAAKsD,wBAAA,CAAyBxB,IAAI;IACvC,KAAK9B,IAAA,CAAKuD,cAAA,GAAiBC,iBAAA,CAAkB1B,IAAI;IAEjDjM,OAAA,CAAQ0F,aAAA,CAAcuG,IAAI;EAC3B;EAEDrB,qBAAA,EAAuB;IACrB,MAAM5K,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMiM,IAAA,GAAO,KAAKoB,iBAAA,CAAmB;IAErC,MAAMC,EAAA,GAAKtN,OAAA,CAAQwF,cAAA,CAAgB;IACnC,KAAK2E,IAAA,CAAKuD,cAAA,GAAiBE,iBAAA,CAAkBN,EAAE;IAC/CtN,OAAA,CAAQyG,UAAA,CAAW6G,EAAA,EAAIrB,IAAI;IAI3B,KAAK9B,IAAA,CAAKsD,wBAAA,CAAyBH,EAAE;IACrC,KAAKnD,IAAA,CAAKuD,cAAA,GAAiBC,iBAAA,CAAkBL,EAAE;IAE/CtN,OAAA,CAAQ0F,aAAA,CAAc4H,EAAE;IACxBtN,OAAA,CAAQ0F,aAAA,CAAcuG,IAAI;EAC3B;EAEDvB,oBAAA,EAAsB;IACpB,MAAM1K,OAAA,GAAU,KAAKA,OAAA;IAErB,MAAMsN,EAAA,GAAK,KAAKC,yBAAA,CAA2B;IAC3C,MAAMpI,CAAA,GAAInF,OAAA,CAAQkG,QAAA,CAASoH,EAAE;IAE7B,MAAMjG,GAAA,GAAMrH,OAAA,CAAQiB,oBAAA,CAAsB;IAC1C,MAAM4M,IAAA,GAAO7N,OAAA,CAAQiB,oBAAA,CAAsB;IAC3C,MAAM6M,IAAA,GAAO9N,OAAA,CAAQiB,oBAAA,CAAsB;IAE3CoG,GAAA,CAAI5F,GAAA,CAAI0D,CAAA,CAAE9D,CAAA,CAAG,GAAE8D,CAAA,CAAE7D,CAAA,CAAG,GAAE6D,CAAA,CAAE5D,CAAA,CAAC,GAAI4D,CAAA,CAAE0C,CAAA,CAAC,CAAE;IAClCgG,IAAA,CAAKE,qBAAA,CAAsB,KAAK3D,IAAA,CAAKjJ,WAAW;IAChD0M,IAAA,CAAKG,SAAA,CAAW;IAChBH,IAAA,CAAKI,QAAA,CAAS5G,GAAG;IAIjByG,IAAA,CAAKC,qBAAA,CAAsB,KAAK3D,IAAA,CAAK8D,MAAM;IAK3C,KAAK9D,IAAA,CAAKpJ,UAAA,CAAWV,IAAA,CAAKuN,IAAA,CAAKI,QAAA,CAASH,IAAI,EAAEK,SAAA,EAAW;IAEzDnO,OAAA,CAAQ+B,mBAAA,CAAoBsF,GAAG;IAC/BrH,OAAA,CAAQ+B,mBAAA,CAAoB8L,IAAI;IAChC7N,OAAA,CAAQ+B,mBAAA,CAAoB+L,IAAI;IAEhC9N,OAAA,CAAQ8F,cAAA,CAAeX,CAAC;IACxBnF,OAAA,CAAQ0F,aAAA,CAAc4H,EAAE;EACzB;EAED3C,oBAAA,EAAsB;IACpB,MAAM3K,OAAA,GAAU,KAAKA,OAAA;IAErB,MAAMsN,EAAA,GAAK,KAAKC,yBAAA,CAA2B;IAE3C,MAAMa,GAAA,GAAMpO,OAAA,CAAQe,iBAAA,CAAmB;IAEvC,MAAM6F,CAAA,GAAI5G,OAAA,CAAQsG,SAAA,CAAUgH,EAAE;IAC9Bc,GAAA,CAAI3M,GAAA,CAAImF,CAAA,CAAEvF,CAAA,CAAG,GAAEuF,CAAA,CAAEtF,CAAA,CAAG,GAAEsF,CAAA,CAAErF,CAAA,EAAG;IAE3B,IAAI,KAAK6I,IAAA,CAAK5I,MAAA,EAAQ;MACpB,KAAK4I,IAAA,CAAK5I,MAAA,CAAO6M,YAAA,CAAaD,GAAG;IAClC;IAED,KAAKhE,IAAA,CAAKtJ,QAAA,CAASR,IAAA,CAAK8N,GAAG;IAE3BpO,OAAA,CAAQ8B,gBAAA,CAAiBsM,GAAG;IAE5BpO,OAAA,CAAQ0F,aAAA,CAAc4H,EAAE;EACzB;AACH;AAIA,MAAMtJ,UAAA,CAAW;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASfvE,YAAYC,IAAA,EAAMa,KAAA,EAAOqD,KAAA,EAAOE,KAAA,EAAOjE,MAAA,EAAQG,OAAA,EAAS;IACtD,KAAKN,IAAA,GAAOA,IAAA;IACZ,KAAKa,KAAA,GAAQA,KAAA;IACb,KAAKqD,KAAA,GAAQA,KAAA;IACb,KAAKE,KAAA,GAAQA,KAAA;IACb,KAAKjE,MAAA,GAASA,MAAA;IACd,KAAKG,OAAA,GAAUA,OAAA;IAEf,KAAKsO,UAAA,GAAa;IAElB,KAAK5N,KAAA,CAAO;EACb;EAAA;EAIDA,MAAA,EAAQ;IACN,MAAMV,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMH,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM+D,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAME,KAAA,GAAQ,KAAKA,KAAA;IAEnB,MAAMmI,IAAA,GAAOjM,OAAA,CAAQwF,cAAA,CAAgB;IACrCxF,OAAA,CAAQiG,WAAA,CAAYgG,IAAI;IACxBjM,OAAA,CAAQiH,mBAAA,CAAoBgF,IAAA,EAAMpM,MAAA,CAAOiB,QAAQ;IACjDd,OAAA,CAAQoH,kBAAA,CAAmB6E,IAAA,EAAMpM,MAAA,CAAOgM,QAAQ;IAEhD,MAAM0C,KAAA,GAAQvO,OAAA,CAAQwF,cAAA,CAAgB;IACtC,MAAMgJ,KAAA,GAAQxO,OAAA,CAAQwF,cAAA,CAAgB;IAEtC5B,KAAA,CAAMuG,IAAA,CAAKuD,cAAA,GAAiBE,iBAAA,CAAkBW,KAAK;IACnDzK,KAAA,CAAMqG,IAAA,CAAKuD,cAAA,GAAiBE,iBAAA,CAAkBY,KAAK;IAEnD,MAAMC,YAAA,GAAezO,OAAA,CAAQyI,gBAAA,CAAiB8F,KAAK;IACnD,MAAMG,YAAA,GAAe1O,OAAA,CAAQyI,gBAAA,CAAiB+F,KAAK;IAEnD,MAAMG,MAAA,GAAS3O,OAAA,CAAQ8H,kBAAA,CAAmB2G,YAAA,EAAcxC,IAAI;IAC5D,MAAM2C,MAAA,GAAS5O,OAAA,CAAQ8H,kBAAA,CAAmB4G,YAAA,EAAczC,IAAI;IAE5D,MAAMqC,UAAA,GAAa,IAAIxO,IAAA,CAAK+O,6BAAA,CAA8BjL,KAAA,CAAMuG,IAAA,EAAMrG,KAAA,CAAMqG,IAAA,EAAMwE,MAAA,EAAQC,MAAA,EAAQ,IAAI;IAEtG,MAAME,GAAA,GAAM9O,OAAA,CAAQ+F,YAAA,CAAc;IAClC,MAAMgJ,GAAA,GAAM/O,OAAA,CAAQ+F,YAAA,CAAc;IAClC,MAAMiJ,GAAA,GAAMhP,OAAA,CAAQ+F,YAAA,CAAc;IAClC,MAAMkJ,GAAA,GAAMjP,OAAA,CAAQ+F,YAAA,CAAc;IAElC+I,GAAA,CAAItI,QAAA,CAAS3G,MAAA,CAAOqP,sBAAA,CAAuB,CAAC,GAAGrP,MAAA,CAAOqP,sBAAA,CAAuB,CAAC,GAAGrP,MAAA,CAAOqP,sBAAA,CAAuB,CAAC,CAAC;IACjHH,GAAA,CAAIvI,QAAA,CAAS3G,MAAA,CAAOsP,sBAAA,CAAuB,CAAC,GAAGtP,MAAA,CAAOsP,sBAAA,CAAuB,CAAC,GAAGtP,MAAA,CAAOsP,sBAAA,CAAuB,CAAC,CAAC;IACjHH,GAAA,CAAIxI,QAAA,CAAS3G,MAAA,CAAOuP,mBAAA,CAAoB,CAAC,GAAGvP,MAAA,CAAOuP,mBAAA,CAAoB,CAAC,GAAGvP,MAAA,CAAOuP,mBAAA,CAAoB,CAAC,CAAC;IACxGH,GAAA,CAAIzI,QAAA,CAAS3G,MAAA,CAAOwP,mBAAA,CAAoB,CAAC,GAAGxP,MAAA,CAAOwP,mBAAA,CAAoB,CAAC,GAAGxP,MAAA,CAAOwP,mBAAA,CAAoB,CAAC,CAAC;IAExGf,UAAA,CAAWgB,mBAAA,CAAoBR,GAAG;IAClCR,UAAA,CAAWiB,mBAAA,CAAoBR,GAAG;IAClCT,UAAA,CAAWkB,oBAAA,CAAqBR,GAAG;IACnCV,UAAA,CAAWmB,oBAAA,CAAqBR,GAAG;IAEnC,SAAShN,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,IAAIpC,MAAA,CAAO6P,cAAA,CAAezN,CAAC,MAAM,GAAG;QAClCqM,UAAA,CAAWqB,YAAA,CAAa1N,CAAA,EAAG,IAAI;QAC/BqM,UAAA,CAAWsB,YAAA,CAAa3N,CAAA,EAAGpC,MAAA,CAAO6P,cAAA,CAAezN,CAAC,CAAC;MACpD;IACF;IAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,IAAIpC,MAAA,CAAOgQ,cAAA,CAAe5N,CAAC,MAAM,GAAG;QAClCqM,UAAA,CAAWqB,YAAA,CAAa1N,CAAA,GAAI,GAAG,IAAI;QACnCqM,UAAA,CAAWsB,YAAA,CAAa3N,CAAA,GAAI,GAAGpC,MAAA,CAAOgQ,cAAA,CAAe5N,CAAC,CAAC;MACxD;IACF;IAQD,IAAIqM,UAAA,CAAWwB,QAAA,KAAa,QAAW;MACrC,SAAS7N,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BqM,UAAA,CAAWwB,QAAA,CAAS,GAAG,OAAO7N,CAAC;MAChC;IACF;IAED,KAAK1B,KAAA,CAAMwP,aAAA,CAAczB,UAAA,EAAY,IAAI;IACzC,KAAKA,UAAA,GAAaA,UAAA;IAElBtO,OAAA,CAAQ0F,aAAA,CAAcuG,IAAI;IAC1BjM,OAAA,CAAQ0F,aAAA,CAAc6I,KAAK;IAC3BvO,OAAA,CAAQ0F,aAAA,CAAc8I,KAAK;IAC3BxO,OAAA,CAAQ0F,aAAA,CAAc+I,YAAY;IAClCzO,OAAA,CAAQ0F,aAAA,CAAcgJ,YAAY;IAClC1O,OAAA,CAAQ0F,aAAA,CAAciJ,MAAM;IAC5B3O,OAAA,CAAQ0F,aAAA,CAAckJ,MAAM;IAC5B5O,OAAA,CAAQgG,WAAA,CAAY8I,GAAG;IACvB9O,OAAA,CAAQgG,WAAA,CAAY+I,GAAG;IACvB/O,OAAA,CAAQgG,WAAA,CAAYgJ,GAAG;IACvBhP,OAAA,CAAQgG,WAAA,CAAYiJ,GAAG;EACxB;AACH;AAEA,MAAMe,SAAA,GAA4B,mBAAI3P,OAAA,CAAS;AAC/C,MAAM4P,WAAA,GAA8B,mBAAI/K,UAAA,CAAY;AACpD,MAAMgL,MAAA,GAAyB,mBAAI7P,OAAA,CAAS;AAC5C,MAAM8P,eAAA,GAAkC,mBAAIpL,OAAA,CAAS;AAErD,MAAMtC,gBAAA,SAAyB2N,QAAA,CAAS;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOtC3Q,YAAYC,IAAA,EAAM2Q,OAAA,EAAS;IACzB,MAAO;IAEP,KAAKC,IAAA,GAAO5Q,IAAA;IACZ,KAAK2Q,OAAA,GAAUA,OAAA;IAEf,KAAKnC,MAAA,CAAO5N,IAAA,CAAKZ,IAAA,CAAKyB,WAAW;IACjC,KAAKoP,gBAAA,GAAmB;IAExB,KAAKC,SAAA,GAAY,EAAE;IAEnB,KAAKA,SAAA,CAAU9M,IAAA,CACb,IAAI+M,iBAAA,CAAkB;MACpBC,KAAA,EAAO,IAAIC,KAAA,CAAM,QAAQ;MACzBC,SAAA,EAAW;MACXC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,OAAA,EAAS;MACTC,WAAA,EAAa;IACrB,CAAO,CACF;IAED,KAAKR,SAAA,CAAU9M,IAAA,CACb,IAAI+M,iBAAA,CAAkB;MACpBC,KAAA,EAAO,IAAIC,KAAA,CAAM,OAAQ;MACzBC,SAAA,EAAW;MACXC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,OAAA,EAAS;MACTC,WAAA,EAAa;IACrB,CAAO,CACF;IAED,KAAKR,SAAA,CAAU9M,IAAA,CACb,IAAI+M,iBAAA,CAAkB;MACpBC,KAAA,EAAO,IAAIC,KAAA,CAAM,OAAQ;MACzBC,SAAA,EAAW;MACXC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,OAAA,EAAS;MACTC,WAAA,EAAa;IACrB,CAAO,CACF;IAED,KAAKtQ,KAAA,CAAO;EACb;EAAA;AAAA;AAAA;EAKDuQ,QAAA,EAAU;IACR,MAAMT,SAAA,GAAY,KAAKA,SAAA;IACvB,MAAMU,QAAA,GAAW,KAAKA,QAAA;IAEtB,SAASjP,CAAA,GAAI,GAAGA,CAAA,GAAIuO,SAAA,CAAUrO,MAAA,EAAQF,CAAA,IAAK;MACzCuO,SAAA,CAAUvO,CAAC,EAAEgP,OAAA,CAAS;IACvB;IAED,SAAShP,CAAA,GAAI,GAAGA,CAAA,GAAIiP,QAAA,CAAS/O,MAAA,EAAQF,CAAA,IAAK;MACxC,MAAMkP,KAAA,GAAQD,QAAA,CAASjP,CAAC;MAExB,IAAIkP,KAAA,CAAMC,MAAA,EAAQD,KAAA,CAAME,QAAA,CAASJ,OAAA,CAAS;IAC3C;EACF;EAAA;AAAA;AAAA;EAKDvP,kBAAkB4P,KAAA,EAAO;IACvB,IAAI5R,IAAA,GAAO,KAAK4Q,IAAA;IAEhB,IAAI,KAAKiB,OAAA,EAAS;MAChB,IAAI/Q,MAAA,GAAS,KAAK6P,OAAA,CAAQ7P,MAAA;MAE1B2P,eAAA,CACG7P,IAAA,CAAKZ,IAAA,CAAKyB,WAAW,EACrBC,SAAA,CAAU4O,SAAA,EAAWC,WAAA,EAAaC,MAAM,EACxCsB,OAAA,CAAQxB,SAAA,EAAWC,WAAA,EAAaC,MAAA,CAAOzO,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC,EACnDgQ,MAAA,CAAQ;MAEX,SAASxP,CAAA,GAAI,GAAGC,EAAA,GAAK1B,MAAA,CAAO2B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/C,IAAIkI,IAAA,GAAO3J,MAAA,CAAOyB,CAAC,EAAEkI,IAAA;QACrB,IAAIgH,KAAA,GAAQ,KAAKD,QAAA,CAASjP,CAAC;QAE3B,IAAIqL,EAAA,GAAKnD,IAAA,CAAKqD,wBAAA,CAA0B;QACxC,IAAIkE,MAAA,GAASpE,EAAA,CAAGhH,SAAA,CAAW;QAC3B,IAAIuF,QAAA,GAAWyB,EAAA,CAAGnH,WAAA,CAAa;QAE/BgL,KAAA,CAAMrQ,QAAA,CAASW,GAAA,CAAIiQ,MAAA,CAAOrQ,CAAA,CAAC,GAAIqQ,MAAA,CAAOpQ,CAAA,CAAC,GAAIoQ,MAAA,CAAOnQ,CAAA,CAAG,GAAEoQ,YAAA,CAAaxB,eAAe;QAEnFgB,KAAA,CAAMnQ,UAAA,CACH+M,qBAAA,CAAsBoC,eAAe,EACrClC,QAAA,CAASgC,WAAA,CAAYxO,GAAA,CAAIoK,QAAA,CAASxK,CAAA,CAAG,GAAEwK,QAAA,CAASvK,CAAA,CAAG,GAAEuK,QAAA,CAAStK,CAAA,CAAC,GAAIsK,QAAA,CAAShE,CAAA,CAAG,EAAC;MACpF;IACF;IAED,KAAKqG,MAAA,CACF5N,IAAA,CAAKZ,IAAA,CAAKyB,WAAW,EACrBC,SAAA,CAAU4O,SAAA,EAAWC,WAAA,EAAaC,MAAM,EACxCsB,OAAA,CAAQxB,SAAA,EAAWC,WAAA,EAAaC,MAAA,CAAOzO,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;IAEtD,MAAMC,iBAAA,CAAkB4P,KAAK;EAC9B;EAAA;EAID5Q,MAAA,EAAQ;IACN,IAAIF,MAAA,GAAS,KAAK6P,OAAA,CAAQ7P,MAAA;IAE1B,SAASoR,eAAeC,MAAA,EAAO;MAC7B,QAAQA,MAAA,CAAM9G,SAAA;QACZ,KAAK;UACH,OAAO,IAAI+G,cAAA,CAAeD,MAAA,CAAM5G,KAAA,EAAO,IAAI,CAAC;QAE9C,KAAK;UACH,OAAO,IAAI8G,WAAA,CAAYF,MAAA,CAAM5G,KAAA,GAAQ,GAAG4G,MAAA,CAAM1G,MAAA,GAAS,GAAG0G,MAAA,CAAMzG,KAAA,GAAQ,GAAG,GAAG,GAAG,CAAC;QAEpF,KAAK;UACH,OAAO,IAAI4G,eAAA,CAAgBH,MAAA,CAAM5G,KAAA,EAAO4G,MAAA,CAAM1G,MAAA,EAAQ,GAAG,EAAE;QAE7D;UACE,OAAO;MACV;IACF;IAED,SAASlJ,CAAA,GAAI,GAAGC,EAAA,GAAK1B,MAAA,CAAO2B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC/C,IAAIgQ,KAAA,GAAQzR,MAAA,CAAOyB,CAAC,EAAEpC,MAAA;MACtB,KAAKqS,GAAA,CAAI,IAAIC,IAAA,CAAKP,cAAA,CAAeK,KAAK,GAAG,KAAKzB,SAAA,CAAUyB,KAAA,CAAMxH,IAAI,CAAC,CAAC;IACrE;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}