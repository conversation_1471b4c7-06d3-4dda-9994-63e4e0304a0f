{"ast": null, "code": "import { StereoCamera, Vector2 } from \"three\";\nclass StereoEffect {\n  constructor(renderer) {\n    const _stereo = new StereoCamera();\n    _stereo.aspect = 0.5;\n    const size = new Vector2();\n    this.setEyeSeparation = function (eyeSep) {\n      _stereo.eyeSep = eyeSep;\n    };\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height);\n    };\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      _stereo.update(camera);\n      renderer.getSize(size);\n      if (renderer.autoClear) renderer.clear();\n      renderer.setScissorTest(true);\n      renderer.setScissor(0, 0, size.width / 2, size.height);\n      renderer.setViewport(0, 0, size.width / 2, size.height);\n      renderer.render(scene, _stereo.cameraL);\n      renderer.setScissor(size.width / 2, 0, size.width / 2, size.height);\n      renderer.setViewport(size.width / 2, 0, size.width / 2, size.height);\n      renderer.render(scene, _stereo.cameraR);\n      renderer.setScissorTest(false);\n    };\n  }\n}\nexport { StereoEffect };", "map": {"version": 3, "names": ["StereoEffect", "constructor", "renderer", "_stereo", "StereoCamera", "aspect", "size", "Vector2", "setEyeSeparation", "eyeSep", "setSize", "width", "height", "render", "scene", "camera", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "update", "getSize", "autoClear", "clear", "setScissorTest", "set<PERSON><PERSON>sor", "setViewport", "cameraL", "cameraR"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/effects/StereoEffect.js"], "sourcesContent": ["import { StereoCamera, Vector2 } from 'three'\n\nclass StereoEffect {\n  constructor(renderer) {\n    const _stereo = new StereoCamera()\n    _stereo.aspect = 0.5\n    const size = new Vector2()\n\n    this.setEyeSeparation = function (eyeSep) {\n      _stereo.eyeSep = eyeSep\n    }\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.getSize(size)\n\n      if (renderer.autoClear) renderer.clear()\n      renderer.setScissorTest(true)\n\n      renderer.setScissor(0, 0, size.width / 2, size.height)\n      renderer.setViewport(0, 0, size.width / 2, size.height)\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setScissor(size.width / 2, 0, size.width / 2, size.height)\n      renderer.setViewport(size.width / 2, 0, size.width / 2, size.height)\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setScissorTest(false)\n    }\n  }\n}\n\nexport { StereoEffect }\n"], "mappings": ";AAEA,MAAMA,YAAA,CAAa;EACjBC,YAAYC,QAAA,EAAU;IACpB,MAAMC,OAAA,GAAU,IAAIC,YAAA,CAAc;IAClCD,OAAA,CAAQE,MAAA,GAAS;IACjB,MAAMC,IAAA,GAAO,IAAIC,OAAA,CAAS;IAE1B,KAAKC,gBAAA,GAAmB,UAAUC,MAAA,EAAQ;MACxCN,OAAA,CAAQM,MAAA,GAASA,MAAA;IAClB;IAED,KAAKC,OAAA,GAAU,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACtCV,QAAA,CAASQ,OAAA,CAAQC,KAAA,EAAOC,MAAM;IAC/B;IAED,KAAKC,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAID,KAAA,CAAME,qBAAA,KAA0B,MAAMF,KAAA,CAAMG,iBAAA,CAAmB;MAEnE,IAAIF,MAAA,CAAOG,MAAA,KAAW,QAAQH,MAAA,CAAOC,qBAAA,KAA0B,MAAMD,MAAA,CAAOE,iBAAA,CAAmB;MAE/Fd,OAAA,CAAQgB,MAAA,CAAOJ,MAAM;MAErBb,QAAA,CAASkB,OAAA,CAAQd,IAAI;MAErB,IAAIJ,QAAA,CAASmB,SAAA,EAAWnB,QAAA,CAASoB,KAAA,CAAO;MACxCpB,QAAA,CAASqB,cAAA,CAAe,IAAI;MAE5BrB,QAAA,CAASsB,UAAA,CAAW,GAAG,GAAGlB,IAAA,CAAKK,KAAA,GAAQ,GAAGL,IAAA,CAAKM,MAAM;MACrDV,QAAA,CAASuB,WAAA,CAAY,GAAG,GAAGnB,IAAA,CAAKK,KAAA,GAAQ,GAAGL,IAAA,CAAKM,MAAM;MACtDV,QAAA,CAASW,MAAA,CAAOC,KAAA,EAAOX,OAAA,CAAQuB,OAAO;MAEtCxB,QAAA,CAASsB,UAAA,CAAWlB,IAAA,CAAKK,KAAA,GAAQ,GAAG,GAAGL,IAAA,CAAKK,KAAA,GAAQ,GAAGL,IAAA,CAAKM,MAAM;MAClEV,QAAA,CAASuB,WAAA,CAAYnB,IAAA,CAAKK,KAAA,GAAQ,GAAG,GAAGL,IAAA,CAAKK,KAAA,GAAQ,GAAGL,IAAA,CAAKM,MAAM;MACnEV,QAAA,CAASW,MAAA,CAAOC,KAAA,EAAOX,OAAA,CAAQwB,OAAO;MAEtCzB,QAAA,CAASqB,cAAA,CAAe,KAAK;IAC9B;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}