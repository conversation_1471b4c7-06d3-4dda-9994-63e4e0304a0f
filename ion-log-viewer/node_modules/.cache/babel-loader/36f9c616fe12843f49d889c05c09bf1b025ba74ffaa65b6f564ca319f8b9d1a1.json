{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { asAscii, ESCAPED_NEWLINE, isNumericTerminator, is_base64_char, is_digit, is_hex_digit, is_keyword, is_letter, is_letter_or_digit, is_operator_char, is_whitespace, WHITESPACE_COMMENT1, WHITESPACE_COMMENT2 } from \"./IonText\";\nimport { SymbolToken } from \"./IonSymbolToken\";\nimport { IonTypes } from \"./IonTypes\";\nconst EOF = -1;\nconst ERROR = -2;\nconst T_NULL = 1;\nconst T_BOOL = 2;\nconst T_INT = 3;\nconst T_HEXINT = 4;\nconst T_FLOAT = 5;\nconst T_FLOAT_SPECIAL = 6;\nconst T_DECIMAL = 7;\nconst T_TIMESTAMP = 8;\nconst T_IDENTIFIER = 9;\nconst T_OPERATOR = 10;\nconst T_STRING1 = 11;\nconst T_STRING2 = 12;\nconst T_STRING3 = 13;\nconst T_CLOB2 = 14;\nconst T_CLOB3 = 15;\nconst T_BLOB = 16;\nconst T_SEXP = 17;\nconst T_LIST = 18;\nconst T_STRUCT = 19;\nconst CH_CR = 13;\nconst CH_NL = 10;\nconst CH_BS = 92;\nconst CH_FORWARD_SLASH = \"/\".charCodeAt(0);\nconst CH_AS = 42;\nconst CH_SQ = 39;\nconst CH_DOUBLE_QUOTE = '\"'.charCodeAt(0);\nconst CH_CM = 44;\nconst CH_OP = 40;\nconst CH_CP = 41;\nconst CH_LEFT_CURLY = \"{\".charCodeAt(0);\nconst CH_CC = 125;\nconst CH_OS = 91;\nconst CH_CS = 93;\nconst CH_CL = 58;\nconst CH_DT = 46;\nconst CH_EQ = 61;\nconst CH_PS = 43;\nconst CH_MS = 45;\nconst CH_0 = 48;\nconst CH_D = 68;\nconst CH_E = 69;\nconst CH_F = 70;\nconst CH_T = 84;\nconst CH_X = 88;\nconst CH_Z = 90;\nconst CH_d = 100;\nconst CH_e = 101;\nconst CH_f = 102;\nconst CH_i = 105;\nconst CH_n = 110;\nconst CH_x = 120;\nconst ESC_0 = 48;\nconst ESC_a = 97;\nconst ESC_b = 98;\nconst ESC_t = 116;\nconst ESC_nl = 110;\nconst ESC_ff = 102;\nconst ESC_cr = 114;\nconst ESC_v = 118;\nconst ESC_dq = CH_DOUBLE_QUOTE;\nconst ESC_sq = CH_SQ;\nconst ESC_qm = 63;\nconst ESC_bs = 92;\nconst ESC_fs = 47;\nconst ESC_nl2 = 10;\nconst ESC_nl3 = 13;\nconst ESC_x = CH_x;\nconst ESC_u = 117;\nconst ESC_U = 85;\nconst INF = [CH_i, CH_n, CH_f];\nconst _UTF16_MASK = 0x03ff;\nexport function get_ion_type(t) {\n  switch (t) {\n    case EOF:\n      return null;\n    case ERROR:\n      return null;\n    case T_NULL:\n      return IonTypes.NULL;\n    case T_BOOL:\n      return IonTypes.BOOL;\n    case T_INT:\n      return IonTypes.INT;\n    case T_HEXINT:\n      return IonTypes.INT;\n    case T_FLOAT:\n      return IonTypes.FLOAT;\n    case T_FLOAT_SPECIAL:\n      return IonTypes.FLOAT;\n    case T_DECIMAL:\n      return IonTypes.DECIMAL;\n    case T_TIMESTAMP:\n      return IonTypes.TIMESTAMP;\n    case T_IDENTIFIER:\n      return IonTypes.SYMBOL;\n    case T_OPERATOR:\n      return IonTypes.SYMBOL;\n    case T_STRING1:\n      return IonTypes.SYMBOL;\n    case T_STRING2:\n      return IonTypes.STRING;\n    case T_STRING3:\n      return IonTypes.STRING;\n    case T_CLOB2:\n      return IonTypes.CLOB;\n    case T_CLOB3:\n      return IonTypes.CLOB;\n    case T_BLOB:\n      return IonTypes.BLOB;\n    case T_SEXP:\n      return IonTypes.SEXP;\n    case T_LIST:\n      return IonTypes.LIST;\n    case T_STRUCT:\n      return IonTypes.STRUCT;\n    default:\n      throw new Error(\"Unknown type: \" + String(t) + \".\");\n  }\n}\nfunction get_keyword_type(str) {\n  if (str === \"null\") {\n    return T_NULL;\n  }\n  if (str === \"true\") {\n    return T_BOOL;\n  }\n  if (str === \"false\") {\n    return T_BOOL;\n  }\n  if (str === \"nan\") {\n    return T_FLOAT_SPECIAL;\n  }\n  if (str === \"+inf\") {\n    return T_FLOAT_SPECIAL;\n  }\n  if (str === \"-inf\") {\n    return T_FLOAT_SPECIAL;\n  }\n  throw new Error(\"Unknown keyword: \" + str + \".\");\n}\nfunction get_type_from_name(str) {\n  if (str === \"null\") {\n    return T_NULL;\n  }\n  if (str === \"bool\") {\n    return T_BOOL;\n  }\n  if (str === \"int\") {\n    return T_INT;\n  }\n  if (str === \"float\") {\n    return T_FLOAT;\n  }\n  if (str === \"decimal\") {\n    return T_DECIMAL;\n  }\n  if (str === \"timestamp\") {\n    return T_TIMESTAMP;\n  }\n  if (str === \"symbol\") {\n    return T_IDENTIFIER;\n  }\n  if (str === \"string\") {\n    return T_STRING2;\n  }\n  if (str === \"clob\") {\n    return T_CLOB2;\n  }\n  if (str === \"blob\") {\n    return T_BLOB;\n  }\n  if (str === \"sexp\") {\n    return T_SEXP;\n  }\n  if (str === \"list\") {\n    return T_LIST;\n  }\n  if (str === \"struct\") {\n    return T_STRUCT;\n  }\n  throw new Error(\"Unknown type: \" + str + \".\");\n}\nfunction get_hex_value(ch) {\n  switch (ch) {\n    case 48:\n      return 0;\n    case 49:\n      return 1;\n    case 50:\n      return 2;\n    case 51:\n      return 3;\n    case 52:\n      return 4;\n    case 53:\n      return 5;\n    case 54:\n      return 6;\n    case 55:\n      return 7;\n    case 56:\n      return 8;\n    case 57:\n      return 9;\n    case 97:\n      return 10;\n    case 98:\n      return 11;\n    case 99:\n      return 12;\n    case 100:\n      return 13;\n    case 101:\n      return 14;\n    case 102:\n      return 15;\n    case 65:\n      return 10;\n    case 66:\n      return 11;\n    case 67:\n      return 12;\n    case 68:\n      return 13;\n    case 69:\n      return 14;\n    case 70:\n      return 15;\n  }\n  throw new Error(\"Unexpected bad hex digit in checked data.\");\n}\nfunction is_valid_base64_length(char_length, trailer_length) {\n  if (trailer_length > 2) {\n    return false;\n  }\n  if ((char_length + trailer_length & 0x3) != 0) {\n    return false;\n  }\n  return true;\n}\nfunction is_valid_string_char(ch, allow_new_line) {\n  if (ch == CH_CR) {\n    return allow_new_line;\n  }\n  if (ch == CH_NL) {\n    return allow_new_line;\n  }\n  if (is_whitespace(ch)) {\n    return true;\n  }\n  if (ch < 32) {\n    return false;\n  }\n  return true;\n}\nexport class ParserTextRaw {\n  constructor(source) {\n    this._value_null = false;\n    this._curr_null = false;\n    this._read_value_helper_minus = function (ch1, accept_operator_symbols, calling_op) {\n      let op,\n        ch2 = this._peek();\n      if (ch2 == CH_i) {\n        ch2 = this._peek(\"inf\");\n        if (isNumericTerminator(ch2)) {\n          op = this._read_minus_inf;\n        } else if (accept_operator_symbols) {\n          op = this._read_operator_symbol;\n        }\n      } else if (is_digit(ch2)) {\n        op = this._read_number;\n      } else if (accept_operator_symbols) {\n        op = this._read_operator_symbol;\n      }\n      if (op != undefined) {\n        this._ops.unshift(op);\n        this._unread(ch1);\n      } else {\n        this._error(\"operator symbols are not valid outside of sexps\");\n      }\n    };\n    this._read_string_helper = function (terminator, allow_new_line) {\n      let ch;\n      this._start = this._in.position();\n      for (;;) {\n        ch = this._read();\n        if (ch == CH_BS) {\n          this._read_string_escape_sequence();\n        } else if (ch == terminator) {\n          break;\n        } else if (!is_valid_string_char(ch, allow_new_line)) {\n          throw new Error(\"invalid character \" + ch + \" in string\");\n        }\n      }\n    };\n    this._in = source;\n    this._ops = [this._read_datagram_values];\n    this._value_type = ERROR;\n    this._value = [];\n    this._start = -1;\n    this._end = -1;\n    this._esc_len = -1;\n    this._curr = EOF;\n    this._ann = [];\n    this._msg = \"\";\n    this._fieldname = null;\n    this._fieldnameType = null;\n    const helpers = {\n      40: this._read_value_helper_paren,\n      91: this._read_value_helper_square,\n      123: this._read_value_helper_curly,\n      43: this._read_value_helper_plus,\n      45: this._read_value_helper_minus,\n      39: this._read_value_helper_single,\n      34: this._read_value_helper_double\n    };\n    const set_helper = function (str, fn) {\n      let i = str.length,\n        ch;\n      while (i > 0) {\n        i--;\n        ch = str.charCodeAt(i);\n        helpers[ch] = fn;\n      }\n    };\n    set_helper(\"0123456789\", this._read_value_helper_digit);\n    set_helper(\"_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\", this._read_value_helper_letter);\n    set_helper(\"!#%&*+-./;<=>?@^`|~\", this._read_value_helper_operator);\n    helpers[CH_PS] = this._read_value_helper_plus;\n    helpers[CH_MS] = this._read_value_helper_minus;\n    this._read_value_helper_helpers = helpers;\n  }\n  fieldName() {\n    return this._fieldname;\n  }\n  fieldNameType() {\n    return this._fieldnameType;\n  }\n  source() {\n    return this._in;\n  }\n  annotations() {\n    return this._ann;\n  }\n  clearFieldName() {\n    this._fieldname = null;\n    this._fieldnameType = null;\n  }\n  isNull() {\n    return this._curr_null;\n  }\n  bigIntValue() {\n    if (this.isNull()) {\n      return null;\n    }\n    const intText = this.get_value_as_string(this._curr).toLowerCase();\n    switch (this._curr) {\n      case T_INT:\n      case T_HEXINT:\n        if (intText.startsWith(\"-\")) {\n          const i = BigInt(intText.slice(1));\n          return -i;\n        }\n        return BigInt(intText);\n      default:\n        throw new Error(\"intValue() was called when the current value was not an integer.\");\n    }\n  }\n  numberValue() {\n    if (this.isNull()) {\n      return null;\n    }\n    const s = this.get_value_as_string(this._curr);\n    switch (this._curr) {\n      case T_INT:\n      case T_HEXINT:\n        return Number(BigInt(s));\n      case T_FLOAT:\n        return Number(s);\n      case T_FLOAT_SPECIAL:\n        if (s == \"+inf\") {\n          return Number.POSITIVE_INFINITY;\n        } else if (s == \"-inf\") {\n          return Number.NEGATIVE_INFINITY;\n        } else if (s == \"nan\") {\n          return Number.NaN;\n        }\n      default:\n        throw new Error(\"can't convert to number\");\n    }\n  }\n  booleanValue() {\n    if (this.isNull()) {\n      return null;\n    }\n    const s = this.get_value_as_string(T_BOOL);\n    if (s === \"true\") {\n      return true;\n    } else if (s === \"false\") {\n      return false;\n    }\n    throw new Error(\"Unrecognized Boolean value '\" + s + \"'\");\n  }\n  get_value_as_string(t) {\n    let index;\n    let ch;\n    let s = \"\";\n    switch (t) {\n      case T_NULL:\n      case T_BOOL:\n      case T_INT:\n      case T_HEXINT:\n      case T_FLOAT:\n      case T_FLOAT_SPECIAL:\n      case T_DECIMAL:\n      case T_TIMESTAMP:\n      case T_IDENTIFIER:\n      case T_OPERATOR:\n        for (index = this._start; index < this._end; index++) {\n          s += String.fromCharCode(this._in.valueAt(index));\n        }\n        break;\n      case T_BLOB:\n        for (index = this._start; index < this._end; index++) {\n          ch = this._in.valueAt(index);\n          if (is_base64_char(ch)) {\n            s += String.fromCharCode(ch);\n          }\n        }\n        break;\n      case T_STRING1:\n      case T_STRING2:\n      case T_STRING3:\n        for (index = this._start; index < this._end; index++) {\n          let isEscaped = false;\n          ch = this._in.valueAt(index);\n          if (ch == CH_BS) {\n            ch = this._read_escape_sequence(index, this._end);\n            index += this._esc_len;\n            isEscaped = true;\n          }\n          if (this.isHighSurrogate(ch)) {\n            index++;\n            let tempChar = this._in.valueAt(index);\n            if (tempChar == CH_BS) {\n              tempChar = this._read_escape_sequence(index, this._end);\n              index += this._esc_len;\n            }\n            if (this.isLowSurrogate(tempChar)) {\n              const hiSurrogate = ch;\n              const loSurrogate = tempChar;\n              const codepoint = 0x10000 + ((hiSurrogate & _UTF16_MASK) << 10) + (loSurrogate & _UTF16_MASK);\n              s += String.fromCodePoint(codepoint);\n            } else {\n              throw new Error(\"expected a low surrogate, but found: \" + ch);\n            }\n          } else if (this.isLowSurrogate(ch)) {\n            throw new Error(\"unexpected low surrogate: \" + ch);\n          } else if (t === T_STRING3 && ch === CH_SQ && !isEscaped && this.verifyTriple(index)) {\n            index = this._skip_triple_quote_gap(index, this._end, true);\n          } else if (ch >= 0) {\n            if (isEscaped) {\n              s += String.fromCodePoint(ch);\n            } else {\n              if (t === T_STRING3 && ch === ESC_nl3 && this._in.valueAt(index + 1) === ESC_nl2) {\n                ch = ESC_nl2;\n                index++;\n              }\n              s += String.fromCharCode(ch);\n            }\n          }\n        }\n        break;\n      default:\n        throw new Error(\"can't get this value as a string\");\n    }\n    return s;\n  }\n  get_value_as_uint8array(t) {\n    const bytes = [];\n    switch (t) {\n      case T_CLOB2:\n        for (let index = this._start; index < this._end; index++) {\n          const ch = this._in.valueAt(index);\n          if (ch === CH_BS) {\n            bytes.push(this.readClobEscapes(index, this._end));\n            index += this._esc_len;\n          } else if (ch < 128) {\n            bytes.push(ch);\n          } else {\n            throw new Error(\"Non-Ascii values illegal within clob.\");\n          }\n        }\n        break;\n      case T_CLOB3:\n        for (let index = this._start; index < this._end; index++) {\n          const ch = this._in.valueAt(index);\n          if (ch === CH_BS) {\n            const escaped = this.readClobEscapes(index, this._end);\n            if (escaped >= 0) {\n              bytes.push(escaped);\n            }\n            index += this._esc_len;\n          } else if (ch === CH_SQ) {\n            if (this.verifyTriple(index)) {\n              index = this._skip_triple_quote_gap(index, this._end, false);\n            } else {\n              bytes.push(ch);\n            }\n          } else if (ch < 128) {\n            bytes.push(ch);\n          } else {\n            throw new Error(\"Non-Ascii values illegal within clob.\");\n          }\n        }\n        break;\n      default:\n        throw new Error(\"can't get this value as a Uint8Array\");\n    }\n    return Uint8Array.from(bytes);\n  }\n  next() {\n    this.clearFieldName();\n    this._ann = [];\n    if (this._value_type === ERROR) {\n      this._run();\n    }\n    this._curr = this._value_pop();\n    let t;\n    if (this._curr === ERROR) {\n      this._value.push(ERROR);\n      return undefined;\n    } else {\n      t = this._curr;\n    }\n    this._curr_null = this._value_null;\n    this._value_null = false;\n    return t;\n  }\n  _read_datagram_values() {\n    const ch = this._peek();\n    if (ch == EOF) {\n      this._value_push(EOF);\n    } else {\n      this._ops.unshift(this._read_datagram_values);\n      this._ops.unshift(this._read_value);\n    }\n  }\n  _read_sexp_values() {\n    const ch = this._read_after_whitespace(true);\n    if (ch == CH_CP) {\n      this._value_push(EOF);\n    } else if (ch === EOF) {\n      throw new Error(\"Expected closing ).\");\n    } else {\n      this._unread(ch);\n      this._ops.unshift(this._read_sexp_values);\n      this._ops.unshift(this._read_sexp_value);\n    }\n  }\n  _read_list_values() {\n    const ch = this._read_after_whitespace(true);\n    if (ch == CH_CS) {\n      this._value_push(EOF);\n    } else {\n      this._unread(ch);\n      this._ops.unshift(this._read_list_comma);\n      this._ops.unshift(this._read_value);\n    }\n  }\n  _read_struct_values() {\n    let op = this._done_with_error,\n      ch = this._read_after_whitespace(true);\n    switch (ch) {\n      case CH_SQ:\n        op = this._read_string1;\n        if (this._peek(\"''\") != ERROR) {\n          op = this._read_string3;\n        }\n        break;\n      case CH_DOUBLE_QUOTE:\n        op = this._read_string2;\n        break;\n      case CH_CC:\n        this._value_push(EOF);\n        return;\n      default:\n        if (is_letter(ch)) {\n          op = this._read_symbol;\n        }\n        break;\n    }\n    if (op === this._done_with_error) {\n      this._error(\"expected field name (or close struct '}') not found\");\n    } else {\n      op.call(this);\n      this._load_field_name();\n      ch = this._read_after_whitespace(true);\n      if (ch != CH_CL) {\n        this._error(\"expected ':'\");\n        return;\n      }\n      this._ops.unshift(this._read_struct_comma);\n      this._ops.unshift(this._read_value);\n    }\n  }\n  _read_list_comma() {\n    let ch = this._read_after_whitespace(true);\n    if (ch == CH_CM) {\n      ch = this._read_after_whitespace(true);\n      if (ch == CH_CS) {\n        this._value_push(EOF);\n      } else {\n        this._unread(ch);\n        this._ops.unshift(this._read_list_comma);\n        this._ops.unshift(this._read_value);\n      }\n    } else if (ch == CH_CS) {\n      this._value_push(EOF);\n    } else {\n      this._error(\"expected ',' or ']'\");\n    }\n  }\n  _read_struct_comma() {\n    let ch = this._read_after_whitespace(true);\n    if (ch == CH_CM) {\n      ch = this._read_after_whitespace(true);\n      if (ch == CH_CC) {\n        this._value_push(EOF);\n      } else {\n        this._unread(ch);\n        this._ops.unshift(this._read_struct_values);\n      }\n    } else if (ch == CH_CC) {\n      this._value_push(EOF);\n    } else {\n      this._error(\"expected ',' or '}'\");\n    }\n  }\n  _load_field_name() {\n    this._fieldnameType = this._value_pop();\n    const s = this.get_value_as_string(this._fieldnameType);\n    switch (this._fieldnameType) {\n      case T_IDENTIFIER:\n        if (is_keyword(s)) {\n          throw new Error(\"can't use '\" + s + \"' as a fieldname without quotes\");\n        }\n      case T_STRING1:\n      case T_STRING2:\n      case T_STRING3:\n        this._fieldname = s;\n        break;\n      default:\n        throw new Error(\"invalid fieldname\" + s);\n    }\n  }\n  _read_value() {\n    this._read_value_helper(false, this._read_value);\n  }\n  _read_sexp_value() {\n    this._read_value_helper(true, this._read_sexp_value);\n  }\n  _read_value_helper(accept_operator_symbols, calling_op) {\n    const ch = this._read_after_whitespace(true);\n    if (ch == EOF) {\n      this._read_value_helper_EOF(ch, accept_operator_symbols, calling_op);\n    } else {\n      const fn = this._read_value_helper_helpers[ch];\n      if (fn != undefined) {\n        fn.call(this, ch, accept_operator_symbols, calling_op);\n      } else {\n        this._error(\"unexpected character '\" + asAscii(ch) + \"'\");\n      }\n    }\n  }\n  _read_value_helper_EOF(ch1, accept_operator_symbols, calling_op) {\n    this._ops.unshift(this._done);\n  }\n  _read_value_helper_paren(ch1, accept_operator_symbols, calling_op) {\n    this._value_push(T_SEXP);\n    this._ops.unshift(this._read_sexp_values);\n  }\n  _read_value_helper_square(ch1, accept_operator_symbols, calling_op) {\n    this._value_push(T_LIST);\n    this._ops.unshift(this._read_list_values);\n  }\n  _read_value_helper_curly(ch1, accept_operator_symbols, calling_op) {\n    let ch3;\n    const ch2 = this._read();\n    if (ch2 == CH_LEFT_CURLY) {\n      ch3 = this._read_after_whitespace(false);\n      if (ch3 == CH_SQ) {\n        this._ops.unshift(this._read_clob_string3);\n      } else if (ch3 == CH_DOUBLE_QUOTE) {\n        this._ops.unshift(this._read_clob_string2);\n      } else {\n        this._unread(ch3);\n        this._ops.unshift(this._read_blob);\n      }\n    } else {\n      this._unread(ch2);\n      this._value_push(T_STRUCT);\n      this._ops.unshift(this._read_struct_values);\n    }\n  }\n  _read_value_helper_plus(ch1, accept_operator_symbols, calling_op) {\n    const ch2 = this._peek(\"inf\");\n    this._unread(ch1);\n    if (isNumericTerminator(ch2)) {\n      this._ops.unshift(this._read_plus_inf);\n    } else if (accept_operator_symbols) {\n      this._ops.unshift(this._read_operator_symbol);\n    } else {\n      this._error(\"unexpected '+'\");\n    }\n  }\n  _read_value_helper_digit(ch1, accept_operator_symbols, calling_op) {\n    const ch2 = this._peek_4_digits(ch1);\n    this._unread(ch1);\n    if (ch2 == CH_T || ch2 == CH_MS) {\n      this._ops.unshift(this._readTimestamp);\n    } else {\n      this._ops.unshift(this._read_number);\n    }\n  }\n  _read_value_helper_single(ch1, accept_operator_symbols, calling_op) {\n    let op;\n    if (this._peek(\"''\") != ERROR) {\n      op = this._read_string3;\n      op.call(this);\n    } else {\n      op = this._read_string1;\n      op.call(this);\n      if (this._test_string_as_annotation(op)) {\n        this._ops.unshift(calling_op);\n      }\n    }\n  }\n  _read_value_helper_double(ch1, accept_operator_symbols, calling_op) {\n    this._ops.unshift(this._read_string2);\n  }\n  _read_value_helper_letter(ch1, accept_operator_symbols, calling_op) {\n    this._read_symbol();\n    const type = this._value_pop();\n    if (type != T_IDENTIFIER) {\n      throw new Error(\"Expecting symbol here.\");\n    }\n    let symbol = this.get_value_as_string(type);\n    if (is_keyword(symbol)) {\n      let kwt = get_keyword_type(symbol);\n      if (kwt === T_NULL) {\n        this._value_null = true;\n        if (this._peek() === CH_DT) {\n          this._read();\n          const ch = this._read();\n          if (is_letter(ch) !== true) {\n            throw new Error(\"Expected type name after 'null.'\");\n          }\n          this._read_symbol();\n          if (this._value_pop() !== T_IDENTIFIER) {\n            throw new Error(\"Expected type name after 'null.'\");\n          }\n          symbol = this.get_value_as_string(T_IDENTIFIER);\n          kwt = get_type_from_name(symbol);\n        }\n        this._start = -1;\n        this._end = -1;\n      }\n      this._value_push(kwt);\n    } else {\n      const ch = this._read_after_whitespace(true);\n      if (ch == CH_CL && this._peek() == CH_CL) {\n        this._read();\n        const sid = this._parseSymbolId(symbol);\n        if (sid === 0) {\n          throw new Error(\"Symbol ID zero is not supported.\");\n        } else if (isNaN(sid)) {\n          this._ann.push(new SymbolToken(symbol));\n        } else {\n          this._ann.push(new SymbolToken(null, sid));\n        }\n        this._ops.unshift(calling_op);\n      } else {\n        const kwt = T_IDENTIFIER;\n        this._unread(ch);\n        this._value_push(kwt);\n      }\n    }\n  }\n  _read_value_helper_operator(ch1, accept_operator_symbols, calling_op) {\n    if (accept_operator_symbols) {\n      this._unread(ch1);\n      this._ops.unshift(this._read_operator_symbol);\n    } else {\n      this._error(\"unexpected operator character\");\n    }\n  }\n  _done() {\n    this._value_push(EOF);\n  }\n  _done_with_error() {\n    this._value_push(ERROR);\n    throw new Error(this._error_msg);\n  }\n  _read_number() {\n    let ch, t;\n    this._start = this._in.position();\n    ch = this._read();\n    if (ch == CH_MS) {\n      ch = this._read();\n    }\n    if (ch == CH_0) {\n      ch = this._peek();\n      if (ch == CH_x || ch == CH_X) {\n        this._read_hex_int();\n        return;\n      }\n      if (is_digit(ch)) {\n        this._error(\"leading zeros are not allowed\");\n      }\n      ch = CH_0;\n    }\n    t = T_INT;\n    ch = this._read_required_digits(ch);\n    if (ch == CH_DT) {\n      t = T_DECIMAL;\n      ch = this._read_optional_digits(this._read());\n    }\n    if (!isNumericTerminator(ch)) {\n      if (ch == CH_d || ch == CH_D) {\n        t = T_DECIMAL;\n        ch = this._read_exponent();\n      } else if (ch == CH_e || ch == CH_E || ch == CH_f || ch == CH_F) {\n        t = T_FLOAT;\n        ch = this._read_exponent();\n      }\n    }\n    if (!isNumericTerminator(ch)) {\n      this._error(\"invalid character after number\");\n    } else {\n      this._unread(ch);\n      this._end = this._in.position();\n      this._value_push(t);\n    }\n  }\n  _read_hex_int() {\n    let ch = this._read();\n    if (ch == CH_x || ch == CH_X) {\n      ch = this._read();\n      ch = this._read_required_hex_digits(ch);\n    }\n    if (isNumericTerminator(ch)) {\n      this._unread(ch);\n      this._end = this._in.position();\n      this._value_push(T_HEXINT);\n    } else {\n      this._error(\"invalid character after number\");\n    }\n  }\n  _read_exponent() {\n    let ch = this._read();\n    if (ch == CH_MS || ch == CH_PS) {\n      ch = this._read();\n    }\n    ch = this._read_required_digits(ch);\n    return ch;\n  }\n  _read_plus_inf() {\n    this._start = this._in.position();\n    if (this._read() == CH_PS) {\n      this._read_inf_helper();\n    } else {\n      this._error(\"expected +inf\");\n    }\n  }\n  _read_minus_inf() {\n    this._start = this._in.position();\n    if (this._read() == CH_MS) {\n      this._read_inf_helper();\n    } else {\n      this._error(\"expected -inf\");\n    }\n  }\n  _read_inf_helper() {\n    let ii, ch;\n    for (ii = 0; ii < 3; ii++) {\n      ch = this._read();\n      if (ch != INF[ii]) {\n        this._error(\"expected 'inf'\");\n        return;\n      }\n    }\n    if (isNumericTerminator(this._peek())) {\n      this._end = this._in.position();\n      this._value_push(T_FLOAT_SPECIAL);\n    } else {\n      this._error(\"invalid numeric terminator after 'inf'\");\n    }\n  }\n  _readTimestamp() {\n    this._start = this._in.position();\n    let ch = this._readPastNDigits(4);\n    if (ch === CH_T) {\n      this._end = this._in.position();\n      this._value_push(T_TIMESTAMP);\n      return;\n    } else if (ch !== CH_MS) {\n      throw new Error(\"Timestamp year must be followed by '-' or 'T'.\");\n    }\n    ch = this._readPastNDigits(2);\n    if (ch === CH_T) {\n      this._end = this._in.position();\n      this._value_push(T_TIMESTAMP);\n      return;\n    } else if (ch !== CH_MS) {\n      throw new Error(\"Timestamp month must be followed by '-' or 'T'.\");\n    }\n    ch = this._readPastNDigits(2);\n    if (isNumericTerminator(ch)) {\n      this._unread(ch);\n      this._end = this._in.position();\n      this._value_push(T_TIMESTAMP);\n      return;\n    } else if (ch !== CH_T) {\n      throw new Error(\"Timestamp day must be followed by a numeric stop character .\");\n    }\n    const peekChar = this._in.peek();\n    if (isNumericTerminator(peekChar)) {\n      this._end = this._in.position();\n      this._value_push(T_TIMESTAMP);\n      return;\n    } else if (!is_digit(peekChar)) {\n      throw new Error(\"Timestamp DATE must be followed by numeric terminator or additional TIME digits.\");\n    }\n    ch = this._readPastNDigits(2);\n    if (ch !== CH_CL) {\n      throw new Error(\"Timestamp time(hr:min) requires format of 00:00\");\n    }\n    ch = this._readPastNDigits(2);\n    if (ch === CH_CL) {\n      ch = this._readPastNDigits(2);\n      if (ch === CH_DT) {\n        if (!is_digit(this._read())) {\n          throw new Error(\"W3C timestamp spec requires atleast one digit after decimal point.\");\n        }\n        while (is_digit(ch = this._read())) {}\n      }\n    }\n    if (ch === CH_Z) {\n      if (!isNumericTerminator(this._peek())) {\n        throw new Error(\"Illegal terminator after Zulu offset.\");\n      }\n      this._end = this._in.position();\n      this._value_push(T_TIMESTAMP);\n      return;\n    } else if (ch !== CH_PS && ch !== CH_MS) {\n      throw new Error(\"Timestamps require an offset.\");\n    }\n    ch = this._readPastNDigits(2);\n    if (ch !== CH_CL) {\n      throw new Error(\"Timestamp offset(hr:min) requires format of +/-00:00.\");\n    }\n    this._readNDigits(2);\n    ch = this._peek();\n    if (!isNumericTerminator(ch)) {\n      throw new Error(\"Improperly formatted timestamp.\");\n    }\n    this._end = this._in.position();\n    this._value_push(T_TIMESTAMP);\n  }\n  _read_symbol() {\n    let ch;\n    this._start = this._in.position() - 1;\n    for (;;) {\n      ch = this._read();\n      if (!is_letter_or_digit(ch)) {\n        break;\n      }\n    }\n    this._unread(ch);\n    this._end = this._in.position();\n    this._value_push(T_IDENTIFIER);\n  }\n  _read_operator_symbol() {\n    let ch;\n    this._start = this._in.position();\n    for (;;) {\n      ch = this._read();\n      if (!is_operator_char(ch)) {\n        break;\n      }\n    }\n    this._end = this._in.position() - 1;\n    this._unread(ch);\n    this._value_push(T_OPERATOR);\n  }\n  _read_string1() {\n    this._read_string_helper(CH_SQ, false);\n    this._end = this._in.position() - 1;\n    this._value_push(T_STRING1);\n  }\n  _read_string2() {\n    this._read_string_helper(CH_DOUBLE_QUOTE, false);\n    this._end = this._in.position() - 1;\n    this._value_push(T_STRING2);\n  }\n  _read_string3(recognizeComments) {\n    if (recognizeComments === undefined) {\n      recognizeComments = true;\n    }\n    let ch;\n    this._unread(this._peek(\"\"));\n    for (this._start = this._in.position() + 3; this._peek(\"'''\") !== ERROR; this._in.unread(this._read_after_whitespace(recognizeComments))) {\n      for (let i = 0; i < 3; i++) {\n        this._read();\n      }\n      while (this._peek(\"'''\") === ERROR) {\n        ch = this._read();\n        if (ch == CH_BS) {\n          this._read_string_escape_sequence();\n        }\n        if (ch === EOF) {\n          throw new Error(\"Closing triple quotes not found.\");\n        }\n        if (!is_valid_string_char(ch, true)) {\n          throw new Error(\"invalid character \" + ch + \" in string\");\n        }\n      }\n      this._end = this._in.position();\n      for (let i = 0; i < 3; i++) {\n        this._read();\n      }\n    }\n    this._value_push(T_STRING3);\n  }\n  verifyTriple(entryIndex) {\n    return this._in.valueAt(entryIndex) === CH_SQ && this._in.valueAt(entryIndex + 1) === CH_SQ && this._in.valueAt(entryIndex + 2) === CH_SQ;\n  }\n  _read_string_escape_sequence() {\n    let ch = this._read();\n    switch (ch) {\n      case ESC_0:\n      case ESC_a:\n      case ESC_b:\n      case ESC_t:\n      case ESC_nl:\n      case ESC_ff:\n      case ESC_cr:\n      case ESC_v:\n      case ESC_dq:\n      case ESC_sq:\n      case ESC_qm:\n      case ESC_bs:\n      case ESC_fs:\n      case ESC_nl2:\n        break;\n      case ESC_nl3:\n        ch = this._read();\n        if (ch != ESC_nl2) {\n          this._unread(ch);\n        }\n        break;\n      case ESC_x:\n        ch = this._read_N_hexdigits(2);\n        this._unread(ch);\n        break;\n      case ESC_u:\n        ch = this._read_N_hexdigits(4);\n        this._unread(ch);\n        break;\n      case ESC_U:\n        ch = this._read_N_hexdigits(8);\n        this._unread(ch);\n        break;\n      default:\n        this._error(\"unexpected character: \" + ch + \" after escape slash\");\n    }\n  }\n  _test_string_as_annotation(op) {\n    let s, ch, is_ann;\n    const t = this._value_pop();\n    if (t != T_STRING1 && t != T_STRING3) {\n      this._error(\"expecting quoted symbol here\");\n    }\n    s = this.get_value_as_string(t);\n    ch = this._read_after_whitespace(true);\n    if (ch == CH_CL && this._peek() == CH_CL) {\n      this._read();\n      this._ann.push(new SymbolToken(s));\n      is_ann = true;\n    } else {\n      this._unread(ch);\n      this._value_push(t);\n      is_ann = false;\n    }\n    return is_ann;\n  }\n  _read_clob_string2() {\n    let t;\n    this._read_string2();\n    t = this._value_pop();\n    if (t != T_STRING2) {\n      this._error(\"string expected\");\n    }\n    this._value_push(T_CLOB2);\n    this._ops.unshift(this._read_close_double_brace);\n  }\n  _read_clob_string3() {\n    let t;\n    this._read_string3(false);\n    t = this._value_pop();\n    if (t != T_STRING3) {\n      this._error(\"string expected\");\n    }\n    this._value_push(T_CLOB3);\n    this._ops.unshift(this._read_close_double_brace);\n  }\n  _read_blob() {\n    let ch,\n      base64_chars = 0,\n      trailers = 0;\n    this._start = this._in.position();\n    while (true) {\n      ch = this._read();\n      if (is_base64_char(ch)) {\n        base64_chars++;\n        this._end = this._in.position();\n      } else if (!is_whitespace(ch)) {\n        break;\n      }\n    }\n    while (ch == CH_EQ) {\n      trailers++;\n      ch = this._read_after_whitespace(false);\n    }\n    if (ch != CH_CC || this._read() != CH_CC) {\n      throw new Error(\"Invalid blob\");\n    }\n    if (!is_valid_base64_length(base64_chars, trailers)) {\n      throw new Error(\"Invalid base64 value\");\n    }\n    this._value_push(T_BLOB);\n  }\n  _read_close_double_brace() {\n    const ch = this._read_after_whitespace(false);\n    if (ch != CH_CC || this._read() != CH_CC) {\n      this._error(\"expected '}}'\");\n    }\n  }\n  isHighSurrogate(ch) {\n    return ch >= 0xd800 && ch <= 0xdbff;\n  }\n  isLowSurrogate(ch) {\n    return ch >= 0xdc00 && ch <= 0xdfff;\n  }\n  indexWhiteSpace(index, acceptComments) {\n    let ch = this._in.valueAt(index);\n    if (!acceptComments) {\n      for (; is_whitespace(ch); ch = this._in.valueAt(index++)) {}\n    } else {\n      for (; is_whitespace(ch) || ch === CH_FORWARD_SLASH; ch = this._in.valueAt(index++)) {\n        if (ch === CH_FORWARD_SLASH) {\n          ch = this._in.valueAt(index++);\n          switch (ch) {\n            case CH_FORWARD_SLASH:\n              index = this.indexToNewLine(index);\n              break;\n            case CH_AS:\n              index = this.indexToCloseComment(index);\n              break;\n            default:\n              index--;\n              break;\n          }\n        }\n      }\n    }\n    return index;\n  }\n  indexToNewLine(index) {\n    let ch = this._in.valueAt(index);\n    while (ch !== EOF && ch !== CH_NL) {\n      if (ch === CH_CR) {\n        if (this._in.valueAt(index + 1) !== CH_NL) {\n          return index;\n        }\n      }\n      ch = this._in.valueAt(index++);\n    }\n    return index;\n  }\n  indexToCloseComment(index) {\n    while (this._in.valueAt(index) !== CH_AS && this._in.valueAt(index + 1) !== CH_FORWARD_SLASH) {\n      index++;\n    }\n    return index;\n  }\n  _skip_triple_quote_gap(entryIndex, end, acceptComments) {\n    let tempIndex = entryIndex + 3;\n    tempIndex = this.indexWhiteSpace(tempIndex, acceptComments);\n    if (tempIndex + 2 <= end && this.verifyTriple(tempIndex)) {\n      return tempIndex + 4;\n    } else {\n      return tempIndex + 1;\n    }\n  }\n  readClobEscapes(ii, end) {\n    let ch;\n    if (ii + 1 >= end) {\n      throw new Error(\"invalid escape sequence\");\n    }\n    ch = this._in.valueAt(ii + 1);\n    this._esc_len = 1;\n    switch (ch) {\n      case ESC_0:\n        return 0;\n      case ESC_a:\n        return 7;\n      case ESC_b:\n        return 8;\n      case ESC_t:\n        return 9;\n      case ESC_nl:\n        return 10;\n      case ESC_ff:\n        return 12;\n      case ESC_cr:\n        return 13;\n      case ESC_v:\n        return 11;\n      case ESC_dq:\n        return 34;\n      case ESC_sq:\n        return 39;\n      case ESC_qm:\n        return 63;\n      case ESC_bs:\n        return 92;\n      case ESC_fs:\n        return 47;\n      case ESC_nl2:\n        return -1;\n      case ESC_nl3:\n        if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {\n          this._esc_len = 2;\n        }\n        return ESCAPED_NEWLINE;\n      case ESC_x:\n        if (ii + 3 >= end) {\n          throw new Error(\"invalid escape sequence\");\n        }\n        ch = this._get_N_hexdigits(ii + 2, ii + 4);\n        this._esc_len = 3;\n        break;\n      default:\n        throw new Error(\"Invalid escape: /\" + ch);\n    }\n    return ch;\n  }\n  _read_escape_sequence(ii, end) {\n    let ch;\n    if (ii + 1 >= end) {\n      throw new Error(\"Invalid escape sequence.\");\n    }\n    ch = this._in.valueAt(ii + 1);\n    this._esc_len = 1;\n    switch (ch) {\n      case ESC_0:\n        return 0;\n      case ESC_a:\n        return 7;\n      case ESC_b:\n        return 8;\n      case ESC_t:\n        return 9;\n      case ESC_nl:\n        return 10;\n      case ESC_ff:\n        return 12;\n      case ESC_cr:\n        return 13;\n      case ESC_v:\n        return 11;\n      case ESC_dq:\n        return 34;\n      case ESC_sq:\n        return 39;\n      case ESC_qm:\n        return 63;\n      case ESC_bs:\n        return 92;\n      case ESC_fs:\n        return 47;\n      case ESC_nl2:\n        return -1;\n      case ESC_nl3:\n        if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {\n          this._esc_len = 2;\n        }\n        return ESCAPED_NEWLINE;\n      case ESC_x:\n        if (ii + 3 >= end) {\n          throw new Error(\"invalid escape sequence\");\n        }\n        ch = this._get_N_hexdigits(ii + 2, ii + 4);\n        this._esc_len = 3;\n        break;\n      case ESC_u:\n        if (ii + 5 >= end) {\n          throw new Error(\"invalid escape sequence\");\n        }\n        ch = this._get_N_hexdigits(ii + 2, ii + 6);\n        this._esc_len = 5;\n        break;\n      case ESC_U:\n        if (ii + 9 >= end) {\n          throw new Error(\"invalid escape sequence\");\n        }\n        ch = this._get_N_hexdigits(ii + 2, ii + 10);\n        this._esc_len = 9;\n        break;\n      default:\n        throw new Error(\"unexpected character after escape slash\");\n    }\n    return ch;\n  }\n  _get_N_hexdigits(ii, end) {\n    let ch,\n      v = 0;\n    while (ii < end) {\n      ch = this._in.valueAt(ii);\n      v = v * 16 + get_hex_value(ch);\n      ii++;\n    }\n    return v;\n  }\n  _value_push(t) {\n    if (this._value_type !== ERROR) {\n      this._error(\"unexpected double push of value type!\");\n    }\n    this._value_type = t;\n  }\n  _value_pop() {\n    const t = this._value_type;\n    this._value_type = ERROR;\n    return t;\n  }\n  _run() {\n    let op;\n    while (this._ops.length > 0 && this._value_type === ERROR) {\n      op = this._ops.shift();\n      op.call(this);\n    }\n  }\n  _read() {\n    const ch = this._in.next();\n    return ch;\n  }\n  _read_skipping_comments() {\n    let ch = this._read();\n    if (ch == CH_FORWARD_SLASH) {\n      ch = this._read();\n      if (ch == CH_FORWARD_SLASH) {\n        this._read_to_newline();\n        ch = WHITESPACE_COMMENT1;\n      } else if (ch == CH_AS) {\n        this._read_to_close_comment();\n        ch = WHITESPACE_COMMENT2;\n      } else {\n        this._unread(ch);\n        ch = CH_FORWARD_SLASH;\n      }\n    }\n    return ch;\n  }\n  _read_to_newline() {\n    let ch;\n    for (;;) {\n      ch = this._read();\n      if (ch == EOF) {\n        break;\n      }\n      if (ch == CH_NL) {\n        break;\n      }\n      if (ch == CH_CR) {\n        ch = this._read();\n        if (ch != CH_NL) {\n          this._unread(ch);\n        }\n        break;\n      }\n    }\n  }\n  _read_to_close_comment() {\n    let ch;\n    for (;;) {\n      ch = this._read();\n      if (ch == EOF) {\n        break;\n      }\n      if (ch == CH_AS) {\n        ch = this._read();\n        if (ch == CH_FORWARD_SLASH) {\n          break;\n        }\n      }\n    }\n  }\n  _unread(ch) {\n    this._in.unread(ch);\n  }\n  _read_after_whitespace(recognize_comments) {\n    let ch;\n    if (recognize_comments) {\n      ch = this._read_skipping_comments();\n      while (is_whitespace(ch)) {\n        ch = this._read_skipping_comments();\n      }\n    } else {\n      ch = this._read();\n      while (is_whitespace(ch)) {\n        ch = this._read();\n      }\n    }\n    return ch;\n  }\n  _peek(expected) {\n    let ch,\n      ii = 0;\n    if (expected === undefined || expected.length < 1) {\n      return this._in.valueAt(this._in.position());\n    }\n    while (ii < expected.length) {\n      ch = this._read();\n      if (ch != expected.charCodeAt(ii)) {\n        break;\n      }\n      ii++;\n    }\n    if (ii === expected.length) {\n      ch = this._peek();\n    } else {\n      this._unread(ch);\n      ch = ERROR;\n    }\n    while (ii > 0) {\n      ii--;\n      this._unread(expected.charCodeAt(ii));\n    }\n    return ch;\n  }\n  _peek_4_digits(ch1) {\n    let ii,\n      ch,\n      is_digits = true;\n    const chars = [];\n    if (!is_digit(ch1)) {\n      return ERROR;\n    }\n    for (ii = 0; ii < 3; ii++) {\n      ch = this._read();\n      chars.push(ch);\n      if (!is_digit(ch)) {\n        is_digits = false;\n        break;\n      }\n    }\n    ch = is_digits && ii == 3 ? this._peek() : ERROR;\n    while (chars.length > 0) {\n      this._unread(chars.pop());\n    }\n    return ch;\n  }\n  _read_required_digits(ch) {\n    if (!is_digit(ch)) {\n      return ERROR;\n    }\n    for (;;) {\n      ch = this._read();\n      if (!is_digit(ch)) {\n        break;\n      }\n    }\n    return ch;\n  }\n  _read_optional_digits(ch) {\n    while (is_digit(ch)) {\n      ch = this._read();\n    }\n    return ch;\n  }\n  _readNDigits(n) {\n    let ch;\n    if (n <= 0) {\n      throw new Error(\"Cannot read a lack of or negative number of digits.\");\n    }\n    while (n--) {\n      if (!is_digit(ch = this._read())) {\n        throw new Error(\"Expected digit, got: \" + String.fromCharCode(ch));\n      }\n    }\n    return ch;\n  }\n  _readPastNDigits(n) {\n    this._readNDigits(n);\n    return this._read();\n  }\n  _read_required_hex_digits(ch) {\n    if (!is_hex_digit(ch)) {\n      return ERROR;\n    }\n    for (;;) {\n      ch = this._read();\n      if (!is_hex_digit(ch)) {\n        break;\n      }\n    }\n    return ch;\n  }\n  _read_N_hexdigits(n) {\n    let ch,\n      ii = 0;\n    while (ii < n) {\n      ch = this._read();\n      if (!is_hex_digit(ch)) {\n        this._error(\"\" + n + \" digits required \" + ii + \" found\");\n        return ERROR;\n      }\n      ii++;\n    }\n    return ch;\n  }\n  _parseSymbolId(s) {\n    if (s[0] !== \"$\") {\n      return NaN;\n    }\n    for (let i = 1; i < s.length; i++) {\n      if (s[i] < \"0\" || s[i] > \"9\") {\n        return NaN;\n      }\n    }\n    return parseInt(s.substr(1, s.length));\n  }\n  _error(msg) {\n    this._ops.unshift(this._done_with_error);\n    this._error_msg = msg;\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "ESCAPED_NEWLINE", "isNumericTerminator", "is_base64_char", "is_digit", "is_hex_digit", "is_keyword", "is_letter", "is_letter_or_digit", "is_operator_char", "is_whitespace", "WHITESPACE_COMMENT1", "WHITESPACE_COMMENT2", "SymbolToken", "IonTypes", "EOF", "ERROR", "T_NULL", "T_BOOL", "T_INT", "T_HEXINT", "T_FLOAT", "T_FLOAT_SPECIAL", "T_DECIMAL", "T_TIMESTAMP", "T_IDENTIFIER", "T_OPERATOR", "T_STRING1", "T_STRING2", "T_STRING3", "T_CLOB2", "T_CLOB3", "T_BLOB", "T_SEXP", "T_LIST", "T_STRUCT", "CH_CR", "CH_NL", "CH_BS", "CH_FORWARD_SLASH", "charCodeAt", "CH_AS", "CH_SQ", "CH_DOUBLE_QUOTE", "CH_CM", "CH_OP", "CH_CP", "CH_LEFT_CURLY", "CH_CC", "CH_OS", "CH_CS", "CH_CL", "CH_DT", "CH_EQ", "CH_PS", "CH_MS", "CH_0", "CH_D", "CH_E", "CH_F", "CH_T", "CH_X", "CH_Z", "CH_d", "CH_e", "CH_f", "CH_i", "CH_n", "CH_x", "ESC_0", "ESC_a", "ESC_b", "ESC_t", "ESC_nl", "ESC_ff", "ESC_cr", "ESC_v", "ESC_dq", "ESC_sq", "ESC_qm", "ESC_bs", "ESC_fs", "ESC_nl2", "ESC_nl3", "ESC_x", "ESC_u", "ESC_U", "INF", "_UTF16_MASK", "get_ion_type", "t", "NULL", "BOOL", "INT", "FLOAT", "DECIMAL", "TIMESTAMP", "SYMBOL", "STRING", "CLOB", "BLOB", "SEXP", "LIST", "STRUCT", "Error", "String", "get_keyword_type", "str", "get_type_from_name", "get_hex_value", "ch", "is_valid_base64_length", "char_length", "trailer_length", "is_valid_string_char", "allow_new_line", "ParserTextRaw", "constructor", "source", "_value_null", "_curr_null", "_read_value_helper_minus", "ch1", "accept_operator_symbols", "calling_op", "op", "ch2", "_peek", "_read_minus_inf", "_read_operator_symbol", "_read_number", "undefined", "_ops", "unshift", "_unread", "_error", "_read_string_helper", "terminator", "_start", "_in", "position", "_read", "_read_string_escape_sequence", "_read_datagram_values", "_value_type", "_value", "_end", "_esc_len", "_curr", "_ann", "_msg", "_fieldname", "_fieldnameType", "helpers", "_read_value_helper_paren", "_read_value_helper_square", "_read_value_helper_curly", "_read_value_helper_plus", "_read_value_helper_single", "_read_value_helper_double", "set_helper", "fn", "i", "length", "_read_value_helper_digit", "_read_value_helper_letter", "_read_value_helper_operator", "_read_value_helper_helpers", "fieldName", "fieldNameType", "annotations", "clearFieldName", "isNull", "bigIntValue", "intText", "get_value_as_string", "toLowerCase", "startsWith", "BigInt", "slice", "numberValue", "s", "Number", "POSITIVE_INFINITY", "NEGATIVE_INFINITY", "NaN", "booleanValue", "index", "fromCharCode", "valueAt", "isEscaped", "_read_escape_sequence", "isHighSurrogate", "tempChar", "isLowSurrogate", "hiSurrogate", "loSurrogate", "codepoint", "fromCodePoint", "verifyTriple", "_skip_triple_quote_gap", "get_value_as_uint8array", "bytes", "push", "readClobEscapes", "escaped", "Uint8Array", "from", "next", "_run", "_value_pop", "_value_push", "_read_value", "_read_sexp_values", "_read_after_whitespace", "_read_sexp_value", "_read_list_values", "_read_list_comma", "_read_struct_values", "_done_with_error", "_read_string1", "_read_string3", "_read_string2", "_read_symbol", "call", "_load_field_name", "_read_struct_comma", "_read_value_helper", "_read_value_helper_EOF", "_done", "ch3", "_read_clob_string3", "_read_clob_string2", "_read_blob", "_read_plus_inf", "_peek_4_digits", "_readTimestamp", "_test_string_as_annotation", "type", "symbol", "kwt", "sid", "_parseSymbolId", "isNaN", "_error_msg", "_read_hex_int", "_read_required_digits", "_read_optional_digits", "_read_exponent", "_read_required_hex_digits", "_read_inf_helper", "ii", "_readPastNDigits", "peekChar", "peek", "_readNDigits", "recognizeComments", "unread", "entryIndex", "_read_N_hexdigits", "is_ann", "_read_close_double_brace", "base64_chars", "trailers", "indexWhiteSpace", "acceptComments", "indexToNewLine", "indexToCloseComment", "end", "tempIndex", "_get_N_hexdigits", "v", "shift", "_read_skipping_comments", "_read_to_newline", "_read_to_close_comment", "recognize_comments", "expected", "is_digits", "chars", "pop", "n", "parseInt", "substr", "msg"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonParserTextRaw.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { asAscii, ESCAPED_NEWLINE, isNumericTerminator, is_base64_char, is_digit, is_hex_digit, is_keyword, is_letter, is_letter_or_digit, is_operator_char, is_whitespace, WHITESPACE_COMMENT1, WHITESPACE_COMMENT2, } from \"./IonText\";\nimport { SymbolToken } from \"./IonSymbolToken\";\nimport { IonTypes } from \"./IonTypes\";\nconst EOF = -1;\nconst ERROR = -2;\nconst T_NULL = 1;\nconst T_BOOL = 2;\nconst T_INT = 3;\nconst T_HEXINT = 4;\nconst T_FLOAT = 5;\nconst T_FLOAT_SPECIAL = 6;\nconst T_DECIMAL = 7;\nconst T_TIMESTAMP = 8;\nconst T_IDENTIFIER = 9;\nconst T_OPERATOR = 10;\nconst T_STRING1 = 11;\nconst T_STRING2 = 12;\nconst T_STRING3 = 13;\nconst T_CLOB2 = 14;\nconst T_CLOB3 = 15;\nconst T_BLOB = 16;\nconst T_SEXP = 17;\nconst T_LIST = 18;\nconst T_STRUCT = 19;\nconst CH_CR = 13;\nconst CH_NL = 10;\nconst CH_BS = 92;\nconst CH_FORWARD_SLASH = \"/\".charCodeAt(0);\nconst CH_AS = 42;\nconst CH_SQ = 39;\nconst CH_DOUBLE_QUOTE = '\"'.charCodeAt(0);\nconst CH_CM = 44;\nconst CH_OP = 40;\nconst CH_CP = 41;\nconst CH_LEFT_CURLY = \"{\".charCodeAt(0);\nconst CH_CC = 125;\nconst CH_OS = 91;\nconst CH_CS = 93;\nconst CH_CL = 58;\nconst CH_DT = 46;\nconst CH_EQ = 61;\nconst CH_PS = 43;\nconst CH_MS = 45;\nconst CH_0 = 48;\nconst CH_D = 68;\nconst CH_E = 69;\nconst CH_F = 70;\nconst CH_T = 84;\nconst CH_X = 88;\nconst CH_Z = 90;\nconst CH_d = 100;\nconst CH_e = 101;\nconst CH_f = 102;\nconst CH_i = 105;\nconst CH_n = 110;\nconst CH_x = 120;\nconst ESC_0 = 48;\nconst ESC_a = 97;\nconst ESC_b = 98;\nconst ESC_t = 116;\nconst ESC_nl = 110;\nconst ESC_ff = 102;\nconst ESC_cr = 114;\nconst ESC_v = 118;\nconst ESC_dq = CH_DOUBLE_QUOTE;\nconst ESC_sq = CH_SQ;\nconst ESC_qm = 63;\nconst ESC_bs = 92;\nconst ESC_fs = 47;\nconst ESC_nl2 = 10;\nconst ESC_nl3 = 13;\nconst ESC_x = CH_x;\nconst ESC_u = 117;\nconst ESC_U = 85;\nconst INF = [CH_i, CH_n, CH_f];\nconst _UTF16_MASK = 0x03ff;\nexport function get_ion_type(t) {\n    switch (t) {\n        case EOF:\n            return null;\n        case ERROR:\n            return null;\n        case T_NULL:\n            return IonTypes.NULL;\n        case T_BOOL:\n            return IonTypes.BOOL;\n        case T_INT:\n            return IonTypes.INT;\n        case T_HEXINT:\n            return IonTypes.INT;\n        case T_FLOAT:\n            return IonTypes.FLOAT;\n        case T_FLOAT_SPECIAL:\n            return IonTypes.FLOAT;\n        case T_DECIMAL:\n            return IonTypes.DECIMAL;\n        case T_TIMESTAMP:\n            return IonTypes.TIMESTAMP;\n        case T_IDENTIFIER:\n            return IonTypes.SYMBOL;\n        case T_OPERATOR:\n            return IonTypes.SYMBOL;\n        case T_STRING1:\n            return IonTypes.SYMBOL;\n        case T_STRING2:\n            return IonTypes.STRING;\n        case T_STRING3:\n            return IonTypes.STRING;\n        case T_CLOB2:\n            return IonTypes.CLOB;\n        case T_CLOB3:\n            return IonTypes.CLOB;\n        case T_BLOB:\n            return IonTypes.BLOB;\n        case T_SEXP:\n            return IonTypes.SEXP;\n        case T_LIST:\n            return IonTypes.LIST;\n        case T_STRUCT:\n            return IonTypes.STRUCT;\n        default:\n            throw new Error(\"Unknown type: \" + String(t) + \".\");\n    }\n}\nfunction get_keyword_type(str) {\n    if (str === \"null\") {\n        return T_NULL;\n    }\n    if (str === \"true\") {\n        return T_BOOL;\n    }\n    if (str === \"false\") {\n        return T_BOOL;\n    }\n    if (str === \"nan\") {\n        return T_FLOAT_SPECIAL;\n    }\n    if (str === \"+inf\") {\n        return T_FLOAT_SPECIAL;\n    }\n    if (str === \"-inf\") {\n        return T_FLOAT_SPECIAL;\n    }\n    throw new Error(\"Unknown keyword: \" + str + \".\");\n}\nfunction get_type_from_name(str) {\n    if (str === \"null\") {\n        return T_NULL;\n    }\n    if (str === \"bool\") {\n        return T_BOOL;\n    }\n    if (str === \"int\") {\n        return T_INT;\n    }\n    if (str === \"float\") {\n        return T_FLOAT;\n    }\n    if (str === \"decimal\") {\n        return T_DECIMAL;\n    }\n    if (str === \"timestamp\") {\n        return T_TIMESTAMP;\n    }\n    if (str === \"symbol\") {\n        return T_IDENTIFIER;\n    }\n    if (str === \"string\") {\n        return T_STRING2;\n    }\n    if (str === \"clob\") {\n        return T_CLOB2;\n    }\n    if (str === \"blob\") {\n        return T_BLOB;\n    }\n    if (str === \"sexp\") {\n        return T_SEXP;\n    }\n    if (str === \"list\") {\n        return T_LIST;\n    }\n    if (str === \"struct\") {\n        return T_STRUCT;\n    }\n    throw new Error(\"Unknown type: \" + str + \".\");\n}\nfunction get_hex_value(ch) {\n    switch (ch) {\n        case 48:\n            return 0;\n        case 49:\n            return 1;\n        case 50:\n            return 2;\n        case 51:\n            return 3;\n        case 52:\n            return 4;\n        case 53:\n            return 5;\n        case 54:\n            return 6;\n        case 55:\n            return 7;\n        case 56:\n            return 8;\n        case 57:\n            return 9;\n        case 97:\n            return 10;\n        case 98:\n            return 11;\n        case 99:\n            return 12;\n        case 100:\n            return 13;\n        case 101:\n            return 14;\n        case 102:\n            return 15;\n        case 65:\n            return 10;\n        case 66:\n            return 11;\n        case 67:\n            return 12;\n        case 68:\n            return 13;\n        case 69:\n            return 14;\n        case 70:\n            return 15;\n    }\n    throw new Error(\"Unexpected bad hex digit in checked data.\");\n}\nfunction is_valid_base64_length(char_length, trailer_length) {\n    if (trailer_length > 2) {\n        return false;\n    }\n    if (((char_length + trailer_length) & 0x3) != 0) {\n        return false;\n    }\n    return true;\n}\nfunction is_valid_string_char(ch, allow_new_line) {\n    if (ch == CH_CR) {\n        return allow_new_line;\n    }\n    if (ch == CH_NL) {\n        return allow_new_line;\n    }\n    if (is_whitespace(ch)) {\n        return true;\n    }\n    if (ch < 32) {\n        return false;\n    }\n    return true;\n}\nexport class ParserTextRaw {\n    constructor(source) {\n        this._value_null = false;\n        this._curr_null = false;\n        this._read_value_helper_minus = function (ch1, accept_operator_symbols, calling_op) {\n            let op, ch2 = this._peek();\n            if (ch2 == CH_i) {\n                ch2 = this._peek(\"inf\");\n                if (isNumericTerminator(ch2)) {\n                    op = this._read_minus_inf;\n                }\n                else if (accept_operator_symbols) {\n                    op = this._read_operator_symbol;\n                }\n            }\n            else if (is_digit(ch2)) {\n                op = this._read_number;\n            }\n            else if (accept_operator_symbols) {\n                op = this._read_operator_symbol;\n            }\n            if (op != undefined) {\n                this._ops.unshift(op);\n                this._unread(ch1);\n            }\n            else {\n                this._error(\"operator symbols are not valid outside of sexps\");\n            }\n        };\n        this._read_string_helper = function (terminator, allow_new_line) {\n            let ch;\n            this._start = this._in.position();\n            for (;;) {\n                ch = this._read();\n                if (ch == CH_BS) {\n                    this._read_string_escape_sequence();\n                }\n                else if (ch == terminator) {\n                    break;\n                }\n                else if (!is_valid_string_char(ch, allow_new_line)) {\n                    throw new Error(\"invalid character \" + ch + \" in string\");\n                }\n            }\n        };\n        this._in = source;\n        this._ops = [this._read_datagram_values];\n        this._value_type = ERROR;\n        this._value = [];\n        this._start = -1;\n        this._end = -1;\n        this._esc_len = -1;\n        this._curr = EOF;\n        this._ann = [];\n        this._msg = \"\";\n        this._fieldname = null;\n        this._fieldnameType = null;\n        const helpers = {\n            40: this._read_value_helper_paren,\n            91: this._read_value_helper_square,\n            123: this._read_value_helper_curly,\n            43: this._read_value_helper_plus,\n            45: this._read_value_helper_minus,\n            39: this._read_value_helper_single,\n            34: this._read_value_helper_double,\n        };\n        const set_helper = function (str, fn) {\n            let i = str.length, ch;\n            while (i > 0) {\n                i--;\n                ch = str.charCodeAt(i);\n                helpers[ch] = fn;\n            }\n        };\n        set_helper(\"0123456789\", this._read_value_helper_digit);\n        set_helper(\"_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\", this._read_value_helper_letter);\n        set_helper(\"!#%&*+-./;<=>?@^`|~\", this._read_value_helper_operator);\n        helpers[CH_PS] = this._read_value_helper_plus;\n        helpers[CH_MS] = this._read_value_helper_minus;\n        this._read_value_helper_helpers = helpers;\n    }\n    fieldName() {\n        return this._fieldname;\n    }\n    fieldNameType() {\n        return this._fieldnameType;\n    }\n    source() {\n        return this._in;\n    }\n    annotations() {\n        return this._ann;\n    }\n    clearFieldName() {\n        this._fieldname = null;\n        this._fieldnameType = null;\n    }\n    isNull() {\n        return this._curr_null;\n    }\n    bigIntValue() {\n        if (this.isNull()) {\n            return null;\n        }\n        const intText = this.get_value_as_string(this._curr).toLowerCase();\n        switch (this._curr) {\n            case T_INT:\n            case T_HEXINT:\n                if (intText.startsWith(\"-\")) {\n                    const i = BigInt(intText.slice(1));\n                    return -i;\n                }\n                return BigInt(intText);\n            default:\n                throw new Error(\"intValue() was called when the current value was not an integer.\");\n        }\n    }\n    numberValue() {\n        if (this.isNull()) {\n            return null;\n        }\n        const s = this.get_value_as_string(this._curr);\n        switch (this._curr) {\n            case T_INT:\n            case T_HEXINT:\n                return Number(BigInt(s));\n            case T_FLOAT:\n                return Number(s);\n            case T_FLOAT_SPECIAL:\n                if (s == \"+inf\") {\n                    return Number.POSITIVE_INFINITY;\n                }\n                else if (s == \"-inf\") {\n                    return Number.NEGATIVE_INFINITY;\n                }\n                else if (s == \"nan\") {\n                    return Number.NaN;\n                }\n            default:\n                throw new Error(\"can't convert to number\");\n        }\n    }\n    booleanValue() {\n        if (this.isNull()) {\n            return null;\n        }\n        const s = this.get_value_as_string(T_BOOL);\n        if (s === \"true\") {\n            return true;\n        }\n        else if (s === \"false\") {\n            return false;\n        }\n        throw new Error(\"Unrecognized Boolean value '\" + s + \"'\");\n    }\n    get_value_as_string(t) {\n        let index;\n        let ch;\n        let s = \"\";\n        switch (t) {\n            case T_NULL:\n            case T_BOOL:\n            case T_INT:\n            case T_HEXINT:\n            case T_FLOAT:\n            case T_FLOAT_SPECIAL:\n            case T_DECIMAL:\n            case T_TIMESTAMP:\n            case T_IDENTIFIER:\n            case T_OPERATOR:\n                for (index = this._start; index < this._end; index++) {\n                    s += String.fromCharCode(this._in.valueAt(index));\n                }\n                break;\n            case T_BLOB:\n                for (index = this._start; index < this._end; index++) {\n                    ch = this._in.valueAt(index);\n                    if (is_base64_char(ch)) {\n                        s += String.fromCharCode(ch);\n                    }\n                }\n                break;\n            case T_STRING1:\n            case T_STRING2:\n            case T_STRING3:\n                for (index = this._start; index < this._end; index++) {\n                    let isEscaped = false;\n                    ch = this._in.valueAt(index);\n                    if (ch == CH_BS) {\n                        ch = this._read_escape_sequence(index, this._end);\n                        index += this._esc_len;\n                        isEscaped = true;\n                    }\n                    if (this.isHighSurrogate(ch)) {\n                        index++;\n                        let tempChar = this._in.valueAt(index);\n                        if (tempChar == CH_BS) {\n                            tempChar = this._read_escape_sequence(index, this._end);\n                            index += this._esc_len;\n                        }\n                        if (this.isLowSurrogate(tempChar)) {\n                            const hiSurrogate = ch;\n                            const loSurrogate = tempChar;\n                            const codepoint = 0x10000 +\n                                ((hiSurrogate & _UTF16_MASK) << 10) +\n                                (loSurrogate & _UTF16_MASK);\n                            s += String.fromCodePoint(codepoint);\n                        }\n                        else {\n                            throw new Error(\"expected a low surrogate, but found: \" + ch);\n                        }\n                    }\n                    else if (this.isLowSurrogate(ch)) {\n                        throw new Error(\"unexpected low surrogate: \" + ch);\n                    }\n                    else if (t === T_STRING3 &&\n                        ch === CH_SQ &&\n                        !isEscaped &&\n                        this.verifyTriple(index)) {\n                        index = this._skip_triple_quote_gap(index, this._end, true);\n                    }\n                    else if (ch >= 0) {\n                        if (isEscaped) {\n                            s += String.fromCodePoint(ch);\n                        }\n                        else {\n                            if (t === T_STRING3 &&\n                                ch === ESC_nl3 &&\n                                this._in.valueAt(index + 1) === ESC_nl2) {\n                                ch = ESC_nl2;\n                                index++;\n                            }\n                            s += String.fromCharCode(ch);\n                        }\n                    }\n                }\n                break;\n            default:\n                throw new Error(\"can't get this value as a string\");\n        }\n        return s;\n    }\n    get_value_as_uint8array(t) {\n        const bytes = [];\n        switch (t) {\n            case T_CLOB2:\n                for (let index = this._start; index < this._end; index++) {\n                    const ch = this._in.valueAt(index);\n                    if (ch === CH_BS) {\n                        bytes.push(this.readClobEscapes(index, this._end));\n                        index += this._esc_len;\n                    }\n                    else if (ch < 128) {\n                        bytes.push(ch);\n                    }\n                    else {\n                        throw new Error(\"Non-Ascii values illegal within clob.\");\n                    }\n                }\n                break;\n            case T_CLOB3:\n                for (let index = this._start; index < this._end; index++) {\n                    const ch = this._in.valueAt(index);\n                    if (ch === CH_BS) {\n                        const escaped = this.readClobEscapes(index, this._end);\n                        if (escaped >= 0) {\n                            bytes.push(escaped);\n                        }\n                        index += this._esc_len;\n                    }\n                    else if (ch === CH_SQ) {\n                        if (this.verifyTriple(index)) {\n                            index = this._skip_triple_quote_gap(index, this._end, false);\n                        }\n                        else {\n                            bytes.push(ch);\n                        }\n                    }\n                    else if (ch < 128) {\n                        bytes.push(ch);\n                    }\n                    else {\n                        throw new Error(\"Non-Ascii values illegal within clob.\");\n                    }\n                }\n                break;\n            default:\n                throw new Error(\"can't get this value as a Uint8Array\");\n        }\n        return Uint8Array.from(bytes);\n    }\n    next() {\n        this.clearFieldName();\n        this._ann = [];\n        if (this._value_type === ERROR) {\n            this._run();\n        }\n        this._curr = this._value_pop();\n        let t;\n        if (this._curr === ERROR) {\n            this._value.push(ERROR);\n            return undefined;\n        }\n        else {\n            t = this._curr;\n        }\n        this._curr_null = this._value_null;\n        this._value_null = false;\n        return t;\n    }\n    _read_datagram_values() {\n        const ch = this._peek();\n        if (ch == EOF) {\n            this._value_push(EOF);\n        }\n        else {\n            this._ops.unshift(this._read_datagram_values);\n            this._ops.unshift(this._read_value);\n        }\n    }\n    _read_sexp_values() {\n        const ch = this._read_after_whitespace(true);\n        if (ch == CH_CP) {\n            this._value_push(EOF);\n        }\n        else if (ch === EOF) {\n            throw new Error(\"Expected closing ).\");\n        }\n        else {\n            this._unread(ch);\n            this._ops.unshift(this._read_sexp_values);\n            this._ops.unshift(this._read_sexp_value);\n        }\n    }\n    _read_list_values() {\n        const ch = this._read_after_whitespace(true);\n        if (ch == CH_CS) {\n            this._value_push(EOF);\n        }\n        else {\n            this._unread(ch);\n            this._ops.unshift(this._read_list_comma);\n            this._ops.unshift(this._read_value);\n        }\n    }\n    _read_struct_values() {\n        let op = this._done_with_error, ch = this._read_after_whitespace(true);\n        switch (ch) {\n            case CH_SQ:\n                op = this._read_string1;\n                if (this._peek(\"''\") != ERROR) {\n                    op = this._read_string3;\n                }\n                break;\n            case CH_DOUBLE_QUOTE:\n                op = this._read_string2;\n                break;\n            case CH_CC:\n                this._value_push(EOF);\n                return;\n            default:\n                if (is_letter(ch)) {\n                    op = this._read_symbol;\n                }\n                break;\n        }\n        if (op === this._done_with_error) {\n            this._error(\"expected field name (or close struct '}') not found\");\n        }\n        else {\n            op.call(this);\n            this._load_field_name();\n            ch = this._read_after_whitespace(true);\n            if (ch != CH_CL) {\n                this._error(\"expected ':'\");\n                return;\n            }\n            this._ops.unshift(this._read_struct_comma);\n            this._ops.unshift(this._read_value);\n        }\n    }\n    _read_list_comma() {\n        let ch = this._read_after_whitespace(true);\n        if (ch == CH_CM) {\n            ch = this._read_after_whitespace(true);\n            if (ch == CH_CS) {\n                this._value_push(EOF);\n            }\n            else {\n                this._unread(ch);\n                this._ops.unshift(this._read_list_comma);\n                this._ops.unshift(this._read_value);\n            }\n        }\n        else if (ch == CH_CS) {\n            this._value_push(EOF);\n        }\n        else {\n            this._error(\"expected ',' or ']'\");\n        }\n    }\n    _read_struct_comma() {\n        let ch = this._read_after_whitespace(true);\n        if (ch == CH_CM) {\n            ch = this._read_after_whitespace(true);\n            if (ch == CH_CC) {\n                this._value_push(EOF);\n            }\n            else {\n                this._unread(ch);\n                this._ops.unshift(this._read_struct_values);\n            }\n        }\n        else if (ch == CH_CC) {\n            this._value_push(EOF);\n        }\n        else {\n            this._error(\"expected ',' or '}'\");\n        }\n    }\n    _load_field_name() {\n        this._fieldnameType = this._value_pop();\n        const s = this.get_value_as_string(this._fieldnameType);\n        switch (this._fieldnameType) {\n            case T_IDENTIFIER:\n                if (is_keyword(s)) {\n                    throw new Error(\"can't use '\" + s + \"' as a fieldname without quotes\");\n                }\n            case T_STRING1:\n            case T_STRING2:\n            case T_STRING3:\n                this._fieldname = s;\n                break;\n            default:\n                throw new Error(\"invalid fieldname\" + s);\n        }\n    }\n    _read_value() {\n        this._read_value_helper(false, this._read_value);\n    }\n    _read_sexp_value() {\n        this._read_value_helper(true, this._read_sexp_value);\n    }\n    _read_value_helper(accept_operator_symbols, calling_op) {\n        const ch = this._read_after_whitespace(true);\n        if (ch == EOF) {\n            this._read_value_helper_EOF(ch, accept_operator_symbols, calling_op);\n        }\n        else {\n            const fn = this._read_value_helper_helpers[ch];\n            if (fn != undefined) {\n                fn.call(this, ch, accept_operator_symbols, calling_op);\n            }\n            else {\n                this._error(\"unexpected character '\" + asAscii(ch) + \"'\");\n            }\n        }\n    }\n    _read_value_helper_EOF(ch1, accept_operator_symbols, calling_op) {\n        this._ops.unshift(this._done);\n    }\n    _read_value_helper_paren(ch1, accept_operator_symbols, calling_op) {\n        this._value_push(T_SEXP);\n        this._ops.unshift(this._read_sexp_values);\n    }\n    _read_value_helper_square(ch1, accept_operator_symbols, calling_op) {\n        this._value_push(T_LIST);\n        this._ops.unshift(this._read_list_values);\n    }\n    _read_value_helper_curly(ch1, accept_operator_symbols, calling_op) {\n        let ch3;\n        const ch2 = this._read();\n        if (ch2 == CH_LEFT_CURLY) {\n            ch3 = this._read_after_whitespace(false);\n            if (ch3 == CH_SQ) {\n                this._ops.unshift(this._read_clob_string3);\n            }\n            else if (ch3 == CH_DOUBLE_QUOTE) {\n                this._ops.unshift(this._read_clob_string2);\n            }\n            else {\n                this._unread(ch3);\n                this._ops.unshift(this._read_blob);\n            }\n        }\n        else {\n            this._unread(ch2);\n            this._value_push(T_STRUCT);\n            this._ops.unshift(this._read_struct_values);\n        }\n    }\n    _read_value_helper_plus(ch1, accept_operator_symbols, calling_op) {\n        const ch2 = this._peek(\"inf\");\n        this._unread(ch1);\n        if (isNumericTerminator(ch2)) {\n            this._ops.unshift(this._read_plus_inf);\n        }\n        else if (accept_operator_symbols) {\n            this._ops.unshift(this._read_operator_symbol);\n        }\n        else {\n            this._error(\"unexpected '+'\");\n        }\n    }\n    _read_value_helper_digit(ch1, accept_operator_symbols, calling_op) {\n        const ch2 = this._peek_4_digits(ch1);\n        this._unread(ch1);\n        if (ch2 == CH_T || ch2 == CH_MS) {\n            this._ops.unshift(this._readTimestamp);\n        }\n        else {\n            this._ops.unshift(this._read_number);\n        }\n    }\n    _read_value_helper_single(ch1, accept_operator_symbols, calling_op) {\n        let op;\n        if (this._peek(\"''\") != ERROR) {\n            op = this._read_string3;\n            op.call(this);\n        }\n        else {\n            op = this._read_string1;\n            op.call(this);\n            if (this._test_string_as_annotation(op)) {\n                this._ops.unshift(calling_op);\n            }\n        }\n    }\n    _read_value_helper_double(ch1, accept_operator_symbols, calling_op) {\n        this._ops.unshift(this._read_string2);\n    }\n    _read_value_helper_letter(ch1, accept_operator_symbols, calling_op) {\n        this._read_symbol();\n        const type = this._value_pop();\n        if (type != T_IDENTIFIER) {\n            throw new Error(\"Expecting symbol here.\");\n        }\n        let symbol = this.get_value_as_string(type);\n        if (is_keyword(symbol)) {\n            let kwt = get_keyword_type(symbol);\n            if (kwt === T_NULL) {\n                this._value_null = true;\n                if (this._peek() === CH_DT) {\n                    this._read();\n                    const ch = this._read();\n                    if (is_letter(ch) !== true) {\n                        throw new Error(\"Expected type name after 'null.'\");\n                    }\n                    this._read_symbol();\n                    if (this._value_pop() !== T_IDENTIFIER) {\n                        throw new Error(\"Expected type name after 'null.'\");\n                    }\n                    symbol = this.get_value_as_string(T_IDENTIFIER);\n                    kwt = get_type_from_name(symbol);\n                }\n                this._start = -1;\n                this._end = -1;\n            }\n            this._value_push(kwt);\n        }\n        else {\n            const ch = this._read_after_whitespace(true);\n            if (ch == CH_CL && this._peek() == CH_CL) {\n                this._read();\n                const sid = this._parseSymbolId(symbol);\n                if (sid === 0) {\n                    throw new Error(\"Symbol ID zero is not supported.\");\n                }\n                else if (isNaN(sid)) {\n                    this._ann.push(new SymbolToken(symbol));\n                }\n                else {\n                    this._ann.push(new SymbolToken(null, sid));\n                }\n                this._ops.unshift(calling_op);\n            }\n            else {\n                const kwt = T_IDENTIFIER;\n                this._unread(ch);\n                this._value_push(kwt);\n            }\n        }\n    }\n    _read_value_helper_operator(ch1, accept_operator_symbols, calling_op) {\n        if (accept_operator_symbols) {\n            this._unread(ch1);\n            this._ops.unshift(this._read_operator_symbol);\n        }\n        else {\n            this._error(\"unexpected operator character\");\n        }\n    }\n    _done() {\n        this._value_push(EOF);\n    }\n    _done_with_error() {\n        this._value_push(ERROR);\n        throw new Error(this._error_msg);\n    }\n    _read_number() {\n        let ch, t;\n        this._start = this._in.position();\n        ch = this._read();\n        if (ch == CH_MS) {\n            ch = this._read();\n        }\n        if (ch == CH_0) {\n            ch = this._peek();\n            if (ch == CH_x || ch == CH_X) {\n                this._read_hex_int();\n                return;\n            }\n            if (is_digit(ch)) {\n                this._error(\"leading zeros are not allowed\");\n            }\n            ch = CH_0;\n        }\n        t = T_INT;\n        ch = this._read_required_digits(ch);\n        if (ch == CH_DT) {\n            t = T_DECIMAL;\n            ch = this._read_optional_digits(this._read());\n        }\n        if (!isNumericTerminator(ch)) {\n            if (ch == CH_d || ch == CH_D) {\n                t = T_DECIMAL;\n                ch = this._read_exponent();\n            }\n            else if (ch == CH_e || ch == CH_E || ch == CH_f || ch == CH_F) {\n                t = T_FLOAT;\n                ch = this._read_exponent();\n            }\n        }\n        if (!isNumericTerminator(ch)) {\n            this._error(\"invalid character after number\");\n        }\n        else {\n            this._unread(ch);\n            this._end = this._in.position();\n            this._value_push(t);\n        }\n    }\n    _read_hex_int() {\n        let ch = this._read();\n        if (ch == CH_x || ch == CH_X) {\n            ch = this._read();\n            ch = this._read_required_hex_digits(ch);\n        }\n        if (isNumericTerminator(ch)) {\n            this._unread(ch);\n            this._end = this._in.position();\n            this._value_push(T_HEXINT);\n        }\n        else {\n            this._error(\"invalid character after number\");\n        }\n    }\n    _read_exponent() {\n        let ch = this._read();\n        if (ch == CH_MS || ch == CH_PS) {\n            ch = this._read();\n        }\n        ch = this._read_required_digits(ch);\n        return ch;\n    }\n    _read_plus_inf() {\n        this._start = this._in.position();\n        if (this._read() == CH_PS) {\n            this._read_inf_helper();\n        }\n        else {\n            this._error(\"expected +inf\");\n        }\n    }\n    _read_minus_inf() {\n        this._start = this._in.position();\n        if (this._read() == CH_MS) {\n            this._read_inf_helper();\n        }\n        else {\n            this._error(\"expected -inf\");\n        }\n    }\n    _read_inf_helper() {\n        let ii, ch;\n        for (ii = 0; ii < 3; ii++) {\n            ch = this._read();\n            if (ch != INF[ii]) {\n                this._error(\"expected 'inf'\");\n                return;\n            }\n        }\n        if (isNumericTerminator(this._peek())) {\n            this._end = this._in.position();\n            this._value_push(T_FLOAT_SPECIAL);\n        }\n        else {\n            this._error(\"invalid numeric terminator after 'inf'\");\n        }\n    }\n    _readTimestamp() {\n        this._start = this._in.position();\n        let ch = this._readPastNDigits(4);\n        if (ch === CH_T) {\n            this._end = this._in.position();\n            this._value_push(T_TIMESTAMP);\n            return;\n        }\n        else if (ch !== CH_MS) {\n            throw new Error(\"Timestamp year must be followed by '-' or 'T'.\");\n        }\n        ch = this._readPastNDigits(2);\n        if (ch === CH_T) {\n            this._end = this._in.position();\n            this._value_push(T_TIMESTAMP);\n            return;\n        }\n        else if (ch !== CH_MS) {\n            throw new Error(\"Timestamp month must be followed by '-' or 'T'.\");\n        }\n        ch = this._readPastNDigits(2);\n        if (isNumericTerminator(ch)) {\n            this._unread(ch);\n            this._end = this._in.position();\n            this._value_push(T_TIMESTAMP);\n            return;\n        }\n        else if (ch !== CH_T) {\n            throw new Error(\"Timestamp day must be followed by a numeric stop character .\");\n        }\n        const peekChar = this._in.peek();\n        if (isNumericTerminator(peekChar)) {\n            this._end = this._in.position();\n            this._value_push(T_TIMESTAMP);\n            return;\n        }\n        else if (!is_digit(peekChar)) {\n            throw new Error(\"Timestamp DATE must be followed by numeric terminator or additional TIME digits.\");\n        }\n        ch = this._readPastNDigits(2);\n        if (ch !== CH_CL) {\n            throw new Error(\"Timestamp time(hr:min) requires format of 00:00\");\n        }\n        ch = this._readPastNDigits(2);\n        if (ch === CH_CL) {\n            ch = this._readPastNDigits(2);\n            if (ch === CH_DT) {\n                if (!is_digit(this._read())) {\n                    throw new Error(\"W3C timestamp spec requires atleast one digit after decimal point.\");\n                }\n                while (is_digit((ch = this._read()))) { }\n            }\n        }\n        if (ch === CH_Z) {\n            if (!isNumericTerminator(this._peek())) {\n                throw new Error(\"Illegal terminator after Zulu offset.\");\n            }\n            this._end = this._in.position();\n            this._value_push(T_TIMESTAMP);\n            return;\n        }\n        else if (ch !== CH_PS && ch !== CH_MS) {\n            throw new Error(\"Timestamps require an offset.\");\n        }\n        ch = this._readPastNDigits(2);\n        if (ch !== CH_CL) {\n            throw new Error(\"Timestamp offset(hr:min) requires format of +/-00:00.\");\n        }\n        this._readNDigits(2);\n        ch = this._peek();\n        if (!isNumericTerminator(ch)) {\n            throw new Error(\"Improperly formatted timestamp.\");\n        }\n        this._end = this._in.position();\n        this._value_push(T_TIMESTAMP);\n    }\n    _read_symbol() {\n        let ch;\n        this._start = this._in.position() - 1;\n        for (;;) {\n            ch = this._read();\n            if (!is_letter_or_digit(ch)) {\n                break;\n            }\n        }\n        this._unread(ch);\n        this._end = this._in.position();\n        this._value_push(T_IDENTIFIER);\n    }\n    _read_operator_symbol() {\n        let ch;\n        this._start = this._in.position();\n        for (;;) {\n            ch = this._read();\n            if (!is_operator_char(ch)) {\n                break;\n            }\n        }\n        this._end = this._in.position() - 1;\n        this._unread(ch);\n        this._value_push(T_OPERATOR);\n    }\n    _read_string1() {\n        this._read_string_helper(CH_SQ, false);\n        this._end = this._in.position() - 1;\n        this._value_push(T_STRING1);\n    }\n    _read_string2() {\n        this._read_string_helper(CH_DOUBLE_QUOTE, false);\n        this._end = this._in.position() - 1;\n        this._value_push(T_STRING2);\n    }\n    _read_string3(recognizeComments) {\n        if (recognizeComments === undefined) {\n            recognizeComments = true;\n        }\n        let ch;\n        this._unread(this._peek(\"\"));\n        for (this._start = this._in.position() + 3; this._peek(\"'''\") !== ERROR; this._in.unread(this._read_after_whitespace(recognizeComments))) {\n            for (let i = 0; i < 3; i++) {\n                this._read();\n            }\n            while (this._peek(\"'''\") === ERROR) {\n                ch = this._read();\n                if (ch == CH_BS) {\n                    this._read_string_escape_sequence();\n                }\n                if (ch === EOF) {\n                    throw new Error(\"Closing triple quotes not found.\");\n                }\n                if (!is_valid_string_char(ch, true)) {\n                    throw new Error(\"invalid character \" + ch + \" in string\");\n                }\n            }\n            this._end = this._in.position();\n            for (let i = 0; i < 3; i++) {\n                this._read();\n            }\n        }\n        this._value_push(T_STRING3);\n    }\n    verifyTriple(entryIndex) {\n        return (this._in.valueAt(entryIndex) === CH_SQ &&\n            this._in.valueAt(entryIndex + 1) === CH_SQ &&\n            this._in.valueAt(entryIndex + 2) === CH_SQ);\n    }\n    _read_string_escape_sequence() {\n        let ch = this._read();\n        switch (ch) {\n            case ESC_0:\n            case ESC_a:\n            case ESC_b:\n            case ESC_t:\n            case ESC_nl:\n            case ESC_ff:\n            case ESC_cr:\n            case ESC_v:\n            case ESC_dq:\n            case ESC_sq:\n            case ESC_qm:\n            case ESC_bs:\n            case ESC_fs:\n            case ESC_nl2:\n                break;\n            case ESC_nl3:\n                ch = this._read();\n                if (ch != ESC_nl2) {\n                    this._unread(ch);\n                }\n                break;\n            case ESC_x:\n                ch = this._read_N_hexdigits(2);\n                this._unread(ch);\n                break;\n            case ESC_u:\n                ch = this._read_N_hexdigits(4);\n                this._unread(ch);\n                break;\n            case ESC_U:\n                ch = this._read_N_hexdigits(8);\n                this._unread(ch);\n                break;\n            default:\n                this._error(\"unexpected character: \" + ch + \" after escape slash\");\n        }\n    }\n    _test_string_as_annotation(op) {\n        let s, ch, is_ann;\n        const t = this._value_pop();\n        if (t != T_STRING1 && t != T_STRING3) {\n            this._error(\"expecting quoted symbol here\");\n        }\n        s = this.get_value_as_string(t);\n        ch = this._read_after_whitespace(true);\n        if (ch == CH_CL && this._peek() == CH_CL) {\n            this._read();\n            this._ann.push(new SymbolToken(s));\n            is_ann = true;\n        }\n        else {\n            this._unread(ch);\n            this._value_push(t);\n            is_ann = false;\n        }\n        return is_ann;\n    }\n    _read_clob_string2() {\n        let t;\n        this._read_string2();\n        t = this._value_pop();\n        if (t != T_STRING2) {\n            this._error(\"string expected\");\n        }\n        this._value_push(T_CLOB2);\n        this._ops.unshift(this._read_close_double_brace);\n    }\n    _read_clob_string3() {\n        let t;\n        this._read_string3(false);\n        t = this._value_pop();\n        if (t != T_STRING3) {\n            this._error(\"string expected\");\n        }\n        this._value_push(T_CLOB3);\n        this._ops.unshift(this._read_close_double_brace);\n    }\n    _read_blob() {\n        let ch, base64_chars = 0, trailers = 0;\n        this._start = this._in.position();\n        while (true) {\n            ch = this._read();\n            if (is_base64_char(ch)) {\n                base64_chars++;\n                this._end = this._in.position();\n            }\n            else if (!is_whitespace(ch)) {\n                break;\n            }\n        }\n        while (ch == CH_EQ) {\n            trailers++;\n            ch = this._read_after_whitespace(false);\n        }\n        if (ch != CH_CC || this._read() != CH_CC) {\n            throw new Error(\"Invalid blob\");\n        }\n        if (!is_valid_base64_length(base64_chars, trailers)) {\n            throw new Error(\"Invalid base64 value\");\n        }\n        this._value_push(T_BLOB);\n    }\n    _read_close_double_brace() {\n        const ch = this._read_after_whitespace(false);\n        if (ch != CH_CC || this._read() != CH_CC) {\n            this._error(\"expected '}}'\");\n        }\n    }\n    isHighSurrogate(ch) {\n        return ch >= 0xd800 && ch <= 0xdbff;\n    }\n    isLowSurrogate(ch) {\n        return ch >= 0xdc00 && ch <= 0xdfff;\n    }\n    indexWhiteSpace(index, acceptComments) {\n        let ch = this._in.valueAt(index);\n        if (!acceptComments) {\n            for (; is_whitespace(ch); ch = this._in.valueAt(index++)) { }\n        }\n        else {\n            for (; is_whitespace(ch) || ch === CH_FORWARD_SLASH; ch = this._in.valueAt(index++)) {\n                if (ch === CH_FORWARD_SLASH) {\n                    ch = this._in.valueAt(index++);\n                    switch (ch) {\n                        case CH_FORWARD_SLASH:\n                            index = this.indexToNewLine(index);\n                            break;\n                        case CH_AS:\n                            index = this.indexToCloseComment(index);\n                            break;\n                        default:\n                            index--;\n                            break;\n                    }\n                }\n            }\n        }\n        return index;\n    }\n    indexToNewLine(index) {\n        let ch = this._in.valueAt(index);\n        while (ch !== EOF && ch !== CH_NL) {\n            if (ch === CH_CR) {\n                if (this._in.valueAt(index + 1) !== CH_NL) {\n                    return index;\n                }\n            }\n            ch = this._in.valueAt(index++);\n        }\n        return index;\n    }\n    indexToCloseComment(index) {\n        while (this._in.valueAt(index) !== CH_AS &&\n            this._in.valueAt(index + 1) !== CH_FORWARD_SLASH) {\n            index++;\n        }\n        return index;\n    }\n    _skip_triple_quote_gap(entryIndex, end, acceptComments) {\n        let tempIndex = entryIndex + 3;\n        tempIndex = this.indexWhiteSpace(tempIndex, acceptComments);\n        if (tempIndex + 2 <= end && this.verifyTriple(tempIndex)) {\n            return tempIndex + 4;\n        }\n        else {\n            return tempIndex + 1;\n        }\n    }\n    readClobEscapes(ii, end) {\n        let ch;\n        if (ii + 1 >= end) {\n            throw new Error(\"invalid escape sequence\");\n        }\n        ch = this._in.valueAt(ii + 1);\n        this._esc_len = 1;\n        switch (ch) {\n            case ESC_0:\n                return 0;\n            case ESC_a:\n                return 7;\n            case ESC_b:\n                return 8;\n            case ESC_t:\n                return 9;\n            case ESC_nl:\n                return 10;\n            case ESC_ff:\n                return 12;\n            case ESC_cr:\n                return 13;\n            case ESC_v:\n                return 11;\n            case ESC_dq:\n                return 34;\n            case ESC_sq:\n                return 39;\n            case ESC_qm:\n                return 63;\n            case ESC_bs:\n                return 92;\n            case ESC_fs:\n                return 47;\n            case ESC_nl2:\n                return -1;\n            case ESC_nl3:\n                if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {\n                    this._esc_len = 2;\n                }\n                return ESCAPED_NEWLINE;\n            case ESC_x:\n                if (ii + 3 >= end) {\n                    throw new Error(\"invalid escape sequence\");\n                }\n                ch = this._get_N_hexdigits(ii + 2, ii + 4);\n                this._esc_len = 3;\n                break;\n            default:\n                throw new Error(\"Invalid escape: /\" + ch);\n        }\n        return ch;\n    }\n    _read_escape_sequence(ii, end) {\n        let ch;\n        if (ii + 1 >= end) {\n            throw new Error(\"Invalid escape sequence.\");\n        }\n        ch = this._in.valueAt(ii + 1);\n        this._esc_len = 1;\n        switch (ch) {\n            case ESC_0:\n                return 0;\n            case ESC_a:\n                return 7;\n            case ESC_b:\n                return 8;\n            case ESC_t:\n                return 9;\n            case ESC_nl:\n                return 10;\n            case ESC_ff:\n                return 12;\n            case ESC_cr:\n                return 13;\n            case ESC_v:\n                return 11;\n            case ESC_dq:\n                return 34;\n            case ESC_sq:\n                return 39;\n            case ESC_qm:\n                return 63;\n            case ESC_bs:\n                return 92;\n            case ESC_fs:\n                return 47;\n            case ESC_nl2:\n                return -1;\n            case ESC_nl3:\n                if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {\n                    this._esc_len = 2;\n                }\n                return ESCAPED_NEWLINE;\n            case ESC_x:\n                if (ii + 3 >= end) {\n                    throw new Error(\"invalid escape sequence\");\n                }\n                ch = this._get_N_hexdigits(ii + 2, ii + 4);\n                this._esc_len = 3;\n                break;\n            case ESC_u:\n                if (ii + 5 >= end) {\n                    throw new Error(\"invalid escape sequence\");\n                }\n                ch = this._get_N_hexdigits(ii + 2, ii + 6);\n                this._esc_len = 5;\n                break;\n            case ESC_U:\n                if (ii + 9 >= end) {\n                    throw new Error(\"invalid escape sequence\");\n                }\n                ch = this._get_N_hexdigits(ii + 2, ii + 10);\n                this._esc_len = 9;\n                break;\n            default:\n                throw new Error(\"unexpected character after escape slash\");\n        }\n        return ch;\n    }\n    _get_N_hexdigits(ii, end) {\n        let ch, v = 0;\n        while (ii < end) {\n            ch = this._in.valueAt(ii);\n            v = v * 16 + get_hex_value(ch);\n            ii++;\n        }\n        return v;\n    }\n    _value_push(t) {\n        if (this._value_type !== ERROR) {\n            this._error(\"unexpected double push of value type!\");\n        }\n        this._value_type = t;\n    }\n    _value_pop() {\n        const t = this._value_type;\n        this._value_type = ERROR;\n        return t;\n    }\n    _run() {\n        let op;\n        while (this._ops.length > 0 && this._value_type === ERROR) {\n            op = this._ops.shift();\n            op.call(this);\n        }\n    }\n    _read() {\n        const ch = this._in.next();\n        return ch;\n    }\n    _read_skipping_comments() {\n        let ch = this._read();\n        if (ch == CH_FORWARD_SLASH) {\n            ch = this._read();\n            if (ch == CH_FORWARD_SLASH) {\n                this._read_to_newline();\n                ch = WHITESPACE_COMMENT1;\n            }\n            else if (ch == CH_AS) {\n                this._read_to_close_comment();\n                ch = WHITESPACE_COMMENT2;\n            }\n            else {\n                this._unread(ch);\n                ch = CH_FORWARD_SLASH;\n            }\n        }\n        return ch;\n    }\n    _read_to_newline() {\n        let ch;\n        for (;;) {\n            ch = this._read();\n            if (ch == EOF) {\n                break;\n            }\n            if (ch == CH_NL) {\n                break;\n            }\n            if (ch == CH_CR) {\n                ch = this._read();\n                if (ch != CH_NL) {\n                    this._unread(ch);\n                }\n                break;\n            }\n        }\n    }\n    _read_to_close_comment() {\n        let ch;\n        for (;;) {\n            ch = this._read();\n            if (ch == EOF) {\n                break;\n            }\n            if (ch == CH_AS) {\n                ch = this._read();\n                if (ch == CH_FORWARD_SLASH) {\n                    break;\n                }\n            }\n        }\n    }\n    _unread(ch) {\n        this._in.unread(ch);\n    }\n    _read_after_whitespace(recognize_comments) {\n        let ch;\n        if (recognize_comments) {\n            ch = this._read_skipping_comments();\n            while (is_whitespace(ch)) {\n                ch = this._read_skipping_comments();\n            }\n        }\n        else {\n            ch = this._read();\n            while (is_whitespace(ch)) {\n                ch = this._read();\n            }\n        }\n        return ch;\n    }\n    _peek(expected) {\n        let ch, ii = 0;\n        if (expected === undefined || expected.length < 1) {\n            return this._in.valueAt(this._in.position());\n        }\n        while (ii < expected.length) {\n            ch = this._read();\n            if (ch != expected.charCodeAt(ii)) {\n                break;\n            }\n            ii++;\n        }\n        if (ii === expected.length) {\n            ch = this._peek();\n        }\n        else {\n            this._unread(ch);\n            ch = ERROR;\n        }\n        while (ii > 0) {\n            ii--;\n            this._unread(expected.charCodeAt(ii));\n        }\n        return ch;\n    }\n    _peek_4_digits(ch1) {\n        let ii, ch, is_digits = true;\n        const chars = [];\n        if (!is_digit(ch1)) {\n            return ERROR;\n        }\n        for (ii = 0; ii < 3; ii++) {\n            ch = this._read();\n            chars.push(ch);\n            if (!is_digit(ch)) {\n                is_digits = false;\n                break;\n            }\n        }\n        ch = is_digits && ii == 3 ? this._peek() : ERROR;\n        while (chars.length > 0) {\n            this._unread(chars.pop());\n        }\n        return ch;\n    }\n    _read_required_digits(ch) {\n        if (!is_digit(ch)) {\n            return ERROR;\n        }\n        for (;;) {\n            ch = this._read();\n            if (!is_digit(ch)) {\n                break;\n            }\n        }\n        return ch;\n    }\n    _read_optional_digits(ch) {\n        while (is_digit(ch)) {\n            ch = this._read();\n        }\n        return ch;\n    }\n    _readNDigits(n) {\n        let ch;\n        if (n <= 0) {\n            throw new Error(\"Cannot read a lack of or negative number of digits.\");\n        }\n        while (n--) {\n            if (!is_digit((ch = this._read()))) {\n                throw new Error(\"Expected digit, got: \" + String.fromCharCode(ch));\n            }\n        }\n        return ch;\n    }\n    _readPastNDigits(n) {\n        this._readNDigits(n);\n        return this._read();\n    }\n    _read_required_hex_digits(ch) {\n        if (!is_hex_digit(ch)) {\n            return ERROR;\n        }\n        for (;;) {\n            ch = this._read();\n            if (!is_hex_digit(ch)) {\n                break;\n            }\n        }\n        return ch;\n    }\n    _read_N_hexdigits(n) {\n        let ch, ii = 0;\n        while (ii < n) {\n            ch = this._read();\n            if (!is_hex_digit(ch)) {\n                this._error(\"\" + n + \" digits required \" + ii + \" found\");\n                return ERROR;\n            }\n            ii++;\n        }\n        return ch;\n    }\n    _parseSymbolId(s) {\n        if (s[0] !== \"$\") {\n            return NaN;\n        }\n        for (let i = 1; i < s.length; i++) {\n            if (s[i] < \"0\" || s[i] > \"9\") {\n                return NaN;\n            }\n        }\n        return parseInt(s.substr(1, s.length));\n    }\n    _error(msg) {\n        this._ops.unshift(this._done_with_error);\n        this._error_msg = msg;\n    }\n}\n//# sourceMappingURL=IonParserTextRaw.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAS,WAAW;AACxO,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,YAAY;AACrC,MAAMC,GAAG,GAAG,CAAC,CAAC;AACd,MAAMC,KAAK,GAAG,CAAC,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,WAAW,GAAG,CAAC;AACrB,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,gBAAgB,GAAG,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;AAC1C,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,eAAe,GAAG,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC;AACzC,MAAMI,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,aAAa,GAAG,GAAG,CAACP,UAAU,CAAC,CAAC,CAAC;AACvC,MAAMQ,KAAK,GAAG,GAAG;AACjB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,GAAG;AACjB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,KAAK,GAAG,GAAG;AACjB,MAAMC,MAAM,GAAGlC,eAAe;AAC9B,MAAMmC,MAAM,GAAGpC,KAAK;AACpB,MAAMqC,MAAM,GAAG,EAAE;AACjB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,KAAK,GAAGhB,IAAI;AAClB,MAAMiB,KAAK,GAAG,GAAG;AACjB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,GAAG,GAAG,CAACrB,IAAI,EAAEC,IAAI,EAAEF,IAAI,CAAC;AAC9B,MAAMuB,WAAW,GAAG,MAAM;AAC1B,OAAO,SAASC,YAAYA,CAACC,CAAC,EAAE;EAC5B,QAAQA,CAAC;IACL,KAAK3E,GAAG;MACJ,OAAO,IAAI;IACf,KAAKC,KAAK;MACN,OAAO,IAAI;IACf,KAAKC,MAAM;MACP,OAAOH,QAAQ,CAAC6E,IAAI;IACxB,KAAKzE,MAAM;MACP,OAAOJ,QAAQ,CAAC8E,IAAI;IACxB,KAAKzE,KAAK;MACN,OAAOL,QAAQ,CAAC+E,GAAG;IACvB,KAAKzE,QAAQ;MACT,OAAON,QAAQ,CAAC+E,GAAG;IACvB,KAAKxE,OAAO;MACR,OAAOP,QAAQ,CAACgF,KAAK;IACzB,KAAKxE,eAAe;MAChB,OAAOR,QAAQ,CAACgF,KAAK;IACzB,KAAKvE,SAAS;MACV,OAAOT,QAAQ,CAACiF,OAAO;IAC3B,KAAKvE,WAAW;MACZ,OAAOV,QAAQ,CAACkF,SAAS;IAC7B,KAAKvE,YAAY;MACb,OAAOX,QAAQ,CAACmF,MAAM;IAC1B,KAAKvE,UAAU;MACX,OAAOZ,QAAQ,CAACmF,MAAM;IAC1B,KAAKtE,SAAS;MACV,OAAOb,QAAQ,CAACmF,MAAM;IAC1B,KAAKrE,SAAS;MACV,OAAOd,QAAQ,CAACoF,MAAM;IAC1B,KAAKrE,SAAS;MACV,OAAOf,QAAQ,CAACoF,MAAM;IAC1B,KAAKpE,OAAO;MACR,OAAOhB,QAAQ,CAACqF,IAAI;IACxB,KAAKpE,OAAO;MACR,OAAOjB,QAAQ,CAACqF,IAAI;IACxB,KAAKnE,MAAM;MACP,OAAOlB,QAAQ,CAACsF,IAAI;IACxB,KAAKnE,MAAM;MACP,OAAOnB,QAAQ,CAACuF,IAAI;IACxB,KAAKnE,MAAM;MACP,OAAOpB,QAAQ,CAACwF,IAAI;IACxB,KAAKnE,QAAQ;MACT,OAAOrB,QAAQ,CAACyF,MAAM;IAC1B;MACI,MAAM,IAAIC,KAAK,CAAC,gBAAgB,GAAGC,MAAM,CAACf,CAAC,CAAC,GAAG,GAAG,CAAC;EAC3D;AACJ;AACA,SAASgB,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIA,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO1F,MAAM;EACjB;EACA,IAAI0F,GAAG,KAAK,MAAM,EAAE;IAChB,OAAOzF,MAAM;EACjB;EACA,IAAIyF,GAAG,KAAK,OAAO,EAAE;IACjB,OAAOzF,MAAM;EACjB;EACA,IAAIyF,GAAG,KAAK,KAAK,EAAE;IACf,OAAOrF,eAAe;EAC1B;EACA,IAAIqF,GAAG,KAAK,MAAM,EAAE;IAChB,OAAOrF,eAAe;EAC1B;EACA,IAAIqF,GAAG,KAAK,MAAM,EAAE;IAChB,OAAOrF,eAAe;EAC1B;EACA,MAAM,IAAIkF,KAAK,CAAC,mBAAmB,GAAGG,GAAG,GAAG,GAAG,CAAC;AACpD;AACA,SAASC,kBAAkBA,CAACD,GAAG,EAAE;EAC7B,IAAIA,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO1F,MAAM;EACjB;EACA,IAAI0F,GAAG,KAAK,MAAM,EAAE;IAChB,OAAOzF,MAAM;EACjB;EACA,IAAIyF,GAAG,KAAK,KAAK,EAAE;IACf,OAAOxF,KAAK;EAChB;EACA,IAAIwF,GAAG,KAAK,OAAO,EAAE;IACjB,OAAOtF,OAAO;EAClB;EACA,IAAIsF,GAAG,KAAK,SAAS,EAAE;IACnB,OAAOpF,SAAS;EACpB;EACA,IAAIoF,GAAG,KAAK,WAAW,EAAE;IACrB,OAAOnF,WAAW;EACtB;EACA,IAAImF,GAAG,KAAK,QAAQ,EAAE;IAClB,OAAOlF,YAAY;EACvB;EACA,IAAIkF,GAAG,KAAK,QAAQ,EAAE;IAClB,OAAO/E,SAAS;EACpB;EACA,IAAI+E,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO7E,OAAO;EAClB;EACA,IAAI6E,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO3E,MAAM;EACjB;EACA,IAAI2E,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO1E,MAAM;EACjB;EACA,IAAI0E,GAAG,KAAK,MAAM,EAAE;IAChB,OAAOzE,MAAM;EACjB;EACA,IAAIyE,GAAG,KAAK,QAAQ,EAAE;IAClB,OAAOxE,QAAQ;EACnB;EACA,MAAM,IAAIqE,KAAK,CAAC,gBAAgB,GAAGG,GAAG,GAAG,GAAG,CAAC;AACjD;AACA,SAASE,aAAaA,CAACC,EAAE,EAAE;EACvB,QAAQA,EAAE;IACN,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,CAAC;IACZ,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,GAAG;MACJ,OAAO,EAAE;IACb,KAAK,GAAG;MACJ,OAAO,EAAE;IACb,KAAK,GAAG;MACJ,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;IACb,KAAK,EAAE;MACH,OAAO,EAAE;EACjB;EACA,MAAM,IAAIN,KAAK,CAAC,2CAA2C,CAAC;AAChE;AACA,SAASO,sBAAsBA,CAACC,WAAW,EAAEC,cAAc,EAAE;EACzD,IAAIA,cAAc,GAAG,CAAC,EAAE;IACpB,OAAO,KAAK;EAChB;EACA,IAAI,CAAED,WAAW,GAAGC,cAAc,GAAI,GAAG,KAAK,CAAC,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,SAASC,oBAAoBA,CAACJ,EAAE,EAAEK,cAAc,EAAE;EAC9C,IAAIL,EAAE,IAAI1E,KAAK,EAAE;IACb,OAAO+E,cAAc;EACzB;EACA,IAAIL,EAAE,IAAIzE,KAAK,EAAE;IACb,OAAO8E,cAAc;EACzB;EACA,IAAIzG,aAAa,CAACoG,EAAE,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAIA,EAAE,GAAG,EAAE,EAAE;IACT,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,OAAO,MAAMM,aAAa,CAAC;EACvBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,wBAAwB,GAAG,UAAUC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;MAChF,IAAIC,EAAE;QAAEC,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;MAC1B,IAAID,GAAG,IAAI5D,IAAI,EAAE;QACb4D,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,KAAK,CAAC;QACvB,IAAI7H,mBAAmB,CAAC4H,GAAG,CAAC,EAAE;UAC1BD,EAAE,GAAG,IAAI,CAACG,eAAe;QAC7B,CAAC,MACI,IAAIL,uBAAuB,EAAE;UAC9BE,EAAE,GAAG,IAAI,CAACI,qBAAqB;QACnC;MACJ,CAAC,MACI,IAAI7H,QAAQ,CAAC0H,GAAG,CAAC,EAAE;QACpBD,EAAE,GAAG,IAAI,CAACK,YAAY;MAC1B,CAAC,MACI,IAAIP,uBAAuB,EAAE;QAC9BE,EAAE,GAAG,IAAI,CAACI,qBAAqB;MACnC;MACA,IAAIJ,EAAE,IAAIM,SAAS,EAAE;QACjB,IAAI,CAACC,IAAI,CAACC,OAAO,CAACR,EAAE,CAAC;QACrB,IAAI,CAACS,OAAO,CAACZ,GAAG,CAAC;MACrB,CAAC,MACI;QACD,IAAI,CAACa,MAAM,CAAC,iDAAiD,CAAC;MAClE;IACJ,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,UAAUC,UAAU,EAAEtB,cAAc,EAAE;MAC7D,IAAIL,EAAE;MACN,IAAI,CAAC4B,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;MACjC,SAAS;QACL9B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;QACjB,IAAI/B,EAAE,IAAIxE,KAAK,EAAE;UACb,IAAI,CAACwG,4BAA4B,CAAC,CAAC;QACvC,CAAC,MACI,IAAIhC,EAAE,IAAI2B,UAAU,EAAE;UACvB;QACJ,CAAC,MACI,IAAI,CAACvB,oBAAoB,CAACJ,EAAE,EAAEK,cAAc,CAAC,EAAE;UAChD,MAAM,IAAIX,KAAK,CAAC,oBAAoB,GAAGM,EAAE,GAAG,YAAY,CAAC;QAC7D;MACJ;IACJ,CAAC;IACD,IAAI,CAAC6B,GAAG,GAAGrB,MAAM;IACjB,IAAI,CAACc,IAAI,GAAG,CAAC,IAAI,CAACW,qBAAqB,CAAC;IACxC,IAAI,CAACC,WAAW,GAAGhI,KAAK;IACxB,IAAI,CAACiI,MAAM,GAAG,EAAE;IAChB,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACQ,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,KAAK,GAAGrI,GAAG;IAChB,IAAI,CAACsI,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,MAAMC,OAAO,GAAG;MACZ,EAAE,EAAE,IAAI,CAACC,wBAAwB;MACjC,EAAE,EAAE,IAAI,CAACC,yBAAyB;MAClC,GAAG,EAAE,IAAI,CAACC,wBAAwB;MAClC,EAAE,EAAE,IAAI,CAACC,uBAAuB;MAChC,EAAE,EAAE,IAAI,CAACpC,wBAAwB;MACjC,EAAE,EAAE,IAAI,CAACqC,yBAAyB;MAClC,EAAE,EAAE,IAAI,CAACC;IACb,CAAC;IACD,MAAMC,UAAU,GAAG,SAAAA,CAAUrD,GAAG,EAAEsD,EAAE,EAAE;MAClC,IAAIC,CAAC,GAAGvD,GAAG,CAACwD,MAAM;QAAErD,EAAE;MACtB,OAAOoD,CAAC,GAAG,CAAC,EAAE;QACVA,CAAC,EAAE;QACHpD,EAAE,GAAGH,GAAG,CAACnE,UAAU,CAAC0H,CAAC,CAAC;QACtBT,OAAO,CAAC3C,EAAE,CAAC,GAAGmD,EAAE;MACpB;IACJ,CAAC;IACDD,UAAU,CAAC,YAAY,EAAE,IAAI,CAACI,wBAAwB,CAAC;IACvDJ,UAAU,CAAC,wDAAwD,EAAE,IAAI,CAACK,yBAAyB,CAAC;IACpGL,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAACM,2BAA2B,CAAC;IACnEb,OAAO,CAACnG,KAAK,CAAC,GAAG,IAAI,CAACuG,uBAAuB;IAC7CJ,OAAO,CAAClG,KAAK,CAAC,GAAG,IAAI,CAACkE,wBAAwB;IAC9C,IAAI,CAAC8C,0BAA0B,GAAGd,OAAO;EAC7C;EACAe,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjB,UAAU;EAC1B;EACAkB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,cAAc;EAC9B;EACAlC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACqB,GAAG;EACnB;EACA+B,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrB,IAAI;EACpB;EACAsB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACpB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACAoB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACpD,UAAU;EAC1B;EACAqD,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACD,MAAM,CAAC,CAAC,EAAE;MACf,OAAO,IAAI;IACf;IACA,MAAME,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC3B,KAAK,CAAC,CAAC4B,WAAW,CAAC,CAAC;IAClE,QAAQ,IAAI,CAAC5B,KAAK;MACd,KAAKjI,KAAK;MACV,KAAKC,QAAQ;QACT,IAAI0J,OAAO,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;UACzB,MAAMf,CAAC,GAAGgB,MAAM,CAACJ,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;UAClC,OAAO,CAACjB,CAAC;QACb;QACA,OAAOgB,MAAM,CAACJ,OAAO,CAAC;MAC1B;QACI,MAAM,IAAItE,KAAK,CAAC,kEAAkE,CAAC;IAC3F;EACJ;EACA4E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACR,MAAM,CAAC,CAAC,EAAE;MACf,OAAO,IAAI;IACf;IACA,MAAMS,CAAC,GAAG,IAAI,CAACN,mBAAmB,CAAC,IAAI,CAAC3B,KAAK,CAAC;IAC9C,QAAQ,IAAI,CAACA,KAAK;MACd,KAAKjI,KAAK;MACV,KAAKC,QAAQ;QACT,OAAOkK,MAAM,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC;MAC5B,KAAKhK,OAAO;QACR,OAAOiK,MAAM,CAACD,CAAC,CAAC;MACpB,KAAK/J,eAAe;QAChB,IAAI+J,CAAC,IAAI,MAAM,EAAE;UACb,OAAOC,MAAM,CAACC,iBAAiB;QACnC,CAAC,MACI,IAAIF,CAAC,IAAI,MAAM,EAAE;UAClB,OAAOC,MAAM,CAACE,iBAAiB;QACnC,CAAC,MACI,IAAIH,CAAC,IAAI,KAAK,EAAE;UACjB,OAAOC,MAAM,CAACG,GAAG;QACrB;MACJ;QACI,MAAM,IAAIjF,KAAK,CAAC,yBAAyB,CAAC;IAClD;EACJ;EACAkF,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACd,MAAM,CAAC,CAAC,EAAE;MACf,OAAO,IAAI;IACf;IACA,MAAMS,CAAC,GAAG,IAAI,CAACN,mBAAmB,CAAC7J,MAAM,CAAC;IAC1C,IAAImK,CAAC,KAAK,MAAM,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,MAAM,IAAI7E,KAAK,CAAC,8BAA8B,GAAG6E,CAAC,GAAG,GAAG,CAAC;EAC7D;EACAN,mBAAmBA,CAACrF,CAAC,EAAE;IACnB,IAAIiG,KAAK;IACT,IAAI7E,EAAE;IACN,IAAIuE,CAAC,GAAG,EAAE;IACV,QAAQ3F,CAAC;MACL,KAAKzE,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,KAAK;MACV,KAAKC,QAAQ;MACb,KAAKC,OAAO;MACZ,KAAKC,eAAe;MACpB,KAAKC,SAAS;MACd,KAAKC,WAAW;MAChB,KAAKC,YAAY;MACjB,KAAKC,UAAU;QACX,KAAKiK,KAAK,GAAG,IAAI,CAACjD,MAAM,EAAEiD,KAAK,GAAG,IAAI,CAACzC,IAAI,EAAEyC,KAAK,EAAE,EAAE;UAClDN,CAAC,IAAI5E,MAAM,CAACmF,YAAY,CAAC,IAAI,CAACjD,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC,CAAC;QACrD;QACA;MACJ,KAAK3J,MAAM;QACP,KAAK2J,KAAK,GAAG,IAAI,CAACjD,MAAM,EAAEiD,KAAK,GAAG,IAAI,CAACzC,IAAI,EAAEyC,KAAK,EAAE,EAAE;UAClD7E,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;UAC5B,IAAIxL,cAAc,CAAC2G,EAAE,CAAC,EAAE;YACpBuE,CAAC,IAAI5E,MAAM,CAACmF,YAAY,CAAC9E,EAAE,CAAC;UAChC;QACJ;QACA;MACJ,KAAKnF,SAAS;MACd,KAAKC,SAAS;MACd,KAAKC,SAAS;QACV,KAAK8J,KAAK,GAAG,IAAI,CAACjD,MAAM,EAAEiD,KAAK,GAAG,IAAI,CAACzC,IAAI,EAAEyC,KAAK,EAAE,EAAE;UAClD,IAAIG,SAAS,GAAG,KAAK;UACrBhF,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;UAC5B,IAAI7E,EAAE,IAAIxE,KAAK,EAAE;YACbwE,EAAE,GAAG,IAAI,CAACiF,qBAAqB,CAACJ,KAAK,EAAE,IAAI,CAACzC,IAAI,CAAC;YACjDyC,KAAK,IAAI,IAAI,CAACxC,QAAQ;YACtB2C,SAAS,GAAG,IAAI;UACpB;UACA,IAAI,IAAI,CAACE,eAAe,CAAClF,EAAE,CAAC,EAAE;YAC1B6E,KAAK,EAAE;YACP,IAAIM,QAAQ,GAAG,IAAI,CAACtD,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;YACtC,IAAIM,QAAQ,IAAI3J,KAAK,EAAE;cACnB2J,QAAQ,GAAG,IAAI,CAACF,qBAAqB,CAACJ,KAAK,EAAE,IAAI,CAACzC,IAAI,CAAC;cACvDyC,KAAK,IAAI,IAAI,CAACxC,QAAQ;YAC1B;YACA,IAAI,IAAI,CAAC+C,cAAc,CAACD,QAAQ,CAAC,EAAE;cAC/B,MAAME,WAAW,GAAGrF,EAAE;cACtB,MAAMsF,WAAW,GAAGH,QAAQ;cAC5B,MAAMI,SAAS,GAAG,OAAO,IACpB,CAACF,WAAW,GAAG3G,WAAW,KAAK,EAAE,CAAC,IAClC4G,WAAW,GAAG5G,WAAW,CAAC;cAC/B6F,CAAC,IAAI5E,MAAM,CAAC6F,aAAa,CAACD,SAAS,CAAC;YACxC,CAAC,MACI;cACD,MAAM,IAAI7F,KAAK,CAAC,uCAAuC,GAAGM,EAAE,CAAC;YACjE;UACJ,CAAC,MACI,IAAI,IAAI,CAACoF,cAAc,CAACpF,EAAE,CAAC,EAAE;YAC9B,MAAM,IAAIN,KAAK,CAAC,4BAA4B,GAAGM,EAAE,CAAC;UACtD,CAAC,MACI,IAAIpB,CAAC,KAAK7D,SAAS,IACpBiF,EAAE,KAAKpE,KAAK,IACZ,CAACoJ,SAAS,IACV,IAAI,CAACS,YAAY,CAACZ,KAAK,CAAC,EAAE;YAC1BA,KAAK,GAAG,IAAI,CAACa,sBAAsB,CAACb,KAAK,EAAE,IAAI,CAACzC,IAAI,EAAE,IAAI,CAAC;UAC/D,CAAC,MACI,IAAIpC,EAAE,IAAI,CAAC,EAAE;YACd,IAAIgF,SAAS,EAAE;cACXT,CAAC,IAAI5E,MAAM,CAAC6F,aAAa,CAACxF,EAAE,CAAC;YACjC,CAAC,MACI;cACD,IAAIpB,CAAC,KAAK7D,SAAS,IACfiF,EAAE,KAAK3B,OAAO,IACd,IAAI,CAACwD,GAAG,CAACkD,OAAO,CAACF,KAAK,GAAG,CAAC,CAAC,KAAKzG,OAAO,EAAE;gBACzC4B,EAAE,GAAG5B,OAAO;gBACZyG,KAAK,EAAE;cACX;cACAN,CAAC,IAAI5E,MAAM,CAACmF,YAAY,CAAC9E,EAAE,CAAC;YAChC;UACJ;QACJ;QACA;MACJ;QACI,MAAM,IAAIN,KAAK,CAAC,kCAAkC,CAAC;IAC3D;IACA,OAAO6E,CAAC;EACZ;EACAoB,uBAAuBA,CAAC/G,CAAC,EAAE;IACvB,MAAMgH,KAAK,GAAG,EAAE;IAChB,QAAQhH,CAAC;MACL,KAAK5D,OAAO;QACR,KAAK,IAAI6J,KAAK,GAAG,IAAI,CAACjD,MAAM,EAAEiD,KAAK,GAAG,IAAI,CAACzC,IAAI,EAAEyC,KAAK,EAAE,EAAE;UACtD,MAAM7E,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;UAClC,IAAI7E,EAAE,KAAKxE,KAAK,EAAE;YACdoK,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACjB,KAAK,EAAE,IAAI,CAACzC,IAAI,CAAC,CAAC;YAClDyC,KAAK,IAAI,IAAI,CAACxC,QAAQ;UAC1B,CAAC,MACI,IAAIrC,EAAE,GAAG,GAAG,EAAE;YACf4F,KAAK,CAACC,IAAI,CAAC7F,EAAE,CAAC;UAClB,CAAC,MACI;YACD,MAAM,IAAIN,KAAK,CAAC,uCAAuC,CAAC;UAC5D;QACJ;QACA;MACJ,KAAKzE,OAAO;QACR,KAAK,IAAI4J,KAAK,GAAG,IAAI,CAACjD,MAAM,EAAEiD,KAAK,GAAG,IAAI,CAACzC,IAAI,EAAEyC,KAAK,EAAE,EAAE;UACtD,MAAM7E,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;UAClC,IAAI7E,EAAE,KAAKxE,KAAK,EAAE;YACd,MAAMuK,OAAO,GAAG,IAAI,CAACD,eAAe,CAACjB,KAAK,EAAE,IAAI,CAACzC,IAAI,CAAC;YACtD,IAAI2D,OAAO,IAAI,CAAC,EAAE;cACdH,KAAK,CAACC,IAAI,CAACE,OAAO,CAAC;YACvB;YACAlB,KAAK,IAAI,IAAI,CAACxC,QAAQ;UAC1B,CAAC,MACI,IAAIrC,EAAE,KAAKpE,KAAK,EAAE;YACnB,IAAI,IAAI,CAAC6J,YAAY,CAACZ,KAAK,CAAC,EAAE;cAC1BA,KAAK,GAAG,IAAI,CAACa,sBAAsB,CAACb,KAAK,EAAE,IAAI,CAACzC,IAAI,EAAE,KAAK,CAAC;YAChE,CAAC,MACI;cACDwD,KAAK,CAACC,IAAI,CAAC7F,EAAE,CAAC;YAClB;UACJ,CAAC,MACI,IAAIA,EAAE,GAAG,GAAG,EAAE;YACf4F,KAAK,CAACC,IAAI,CAAC7F,EAAE,CAAC;UAClB,CAAC,MACI;YACD,MAAM,IAAIN,KAAK,CAAC,uCAAuC,CAAC;UAC5D;QACJ;QACA;MACJ;QACI,MAAM,IAAIA,KAAK,CAAC,sCAAsC,CAAC;IAC/D;IACA,OAAOsG,UAAU,CAACC,IAAI,CAACL,KAAK,CAAC;EACjC;EACAM,IAAIA,CAAA,EAAG;IACH,IAAI,CAACrC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACtB,IAAI,GAAG,EAAE;IACd,IAAI,IAAI,CAACL,WAAW,KAAKhI,KAAK,EAAE;MAC5B,IAAI,CAACiM,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAAC7D,KAAK,GAAG,IAAI,CAAC8D,UAAU,CAAC,CAAC;IAC9B,IAAIxH,CAAC;IACL,IAAI,IAAI,CAAC0D,KAAK,KAAKpI,KAAK,EAAE;MACtB,IAAI,CAACiI,MAAM,CAAC0D,IAAI,CAAC3L,KAAK,CAAC;MACvB,OAAOmH,SAAS;IACpB,CAAC,MACI;MACDzC,CAAC,GAAG,IAAI,CAAC0D,KAAK;IAClB;IACA,IAAI,CAAC5B,UAAU,GAAG,IAAI,CAACD,WAAW;IAClC,IAAI,CAACA,WAAW,GAAG,KAAK;IACxB,OAAO7B,CAAC;EACZ;EACAqD,qBAAqBA,CAAA,EAAG;IACpB,MAAMjC,EAAE,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC;IACvB,IAAIjB,EAAE,IAAI/F,GAAG,EAAE;MACX,IAAI,CAACoM,WAAW,CAACpM,GAAG,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACqH,IAAI,CAACC,OAAO,CAAC,IAAI,CAACU,qBAAqB,CAAC;MAC7C,IAAI,CAACX,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC+E,WAAW,CAAC;IACvC;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAMvG,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IAC5C,IAAIxG,EAAE,IAAIhE,KAAK,EAAE;MACb,IAAI,CAACqK,WAAW,CAACpM,GAAG,CAAC;IACzB,CAAC,MACI,IAAI+F,EAAE,KAAK/F,GAAG,EAAE;MACjB,MAAM,IAAIyF,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAAC8B,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACsB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACgF,iBAAiB,CAAC;MACzC,IAAI,CAACjF,IAAI,CAACC,OAAO,CAAC,IAAI,CAACkF,gBAAgB,CAAC;IAC5C;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM1G,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IAC5C,IAAIxG,EAAE,IAAI5D,KAAK,EAAE;MACb,IAAI,CAACiK,WAAW,CAACpM,GAAG,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACuH,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACsB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACoF,gBAAgB,CAAC;MACxC,IAAI,CAACrF,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC+E,WAAW,CAAC;IACvC;EACJ;EACAM,mBAAmBA,CAAA,EAAG;IAClB,IAAI7F,EAAE,GAAG,IAAI,CAAC8F,gBAAgB;MAAE7G,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IACtE,QAAQxG,EAAE;MACN,KAAKpE,KAAK;QACNmF,EAAE,GAAG,IAAI,CAAC+F,aAAa;QACvB,IAAI,IAAI,CAAC7F,KAAK,CAAC,IAAI,CAAC,IAAI/G,KAAK,EAAE;UAC3B6G,EAAE,GAAG,IAAI,CAACgG,aAAa;QAC3B;QACA;MACJ,KAAKlL,eAAe;QAChBkF,EAAE,GAAG,IAAI,CAACiG,aAAa;QACvB;MACJ,KAAK9K,KAAK;QACN,IAAI,CAACmK,WAAW,CAACpM,GAAG,CAAC;QACrB;MACJ;QACI,IAAIR,SAAS,CAACuG,EAAE,CAAC,EAAE;UACfe,EAAE,GAAG,IAAI,CAACkG,YAAY;QAC1B;QACA;IACR;IACA,IAAIlG,EAAE,KAAK,IAAI,CAAC8F,gBAAgB,EAAE;MAC9B,IAAI,CAACpF,MAAM,CAAC,qDAAqD,CAAC;IACtE,CAAC,MACI;MACDV,EAAE,CAACmG,IAAI,CAAC,IAAI,CAAC;MACb,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvBnH,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;MACtC,IAAIxG,EAAE,IAAI3D,KAAK,EAAE;QACb,IAAI,CAACoF,MAAM,CAAC,cAAc,CAAC;QAC3B;MACJ;MACA,IAAI,CAACH,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC6F,kBAAkB,CAAC;MAC1C,IAAI,CAAC9F,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC+E,WAAW,CAAC;IACvC;EACJ;EACAK,gBAAgBA,CAAA,EAAG;IACf,IAAI3G,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IAC1C,IAAIxG,EAAE,IAAIlE,KAAK,EAAE;MACbkE,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;MACtC,IAAIxG,EAAE,IAAI5D,KAAK,EAAE;QACb,IAAI,CAACiK,WAAW,CAACpM,GAAG,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACuH,OAAO,CAACxB,EAAE,CAAC;QAChB,IAAI,CAACsB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACoF,gBAAgB,CAAC;QACxC,IAAI,CAACrF,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC+E,WAAW,CAAC;MACvC;IACJ,CAAC,MACI,IAAItG,EAAE,IAAI5D,KAAK,EAAE;MAClB,IAAI,CAACiK,WAAW,CAACpM,GAAG,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACwH,MAAM,CAAC,qBAAqB,CAAC;IACtC;EACJ;EACA2F,kBAAkBA,CAAA,EAAG;IACjB,IAAIpH,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IAC1C,IAAIxG,EAAE,IAAIlE,KAAK,EAAE;MACbkE,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;MACtC,IAAIxG,EAAE,IAAI9D,KAAK,EAAE;QACb,IAAI,CAACmK,WAAW,CAACpM,GAAG,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACuH,OAAO,CAACxB,EAAE,CAAC;QAChB,IAAI,CAACsB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACqF,mBAAmB,CAAC;MAC/C;IACJ,CAAC,MACI,IAAI5G,EAAE,IAAI9D,KAAK,EAAE;MAClB,IAAI,CAACmK,WAAW,CAACpM,GAAG,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACwH,MAAM,CAAC,qBAAqB,CAAC;IACtC;EACJ;EACA0F,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACzE,cAAc,GAAG,IAAI,CAAC0D,UAAU,CAAC,CAAC;IACvC,MAAM7B,CAAC,GAAG,IAAI,CAACN,mBAAmB,CAAC,IAAI,CAACvB,cAAc,CAAC;IACvD,QAAQ,IAAI,CAACA,cAAc;MACvB,KAAK/H,YAAY;QACb,IAAInB,UAAU,CAAC+K,CAAC,CAAC,EAAE;UACf,MAAM,IAAI7E,KAAK,CAAC,aAAa,GAAG6E,CAAC,GAAG,iCAAiC,CAAC;QAC1E;MACJ,KAAK1J,SAAS;MACd,KAAKC,SAAS;MACd,KAAKC,SAAS;QACV,IAAI,CAAC0H,UAAU,GAAG8B,CAAC;QACnB;MACJ;QACI,MAAM,IAAI7E,KAAK,CAAC,mBAAmB,GAAG6E,CAAC,CAAC;IAChD;EACJ;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACe,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;EACpD;EACAG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACY,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACZ,gBAAgB,CAAC;EACxD;EACAY,kBAAkBA,CAACxG,uBAAuB,EAAEC,UAAU,EAAE;IACpD,MAAMd,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IAC5C,IAAIxG,EAAE,IAAI/F,GAAG,EAAE;MACX,IAAI,CAACqN,sBAAsB,CAACtH,EAAE,EAAEa,uBAAuB,EAAEC,UAAU,CAAC;IACxE,CAAC,MACI;MACD,MAAMqC,EAAE,GAAG,IAAI,CAACM,0BAA0B,CAACzD,EAAE,CAAC;MAC9C,IAAImD,EAAE,IAAI9B,SAAS,EAAE;QACjB8B,EAAE,CAAC+D,IAAI,CAAC,IAAI,EAAElH,EAAE,EAAEa,uBAAuB,EAAEC,UAAU,CAAC;MAC1D,CAAC,MACI;QACD,IAAI,CAACW,MAAM,CAAC,wBAAwB,GAAGvI,OAAO,CAAC8G,EAAE,CAAC,GAAG,GAAG,CAAC;MAC7D;IACJ;EACJ;EACAsH,sBAAsBA,CAAC1G,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAC7D,IAAI,CAACQ,IAAI,CAACC,OAAO,CAAC,IAAI,CAACgG,KAAK,CAAC;EACjC;EACA3E,wBAAwBA,CAAChC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAC/D,IAAI,CAACuF,WAAW,CAAClL,MAAM,CAAC;IACxB,IAAI,CAACmG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACgF,iBAAiB,CAAC;EAC7C;EACA1D,yBAAyBA,CAACjC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAChE,IAAI,CAACuF,WAAW,CAACjL,MAAM,CAAC;IACxB,IAAI,CAACkG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACmF,iBAAiB,CAAC;EAC7C;EACA5D,wBAAwBA,CAAClC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAC/D,IAAI0G,GAAG;IACP,MAAMxG,GAAG,GAAG,IAAI,CAACe,KAAK,CAAC,CAAC;IACxB,IAAIf,GAAG,IAAI/E,aAAa,EAAE;MACtBuL,GAAG,GAAG,IAAI,CAAChB,sBAAsB,CAAC,KAAK,CAAC;MACxC,IAAIgB,GAAG,IAAI5L,KAAK,EAAE;QACd,IAAI,CAAC0F,IAAI,CAACC,OAAO,CAAC,IAAI,CAACkG,kBAAkB,CAAC;MAC9C,CAAC,MACI,IAAID,GAAG,IAAI3L,eAAe,EAAE;QAC7B,IAAI,CAACyF,IAAI,CAACC,OAAO,CAAC,IAAI,CAACmG,kBAAkB,CAAC;MAC9C,CAAC,MACI;QACD,IAAI,CAAClG,OAAO,CAACgG,GAAG,CAAC;QACjB,IAAI,CAAClG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACoG,UAAU,CAAC;MACtC;IACJ,CAAC,MACI;MACD,IAAI,CAACnG,OAAO,CAACR,GAAG,CAAC;MACjB,IAAI,CAACqF,WAAW,CAAChL,QAAQ,CAAC;MAC1B,IAAI,CAACiG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACqF,mBAAmB,CAAC;IAC/C;EACJ;EACA7D,uBAAuBA,CAACnC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAC9D,MAAME,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,KAAK,CAAC;IAC7B,IAAI,CAACO,OAAO,CAACZ,GAAG,CAAC;IACjB,IAAIxH,mBAAmB,CAAC4H,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACM,IAAI,CAACC,OAAO,CAAC,IAAI,CAACqG,cAAc,CAAC;IAC1C,CAAC,MACI,IAAI/G,uBAAuB,EAAE;MAC9B,IAAI,CAACS,IAAI,CAACC,OAAO,CAAC,IAAI,CAACJ,qBAAqB,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACM,MAAM,CAAC,gBAAgB,CAAC;IACjC;EACJ;EACA6B,wBAAwBA,CAAC1C,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAC/D,MAAME,GAAG,GAAG,IAAI,CAAC6G,cAAc,CAACjH,GAAG,CAAC;IACpC,IAAI,CAACY,OAAO,CAACZ,GAAG,CAAC;IACjB,IAAII,GAAG,IAAIlE,IAAI,IAAIkE,GAAG,IAAIvE,KAAK,EAAE;MAC7B,IAAI,CAAC6E,IAAI,CAACC,OAAO,CAAC,IAAI,CAACuG,cAAc,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAACxG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACH,YAAY,CAAC;IACxC;EACJ;EACA4B,yBAAyBA,CAACpC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAChE,IAAIC,EAAE;IACN,IAAI,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC,IAAI/G,KAAK,EAAE;MAC3B6G,EAAE,GAAG,IAAI,CAACgG,aAAa;MACvBhG,EAAE,CAACmG,IAAI,CAAC,IAAI,CAAC;IACjB,CAAC,MACI;MACDnG,EAAE,GAAG,IAAI,CAAC+F,aAAa;MACvB/F,EAAE,CAACmG,IAAI,CAAC,IAAI,CAAC;MACb,IAAI,IAAI,CAACa,0BAA0B,CAAChH,EAAE,CAAC,EAAE;QACrC,IAAI,CAACO,IAAI,CAACC,OAAO,CAACT,UAAU,CAAC;MACjC;IACJ;EACJ;EACAmC,yBAAyBA,CAACrC,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAChE,IAAI,CAACQ,IAAI,CAACC,OAAO,CAAC,IAAI,CAACyF,aAAa,CAAC;EACzC;EACAzD,yBAAyBA,CAAC3C,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAChE,IAAI,CAACmG,YAAY,CAAC,CAAC;IACnB,MAAMe,IAAI,GAAG,IAAI,CAAC5B,UAAU,CAAC,CAAC;IAC9B,IAAI4B,IAAI,IAAIrN,YAAY,EAAE;MACtB,MAAM,IAAI+E,KAAK,CAAC,wBAAwB,CAAC;IAC7C;IACA,IAAIuI,MAAM,GAAG,IAAI,CAAChE,mBAAmB,CAAC+D,IAAI,CAAC;IAC3C,IAAIxO,UAAU,CAACyO,MAAM,CAAC,EAAE;MACpB,IAAIC,GAAG,GAAGtI,gBAAgB,CAACqI,MAAM,CAAC;MAClC,IAAIC,GAAG,KAAK/N,MAAM,EAAE;QAChB,IAAI,CAACsG,WAAW,GAAG,IAAI;QACvB,IAAI,IAAI,CAACQ,KAAK,CAAC,CAAC,KAAK3E,KAAK,EAAE;UACxB,IAAI,CAACyF,KAAK,CAAC,CAAC;UACZ,MAAM/B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;UACvB,IAAItI,SAAS,CAACuG,EAAE,CAAC,KAAK,IAAI,EAAE;YACxB,MAAM,IAAIN,KAAK,CAAC,kCAAkC,CAAC;UACvD;UACA,IAAI,CAACuH,YAAY,CAAC,CAAC;UACnB,IAAI,IAAI,CAACb,UAAU,CAAC,CAAC,KAAKzL,YAAY,EAAE;YACpC,MAAM,IAAI+E,KAAK,CAAC,kCAAkC,CAAC;UACvD;UACAuI,MAAM,GAAG,IAAI,CAAChE,mBAAmB,CAACtJ,YAAY,CAAC;UAC/CuN,GAAG,GAAGpI,kBAAkB,CAACmI,MAAM,CAAC;QACpC;QACA,IAAI,CAACrG,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAACQ,IAAI,GAAG,CAAC,CAAC;MAClB;MACA,IAAI,CAACiE,WAAW,CAAC6B,GAAG,CAAC;IACzB,CAAC,MACI;MACD,MAAMlI,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;MAC5C,IAAIxG,EAAE,IAAI3D,KAAK,IAAI,IAAI,CAAC4E,KAAK,CAAC,CAAC,IAAI5E,KAAK,EAAE;QACtC,IAAI,CAAC0F,KAAK,CAAC,CAAC;QACZ,MAAMoG,GAAG,GAAG,IAAI,CAACC,cAAc,CAACH,MAAM,CAAC;QACvC,IAAIE,GAAG,KAAK,CAAC,EAAE;UACX,MAAM,IAAIzI,KAAK,CAAC,kCAAkC,CAAC;QACvD,CAAC,MACI,IAAI2I,KAAK,CAACF,GAAG,CAAC,EAAE;UACjB,IAAI,CAAC5F,IAAI,CAACsD,IAAI,CAAC,IAAI9L,WAAW,CAACkO,MAAM,CAAC,CAAC;QAC3C,CAAC,MACI;UACD,IAAI,CAAC1F,IAAI,CAACsD,IAAI,CAAC,IAAI9L,WAAW,CAAC,IAAI,EAAEoO,GAAG,CAAC,CAAC;QAC9C;QACA,IAAI,CAAC7G,IAAI,CAACC,OAAO,CAACT,UAAU,CAAC;MACjC,CAAC,MACI;QACD,MAAMoH,GAAG,GAAGvN,YAAY;QACxB,IAAI,CAAC6G,OAAO,CAACxB,EAAE,CAAC;QAChB,IAAI,CAACqG,WAAW,CAAC6B,GAAG,CAAC;MACzB;IACJ;EACJ;EACA1E,2BAA2BA,CAAC5C,GAAG,EAAEC,uBAAuB,EAAEC,UAAU,EAAE;IAClE,IAAID,uBAAuB,EAAE;MACzB,IAAI,CAACW,OAAO,CAACZ,GAAG,CAAC;MACjB,IAAI,CAACU,IAAI,CAACC,OAAO,CAAC,IAAI,CAACJ,qBAAqB,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACM,MAAM,CAAC,+BAA+B,CAAC;IAChD;EACJ;EACA8F,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClB,WAAW,CAACpM,GAAG,CAAC;EACzB;EACA4M,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACR,WAAW,CAACnM,KAAK,CAAC;IACvB,MAAM,IAAIwF,KAAK,CAAC,IAAI,CAAC4I,UAAU,CAAC;EACpC;EACAlH,YAAYA,CAAA,EAAG;IACX,IAAIpB,EAAE,EAAEpB,CAAC;IACT,IAAI,CAACgD,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC9B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACjB,IAAI/B,EAAE,IAAIvD,KAAK,EAAE;MACbuD,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB;IACA,IAAI/B,EAAE,IAAItD,IAAI,EAAE;MACZsD,EAAE,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC;MACjB,IAAIjB,EAAE,IAAI1C,IAAI,IAAI0C,EAAE,IAAIjD,IAAI,EAAE;QAC1B,IAAI,CAACwL,aAAa,CAAC,CAAC;QACpB;MACJ;MACA,IAAIjP,QAAQ,CAAC0G,EAAE,CAAC,EAAE;QACd,IAAI,CAACyB,MAAM,CAAC,+BAA+B,CAAC;MAChD;MACAzB,EAAE,GAAGtD,IAAI;IACb;IACAkC,CAAC,GAAGvE,KAAK;IACT2F,EAAE,GAAG,IAAI,CAACwI,qBAAqB,CAACxI,EAAE,CAAC;IACnC,IAAIA,EAAE,IAAI1D,KAAK,EAAE;MACbsC,CAAC,GAAGnE,SAAS;MACbuF,EAAE,GAAG,IAAI,CAACyI,qBAAqB,CAAC,IAAI,CAAC1G,KAAK,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,CAAC3I,mBAAmB,CAAC4G,EAAE,CAAC,EAAE;MAC1B,IAAIA,EAAE,IAAI/C,IAAI,IAAI+C,EAAE,IAAIrD,IAAI,EAAE;QAC1BiC,CAAC,GAAGnE,SAAS;QACbuF,EAAE,GAAG,IAAI,CAAC0I,cAAc,CAAC,CAAC;MAC9B,CAAC,MACI,IAAI1I,EAAE,IAAI9C,IAAI,IAAI8C,EAAE,IAAIpD,IAAI,IAAIoD,EAAE,IAAI7C,IAAI,IAAI6C,EAAE,IAAInD,IAAI,EAAE;QAC3D+B,CAAC,GAAGrE,OAAO;QACXyF,EAAE,GAAG,IAAI,CAAC0I,cAAc,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,CAACtP,mBAAmB,CAAC4G,EAAE,CAAC,EAAE;MAC1B,IAAI,CAACyB,MAAM,CAAC,gCAAgC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACD,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAACzH,CAAC,CAAC;IACvB;EACJ;EACA2J,aAAaA,CAAA,EAAG;IACZ,IAAIvI,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB,IAAI/B,EAAE,IAAI1C,IAAI,IAAI0C,EAAE,IAAIjD,IAAI,EAAE;MAC1BiD,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB/B,EAAE,GAAG,IAAI,CAAC2I,yBAAyB,CAAC3I,EAAE,CAAC;IAC3C;IACA,IAAI5G,mBAAmB,CAAC4G,EAAE,CAAC,EAAE;MACzB,IAAI,CAACwB,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC/L,QAAQ,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAACmH,MAAM,CAAC,gCAAgC,CAAC;IACjD;EACJ;EACAiH,cAAcA,CAAA,EAAG;IACb,IAAI1I,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB,IAAI/B,EAAE,IAAIvD,KAAK,IAAIuD,EAAE,IAAIxD,KAAK,EAAE;MAC5BwD,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB;IACA/B,EAAE,GAAG,IAAI,CAACwI,qBAAqB,CAACxI,EAAE,CAAC;IACnC,OAAOA,EAAE;EACb;EACA4H,cAAcA,CAAA,EAAG;IACb,IAAI,CAAChG,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIvF,KAAK,EAAE;MACvB,IAAI,CAACoM,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACnH,MAAM,CAAC,eAAe,CAAC;IAChC;EACJ;EACAP,eAAeA,CAAA,EAAG;IACd,IAAI,CAACU,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,IAAItF,KAAK,EAAE;MACvB,IAAI,CAACmM,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACnH,MAAM,CAAC,eAAe,CAAC;IAChC;EACJ;EACAmH,gBAAgBA,CAAA,EAAG;IACf,IAAIC,EAAE,EAAE7I,EAAE;IACV,KAAK6I,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;MACvB7I,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI/B,EAAE,IAAIvB,GAAG,CAACoK,EAAE,CAAC,EAAE;QACf,IAAI,CAACpH,MAAM,CAAC,gBAAgB,CAAC;QAC7B;MACJ;IACJ;IACA,IAAIrI,mBAAmB,CAAC,IAAI,CAAC6H,KAAK,CAAC,CAAC,CAAC,EAAE;MACnC,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC7L,eAAe,CAAC;IACrC,CAAC,MACI;MACD,IAAI,CAACiH,MAAM,CAAC,wCAAwC,CAAC;IACzD;EACJ;EACAqG,cAAcA,CAAA,EAAG;IACb,IAAI,CAAClG,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC,IAAI9B,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IACjC,IAAI9I,EAAE,KAAKlD,IAAI,EAAE;MACb,IAAI,CAACsF,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIsF,EAAE,KAAKvD,KAAK,EAAE;MACnB,MAAM,IAAIiD,KAAK,CAAC,gDAAgD,CAAC;IACrE;IACAM,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IAC7B,IAAI9I,EAAE,KAAKlD,IAAI,EAAE;MACb,IAAI,CAACsF,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIsF,EAAE,KAAKvD,KAAK,EAAE;MACnB,MAAM,IAAIiD,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACAM,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IAC7B,IAAI1P,mBAAmB,CAAC4G,EAAE,CAAC,EAAE;MACzB,IAAI,CAACwB,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIsF,EAAE,KAAKlD,IAAI,EAAE;MAClB,MAAM,IAAI4C,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACA,MAAMqJ,QAAQ,GAAG,IAAI,CAAClH,GAAG,CAACmH,IAAI,CAAC,CAAC;IAChC,IAAI5P,mBAAmB,CAAC2P,QAAQ,CAAC,EAAE;MAC/B,IAAI,CAAC3G,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;MAC7B;IACJ,CAAC,MACI,IAAI,CAACpB,QAAQ,CAACyP,QAAQ,CAAC,EAAE;MAC1B,MAAM,IAAIrJ,KAAK,CAAC,kFAAkF,CAAC;IACvG;IACAM,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IAC7B,IAAI9I,EAAE,KAAK3D,KAAK,EAAE;MACd,MAAM,IAAIqD,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACAM,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IAC7B,IAAI9I,EAAE,KAAK3D,KAAK,EAAE;MACd2D,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;MAC7B,IAAI9I,EAAE,KAAK1D,KAAK,EAAE;QACd,IAAI,CAAChD,QAAQ,CAAC,IAAI,CAACyI,KAAK,CAAC,CAAC,CAAC,EAAE;UACzB,MAAM,IAAIrC,KAAK,CAAC,oEAAoE,CAAC;QACzF;QACA,OAAOpG,QAAQ,CAAE0G,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAE,CAAC,EAAE,CAAE;MAC5C;IACJ;IACA,IAAI/B,EAAE,KAAKhD,IAAI,EAAE;MACb,IAAI,CAAC5D,mBAAmB,CAAC,IAAI,CAAC6H,KAAK,CAAC,CAAC,CAAC,EAAE;QACpC,MAAM,IAAIvB,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACA,IAAI,CAAC0C,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIsF,EAAE,KAAKxD,KAAK,IAAIwD,EAAE,KAAKvD,KAAK,EAAE;MACnC,MAAM,IAAIiD,KAAK,CAAC,+BAA+B,CAAC;IACpD;IACAM,EAAE,GAAG,IAAI,CAAC8I,gBAAgB,CAAC,CAAC,CAAC;IAC7B,IAAI9I,EAAE,KAAK3D,KAAK,EAAE;MACd,MAAM,IAAIqD,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,IAAI,CAACuJ,YAAY,CAAC,CAAC,CAAC;IACpBjJ,EAAE,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC;IACjB,IAAI,CAAC7H,mBAAmB,CAAC4G,EAAE,CAAC,EAAE;MAC1B,MAAM,IAAIN,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAI,CAAC0C,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACuE,WAAW,CAAC3L,WAAW,CAAC;EACjC;EACAuM,YAAYA,CAAA,EAAG;IACX,IAAIjH,EAAE;IACN,IAAI,CAAC4B,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IACrC,SAAS;MACL9B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI,CAACrI,kBAAkB,CAACsG,EAAE,CAAC,EAAE;QACzB;MACJ;IACJ;IACA,IAAI,CAACwB,OAAO,CAACxB,EAAE,CAAC;IAChB,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACuE,WAAW,CAAC1L,YAAY,CAAC;EAClC;EACAwG,qBAAqBA,CAAA,EAAG;IACpB,IAAInB,EAAE;IACN,IAAI,CAAC4B,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC,SAAS;MACL9B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI,CAACpI,gBAAgB,CAACqG,EAAE,CAAC,EAAE;QACvB;MACJ;IACJ;IACA,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IACnC,IAAI,CAACN,OAAO,CAACxB,EAAE,CAAC;IAChB,IAAI,CAACqG,WAAW,CAACzL,UAAU,CAAC;EAChC;EACAkM,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACpF,mBAAmB,CAAC9F,KAAK,EAAE,KAAK,CAAC;IACtC,IAAI,CAACwG,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IACnC,IAAI,CAACuE,WAAW,CAACxL,SAAS,CAAC;EAC/B;EACAmM,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACtF,mBAAmB,CAAC7F,eAAe,EAAE,KAAK,CAAC;IAChD,IAAI,CAACuG,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;IACnC,IAAI,CAACuE,WAAW,CAACvL,SAAS,CAAC;EAC/B;EACAiM,aAAaA,CAACmC,iBAAiB,EAAE;IAC7B,IAAIA,iBAAiB,KAAK7H,SAAS,EAAE;MACjC6H,iBAAiB,GAAG,IAAI;IAC5B;IACA,IAAIlJ,EAAE;IACN,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACP,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5B,KAAK,IAAI,CAACW,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACb,KAAK,CAAC,KAAK,CAAC,KAAK/G,KAAK,EAAE,IAAI,CAAC2H,GAAG,CAACsH,MAAM,CAAC,IAAI,CAAC3C,sBAAsB,CAAC0C,iBAAiB,CAAC,CAAC,EAAE;MACtI,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAI,CAACrB,KAAK,CAAC,CAAC;MAChB;MACA,OAAO,IAAI,CAACd,KAAK,CAAC,KAAK,CAAC,KAAK/G,KAAK,EAAE;QAChC8F,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;QACjB,IAAI/B,EAAE,IAAIxE,KAAK,EAAE;UACb,IAAI,CAACwG,4BAA4B,CAAC,CAAC;QACvC;QACA,IAAIhC,EAAE,KAAK/F,GAAG,EAAE;UACZ,MAAM,IAAIyF,KAAK,CAAC,kCAAkC,CAAC;QACvD;QACA,IAAI,CAACU,oBAAoB,CAACJ,EAAE,EAAE,IAAI,CAAC,EAAE;UACjC,MAAM,IAAIN,KAAK,CAAC,oBAAoB,GAAGM,EAAE,GAAG,YAAY,CAAC;QAC7D;MACJ;MACA,IAAI,CAACoC,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MAC/B,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAI,CAACrB,KAAK,CAAC,CAAC;MAChB;IACJ;IACA,IAAI,CAACsE,WAAW,CAACtL,SAAS,CAAC;EAC/B;EACA0K,YAAYA,CAAC2D,UAAU,EAAE;IACrB,OAAQ,IAAI,CAACvH,GAAG,CAACkD,OAAO,CAACqE,UAAU,CAAC,KAAKxN,KAAK,IAC1C,IAAI,CAACiG,GAAG,CAACkD,OAAO,CAACqE,UAAU,GAAG,CAAC,CAAC,KAAKxN,KAAK,IAC1C,IAAI,CAACiG,GAAG,CAACkD,OAAO,CAACqE,UAAU,GAAG,CAAC,CAAC,KAAKxN,KAAK;EAClD;EACAoG,4BAA4BA,CAAA,EAAG;IAC3B,IAAIhC,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB,QAAQ/B,EAAE;MACN,KAAKzC,KAAK;MACV,KAAKC,KAAK;MACV,KAAKC,KAAK;MACV,KAAKC,KAAK;MACV,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,KAAK;MACV,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,MAAM;MACX,KAAKC,OAAO;QACR;MACJ,KAAKC,OAAO;QACR2B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;QACjB,IAAI/B,EAAE,IAAI5B,OAAO,EAAE;UACf,IAAI,CAACoD,OAAO,CAACxB,EAAE,CAAC;QACpB;QACA;MACJ,KAAK1B,KAAK;QACN0B,EAAE,GAAG,IAAI,CAACqJ,iBAAiB,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC7H,OAAO,CAACxB,EAAE,CAAC;QAChB;MACJ,KAAKzB,KAAK;QACNyB,EAAE,GAAG,IAAI,CAACqJ,iBAAiB,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC7H,OAAO,CAACxB,EAAE,CAAC;QAChB;MACJ,KAAKxB,KAAK;QACNwB,EAAE,GAAG,IAAI,CAACqJ,iBAAiB,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC7H,OAAO,CAACxB,EAAE,CAAC;QAChB;MACJ;QACI,IAAI,CAACyB,MAAM,CAAC,wBAAwB,GAAGzB,EAAE,GAAG,qBAAqB,CAAC;IAC1E;EACJ;EACA+H,0BAA0BA,CAAChH,EAAE,EAAE;IAC3B,IAAIwD,CAAC,EAAEvE,EAAE,EAAEsJ,MAAM;IACjB,MAAM1K,CAAC,GAAG,IAAI,CAACwH,UAAU,CAAC,CAAC;IAC3B,IAAIxH,CAAC,IAAI/D,SAAS,IAAI+D,CAAC,IAAI7D,SAAS,EAAE;MAClC,IAAI,CAAC0G,MAAM,CAAC,8BAA8B,CAAC;IAC/C;IACA8C,CAAC,GAAG,IAAI,CAACN,mBAAmB,CAACrF,CAAC,CAAC;IAC/BoB,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,IAAI,CAAC;IACtC,IAAIxG,EAAE,IAAI3D,KAAK,IAAI,IAAI,CAAC4E,KAAK,CAAC,CAAC,IAAI5E,KAAK,EAAE;MACtC,IAAI,CAAC0F,KAAK,CAAC,CAAC;MACZ,IAAI,CAACQ,IAAI,CAACsD,IAAI,CAAC,IAAI9L,WAAW,CAACwK,CAAC,CAAC,CAAC;MAClC+E,MAAM,GAAG,IAAI;IACjB,CAAC,MACI;MACD,IAAI,CAAC9H,OAAO,CAACxB,EAAE,CAAC;MAChB,IAAI,CAACqG,WAAW,CAACzH,CAAC,CAAC;MACnB0K,MAAM,GAAG,KAAK;IAClB;IACA,OAAOA,MAAM;EACjB;EACA5B,kBAAkBA,CAAA,EAAG;IACjB,IAAI9I,CAAC;IACL,IAAI,CAACoI,aAAa,CAAC,CAAC;IACpBpI,CAAC,GAAG,IAAI,CAACwH,UAAU,CAAC,CAAC;IACrB,IAAIxH,CAAC,IAAI9D,SAAS,EAAE;MAChB,IAAI,CAAC2G,MAAM,CAAC,iBAAiB,CAAC;IAClC;IACA,IAAI,CAAC4E,WAAW,CAACrL,OAAO,CAAC;IACzB,IAAI,CAACsG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACgI,wBAAwB,CAAC;EACpD;EACA9B,kBAAkBA,CAAA,EAAG;IACjB,IAAI7I,CAAC;IACL,IAAI,CAACmI,aAAa,CAAC,KAAK,CAAC;IACzBnI,CAAC,GAAG,IAAI,CAACwH,UAAU,CAAC,CAAC;IACrB,IAAIxH,CAAC,IAAI7D,SAAS,EAAE;MAChB,IAAI,CAAC0G,MAAM,CAAC,iBAAiB,CAAC;IAClC;IACA,IAAI,CAAC4E,WAAW,CAACpL,OAAO,CAAC;IACzB,IAAI,CAACqG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACgI,wBAAwB,CAAC;EACpD;EACA5B,UAAUA,CAAA,EAAG;IACT,IAAI3H,EAAE;MAAEwJ,YAAY,GAAG,CAAC;MAAEC,QAAQ,GAAG,CAAC;IACtC,IAAI,CAAC7H,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,QAAQ,CAAC,CAAC;IACjC,OAAO,IAAI,EAAE;MACT9B,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI1I,cAAc,CAAC2G,EAAE,CAAC,EAAE;QACpBwJ,YAAY,EAAE;QACd,IAAI,CAACpH,IAAI,GAAG,IAAI,CAACP,GAAG,CAACC,QAAQ,CAAC,CAAC;MACnC,CAAC,MACI,IAAI,CAAClI,aAAa,CAACoG,EAAE,CAAC,EAAE;QACzB;MACJ;IACJ;IACA,OAAOA,EAAE,IAAIzD,KAAK,EAAE;MAChBkN,QAAQ,EAAE;MACVzJ,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,KAAK,CAAC;IAC3C;IACA,IAAIxG,EAAE,IAAI9D,KAAK,IAAI,IAAI,CAAC6F,KAAK,CAAC,CAAC,IAAI7F,KAAK,EAAE;MACtC,MAAM,IAAIwD,KAAK,CAAC,cAAc,CAAC;IACnC;IACA,IAAI,CAACO,sBAAsB,CAACuJ,YAAY,EAAEC,QAAQ,CAAC,EAAE;MACjD,MAAM,IAAI/J,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IACA,IAAI,CAAC2G,WAAW,CAACnL,MAAM,CAAC;EAC5B;EACAqO,wBAAwBA,CAAA,EAAG;IACvB,MAAMvJ,EAAE,GAAG,IAAI,CAACwG,sBAAsB,CAAC,KAAK,CAAC;IAC7C,IAAIxG,EAAE,IAAI9D,KAAK,IAAI,IAAI,CAAC6F,KAAK,CAAC,CAAC,IAAI7F,KAAK,EAAE;MACtC,IAAI,CAACuF,MAAM,CAAC,eAAe,CAAC;IAChC;EACJ;EACAyD,eAAeA,CAAClF,EAAE,EAAE;IAChB,OAAOA,EAAE,IAAI,MAAM,IAAIA,EAAE,IAAI,MAAM;EACvC;EACAoF,cAAcA,CAACpF,EAAE,EAAE;IACf,OAAOA,EAAE,IAAI,MAAM,IAAIA,EAAE,IAAI,MAAM;EACvC;EACA0J,eAAeA,CAAC7E,KAAK,EAAE8E,cAAc,EAAE;IACnC,IAAI3J,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;IAChC,IAAI,CAAC8E,cAAc,EAAE;MACjB,OAAO/P,aAAa,CAACoG,EAAE,CAAC,EAAEA,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,EAAE,CAAC,EAAE,CAAE;IAChE,CAAC,MACI;MACD,OAAOjL,aAAa,CAACoG,EAAE,CAAC,IAAIA,EAAE,KAAKvE,gBAAgB,EAAEuE,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,EAAE,CAAC,EAAE;QACjF,IAAI7E,EAAE,KAAKvE,gBAAgB,EAAE;UACzBuE,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,EAAE,CAAC;UAC9B,QAAQ7E,EAAE;YACN,KAAKvE,gBAAgB;cACjBoJ,KAAK,GAAG,IAAI,CAAC+E,cAAc,CAAC/E,KAAK,CAAC;cAClC;YACJ,KAAKlJ,KAAK;cACNkJ,KAAK,GAAG,IAAI,CAACgF,mBAAmB,CAAChF,KAAK,CAAC;cACvC;YACJ;cACIA,KAAK,EAAE;cACP;UACR;QACJ;MACJ;IACJ;IACA,OAAOA,KAAK;EAChB;EACA+E,cAAcA,CAAC/E,KAAK,EAAE;IAClB,IAAI7E,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC;IAChC,OAAO7E,EAAE,KAAK/F,GAAG,IAAI+F,EAAE,KAAKzE,KAAK,EAAE;MAC/B,IAAIyE,EAAE,KAAK1E,KAAK,EAAE;QACd,IAAI,IAAI,CAACuG,GAAG,CAACkD,OAAO,CAACF,KAAK,GAAG,CAAC,CAAC,KAAKtJ,KAAK,EAAE;UACvC,OAAOsJ,KAAK;QAChB;MACJ;MACA7E,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAACF,KAAK,EAAE,CAAC;IAClC;IACA,OAAOA,KAAK;EAChB;EACAgF,mBAAmBA,CAAChF,KAAK,EAAE;IACvB,OAAO,IAAI,CAAChD,GAAG,CAACkD,OAAO,CAACF,KAAK,CAAC,KAAKlJ,KAAK,IACpC,IAAI,CAACkG,GAAG,CAACkD,OAAO,CAACF,KAAK,GAAG,CAAC,CAAC,KAAKpJ,gBAAgB,EAAE;MAClDoJ,KAAK,EAAE;IACX;IACA,OAAOA,KAAK;EAChB;EACAa,sBAAsBA,CAAC0D,UAAU,EAAEU,GAAG,EAAEH,cAAc,EAAE;IACpD,IAAII,SAAS,GAAGX,UAAU,GAAG,CAAC;IAC9BW,SAAS,GAAG,IAAI,CAACL,eAAe,CAACK,SAAS,EAAEJ,cAAc,CAAC;IAC3D,IAAII,SAAS,GAAG,CAAC,IAAID,GAAG,IAAI,IAAI,CAACrE,YAAY,CAACsE,SAAS,CAAC,EAAE;MACtD,OAAOA,SAAS,GAAG,CAAC;IACxB,CAAC,MACI;MACD,OAAOA,SAAS,GAAG,CAAC;IACxB;EACJ;EACAjE,eAAeA,CAAC+C,EAAE,EAAEiB,GAAG,EAAE;IACrB,IAAI9J,EAAE;IACN,IAAI6I,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;MACf,MAAM,IAAIpK,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACAM,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAAC8D,EAAE,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACxG,QAAQ,GAAG,CAAC;IACjB,QAAQrC,EAAE;MACN,KAAKzC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,KAAK;QACN,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,OAAO;QACR,OAAO,CAAC,CAAC;MACb,KAAKC,OAAO;QACR,IAAIwK,EAAE,GAAG,CAAC,GAAGiB,GAAG,IAAI,IAAI,CAACjI,GAAG,CAACkD,OAAO,CAAC8D,EAAE,GAAG,CAAC,CAAC,IAAItN,KAAK,EAAE;UACnD,IAAI,CAAC8G,QAAQ,GAAG,CAAC;QACrB;QACA,OAAOlJ,eAAe;MAC1B,KAAKmF,KAAK;QACN,IAAIuK,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;UACf,MAAM,IAAIpK,KAAK,CAAC,yBAAyB,CAAC;QAC9C;QACAM,EAAE,GAAG,IAAI,CAACgK,gBAAgB,CAACnB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC;QAC1C,IAAI,CAACxG,QAAQ,GAAG,CAAC;QACjB;MACJ;QACI,MAAM,IAAI3C,KAAK,CAAC,mBAAmB,GAAGM,EAAE,CAAC;IACjD;IACA,OAAOA,EAAE;EACb;EACAiF,qBAAqBA,CAAC4D,EAAE,EAAEiB,GAAG,EAAE;IAC3B,IAAI9J,EAAE;IACN,IAAI6I,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;MACf,MAAM,IAAIpK,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACAM,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAAC8D,EAAE,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACxG,QAAQ,GAAG,CAAC;IACjB,QAAQrC,EAAE;MACN,KAAKzC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,KAAK;QACN,OAAO,CAAC;MACZ,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,KAAK;QACN,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,MAAM;QACP,OAAO,EAAE;MACb,KAAKC,OAAO;QACR,OAAO,CAAC,CAAC;MACb,KAAKC,OAAO;QACR,IAAIwK,EAAE,GAAG,CAAC,GAAGiB,GAAG,IAAI,IAAI,CAACjI,GAAG,CAACkD,OAAO,CAAC8D,EAAE,GAAG,CAAC,CAAC,IAAItN,KAAK,EAAE;UACnD,IAAI,CAAC8G,QAAQ,GAAG,CAAC;QACrB;QACA,OAAOlJ,eAAe;MAC1B,KAAKmF,KAAK;QACN,IAAIuK,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;UACf,MAAM,IAAIpK,KAAK,CAAC,yBAAyB,CAAC;QAC9C;QACAM,EAAE,GAAG,IAAI,CAACgK,gBAAgB,CAACnB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC;QAC1C,IAAI,CAACxG,QAAQ,GAAG,CAAC;QACjB;MACJ,KAAK9D,KAAK;QACN,IAAIsK,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;UACf,MAAM,IAAIpK,KAAK,CAAC,yBAAyB,CAAC;QAC9C;QACAM,EAAE,GAAG,IAAI,CAACgK,gBAAgB,CAACnB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC;QAC1C,IAAI,CAACxG,QAAQ,GAAG,CAAC;QACjB;MACJ,KAAK7D,KAAK;QACN,IAAIqK,EAAE,GAAG,CAAC,IAAIiB,GAAG,EAAE;UACf,MAAM,IAAIpK,KAAK,CAAC,yBAAyB,CAAC;QAC9C;QACAM,EAAE,GAAG,IAAI,CAACgK,gBAAgB,CAACnB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,EAAE,CAAC;QAC3C,IAAI,CAACxG,QAAQ,GAAG,CAAC;QACjB;MACJ;QACI,MAAM,IAAI3C,KAAK,CAAC,yCAAyC,CAAC;IAClE;IACA,OAAOM,EAAE;EACb;EACAgK,gBAAgBA,CAACnB,EAAE,EAAEiB,GAAG,EAAE;IACtB,IAAI9J,EAAE;MAAEiK,CAAC,GAAG,CAAC;IACb,OAAOpB,EAAE,GAAGiB,GAAG,EAAE;MACb9J,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACkD,OAAO,CAAC8D,EAAE,CAAC;MACzBoB,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAGlK,aAAa,CAACC,EAAE,CAAC;MAC9B6I,EAAE,EAAE;IACR;IACA,OAAOoB,CAAC;EACZ;EACA5D,WAAWA,CAACzH,CAAC,EAAE;IACX,IAAI,IAAI,CAACsD,WAAW,KAAKhI,KAAK,EAAE;MAC5B,IAAI,CAACuH,MAAM,CAAC,uCAAuC,CAAC;IACxD;IACA,IAAI,CAACS,WAAW,GAAGtD,CAAC;EACxB;EACAwH,UAAUA,CAAA,EAAG;IACT,MAAMxH,CAAC,GAAG,IAAI,CAACsD,WAAW;IAC1B,IAAI,CAACA,WAAW,GAAGhI,KAAK;IACxB,OAAO0E,CAAC;EACZ;EACAuH,IAAIA,CAAA,EAAG;IACH,IAAIpF,EAAE;IACN,OAAO,IAAI,CAACO,IAAI,CAAC+B,MAAM,GAAG,CAAC,IAAI,IAAI,CAACnB,WAAW,KAAKhI,KAAK,EAAE;MACvD6G,EAAE,GAAG,IAAI,CAACO,IAAI,CAAC4I,KAAK,CAAC,CAAC;MACtBnJ,EAAE,CAACmG,IAAI,CAAC,IAAI,CAAC;IACjB;EACJ;EACAnF,KAAKA,CAAA,EAAG;IACJ,MAAM/B,EAAE,GAAG,IAAI,CAAC6B,GAAG,CAACqE,IAAI,CAAC,CAAC;IAC1B,OAAOlG,EAAE;EACb;EACAmK,uBAAuBA,CAAA,EAAG;IACtB,IAAInK,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB,IAAI/B,EAAE,IAAIvE,gBAAgB,EAAE;MACxBuE,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI/B,EAAE,IAAIvE,gBAAgB,EAAE;QACxB,IAAI,CAAC2O,gBAAgB,CAAC,CAAC;QACvBpK,EAAE,GAAGnG,mBAAmB;MAC5B,CAAC,MACI,IAAImG,EAAE,IAAIrE,KAAK,EAAE;QAClB,IAAI,CAAC0O,sBAAsB,CAAC,CAAC;QAC7BrK,EAAE,GAAGlG,mBAAmB;MAC5B,CAAC,MACI;QACD,IAAI,CAAC0H,OAAO,CAACxB,EAAE,CAAC;QAChBA,EAAE,GAAGvE,gBAAgB;MACzB;IACJ;IACA,OAAOuE,EAAE;EACb;EACAoK,gBAAgBA,CAAA,EAAG;IACf,IAAIpK,EAAE;IACN,SAAS;MACLA,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI/B,EAAE,IAAI/F,GAAG,EAAE;QACX;MACJ;MACA,IAAI+F,EAAE,IAAIzE,KAAK,EAAE;QACb;MACJ;MACA,IAAIyE,EAAE,IAAI1E,KAAK,EAAE;QACb0E,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;QACjB,IAAI/B,EAAE,IAAIzE,KAAK,EAAE;UACb,IAAI,CAACiG,OAAO,CAACxB,EAAE,CAAC;QACpB;QACA;MACJ;IACJ;EACJ;EACAqK,sBAAsBA,CAAA,EAAG;IACrB,IAAIrK,EAAE;IACN,SAAS;MACLA,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI/B,EAAE,IAAI/F,GAAG,EAAE;QACX;MACJ;MACA,IAAI+F,EAAE,IAAIrE,KAAK,EAAE;QACbqE,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;QACjB,IAAI/B,EAAE,IAAIvE,gBAAgB,EAAE;UACxB;QACJ;MACJ;IACJ;EACJ;EACA+F,OAAOA,CAACxB,EAAE,EAAE;IACR,IAAI,CAAC6B,GAAG,CAACsH,MAAM,CAACnJ,EAAE,CAAC;EACvB;EACAwG,sBAAsBA,CAAC8D,kBAAkB,EAAE;IACvC,IAAItK,EAAE;IACN,IAAIsK,kBAAkB,EAAE;MACpBtK,EAAE,GAAG,IAAI,CAACmK,uBAAuB,CAAC,CAAC;MACnC,OAAOvQ,aAAa,CAACoG,EAAE,CAAC,EAAE;QACtBA,EAAE,GAAG,IAAI,CAACmK,uBAAuB,CAAC,CAAC;MACvC;IACJ,CAAC,MACI;MACDnK,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,OAAOnI,aAAa,CAACoG,EAAE,CAAC,EAAE;QACtBA,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACrB;IACJ;IACA,OAAO/B,EAAE;EACb;EACAiB,KAAKA,CAACsJ,QAAQ,EAAE;IACZ,IAAIvK,EAAE;MAAE6I,EAAE,GAAG,CAAC;IACd,IAAI0B,QAAQ,KAAKlJ,SAAS,IAAIkJ,QAAQ,CAAClH,MAAM,GAAG,CAAC,EAAE;MAC/C,OAAO,IAAI,CAACxB,GAAG,CAACkD,OAAO,CAAC,IAAI,CAAClD,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;IAChD;IACA,OAAO+G,EAAE,GAAG0B,QAAQ,CAAClH,MAAM,EAAE;MACzBrD,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI/B,EAAE,IAAIuK,QAAQ,CAAC7O,UAAU,CAACmN,EAAE,CAAC,EAAE;QAC/B;MACJ;MACAA,EAAE,EAAE;IACR;IACA,IAAIA,EAAE,KAAK0B,QAAQ,CAAClH,MAAM,EAAE;MACxBrD,EAAE,GAAG,IAAI,CAACiB,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACD,IAAI,CAACO,OAAO,CAACxB,EAAE,CAAC;MAChBA,EAAE,GAAG9F,KAAK;IACd;IACA,OAAO2O,EAAE,GAAG,CAAC,EAAE;MACXA,EAAE,EAAE;MACJ,IAAI,CAACrH,OAAO,CAAC+I,QAAQ,CAAC7O,UAAU,CAACmN,EAAE,CAAC,CAAC;IACzC;IACA,OAAO7I,EAAE;EACb;EACA6H,cAAcA,CAACjH,GAAG,EAAE;IAChB,IAAIiI,EAAE;MAAE7I,EAAE;MAAEwK,SAAS,GAAG,IAAI;IAC5B,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAI,CAACnR,QAAQ,CAACsH,GAAG,CAAC,EAAE;MAChB,OAAO1G,KAAK;IAChB;IACA,KAAK2O,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;MACvB7I,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB0I,KAAK,CAAC5E,IAAI,CAAC7F,EAAE,CAAC;MACd,IAAI,CAAC1G,QAAQ,CAAC0G,EAAE,CAAC,EAAE;QACfwK,SAAS,GAAG,KAAK;QACjB;MACJ;IACJ;IACAxK,EAAE,GAAGwK,SAAS,IAAI3B,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC5H,KAAK,CAAC,CAAC,GAAG/G,KAAK;IAChD,OAAOuQ,KAAK,CAACpH,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAAC7B,OAAO,CAACiJ,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC;IAC7B;IACA,OAAO1K,EAAE;EACb;EACAwI,qBAAqBA,CAACxI,EAAE,EAAE;IACtB,IAAI,CAAC1G,QAAQ,CAAC0G,EAAE,CAAC,EAAE;MACf,OAAO9F,KAAK;IAChB;IACA,SAAS;MACL8F,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI,CAACzI,QAAQ,CAAC0G,EAAE,CAAC,EAAE;QACf;MACJ;IACJ;IACA,OAAOA,EAAE;EACb;EACAyI,qBAAqBA,CAACzI,EAAE,EAAE;IACtB,OAAO1G,QAAQ,CAAC0G,EAAE,CAAC,EAAE;MACjBA,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACrB;IACA,OAAO/B,EAAE;EACb;EACAiJ,YAAYA,CAAC0B,CAAC,EAAE;IACZ,IAAI3K,EAAE;IACN,IAAI2K,CAAC,IAAI,CAAC,EAAE;MACR,MAAM,IAAIjL,KAAK,CAAC,qDAAqD,CAAC;IAC1E;IACA,OAAOiL,CAAC,EAAE,EAAE;MACR,IAAI,CAACrR,QAAQ,CAAE0G,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAE,CAAC,EAAE;QAChC,MAAM,IAAIrC,KAAK,CAAC,uBAAuB,GAAGC,MAAM,CAACmF,YAAY,CAAC9E,EAAE,CAAC,CAAC;MACtE;IACJ;IACA,OAAOA,EAAE;EACb;EACA8I,gBAAgBA,CAAC6B,CAAC,EAAE;IAChB,IAAI,CAAC1B,YAAY,CAAC0B,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC5I,KAAK,CAAC,CAAC;EACvB;EACA4G,yBAAyBA,CAAC3I,EAAE,EAAE;IAC1B,IAAI,CAACzG,YAAY,CAACyG,EAAE,CAAC,EAAE;MACnB,OAAO9F,KAAK;IAChB;IACA,SAAS;MACL8F,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI,CAACxI,YAAY,CAACyG,EAAE,CAAC,EAAE;QACnB;MACJ;IACJ;IACA,OAAOA,EAAE;EACb;EACAqJ,iBAAiBA,CAACsB,CAAC,EAAE;IACjB,IAAI3K,EAAE;MAAE6I,EAAE,GAAG,CAAC;IACd,OAAOA,EAAE,GAAG8B,CAAC,EAAE;MACX3K,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAC,CAAC;MACjB,IAAI,CAACxI,YAAY,CAACyG,EAAE,CAAC,EAAE;QACnB,IAAI,CAACyB,MAAM,CAAC,EAAE,GAAGkJ,CAAC,GAAG,mBAAmB,GAAG9B,EAAE,GAAG,QAAQ,CAAC;QACzD,OAAO3O,KAAK;MAChB;MACA2O,EAAE,EAAE;IACR;IACA,OAAO7I,EAAE;EACb;EACAoI,cAAcA,CAAC7D,CAAC,EAAE;IACd,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACd,OAAOI,GAAG;IACd;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,CAAC,CAAClB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/B,IAAImB,CAAC,CAACnB,CAAC,CAAC,GAAG,GAAG,IAAImB,CAAC,CAACnB,CAAC,CAAC,GAAG,GAAG,EAAE;QAC1B,OAAOuB,GAAG;MACd;IACJ;IACA,OAAOiG,QAAQ,CAACrG,CAAC,CAACsG,MAAM,CAAC,CAAC,EAAEtG,CAAC,CAAClB,MAAM,CAAC,CAAC;EAC1C;EACA5B,MAAMA,CAACqJ,GAAG,EAAE;IACR,IAAI,CAACxJ,IAAI,CAACC,OAAO,CAAC,IAAI,CAACsF,gBAAgB,CAAC;IACxC,IAAI,CAACyB,UAAU,GAAGwC,GAAG;EACzB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}