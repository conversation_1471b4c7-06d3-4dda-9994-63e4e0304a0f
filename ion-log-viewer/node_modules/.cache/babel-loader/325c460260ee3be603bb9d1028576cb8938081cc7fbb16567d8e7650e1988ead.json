{"ast": null, "code": "import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\n\n// ION Value type handlers\nexport class IonValueHandler {\n  static handleValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle different ION types using DOM API\n    if (value && typeof value === 'object' && value.getType) {\n      const ionType = value.getType();\n      if (ionType === ion.IonTypes.NULL) {\n        return null;\n      }\n      if (ionType === ion.IonTypes.BLOB) {\n        return new Uint8Array(value.uInt8ArrayValue());\n      }\n      if (ionType === ion.IonTypes.CLOB) {\n        return value.stringValue();\n      }\n      if (ionType === ion.IonTypes.TIMESTAMP) {\n        return value.timestampValue().getTime();\n      }\n      if (ionType === ion.IonTypes.LIST) {\n        return value.elements().map(item => this.handleValue(item));\n      }\n      if (ionType === ion.IonTypes.STRUCT) {\n        const result = {};\n        for (const field of value.fields()) {\n          result[field.fieldName()] = this.handleValue(field);\n        }\n        return result;\n      }\n\n      // For primitive types, get the native value\n      return value.value();\n    }\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n    if (typeof value === 'object') {\n      const result = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n    return value;\n  }\n  static isHumanReadable(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check ION types\n    if (value && typeof value === 'object' && value.getType) {\n      const ionType = value.getType();\n      if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n      if (ionType === ion.IonTypes.CLOB) return true;\n      if (ionType === ion.IonTypes.TIMESTAMP) return true;\n      if (ionType === ion.IonTypes.LIST) {\n        return value.elements().every(item => this.isHumanReadable(item));\n      }\n      if (ionType === ion.IonTypes.STRUCT) {\n        return value.fields().every(field => this.isHumanReadable(field));\n      }\n    }\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data) {\n  try {\n    // Parse the ION data\n    const parsedData = ion.load(data);\n    if (!parsedData || typeof parsedData !== 'object') {\n      throw new Error('Invalid ION log format: root must be a struct');\n    }\n\n    // Extract session information\n    const sessionInfo = extractSessionInfo(parsedData);\n\n    // Extract robot information\n    const robotInfo = extractRobotInfo(parsedData);\n\n    // Extract topics\n    const topics = extractTopics(parsedData);\n\n    // Calculate time bounds\n    const {\n      startTime,\n      endTime,\n      totalDuration\n    } = calculateTimeBounds(topics);\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: parsedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    throw new Error(`Failed to parse ION log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\nfunction extractSessionInfo(data) {\n  const sessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n  return sessionInfo;\n}\nfunction extractRobotInfo(data) {\n  const robotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n  return robotInfo;\n}\nfunction extractTopics(data) {\n  const topics = [];\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n    const messages = [];\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 || messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n  return topics;\n}\nfunction calculateTimeBounds(topics) {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n  const totalDuration = endTime - startTime;\n  return {\n    startTime,\n    endTime,\n    totalDuration\n  };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages, targetTime) {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages, targetTime) {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}", "map": {"version": 3, "names": ["ion", "IonValueHandler", "handleValue", "value", "undefined", "getType", "ionType", "IonTypes", "NULL", "BLOB", "Uint8Array", "uInt8ArrayValue", "CLOB", "stringValue", "TIMESTAMP", "timestampValue", "getTime", "LIST", "elements", "map", "item", "STRUCT", "result", "field", "fields", "fieldName", "Array", "isArray", "key", "val", "Object", "entries", "isHumanReadable", "every", "values", "parseIonLog", "data", "parsedData", "load", "Error", "sessionInfo", "extractSessionInfo", "robotInfo", "extractRobotInfo", "topics", "extractTopics", "startTime", "endTime", "totalDuration", "calculateTimeBounds", "metadata", "error", "message", "session", "assign", "duration", "recordingDate", "version", "robot", "robotModel", "model", "robotName", "name", "robotVersion", "botModel", "topicData", "topic", "type", "messages", "msgData", "push", "timestamp", "publishTime", "content", "sort", "a", "b", "length", "msg", "frequency", "Infinity", "findMessageAtTime", "targetTime", "left", "right", "closest", "mid", "Math", "floor", "abs", "getMessagesUpToTime", "filter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts"], "sourcesContent": ["import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\nexport interface IonMessage {\n  timestamp: number;\n  publishTime?: number;\n  content: any;\n}\n\nexport interface IonTopic {\n  name: string;\n  type: string;\n  frequency?: number;\n  messages: IonMessage[];\n  isHumanReadable: boolean;\n}\n\nexport interface IonSessionInfo {\n  startTime?: number;\n  endTime?: number;\n  duration?: number;\n  recordingDate?: string;\n  version?: string;\n  [key: string]: any;\n}\n\nexport interface IonRobotInfo {\n  model?: string;\n  name?: string;\n  version?: string;\n  botModel?: Uint8Array; // 3D model data\n  [key: string]: any;\n}\n\nexport interface IonLogData {\n  sessionInfo: IonSessionInfo;\n  robotInfo: IonRobotInfo;\n  topics: IonTopic[];\n  metadata: any;\n  totalDuration: number;\n  startTime: number;\n  endTime: number;\n}\n\n// ION Value type handlers\nexport class IonValueHandler {\n  static handleValue(value: any): any {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle different ION types using DOM API\n    if (value && typeof value === 'object' && value.getType) {\n      const ionType = value.getType();\n\n      if (ionType === ion.IonTypes.NULL) {\n        return null;\n      }\n\n      if (ionType === ion.IonTypes.BLOB) {\n        return new Uint8Array(value.uInt8ArrayValue());\n      }\n\n      if (ionType === ion.IonTypes.CLOB) {\n        return value.stringValue();\n      }\n\n      if (ionType === ion.IonTypes.TIMESTAMP) {\n        return value.timestampValue().getTime();\n      }\n\n      if (ionType === ion.IonTypes.LIST) {\n        return value.elements().map((item: any) => this.handleValue(item));\n      }\n\n      if (ionType === ion.IonTypes.STRUCT) {\n        const result: any = {};\n        for (const field of value.fields()) {\n          result[field.fieldName()] = this.handleValue(field);\n        }\n        return result;\n      }\n\n      // For primitive types, get the native value\n      return value.value();\n    }\n\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n\n    if (typeof value === 'object') {\n      const result: any = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n\n    return value;\n  }\n\n  static isHumanReadable(value: any): boolean {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check ION types\n    if (value && typeof value === 'object' && value.getType) {\n      const ionType = value.getType();\n      if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n      if (ionType === ion.IonTypes.CLOB) return true;\n      if (ionType === ion.IonTypes.TIMESTAMP) return true;\n\n      if (ionType === ion.IonTypes.LIST) {\n        return value.elements().every((item: any) => this.isHumanReadable(item));\n      }\n\n      if (ionType === ion.IonTypes.STRUCT) {\n        return value.fields().every((field: any) => this.isHumanReadable(field));\n      }\n    }\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data: Uint8Array): Promise<IonLogData> {\n  try {\n    // Parse the ION data\n    const parsedData = ion.load(data);\n\n    if (!parsedData || typeof parsedData !== 'object') {\n      throw new Error('Invalid ION log format: root must be a struct');\n    }\n\n    // Extract session information\n    const sessionInfo: IonSessionInfo = extractSessionInfo(parsedData);\n\n    // Extract robot information\n    const robotInfo: IonRobotInfo = extractRobotInfo(parsedData);\n\n    // Extract topics\n    const topics: IonTopic[] = extractTopics(parsedData);\n\n    // Calculate time bounds\n    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);\n\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: parsedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    throw new Error(`Failed to parse ION log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\nfunction extractSessionInfo(data: any): IonSessionInfo {\n  const sessionInfo: IonSessionInfo = {};\n\n  // Look for session-related fields\n  if (data.session) {\n    Object.assign(sessionInfo, IonValueHandler.handleValue(data.session));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  return sessionInfo;\n}\n\nfunction extractRobotInfo(data: any): IonRobotInfo {\n  const robotInfo: IonRobotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, IonValueHandler.handleValue(data.robot));\n  }\n\n  if (data.metadata) {\n    const metadata = IonValueHandler.handleValue(data.metadata);\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n\n  return robotInfo;\n}\n\nfunction extractTopics(data: any): IonTopic[] {\n  const topics: IonTopic[] = [];\n\n  if (!data.topics || !Array.isArray(data.topics)) {\n    return topics;\n  }\n\n  for (const topicData of data.topics) {\n    const topic = IonValueHandler.handleValue(topicData);\n\n    if (!topic.name || !topic.type) {\n      continue; // Skip invalid topics\n    }\n\n    const messages: IonMessage[] = [];\n\n    if (topic.messages && Array.isArray(topic.messages)) {\n      for (const msgData of topic.messages) {\n        const message = IonValueHandler.handleValue(msgData);\n        messages.push({\n          timestamp: message.timestamp || 0,\n          publishTime: message.publishTime,\n          content: message.content || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 ||\n      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n\n    topics.push({\n      name: topic.name,\n      type: topic.type,\n      frequency: topic.frequency,\n      messages,\n      isHumanReadable\n    });\n  }\n\n  return topics;\n}\n\nfunction calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n\n  const totalDuration = endTime - startTime;\n\n  return { startTime, endTime, totalDuration };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;;AAE7B;;AA0CA;AACA,OAAO,MAAMC,eAAe,CAAC;EAC3B,OAAOC,WAAWA,CAACC,KAAU,EAAO;IAClC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;;IAEA;IACA,IAAID,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,OAAO,EAAE;MACvD,MAAMC,OAAO,GAAGH,KAAK,CAACE,OAAO,CAAC,CAAC;MAE/B,IAAIC,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACC,IAAI,EAAE;QACjC,OAAO,IAAI;MACb;MAEA,IAAIF,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACE,IAAI,EAAE;QACjC,OAAO,IAAIC,UAAU,CAACP,KAAK,CAACQ,eAAe,CAAC,CAAC,CAAC;MAChD;MAEA,IAAIL,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACK,IAAI,EAAE;QACjC,OAAOT,KAAK,CAACU,WAAW,CAAC,CAAC;MAC5B;MAEA,IAAIP,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACO,SAAS,EAAE;QACtC,OAAOX,KAAK,CAACY,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACzC;MAEA,IAAIV,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACU,IAAI,EAAE;QACjC,OAAOd,KAAK,CAACe,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAS,IAAK,IAAI,CAAClB,WAAW,CAACkB,IAAI,CAAC,CAAC;MACpE;MAEA,IAAId,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACc,MAAM,EAAE;QACnC,MAAMC,MAAW,GAAG,CAAC,CAAC;QACtB,KAAK,MAAMC,KAAK,IAAIpB,KAAK,CAACqB,MAAM,CAAC,CAAC,EAAE;UAClCF,MAAM,CAACC,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACvB,WAAW,CAACqB,KAAK,CAAC;QACrD;QACA,OAAOD,MAAM;MACf;;MAEA;MACA,OAAOnB,KAAK,CAACA,KAAK,CAAC,CAAC;IACtB;IAEA,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxF,OAAOA,KAAK;IACd;IAEA,IAAIuB,KAAK,CAACC,OAAO,CAACxB,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACgB,GAAG,CAACC,IAAI,IAAI,IAAI,CAAClB,WAAW,CAACkB,IAAI,CAAC,CAAC;IAClD;IAEA,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMmB,MAAW,GAAG,CAAC,CAAC;MACtB,KAAK,MAAM,CAACM,GAAG,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC5B,KAAK,CAAC,EAAE;QAC9CmB,MAAM,CAACM,GAAG,CAAC,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,GAAG,CAAC;MACrC;MACA,OAAOP,MAAM;IACf;IAEA,OAAOnB,KAAK;EACd;EAEA,OAAO6B,eAAeA,CAAC7B,KAAU,EAAW;IAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE,OAAO,IAAI;IACtD,IAAI,OAAOD,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErG;IACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,OAAO,EAAE;MACvD,MAAMC,OAAO,GAAGH,KAAK,CAACE,OAAO,CAAC,CAAC;MAC/B,IAAIC,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACE,IAAI,EAAE,OAAO,KAAK,CAAC,CAAC;MACjD,IAAIH,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACK,IAAI,EAAE,OAAO,IAAI;MAC9C,IAAIN,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACO,SAAS,EAAE,OAAO,IAAI;MAEnD,IAAIR,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACU,IAAI,EAAE;QACjC,OAAOd,KAAK,CAACe,QAAQ,CAAC,CAAC,CAACe,KAAK,CAAEb,IAAS,IAAK,IAAI,CAACY,eAAe,CAACZ,IAAI,CAAC,CAAC;MAC1E;MAEA,IAAId,OAAO,KAAKN,GAAG,CAACO,QAAQ,CAACc,MAAM,EAAE;QACnC,OAAOlB,KAAK,CAACqB,MAAM,CAAC,CAAC,CAACS,KAAK,CAAEV,KAAU,IAAK,IAAI,CAACS,eAAe,CAACT,KAAK,CAAC,CAAC;MAC1E;IACF;IAEA,IAAIG,KAAK,CAACC,OAAO,CAACxB,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAAC8B,KAAK,CAACb,IAAI,IAAI,IAAI,CAACY,eAAe,CAACZ,IAAI,CAAC,CAAC;IACxD;IAEA,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO2B,MAAM,CAACI,MAAM,CAAC/B,KAAK,CAAC,CAAC8B,KAAK,CAACJ,GAAG,IAAI,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC,CAAC;IACrE;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,eAAeM,WAAWA,CAACC,IAAgB,EAAuB;EACvE,IAAI;IACF;IACA,MAAMC,UAAU,GAAGrC,GAAG,CAACsC,IAAI,CAACF,IAAI,CAAC;IAEjC,IAAI,CAACC,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjD,MAAM,IAAIE,KAAK,CAAC,+CAA+C,CAAC;IAClE;;IAEA;IACA,MAAMC,WAA2B,GAAGC,kBAAkB,CAACJ,UAAU,CAAC;;IAElE;IACA,MAAMK,SAAuB,GAAGC,gBAAgB,CAACN,UAAU,CAAC;;IAE5D;IACA,MAAMO,MAAkB,GAAGC,aAAa,CAACR,UAAU,CAAC;;IAEpD;IACA,MAAM;MAAES,SAAS;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGC,mBAAmB,CAACL,MAAM,CAAC;IAEzE,OAAO;MACLJ,WAAW;MACXE,SAAS;MACTE,MAAM;MACNM,QAAQ,EAAEb,UAAU;MACpBW,aAAa;MACbF,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC,OAAOI,KAAK,EAAE;IACd,MAAM,IAAIZ,KAAK,CAAC,4BAA4BY,KAAK,YAAYZ,KAAK,GAAGY,KAAK,CAACC,OAAO,GAAG,eAAe,EAAE,CAAC;EACzG;AACF;AAEA,SAASX,kBAAkBA,CAACL,IAAS,EAAkB;EACrD,MAAMI,WAA2B,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAIJ,IAAI,CAACiB,OAAO,EAAE;IAChBvB,MAAM,CAACwB,MAAM,CAACd,WAAW,EAAEvC,eAAe,CAACC,WAAW,CAACkC,IAAI,CAACiB,OAAO,CAAC,CAAC;EACvE;EAEA,IAAIjB,IAAI,CAACc,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGjD,eAAe,CAACC,WAAW,CAACkC,IAAI,CAACc,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACJ,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGI,QAAQ,CAACJ,SAAS;IAClE,IAAII,QAAQ,CAACH,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGG,QAAQ,CAACH,OAAO;IAC5D,IAAIG,QAAQ,CAACK,QAAQ,EAAEf,WAAW,CAACe,QAAQ,GAAGL,QAAQ,CAACK,QAAQ;IAC/D,IAAIL,QAAQ,CAACM,aAAa,EAAEhB,WAAW,CAACgB,aAAa,GAAGN,QAAQ,CAACM,aAAa;IAC9E,IAAIN,QAAQ,CAACO,OAAO,EAAEjB,WAAW,CAACiB,OAAO,GAAGP,QAAQ,CAACO,OAAO;EAC9D;EAEA,OAAOjB,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAACP,IAAS,EAAgB;EACjD,MAAMM,SAAuB,GAAG,CAAC,CAAC;;EAElC;EACA,IAAIN,IAAI,CAACsB,KAAK,EAAE;IACd5B,MAAM,CAACwB,MAAM,CAACZ,SAAS,EAAEzC,eAAe,CAACC,WAAW,CAACkC,IAAI,CAACsB,KAAK,CAAC,CAAC;EACnE;EAEA,IAAItB,IAAI,CAACc,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAGjD,eAAe,CAACC,WAAW,CAACkC,IAAI,CAACc,QAAQ,CAAC;IAC3D,IAAIA,QAAQ,CAACS,UAAU,EAAEjB,SAAS,CAACkB,KAAK,GAAGV,QAAQ,CAACS,UAAU;IAC9D,IAAIT,QAAQ,CAACW,SAAS,EAAEnB,SAAS,CAACoB,IAAI,GAAGZ,QAAQ,CAACW,SAAS;IAC3D,IAAIX,QAAQ,CAACa,YAAY,EAAErB,SAAS,CAACe,OAAO,GAAGP,QAAQ,CAACa,YAAY;IACpE,IAAIb,QAAQ,CAACc,QAAQ,EAAEtB,SAAS,CAACsB,QAAQ,GAAGd,QAAQ,CAACc,QAAQ;EAC/D;EAEA,OAAOtB,SAAS;AAClB;AAEA,SAASG,aAAaA,CAACT,IAAS,EAAc;EAC5C,MAAMQ,MAAkB,GAAG,EAAE;EAE7B,IAAI,CAACR,IAAI,CAACQ,MAAM,IAAI,CAAClB,KAAK,CAACC,OAAO,CAACS,IAAI,CAACQ,MAAM,CAAC,EAAE;IAC/C,OAAOA,MAAM;EACf;EAEA,KAAK,MAAMqB,SAAS,IAAI7B,IAAI,CAACQ,MAAM,EAAE;IACnC,MAAMsB,KAAK,GAAGjE,eAAe,CAACC,WAAW,CAAC+D,SAAS,CAAC;IAEpD,IAAI,CAACC,KAAK,CAACJ,IAAI,IAAI,CAACI,KAAK,CAACC,IAAI,EAAE;MAC9B,SAAS,CAAC;IACZ;IAEA,MAAMC,QAAsB,GAAG,EAAE;IAEjC,IAAIF,KAAK,CAACE,QAAQ,IAAI1C,KAAK,CAACC,OAAO,CAACuC,KAAK,CAACE,QAAQ,CAAC,EAAE;MACnD,KAAK,MAAMC,OAAO,IAAIH,KAAK,CAACE,QAAQ,EAAE;QACpC,MAAMhB,OAAO,GAAGnD,eAAe,CAACC,WAAW,CAACmE,OAAO,CAAC;QACpDD,QAAQ,CAACE,IAAI,CAAC;UACZC,SAAS,EAAEnB,OAAO,CAACmB,SAAS,IAAI,CAAC;UACjCC,WAAW,EAAEpB,OAAO,CAACoB,WAAW;UAChCC,OAAO,EAAErB,OAAO,CAACqB,OAAO,IAAIrB;QAC9B,CAAC,CAAC;MACJ;IACF;;IAEA;IACAgB,QAAQ,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,SAAS,GAAGK,CAAC,CAACL,SAAS,CAAC;;IAElD;IACA,MAAMvC,eAAe,GAAGoC,QAAQ,CAACS,MAAM,KAAK,CAAC,IAC3CT,QAAQ,CAACnC,KAAK,CAAC6C,GAAG,IAAI7E,eAAe,CAAC+B,eAAe,CAAC8C,GAAG,CAACL,OAAO,CAAC,CAAC;IAErE7B,MAAM,CAAC0B,IAAI,CAAC;MACVR,IAAI,EAAEI,KAAK,CAACJ,IAAI;MAChBK,IAAI,EAAED,KAAK,CAACC,IAAI;MAChBY,SAAS,EAAEb,KAAK,CAACa,SAAS;MAC1BX,QAAQ;MACRpC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOY,MAAM;AACf;AAEA,SAASK,mBAAmBA,CAACL,MAAkB,EAAiE;EAC9G,IAAIE,SAAS,GAAGkC,QAAQ;EACxB,IAAIjC,OAAO,GAAG,CAACiC,QAAQ;EAEvB,KAAK,MAAMd,KAAK,IAAItB,MAAM,EAAE;IAC1B,KAAK,MAAMQ,OAAO,IAAIc,KAAK,CAACE,QAAQ,EAAE;MACpC,IAAIhB,OAAO,CAACmB,SAAS,GAAGzB,SAAS,EAAE;QACjCA,SAAS,GAAGM,OAAO,CAACmB,SAAS;MAC/B;MACA,IAAInB,OAAO,CAACmB,SAAS,GAAGxB,OAAO,EAAE;QAC/BA,OAAO,GAAGK,OAAO,CAACmB,SAAS;MAC7B;IACF;EACF;EAEA,IAAIzB,SAAS,KAAKkC,QAAQ,EAAE;IAC1BlC,SAAS,GAAG,CAAC;IACbC,OAAO,GAAG,CAAC;EACb;EAEA,MAAMC,aAAa,GAAGD,OAAO,GAAGD,SAAS;EAEzC,OAAO;IAAEA,SAAS;IAAEC,OAAO;IAAEC;EAAc,CAAC;AAC9C;;AAEA;AACA,OAAO,SAASiC,iBAAiBA,CAACb,QAAsB,EAAEc,UAAkB,EAAqB;EAC/F,IAAId,QAAQ,CAACS,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEtC;EACA,IAAIM,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAGhB,QAAQ,CAACS,MAAM,GAAG,CAAC;EAC/B,IAAIQ,OAAO,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAEzB,OAAOe,IAAI,IAAIC,KAAK,EAAE;IACpB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC1C,MAAMhC,OAAO,GAAGgB,QAAQ,CAACkB,GAAG,CAAC;IAE7B,IAAIC,IAAI,CAACE,GAAG,CAACrC,OAAO,CAACmB,SAAS,GAAGW,UAAU,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACJ,OAAO,CAACd,SAAS,GAAGW,UAAU,CAAC,EAAE;MACvFG,OAAO,GAAGjC,OAAO;IACnB;IAEA,IAAIA,OAAO,CAACmB,SAAS,GAAGW,UAAU,EAAE;MAClCC,IAAI,GAAGG,GAAG,GAAG,CAAC;IAChB,CAAC,MAAM,IAAIlC,OAAO,CAACmB,SAAS,GAAGW,UAAU,EAAE;MACzCE,KAAK,GAAGE,GAAG,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,OAAOlC,OAAO;IAChB;EACF;EAEA,OAAOiC,OAAO;AAChB;;AAEA;AACA,OAAO,SAASK,mBAAmBA,CAACtB,QAAsB,EAAEc,UAAkB,EAAgB;EAC5F,OAAOd,QAAQ,CAACuB,MAAM,CAACb,GAAG,IAAIA,GAAG,CAACP,SAAS,IAAIW,UAAU,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}