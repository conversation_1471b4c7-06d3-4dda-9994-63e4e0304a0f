{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _bigintConstructor = BigInt;\nconst _fromJsConstructor = new FromJsConstructorBuilder().withPrimitives(Primitives.Number, Primitives.BigInt).withClassesToUnbox(Number).withClasses(_bigintConstructor).build();\nexport class Integer extends Value(Number, IonTypes.INT, _fromJsConstructor) {\n  constructor(value, annotations = []) {\n    if (typeof value === \"number\") {\n      super(value);\n      this._numberValue = value;\n      this._bigIntValue = null;\n    } else {\n      const numberValue = Number(value);\n      super(numberValue);\n      this._bigIntValue = value;\n      this._numberValue = numberValue;\n    }\n    this._setAnnotations(annotations);\n  }\n  bigIntValue() {\n    if (this._bigIntValue === null) {\n      this._bigIntValue = BigInt(this.numberValue());\n    }\n    return this._bigIntValue;\n  }\n  numberValue() {\n    return this._numberValue;\n  }\n  toString() {\n    if (this._bigIntValue === null) {\n      return this._numberValue.toString();\n    }\n    return this._bigIntValue.toString();\n  }\n  valueOf() {\n    return this.numberValue();\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    if (this._bigIntValue === null) {\n      writer.writeInt(this.numberValue());\n    } else {\n      writer.writeInt(this._bigIntValue);\n    }\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Integer) {\n      isSupportedType = true;\n      if (this._bigIntValue == null && other._bigIntValue == null) {\n        valueToCompare = other.numberValue();\n      } else {\n        valueToCompare = other.bigIntValue();\n      }\n    } else if (!options.onlyCompareIon) {\n      if (other instanceof Number || typeof other === \"number\") {\n        isSupportedType = true;\n        if (this.bigIntValue == null) {\n          valueToCompare = other.valueOf();\n        } else {\n          valueToCompare = BigInt(other.valueOf());\n        }\n      } else if (typeof other === \"bigint\") {\n        isSupportedType = true;\n        valueToCompare = other;\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    if (typeof valueToCompare === \"bigint\") {\n      return this.bigIntValue() === valueToCompare;\n    }\n    return this.numberValue() == valueToCompare;\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Primitives", "Value", "_bigintConstructor", "BigInt", "_fromJsConstructor", "withPrimitives", "Number", "withClassesToUnbox", "withClasses", "build", "Integer", "INT", "constructor", "value", "annotations", "_numberValue", "_bigIntValue", "numberValue", "_setAnnotations", "bigIntValue", "toString", "valueOf", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeInt", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Integer.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _bigintConstructor = BigInt;\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withPrimitives(Primitives.Number, Primitives.BigInt)\n    .withClassesToUnbox(Number)\n    .withClasses(_bigintConstructor)\n    .build();\nexport class Integer extends Value(Number, IonTypes.INT, _fromJsConstructor) {\n    constructor(value, annotations = []) {\n        if (typeof value === \"number\") {\n            super(value);\n            this._numberValue = value;\n            this._bigIntValue = null;\n        }\n        else {\n            const numberValue = Number(value);\n            super(numberValue);\n            this._bigIntValue = value;\n            this._numberValue = numberValue;\n        }\n        this._setAnnotations(annotations);\n    }\n    bigIntValue() {\n        if (this._bigIntValue === null) {\n            this._bigIntValue = BigInt(this.numberValue());\n        }\n        return this._bigIntValue;\n    }\n    numberValue() {\n        return this._numberValue;\n    }\n    toString() {\n        if (this._bigIntValue === null) {\n            return this._numberValue.toString();\n        }\n        return this._bigIntValue.toString();\n    }\n    valueOf() {\n        return this.numberValue();\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        if (this._bigIntValue === null) {\n            writer.writeInt(this.numberValue());\n        }\n        else {\n            writer.writeInt(this._bigIntValue);\n        }\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Integer) {\n            isSupportedType = true;\n            if (this._bigIntValue == null && other._bigIntValue == null) {\n                valueToCompare = other.numberValue();\n            }\n            else {\n                valueToCompare = other.bigIntValue();\n            }\n        }\n        else if (!options.onlyCompareIon) {\n            if (other instanceof Number || typeof other === \"number\") {\n                isSupportedType = true;\n                if (this.bigIntValue == null) {\n                    valueToCompare = other.valueOf();\n                }\n                else {\n                    valueToCompare = BigInt(other.valueOf());\n                }\n            }\n            else if (typeof other === \"bigint\") {\n                isSupportedType = true;\n                valueToCompare = other;\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        if (typeof valueToCompare === \"bigint\") {\n            return this.bigIntValue() === valueToCompare;\n        }\n        return this.numberValue() == valueToCompare;\n    }\n}\n//# sourceMappingURL=Integer.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,EAAEC,UAAU,QAAS,qBAAqB;AAC3E,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAGC,MAAM;AACjC,MAAMC,kBAAkB,GAAG,IAAIL,wBAAwB,CAAC,CAAC,CACpDM,cAAc,CAACL,UAAU,CAACM,MAAM,EAAEN,UAAU,CAACG,MAAM,CAAC,CACpDI,kBAAkB,CAACD,MAAM,CAAC,CAC1BE,WAAW,CAACN,kBAAkB,CAAC,CAC/BO,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMC,OAAO,SAAST,KAAK,CAACK,MAAM,EAAER,QAAQ,CAACa,GAAG,EAAEP,kBAAkB,CAAC,CAAC;EACzEQ,WAAWA,CAACC,KAAK,EAAEC,WAAW,GAAG,EAAE,EAAE;IACjC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC3B,KAAK,CAACA,KAAK,CAAC;MACZ,IAAI,CAACE,YAAY,GAAGF,KAAK;MACzB,IAAI,CAACG,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI;MACD,MAAMC,WAAW,GAAGX,MAAM,CAACO,KAAK,CAAC;MACjC,KAAK,CAACI,WAAW,CAAC;MAClB,IAAI,CAACD,YAAY,GAAGH,KAAK;MACzB,IAAI,CAACE,YAAY,GAAGE,WAAW;IACnC;IACA,IAAI,CAACC,eAAe,CAACJ,WAAW,CAAC;EACrC;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACH,YAAY,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACA,YAAY,GAAGb,MAAM,CAAC,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACD,YAAY;EAC5B;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAK,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACJ,YAAY,KAAK,IAAI,EAAE;MAC5B,OAAO,IAAI,CAACD,YAAY,CAACK,QAAQ,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACJ,YAAY,CAACI,QAAQ,CAAC,CAAC;EACvC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACJ,WAAW,CAAC,CAAC;EAC7B;EACAK,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACT,YAAY,KAAK,IAAI,EAAE;MAC5BO,MAAM,CAACG,QAAQ,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,CAAC;IACvC,CAAC,MACI;MACDM,MAAM,CAACG,QAAQ,CAAC,IAAI,CAACV,YAAY,CAAC;IACtC;EACJ;EACAW,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAYlB,OAAO,EAAE;MAC1BwB,eAAe,GAAG,IAAI;MACtB,IAAI,IAAI,CAAClB,YAAY,IAAI,IAAI,IAAIY,KAAK,CAACZ,YAAY,IAAI,IAAI,EAAE;QACzDmB,cAAc,GAAGP,KAAK,CAACX,WAAW,CAAC,CAAC;MACxC,CAAC,MACI;QACDkB,cAAc,GAAGP,KAAK,CAACT,WAAW,CAAC,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,CAACU,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAIL,KAAK,YAAYtB,MAAM,IAAI,OAAOsB,KAAK,KAAK,QAAQ,EAAE;QACtDM,eAAe,GAAG,IAAI;QACtB,IAAI,IAAI,CAACf,WAAW,IAAI,IAAI,EAAE;UAC1BgB,cAAc,GAAGP,KAAK,CAACP,OAAO,CAAC,CAAC;QACpC,CAAC,MACI;UACDc,cAAc,GAAGhC,MAAM,CAACyB,KAAK,CAACP,OAAO,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,MACI,IAAI,OAAOO,KAAK,KAAK,QAAQ,EAAE;QAChCM,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGP,KAAK;MAC1B;IACJ;IACA,IAAI,CAACM,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACpC,OAAO,IAAI,CAAChB,WAAW,CAAC,CAAC,KAAKgB,cAAc;IAChD;IACA,OAAO,IAAI,CAAClB,WAAW,CAAC,CAAC,IAAIkB,cAAc;EAC/C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}