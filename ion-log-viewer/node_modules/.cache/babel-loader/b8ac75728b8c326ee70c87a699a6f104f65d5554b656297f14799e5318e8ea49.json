{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nexport { _objectSpread2 as _, _defineProperty as a };", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_", "a"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/maath/dist/objectSpread2-284232a6.esm.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nexport { _objectSpread2 as _, _defineProperty as a };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACxC,IAAID,GAAG,IAAID,GAAG,EAAE;IACdG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOF,GAAG;AACZ;AAEA,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIN,MAAM,CAACS,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGV,MAAM,CAACS,qBAAqB,CAACH,MAAM,CAAC;IAElD,IAAIC,cAAc,EAAE;MAClBG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QACtC,OAAOZ,MAAM,CAACa,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACV,UAAU;MAChE,CAAC,CAAC;IACJ;IAEAM,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAChC;EAEA,OAAOF,IAAI;AACb;AAEA,SAASQ,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTb,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QACnDF,eAAe,CAACqB,MAAM,EAAEnB,GAAG,EAAEuB,MAAM,CAACvB,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIE,MAAM,CAACuB,yBAAyB,EAAE;MAC3CvB,MAAM,CAACwB,gBAAgB,CAACP,MAAM,EAAEjB,MAAM,CAACuB,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLhB,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QAC7CE,MAAM,CAACC,cAAc,CAACgB,MAAM,EAAEnB,GAAG,EAAEE,MAAM,CAACa,wBAAwB,CAACQ,MAAM,EAAEvB,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASD,cAAc,IAAIS,CAAC,EAAE7B,eAAe,IAAI8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}