{"ast": null, "code": "import { Vector2, MeshNormalMaterial, ShaderMaterial, Vector4, WebGLRenderTarget, DepthTexture, NearestFilter, RGBAFormat } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nclass RenderPixelatedPass extends Pass {\n  constructor(resolution, pixelSize, scene, camera, options = {}) {\n    var _a, _b;\n    super();\n    this.pixelSize = pixelSize;\n    this.resolution = new Vector2();\n    this.renderResolution = new Vector2();\n    this.setSize(resolution.x, resolution.y);\n    this.fsQuad = new FullScreenQuad(this.material());\n    this.scene = scene;\n    this.camera = camera;\n    this.normalEdgeStrength = (_a = options.normalEdgeStrength) != null ? _a : 0.3;\n    this.depthEdgeStrength = (_b = options.depthEdgeStrength) != null ? _b : 0.4;\n    this.rgbRenderTarget = pixelRenderTarget(this.renderResolution, RGBAFormat, true);\n    this.normalRenderTarget = pixelRenderTarget(this.renderResolution, RGBAFormat, false);\n    this.normalMaterial = new MeshNormalMaterial();\n  }\n  dispose() {\n    this.rgbRenderTarget.dispose();\n    this.normalRenderTarget.dispose();\n    this.fsQuad.dispose();\n  }\n  setSize(width, height) {\n    var _a, _b, _c;\n    this.resolution.set(width, height);\n    this.renderResolution.set(width / this.pixelSize | 0, height / this.pixelSize | 0);\n    const {\n      x,\n      y\n    } = this.renderResolution;\n    (_a = this.rgbRenderTarget) == null ? void 0 : _a.setSize(x, y);\n    (_b = this.normalRenderTarget) == null ? void 0 : _b.setSize(x, y);\n    (_c = this.fsQuad) == null ? void 0 : _c.material.uniforms.resolution.value.set(x, y, 1 / x, 1 / y);\n  }\n  setPixelSize(pixelSize) {\n    this.pixelSize = pixelSize;\n    this.setSize(this.resolution.x, this.resolution.y);\n  }\n  render(renderer, writeBuffer) {\n    const uniforms = this.fsQuad.material.uniforms;\n    uniforms.normalEdgeStrength.value = this.normalEdgeStrength;\n    uniforms.depthEdgeStrength.value = this.depthEdgeStrength;\n    renderer.setRenderTarget(this.rgbRenderTarget);\n    renderer.render(this.scene, this.camera);\n    const overrideMaterial_old = this.scene.overrideMaterial;\n    renderer.setRenderTarget(this.normalRenderTarget);\n    this.scene.overrideMaterial = this.normalMaterial;\n    renderer.render(this.scene, this.camera);\n    this.scene.overrideMaterial = overrideMaterial_old;\n    uniforms.tDiffuse.value = this.rgbRenderTarget.texture;\n    uniforms.tDepth.value = this.rgbRenderTarget.depthTexture;\n    uniforms.tNormal.value = this.normalRenderTarget.texture;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n    }\n    this.fsQuad.render(renderer);\n  }\n  material() {\n    return new ShaderMaterial({\n      uniforms: {\n        tDiffuse: {\n          value: null\n        },\n        tDepth: {\n          value: null\n        },\n        tNormal: {\n          value: null\n        },\n        resolution: {\n          value: new Vector4(this.renderResolution.x, this.renderResolution.y, 1 / this.renderResolution.x, 1 / this.renderResolution.y)\n        },\n        normalEdgeStrength: {\n          value: 0\n        },\n        depthEdgeStrength: {\n          value: 0\n        }\n      },\n      vertexShader: `\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\t}\n\t\t\t\t`,\n      fragmentShader: `\n\t\t\t\tuniform sampler2D tDiffuse;\n\t\t\t\tuniform sampler2D tDepth;\n\t\t\t\tuniform sampler2D tNormal;\n\t\t\t\tuniform vec4 resolution;\n\t\t\t\tuniform float normalEdgeStrength;\n\t\t\t\tuniform float depthEdgeStrength;\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\tfloat getDepth(int x, int y) {\n\n\t\t\t\t\treturn texture2D( tDepth, vUv + vec2(x, y) * resolution.zw ).r;\n\n\t\t\t\t}\n\n\t\t\t\tvec3 getNormal(int x, int y) {\n\n\t\t\t\t\treturn texture2D( tNormal, vUv + vec2(x, y) * resolution.zw ).rgb * 2.0 - 1.0;\n\n\t\t\t\t}\n\n\t\t\t\tfloat depthEdgeIndicator(float depth, vec3 normal) {\n\n\t\t\t\t\tfloat diff = 0.0;\n\t\t\t\t\tdiff += clamp(getDepth(1, 0) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(-1, 0) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(0, 1) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(0, -1) - depth, 0.0, 1.0);\n\t\t\t\t\treturn floor(smoothstep(0.01, 0.02, diff) * 2.) / 2.;\n\n\t\t\t\t}\n\n\t\t\t\tfloat neighborNormalEdgeIndicator(int x, int y, float depth, vec3 normal) {\n\n\t\t\t\t\tfloat depthDiff = getDepth(x, y) - depth;\n\t\t\t\t\tvec3 neighborNormal = getNormal(x, y);\n\t\t\t\t\t\n\t\t\t\t\t// Edge pixels should yield to faces who's normals are closer to the bias normal.\n\t\t\t\t\tvec3 normalEdgeBias = vec3(1., 1., 1.); // This should probably be a parameter.\n\t\t\t\t\tfloat normalDiff = dot(normal - neighborNormal, normalEdgeBias);\n\t\t\t\t\tfloat normalIndicator = clamp(smoothstep(-.01, .01, normalDiff), 0.0, 1.0);\n\t\t\t\t\t\n\t\t\t\t\t// Only the shallower pixel should detect the normal edge.\n\t\t\t\t\tfloat depthIndicator = clamp(sign(depthDiff * .25 + .0025), 0.0, 1.0);\n\n\t\t\t\t\treturn (1.0 - dot(normal, neighborNormal)) * depthIndicator * normalIndicator;\n\n\t\t\t\t}\n\n\t\t\t\tfloat normalEdgeIndicator(float depth, vec3 normal) {\n\t\t\t\t\t\n\t\t\t\t\tfloat indicator = 0.0;\n\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(0, -1, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(0, 1, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(-1, 0, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(1, 0, depth, normal);\n\n\t\t\t\t\treturn step(0.1, indicator);\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec4 texel = texture2D( tDiffuse, vUv );\n\n\t\t\t\t\tfloat depth = 0.0;\n\t\t\t\t\tvec3 normal = vec3(0.0);\n\n\t\t\t\t\tif (depthEdgeStrength > 0.0 || normalEdgeStrength > 0.0) {\n\n\t\t\t\t\t\tdepth = getDepth(0, 0);\n\t\t\t\t\t\tnormal = getNormal(0, 0);\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfloat dei = 0.0;\n\t\t\t\t\tif (depthEdgeStrength > 0.0) \n\t\t\t\t\t\tdei = depthEdgeIndicator(depth, normal);\n\n\t\t\t\t\tfloat nei = 0.0; \n\t\t\t\t\tif (normalEdgeStrength > 0.0) \n\t\t\t\t\t\tnei = normalEdgeIndicator(depth, normal);\n\n\t\t\t\t\tfloat Strength = dei > 0.0 ? (1.0 - depthEdgeStrength * dei) : (1.0 + normalEdgeStrength * nei);\n\n\t\t\t\t\tgl_FragColor = texel * Strength;\n\n\t\t\t\t}\n\t\t\t\t`\n    });\n  }\n}\nfunction pixelRenderTarget(resolution, pixelFormat, useDepthTexture) {\n  const renderTarget = new WebGLRenderTarget(resolution.x, resolution.y, !useDepthTexture ? void 0 : {\n    depthTexture: new DepthTexture(resolution.x, resolution.y),\n    depthBuffer: true\n  });\n  renderTarget.texture.format = pixelFormat;\n  renderTarget.texture.minFilter = NearestFilter;\n  renderTarget.texture.magFilter = NearestFilter;\n  renderTarget.texture.generateMipmaps = false;\n  renderTarget.stencilBuffer = false;\n  return renderTarget;\n}\nexport { RenderPixelatedPass };", "map": {"version": 3, "names": ["RenderPixelatedPass", "Pass", "constructor", "resolution", "pixelSize", "scene", "camera", "options", "Vector2", "renderResolution", "setSize", "x", "y", "fsQuad", "FullScreenQuad", "material", "normalEdgeStrength", "_a", "depthEdgeStrength", "_b", "rgbRenderTarget", "pixelRenderTarget", "RGBAFormat", "normalRenderTarget", "normalMaterial", "MeshNormalMaterial", "dispose", "width", "height", "set", "_c", "uniforms", "value", "setPixelSize", "render", "renderer", "writeBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overrideMaterial_old", "overrideMaterial", "tDiffuse", "texture", "tD<PERSON>h", "depthTexture", "tNormal", "renderToScreen", "clear", "ShaderMaterial", "Vector4", "vertexShader", "fragmentShader", "pixelFormat", "useDepthTexture", "renderTarget", "WebGLRenderTarget", "DepthTexture", "depthBuffer", "format", "minFilter", "NearestFilter", "magFilter", "generateMipmaps", "stencil<PERSON>uffer"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/postprocessing/RenderPixelatedPass.js"], "sourcesContent": ["import {\n  WebGLRenderTarget,\n  RGBAFormat,\n  MeshNormalMaterial,\n  ShaderMaterial,\n  Vector2,\n  Vector4,\n  DepthTexture,\n  NearestFilter,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\n\nclass RenderPixelatedPass extends Pass {\n  constructor(resolution, pixelSize, scene, camera, options = {}) {\n    super()\n\n    this.pixelSize = pixelSize\n    this.resolution = new Vector2()\n    this.renderResolution = new Vector2()\n    this.setSize(resolution.x, resolution.y)\n\n    this.fsQuad = new FullScreenQuad(this.material())\n    this.scene = scene\n    this.camera = camera\n\n    this.normalEdgeStrength = options.normalEdgeStrength ?? 0.3\n    this.depthEdgeStrength = options.depthEdgeStrength ?? 0.4\n\n    this.rgbRenderTarget = pixelRenderTarget(this.renderResolution, RGBAFormat, true)\n    this.normalRenderTarget = pixelRenderTarget(this.renderResolution, RGBAFormat, false)\n\n    this.normalMaterial = new MeshNormalMaterial()\n  }\n\n  dispose() {\n    this.rgbRenderTarget.dispose()\n    this.normalRenderTarget.dispose()\n    this.fsQuad.dispose()\n  }\n\n  setSize(width, height) {\n    this.resolution.set(width, height)\n    this.renderResolution.set((width / this.pixelSize) | 0, (height / this.pixelSize) | 0)\n    const { x, y } = this.renderResolution\n    this.rgbRenderTarget?.setSize(x, y)\n    this.normalRenderTarget?.setSize(x, y)\n    this.fsQuad?.material.uniforms.resolution.value.set(x, y, 1 / x, 1 / y)\n  }\n\n  setPixelSize(pixelSize) {\n    this.pixelSize = pixelSize\n    this.setSize(this.resolution.x, this.resolution.y)\n  }\n\n  render(renderer, writeBuffer) {\n    const uniforms = this.fsQuad.material.uniforms\n    uniforms.normalEdgeStrength.value = this.normalEdgeStrength\n    uniforms.depthEdgeStrength.value = this.depthEdgeStrength\n\n    renderer.setRenderTarget(this.rgbRenderTarget)\n    renderer.render(this.scene, this.camera)\n\n    const overrideMaterial_old = this.scene.overrideMaterial\n    renderer.setRenderTarget(this.normalRenderTarget)\n    this.scene.overrideMaterial = this.normalMaterial\n    renderer.render(this.scene, this.camera)\n    this.scene.overrideMaterial = overrideMaterial_old\n\n    uniforms.tDiffuse.value = this.rgbRenderTarget.texture\n    uniforms.tDepth.value = this.rgbRenderTarget.depthTexture\n    uniforms.tNormal.value = this.normalRenderTarget.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n\n      if (this.clear) renderer.clear()\n    }\n\n    this.fsQuad.render(renderer)\n  }\n\n  material() {\n    return new ShaderMaterial({\n      uniforms: {\n        tDiffuse: { value: null },\n        tDepth: { value: null },\n        tNormal: { value: null },\n        resolution: {\n          value: new Vector4(\n            this.renderResolution.x,\n            this.renderResolution.y,\n            1 / this.renderResolution.x,\n            1 / this.renderResolution.y,\n          ),\n        },\n        normalEdgeStrength: { value: 0 },\n        depthEdgeStrength: { value: 0 },\n      },\n      vertexShader: `\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\t}\n\t\t\t\t`,\n      fragmentShader: `\n\t\t\t\tuniform sampler2D tDiffuse;\n\t\t\t\tuniform sampler2D tDepth;\n\t\t\t\tuniform sampler2D tNormal;\n\t\t\t\tuniform vec4 resolution;\n\t\t\t\tuniform float normalEdgeStrength;\n\t\t\t\tuniform float depthEdgeStrength;\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\tfloat getDepth(int x, int y) {\n\n\t\t\t\t\treturn texture2D( tDepth, vUv + vec2(x, y) * resolution.zw ).r;\n\n\t\t\t\t}\n\n\t\t\t\tvec3 getNormal(int x, int y) {\n\n\t\t\t\t\treturn texture2D( tNormal, vUv + vec2(x, y) * resolution.zw ).rgb * 2.0 - 1.0;\n\n\t\t\t\t}\n\n\t\t\t\tfloat depthEdgeIndicator(float depth, vec3 normal) {\n\n\t\t\t\t\tfloat diff = 0.0;\n\t\t\t\t\tdiff += clamp(getDepth(1, 0) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(-1, 0) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(0, 1) - depth, 0.0, 1.0);\n\t\t\t\t\tdiff += clamp(getDepth(0, -1) - depth, 0.0, 1.0);\n\t\t\t\t\treturn floor(smoothstep(0.01, 0.02, diff) * 2.) / 2.;\n\n\t\t\t\t}\n\n\t\t\t\tfloat neighborNormalEdgeIndicator(int x, int y, float depth, vec3 normal) {\n\n\t\t\t\t\tfloat depthDiff = getDepth(x, y) - depth;\n\t\t\t\t\tvec3 neighborNormal = getNormal(x, y);\n\t\t\t\t\t\n\t\t\t\t\t// Edge pixels should yield to faces who's normals are closer to the bias normal.\n\t\t\t\t\tvec3 normalEdgeBias = vec3(1., 1., 1.); // This should probably be a parameter.\n\t\t\t\t\tfloat normalDiff = dot(normal - neighborNormal, normalEdgeBias);\n\t\t\t\t\tfloat normalIndicator = clamp(smoothstep(-.01, .01, normalDiff), 0.0, 1.0);\n\t\t\t\t\t\n\t\t\t\t\t// Only the shallower pixel should detect the normal edge.\n\t\t\t\t\tfloat depthIndicator = clamp(sign(depthDiff * .25 + .0025), 0.0, 1.0);\n\n\t\t\t\t\treturn (1.0 - dot(normal, neighborNormal)) * depthIndicator * normalIndicator;\n\n\t\t\t\t}\n\n\t\t\t\tfloat normalEdgeIndicator(float depth, vec3 normal) {\n\t\t\t\t\t\n\t\t\t\t\tfloat indicator = 0.0;\n\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(0, -1, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(0, 1, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(-1, 0, depth, normal);\n\t\t\t\t\tindicator += neighborNormalEdgeIndicator(1, 0, depth, normal);\n\n\t\t\t\t\treturn step(0.1, indicator);\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec4 texel = texture2D( tDiffuse, vUv );\n\n\t\t\t\t\tfloat depth = 0.0;\n\t\t\t\t\tvec3 normal = vec3(0.0);\n\n\t\t\t\t\tif (depthEdgeStrength > 0.0 || normalEdgeStrength > 0.0) {\n\n\t\t\t\t\t\tdepth = getDepth(0, 0);\n\t\t\t\t\t\tnormal = getNormal(0, 0);\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfloat dei = 0.0;\n\t\t\t\t\tif (depthEdgeStrength > 0.0) \n\t\t\t\t\t\tdei = depthEdgeIndicator(depth, normal);\n\n\t\t\t\t\tfloat nei = 0.0; \n\t\t\t\t\tif (normalEdgeStrength > 0.0) \n\t\t\t\t\t\tnei = normalEdgeIndicator(depth, normal);\n\n\t\t\t\t\tfloat Strength = dei > 0.0 ? (1.0 - depthEdgeStrength * dei) : (1.0 + normalEdgeStrength * nei);\n\n\t\t\t\t\tgl_FragColor = texel * Strength;\n\n\t\t\t\t}\n\t\t\t\t`,\n    })\n  }\n}\n\nfunction pixelRenderTarget(resolution, pixelFormat, useDepthTexture) {\n  const renderTarget = new WebGLRenderTarget(\n    resolution.x,\n    resolution.y,\n    !useDepthTexture\n      ? undefined\n      : {\n          depthTexture: new DepthTexture(resolution.x, resolution.y),\n          depthBuffer: true,\n        },\n  )\n  renderTarget.texture.format = pixelFormat\n  renderTarget.texture.minFilter = NearestFilter\n  renderTarget.texture.magFilter = NearestFilter\n  renderTarget.texture.generateMipmaps = false\n  renderTarget.stencilBuffer = false\n  return renderTarget\n}\n\nexport { RenderPixelatedPass }\n"], "mappings": ";;AAYA,MAAMA,mBAAA,SAA4BC,IAAA,CAAK;EACrCC,YAAYC,UAAA,EAAYC,SAAA,EAAWC,KAAA,EAAOC,MAAA,EAAQC,OAAA,GAAU,IAAI;;IAC9D,MAAO;IAEP,KAAKH,SAAA,GAAYA,SAAA;IACjB,KAAKD,UAAA,GAAa,IAAIK,OAAA,CAAS;IAC/B,KAAKC,gBAAA,GAAmB,IAAID,OAAA,CAAS;IACrC,KAAKE,OAAA,CAAQP,UAAA,CAAWQ,CAAA,EAAGR,UAAA,CAAWS,CAAC;IAEvC,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKC,QAAA,CAAQ,CAAE;IAChD,KAAKV,KAAA,GAAQA,KAAA;IACb,KAAKC,MAAA,GAASA,MAAA;IAEd,KAAKU,kBAAA,IAAqBC,EAAA,GAAAV,OAAA,CAAQS,kBAAA,KAAR,OAAAC,EAAA,GAA8B;IACxD,KAAKC,iBAAA,IAAoBC,EAAA,GAAAZ,OAAA,CAAQW,iBAAA,KAAR,OAAAC,EAAA,GAA6B;IAEtD,KAAKC,eAAA,GAAkBC,iBAAA,CAAkB,KAAKZ,gBAAA,EAAkBa,UAAA,EAAY,IAAI;IAChF,KAAKC,kBAAA,GAAqBF,iBAAA,CAAkB,KAAKZ,gBAAA,EAAkBa,UAAA,EAAY,KAAK;IAEpF,KAAKE,cAAA,GAAiB,IAAIC,kBAAA,CAAoB;EAC/C;EAEDC,QAAA,EAAU;IACR,KAAKN,eAAA,CAAgBM,OAAA,CAAS;IAC9B,KAAKH,kBAAA,CAAmBG,OAAA,CAAS;IACjC,KAAKb,MAAA,CAAOa,OAAA,CAAS;EACtB;EAEDhB,QAAQiB,KAAA,EAAOC,MAAA,EAAQ;;IACrB,KAAKzB,UAAA,CAAW0B,GAAA,CAAIF,KAAA,EAAOC,MAAM;IACjC,KAAKnB,gBAAA,CAAiBoB,GAAA,CAAKF,KAAA,GAAQ,KAAKvB,SAAA,GAAa,GAAIwB,MAAA,GAAS,KAAKxB,SAAA,GAAa,CAAC;IACrF,MAAM;MAAEO,CAAA;MAAGC;IAAG,IAAG,KAAKH,gBAAA;IACtB,CAAAQ,EAAA,QAAKG,eAAA,KAAL,gBAAAH,EAAA,CAAsBP,OAAA,CAAQC,CAAA,EAAGC,CAAA;IACjC,CAAAO,EAAA,QAAKI,kBAAA,KAAL,gBAAAJ,EAAA,CAAyBT,OAAA,CAAQC,CAAA,EAAGC,CAAA;IACpC,CAAAkB,EAAA,QAAKjB,MAAA,KAAL,gBAAAiB,EAAA,CAAaf,QAAA,CAASgB,QAAA,CAAS5B,UAAA,CAAW6B,KAAA,CAAMH,GAAA,CAAIlB,CAAA,EAAGC,CAAA,EAAG,IAAID,CAAA,EAAG,IAAIC,CAAA;EACtE;EAEDqB,aAAa7B,SAAA,EAAW;IACtB,KAAKA,SAAA,GAAYA,SAAA;IACjB,KAAKM,OAAA,CAAQ,KAAKP,UAAA,CAAWQ,CAAA,EAAG,KAAKR,UAAA,CAAWS,CAAC;EAClD;EAEDsB,OAAOC,QAAA,EAAUC,WAAA,EAAa;IAC5B,MAAML,QAAA,GAAW,KAAKlB,MAAA,CAAOE,QAAA,CAASgB,QAAA;IACtCA,QAAA,CAASf,kBAAA,CAAmBgB,KAAA,GAAQ,KAAKhB,kBAAA;IACzCe,QAAA,CAASb,iBAAA,CAAkBc,KAAA,GAAQ,KAAKd,iBAAA;IAExCiB,QAAA,CAASE,eAAA,CAAgB,KAAKjB,eAAe;IAC7Ce,QAAA,CAASD,MAAA,CAAO,KAAK7B,KAAA,EAAO,KAAKC,MAAM;IAEvC,MAAMgC,oBAAA,GAAuB,KAAKjC,KAAA,CAAMkC,gBAAA;IACxCJ,QAAA,CAASE,eAAA,CAAgB,KAAKd,kBAAkB;IAChD,KAAKlB,KAAA,CAAMkC,gBAAA,GAAmB,KAAKf,cAAA;IACnCW,QAAA,CAASD,MAAA,CAAO,KAAK7B,KAAA,EAAO,KAAKC,MAAM;IACvC,KAAKD,KAAA,CAAMkC,gBAAA,GAAmBD,oBAAA;IAE9BP,QAAA,CAASS,QAAA,CAASR,KAAA,GAAQ,KAAKZ,eAAA,CAAgBqB,OAAA;IAC/CV,QAAA,CAASW,MAAA,CAAOV,KAAA,GAAQ,KAAKZ,eAAA,CAAgBuB,YAAA;IAC7CZ,QAAA,CAASa,OAAA,CAAQZ,KAAA,GAAQ,KAAKT,kBAAA,CAAmBkB,OAAA;IAEjD,IAAI,KAAKI,cAAA,EAAgB;MACvBV,QAAA,CAASE,eAAA,CAAgB,IAAI;IACnC,OAAW;MACLF,QAAA,CAASE,eAAA,CAAgBD,WAAW;MAEpC,IAAI,KAAKU,KAAA,EAAOX,QAAA,CAASW,KAAA,CAAO;IACjC;IAED,KAAKjC,MAAA,CAAOqB,MAAA,CAAOC,QAAQ;EAC5B;EAEDpB,SAAA,EAAW;IACT,OAAO,IAAIgC,cAAA,CAAe;MACxBhB,QAAA,EAAU;QACRS,QAAA,EAAU;UAAER,KAAA,EAAO;QAAM;QACzBU,MAAA,EAAQ;UAAEV,KAAA,EAAO;QAAM;QACvBY,OAAA,EAAS;UAAEZ,KAAA,EAAO;QAAM;QACxB7B,UAAA,EAAY;UACV6B,KAAA,EAAO,IAAIgB,OAAA,CACT,KAAKvC,gBAAA,CAAiBE,CAAA,EACtB,KAAKF,gBAAA,CAAiBG,CAAA,EACtB,IAAI,KAAKH,gBAAA,CAAiBE,CAAA,EAC1B,IAAI,KAAKF,gBAAA,CAAiBG,CAC3B;QACF;QACDI,kBAAA,EAAoB;UAAEgB,KAAA,EAAO;QAAG;QAChCd,iBAAA,EAAmB;UAAEc,KAAA,EAAO;QAAG;MAChC;MACDiB,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAUdC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IA0FtB,CAAK;EACF;AACH;AAEA,SAAS7B,kBAAkBlB,UAAA,EAAYgD,WAAA,EAAaC,eAAA,EAAiB;EACnE,MAAMC,YAAA,GAAe,IAAIC,iBAAA,CACvBnD,UAAA,CAAWQ,CAAA,EACXR,UAAA,CAAWS,CAAA,EACX,CAACwC,eAAA,GACG,SACA;IACET,YAAA,EAAc,IAAIY,YAAA,CAAapD,UAAA,CAAWQ,CAAA,EAAGR,UAAA,CAAWS,CAAC;IACzD4C,WAAA,EAAa;EACd,CACN;EACDH,YAAA,CAAaZ,OAAA,CAAQgB,MAAA,GAASN,WAAA;EAC9BE,YAAA,CAAaZ,OAAA,CAAQiB,SAAA,GAAYC,aAAA;EACjCN,YAAA,CAAaZ,OAAA,CAAQmB,SAAA,GAAYD,aAAA;EACjCN,YAAA,CAAaZ,OAAA,CAAQoB,eAAA,GAAkB;EACvCR,YAAA,CAAaS,aAAA,GAAgB;EAC7B,OAAOT,YAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}