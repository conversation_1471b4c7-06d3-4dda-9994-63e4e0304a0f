{"ast": null, "code": "import * as ion from \"../Ion\";\nimport { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nimport { Float } from \"./Float\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withClasses(ion.Decimal).build();\nexport class Decimal extends Value(Number, IonTypes.DECIMAL, _fromJsConstructor) {\n  constructor(value, annotations = []) {\n    if (typeof value === \"string\") {\n      let numberValue = Number(value);\n      super(numberValue);\n      this._decimalValue = new ion.Decimal(value);\n      this._numberValue = numberValue;\n    } else if (value instanceof ion.Decimal) {\n      super(value.numberValue());\n      this._decimalValue = value;\n      this._numberValue = value.numberValue();\n    } else if (typeof value === \"number\") {\n      super(value);\n      this._decimalValue = new ion.Decimal(\"\" + value);\n      this._numberValue = value;\n    } else {\n      throw new Error(\"Decimal value can only be created from number, ion.Decimal or string\");\n    }\n    this._setAnnotations(annotations);\n  }\n  numberValue() {\n    return this._numberValue;\n  }\n  decimalValue() {\n    return this._decimalValue;\n  }\n  toString() {\n    return this._decimalValue.toString();\n  }\n  valueOf() {\n    return this._numberValue;\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeDecimal(this.decimalValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true,\n    coerceNumericType: false\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Decimal) {\n      isSupportedType = true;\n      valueToCompare = other.decimalValue();\n    } else if (options.coerceNumericType === true && other instanceof Float) {\n      isSupportedType = true;\n      valueToCompare = new ion.Decimal(other.toString());\n    } else if (!options.onlyCompareIon) {\n      if (other instanceof ion.Decimal) {\n        isSupportedType = true;\n        valueToCompare = other;\n      } else if (other instanceof Number || typeof other === \"number\") {\n        isSupportedType = true;\n        valueToCompare = new ion.Decimal(other.toString());\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    return this.decimalValue().equals(valueToCompare);\n  }\n}", "map": {"version": 3, "names": ["ion", "IonTypes", "FromJsConstructorBuilder", "Value", "Float", "_fromJsConstructor", "withClasses", "Decimal", "build", "Number", "DECIMAL", "constructor", "value", "annotations", "numberValue", "_decimalValue", "_numberValue", "Error", "_setAnnotations", "decimalValue", "toString", "valueOf", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeDecimal", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "coerceNumericType", "isSupportedType", "valueToCompare", "equals"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Decimal.js"], "sourcesContent": ["import * as ion from \"../Ion\";\nimport { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nimport { Float } from \"./Float\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withClasses(ion.Decimal)\n    .build();\nexport class Decimal extends Value(Number, IonTypes.DECIMAL, _fromJsConstructor) {\n    constructor(value, annotations = []) {\n        if (typeof value === \"string\") {\n            let numberValue = Number(value);\n            super(numberValue);\n            this._decimalValue = new ion.Decimal(value);\n            this._numberValue = numberValue;\n        }\n        else if (value instanceof ion.Decimal) {\n            super(value.numberValue());\n            this._decimalValue = value;\n            this._numberValue = value.numberValue();\n        }\n        else if (typeof value === \"number\") {\n            super(value);\n            this._decimalValue = new ion.Decimal(\"\" + value);\n            this._numberValue = value;\n        }\n        else {\n            throw new Error(\"Decimal value can only be created from number, ion.Decimal or string\");\n        }\n        this._setAnnotations(annotations);\n    }\n    numberValue() {\n        return this._numberValue;\n    }\n    decimalValue() {\n        return this._decimalValue;\n    }\n    toString() {\n        return this._decimalValue.toString();\n    }\n    valueOf() {\n        return this._numberValue;\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeDecimal(this.decimalValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n        coerceNumericType: false,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Decimal) {\n            isSupportedType = true;\n            valueToCompare = other.decimalValue();\n        }\n        else if (options.coerceNumericType === true && other instanceof Float) {\n            isSupportedType = true;\n            valueToCompare = new ion.Decimal(other.toString());\n        }\n        else if (!options.onlyCompareIon) {\n            if (other instanceof ion.Decimal) {\n                isSupportedType = true;\n                valueToCompare = other;\n            }\n            else if (other instanceof Number || typeof other === \"number\") {\n                isSupportedType = true;\n                valueToCompare = new ion.Decimal(other.toString());\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        return this.decimalValue().equals(valueToCompare);\n    }\n}\n//# sourceMappingURL=Decimal.js.map"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,QAAS,qBAAqB;AAC/D,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIH,wBAAwB,CAAC,CAAC,CACpDI,WAAW,CAACN,GAAG,CAACO,OAAO,CAAC,CACxBC,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMD,OAAO,SAASJ,KAAK,CAACM,MAAM,EAAER,QAAQ,CAACS,OAAO,EAAEL,kBAAkB,CAAC,CAAC;EAC7EM,WAAWA,CAACC,KAAK,EAAEC,WAAW,GAAG,EAAE,EAAE;IACjC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAIE,WAAW,GAAGL,MAAM,CAACG,KAAK,CAAC;MAC/B,KAAK,CAACE,WAAW,CAAC;MAClB,IAAI,CAACC,aAAa,GAAG,IAAIf,GAAG,CAACO,OAAO,CAACK,KAAK,CAAC;MAC3C,IAAI,CAACI,YAAY,GAAGF,WAAW;IACnC,CAAC,MACI,IAAIF,KAAK,YAAYZ,GAAG,CAACO,OAAO,EAAE;MACnC,KAAK,CAACK,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACC,aAAa,GAAGH,KAAK;MAC1B,IAAI,CAACI,YAAY,GAAGJ,KAAK,CAACE,WAAW,CAAC,CAAC;IAC3C,CAAC,MACI,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAChC,KAAK,CAACA,KAAK,CAAC;MACZ,IAAI,CAACG,aAAa,GAAG,IAAIf,GAAG,CAACO,OAAO,CAAC,EAAE,GAAGK,KAAK,CAAC;MAChD,IAAI,CAACI,YAAY,GAAGJ,KAAK;IAC7B,CAAC,MACI;MACD,MAAM,IAAIK,KAAK,CAAC,sEAAsE,CAAC;IAC3F;IACA,IAAI,CAACC,eAAe,CAACL,WAAW,CAAC;EACrC;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACE,YAAY;EAC5B;EACAG,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,aAAa;EAC7B;EACAK,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACL,aAAa,CAACK,QAAQ,CAAC,CAAC;EACxC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACL,YAAY;EAC5B;EACAM,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACP,YAAY,CAAC,CAAC,CAAC;EAC5C;EACAQ,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE;EACvB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIR,KAAK,YAAYrB,OAAO,EAAE;MAC1B4B,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGR,KAAK,CAACT,YAAY,CAAC,CAAC;IACzC,CAAC,MACI,IAAIU,OAAO,CAACK,iBAAiB,KAAK,IAAI,IAAIN,KAAK,YAAYxB,KAAK,EAAE;MACnE+B,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAG,IAAIpC,GAAG,CAACO,OAAO,CAACqB,KAAK,CAACR,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,CAACS,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAIL,KAAK,YAAY5B,GAAG,CAACO,OAAO,EAAE;QAC9B4B,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGR,KAAK;MAC1B,CAAC,MACI,IAAIA,KAAK,YAAYnB,MAAM,IAAI,OAAOmB,KAAK,KAAK,QAAQ,EAAE;QAC3DO,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAG,IAAIpC,GAAG,CAACO,OAAO,CAACqB,KAAK,CAACR,QAAQ,CAAC,CAAC,CAAC;MACtD;IACJ;IACA,IAAI,CAACe,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAAChB,YAAY,CAAC,CAAC,CAACkB,MAAM,CAACD,cAAc,CAAC;EACrD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}