{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives } from \"./FromJsConstructor\";\nimport { _NativeJsString } from \"./JsValueConversion\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withPrimitives(Primitives.String).withClassesToUnbox(_NativeJsString).build();\nexport class String extends Value(_NativeJsString, IonTypes.STRING, _fromJsConstructor) {\n  constructor(text, annotations = []) {\n    super(text);\n    this._setAnnotations(annotations);\n  }\n  stringValue() {\n    return this.toString();\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeString(this.stringValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof String) {\n      isSupportedType = true;\n      valueToCompare = other.stringValue();\n    } else if (!options.onlyCompareIon) {\n      if (typeof other === \"string\" || other instanceof _NativeJsString) {\n        isSupportedType = true;\n        valueToCompare = other.valueOf();\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    return this.compareValue(valueToCompare) === 0;\n  }\n  compareValue(expectedValue) {\n    return this.stringValue().localeCompare(expectedValue);\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Primitives", "_NativeJsString", "Value", "_fromJsConstructor", "withPrimitives", "String", "withClassesToUnbox", "build", "STRING", "constructor", "text", "annotations", "_setAnnotations", "stringValue", "toString", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeString", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "valueOf", "compareValue", "expectedValue", "localeCompare"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/String.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives, } from \"./FromJsConstructor\";\nimport { _NativeJsString } from \"./JsValueConversion\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withPrimitives(Primitives.String)\n    .withClassesToUnbox(_NativeJsString)\n    .build();\nexport class String extends Value(_NativeJsString, IonTypes.STRING, _fromJsConstructor) {\n    constructor(text, annotations = []) {\n        super(text);\n        this._setAnnotations(annotations);\n    }\n    stringValue() {\n        return this.toString();\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeString(this.stringValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof String) {\n            isSupportedType = true;\n            valueToCompare = other.stringValue();\n        }\n        else if (!options.onlyCompareIon) {\n            if (typeof other === \"string\" || other instanceof _NativeJsString) {\n                isSupportedType = true;\n                valueToCompare = other.valueOf();\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        return this.compareValue(valueToCompare) === 0;\n    }\n    compareValue(expectedValue) {\n        return this.stringValue().localeCompare(expectedValue);\n    }\n}\n//# sourceMappingURL=String.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,EAAEC,UAAU,QAAS,qBAAqB;AAC3E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIJ,wBAAwB,CAAC,CAAC,CACpDK,cAAc,CAACJ,UAAU,CAACK,MAAM,CAAC,CACjCC,kBAAkB,CAACL,eAAe,CAAC,CACnCM,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMF,MAAM,SAASH,KAAK,CAACD,eAAe,EAAEH,QAAQ,CAACU,MAAM,EAAEL,kBAAkB,CAAC,CAAC;EACpFM,WAAWA,CAACC,IAAI,EAAEC,WAAW,GAAG,EAAE,EAAE;IAChC,KAAK,CAACD,IAAI,CAAC;IACX,IAAI,CAACE,eAAe,CAACD,WAAW,CAAC;EACrC;EACAE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,WAAW,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC;EAC1C;EACAO,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAYhB,MAAM,EAAE;MACzBsB,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK,CAACR,WAAW,CAAC,CAAC;IACxC,CAAC,MACI,IAAI,CAACS,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYpB,eAAe,EAAE;QAC/D0B,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGP,KAAK,CAACQ,OAAO,CAAC,CAAC;MACpC;IACJ;IACA,IAAI,CAACF,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACG,YAAY,CAACF,cAAc,CAAC,KAAK,CAAC;EAClD;EACAE,YAAYA,CAACC,aAAa,EAAE;IACxB,OAAO,IAAI,CAAClB,WAAW,CAAC,CAAC,CAACmB,aAAa,CAACD,aAAa,CAAC;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}