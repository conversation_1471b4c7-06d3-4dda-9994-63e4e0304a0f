{"ast": null, "code": "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "map": {"version": 3, "names": ["getNodeName", "isHTMLElement", "applyStyles", "_ref", "state", "Object", "keys", "elements", "for<PERSON>ach", "name", "style", "styles", "attributes", "element", "assign", "value", "removeAttribute", "setAttribute", "effect", "_ref2", "initialStyles", "popper", "position", "options", "strategy", "left", "top", "margin", "arrow", "reference", "styleProperties", "hasOwnProperty", "reduce", "property", "attribute", "enabled", "phase", "fn", "requires"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@popperjs/core/lib/modifiers/applyStyles.js"], "sourcesContent": ["import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};"], "mappings": "AAAA,OAAOA,WAAW,MAAM,6BAA6B;AACrD,SAASC,aAAa,QAAQ,4BAA4B,CAAC,CAAC;AAC5D;;AAEA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACtBC,MAAM,CAACC,IAAI,CAACF,KAAK,CAACG,QAAQ,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;IAClD,IAAIC,KAAK,GAAGN,KAAK,CAACO,MAAM,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,IAAIG,UAAU,GAAGR,KAAK,CAACQ,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAII,OAAO,GAAGT,KAAK,CAACG,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;;IAEpC,IAAI,CAACR,aAAa,CAACY,OAAO,CAAC,IAAI,CAACb,WAAW,CAACa,OAAO,CAAC,EAAE;MACpD;IACF,CAAC,CAAC;IACF;IACA;;IAGAR,MAAM,CAACS,MAAM,CAACD,OAAO,CAACH,KAAK,EAAEA,KAAK,CAAC;IACnCL,MAAM,CAACC,IAAI,CAACM,UAAU,CAAC,CAACJ,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC9C,IAAIM,KAAK,GAAGH,UAAU,CAACH,IAAI,CAAC;MAE5B,IAAIM,KAAK,KAAK,KAAK,EAAE;QACnBF,OAAO,CAACG,eAAe,CAACP,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLI,OAAO,CAACI,YAAY,CAACR,IAAI,EAAEM,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC;MACzD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASG,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAIf,KAAK,GAAGe,KAAK,CAACf,KAAK;EACvB,IAAIgB,aAAa,GAAG;IAClBC,MAAM,EAAE;MACNC,QAAQ,EAAElB,KAAK,CAACmB,OAAO,CAACC,QAAQ;MAChCC,IAAI,EAAE,GAAG;MACTC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLN,QAAQ,EAAE;IACZ,CAAC;IACDO,SAAS,EAAE,CAAC;EACd,CAAC;EACDxB,MAAM,CAACS,MAAM,CAACV,KAAK,CAACG,QAAQ,CAACc,MAAM,CAACX,KAAK,EAAEU,aAAa,CAACC,MAAM,CAAC;EAChEjB,KAAK,CAACO,MAAM,GAAGS,aAAa;EAE5B,IAAIhB,KAAK,CAACG,QAAQ,CAACqB,KAAK,EAAE;IACxBvB,MAAM,CAACS,MAAM,CAACV,KAAK,CAACG,QAAQ,CAACqB,KAAK,CAAClB,KAAK,EAAEU,aAAa,CAACQ,KAAK,CAAC;EAChE;EAEA,OAAO,YAAY;IACjBvB,MAAM,CAACC,IAAI,CAACF,KAAK,CAACG,QAAQ,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MAClD,IAAII,OAAO,GAAGT,KAAK,CAACG,QAAQ,CAACE,IAAI,CAAC;MAClC,IAAIG,UAAU,GAAGR,KAAK,CAACQ,UAAU,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7C,IAAIqB,eAAe,GAAGzB,MAAM,CAACC,IAAI,CAACF,KAAK,CAACO,MAAM,CAACoB,cAAc,CAACtB,IAAI,CAAC,GAAGL,KAAK,CAACO,MAAM,CAACF,IAAI,CAAC,GAAGW,aAAa,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjH,IAAIC,KAAK,GAAGoB,eAAe,CAACE,MAAM,CAAC,UAAUtB,KAAK,EAAEuB,QAAQ,EAAE;QAC5DvB,KAAK,CAACuB,QAAQ,CAAC,GAAG,EAAE;QACpB,OAAOvB,KAAK;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,IAAI,CAACT,aAAa,CAACY,OAAO,CAAC,IAAI,CAACb,WAAW,CAACa,OAAO,CAAC,EAAE;QACpD;MACF;MAEAR,MAAM,CAACS,MAAM,CAACD,OAAO,CAACH,KAAK,EAAEA,KAAK,CAAC;MACnCL,MAAM,CAACC,IAAI,CAACM,UAAU,CAAC,CAACJ,OAAO,CAAC,UAAU0B,SAAS,EAAE;QACnDrB,OAAO,CAACG,eAAe,CAACkB,SAAS,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC;;AAGF,eAAe;EACbzB,IAAI,EAAE,aAAa;EACnB0B,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,OAAO;EACdC,EAAE,EAAEnC,WAAW;EACfgB,MAAM,EAAEA,MAAM;EACdoB,QAAQ,EAAE,CAAC,eAAe;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}