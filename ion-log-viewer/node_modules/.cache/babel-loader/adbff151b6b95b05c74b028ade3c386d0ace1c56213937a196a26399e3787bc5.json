{"ast": null, "code": "const createStoreImpl = createState => {\n  let state;\n  const listeners = /* @__PURE__ */new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach(listener => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = {\n    setState,\n    getState,\n    getInitialState,\n    subscribe\n  };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = createState => createState ? createStoreImpl(createState) : createStoreImpl;\nexport { createStore };", "map": {"version": 3, "names": ["createStoreImpl", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "Object", "is", "previousState", "assign", "for<PERSON>ach", "listener", "getState", "getInitialState", "initialState", "subscribe", "add", "delete", "api", "createStore"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAIC,WAAW,IAAK;EACvC,IAAIC,KAAK;EACT,MAAMC,SAAS,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC3C,MAAMC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;IACrC,MAAMC,SAAS,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACJ,KAAK,CAAC,GAAGI,OAAO;IAC1E,IAAI,CAACG,MAAM,CAACC,EAAE,CAACF,SAAS,EAAEN,KAAK,CAAC,EAAE;MAChC,MAAMS,aAAa,GAAGT,KAAK;MAC3BA,KAAK,GAAG,CAACK,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,GAAGC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,EAAEM,SAAS,CAAC;MAC3IL,SAAS,CAACU,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACZ,KAAK,EAAES,aAAa,CAAC,CAAC;IACjE;EACF,CAAC;EACD,MAAMI,QAAQ,GAAGA,CAAA,KAAMb,KAAK;EAC5B,MAAMc,eAAe,GAAGA,CAAA,KAAMC,YAAY;EAC1C,MAAMC,SAAS,GAAIJ,QAAQ,IAAK;IAC9BX,SAAS,CAACgB,GAAG,CAACL,QAAQ,CAAC;IACvB,OAAO,MAAMX,SAAS,CAACiB,MAAM,CAACN,QAAQ,CAAC;EACzC,CAAC;EACD,MAAMO,GAAG,GAAG;IAAEhB,QAAQ;IAAEU,QAAQ;IAAEC,eAAe;IAAEE;EAAU,CAAC;EAC9D,MAAMD,YAAY,GAAGf,KAAK,GAAGD,WAAW,CAACI,QAAQ,EAAEU,QAAQ,EAAEM,GAAG,CAAC;EACjE,OAAOA,GAAG;AACZ,CAAC;AACD,MAAMC,WAAW,GAAIrB,WAAW,IAAKA,WAAW,GAAGD,eAAe,CAACC,WAAW,CAAC,GAAGD,eAAe;AAEjG,SAASsB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}