{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport function Sequence(ionType) {\n  return class extends Value(Array, ionType, FromJsConstructor.NONE) {\n    constructor(children, annotations = []) {\n      super();\n      for (const child of children) {\n        this.push(child);\n      }\n      this._setAnnotations(annotations);\n      return new Proxy(this, {\n        set: function (target, index, value) {\n          if (!(value instanceof Value)) {\n            value = Value.from(value);\n          }\n          target[index] = value;\n          return true;\n        }\n      });\n    }\n    get(...pathElements) {\n      if (pathElements.length === 0) {\n        throw new Error(\"Value#get requires at least one parameter.\");\n      }\n      const [pathHead, ...pathTail] = pathElements;\n      if (typeof pathHead !== \"number\") {\n        throw new Error(`Cannot index into a ${this.getType().name} with a ${typeof pathHead}.`);\n      }\n      const children = this;\n      const maybeChild = children[pathHead];\n      const child = maybeChild === undefined ? null : maybeChild;\n      if (pathTail.length === 0 || child === null) {\n        return child;\n      }\n      return child.get(...pathTail);\n    }\n    elements() {\n      return Object.values(this);\n    }\n    toString() {\n      return \"[\" + this.join(\", \") + \"]\";\n    }\n    static _fromJsValue(jsValue, annotations) {\n      if (!(jsValue instanceof Array)) {\n        throw new Error(`Cannot create a ${this.name} from: ${jsValue.toString()}`);\n      }\n      const children = jsValue.map(child => Value.from(child));\n      return new this(children, annotations);\n    }\n    writeTo(writer) {\n      writer.setAnnotations(this.getAnnotations());\n      writer.stepIn(ionType);\n      for (const child of this) {\n        child.writeTo(writer);\n      }\n      writer.stepOut();\n    }\n    _valueEquals(other, options = {\n      epsilon: null,\n      ignoreAnnotations: false,\n      ignoreTimestampPrecision: false,\n      onlyCompareIon: true\n    }) {\n      let isSupportedType = false;\n      let valueToCompare = null;\n      if (options.onlyCompareIon) {\n        if (other.getType() === IonTypes.LIST || other.getType() === IonTypes.SEXP) {\n          isSupportedType = true;\n          valueToCompare = other.elements();\n        }\n      } else {\n        if (other instanceof Array) {\n          isSupportedType = true;\n          valueToCompare = other;\n        }\n      }\n      if (!isSupportedType) {\n        return false;\n      }\n      let actualSequence = this.elements();\n      let expectedSequence = valueToCompare;\n      if (actualSequence.length !== expectedSequence.length) {\n        return false;\n      }\n      for (let i = 0; i < actualSequence.length; i++) {\n        if (options.onlyCompareIon) {\n          if (!actualSequence[i].ionEquals(expectedSequence[i], options)) {\n            return false;\n          }\n        } else {\n          if (!actualSequence[i].equals(expectedSequence[i])) {\n            return false;\n          }\n        }\n      }\n      return true;\n    }\n  };\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructor", "Value", "Sequence", "ionType", "Array", "NONE", "constructor", "children", "annotations", "child", "push", "_setAnnotations", "Proxy", "set", "target", "index", "value", "from", "get", "pathElements", "length", "Error", "pathHead", "pathTail", "getType", "name", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "elements", "Object", "values", "toString", "join", "_fromJsValue", "jsValue", "map", "writeTo", "writer", "setAnnotations", "getAnnotations", "stepIn", "stepOut", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "LIST", "SEXP", "actualSequence", "expectedSequence", "i", "ionEquals", "equals"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Sequence.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport function Sequence(ionType) {\n    return class extends Value(Array, ionType, FromJsConstructor.NONE) {\n        constructor(children, annotations = []) {\n            super();\n            for (const child of children) {\n                this.push(child);\n            }\n            this._setAnnotations(annotations);\n            return new Proxy(this, {\n                set: function (target, index, value) {\n                    if (!(value instanceof Value)) {\n                        value = Value.from(value);\n                    }\n                    target[index] = value;\n                    return true;\n                },\n            });\n        }\n        get(...pathElements) {\n            if (pathElements.length === 0) {\n                throw new Error(\"Value#get requires at least one parameter.\");\n            }\n            const [pathHead, ...pathTail] = pathElements;\n            if (typeof pathHead !== \"number\") {\n                throw new Error(`Cannot index into a ${this.getType().name} with a ${typeof pathHead}.`);\n            }\n            const children = this;\n            const maybeChild = children[pathHead];\n            const child = maybeChild === undefined ? null : maybeChild;\n            if (pathTail.length === 0 || child === null) {\n                return child;\n            }\n            return child.get(...pathTail);\n        }\n        elements() {\n            return Object.values(this);\n        }\n        toString() {\n            return \"[\" + this.join(\", \") + \"]\";\n        }\n        static _fromJsValue(jsValue, annotations) {\n            if (!(jsValue instanceof Array)) {\n                throw new Error(`Cannot create a ${this.name} from: ${jsValue.toString()}`);\n            }\n            const children = jsValue.map((child) => Value.from(child));\n            return new this(children, annotations);\n        }\n        writeTo(writer) {\n            writer.setAnnotations(this.getAnnotations());\n            writer.stepIn(ionType);\n            for (const child of this) {\n                child.writeTo(writer);\n            }\n            writer.stepOut();\n        }\n        _valueEquals(other, options = {\n            epsilon: null,\n            ignoreAnnotations: false,\n            ignoreTimestampPrecision: false,\n            onlyCompareIon: true,\n        }) {\n            let isSupportedType = false;\n            let valueToCompare = null;\n            if (options.onlyCompareIon) {\n                if (other.getType() === IonTypes.LIST ||\n                    other.getType() === IonTypes.SEXP) {\n                    isSupportedType = true;\n                    valueToCompare = other.elements();\n                }\n            }\n            else {\n                if (other instanceof Array) {\n                    isSupportedType = true;\n                    valueToCompare = other;\n                }\n            }\n            if (!isSupportedType) {\n                return false;\n            }\n            let actualSequence = this.elements();\n            let expectedSequence = valueToCompare;\n            if (actualSequence.length !== expectedSequence.length) {\n                return false;\n            }\n            for (let i = 0; i < actualSequence.length; i++) {\n                if (options.onlyCompareIon) {\n                    if (!actualSequence[i].ionEquals(expectedSequence[i], options)) {\n                        return false;\n                    }\n                }\n                else {\n                    if (!actualSequence[i].equals(expectedSequence[i])) {\n                        return false;\n                    }\n                }\n            }\n            return true;\n        }\n    };\n}\n//# sourceMappingURL=Sequence.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAE;EAC9B,OAAO,cAAcF,KAAK,CAACG,KAAK,EAAED,OAAO,EAAEH,iBAAiB,CAACK,IAAI,CAAC,CAAC;IAC/DC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,EAAE,EAAE;MACpC,KAAK,CAAC,CAAC;MACP,KAAK,MAAMC,KAAK,IAAIF,QAAQ,EAAE;QAC1B,IAAI,CAACG,IAAI,CAACD,KAAK,CAAC;MACpB;MACA,IAAI,CAACE,eAAe,CAACH,WAAW,CAAC;MACjC,OAAO,IAAII,KAAK,CAAC,IAAI,EAAE;QACnBC,GAAG,EAAE,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;UACjC,IAAI,EAAEA,KAAK,YAAYf,KAAK,CAAC,EAAE;YAC3Be,KAAK,GAAGf,KAAK,CAACgB,IAAI,CAACD,KAAK,CAAC;UAC7B;UACAF,MAAM,CAACC,KAAK,CAAC,GAAGC,KAAK;UACrB,OAAO,IAAI;QACf;MACJ,CAAC,CAAC;IACN;IACAE,GAAGA,CAAC,GAAGC,YAAY,EAAE;MACjB,IAAIA,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;MACjE;MACA,MAAM,CAACC,QAAQ,EAAE,GAAGC,QAAQ,CAAC,GAAGJ,YAAY;MAC5C,IAAI,OAAOG,QAAQ,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAID,KAAK,CAAC,uBAAuB,IAAI,CAACG,OAAO,CAAC,CAAC,CAACC,IAAI,WAAW,OAAOH,QAAQ,GAAG,CAAC;MAC5F;MACA,MAAMf,QAAQ,GAAG,IAAI;MACrB,MAAMmB,UAAU,GAAGnB,QAAQ,CAACe,QAAQ,CAAC;MACrC,MAAMb,KAAK,GAAGiB,UAAU,KAAKC,SAAS,GAAG,IAAI,GAAGD,UAAU;MAC1D,IAAIH,QAAQ,CAACH,MAAM,KAAK,CAAC,IAAIX,KAAK,KAAK,IAAI,EAAE;QACzC,OAAOA,KAAK;MAChB;MACA,OAAOA,KAAK,CAACS,GAAG,CAAC,GAAGK,QAAQ,CAAC;IACjC;IACAK,QAAQA,CAAA,EAAG;MACP,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC9B;IACAC,QAAQA,CAAA,EAAG;MACP,OAAO,GAAG,GAAG,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACtC;IACA,OAAOC,YAAYA,CAACC,OAAO,EAAE1B,WAAW,EAAE;MACtC,IAAI,EAAE0B,OAAO,YAAY9B,KAAK,CAAC,EAAE;QAC7B,MAAM,IAAIiB,KAAK,CAAC,mBAAmB,IAAI,CAACI,IAAI,UAAUS,OAAO,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC;MAC/E;MACA,MAAMxB,QAAQ,GAAG2B,OAAO,CAACC,GAAG,CAAE1B,KAAK,IAAKR,KAAK,CAACgB,IAAI,CAACR,KAAK,CAAC,CAAC;MAC1D,OAAO,IAAI,IAAI,CAACF,QAAQ,EAAEC,WAAW,CAAC;IAC1C;IACA4B,OAAOA,CAACC,MAAM,EAAE;MACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;MAC5CF,MAAM,CAACG,MAAM,CAACrC,OAAO,CAAC;MACtB,KAAK,MAAMM,KAAK,IAAI,IAAI,EAAE;QACtBA,KAAK,CAAC2B,OAAO,CAACC,MAAM,CAAC;MACzB;MACAA,MAAM,CAACI,OAAO,CAAC,CAAC;IACpB;IACAC,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;MAC1BC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,KAAK;MACxBC,wBAAwB,EAAE,KAAK;MAC/BC,cAAc,EAAE;IACpB,CAAC,EAAE;MACC,IAAIC,eAAe,GAAG,KAAK;MAC3B,IAAIC,cAAc,GAAG,IAAI;MACzB,IAAIN,OAAO,CAACI,cAAc,EAAE;QACxB,IAAIL,KAAK,CAACnB,OAAO,CAAC,CAAC,KAAKzB,QAAQ,CAACoD,IAAI,IACjCR,KAAK,CAACnB,OAAO,CAAC,CAAC,KAAKzB,QAAQ,CAACqD,IAAI,EAAE;UACnCH,eAAe,GAAG,IAAI;UACtBC,cAAc,GAAGP,KAAK,CAACf,QAAQ,CAAC,CAAC;QACrC;MACJ,CAAC,MACI;QACD,IAAIe,KAAK,YAAYvC,KAAK,EAAE;UACxB6C,eAAe,GAAG,IAAI;UACtBC,cAAc,GAAGP,KAAK;QAC1B;MACJ;MACA,IAAI,CAACM,eAAe,EAAE;QAClB,OAAO,KAAK;MAChB;MACA,IAAII,cAAc,GAAG,IAAI,CAACzB,QAAQ,CAAC,CAAC;MACpC,IAAI0B,gBAAgB,GAAGJ,cAAc;MACrC,IAAIG,cAAc,CAACjC,MAAM,KAAKkC,gBAAgB,CAAClC,MAAM,EAAE;QACnD,OAAO,KAAK;MAChB;MACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACjC,MAAM,EAAEmC,CAAC,EAAE,EAAE;QAC5C,IAAIX,OAAO,CAACI,cAAc,EAAE;UACxB,IAAI,CAACK,cAAc,CAACE,CAAC,CAAC,CAACC,SAAS,CAACF,gBAAgB,CAACC,CAAC,CAAC,EAAEX,OAAO,CAAC,EAAE;YAC5D,OAAO,KAAK;UAChB;QACJ,CAAC,MACI;UACD,IAAI,CAACS,cAAc,CAACE,CAAC,CAAC,CAACE,MAAM,CAACH,gBAAgB,CAACC,CAAC,CAAC,CAAC,EAAE;YAChD,OAAO,KAAK;UAChB;QACJ;MACJ;MACA,OAAO,IAAI;IACf;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}