{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { Sequence } from \"./Sequence\";\nexport class List extends Sequence(IonTypes.LIST) {\n  constructor(children, annotations = []) {\n    super(children, annotations);\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "Sequence", "List", "LIST", "constructor", "children", "annotations"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/List.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { Sequence } from \"./Sequence\";\nexport class List extends Sequence(IonTypes.LIST) {\n    constructor(children, annotations = []) {\n        super(children, annotations);\n    }\n}\n//# sourceMappingURL=List.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,IAAI,SAASD,QAAQ,CAACD,QAAQ,CAACG,IAAI,CAAC,CAAC;EAC9CC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,EAAE,EAAE;IACpC,KAAK,CAACD,QAAQ,EAAEC,WAAW,CAAC;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}