{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { SharedSymbolTable } from \"./IonSharedSymbolTable\";\nexport class SubstituteSymbolTable extends SharedSymbolTable {\n  constructor(length) {\n    if (length < 0) {\n      throw new Error(\"Cannot instantiate a SubstituteSymbolTable with a negative length. (\" + length + \")\");\n    }\n    super(\"_substitute\", -1, []);\n    this._numberOfSymbols = length;\n  }\n  getSymbolText(symbolId) {\n    if (symbolId < 0) {\n      throw new Error(`Index ${symbolId} is out of bounds for the SharedSymbolTable name=${this.name}, version=${this.version}`);\n    }\n    return undefined;\n  }\n  getSymbolId(text) {\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["SharedSymbolTable", "SubstituteSymbolTable", "constructor", "length", "Error", "_numberOfSymbols", "getSymbolText", "symbolId", "name", "version", "undefined", "getSymbolId", "text"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSubstituteSymbolTable.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { SharedSymbolTable } from \"./IonSharedSymbolTable\";\nexport class SubstituteSymbolTable extends SharedSymbolTable {\n    constructor(length) {\n        if (length < 0) {\n            throw new Error(\"Cannot instantiate a SubstituteSymbolTable with a negative length. (\" +\n                length +\n                \")\");\n        }\n        super(\"_substitute\", -1, []);\n        this._numberOfSymbols = length;\n    }\n    getSymbolText(symbolId) {\n        if (symbolId < 0) {\n            throw new Error(`Index ${symbolId} is out of bounds for the SharedSymbolTable name=${this.name}, version=${this.version}`);\n        }\n        return undefined;\n    }\n    getSymbolId(text) {\n        return undefined;\n    }\n}\n//# sourceMappingURL=IonSubstituteSymbolTable.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,MAAMC,qBAAqB,SAASD,iBAAiB,CAAC;EACzDE,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIC,KAAK,CAAC,sEAAsE,GAClFD,MAAM,GACN,GAAG,CAAC;IACZ;IACA,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5B,IAAI,CAACE,gBAAgB,GAAGF,MAAM;EAClC;EACAG,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACd,MAAM,IAAIH,KAAK,CAAC,SAASG,QAAQ,oDAAoD,IAAI,CAACC,IAAI,aAAa,IAAI,CAACC,OAAO,EAAE,CAAC;IAC9H;IACA,OAAOC,SAAS;EACpB;EACAC,WAAWA,CAACC,IAAI,EAAE;IACd,OAAOF,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}