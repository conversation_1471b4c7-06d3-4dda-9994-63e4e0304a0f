{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { Import } from \"./IonImport\";\nimport { SharedSymbolTable } from \"./IonSharedSymbolTable\";\nconst systemSymbolTable = new SharedSymbolTable(\"$ion\", 1, [\"$ion\", \"$ion_1_0\", \"$ion_symbol_table\", \"name\", \"version\", \"imports\", \"symbols\", \"max_id\", \"$ion_shared_symbol_table\"]);\nexport function getSystemSymbolTable() {\n  return systemSymbolTable;\n}\nexport function getSystemSymbolTableImport() {\n  return new Import(null, getSystemSymbolTable());\n}", "map": {"version": 3, "names": ["Import", "SharedSymbolTable", "systemSymbolTable", "getSystemSymbolTable", "getSystemSymbolTableImport"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSystemSymbolTable.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { Import } from \"./IonImport\";\nimport { SharedSymbolTable } from \"./IonSharedSymbolTable\";\nconst systemSymbolTable = new SharedSymbolTable(\"$ion\", 1, [\n    \"$ion\",\n    \"$ion_1_0\",\n    \"$ion_symbol_table\",\n    \"name\",\n    \"version\",\n    \"imports\",\n    \"symbols\",\n    \"max_id\",\n    \"$ion_shared_symbol_table\",\n]);\nexport function getSystemSymbolTable() {\n    return systemSymbolTable;\n}\nexport function getSystemSymbolTableImport() {\n    return new Import(null, getSystemSymbolTable());\n}\n//# sourceMappingURL=IonSystemSymbolTable.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,MAAMC,iBAAiB,GAAG,IAAID,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CACvD,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,0BAA0B,CAC7B,CAAC;AACF,OAAO,SAASE,oBAAoBA,CAAA,EAAG;EACnC,OAAOD,iBAAiB;AAC5B;AACA,OAAO,SAASE,0BAA0BA,CAAA,EAAG;EACzC,OAAO,IAAIJ,MAAM,CAAC,IAAI,EAAEG,oBAAoB,CAAC,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}