{"ast": null, "code": "import { Loader, FileLoader, TextureLoader, Group, Color, Matrix4, BufferGeometry, Float32BufferAttribute, Mesh, MeshPhongMaterial, BufferAttribute, MeshStandardMaterial, RepeatWrapping, ClampToEdgeWrapping, MirroredRepeatWrapping, LinearFilter, LinearMipmapLinearFilter, NearestFilter } from \"three\";\nimport { unzipSync } from \"fflate\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass ThreeMFLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.availableExtensions = [];\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    const scope = this;\n    const textureLoader = new TextureLoader(this.manager);\n    function loadDocument(data2) {\n      let zip = null;\n      let file = null;\n      let relsName;\n      let modelRelsName;\n      const modelPartNames = [];\n      const texturesPartNames = [];\n      let modelRels;\n      const modelParts = {};\n      const printTicketParts = {};\n      const texturesParts = {};\n      const otherParts = {};\n      try {\n        zip = unzipSync(new Uint8Array(data2));\n      } catch (e) {\n        if (e instanceof ReferenceError) {\n          console.error(\"THREE.3MFLoader: fflate missing and file is compressed.\");\n          return null;\n        }\n      }\n      for (file in zip) {\n        if (file.match(/\\_rels\\/.rels$/)) {\n          relsName = file;\n        } else if (file.match(/3D\\/_rels\\/.*\\.model\\.rels$/)) {\n          modelRelsName = file;\n        } else if (file.match(/^3D\\/.*\\.model$/)) {\n          modelPartNames.push(file);\n        } else if (file.match(/^3D\\/Metadata\\/.*\\.xml$/)) ;else if (file.match(/^3D\\/Textures?\\/.*/)) {\n          texturesPartNames.push(file);\n        } else if (file.match(/^3D\\/Other\\/.*/)) ;\n      }\n      const relsView = zip[relsName];\n      const relsFileText = decodeText(relsView);\n      const rels = parseRelsXml(relsFileText);\n      if (modelRelsName) {\n        const relsView2 = zip[modelRelsName];\n        const relsFileText2 = decodeText(relsView2);\n        modelRels = parseRelsXml(relsFileText2);\n      }\n      for (let i = 0; i < modelPartNames.length; i++) {\n        const modelPart = modelPartNames[i];\n        const view = zip[modelPart];\n        const fileText = decodeText(view);\n        const xmlData = new DOMParser().parseFromString(fileText, \"application/xml\");\n        if (xmlData.documentElement.nodeName.toLowerCase() !== \"model\") {\n          console.error(\"THREE.3MFLoader: Error loading 3MF - no 3MF document found: \", modelPart);\n        }\n        const modelNode = xmlData.querySelector(\"model\");\n        const extensions = {};\n        for (let i2 = 0; i2 < modelNode.attributes.length; i2++) {\n          const attr = modelNode.attributes[i2];\n          if (attr.name.match(/^xmlns:(.+)$/)) {\n            extensions[attr.value] = RegExp.$1;\n          }\n        }\n        const modelData = parseModelNode(modelNode);\n        modelData[\"xml\"] = modelNode;\n        if (0 < Object.keys(extensions).length) {\n          modelData[\"extensions\"] = extensions;\n        }\n        modelParts[modelPart] = modelData;\n      }\n      for (let i = 0; i < texturesPartNames.length; i++) {\n        const texturesPartName = texturesPartNames[i];\n        texturesParts[texturesPartName] = zip[texturesPartName].buffer;\n      }\n      return {\n        rels,\n        modelRels,\n        model: modelParts,\n        printTicket: printTicketParts,\n        texture: texturesParts,\n        other: otherParts\n      };\n    }\n    function parseRelsXml(relsFileText) {\n      const relationships = [];\n      const relsXmlData = new DOMParser().parseFromString(relsFileText, \"application/xml\");\n      const relsNodes = relsXmlData.querySelectorAll(\"Relationship\");\n      for (let i = 0; i < relsNodes.length; i++) {\n        const relsNode = relsNodes[i];\n        const relationship = {\n          target: relsNode.getAttribute(\"Target\"),\n          //required\n          id: relsNode.getAttribute(\"Id\"),\n          //required\n          type: relsNode.getAttribute(\"Type\")\n          //required\n        };\n        relationships.push(relationship);\n      }\n      return relationships;\n    }\n    function parseMetadataNodes(metadataNodes) {\n      const metadataData = {};\n      for (let i = 0; i < metadataNodes.length; i++) {\n        const metadataNode = metadataNodes[i];\n        const name = metadataNode.getAttribute(\"name\");\n        const validNames = [\"Title\", \"Designer\", \"Description\", \"Copyright\", \"LicenseTerms\", \"Rating\", \"CreationDate\", \"ModificationDate\"];\n        if (0 <= validNames.indexOf(name)) {\n          metadataData[name] = metadataNode.textContent;\n        }\n      }\n      return metadataData;\n    }\n    function parseBasematerialsNode(basematerialsNode) {\n      const basematerialsData = {\n        id: basematerialsNode.getAttribute(\"id\"),\n        // required\n        basematerials: []\n      };\n      const basematerialNodes = basematerialsNode.querySelectorAll(\"base\");\n      for (let i = 0; i < basematerialNodes.length; i++) {\n        const basematerialNode = basematerialNodes[i];\n        const basematerialData = parseBasematerialNode(basematerialNode);\n        basematerialData.index = i;\n        basematerialsData.basematerials.push(basematerialData);\n      }\n      return basematerialsData;\n    }\n    function parseTexture2DNode(texture2DNode) {\n      const texture2dData = {\n        id: texture2DNode.getAttribute(\"id\"),\n        // required\n        path: texture2DNode.getAttribute(\"path\"),\n        // required\n        contenttype: texture2DNode.getAttribute(\"contenttype\"),\n        // required\n        tilestyleu: texture2DNode.getAttribute(\"tilestyleu\"),\n        tilestylev: texture2DNode.getAttribute(\"tilestylev\"),\n        filter: texture2DNode.getAttribute(\"filter\")\n      };\n      return texture2dData;\n    }\n    function parseTextures2DGroupNode(texture2DGroupNode) {\n      const texture2DGroupData = {\n        id: texture2DGroupNode.getAttribute(\"id\"),\n        // required\n        texid: texture2DGroupNode.getAttribute(\"texid\"),\n        // required\n        displaypropertiesid: texture2DGroupNode.getAttribute(\"displaypropertiesid\")\n      };\n      const tex2coordNodes = texture2DGroupNode.querySelectorAll(\"tex2coord\");\n      const uvs = [];\n      for (let i = 0; i < tex2coordNodes.length; i++) {\n        const tex2coordNode = tex2coordNodes[i];\n        const u = tex2coordNode.getAttribute(\"u\");\n        const v = tex2coordNode.getAttribute(\"v\");\n        uvs.push(parseFloat(u), parseFloat(v));\n      }\n      texture2DGroupData[\"uvs\"] = new Float32Array(uvs);\n      return texture2DGroupData;\n    }\n    function parseColorGroupNode(colorGroupNode) {\n      const colorGroupData = {\n        id: colorGroupNode.getAttribute(\"id\"),\n        // required\n        displaypropertiesid: colorGroupNode.getAttribute(\"displaypropertiesid\")\n      };\n      const colorNodes = colorGroupNode.querySelectorAll(\"color\");\n      const colors = [];\n      const colorObject = new Color();\n      for (let i = 0; i < colorNodes.length; i++) {\n        const colorNode = colorNodes[i];\n        const color = colorNode.getAttribute(\"color\");\n        colorObject.setStyle(color.substring(0, 7));\n        colorObject.convertSRGBToLinear();\n        colors.push(colorObject.r, colorObject.g, colorObject.b);\n      }\n      colorGroupData[\"colors\"] = new Float32Array(colors);\n      return colorGroupData;\n    }\n    function parseMetallicDisplaypropertiesNode(metallicDisplaypropetiesNode) {\n      const metallicDisplaypropertiesData = {\n        id: metallicDisplaypropetiesNode.getAttribute(\"id\")\n        // required\n      };\n      const metallicNodes = metallicDisplaypropetiesNode.querySelectorAll(\"pbmetallic\");\n      const metallicData = [];\n      for (let i = 0; i < metallicNodes.length; i++) {\n        const metallicNode = metallicNodes[i];\n        metallicData.push({\n          name: metallicNode.getAttribute(\"name\"),\n          // required\n          metallicness: parseFloat(metallicNode.getAttribute(\"metallicness\")),\n          // required\n          roughness: parseFloat(metallicNode.getAttribute(\"roughness\"))\n          // required\n        });\n      }\n      metallicDisplaypropertiesData.data = metallicData;\n      return metallicDisplaypropertiesData;\n    }\n    function parseBasematerialNode(basematerialNode) {\n      const basematerialData = {};\n      basematerialData[\"name\"] = basematerialNode.getAttribute(\"name\");\n      basematerialData[\"displaycolor\"] = basematerialNode.getAttribute(\"displaycolor\");\n      basematerialData[\"displaypropertiesid\"] = basematerialNode.getAttribute(\"displaypropertiesid\");\n      return basematerialData;\n    }\n    function parseMeshNode(meshNode) {\n      const meshData = {};\n      const vertices = [];\n      const vertexNodes = meshNode.querySelectorAll(\"vertices vertex\");\n      for (let i = 0; i < vertexNodes.length; i++) {\n        const vertexNode = vertexNodes[i];\n        const x = vertexNode.getAttribute(\"x\");\n        const y = vertexNode.getAttribute(\"y\");\n        const z = vertexNode.getAttribute(\"z\");\n        vertices.push(parseFloat(x), parseFloat(y), parseFloat(z));\n      }\n      meshData[\"vertices\"] = new Float32Array(vertices);\n      const triangleProperties = [];\n      const triangles = [];\n      const triangleNodes = meshNode.querySelectorAll(\"triangles triangle\");\n      for (let i = 0; i < triangleNodes.length; i++) {\n        const triangleNode = triangleNodes[i];\n        const v1 = triangleNode.getAttribute(\"v1\");\n        const v2 = triangleNode.getAttribute(\"v2\");\n        const v3 = triangleNode.getAttribute(\"v3\");\n        const p1 = triangleNode.getAttribute(\"p1\");\n        const p2 = triangleNode.getAttribute(\"p2\");\n        const p3 = triangleNode.getAttribute(\"p3\");\n        const pid = triangleNode.getAttribute(\"pid\");\n        const triangleProperty = {};\n        triangleProperty[\"v1\"] = parseInt(v1, 10);\n        triangleProperty[\"v2\"] = parseInt(v2, 10);\n        triangleProperty[\"v3\"] = parseInt(v3, 10);\n        triangles.push(triangleProperty[\"v1\"], triangleProperty[\"v2\"], triangleProperty[\"v3\"]);\n        if (p1) {\n          triangleProperty[\"p1\"] = parseInt(p1, 10);\n        }\n        if (p2) {\n          triangleProperty[\"p2\"] = parseInt(p2, 10);\n        }\n        if (p3) {\n          triangleProperty[\"p3\"] = parseInt(p3, 10);\n        }\n        if (pid) {\n          triangleProperty[\"pid\"] = pid;\n        }\n        if (0 < Object.keys(triangleProperty).length) {\n          triangleProperties.push(triangleProperty);\n        }\n      }\n      meshData[\"triangleProperties\"] = triangleProperties;\n      meshData[\"triangles\"] = new Uint32Array(triangles);\n      return meshData;\n    }\n    function parseComponentsNode(componentsNode) {\n      const components = [];\n      const componentNodes = componentsNode.querySelectorAll(\"component\");\n      for (let i = 0; i < componentNodes.length; i++) {\n        const componentNode = componentNodes[i];\n        const componentData = parseComponentNode(componentNode);\n        components.push(componentData);\n      }\n      return components;\n    }\n    function parseComponentNode(componentNode) {\n      const componentData = {};\n      componentData[\"objectId\"] = componentNode.getAttribute(\"objectid\");\n      const transform = componentNode.getAttribute(\"transform\");\n      if (transform) {\n        componentData[\"transform\"] = parseTransform(transform);\n      }\n      return componentData;\n    }\n    function parseTransform(transform) {\n      const t = [];\n      transform.split(\" \").forEach(function (s) {\n        t.push(parseFloat(s));\n      });\n      const matrix = new Matrix4();\n      matrix.set(t[0], t[3], t[6], t[9], t[1], t[4], t[7], t[10], t[2], t[5], t[8], t[11], 0, 0, 0, 1);\n      return matrix;\n    }\n    function parseObjectNode(objectNode) {\n      const objectData = {\n        type: objectNode.getAttribute(\"type\")\n      };\n      const id = objectNode.getAttribute(\"id\");\n      if (id) {\n        objectData[\"id\"] = id;\n      }\n      const pid = objectNode.getAttribute(\"pid\");\n      if (pid) {\n        objectData[\"pid\"] = pid;\n      }\n      const pindex = objectNode.getAttribute(\"pindex\");\n      if (pindex) {\n        objectData[\"pindex\"] = pindex;\n      }\n      const thumbnail = objectNode.getAttribute(\"thumbnail\");\n      if (thumbnail) {\n        objectData[\"thumbnail\"] = thumbnail;\n      }\n      const partnumber = objectNode.getAttribute(\"partnumber\");\n      if (partnumber) {\n        objectData[\"partnumber\"] = partnumber;\n      }\n      const name = objectNode.getAttribute(\"name\");\n      if (name) {\n        objectData[\"name\"] = name;\n      }\n      const meshNode = objectNode.querySelector(\"mesh\");\n      if (meshNode) {\n        objectData[\"mesh\"] = parseMeshNode(meshNode);\n      }\n      const componentsNode = objectNode.querySelector(\"components\");\n      if (componentsNode) {\n        objectData[\"components\"] = parseComponentsNode(componentsNode);\n      }\n      return objectData;\n    }\n    function parseResourcesNode(resourcesNode) {\n      const resourcesData = {};\n      resourcesData[\"basematerials\"] = {};\n      const basematerialsNodes = resourcesNode.querySelectorAll(\"basematerials\");\n      for (let i = 0; i < basematerialsNodes.length; i++) {\n        const basematerialsNode = basematerialsNodes[i];\n        const basematerialsData = parseBasematerialsNode(basematerialsNode);\n        resourcesData[\"basematerials\"][basematerialsData[\"id\"]] = basematerialsData;\n      }\n      resourcesData[\"texture2d\"] = {};\n      const textures2DNodes = resourcesNode.querySelectorAll(\"texture2d\");\n      for (let i = 0; i < textures2DNodes.length; i++) {\n        const textures2DNode = textures2DNodes[i];\n        const texture2DData = parseTexture2DNode(textures2DNode);\n        resourcesData[\"texture2d\"][texture2DData[\"id\"]] = texture2DData;\n      }\n      resourcesData[\"colorgroup\"] = {};\n      const colorGroupNodes = resourcesNode.querySelectorAll(\"colorgroup\");\n      for (let i = 0; i < colorGroupNodes.length; i++) {\n        const colorGroupNode = colorGroupNodes[i];\n        const colorGroupData = parseColorGroupNode(colorGroupNode);\n        resourcesData[\"colorgroup\"][colorGroupData[\"id\"]] = colorGroupData;\n      }\n      resourcesData[\"pbmetallicdisplayproperties\"] = {};\n      const pbmetallicdisplaypropertiesNodes = resourcesNode.querySelectorAll(\"pbmetallicdisplayproperties\");\n      for (let i = 0; i < pbmetallicdisplaypropertiesNodes.length; i++) {\n        const pbmetallicdisplaypropertiesNode = pbmetallicdisplaypropertiesNodes[i];\n        const pbmetallicdisplaypropertiesData = parseMetallicDisplaypropertiesNode(pbmetallicdisplaypropertiesNode);\n        resourcesData[\"pbmetallicdisplayproperties\"][pbmetallicdisplaypropertiesData[\"id\"]] = pbmetallicdisplaypropertiesData;\n      }\n      resourcesData[\"texture2dgroup\"] = {};\n      const textures2DGroupNodes = resourcesNode.querySelectorAll(\"texture2dgroup\");\n      for (let i = 0; i < textures2DGroupNodes.length; i++) {\n        const textures2DGroupNode = textures2DGroupNodes[i];\n        const textures2DGroupData = parseTextures2DGroupNode(textures2DGroupNode);\n        resourcesData[\"texture2dgroup\"][textures2DGroupData[\"id\"]] = textures2DGroupData;\n      }\n      resourcesData[\"object\"] = {};\n      const objectNodes = resourcesNode.querySelectorAll(\"object\");\n      for (let i = 0; i < objectNodes.length; i++) {\n        const objectNode = objectNodes[i];\n        const objectData = parseObjectNode(objectNode);\n        resourcesData[\"object\"][objectData[\"id\"]] = objectData;\n      }\n      return resourcesData;\n    }\n    function parseBuildNode(buildNode) {\n      const buildData = [];\n      const itemNodes = buildNode.querySelectorAll(\"item\");\n      for (let i = 0; i < itemNodes.length; i++) {\n        const itemNode = itemNodes[i];\n        const buildItem = {\n          objectId: itemNode.getAttribute(\"objectid\")\n        };\n        const transform = itemNode.getAttribute(\"transform\");\n        if (transform) {\n          buildItem[\"transform\"] = parseTransform(transform);\n        }\n        buildData.push(buildItem);\n      }\n      return buildData;\n    }\n    function parseModelNode(modelNode) {\n      const modelData = {\n        unit: modelNode.getAttribute(\"unit\") || \"millimeter\"\n      };\n      const metadataNodes = modelNode.querySelectorAll(\"metadata\");\n      if (metadataNodes) {\n        modelData[\"metadata\"] = parseMetadataNodes(metadataNodes);\n      }\n      const resourcesNode = modelNode.querySelector(\"resources\");\n      if (resourcesNode) {\n        modelData[\"resources\"] = parseResourcesNode(resourcesNode);\n      }\n      const buildNode = modelNode.querySelector(\"build\");\n      if (buildNode) {\n        modelData[\"build\"] = parseBuildNode(buildNode);\n      }\n      return modelData;\n    }\n    function buildTexture(texture2dgroup, objects2, modelData, textureData) {\n      const texid = texture2dgroup.texid;\n      const texture2ds = modelData.resources.texture2d;\n      const texture2d = texture2ds[texid];\n      if (texture2d) {\n        const data2 = textureData[texture2d.path];\n        const type = texture2d.contenttype;\n        const blob = new Blob([data2], {\n          type\n        });\n        const sourceURI = URL.createObjectURL(blob);\n        const texture = textureLoader.load(sourceURI, function () {\n          URL.revokeObjectURL(sourceURI);\n        });\n        if (\"colorSpace\" in texture) texture.colorSpace = \"srgb\";else texture.encoding = 3001;\n        switch (texture2d.tilestyleu) {\n          case \"wrap\":\n            texture.wrapS = RepeatWrapping;\n            break;\n          case \"mirror\":\n            texture.wrapS = MirroredRepeatWrapping;\n            break;\n          case \"none\":\n          case \"clamp\":\n            texture.wrapS = ClampToEdgeWrapping;\n            break;\n          default:\n            texture.wrapS = RepeatWrapping;\n        }\n        switch (texture2d.tilestylev) {\n          case \"wrap\":\n            texture.wrapT = RepeatWrapping;\n            break;\n          case \"mirror\":\n            texture.wrapT = MirroredRepeatWrapping;\n            break;\n          case \"none\":\n          case \"clamp\":\n            texture.wrapT = ClampToEdgeWrapping;\n            break;\n          default:\n            texture.wrapT = RepeatWrapping;\n        }\n        switch (texture2d.filter) {\n          case \"auto\":\n            texture.magFilter = LinearFilter;\n            texture.minFilter = LinearMipmapLinearFilter;\n            break;\n          case \"linear\":\n            texture.magFilter = LinearFilter;\n            texture.minFilter = LinearFilter;\n            break;\n          case \"nearest\":\n            texture.magFilter = NearestFilter;\n            texture.minFilter = NearestFilter;\n            break;\n          default:\n            texture.magFilter = LinearFilter;\n            texture.minFilter = LinearMipmapLinearFilter;\n        }\n        return texture;\n      } else {\n        return null;\n      }\n    }\n    function buildBasematerialsMeshes(basematerials, triangleProperties, meshData, objects2, modelData, textureData, objectData) {\n      const objectPindex = objectData.pindex;\n      const materialMap = {};\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i];\n        const pindex = triangleProperty.p1 !== void 0 ? triangleProperty.p1 : objectPindex;\n        if (materialMap[pindex] === void 0) materialMap[pindex] = [];\n        materialMap[pindex].push(triangleProperty);\n      }\n      const keys = Object.keys(materialMap);\n      const meshes = [];\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const materialIndex = keys[i];\n        const trianglePropertiesProps = materialMap[materialIndex];\n        const basematerialData = basematerials.basematerials[materialIndex];\n        const material = getBuild(basematerialData, objects2, modelData, textureData, objectData, buildBasematerial);\n        const geometry = new BufferGeometry();\n        const positionData = [];\n        const vertices = meshData.vertices;\n        for (let j = 0, jl = trianglePropertiesProps.length; j < jl; j++) {\n          const triangleProperty = trianglePropertiesProps[j];\n          positionData.push(vertices[triangleProperty.v1 * 3 + 0]);\n          positionData.push(vertices[triangleProperty.v1 * 3 + 1]);\n          positionData.push(vertices[triangleProperty.v1 * 3 + 2]);\n          positionData.push(vertices[triangleProperty.v2 * 3 + 0]);\n          positionData.push(vertices[triangleProperty.v2 * 3 + 1]);\n          positionData.push(vertices[triangleProperty.v2 * 3 + 2]);\n          positionData.push(vertices[triangleProperty.v3 * 3 + 0]);\n          positionData.push(vertices[triangleProperty.v3 * 3 + 1]);\n          positionData.push(vertices[triangleProperty.v3 * 3 + 2]);\n        }\n        geometry.setAttribute(\"position\", new Float32BufferAttribute(positionData, 3));\n        const mesh = new Mesh(geometry, material);\n        meshes.push(mesh);\n      }\n      return meshes;\n    }\n    function buildTexturedMesh(texture2dgroup, triangleProperties, meshData, objects2, modelData, textureData, objectData) {\n      const geometry = new BufferGeometry();\n      const positionData = [];\n      const uvData = [];\n      const vertices = meshData.vertices;\n      const uvs = texture2dgroup.uvs;\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i];\n        positionData.push(vertices[triangleProperty.v1 * 3 + 0]);\n        positionData.push(vertices[triangleProperty.v1 * 3 + 1]);\n        positionData.push(vertices[triangleProperty.v1 * 3 + 2]);\n        positionData.push(vertices[triangleProperty.v2 * 3 + 0]);\n        positionData.push(vertices[triangleProperty.v2 * 3 + 1]);\n        positionData.push(vertices[triangleProperty.v2 * 3 + 2]);\n        positionData.push(vertices[triangleProperty.v3 * 3 + 0]);\n        positionData.push(vertices[triangleProperty.v3 * 3 + 1]);\n        positionData.push(vertices[triangleProperty.v3 * 3 + 2]);\n        uvData.push(uvs[triangleProperty.p1 * 2 + 0]);\n        uvData.push(uvs[triangleProperty.p1 * 2 + 1]);\n        uvData.push(uvs[triangleProperty.p2 * 2 + 0]);\n        uvData.push(uvs[triangleProperty.p2 * 2 + 1]);\n        uvData.push(uvs[triangleProperty.p3 * 2 + 0]);\n        uvData.push(uvs[triangleProperty.p3 * 2 + 1]);\n      }\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(positionData, 3));\n      geometry.setAttribute(\"uv\", new Float32BufferAttribute(uvData, 2));\n      const texture = getBuild(texture2dgroup, objects2, modelData, textureData, objectData, buildTexture);\n      const material = new MeshPhongMaterial({\n        map: texture,\n        flatShading: true\n      });\n      const mesh = new Mesh(geometry, material);\n      return mesh;\n    }\n    function buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects2, modelData, objectData) {\n      const geometry = new BufferGeometry();\n      const positionData = [];\n      const colorData = [];\n      const vertices = meshData.vertices;\n      const colors = colorgroup.colors;\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i];\n        const v1 = triangleProperty.v1;\n        const v2 = triangleProperty.v2;\n        const v3 = triangleProperty.v3;\n        positionData.push(vertices[v1 * 3 + 0]);\n        positionData.push(vertices[v1 * 3 + 1]);\n        positionData.push(vertices[v1 * 3 + 2]);\n        positionData.push(vertices[v2 * 3 + 0]);\n        positionData.push(vertices[v2 * 3 + 1]);\n        positionData.push(vertices[v2 * 3 + 2]);\n        positionData.push(vertices[v3 * 3 + 0]);\n        positionData.push(vertices[v3 * 3 + 1]);\n        positionData.push(vertices[v3 * 3 + 2]);\n        const p1 = triangleProperty.p1 !== void 0 ? triangleProperty.p1 : objectData.pindex;\n        const p2 = triangleProperty.p2 !== void 0 ? triangleProperty.p2 : p1;\n        const p3 = triangleProperty.p3 !== void 0 ? triangleProperty.p3 : p1;\n        colorData.push(colors[p1 * 3 + 0]);\n        colorData.push(colors[p1 * 3 + 1]);\n        colorData.push(colors[p1 * 3 + 2]);\n        colorData.push(colors[p2 * 3 + 0]);\n        colorData.push(colors[p2 * 3 + 1]);\n        colorData.push(colors[p2 * 3 + 2]);\n        colorData.push(colors[p3 * 3 + 0]);\n        colorData.push(colors[p3 * 3 + 1]);\n        colorData.push(colors[p3 * 3 + 2]);\n      }\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(positionData, 3));\n      geometry.setAttribute(\"color\", new Float32BufferAttribute(colorData, 3));\n      const material = new MeshPhongMaterial({\n        vertexColors: true,\n        flatShading: true\n      });\n      const mesh = new Mesh(geometry, material);\n      return mesh;\n    }\n    function buildDefaultMesh(meshData) {\n      const geometry = new BufferGeometry();\n      geometry.setIndex(new BufferAttribute(meshData[\"triangles\"], 1));\n      geometry.setAttribute(\"position\", new BufferAttribute(meshData[\"vertices\"], 3));\n      const material = new MeshPhongMaterial({\n        color: 11184895,\n        flatShading: true\n      });\n      const mesh = new Mesh(geometry, material);\n      return mesh;\n    }\n    function buildMeshes(resourceMap, meshData, objects2, modelData, textureData, objectData) {\n      const keys = Object.keys(resourceMap);\n      const meshes = [];\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const resourceId = keys[i];\n        const triangleProperties = resourceMap[resourceId];\n        const resourceType = getResourceType(resourceId, modelData);\n        switch (resourceType) {\n          case \"material\":\n            const basematerials = modelData.resources.basematerials[resourceId];\n            const newMeshes = buildBasematerialsMeshes(basematerials, triangleProperties, meshData, objects2, modelData, textureData, objectData);\n            for (let j = 0, jl = newMeshes.length; j < jl; j++) {\n              meshes.push(newMeshes[j]);\n            }\n            break;\n          case \"texture\":\n            const texture2dgroup = modelData.resources.texture2dgroup[resourceId];\n            meshes.push(buildTexturedMesh(texture2dgroup, triangleProperties, meshData, objects2, modelData, textureData, objectData));\n            break;\n          case \"vertexColors\":\n            const colorgroup = modelData.resources.colorgroup[resourceId];\n            meshes.push(buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects2, modelData, objectData));\n            break;\n          case \"default\":\n            meshes.push(buildDefaultMesh(meshData));\n            break;\n          default:\n            console.error(\"THREE.3MFLoader: Unsupported resource type.\");\n        }\n      }\n      return meshes;\n    }\n    function getResourceType(pid, modelData) {\n      if (modelData.resources.texture2dgroup[pid] !== void 0) {\n        return \"texture\";\n      } else if (modelData.resources.basematerials[pid] !== void 0) {\n        return \"material\";\n      } else if (modelData.resources.colorgroup[pid] !== void 0) {\n        return \"vertexColors\";\n      } else if (pid === \"default\") {\n        return \"default\";\n      } else {\n        return void 0;\n      }\n    }\n    function analyzeObject(modelData, meshData, objectData) {\n      const resourceMap = {};\n      const triangleProperties = meshData[\"triangleProperties\"];\n      const objectPid = objectData.pid;\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i];\n        let pid = triangleProperty.pid !== void 0 ? triangleProperty.pid : objectPid;\n        if (pid === void 0) pid = \"default\";\n        if (resourceMap[pid] === void 0) resourceMap[pid] = [];\n        resourceMap[pid].push(triangleProperty);\n      }\n      return resourceMap;\n    }\n    function buildGroup(meshData, objects2, modelData, textureData, objectData) {\n      const group = new Group();\n      const resourceMap = analyzeObject(modelData, meshData, objectData);\n      const meshes = buildMeshes(resourceMap, meshData, objects2, modelData, textureData, objectData);\n      for (let i = 0, l = meshes.length; i < l; i++) {\n        group.add(meshes[i]);\n      }\n      return group;\n    }\n    function applyExtensions(extensions, meshData, modelXml) {\n      if (!extensions) {\n        return;\n      }\n      const availableExtensions = [];\n      const keys = Object.keys(extensions);\n      for (let i = 0; i < keys.length; i++) {\n        const ns = keys[i];\n        for (let j = 0; j < scope.availableExtensions.length; j++) {\n          const extension = scope.availableExtensions[j];\n          if (extension.ns === ns) {\n            availableExtensions.push(extension);\n          }\n        }\n      }\n      for (let i = 0; i < availableExtensions.length; i++) {\n        const extension = availableExtensions[i];\n        extension.apply(modelXml, extensions[extension[\"ns\"]], meshData);\n      }\n    }\n    function getBuild(data2, objects2, modelData, textureData, objectData, builder) {\n      if (data2.build !== void 0) return data2.build;\n      data2.build = builder(data2, objects2, modelData, textureData, objectData);\n      return data2.build;\n    }\n    function buildBasematerial(materialData, objects2, modelData) {\n      let material;\n      const displaypropertiesid = materialData.displaypropertiesid;\n      const pbmetallicdisplayproperties = modelData.resources.pbmetallicdisplayproperties;\n      if (displaypropertiesid !== null && pbmetallicdisplayproperties[displaypropertiesid] !== void 0) {\n        const pbmetallicdisplayproperty = pbmetallicdisplayproperties[displaypropertiesid];\n        const metallicData = pbmetallicdisplayproperty.data[materialData.index];\n        material = new MeshStandardMaterial({\n          flatShading: true,\n          roughness: metallicData.roughness,\n          metalness: metallicData.metallicness\n        });\n      } else {\n        material = new MeshPhongMaterial({\n          flatShading: true\n        });\n      }\n      material.name = materialData.name;\n      const displaycolor = materialData.displaycolor;\n      const color = displaycolor.substring(0, 7);\n      material.color.setStyle(color);\n      material.color.convertSRGBToLinear();\n      if (displaycolor.length === 9) {\n        material.opacity = parseInt(displaycolor.charAt(7) + displaycolor.charAt(8), 16) / 255;\n      }\n      return material;\n    }\n    function buildComposite(compositeData, objects2, modelData, textureData) {\n      const composite = new Group();\n      for (let j = 0; j < compositeData.length; j++) {\n        const component = compositeData[j];\n        let build2 = objects2[component.objectId];\n        if (build2 === void 0) {\n          buildObject(component.objectId, objects2, modelData, textureData);\n          build2 = objects2[component.objectId];\n        }\n        const object3D = build2.clone();\n        const transform = component.transform;\n        if (transform) {\n          object3D.applyMatrix4(transform);\n        }\n        composite.add(object3D);\n      }\n      return composite;\n    }\n    function buildObject(objectId, objects2, modelData, textureData) {\n      const objectData = modelData[\"resources\"][\"object\"][objectId];\n      if (objectData[\"mesh\"]) {\n        const meshData = objectData[\"mesh\"];\n        const extensions = modelData[\"extensions\"];\n        const modelXml = modelData[\"xml\"];\n        applyExtensions(extensions, meshData, modelXml);\n        objects2[objectData.id] = getBuild(meshData, objects2, modelData, textureData, objectData, buildGroup);\n      } else {\n        const compositeData = objectData[\"components\"];\n        objects2[objectData.id] = getBuild(compositeData, objects2, modelData, textureData, objectData, buildComposite);\n      }\n    }\n    function buildObjects(data3mf2) {\n      const modelsData = data3mf2.model;\n      const modelRels = data3mf2.modelRels;\n      const objects2 = {};\n      const modelsKeys = Object.keys(modelsData);\n      const textureData = {};\n      if (modelRels) {\n        for (let i = 0, l = modelRels.length; i < l; i++) {\n          const modelRel = modelRels[i];\n          const textureKey = modelRel.target.substring(1);\n          if (data3mf2.texture[textureKey]) {\n            textureData[modelRel.target] = data3mf2.texture[textureKey];\n          }\n        }\n      }\n      for (let i = 0; i < modelsKeys.length; i++) {\n        const modelsKey = modelsKeys[i];\n        const modelData = modelsData[modelsKey];\n        const objectIds = Object.keys(modelData[\"resources\"][\"object\"]);\n        for (let j = 0; j < objectIds.length; j++) {\n          const objectId = objectIds[j];\n          buildObject(objectId, objects2, modelData, textureData);\n        }\n      }\n      return objects2;\n    }\n    function fetch3DModelPart(rels) {\n      for (let i = 0; i < rels.length; i++) {\n        const rel = rels[i];\n        const extension = rel.target.split(\".\").pop();\n        if (extension.toLowerCase() === \"model\") return rel;\n      }\n    }\n    function build(objects2, data3mf2) {\n      const group = new Group();\n      const relationship = fetch3DModelPart(data3mf2[\"rels\"]);\n      const buildData = data3mf2.model[relationship[\"target\"].substring(1)][\"build\"];\n      for (let i = 0; i < buildData.length; i++) {\n        const buildItem = buildData[i];\n        const object3D = objects2[buildItem[\"objectId\"]];\n        const transform = buildItem[\"transform\"];\n        if (transform) {\n          object3D.applyMatrix4(transform);\n        }\n        group.add(object3D);\n      }\n      return group;\n    }\n    const data3mf = loadDocument(data);\n    const objects = buildObjects(data3mf);\n    return build(objects, data3mf);\n  }\n  addExtension(extension) {\n    this.availableExtensions.push(extension);\n  }\n}\nexport { ThreeMFLoader };", "map": {"version": 3, "names": ["ThreeMFL<PERSON>der", "Loader", "constructor", "manager", "availableExtensions", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parse", "e", "console", "error", "itemError", "data", "textureLoader", "TextureLoader", "loadDocument", "data2", "zip", "file", "relsName", "modelRelsName", "modelPartNames", "texturesPartNames", "modelRels", "modelParts", "printTicketParts", "texturesParts", "otherParts", "unzipSync", "Uint8Array", "ReferenceError", "match", "push", "rels<PERSON>iew", "relsFileText", "decodeText", "rels", "parseRelsXml", "relsView2", "relsFileText2", "i", "length", "modelPart", "view", "fileText", "xmlData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "nodeName", "toLowerCase", "modelNode", "querySelector", "extensions", "i2", "attributes", "attr", "name", "value", "RegExp", "$1", "modelData", "parseModelNode", "Object", "keys", "texturesPartName", "model", "printTicket", "texture", "other", "relationships", "relsXmlData", "relsNodes", "querySelectorAll", "relsNode", "relationship", "target", "getAttribute", "id", "type", "parseMetadataNodes", "metadataNodes", "metadataData", "metadataNode", "validNames", "indexOf", "textContent", "parseBasematerialsNode", "basematerialsNode", "basematerialsData", "basematerials", "basematerialNodes", "basematerialNode", "basematerialData", "parseBasematerialNode", "index", "parseTexture2DNode", "texture2DNode", "texture2dData", "contenttype", "tilestyleu", "<PERSON><PERSON><PERSON>", "filter", "parseTextures2DGroupNode", "texture2DGroupNode", "texture2DGroupData", "texid", "displaypropertiesid", "tex2coordNodes", "uvs", "tex2coordNode", "u", "v", "parseFloat", "Float32Array", "parseColorGroupNode", "colorGroupNode", "colorGroupData", "colorNodes", "colors", "colorObject", "Color", "colorNode", "color", "setStyle", "substring", "convertSRGBToLinear", "r", "g", "b", "parseMetallicDisplaypropertiesNode", "metallicDisplaypropetiesNode", "metallicDisplaypropertiesData", "metallicNodes", "metallicData", "metallicNode", "metallicness", "roughness", "parseMeshNode", "meshNode", "meshData", "vertices", "vertexNodes", "vertexNode", "x", "y", "z", "triangleProperties", "triangles", "triangleNodes", "triangleNode", "v1", "v2", "v3", "p1", "p2", "p3", "pid", "triangleProperty", "parseInt", "Uint32Array", "parseComponentsNode", "componentsNode", "components", "componentNodes", "componentNode", "componentData", "parseComponentNode", "transform", "parseTransform", "t", "split", "for<PERSON>ach", "s", "matrix", "Matrix4", "set", "parseObjectNode", "objectNode", "objectData", "pindex", "thumbnail", "partnumber", "parseResourcesNode", "resourcesNode", "resourcesData", "basematerialsNodes", "textures2DNodes", "textures2DNode", "texture2DData", "colorGroupNodes", "pbmetallicdisplaypropertiesNodes", "pbmetallicdisplaypropertiesNode", "pbmetallicdisplaypropertiesData", "textures2DGroupNodes", "textures2DGroupNode", "textures2DGroupData", "objectNodes", "parseBuildNode", "buildNode", "buildData", "itemNodes", "itemNode", "buildItem", "objectId", "unit", "buildTexture", "texture2dgroup", "objects2", "textureData", "texture2ds", "resources", "texture2d", "blob", "Blob", "sourceURI", "URL", "createObjectURL", "revokeObjectURL", "colorSpace", "encoding", "wrapS", "RepeatWrapping", "MirroredRepeatWrapping", "ClampToEdgeWrapping", "wrapT", "magFilter", "LinearFilter", "minFilter", "LinearMipmapLinearFilter", "NearestFilter", "buildBasematerialsMeshes", "objectPindex", "materialMap", "l", "meshes", "materialIndex", "trianglePropertiesProps", "material", "getBuild", "buildBasematerial", "geometry", "BufferGeometry", "positionData", "j", "jl", "setAttribute", "Float32BufferAttribute", "mesh", "<PERSON><PERSON>", "buildTexturedMesh", "uvData", "MeshPhongMaterial", "map", "flatShading", "buildVertexColorMesh", "colorgroup", "colorData", "vertexColors", "buildDefaultMesh", "setIndex", "BufferAttribute", "buildMeshes", "resourceMap", "il", "resourceId", "resourceType", "getResourceType", "new<PERSON><PERSON><PERSON>", "analyzeObject", "objectPid", "buildGroup", "group", "Group", "add", "applyExtensions", "modelXml", "ns", "extension", "apply", "builder", "build", "materialData", "pbmetallicdisplayproperties", "pbmetallicdisplayproperty", "MeshStandardMaterial", "metalness", "displaycolor", "opacity", "char<PERSON>t", "buildComposite", "compositeData", "composite", "component", "build2", "buildObject", "object3D", "clone", "applyMatrix4", "buildObjects", "data3mf2", "modelsData", "modelsKeys", "modelRel", "textureKey", "modelsKey", "objectIds", "fetch3DModelPart", "rel", "pop", "data3mf", "objects", "addExtension"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/3MFLoader.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  RepeatWrapping,\n  TextureLoader,\n} from 'three'\nimport { unzipSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n *\n * 3D Manufacturing Format (3MF) specification: https://3mf.io/specification/\n *\n * The following features from the core specification are supported:\n *\n * - 3D Models\n * - Object Resources (Meshes and Components)\n * - Material Resources (Base Materials)\n *\n * 3MF Materials and Properties Extension are only partially supported.\n *\n * - Texture 2D\n * - Texture 2D Groups\n * - Color Groups (Vertex Colors)\n * - Metallic Display Properties (PBR)\n */\n\nclass ThreeMFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n    this.availableExtensions = []\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    const scope = this\n    const textureLoader = new TextureLoader(this.manager)\n\n    function loadDocument(data) {\n      let zip = null\n      let file = null\n\n      let relsName\n      let modelRelsName\n      const modelPartNames = []\n      const printTicketPartNames = []\n      const texturesPartNames = []\n      const otherPartNames = []\n\n      let modelRels\n      const modelParts = {}\n      const printTicketParts = {}\n      const texturesParts = {}\n      const otherParts = {}\n\n      try {\n        zip = unzipSync(new Uint8Array(data))\n      } catch (e) {\n        if (e instanceof ReferenceError) {\n          console.error('THREE.3MFLoader: fflate missing and file is compressed.')\n          return null\n        }\n      }\n\n      for (file in zip) {\n        if (file.match(/\\_rels\\/.rels$/)) {\n          relsName = file\n        } else if (file.match(/3D\\/_rels\\/.*\\.model\\.rels$/)) {\n          modelRelsName = file\n        } else if (file.match(/^3D\\/.*\\.model$/)) {\n          modelPartNames.push(file)\n        } else if (file.match(/^3D\\/Metadata\\/.*\\.xml$/)) {\n          printTicketPartNames.push(file)\n        } else if (file.match(/^3D\\/Textures?\\/.*/)) {\n          texturesPartNames.push(file)\n        } else if (file.match(/^3D\\/Other\\/.*/)) {\n          otherPartNames.push(file)\n        }\n      }\n\n      //\n\n      const relsView = zip[relsName]\n      const relsFileText = decodeText(relsView)\n      const rels = parseRelsXml(relsFileText)\n\n      //\n\n      if (modelRelsName) {\n        const relsView = zip[modelRelsName]\n        const relsFileText = decodeText(relsView)\n        modelRels = parseRelsXml(relsFileText)\n      }\n\n      //\n\n      for (let i = 0; i < modelPartNames.length; i++) {\n        const modelPart = modelPartNames[i]\n        const view = zip[modelPart]\n\n        const fileText = decodeText(view)\n        const xmlData = new DOMParser().parseFromString(fileText, 'application/xml')\n\n        if (xmlData.documentElement.nodeName.toLowerCase() !== 'model') {\n          console.error('THREE.3MFLoader: Error loading 3MF - no 3MF document found: ', modelPart)\n        }\n\n        const modelNode = xmlData.querySelector('model')\n        const extensions = {}\n\n        for (let i = 0; i < modelNode.attributes.length; i++) {\n          const attr = modelNode.attributes[i]\n          if (attr.name.match(/^xmlns:(.+)$/)) {\n            extensions[attr.value] = RegExp.$1\n          }\n        }\n\n        const modelData = parseModelNode(modelNode)\n        modelData['xml'] = modelNode\n\n        if (0 < Object.keys(extensions).length) {\n          modelData['extensions'] = extensions\n        }\n\n        modelParts[modelPart] = modelData\n      }\n\n      //\n\n      for (let i = 0; i < texturesPartNames.length; i++) {\n        const texturesPartName = texturesPartNames[i]\n        texturesParts[texturesPartName] = zip[texturesPartName].buffer\n      }\n\n      return {\n        rels: rels,\n        modelRels: modelRels,\n        model: modelParts,\n        printTicket: printTicketParts,\n        texture: texturesParts,\n        other: otherParts,\n      }\n    }\n\n    function parseRelsXml(relsFileText) {\n      const relationships = []\n\n      const relsXmlData = new DOMParser().parseFromString(relsFileText, 'application/xml')\n\n      const relsNodes = relsXmlData.querySelectorAll('Relationship')\n\n      for (let i = 0; i < relsNodes.length; i++) {\n        const relsNode = relsNodes[i]\n\n        const relationship = {\n          target: relsNode.getAttribute('Target'), //required\n          id: relsNode.getAttribute('Id'), //required\n          type: relsNode.getAttribute('Type'), //required\n        }\n\n        relationships.push(relationship)\n      }\n\n      return relationships\n    }\n\n    function parseMetadataNodes(metadataNodes) {\n      const metadataData = {}\n\n      for (let i = 0; i < metadataNodes.length; i++) {\n        const metadataNode = metadataNodes[i]\n        const name = metadataNode.getAttribute('name')\n        const validNames = [\n          'Title',\n          'Designer',\n          'Description',\n          'Copyright',\n          'LicenseTerms',\n          'Rating',\n          'CreationDate',\n          'ModificationDate',\n        ]\n\n        if (0 <= validNames.indexOf(name)) {\n          metadataData[name] = metadataNode.textContent\n        }\n      }\n\n      return metadataData\n    }\n\n    function parseBasematerialsNode(basematerialsNode) {\n      const basematerialsData = {\n        id: basematerialsNode.getAttribute('id'), // required\n        basematerials: [],\n      }\n\n      const basematerialNodes = basematerialsNode.querySelectorAll('base')\n\n      for (let i = 0; i < basematerialNodes.length; i++) {\n        const basematerialNode = basematerialNodes[i]\n        const basematerialData = parseBasematerialNode(basematerialNode)\n        basematerialData.index = i // the order and count of the material nodes form an implicit 0-based index\n        basematerialsData.basematerials.push(basematerialData)\n      }\n\n      return basematerialsData\n    }\n\n    function parseTexture2DNode(texture2DNode) {\n      const texture2dData = {\n        id: texture2DNode.getAttribute('id'), // required\n        path: texture2DNode.getAttribute('path'), // required\n        contenttype: texture2DNode.getAttribute('contenttype'), // required\n        tilestyleu: texture2DNode.getAttribute('tilestyleu'),\n        tilestylev: texture2DNode.getAttribute('tilestylev'),\n        filter: texture2DNode.getAttribute('filter'),\n      }\n\n      return texture2dData\n    }\n\n    function parseTextures2DGroupNode(texture2DGroupNode) {\n      const texture2DGroupData = {\n        id: texture2DGroupNode.getAttribute('id'), // required\n        texid: texture2DGroupNode.getAttribute('texid'), // required\n        displaypropertiesid: texture2DGroupNode.getAttribute('displaypropertiesid'),\n      }\n\n      const tex2coordNodes = texture2DGroupNode.querySelectorAll('tex2coord')\n\n      const uvs = []\n\n      for (let i = 0; i < tex2coordNodes.length; i++) {\n        const tex2coordNode = tex2coordNodes[i]\n        const u = tex2coordNode.getAttribute('u')\n        const v = tex2coordNode.getAttribute('v')\n\n        uvs.push(parseFloat(u), parseFloat(v))\n      }\n\n      texture2DGroupData['uvs'] = new Float32Array(uvs)\n\n      return texture2DGroupData\n    }\n\n    function parseColorGroupNode(colorGroupNode) {\n      const colorGroupData = {\n        id: colorGroupNode.getAttribute('id'), // required\n        displaypropertiesid: colorGroupNode.getAttribute('displaypropertiesid'),\n      }\n\n      const colorNodes = colorGroupNode.querySelectorAll('color')\n\n      const colors = []\n      const colorObject = new Color()\n\n      for (let i = 0; i < colorNodes.length; i++) {\n        const colorNode = colorNodes[i]\n        const color = colorNode.getAttribute('color')\n\n        colorObject.setStyle(color.substring(0, 7))\n        colorObject.convertSRGBToLinear() // color is in sRGB\n\n        colors.push(colorObject.r, colorObject.g, colorObject.b)\n      }\n\n      colorGroupData['colors'] = new Float32Array(colors)\n\n      return colorGroupData\n    }\n\n    function parseMetallicDisplaypropertiesNode(metallicDisplaypropetiesNode) {\n      const metallicDisplaypropertiesData = {\n        id: metallicDisplaypropetiesNode.getAttribute('id'), // required\n      }\n\n      const metallicNodes = metallicDisplaypropetiesNode.querySelectorAll('pbmetallic')\n\n      const metallicData = []\n\n      for (let i = 0; i < metallicNodes.length; i++) {\n        const metallicNode = metallicNodes[i]\n\n        metallicData.push({\n          name: metallicNode.getAttribute('name'), // required\n          metallicness: parseFloat(metallicNode.getAttribute('metallicness')), // required\n          roughness: parseFloat(metallicNode.getAttribute('roughness')), // required\n        })\n      }\n\n      metallicDisplaypropertiesData.data = metallicData\n\n      return metallicDisplaypropertiesData\n    }\n\n    function parseBasematerialNode(basematerialNode) {\n      const basematerialData = {}\n\n      basematerialData['name'] = basematerialNode.getAttribute('name') // required\n      basematerialData['displaycolor'] = basematerialNode.getAttribute('displaycolor') // required\n      basematerialData['displaypropertiesid'] = basematerialNode.getAttribute('displaypropertiesid')\n\n      return basematerialData\n    }\n\n    function parseMeshNode(meshNode) {\n      const meshData = {}\n\n      const vertices = []\n      const vertexNodes = meshNode.querySelectorAll('vertices vertex')\n\n      for (let i = 0; i < vertexNodes.length; i++) {\n        const vertexNode = vertexNodes[i]\n        const x = vertexNode.getAttribute('x')\n        const y = vertexNode.getAttribute('y')\n        const z = vertexNode.getAttribute('z')\n\n        vertices.push(parseFloat(x), parseFloat(y), parseFloat(z))\n      }\n\n      meshData['vertices'] = new Float32Array(vertices)\n\n      const triangleProperties = []\n      const triangles = []\n      const triangleNodes = meshNode.querySelectorAll('triangles triangle')\n\n      for (let i = 0; i < triangleNodes.length; i++) {\n        const triangleNode = triangleNodes[i]\n        const v1 = triangleNode.getAttribute('v1')\n        const v2 = triangleNode.getAttribute('v2')\n        const v3 = triangleNode.getAttribute('v3')\n        const p1 = triangleNode.getAttribute('p1')\n        const p2 = triangleNode.getAttribute('p2')\n        const p3 = triangleNode.getAttribute('p3')\n        const pid = triangleNode.getAttribute('pid')\n\n        const triangleProperty = {}\n\n        triangleProperty['v1'] = parseInt(v1, 10)\n        triangleProperty['v2'] = parseInt(v2, 10)\n        triangleProperty['v3'] = parseInt(v3, 10)\n\n        triangles.push(triangleProperty['v1'], triangleProperty['v2'], triangleProperty['v3'])\n\n        // optional\n\n        if (p1) {\n          triangleProperty['p1'] = parseInt(p1, 10)\n        }\n\n        if (p2) {\n          triangleProperty['p2'] = parseInt(p2, 10)\n        }\n\n        if (p3) {\n          triangleProperty['p3'] = parseInt(p3, 10)\n        }\n\n        if (pid) {\n          triangleProperty['pid'] = pid\n        }\n\n        if (0 < Object.keys(triangleProperty).length) {\n          triangleProperties.push(triangleProperty)\n        }\n      }\n\n      meshData['triangleProperties'] = triangleProperties\n      meshData['triangles'] = new Uint32Array(triangles)\n\n      return meshData\n    }\n\n    function parseComponentsNode(componentsNode) {\n      const components = []\n\n      const componentNodes = componentsNode.querySelectorAll('component')\n\n      for (let i = 0; i < componentNodes.length; i++) {\n        const componentNode = componentNodes[i]\n        const componentData = parseComponentNode(componentNode)\n        components.push(componentData)\n      }\n\n      return components\n    }\n\n    function parseComponentNode(componentNode) {\n      const componentData = {}\n\n      componentData['objectId'] = componentNode.getAttribute('objectid') // required\n\n      const transform = componentNode.getAttribute('transform')\n\n      if (transform) {\n        componentData['transform'] = parseTransform(transform)\n      }\n\n      return componentData\n    }\n\n    function parseTransform(transform) {\n      const t = []\n      transform.split(' ').forEach(function (s) {\n        t.push(parseFloat(s))\n      })\n\n      const matrix = new Matrix4()\n      matrix.set(t[0], t[3], t[6], t[9], t[1], t[4], t[7], t[10], t[2], t[5], t[8], t[11], 0.0, 0.0, 0.0, 1.0)\n\n      return matrix\n    }\n\n    function parseObjectNode(objectNode) {\n      const objectData = {\n        type: objectNode.getAttribute('type'),\n      }\n\n      const id = objectNode.getAttribute('id')\n\n      if (id) {\n        objectData['id'] = id\n      }\n\n      const pid = objectNode.getAttribute('pid')\n\n      if (pid) {\n        objectData['pid'] = pid\n      }\n\n      const pindex = objectNode.getAttribute('pindex')\n\n      if (pindex) {\n        objectData['pindex'] = pindex\n      }\n\n      const thumbnail = objectNode.getAttribute('thumbnail')\n\n      if (thumbnail) {\n        objectData['thumbnail'] = thumbnail\n      }\n\n      const partnumber = objectNode.getAttribute('partnumber')\n\n      if (partnumber) {\n        objectData['partnumber'] = partnumber\n      }\n\n      const name = objectNode.getAttribute('name')\n\n      if (name) {\n        objectData['name'] = name\n      }\n\n      const meshNode = objectNode.querySelector('mesh')\n\n      if (meshNode) {\n        objectData['mesh'] = parseMeshNode(meshNode)\n      }\n\n      const componentsNode = objectNode.querySelector('components')\n\n      if (componentsNode) {\n        objectData['components'] = parseComponentsNode(componentsNode)\n      }\n\n      return objectData\n    }\n\n    function parseResourcesNode(resourcesNode) {\n      const resourcesData = {}\n\n      resourcesData['basematerials'] = {}\n      const basematerialsNodes = resourcesNode.querySelectorAll('basematerials')\n\n      for (let i = 0; i < basematerialsNodes.length; i++) {\n        const basematerialsNode = basematerialsNodes[i]\n        const basematerialsData = parseBasematerialsNode(basematerialsNode)\n        resourcesData['basematerials'][basematerialsData['id']] = basematerialsData\n      }\n\n      //\n\n      resourcesData['texture2d'] = {}\n      const textures2DNodes = resourcesNode.querySelectorAll('texture2d')\n\n      for (let i = 0; i < textures2DNodes.length; i++) {\n        const textures2DNode = textures2DNodes[i]\n        const texture2DData = parseTexture2DNode(textures2DNode)\n        resourcesData['texture2d'][texture2DData['id']] = texture2DData\n      }\n\n      //\n\n      resourcesData['colorgroup'] = {}\n      const colorGroupNodes = resourcesNode.querySelectorAll('colorgroup')\n\n      for (let i = 0; i < colorGroupNodes.length; i++) {\n        const colorGroupNode = colorGroupNodes[i]\n        const colorGroupData = parseColorGroupNode(colorGroupNode)\n        resourcesData['colorgroup'][colorGroupData['id']] = colorGroupData\n      }\n\n      //\n\n      resourcesData['pbmetallicdisplayproperties'] = {}\n      const pbmetallicdisplaypropertiesNodes = resourcesNode.querySelectorAll('pbmetallicdisplayproperties')\n\n      for (let i = 0; i < pbmetallicdisplaypropertiesNodes.length; i++) {\n        const pbmetallicdisplaypropertiesNode = pbmetallicdisplaypropertiesNodes[i]\n        const pbmetallicdisplaypropertiesData = parseMetallicDisplaypropertiesNode(pbmetallicdisplaypropertiesNode)\n        resourcesData['pbmetallicdisplayproperties'][\n          pbmetallicdisplaypropertiesData['id']\n        ] = pbmetallicdisplaypropertiesData\n      }\n\n      //\n\n      resourcesData['texture2dgroup'] = {}\n      const textures2DGroupNodes = resourcesNode.querySelectorAll('texture2dgroup')\n\n      for (let i = 0; i < textures2DGroupNodes.length; i++) {\n        const textures2DGroupNode = textures2DGroupNodes[i]\n        const textures2DGroupData = parseTextures2DGroupNode(textures2DGroupNode)\n        resourcesData['texture2dgroup'][textures2DGroupData['id']] = textures2DGroupData\n      }\n\n      //\n\n      resourcesData['object'] = {}\n      const objectNodes = resourcesNode.querySelectorAll('object')\n\n      for (let i = 0; i < objectNodes.length; i++) {\n        const objectNode = objectNodes[i]\n        const objectData = parseObjectNode(objectNode)\n        resourcesData['object'][objectData['id']] = objectData\n      }\n\n      return resourcesData\n    }\n\n    function parseBuildNode(buildNode) {\n      const buildData = []\n      const itemNodes = buildNode.querySelectorAll('item')\n\n      for (let i = 0; i < itemNodes.length; i++) {\n        const itemNode = itemNodes[i]\n        const buildItem = {\n          objectId: itemNode.getAttribute('objectid'),\n        }\n        const transform = itemNode.getAttribute('transform')\n\n        if (transform) {\n          buildItem['transform'] = parseTransform(transform)\n        }\n\n        buildData.push(buildItem)\n      }\n\n      return buildData\n    }\n\n    function parseModelNode(modelNode) {\n      const modelData = { unit: modelNode.getAttribute('unit') || 'millimeter' }\n      const metadataNodes = modelNode.querySelectorAll('metadata')\n\n      if (metadataNodes) {\n        modelData['metadata'] = parseMetadataNodes(metadataNodes)\n      }\n\n      const resourcesNode = modelNode.querySelector('resources')\n\n      if (resourcesNode) {\n        modelData['resources'] = parseResourcesNode(resourcesNode)\n      }\n\n      const buildNode = modelNode.querySelector('build')\n\n      if (buildNode) {\n        modelData['build'] = parseBuildNode(buildNode)\n      }\n\n      return modelData\n    }\n\n    function buildTexture(texture2dgroup, objects, modelData, textureData) {\n      const texid = texture2dgroup.texid\n      const texture2ds = modelData.resources.texture2d\n      const texture2d = texture2ds[texid]\n\n      if (texture2d) {\n        const data = textureData[texture2d.path]\n        const type = texture2d.contenttype\n\n        const blob = new Blob([data], { type: type })\n        const sourceURI = URL.createObjectURL(blob)\n\n        const texture = textureLoader.load(sourceURI, function () {\n          URL.revokeObjectURL(sourceURI)\n        })\n\n        if ('colorSpace' in texture) texture.colorSpace = 'srgb'\n        else texture.encoding = 3001 // sRGBEncoding\n\n        // texture parameters\n\n        switch (texture2d.tilestyleu) {\n          case 'wrap':\n            texture.wrapS = RepeatWrapping\n            break\n\n          case 'mirror':\n            texture.wrapS = MirroredRepeatWrapping\n            break\n\n          case 'none':\n          case 'clamp':\n            texture.wrapS = ClampToEdgeWrapping\n            break\n\n          default:\n            texture.wrapS = RepeatWrapping\n        }\n\n        switch (texture2d.tilestylev) {\n          case 'wrap':\n            texture.wrapT = RepeatWrapping\n            break\n\n          case 'mirror':\n            texture.wrapT = MirroredRepeatWrapping\n            break\n\n          case 'none':\n          case 'clamp':\n            texture.wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            texture.wrapT = RepeatWrapping\n        }\n\n        switch (texture2d.filter) {\n          case 'auto':\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearMipmapLinearFilter\n            break\n\n          case 'linear':\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearFilter\n            break\n\n          case 'nearest':\n            texture.magFilter = NearestFilter\n            texture.minFilter = NearestFilter\n            break\n\n          default:\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearMipmapLinearFilter\n        }\n\n        return texture\n      } else {\n        return null\n      }\n    }\n\n    function buildBasematerialsMeshes(\n      basematerials,\n      triangleProperties,\n      meshData,\n      objects,\n      modelData,\n      textureData,\n      objectData,\n    ) {\n      const objectPindex = objectData.pindex\n\n      const materialMap = {}\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n        const pindex = triangleProperty.p1 !== undefined ? triangleProperty.p1 : objectPindex\n\n        if (materialMap[pindex] === undefined) materialMap[pindex] = []\n\n        materialMap[pindex].push(triangleProperty)\n      }\n\n      //\n\n      const keys = Object.keys(materialMap)\n      const meshes = []\n\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const materialIndex = keys[i]\n        const trianglePropertiesProps = materialMap[materialIndex]\n        const basematerialData = basematerials.basematerials[materialIndex]\n        const material = getBuild(basematerialData, objects, modelData, textureData, objectData, buildBasematerial)\n\n        //\n\n        const geometry = new BufferGeometry()\n\n        const positionData = []\n\n        const vertices = meshData.vertices\n\n        for (let j = 0, jl = trianglePropertiesProps.length; j < jl; j++) {\n          const triangleProperty = trianglePropertiesProps[j]\n\n          positionData.push(vertices[triangleProperty.v1 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v1 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v1 * 3 + 2])\n\n          positionData.push(vertices[triangleProperty.v2 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v2 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v2 * 3 + 2])\n\n          positionData.push(vertices[triangleProperty.v3 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v3 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v3 * 3 + 2])\n        }\n\n        geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n\n        //\n\n        const mesh = new Mesh(geometry, material)\n        meshes.push(mesh)\n      }\n\n      return meshes\n    }\n\n    function buildTexturedMesh(\n      texture2dgroup,\n      triangleProperties,\n      meshData,\n      objects,\n      modelData,\n      textureData,\n      objectData,\n    ) {\n      // geometry\n\n      const geometry = new BufferGeometry()\n\n      const positionData = []\n      const uvData = []\n\n      const vertices = meshData.vertices\n      const uvs = texture2dgroup.uvs\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n\n        positionData.push(vertices[triangleProperty.v1 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v1 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v1 * 3 + 2])\n\n        positionData.push(vertices[triangleProperty.v2 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v2 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v2 * 3 + 2])\n\n        positionData.push(vertices[triangleProperty.v3 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v3 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v3 * 3 + 2])\n\n        //\n\n        uvData.push(uvs[triangleProperty.p1 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p1 * 2 + 1])\n\n        uvData.push(uvs[triangleProperty.p2 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p2 * 2 + 1])\n\n        uvData.push(uvs[triangleProperty.p3 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p3 * 2 + 1])\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n      geometry.setAttribute('uv', new Float32BufferAttribute(uvData, 2))\n\n      // material\n\n      const texture = getBuild(texture2dgroup, objects, modelData, textureData, objectData, buildTexture)\n\n      const material = new MeshPhongMaterial({ map: texture, flatShading: true })\n\n      // mesh\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects, modelData, objectData) {\n      // geometry\n\n      const geometry = new BufferGeometry()\n\n      const positionData = []\n      const colorData = []\n\n      const vertices = meshData.vertices\n      const colors = colorgroup.colors\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n\n        const v1 = triangleProperty.v1\n        const v2 = triangleProperty.v2\n        const v3 = triangleProperty.v3\n\n        positionData.push(vertices[v1 * 3 + 0])\n        positionData.push(vertices[v1 * 3 + 1])\n        positionData.push(vertices[v1 * 3 + 2])\n\n        positionData.push(vertices[v2 * 3 + 0])\n        positionData.push(vertices[v2 * 3 + 1])\n        positionData.push(vertices[v2 * 3 + 2])\n\n        positionData.push(vertices[v3 * 3 + 0])\n        positionData.push(vertices[v3 * 3 + 1])\n        positionData.push(vertices[v3 * 3 + 2])\n\n        //\n\n        const p1 = triangleProperty.p1 !== undefined ? triangleProperty.p1 : objectData.pindex\n        const p2 = triangleProperty.p2 !== undefined ? triangleProperty.p2 : p1\n        const p3 = triangleProperty.p3 !== undefined ? triangleProperty.p3 : p1\n\n        colorData.push(colors[p1 * 3 + 0])\n        colorData.push(colors[p1 * 3 + 1])\n        colorData.push(colors[p1 * 3 + 2])\n\n        colorData.push(colors[p2 * 3 + 0])\n        colorData.push(colors[p2 * 3 + 1])\n        colorData.push(colors[p2 * 3 + 2])\n\n        colorData.push(colors[p3 * 3 + 0])\n        colorData.push(colors[p3 * 3 + 1])\n        colorData.push(colors[p3 * 3 + 2])\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n      geometry.setAttribute('color', new Float32BufferAttribute(colorData, 3))\n\n      // material\n\n      const material = new MeshPhongMaterial({ vertexColors: true, flatShading: true })\n\n      // mesh\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildDefaultMesh(meshData) {\n      const geometry = new BufferGeometry()\n      geometry.setIndex(new BufferAttribute(meshData['triangles'], 1))\n      geometry.setAttribute('position', new BufferAttribute(meshData['vertices'], 3))\n\n      const material = new MeshPhongMaterial({ color: 0xaaaaff, flatShading: true })\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildMeshes(resourceMap, meshData, objects, modelData, textureData, objectData) {\n      const keys = Object.keys(resourceMap)\n      const meshes = []\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const resourceId = keys[i]\n        const triangleProperties = resourceMap[resourceId]\n        const resourceType = getResourceType(resourceId, modelData)\n\n        switch (resourceType) {\n          case 'material':\n            const basematerials = modelData.resources.basematerials[resourceId]\n            const newMeshes = buildBasematerialsMeshes(\n              basematerials,\n              triangleProperties,\n              meshData,\n              objects,\n              modelData,\n              textureData,\n              objectData,\n            )\n\n            for (let j = 0, jl = newMeshes.length; j < jl; j++) {\n              meshes.push(newMeshes[j])\n            }\n\n            break\n\n          case 'texture':\n            const texture2dgroup = modelData.resources.texture2dgroup[resourceId]\n            meshes.push(\n              buildTexturedMesh(\n                texture2dgroup,\n                triangleProperties,\n                meshData,\n                objects,\n                modelData,\n                textureData,\n                objectData,\n              ),\n            )\n            break\n\n          case 'vertexColors':\n            const colorgroup = modelData.resources.colorgroup[resourceId]\n            meshes.push(buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects, modelData, objectData))\n            break\n\n          case 'default':\n            meshes.push(buildDefaultMesh(meshData))\n            break\n\n          default:\n            console.error('THREE.3MFLoader: Unsupported resource type.')\n        }\n      }\n\n      return meshes\n    }\n\n    function getResourceType(pid, modelData) {\n      if (modelData.resources.texture2dgroup[pid] !== undefined) {\n        return 'texture'\n      } else if (modelData.resources.basematerials[pid] !== undefined) {\n        return 'material'\n      } else if (modelData.resources.colorgroup[pid] !== undefined) {\n        return 'vertexColors'\n      } else if (pid === 'default') {\n        return 'default'\n      } else {\n        return undefined\n      }\n    }\n\n    function analyzeObject(modelData, meshData, objectData) {\n      const resourceMap = {}\n\n      const triangleProperties = meshData['triangleProperties']\n\n      const objectPid = objectData.pid\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n        let pid = triangleProperty.pid !== undefined ? triangleProperty.pid : objectPid\n\n        if (pid === undefined) pid = 'default'\n\n        if (resourceMap[pid] === undefined) resourceMap[pid] = []\n\n        resourceMap[pid].push(triangleProperty)\n      }\n\n      return resourceMap\n    }\n\n    function buildGroup(meshData, objects, modelData, textureData, objectData) {\n      const group = new Group()\n\n      const resourceMap = analyzeObject(modelData, meshData, objectData)\n      const meshes = buildMeshes(resourceMap, meshData, objects, modelData, textureData, objectData)\n\n      for (let i = 0, l = meshes.length; i < l; i++) {\n        group.add(meshes[i])\n      }\n\n      return group\n    }\n\n    function applyExtensions(extensions, meshData, modelXml) {\n      if (!extensions) {\n        return\n      }\n\n      const availableExtensions = []\n      const keys = Object.keys(extensions)\n\n      for (let i = 0; i < keys.length; i++) {\n        const ns = keys[i]\n\n        for (let j = 0; j < scope.availableExtensions.length; j++) {\n          const extension = scope.availableExtensions[j]\n\n          if (extension.ns === ns) {\n            availableExtensions.push(extension)\n          }\n        }\n      }\n\n      for (let i = 0; i < availableExtensions.length; i++) {\n        const extension = availableExtensions[i]\n        extension.apply(modelXml, extensions[extension['ns']], meshData)\n      }\n    }\n\n    function getBuild(data, objects, modelData, textureData, objectData, builder) {\n      if (data.build !== undefined) return data.build\n\n      data.build = builder(data, objects, modelData, textureData, objectData)\n\n      return data.build\n    }\n\n    function buildBasematerial(materialData, objects, modelData) {\n      let material\n\n      const displaypropertiesid = materialData.displaypropertiesid\n      const pbmetallicdisplayproperties = modelData.resources.pbmetallicdisplayproperties\n\n      if (displaypropertiesid !== null && pbmetallicdisplayproperties[displaypropertiesid] !== undefined) {\n        // metallic display property, use StandardMaterial\n\n        const pbmetallicdisplayproperty = pbmetallicdisplayproperties[displaypropertiesid]\n        const metallicData = pbmetallicdisplayproperty.data[materialData.index]\n\n        material = new MeshStandardMaterial({\n          flatShading: true,\n          roughness: metallicData.roughness,\n          metalness: metallicData.metallicness,\n        })\n      } else {\n        // otherwise use PhongMaterial\n\n        material = new MeshPhongMaterial({ flatShading: true })\n      }\n\n      material.name = materialData.name\n\n      // displaycolor MUST be specified with a value of a 6 or 8 digit hexadecimal number, e.g. \"#RRGGBB\" or \"#RRGGBBAA\"\n\n      const displaycolor = materialData.displaycolor\n\n      const color = displaycolor.substring(0, 7)\n      material.color.setStyle(color)\n      material.color.convertSRGBToLinear() // displaycolor is in sRGB\n\n      // process alpha if set\n\n      if (displaycolor.length === 9) {\n        material.opacity = parseInt(displaycolor.charAt(7) + displaycolor.charAt(8), 16) / 255\n      }\n\n      return material\n    }\n\n    function buildComposite(compositeData, objects, modelData, textureData) {\n      const composite = new Group()\n\n      for (let j = 0; j < compositeData.length; j++) {\n        const component = compositeData[j]\n        let build = objects[component.objectId]\n\n        if (build === undefined) {\n          buildObject(component.objectId, objects, modelData, textureData)\n          build = objects[component.objectId]\n        }\n\n        const object3D = build.clone()\n\n        // apply component transform\n\n        const transform = component.transform\n\n        if (transform) {\n          object3D.applyMatrix4(transform)\n        }\n\n        composite.add(object3D)\n      }\n\n      return composite\n    }\n\n    function buildObject(objectId, objects, modelData, textureData) {\n      const objectData = modelData['resources']['object'][objectId]\n\n      if (objectData['mesh']) {\n        const meshData = objectData['mesh']\n\n        const extensions = modelData['extensions']\n        const modelXml = modelData['xml']\n\n        applyExtensions(extensions, meshData, modelXml)\n\n        objects[objectData.id] = getBuild(meshData, objects, modelData, textureData, objectData, buildGroup)\n      } else {\n        const compositeData = objectData['components']\n\n        objects[objectData.id] = getBuild(compositeData, objects, modelData, textureData, objectData, buildComposite)\n      }\n    }\n\n    function buildObjects(data3mf) {\n      const modelsData = data3mf.model\n      const modelRels = data3mf.modelRels\n      const objects = {}\n      const modelsKeys = Object.keys(modelsData)\n      const textureData = {}\n\n      // evaluate model relationships to textures\n\n      if (modelRels) {\n        for (let i = 0, l = modelRels.length; i < l; i++) {\n          const modelRel = modelRels[i]\n          const textureKey = modelRel.target.substring(1)\n\n          if (data3mf.texture[textureKey]) {\n            textureData[modelRel.target] = data3mf.texture[textureKey]\n          }\n        }\n      }\n\n      // start build\n\n      for (let i = 0; i < modelsKeys.length; i++) {\n        const modelsKey = modelsKeys[i]\n        const modelData = modelsData[modelsKey]\n\n        const objectIds = Object.keys(modelData['resources']['object'])\n\n        for (let j = 0; j < objectIds.length; j++) {\n          const objectId = objectIds[j]\n\n          buildObject(objectId, objects, modelData, textureData)\n        }\n      }\n\n      return objects\n    }\n\n    function fetch3DModelPart(rels) {\n      for (let i = 0; i < rels.length; i++) {\n        const rel = rels[i]\n        const extension = rel.target.split('.').pop()\n\n        if (extension.toLowerCase() === 'model') return rel\n      }\n    }\n\n    function build(objects, data3mf) {\n      const group = new Group()\n\n      const relationship = fetch3DModelPart(data3mf['rels'])\n      const buildData = data3mf.model[relationship['target'].substring(1)]['build']\n\n      for (let i = 0; i < buildData.length; i++) {\n        const buildItem = buildData[i]\n        const object3D = objects[buildItem['objectId']]\n\n        // apply transform\n\n        const transform = buildItem['transform']\n\n        if (transform) {\n          object3D.applyMatrix4(transform)\n        }\n\n        group.add(object3D)\n      }\n\n      return group\n    }\n\n    const data3mf = loadDocument(data)\n    const objects = buildObjects(data3mf)\n\n    return build(objects, data3mf)\n  }\n\n  addExtension(extension) {\n    this.availableExtensions.push(extension)\n  }\n}\n\nexport { ThreeMFLoader }\n"], "mappings": ";;;AA0CA,MAAMA,aAAA,SAAsBC,MAAA,CAAO;EACjCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IACb,KAAKC,mBAAA,GAAsB,EAAE;EAC9B;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IACd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMP,OAAO;IAC3CQ,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,MAAA,EAAQ;MAChB,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,MAAM,CAAC;MAC3B,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMP,OAAA,CAAQsB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,IAAA,EAAM;IACV,MAAMhB,KAAA,GAAQ;IACd,MAAMiB,aAAA,GAAgB,IAAIC,aAAA,CAAc,KAAKzB,OAAO;IAEpD,SAAS0B,aAAaC,KAAA,EAAM;MAC1B,IAAIC,GAAA,GAAM;MACV,IAAIC,IAAA,GAAO;MAEX,IAAIC,QAAA;MACJ,IAAIC,aAAA;MACJ,MAAMC,cAAA,GAAiB,EAAE;MAEzB,MAAMC,iBAAA,GAAoB,EAAE;MAG5B,IAAIC,SAAA;MACJ,MAAMC,UAAA,GAAa,CAAE;MACrB,MAAMC,gBAAA,GAAmB,CAAE;MAC3B,MAAMC,aAAA,GAAgB,CAAE;MACxB,MAAMC,UAAA,GAAa,CAAE;MAErB,IAAI;QACFV,GAAA,GAAMW,SAAA,CAAU,IAAIC,UAAA,CAAWb,KAAI,CAAC;MACrC,SAAQR,CAAA,EAAP;QACA,IAAIA,CAAA,YAAasB,cAAA,EAAgB;UAC/BrB,OAAA,CAAQC,KAAA,CAAM,yDAAyD;UACvE,OAAO;QACR;MACF;MAED,KAAKQ,IAAA,IAAQD,GAAA,EAAK;QAChB,IAAIC,IAAA,CAAKa,KAAA,CAAM,gBAAgB,GAAG;UAChCZ,QAAA,GAAWD,IAAA;QACZ,WAAUA,IAAA,CAAKa,KAAA,CAAM,6BAA6B,GAAG;UACpDX,aAAA,GAAgBF,IAAA;QACjB,WAAUA,IAAA,CAAKa,KAAA,CAAM,iBAAiB,GAAG;UACxCV,cAAA,CAAeW,IAAA,CAAKd,IAAI;QAClC,WAAmBA,IAAA,CAAKa,KAAA,CAAM,yBAAyB,GAAG,UAEvCb,IAAA,CAAKa,KAAA,CAAM,oBAAoB,GAAG;UAC3CT,iBAAA,CAAkBU,IAAA,CAAKd,IAAI;QAC5B,WAAUA,IAAA,CAAKa,KAAA,CAAM,gBAAgB,GAAG;MAG1C;MAID,MAAME,QAAA,GAAWhB,GAAA,CAAIE,QAAQ;MAC7B,MAAMe,YAAA,GAAeC,UAAA,CAAWF,QAAQ;MACxC,MAAMG,IAAA,GAAOC,YAAA,CAAaH,YAAY;MAItC,IAAId,aAAA,EAAe;QACjB,MAAMkB,SAAA,GAAWrB,GAAA,CAAIG,aAAa;QAClC,MAAMmB,aAAA,GAAeJ,UAAA,CAAWG,SAAQ;QACxCf,SAAA,GAAYc,YAAA,CAAaE,aAAY;MACtC;MAID,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAInB,cAAA,CAAeoB,MAAA,EAAQD,CAAA,IAAK;QAC9C,MAAME,SAAA,GAAYrB,cAAA,CAAemB,CAAC;QAClC,MAAMG,IAAA,GAAO1B,GAAA,CAAIyB,SAAS;QAE1B,MAAME,QAAA,GAAWT,UAAA,CAAWQ,IAAI;QAChC,MAAME,OAAA,GAAU,IAAIC,SAAA,CAAS,EAAGC,eAAA,CAAgBH,QAAA,EAAU,iBAAiB;QAE3E,IAAIC,OAAA,CAAQG,eAAA,CAAgBC,QAAA,CAASC,WAAA,CAAW,MAAO,SAAS;UAC9DzC,OAAA,CAAQC,KAAA,CAAM,gEAAgEgC,SAAS;QACxF;QAED,MAAMS,SAAA,GAAYN,OAAA,CAAQO,aAAA,CAAc,OAAO;QAC/C,MAAMC,UAAA,GAAa,CAAE;QAErB,SAASC,EAAA,GAAI,GAAGA,EAAA,GAAIH,SAAA,CAAUI,UAAA,CAAWd,MAAA,EAAQa,EAAA,IAAK;UACpD,MAAME,IAAA,GAAOL,SAAA,CAAUI,UAAA,CAAWD,EAAC;UACnC,IAAIE,IAAA,CAAKC,IAAA,CAAK1B,KAAA,CAAM,cAAc,GAAG;YACnCsB,UAAA,CAAWG,IAAA,CAAKE,KAAK,IAAIC,MAAA,CAAOC,EAAA;UACjC;QACF;QAED,MAAMC,SAAA,GAAYC,cAAA,CAAeX,SAAS;QAC1CU,SAAA,CAAU,KAAK,IAAIV,SAAA;QAEnB,IAAI,IAAIY,MAAA,CAAOC,IAAA,CAAKX,UAAU,EAAEZ,MAAA,EAAQ;UACtCoB,SAAA,CAAU,YAAY,IAAIR,UAAA;QAC3B;QAED7B,UAAA,CAAWkB,SAAS,IAAImB,SAAA;MACzB;MAID,SAASrB,CAAA,GAAI,GAAGA,CAAA,GAAIlB,iBAAA,CAAkBmB,MAAA,EAAQD,CAAA,IAAK;QACjD,MAAMyB,gBAAA,GAAmB3C,iBAAA,CAAkBkB,CAAC;QAC5Cd,aAAA,CAAcuC,gBAAgB,IAAIhD,GAAA,CAAIgD,gBAAgB,EAAE3D,MAAA;MACzD;MAED,OAAO;QACL8B,IAAA;QACAb,SAAA;QACA2C,KAAA,EAAO1C,UAAA;QACP2C,WAAA,EAAa1C,gBAAA;QACb2C,OAAA,EAAS1C,aAAA;QACT2C,KAAA,EAAO1C;MACR;IACF;IAED,SAASU,aAAaH,YAAA,EAAc;MAClC,MAAMoC,aAAA,GAAgB,EAAE;MAExB,MAAMC,WAAA,GAAc,IAAIzB,SAAA,CAAS,EAAGC,eAAA,CAAgBb,YAAA,EAAc,iBAAiB;MAEnF,MAAMsC,SAAA,GAAYD,WAAA,CAAYE,gBAAA,CAAiB,cAAc;MAE7D,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIgC,SAAA,CAAU/B,MAAA,EAAQD,CAAA,IAAK;QACzC,MAAMkC,QAAA,GAAWF,SAAA,CAAUhC,CAAC;QAE5B,MAAMmC,YAAA,GAAe;UACnBC,MAAA,EAAQF,QAAA,CAASG,YAAA,CAAa,QAAQ;UAAA;UACtCC,EAAA,EAAIJ,QAAA,CAASG,YAAA,CAAa,IAAI;UAAA;UAC9BE,IAAA,EAAML,QAAA,CAASG,YAAA,CAAa,MAAM;UAAA;QACnC;QAEDP,aAAA,CAActC,IAAA,CAAK2C,YAAY;MAChC;MAED,OAAOL,aAAA;IACR;IAED,SAASU,mBAAmBC,aAAA,EAAe;MACzC,MAAMC,YAAA,GAAe,CAAE;MAEvB,SAAS1C,CAAA,GAAI,GAAGA,CAAA,GAAIyC,aAAA,CAAcxC,MAAA,EAAQD,CAAA,IAAK;QAC7C,MAAM2C,YAAA,GAAeF,aAAA,CAAczC,CAAC;QACpC,MAAMiB,IAAA,GAAO0B,YAAA,CAAaN,YAAA,CAAa,MAAM;QAC7C,MAAMO,UAAA,GAAa,CACjB,SACA,YACA,eACA,aACA,gBACA,UACA,gBACA,mBACD;QAED,IAAI,KAAKA,UAAA,CAAWC,OAAA,CAAQ5B,IAAI,GAAG;UACjCyB,YAAA,CAAazB,IAAI,IAAI0B,YAAA,CAAaG,WAAA;QACnC;MACF;MAED,OAAOJ,YAAA;IACR;IAED,SAASK,uBAAuBC,iBAAA,EAAmB;MACjD,MAAMC,iBAAA,GAAoB;QACxBX,EAAA,EAAIU,iBAAA,CAAkBX,YAAA,CAAa,IAAI;QAAA;QACvCa,aAAA,EAAe;MAChB;MAED,MAAMC,iBAAA,GAAoBH,iBAAA,CAAkBf,gBAAA,CAAiB,MAAM;MAEnE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAImD,iBAAA,CAAkBlD,MAAA,EAAQD,CAAA,IAAK;QACjD,MAAMoD,gBAAA,GAAmBD,iBAAA,CAAkBnD,CAAC;QAC5C,MAAMqD,gBAAA,GAAmBC,qBAAA,CAAsBF,gBAAgB;QAC/DC,gBAAA,CAAiBE,KAAA,GAAQvD,CAAA;QACzBiD,iBAAA,CAAkBC,aAAA,CAAc1D,IAAA,CAAK6D,gBAAgB;MACtD;MAED,OAAOJ,iBAAA;IACR;IAED,SAASO,mBAAmBC,aAAA,EAAe;MACzC,MAAMC,aAAA,GAAgB;QACpBpB,EAAA,EAAImB,aAAA,CAAcpB,YAAA,CAAa,IAAI;QAAA;QACnC7E,IAAA,EAAMiG,aAAA,CAAcpB,YAAA,CAAa,MAAM;QAAA;QACvCsB,WAAA,EAAaF,aAAA,CAAcpB,YAAA,CAAa,aAAa;QAAA;QACrDuB,UAAA,EAAYH,aAAA,CAAcpB,YAAA,CAAa,YAAY;QACnDwB,UAAA,EAAYJ,aAAA,CAAcpB,YAAA,CAAa,YAAY;QACnDyB,MAAA,EAAQL,aAAA,CAAcpB,YAAA,CAAa,QAAQ;MAC5C;MAED,OAAOqB,aAAA;IACR;IAED,SAASK,yBAAyBC,kBAAA,EAAoB;MACpD,MAAMC,kBAAA,GAAqB;QACzB3B,EAAA,EAAI0B,kBAAA,CAAmB3B,YAAA,CAAa,IAAI;QAAA;QACxC6B,KAAA,EAAOF,kBAAA,CAAmB3B,YAAA,CAAa,OAAO;QAAA;QAC9C8B,mBAAA,EAAqBH,kBAAA,CAAmB3B,YAAA,CAAa,qBAAqB;MAC3E;MAED,MAAM+B,cAAA,GAAiBJ,kBAAA,CAAmB/B,gBAAA,CAAiB,WAAW;MAEtE,MAAMoC,GAAA,GAAM,EAAE;MAEd,SAASrE,CAAA,GAAI,GAAGA,CAAA,GAAIoE,cAAA,CAAenE,MAAA,EAAQD,CAAA,IAAK;QAC9C,MAAMsE,aAAA,GAAgBF,cAAA,CAAepE,CAAC;QACtC,MAAMuE,CAAA,GAAID,aAAA,CAAcjC,YAAA,CAAa,GAAG;QACxC,MAAMmC,CAAA,GAAIF,aAAA,CAAcjC,YAAA,CAAa,GAAG;QAExCgC,GAAA,CAAI7E,IAAA,CAAKiF,UAAA,CAAWF,CAAC,GAAGE,UAAA,CAAWD,CAAC,CAAC;MACtC;MAEDP,kBAAA,CAAmB,KAAK,IAAI,IAAIS,YAAA,CAAaL,GAAG;MAEhD,OAAOJ,kBAAA;IACR;IAED,SAASU,oBAAoBC,cAAA,EAAgB;MAC3C,MAAMC,cAAA,GAAiB;QACrBvC,EAAA,EAAIsC,cAAA,CAAevC,YAAA,CAAa,IAAI;QAAA;QACpC8B,mBAAA,EAAqBS,cAAA,CAAevC,YAAA,CAAa,qBAAqB;MACvE;MAED,MAAMyC,UAAA,GAAaF,cAAA,CAAe3C,gBAAA,CAAiB,OAAO;MAE1D,MAAM8C,MAAA,GAAS,EAAE;MACjB,MAAMC,WAAA,GAAc,IAAIC,KAAA,CAAO;MAE/B,SAASjF,CAAA,GAAI,GAAGA,CAAA,GAAI8E,UAAA,CAAW7E,MAAA,EAAQD,CAAA,IAAK;QAC1C,MAAMkF,SAAA,GAAYJ,UAAA,CAAW9E,CAAC;QAC9B,MAAMmF,KAAA,GAAQD,SAAA,CAAU7C,YAAA,CAAa,OAAO;QAE5C2C,WAAA,CAAYI,QAAA,CAASD,KAAA,CAAME,SAAA,CAAU,GAAG,CAAC,CAAC;QAC1CL,WAAA,CAAYM,mBAAA,CAAqB;QAEjCP,MAAA,CAAOvF,IAAA,CAAKwF,WAAA,CAAYO,CAAA,EAAGP,WAAA,CAAYQ,CAAA,EAAGR,WAAA,CAAYS,CAAC;MACxD;MAEDZ,cAAA,CAAe,QAAQ,IAAI,IAAIH,YAAA,CAAaK,MAAM;MAElD,OAAOF,cAAA;IACR;IAED,SAASa,mCAAmCC,4BAAA,EAA8B;MACxE,MAAMC,6BAAA,GAAgC;QACpCtD,EAAA,EAAIqD,4BAAA,CAA6BtD,YAAA,CAAa,IAAI;QAAA;MACnD;MAED,MAAMwD,aAAA,GAAgBF,4BAAA,CAA6B1D,gBAAA,CAAiB,YAAY;MAEhF,MAAM6D,YAAA,GAAe,EAAE;MAEvB,SAAS9F,CAAA,GAAI,GAAGA,CAAA,GAAI6F,aAAA,CAAc5F,MAAA,EAAQD,CAAA,IAAK;QAC7C,MAAM+F,YAAA,GAAeF,aAAA,CAAc7F,CAAC;QAEpC8F,YAAA,CAAatG,IAAA,CAAK;UAChByB,IAAA,EAAM8E,YAAA,CAAa1D,YAAA,CAAa,MAAM;UAAA;UACtC2D,YAAA,EAAcvB,UAAA,CAAWsB,YAAA,CAAa1D,YAAA,CAAa,cAAc,CAAC;UAAA;UAClE4D,SAAA,EAAWxB,UAAA,CAAWsB,YAAA,CAAa1D,YAAA,CAAa,WAAW,CAAC;UAAA;QACtE,CAAS;MACF;MAEDuD,6BAAA,CAA8BxH,IAAA,GAAO0H,YAAA;MAErC,OAAOF,6BAAA;IACR;IAED,SAAStC,sBAAsBF,gBAAA,EAAkB;MAC/C,MAAMC,gBAAA,GAAmB,CAAE;MAE3BA,gBAAA,CAAiB,MAAM,IAAID,gBAAA,CAAiBf,YAAA,CAAa,MAAM;MAC/DgB,gBAAA,CAAiB,cAAc,IAAID,gBAAA,CAAiBf,YAAA,CAAa,cAAc;MAC/EgB,gBAAA,CAAiB,qBAAqB,IAAID,gBAAA,CAAiBf,YAAA,CAAa,qBAAqB;MAE7F,OAAOgB,gBAAA;IACR;IAED,SAAS6C,cAAcC,QAAA,EAAU;MAC/B,MAAMC,QAAA,GAAW,CAAE;MAEnB,MAAMC,QAAA,GAAW,EAAE;MACnB,MAAMC,WAAA,GAAcH,QAAA,CAASlE,gBAAA,CAAiB,iBAAiB;MAE/D,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIsG,WAAA,CAAYrG,MAAA,EAAQD,CAAA,IAAK;QAC3C,MAAMuG,UAAA,GAAaD,WAAA,CAAYtG,CAAC;QAChC,MAAMwG,CAAA,GAAID,UAAA,CAAWlE,YAAA,CAAa,GAAG;QACrC,MAAMoE,CAAA,GAAIF,UAAA,CAAWlE,YAAA,CAAa,GAAG;QACrC,MAAMqE,CAAA,GAAIH,UAAA,CAAWlE,YAAA,CAAa,GAAG;QAErCgE,QAAA,CAAS7G,IAAA,CAAKiF,UAAA,CAAW+B,CAAC,GAAG/B,UAAA,CAAWgC,CAAC,GAAGhC,UAAA,CAAWiC,CAAC,CAAC;MAC1D;MAEDN,QAAA,CAAS,UAAU,IAAI,IAAI1B,YAAA,CAAa2B,QAAQ;MAEhD,MAAMM,kBAAA,GAAqB,EAAE;MAC7B,MAAMC,SAAA,GAAY,EAAE;MACpB,MAAMC,aAAA,GAAgBV,QAAA,CAASlE,gBAAA,CAAiB,oBAAoB;MAEpE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAI6G,aAAA,CAAc5G,MAAA,EAAQD,CAAA,IAAK;QAC7C,MAAM8G,YAAA,GAAeD,aAAA,CAAc7G,CAAC;QACpC,MAAM+G,EAAA,GAAKD,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAM2E,EAAA,GAAKF,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAM4E,EAAA,GAAKH,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAM6E,EAAA,GAAKJ,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAM8E,EAAA,GAAKL,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAM+E,EAAA,GAAKN,YAAA,CAAazE,YAAA,CAAa,IAAI;QACzC,MAAMgF,GAAA,GAAMP,YAAA,CAAazE,YAAA,CAAa,KAAK;QAE3C,MAAMiF,gBAAA,GAAmB,CAAE;QAE3BA,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASR,EAAA,EAAI,EAAE;QACxCO,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASP,EAAA,EAAI,EAAE;QACxCM,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASN,EAAA,EAAI,EAAE;QAExCL,SAAA,CAAUpH,IAAA,CAAK8H,gBAAA,CAAiB,IAAI,GAAGA,gBAAA,CAAiB,IAAI,GAAGA,gBAAA,CAAiB,IAAI,CAAC;QAIrF,IAAIJ,EAAA,EAAI;UACNI,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASL,EAAA,EAAI,EAAE;QACzC;QAED,IAAIC,EAAA,EAAI;UACNG,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASJ,EAAA,EAAI,EAAE;QACzC;QAED,IAAIC,EAAA,EAAI;UACNE,gBAAA,CAAiB,IAAI,IAAIC,QAAA,CAASH,EAAA,EAAI,EAAE;QACzC;QAED,IAAIC,GAAA,EAAK;UACPC,gBAAA,CAAiB,KAAK,IAAID,GAAA;QAC3B;QAED,IAAI,IAAI9F,MAAA,CAAOC,IAAA,CAAK8F,gBAAgB,EAAErH,MAAA,EAAQ;UAC5C0G,kBAAA,CAAmBnH,IAAA,CAAK8H,gBAAgB;QACzC;MACF;MAEDlB,QAAA,CAAS,oBAAoB,IAAIO,kBAAA;MACjCP,QAAA,CAAS,WAAW,IAAI,IAAIoB,WAAA,CAAYZ,SAAS;MAEjD,OAAOR,QAAA;IACR;IAED,SAASqB,oBAAoBC,cAAA,EAAgB;MAC3C,MAAMC,UAAA,GAAa,EAAE;MAErB,MAAMC,cAAA,GAAiBF,cAAA,CAAezF,gBAAA,CAAiB,WAAW;MAElE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAI4H,cAAA,CAAe3H,MAAA,EAAQD,CAAA,IAAK;QAC9C,MAAM6H,aAAA,GAAgBD,cAAA,CAAe5H,CAAC;QACtC,MAAM8H,aAAA,GAAgBC,kBAAA,CAAmBF,aAAa;QACtDF,UAAA,CAAWnI,IAAA,CAAKsI,aAAa;MAC9B;MAED,OAAOH,UAAA;IACR;IAED,SAASI,mBAAmBF,aAAA,EAAe;MACzC,MAAMC,aAAA,GAAgB,CAAE;MAExBA,aAAA,CAAc,UAAU,IAAID,aAAA,CAAcxF,YAAA,CAAa,UAAU;MAEjE,MAAM2F,SAAA,GAAYH,aAAA,CAAcxF,YAAA,CAAa,WAAW;MAExD,IAAI2F,SAAA,EAAW;QACbF,aAAA,CAAc,WAAW,IAAIG,cAAA,CAAeD,SAAS;MACtD;MAED,OAAOF,aAAA;IACR;IAED,SAASG,eAAeD,SAAA,EAAW;MACjC,MAAME,CAAA,GAAI,EAAE;MACZF,SAAA,CAAUG,KAAA,CAAM,GAAG,EAAEC,OAAA,CAAQ,UAAUC,CAAA,EAAG;QACxCH,CAAA,CAAE1I,IAAA,CAAKiF,UAAA,CAAW4D,CAAC,CAAC;MAC5B,CAAO;MAED,MAAMC,MAAA,GAAS,IAAIC,OAAA,CAAS;MAC5BD,MAAA,CAAOE,GAAA,CAAIN,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,EAAE,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,EAAE,GAAG,GAAK,GAAK,GAAK,CAAG;MAEvG,OAAOI,MAAA;IACR;IAED,SAASG,gBAAgBC,UAAA,EAAY;MACnC,MAAMC,UAAA,GAAa;QACjBpG,IAAA,EAAMmG,UAAA,CAAWrG,YAAA,CAAa,MAAM;MACrC;MAED,MAAMC,EAAA,GAAKoG,UAAA,CAAWrG,YAAA,CAAa,IAAI;MAEvC,IAAIC,EAAA,EAAI;QACNqG,UAAA,CAAW,IAAI,IAAIrG,EAAA;MACpB;MAED,MAAM+E,GAAA,GAAMqB,UAAA,CAAWrG,YAAA,CAAa,KAAK;MAEzC,IAAIgF,GAAA,EAAK;QACPsB,UAAA,CAAW,KAAK,IAAItB,GAAA;MACrB;MAED,MAAMuB,MAAA,GAASF,UAAA,CAAWrG,YAAA,CAAa,QAAQ;MAE/C,IAAIuG,MAAA,EAAQ;QACVD,UAAA,CAAW,QAAQ,IAAIC,MAAA;MACxB;MAED,MAAMC,SAAA,GAAYH,UAAA,CAAWrG,YAAA,CAAa,WAAW;MAErD,IAAIwG,SAAA,EAAW;QACbF,UAAA,CAAW,WAAW,IAAIE,SAAA;MAC3B;MAED,MAAMC,UAAA,GAAaJ,UAAA,CAAWrG,YAAA,CAAa,YAAY;MAEvD,IAAIyG,UAAA,EAAY;QACdH,UAAA,CAAW,YAAY,IAAIG,UAAA;MAC5B;MAED,MAAM7H,IAAA,GAAOyH,UAAA,CAAWrG,YAAA,CAAa,MAAM;MAE3C,IAAIpB,IAAA,EAAM;QACR0H,UAAA,CAAW,MAAM,IAAI1H,IAAA;MACtB;MAED,MAAMkF,QAAA,GAAWuC,UAAA,CAAW9H,aAAA,CAAc,MAAM;MAEhD,IAAIuF,QAAA,EAAU;QACZwC,UAAA,CAAW,MAAM,IAAIzC,aAAA,CAAcC,QAAQ;MAC5C;MAED,MAAMuB,cAAA,GAAiBgB,UAAA,CAAW9H,aAAA,CAAc,YAAY;MAE5D,IAAI8G,cAAA,EAAgB;QAClBiB,UAAA,CAAW,YAAY,IAAIlB,mBAAA,CAAoBC,cAAc;MAC9D;MAED,OAAOiB,UAAA;IACR;IAED,SAASI,mBAAmBC,aAAA,EAAe;MACzC,MAAMC,aAAA,GAAgB,CAAE;MAExBA,aAAA,CAAc,eAAe,IAAI,CAAE;MACnC,MAAMC,kBAAA,GAAqBF,aAAA,CAAc/G,gBAAA,CAAiB,eAAe;MAEzE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIkJ,kBAAA,CAAmBjJ,MAAA,EAAQD,CAAA,IAAK;QAClD,MAAMgD,iBAAA,GAAoBkG,kBAAA,CAAmBlJ,CAAC;QAC9C,MAAMiD,iBAAA,GAAoBF,sBAAA,CAAuBC,iBAAiB;QAClEiG,aAAA,CAAc,eAAe,EAAEhG,iBAAA,CAAkB,IAAI,CAAC,IAAIA,iBAAA;MAC3D;MAIDgG,aAAA,CAAc,WAAW,IAAI,CAAE;MAC/B,MAAME,eAAA,GAAkBH,aAAA,CAAc/G,gBAAA,CAAiB,WAAW;MAElE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAImJ,eAAA,CAAgBlJ,MAAA,EAAQD,CAAA,IAAK;QAC/C,MAAMoJ,cAAA,GAAiBD,eAAA,CAAgBnJ,CAAC;QACxC,MAAMqJ,aAAA,GAAgB7F,kBAAA,CAAmB4F,cAAc;QACvDH,aAAA,CAAc,WAAW,EAAEI,aAAA,CAAc,IAAI,CAAC,IAAIA,aAAA;MACnD;MAIDJ,aAAA,CAAc,YAAY,IAAI,CAAE;MAChC,MAAMK,eAAA,GAAkBN,aAAA,CAAc/G,gBAAA,CAAiB,YAAY;MAEnE,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIsJ,eAAA,CAAgBrJ,MAAA,EAAQD,CAAA,IAAK;QAC/C,MAAM4E,cAAA,GAAiB0E,eAAA,CAAgBtJ,CAAC;QACxC,MAAM6E,cAAA,GAAiBF,mBAAA,CAAoBC,cAAc;QACzDqE,aAAA,CAAc,YAAY,EAAEpE,cAAA,CAAe,IAAI,CAAC,IAAIA,cAAA;MACrD;MAIDoE,aAAA,CAAc,6BAA6B,IAAI,CAAE;MACjD,MAAMM,gCAAA,GAAmCP,aAAA,CAAc/G,gBAAA,CAAiB,6BAA6B;MAErG,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIuJ,gCAAA,CAAiCtJ,MAAA,EAAQD,CAAA,IAAK;QAChE,MAAMwJ,+BAAA,GAAkCD,gCAAA,CAAiCvJ,CAAC;QAC1E,MAAMyJ,+BAAA,GAAkC/D,kCAAA,CAAmC8D,+BAA+B;QAC1GP,aAAA,CAAc,6BAA6B,EACzCQ,+BAAA,CAAgC,IAAI,CAC9C,IAAYA,+BAAA;MACL;MAIDR,aAAA,CAAc,gBAAgB,IAAI,CAAE;MACpC,MAAMS,oBAAA,GAAuBV,aAAA,CAAc/G,gBAAA,CAAiB,gBAAgB;MAE5E,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAI0J,oBAAA,CAAqBzJ,MAAA,EAAQD,CAAA,IAAK;QACpD,MAAM2J,mBAAA,GAAsBD,oBAAA,CAAqB1J,CAAC;QAClD,MAAM4J,mBAAA,GAAsB7F,wBAAA,CAAyB4F,mBAAmB;QACxEV,aAAA,CAAc,gBAAgB,EAAEW,mBAAA,CAAoB,IAAI,CAAC,IAAIA,mBAAA;MAC9D;MAIDX,aAAA,CAAc,QAAQ,IAAI,CAAE;MAC5B,MAAMY,WAAA,GAAcb,aAAA,CAAc/G,gBAAA,CAAiB,QAAQ;MAE3D,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAI6J,WAAA,CAAY5J,MAAA,EAAQD,CAAA,IAAK;QAC3C,MAAM0I,UAAA,GAAamB,WAAA,CAAY7J,CAAC;QAChC,MAAM2I,UAAA,GAAaF,eAAA,CAAgBC,UAAU;QAC7CO,aAAA,CAAc,QAAQ,EAAEN,UAAA,CAAW,IAAI,CAAC,IAAIA,UAAA;MAC7C;MAED,OAAOM,aAAA;IACR;IAED,SAASa,eAAeC,SAAA,EAAW;MACjC,MAAMC,SAAA,GAAY,EAAE;MACpB,MAAMC,SAAA,GAAYF,SAAA,CAAU9H,gBAAA,CAAiB,MAAM;MAEnD,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIiK,SAAA,CAAUhK,MAAA,EAAQD,CAAA,IAAK;QACzC,MAAMkK,QAAA,GAAWD,SAAA,CAAUjK,CAAC;QAC5B,MAAMmK,SAAA,GAAY;UAChBC,QAAA,EAAUF,QAAA,CAAS7H,YAAA,CAAa,UAAU;QAC3C;QACD,MAAM2F,SAAA,GAAYkC,QAAA,CAAS7H,YAAA,CAAa,WAAW;QAEnD,IAAI2F,SAAA,EAAW;UACbmC,SAAA,CAAU,WAAW,IAAIlC,cAAA,CAAeD,SAAS;QAClD;QAEDgC,SAAA,CAAUxK,IAAA,CAAK2K,SAAS;MACzB;MAED,OAAOH,SAAA;IACR;IAED,SAAS1I,eAAeX,SAAA,EAAW;MACjC,MAAMU,SAAA,GAAY;QAAEgJ,IAAA,EAAM1J,SAAA,CAAU0B,YAAA,CAAa,MAAM,KAAK;MAAc;MAC1E,MAAMI,aAAA,GAAgB9B,SAAA,CAAUsB,gBAAA,CAAiB,UAAU;MAE3D,IAAIQ,aAAA,EAAe;QACjBpB,SAAA,CAAU,UAAU,IAAImB,kBAAA,CAAmBC,aAAa;MACzD;MAED,MAAMuG,aAAA,GAAgBrI,SAAA,CAAUC,aAAA,CAAc,WAAW;MAEzD,IAAIoI,aAAA,EAAe;QACjB3H,SAAA,CAAU,WAAW,IAAI0H,kBAAA,CAAmBC,aAAa;MAC1D;MAED,MAAMe,SAAA,GAAYpJ,SAAA,CAAUC,aAAA,CAAc,OAAO;MAEjD,IAAImJ,SAAA,EAAW;QACb1I,SAAA,CAAU,OAAO,IAAIyI,cAAA,CAAeC,SAAS;MAC9C;MAED,OAAO1I,SAAA;IACR;IAED,SAASiJ,aAAaC,cAAA,EAAgBC,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa;MACrE,MAAMvG,KAAA,GAAQqG,cAAA,CAAerG,KAAA;MAC7B,MAAMwG,UAAA,GAAarJ,SAAA,CAAUsJ,SAAA,CAAUC,SAAA;MACvC,MAAMA,SAAA,GAAYF,UAAA,CAAWxG,KAAK;MAElC,IAAI0G,SAAA,EAAW;QACb,MAAMpM,KAAA,GAAOiM,WAAA,CAAYG,SAAA,CAAUpN,IAAI;QACvC,MAAM+E,IAAA,GAAOqI,SAAA,CAAUjH,WAAA;QAEvB,MAAMkH,IAAA,GAAO,IAAIC,IAAA,CAAK,CAACtM,KAAI,GAAG;UAAE+D;QAAA,CAAY;QAC5C,MAAMwI,SAAA,GAAYC,GAAA,CAAIC,eAAA,CAAgBJ,IAAI;QAE1C,MAAMjJ,OAAA,GAAUvD,aAAA,CAActB,IAAA,CAAKgO,SAAA,EAAW,YAAY;UACxDC,GAAA,CAAIE,eAAA,CAAgBH,SAAS;QACvC,CAAS;QAED,IAAI,gBAAgBnJ,OAAA,EAASA,OAAA,CAAQuJ,UAAA,GAAa,YAC7CvJ,OAAA,CAAQwJ,QAAA,GAAW;QAIxB,QAAQR,SAAA,CAAUhH,UAAA;UAChB,KAAK;YACHhC,OAAA,CAAQyJ,KAAA,GAAQC,cAAA;YAChB;UAEF,KAAK;YACH1J,OAAA,CAAQyJ,KAAA,GAAQE,sBAAA;YAChB;UAEF,KAAK;UACL,KAAK;YACH3J,OAAA,CAAQyJ,KAAA,GAAQG,mBAAA;YAChB;UAEF;YACE5J,OAAA,CAAQyJ,KAAA,GAAQC,cAAA;QACnB;QAED,QAAQV,SAAA,CAAU/G,UAAA;UAChB,KAAK;YACHjC,OAAA,CAAQ6J,KAAA,GAAQH,cAAA;YAChB;UAEF,KAAK;YACH1J,OAAA,CAAQ6J,KAAA,GAAQF,sBAAA;YAChB;UAEF,KAAK;UACL,KAAK;YACH3J,OAAA,CAAQ6J,KAAA,GAAQD,mBAAA;YAChB;UAEF;YACE5J,OAAA,CAAQ6J,KAAA,GAAQH,cAAA;QACnB;QAED,QAAQV,SAAA,CAAU9G,MAAA;UAChB,KAAK;YACHlC,OAAA,CAAQ8J,SAAA,GAAYC,YAAA;YACpB/J,OAAA,CAAQgK,SAAA,GAAYC,wBAAA;YACpB;UAEF,KAAK;YACHjK,OAAA,CAAQ8J,SAAA,GAAYC,YAAA;YACpB/J,OAAA,CAAQgK,SAAA,GAAYD,YAAA;YACpB;UAEF,KAAK;YACH/J,OAAA,CAAQ8J,SAAA,GAAYI,aAAA;YACpBlK,OAAA,CAAQgK,SAAA,GAAYE,aAAA;YACpB;UAEF;YACElK,OAAA,CAAQ8J,SAAA,GAAYC,YAAA;YACpB/J,OAAA,CAAQgK,SAAA,GAAYC,wBAAA;QACvB;QAED,OAAOjK,OAAA;MACf,OAAa;QACL,OAAO;MACR;IACF;IAED,SAASmK,yBACP7I,aAAA,EACAyD,kBAAA,EACAP,QAAA,EACAoE,QAAA,EACAnJ,SAAA,EACAoJ,WAAA,EACA9B,UAAA,EACA;MACA,MAAMqD,YAAA,GAAerD,UAAA,CAAWC,MAAA;MAEhC,MAAMqD,WAAA,GAAc,CAAE;MAEtB,SAASjM,CAAA,GAAI,GAAGkM,CAAA,GAAIvF,kBAAA,CAAmB1G,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QACzD,MAAMsH,gBAAA,GAAmBX,kBAAA,CAAmB3G,CAAC;QAC7C,MAAM4I,MAAA,GAAStB,gBAAA,CAAiBJ,EAAA,KAAO,SAAYI,gBAAA,CAAiBJ,EAAA,GAAK8E,YAAA;QAEzE,IAAIC,WAAA,CAAYrD,MAAM,MAAM,QAAWqD,WAAA,CAAYrD,MAAM,IAAI,EAAE;QAE/DqD,WAAA,CAAYrD,MAAM,EAAEpJ,IAAA,CAAK8H,gBAAgB;MAC1C;MAID,MAAM9F,IAAA,GAAOD,MAAA,CAAOC,IAAA,CAAKyK,WAAW;MACpC,MAAME,MAAA,GAAS,EAAE;MAEjB,SAASnM,CAAA,GAAI,GAAGkM,CAAA,GAAI1K,IAAA,CAAKvB,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QAC3C,MAAMoM,aAAA,GAAgB5K,IAAA,CAAKxB,CAAC;QAC5B,MAAMqM,uBAAA,GAA0BJ,WAAA,CAAYG,aAAa;QACzD,MAAM/I,gBAAA,GAAmBH,aAAA,CAAcA,aAAA,CAAckJ,aAAa;QAClE,MAAME,QAAA,GAAWC,QAAA,CAASlJ,gBAAA,EAAkBmH,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY6D,iBAAiB;QAI1G,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;QAErC,MAAMC,YAAA,GAAe,EAAE;QAEvB,MAAMtG,QAAA,GAAWD,QAAA,CAASC,QAAA;QAE1B,SAASuG,CAAA,GAAI,GAAGC,EAAA,GAAKR,uBAAA,CAAwBpM,MAAA,EAAQ2M,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UAChE,MAAMtF,gBAAA,GAAmB+E,uBAAA,CAAwBO,CAAC;UAElDD,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;UAEvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;UAEvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;UACvD0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;QACxD;QAEDwF,QAAA,CAASK,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBJ,YAAA,EAAc,CAAC,CAAC;QAI7E,MAAMK,IAAA,GAAO,IAAIC,IAAA,CAAKR,QAAA,EAAUH,QAAQ;QACxCH,MAAA,CAAO3M,IAAA,CAAKwN,IAAI;MACjB;MAED,OAAOb,MAAA;IACR;IAED,SAASe,kBACP3C,cAAA,EACA5D,kBAAA,EACAP,QAAA,EACAoE,QAAA,EACAnJ,SAAA,EACAoJ,WAAA,EACA9B,UAAA,EACA;MAGA,MAAM8D,QAAA,GAAW,IAAIC,cAAA,CAAgB;MAErC,MAAMC,YAAA,GAAe,EAAE;MACvB,MAAMQ,MAAA,GAAS,EAAE;MAEjB,MAAM9G,QAAA,GAAWD,QAAA,CAASC,QAAA;MAC1B,MAAMhC,GAAA,GAAMkG,cAAA,CAAelG,GAAA;MAE3B,SAASrE,CAAA,GAAI,GAAGkM,CAAA,GAAIvF,kBAAA,CAAmB1G,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QACzD,MAAMsH,gBAAA,GAAmBX,kBAAA,CAAmB3G,CAAC;QAE7C2M,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBP,EAAA,GAAK,IAAI,CAAC,CAAC;QAEvD4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBN,EAAA,GAAK,IAAI,CAAC,CAAC;QAEvD2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;QACvD0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASiB,gBAAA,CAAiBL,EAAA,GAAK,IAAI,CAAC,CAAC;QAIvDkG,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBJ,EAAA,GAAK,IAAI,CAAC,CAAC;QAC5CiG,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBJ,EAAA,GAAK,IAAI,CAAC,CAAC;QAE5CiG,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBH,EAAA,GAAK,IAAI,CAAC,CAAC;QAC5CgG,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBH,EAAA,GAAK,IAAI,CAAC,CAAC;QAE5CgG,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBF,EAAA,GAAK,IAAI,CAAC,CAAC;QAC5C+F,MAAA,CAAO3N,IAAA,CAAK6E,GAAA,CAAIiD,gBAAA,CAAiBF,EAAA,GAAK,IAAI,CAAC,CAAC;MAC7C;MAEDqF,QAAA,CAASK,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBJ,YAAA,EAAc,CAAC,CAAC;MAC7EF,QAAA,CAASK,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBI,MAAA,EAAQ,CAAC,CAAC;MAIjE,MAAMvL,OAAA,GAAU2K,QAAA,CAAShC,cAAA,EAAgBC,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY2B,YAAY;MAElG,MAAMgC,QAAA,GAAW,IAAIc,iBAAA,CAAkB;QAAEC,GAAA,EAAKzL,OAAA;QAAS0L,WAAA,EAAa;MAAA,CAAM;MAI1E,MAAMN,IAAA,GAAO,IAAIC,IAAA,CAAKR,QAAA,EAAUH,QAAQ;MAExC,OAAOU,IAAA;IACR;IAED,SAASO,qBAAqBC,UAAA,EAAY7G,kBAAA,EAAoBP,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWsH,UAAA,EAAY;MAGtG,MAAM8D,QAAA,GAAW,IAAIC,cAAA,CAAgB;MAErC,MAAMC,YAAA,GAAe,EAAE;MACvB,MAAMc,SAAA,GAAY,EAAE;MAEpB,MAAMpH,QAAA,GAAWD,QAAA,CAASC,QAAA;MAC1B,MAAMtB,MAAA,GAASyI,UAAA,CAAWzI,MAAA;MAE1B,SAAS/E,CAAA,GAAI,GAAGkM,CAAA,GAAIvF,kBAAA,CAAmB1G,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QACzD,MAAMsH,gBAAA,GAAmBX,kBAAA,CAAmB3G,CAAC;QAE7C,MAAM+G,EAAA,GAAKO,gBAAA,CAAiBP,EAAA;QAC5B,MAAMC,EAAA,GAAKM,gBAAA,CAAiBN,EAAA;QAC5B,MAAMC,EAAA,GAAKK,gBAAA,CAAiBL,EAAA;QAE5B0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASU,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASU,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASU,EAAA,GAAK,IAAI,CAAC,CAAC;QAEtC4F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASW,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASW,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASW,EAAA,GAAK,IAAI,CAAC,CAAC;QAEtC2F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASY,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASY,EAAA,GAAK,IAAI,CAAC,CAAC;QACtC0F,YAAA,CAAanN,IAAA,CAAK6G,QAAA,CAASY,EAAA,GAAK,IAAI,CAAC,CAAC;QAItC,MAAMC,EAAA,GAAKI,gBAAA,CAAiBJ,EAAA,KAAO,SAAYI,gBAAA,CAAiBJ,EAAA,GAAKyB,UAAA,CAAWC,MAAA;QAChF,MAAMzB,EAAA,GAAKG,gBAAA,CAAiBH,EAAA,KAAO,SAAYG,gBAAA,CAAiBH,EAAA,GAAKD,EAAA;QACrE,MAAME,EAAA,GAAKE,gBAAA,CAAiBF,EAAA,KAAO,SAAYE,gBAAA,CAAiBF,EAAA,GAAKF,EAAA;QAErEuG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOmC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCuG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOmC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCuG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOmC,EAAA,GAAK,IAAI,CAAC,CAAC;QAEjCuG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOoC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCsG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOoC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCsG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOoC,EAAA,GAAK,IAAI,CAAC,CAAC;QAEjCsG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOqC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCqG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOqC,EAAA,GAAK,IAAI,CAAC,CAAC;QACjCqG,SAAA,CAAUjO,IAAA,CAAKuF,MAAA,CAAOqC,EAAA,GAAK,IAAI,CAAC,CAAC;MAClC;MAEDqF,QAAA,CAASK,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBJ,YAAA,EAAc,CAAC,CAAC;MAC7EF,QAAA,CAASK,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBU,SAAA,EAAW,CAAC,CAAC;MAIvE,MAAMnB,QAAA,GAAW,IAAIc,iBAAA,CAAkB;QAAEM,YAAA,EAAc;QAAMJ,WAAA,EAAa;MAAA,CAAM;MAIhF,MAAMN,IAAA,GAAO,IAAIC,IAAA,CAAKR,QAAA,EAAUH,QAAQ;MAExC,OAAOU,IAAA;IACR;IAED,SAASW,iBAAiBvH,QAAA,EAAU;MAClC,MAAMqG,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACrCD,QAAA,CAASmB,QAAA,CAAS,IAAIC,eAAA,CAAgBzH,QAAA,CAAS,WAAW,GAAG,CAAC,CAAC;MAC/DqG,QAAA,CAASK,YAAA,CAAa,YAAY,IAAIe,eAAA,CAAgBzH,QAAA,CAAS,UAAU,GAAG,CAAC,CAAC;MAE9E,MAAMkG,QAAA,GAAW,IAAIc,iBAAA,CAAkB;QAAEjI,KAAA,EAAO;QAAUmI,WAAA,EAAa;MAAA,CAAM;MAE7E,MAAMN,IAAA,GAAO,IAAIC,IAAA,CAAKR,QAAA,EAAUH,QAAQ;MAExC,OAAOU,IAAA;IACR;IAED,SAASc,YAAYC,WAAA,EAAa3H,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY;MACvF,MAAMnH,IAAA,GAAOD,MAAA,CAAOC,IAAA,CAAKuM,WAAW;MACpC,MAAM5B,MAAA,GAAS,EAAE;MAEjB,SAASnM,CAAA,GAAI,GAAGgO,EAAA,GAAKxM,IAAA,CAAKvB,MAAA,EAAQD,CAAA,GAAIgO,EAAA,EAAIhO,CAAA,IAAK;QAC7C,MAAMiO,UAAA,GAAazM,IAAA,CAAKxB,CAAC;QACzB,MAAM2G,kBAAA,GAAqBoH,WAAA,CAAYE,UAAU;QACjD,MAAMC,YAAA,GAAeC,eAAA,CAAgBF,UAAA,EAAY5M,SAAS;QAE1D,QAAQ6M,YAAA;UACN,KAAK;YACH,MAAMhL,aAAA,GAAgB7B,SAAA,CAAUsJ,SAAA,CAAUzH,aAAA,CAAc+K,UAAU;YAClE,MAAMG,SAAA,GAAYrC,wBAAA,CAChB7I,aAAA,EACAyD,kBAAA,EACAP,QAAA,EACAoE,QAAA,EACAnJ,SAAA,EACAoJ,WAAA,EACA9B,UACD;YAED,SAASiE,CAAA,GAAI,GAAGC,EAAA,GAAKuB,SAAA,CAAUnO,MAAA,EAAQ2M,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;cAClDT,MAAA,CAAO3M,IAAA,CAAK4O,SAAA,CAAUxB,CAAC,CAAC;YACzB;YAED;UAEF,KAAK;YACH,MAAMrC,cAAA,GAAiBlJ,SAAA,CAAUsJ,SAAA,CAAUJ,cAAA,CAAe0D,UAAU;YACpE9B,MAAA,CAAO3M,IAAA,CACL0N,iBAAA,CACE3C,cAAA,EACA5D,kBAAA,EACAP,QAAA,EACAoE,QAAA,EACAnJ,SAAA,EACAoJ,WAAA,EACA9B,UACD,CACF;YACD;UAEF,KAAK;YACH,MAAM6E,UAAA,GAAanM,SAAA,CAAUsJ,SAAA,CAAU6C,UAAA,CAAWS,UAAU;YAC5D9B,MAAA,CAAO3M,IAAA,CAAK+N,oBAAA,CAAqBC,UAAA,EAAY7G,kBAAA,EAAoBP,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWsH,UAAU,CAAC;YAC1G;UAEF,KAAK;YACHwD,MAAA,CAAO3M,IAAA,CAAKmO,gBAAA,CAAiBvH,QAAQ,CAAC;YACtC;UAEF;YACEnI,OAAA,CAAQC,KAAA,CAAM,6CAA6C;QAC9D;MACF;MAED,OAAOiO,MAAA;IACR;IAED,SAASgC,gBAAgB9G,GAAA,EAAKhG,SAAA,EAAW;MACvC,IAAIA,SAAA,CAAUsJ,SAAA,CAAUJ,cAAA,CAAelD,GAAG,MAAM,QAAW;QACzD,OAAO;MACf,WAAiBhG,SAAA,CAAUsJ,SAAA,CAAUzH,aAAA,CAAcmE,GAAG,MAAM,QAAW;QAC/D,OAAO;MACf,WAAiBhG,SAAA,CAAUsJ,SAAA,CAAU6C,UAAA,CAAWnG,GAAG,MAAM,QAAW;QAC5D,OAAO;MACf,WAAiBA,GAAA,KAAQ,WAAW;QAC5B,OAAO;MACf,OAAa;QACL,OAAO;MACR;IACF;IAED,SAASgH,cAAchN,SAAA,EAAW+E,QAAA,EAAUuC,UAAA,EAAY;MACtD,MAAMoF,WAAA,GAAc,CAAE;MAEtB,MAAMpH,kBAAA,GAAqBP,QAAA,CAAS,oBAAoB;MAExD,MAAMkI,SAAA,GAAY3F,UAAA,CAAWtB,GAAA;MAE7B,SAASrH,CAAA,GAAI,GAAGkM,CAAA,GAAIvF,kBAAA,CAAmB1G,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QACzD,MAAMsH,gBAAA,GAAmBX,kBAAA,CAAmB3G,CAAC;QAC7C,IAAIqH,GAAA,GAAMC,gBAAA,CAAiBD,GAAA,KAAQ,SAAYC,gBAAA,CAAiBD,GAAA,GAAMiH,SAAA;QAEtE,IAAIjH,GAAA,KAAQ,QAAWA,GAAA,GAAM;QAE7B,IAAI0G,WAAA,CAAY1G,GAAG,MAAM,QAAW0G,WAAA,CAAY1G,GAAG,IAAI,EAAE;QAEzD0G,WAAA,CAAY1G,GAAG,EAAE7H,IAAA,CAAK8H,gBAAgB;MACvC;MAED,OAAOyG,WAAA;IACR;IAED,SAASQ,WAAWnI,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY;MACzE,MAAM6F,KAAA,GAAQ,IAAIC,KAAA,CAAO;MAEzB,MAAMV,WAAA,GAAcM,aAAA,CAAchN,SAAA,EAAW+E,QAAA,EAAUuC,UAAU;MACjE,MAAMwD,MAAA,GAAS2B,WAAA,CAAYC,WAAA,EAAa3H,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAU;MAE7F,SAAS3I,CAAA,GAAI,GAAGkM,CAAA,GAAIC,MAAA,CAAOlM,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;QAC7CwO,KAAA,CAAME,GAAA,CAAIvC,MAAA,CAAOnM,CAAC,CAAC;MACpB;MAED,OAAOwO,KAAA;IACR;IAED,SAASG,gBAAgB9N,UAAA,EAAYuF,QAAA,EAAUwI,QAAA,EAAU;MACvD,IAAI,CAAC/N,UAAA,EAAY;QACf;MACD;MAED,MAAM/D,mBAAA,GAAsB,EAAE;MAC9B,MAAM0E,IAAA,GAAOD,MAAA,CAAOC,IAAA,CAAKX,UAAU;MAEnC,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAIwB,IAAA,CAAKvB,MAAA,EAAQD,CAAA,IAAK;QACpC,MAAM6O,EAAA,GAAKrN,IAAA,CAAKxB,CAAC;QAEjB,SAAS4M,CAAA,GAAI,GAAGA,CAAA,GAAIxP,KAAA,CAAMN,mBAAA,CAAoBmD,MAAA,EAAQ2M,CAAA,IAAK;UACzD,MAAMkC,SAAA,GAAY1R,KAAA,CAAMN,mBAAA,CAAoB8P,CAAC;UAE7C,IAAIkC,SAAA,CAAUD,EAAA,KAAOA,EAAA,EAAI;YACvB/R,mBAAA,CAAoB0C,IAAA,CAAKsP,SAAS;UACnC;QACF;MACF;MAED,SAAS9O,CAAA,GAAI,GAAGA,CAAA,GAAIlD,mBAAA,CAAoBmD,MAAA,EAAQD,CAAA,IAAK;QACnD,MAAM8O,SAAA,GAAYhS,mBAAA,CAAoBkD,CAAC;QACvC8O,SAAA,CAAUC,KAAA,CAAMH,QAAA,EAAU/N,UAAA,CAAWiO,SAAA,CAAU,IAAI,CAAC,GAAG1I,QAAQ;MAChE;IACF;IAED,SAASmG,SAAS/N,KAAA,EAAMgM,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAYqG,OAAA,EAAS;MAC5E,IAAIxQ,KAAA,CAAKyQ,KAAA,KAAU,QAAW,OAAOzQ,KAAA,CAAKyQ,KAAA;MAE1CzQ,KAAA,CAAKyQ,KAAA,GAAQD,OAAA,CAAQxQ,KAAA,EAAMgM,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAU;MAEtE,OAAOnK,KAAA,CAAKyQ,KAAA;IACb;IAED,SAASzC,kBAAkB0C,YAAA,EAAc1E,QAAA,EAASnJ,SAAA,EAAW;MAC3D,IAAIiL,QAAA;MAEJ,MAAMnI,mBAAA,GAAsB+K,YAAA,CAAa/K,mBAAA;MACzC,MAAMgL,2BAAA,GAA8B9N,SAAA,CAAUsJ,SAAA,CAAUwE,2BAAA;MAExD,IAAIhL,mBAAA,KAAwB,QAAQgL,2BAAA,CAA4BhL,mBAAmB,MAAM,QAAW;QAGlG,MAAMiL,yBAAA,GAA4BD,2BAAA,CAA4BhL,mBAAmB;QACjF,MAAM2B,YAAA,GAAesJ,yBAAA,CAA0BhR,IAAA,CAAK8Q,YAAA,CAAa3L,KAAK;QAEtE+I,QAAA,GAAW,IAAI+C,oBAAA,CAAqB;UAClC/B,WAAA,EAAa;UACbrH,SAAA,EAAWH,YAAA,CAAaG,SAAA;UACxBqJ,SAAA,EAAWxJ,YAAA,CAAaE;QAClC,CAAS;MACT,OAAa;QAGLsG,QAAA,GAAW,IAAIc,iBAAA,CAAkB;UAAEE,WAAA,EAAa;QAAI,CAAE;MACvD;MAEDhB,QAAA,CAASrL,IAAA,GAAOiO,YAAA,CAAajO,IAAA;MAI7B,MAAMsO,YAAA,GAAeL,YAAA,CAAaK,YAAA;MAElC,MAAMpK,KAAA,GAAQoK,YAAA,CAAalK,SAAA,CAAU,GAAG,CAAC;MACzCiH,QAAA,CAASnH,KAAA,CAAMC,QAAA,CAASD,KAAK;MAC7BmH,QAAA,CAASnH,KAAA,CAAMG,mBAAA,CAAqB;MAIpC,IAAIiK,YAAA,CAAatP,MAAA,KAAW,GAAG;QAC7BqM,QAAA,CAASkD,OAAA,GAAUjI,QAAA,CAASgI,YAAA,CAAaE,MAAA,CAAO,CAAC,IAAIF,YAAA,CAAaE,MAAA,CAAO,CAAC,GAAG,EAAE,IAAI;MACpF;MAED,OAAOnD,QAAA;IACR;IAED,SAASoD,eAAeC,aAAA,EAAenF,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa;MACtE,MAAMmF,SAAA,GAAY,IAAInB,KAAA,CAAO;MAE7B,SAAS7B,CAAA,GAAI,GAAGA,CAAA,GAAI+C,aAAA,CAAc1P,MAAA,EAAQ2M,CAAA,IAAK;QAC7C,MAAMiD,SAAA,GAAYF,aAAA,CAAc/C,CAAC;QACjC,IAAIkD,MAAA,GAAQtF,QAAA,CAAQqF,SAAA,CAAUzF,QAAQ;QAEtC,IAAI0F,MAAA,KAAU,QAAW;UACvBC,WAAA,CAAYF,SAAA,CAAUzF,QAAA,EAAUI,QAAA,EAASnJ,SAAA,EAAWoJ,WAAW;UAC/DqF,MAAA,GAAQtF,QAAA,CAAQqF,SAAA,CAAUzF,QAAQ;QACnC;QAED,MAAM4F,QAAA,GAAWF,MAAA,CAAMG,KAAA,CAAO;QAI9B,MAAMjI,SAAA,GAAY6H,SAAA,CAAU7H,SAAA;QAE5B,IAAIA,SAAA,EAAW;UACbgI,QAAA,CAASE,YAAA,CAAalI,SAAS;QAChC;QAED4H,SAAA,CAAUlB,GAAA,CAAIsB,QAAQ;MACvB;MAED,OAAOJ,SAAA;IACR;IAED,SAASG,YAAY3F,QAAA,EAAUI,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa;MAC9D,MAAM9B,UAAA,GAAatH,SAAA,CAAU,WAAW,EAAE,QAAQ,EAAE+I,QAAQ;MAE5D,IAAIzB,UAAA,CAAW,MAAM,GAAG;QACtB,MAAMvC,QAAA,GAAWuC,UAAA,CAAW,MAAM;QAElC,MAAM9H,UAAA,GAAaQ,SAAA,CAAU,YAAY;QACzC,MAAMuN,QAAA,GAAWvN,SAAA,CAAU,KAAK;QAEhCsN,eAAA,CAAgB9N,UAAA,EAAYuF,QAAA,EAAUwI,QAAQ;QAE9CpE,QAAA,CAAQ7B,UAAA,CAAWrG,EAAE,IAAIiK,QAAA,CAASnG,QAAA,EAAUoE,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY4F,UAAU;MAC3G,OAAa;QACL,MAAMoB,aAAA,GAAgBhH,UAAA,CAAW,YAAY;QAE7C6B,QAAA,CAAQ7B,UAAA,CAAWrG,EAAE,IAAIiK,QAAA,CAASoD,aAAA,EAAenF,QAAA,EAASnJ,SAAA,EAAWoJ,WAAA,EAAa9B,UAAA,EAAY+G,cAAc;MAC7G;IACF;IAED,SAASS,aAAaC,QAAA,EAAS;MAC7B,MAAMC,UAAA,GAAaD,QAAA,CAAQ1O,KAAA;MAC3B,MAAM3C,SAAA,GAAYqR,QAAA,CAAQrR,SAAA;MAC1B,MAAMyL,QAAA,GAAU,CAAE;MAClB,MAAM8F,UAAA,GAAa/O,MAAA,CAAOC,IAAA,CAAK6O,UAAU;MACzC,MAAM5F,WAAA,GAAc,CAAE;MAItB,IAAI1L,SAAA,EAAW;QACb,SAASiB,CAAA,GAAI,GAAGkM,CAAA,GAAInN,SAAA,CAAUkB,MAAA,EAAQD,CAAA,GAAIkM,CAAA,EAAGlM,CAAA,IAAK;UAChD,MAAMuQ,QAAA,GAAWxR,SAAA,CAAUiB,CAAC;UAC5B,MAAMwQ,UAAA,GAAaD,QAAA,CAASnO,MAAA,CAAOiD,SAAA,CAAU,CAAC;UAE9C,IAAI+K,QAAA,CAAQxO,OAAA,CAAQ4O,UAAU,GAAG;YAC/B/F,WAAA,CAAY8F,QAAA,CAASnO,MAAM,IAAIgO,QAAA,CAAQxO,OAAA,CAAQ4O,UAAU;UAC1D;QACF;MACF;MAID,SAASxQ,CAAA,GAAI,GAAGA,CAAA,GAAIsQ,UAAA,CAAWrQ,MAAA,EAAQD,CAAA,IAAK;QAC1C,MAAMyQ,SAAA,GAAYH,UAAA,CAAWtQ,CAAC;QAC9B,MAAMqB,SAAA,GAAYgP,UAAA,CAAWI,SAAS;QAEtC,MAAMC,SAAA,GAAYnP,MAAA,CAAOC,IAAA,CAAKH,SAAA,CAAU,WAAW,EAAE,QAAQ,CAAC;QAE9D,SAASuL,CAAA,GAAI,GAAGA,CAAA,GAAI8D,SAAA,CAAUzQ,MAAA,EAAQ2M,CAAA,IAAK;UACzC,MAAMxC,QAAA,GAAWsG,SAAA,CAAU9D,CAAC;UAE5BmD,WAAA,CAAY3F,QAAA,EAAUI,QAAA,EAASnJ,SAAA,EAAWoJ,WAAW;QACtD;MACF;MAED,OAAOD,QAAA;IACR;IAED,SAASmG,iBAAiB/Q,IAAA,EAAM;MAC9B,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAKK,MAAA,EAAQD,CAAA,IAAK;QACpC,MAAM4Q,GAAA,GAAMhR,IAAA,CAAKI,CAAC;QAClB,MAAM8O,SAAA,GAAY8B,GAAA,CAAIxO,MAAA,CAAO+F,KAAA,CAAM,GAAG,EAAE0I,GAAA,CAAK;QAE7C,IAAI/B,SAAA,CAAUpO,WAAA,OAAkB,SAAS,OAAOkQ,GAAA;MACjD;IACF;IAED,SAAS3B,MAAMzE,QAAA,EAAS4F,QAAA,EAAS;MAC/B,MAAM5B,KAAA,GAAQ,IAAIC,KAAA,CAAO;MAEzB,MAAMtM,YAAA,GAAewO,gBAAA,CAAiBP,QAAA,CAAQ,MAAM,CAAC;MACrD,MAAMpG,SAAA,GAAYoG,QAAA,CAAQ1O,KAAA,CAAMS,YAAA,CAAa,QAAQ,EAAEkD,SAAA,CAAU,CAAC,CAAC,EAAE,OAAO;MAE5E,SAASrF,CAAA,GAAI,GAAGA,CAAA,GAAIgK,SAAA,CAAU/J,MAAA,EAAQD,CAAA,IAAK;QACzC,MAAMmK,SAAA,GAAYH,SAAA,CAAUhK,CAAC;QAC7B,MAAMgQ,QAAA,GAAWxF,QAAA,CAAQL,SAAA,CAAU,UAAU,CAAC;QAI9C,MAAMnC,SAAA,GAAYmC,SAAA,CAAU,WAAW;QAEvC,IAAInC,SAAA,EAAW;UACbgI,QAAA,CAASE,YAAA,CAAalI,SAAS;QAChC;QAEDwG,KAAA,CAAME,GAAA,CAAIsB,QAAQ;MACnB;MAED,OAAOxB,KAAA;IACR;IAED,MAAMsC,OAAA,GAAUvS,YAAA,CAAaH,IAAI;IACjC,MAAM2S,OAAA,GAAUZ,YAAA,CAAaW,OAAO;IAEpC,OAAO7B,KAAA,CAAM8B,OAAA,EAASD,OAAO;EAC9B;EAEDE,aAAalC,SAAA,EAAW;IACtB,KAAKhS,mBAAA,CAAoB0C,IAAA,CAAKsP,SAAS;EACxC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}