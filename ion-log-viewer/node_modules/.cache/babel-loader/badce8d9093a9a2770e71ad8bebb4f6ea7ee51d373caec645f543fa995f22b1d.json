{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport class Null extends Value(Object, IonTypes.NULL, FromJsConstructor.NONE) {\n  constructor(ionType = IonTypes.NULL, annotations = []) {\n    super();\n    this._ionType = ionType;\n    this._setAnnotations(annotations);\n  }\n  static _operationIsSupported(ionType, operation) {\n    return Null._supportedIonTypesByOperation.get(operation).has(ionType);\n  }\n  isNull() {\n    return true;\n  }\n  _convertToJsNull(operation) {\n    if (Null._operationIsSupported(this.getType(), operation)) {\n      return null;\n    }\n    throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);\n  }\n  _unsupportedOperationOrNullDereference(operation) {\n    if (Null._operationIsSupported(this.getType(), operation)) {\n      throw new Error(`${operation}() called on a null ${this.getType().name}.`);\n    }\n    throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);\n  }\n  booleanValue() {\n    return this._convertToJsNull(\"booleanValue\");\n  }\n  numberValue() {\n    return this._convertToJsNull(\"numberValue\");\n  }\n  bigIntValue() {\n    return this._convertToJsNull(\"bigIntValue\");\n  }\n  decimalValue() {\n    return this._convertToJsNull(\"decimalValue\");\n  }\n  stringValue() {\n    return this._convertToJsNull(\"stringValue\");\n  }\n  dateValue() {\n    return this._convertToJsNull(\"dateValue\");\n  }\n  uInt8ArrayValue() {\n    return this._convertToJsNull(\"uInt8ArrayValue\");\n  }\n  fieldNames() {\n    this._unsupportedOperationOrNullDereference(\"fieldNames\");\n  }\n  fields() {\n    this._unsupportedOperationOrNullDereference(\"fields\");\n  }\n  elements() {\n    this._unsupportedOperationOrNullDereference(\"elements\");\n  }\n  get(...pathElements) {\n    return null;\n  }\n  toString() {\n    if (this.getType() == IonTypes.NULL) {\n      return \"null\";\n    }\n    return \"null.\" + this._ionType.name;\n  }\n  toJSON() {\n    return null;\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeNull(this.getType());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Null) {\n      isSupportedType = true;\n      valueToCompare = other;\n    } else if (!options.onlyCompareIon) {\n      if (other === null && this._ionType.name === \"null\") {\n        return true;\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    return this._ionType.name === valueToCompare._ionType.name;\n  }\n}\nNull._supportedIonTypesByOperation = new Map([[\"booleanValue\", new Set([IonTypes.BOOL])], [\"numberValue\", new Set([IonTypes.INT, IonTypes.FLOAT, IonTypes.DECIMAL])], [\"bigIntValue\", new Set([IonTypes.INT])], [\"decimalValue\", new Set([IonTypes.DECIMAL])], [\"stringValue\", new Set([IonTypes.STRING, IonTypes.SYMBOL])], [\"dateValue\", new Set([IonTypes.TIMESTAMP])], [\"timestampValue\", new Set([IonTypes.TIMESTAMP])], [\"uInt8ArrayValue\", new Set([IonTypes.BLOB, IonTypes.CLOB])], [\"fields\", new Set([IonTypes.STRUCT])], [\"fieldNames\", new Set([IonTypes.STRUCT])], [\"elements\", new Set([IonTypes.LIST, IonTypes.SEXP, IonTypes.STRUCT])]]);", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructor", "Value", "<PERSON><PERSON>", "Object", "NULL", "NONE", "constructor", "ionType", "annotations", "_ionType", "_setAnnotations", "_operationIsSupported", "operation", "_supportedIonTypesByOperation", "get", "has", "isNull", "_convertToJsNull", "getType", "Error", "name", "_unsupportedOperationOrNullDereference", "booleanValue", "numberValue", "bigIntValue", "decimalValue", "stringValue", "dateValue", "uInt8ArrayValue", "fieldNames", "fields", "elements", "pathElements", "toString", "toJSON", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeNull", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "Map", "Set", "BOOL", "INT", "FLOAT", "DECIMAL", "STRING", "SYMBOL", "TIMESTAMP", "BLOB", "CLOB", "STRUCT", "LIST", "SEXP"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Null.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport class Null extends Value(Object, IonTypes.NULL, FromJsConstructor.NONE) {\n    constructor(ionType = IonTypes.NULL, annotations = []) {\n        super();\n        this._ionType = ionType;\n        this._setAnnotations(annotations);\n    }\n    static _operationIsSupported(ionType, operation) {\n        return Null._supportedIonTypesByOperation.get(operation).has(ionType);\n    }\n    isNull() {\n        return true;\n    }\n    _convertToJsNull(operation) {\n        if (Null._operationIsSupported(this.getType(), operation)) {\n            return null;\n        }\n        throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);\n    }\n    _unsupportedOperationOrNullDereference(operation) {\n        if (Null._operationIsSupported(this.getType(), operation)) {\n            throw new Error(`${operation}() called on a null ${this.getType().name}.`);\n        }\n        throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);\n    }\n    booleanValue() {\n        return this._convertToJsNull(\"booleanValue\");\n    }\n    numberValue() {\n        return this._convertToJsNull(\"numberValue\");\n    }\n    bigIntValue() {\n        return this._convertToJsNull(\"bigIntValue\");\n    }\n    decimalValue() {\n        return this._convertToJsNull(\"decimalValue\");\n    }\n    stringValue() {\n        return this._convertToJsNull(\"stringValue\");\n    }\n    dateValue() {\n        return this._convertToJsNull(\"dateValue\");\n    }\n    uInt8ArrayValue() {\n        return this._convertToJsNull(\"uInt8ArrayValue\");\n    }\n    fieldNames() {\n        this._unsupportedOperationOrNullDereference(\"fieldNames\");\n    }\n    fields() {\n        this._unsupportedOperationOrNullDereference(\"fields\");\n    }\n    elements() {\n        this._unsupportedOperationOrNullDereference(\"elements\");\n    }\n    get(...pathElements) {\n        return null;\n    }\n    toString() {\n        if (this.getType() == IonTypes.NULL) {\n            return \"null\";\n        }\n        return \"null.\" + this._ionType.name;\n    }\n    toJSON() {\n        return null;\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeNull(this.getType());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Null) {\n            isSupportedType = true;\n            valueToCompare = other;\n        }\n        else if (!options.onlyCompareIon) {\n            if (other === null && this._ionType.name === \"null\") {\n                return true;\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        return this._ionType.name === valueToCompare._ionType.name;\n    }\n}\nNull._supportedIonTypesByOperation = new Map([\n    [\"booleanValue\", new Set([IonTypes.BOOL])],\n    [\"numberValue\", new Set([IonTypes.INT, IonTypes.FLOAT, IonTypes.DECIMAL])],\n    [\"bigIntValue\", new Set([IonTypes.INT])],\n    [\"decimalValue\", new Set([IonTypes.DECIMAL])],\n    [\"stringValue\", new Set([IonTypes.STRING, IonTypes.SYMBOL])],\n    [\"dateValue\", new Set([IonTypes.TIMESTAMP])],\n    [\"timestampValue\", new Set([IonTypes.TIMESTAMP])],\n    [\"uInt8ArrayValue\", new Set([IonTypes.BLOB, IonTypes.CLOB])],\n    [\"fields\", new Set([IonTypes.STRUCT])],\n    [\"fieldNames\", new Set([IonTypes.STRUCT])],\n    [\"elements\", new Set([IonTypes.LIST, IonTypes.SEXP, IonTypes.STRUCT])],\n]);\n//# sourceMappingURL=Null.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,MAAMC,IAAI,SAASD,KAAK,CAACE,MAAM,EAAEJ,QAAQ,CAACK,IAAI,EAAEJ,iBAAiB,CAACK,IAAI,CAAC,CAAC;EAC3EC,WAAWA,CAACC,OAAO,GAAGR,QAAQ,CAACK,IAAI,EAAEI,WAAW,GAAG,EAAE,EAAE;IACnD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,GAAGF,OAAO;IACvB,IAAI,CAACG,eAAe,CAACF,WAAW,CAAC;EACrC;EACA,OAAOG,qBAAqBA,CAACJ,OAAO,EAAEK,SAAS,EAAE;IAC7C,OAAOV,IAAI,CAACW,6BAA6B,CAACC,GAAG,CAACF,SAAS,CAAC,CAACG,GAAG,CAACR,OAAO,CAAC;EACzE;EACAS,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI;EACf;EACAC,gBAAgBA,CAACL,SAAS,EAAE;IACxB,IAAIV,IAAI,CAACS,qBAAqB,CAAC,IAAI,CAACO,OAAO,CAAC,CAAC,EAAEN,SAAS,CAAC,EAAE;MACvD,OAAO,IAAI;IACf;IACA,MAAM,IAAIO,KAAK,CAAC,GAAGP,SAAS,mCAAmC,IAAI,CAACM,OAAO,CAAC,CAAC,CAACE,IAAI,EAAE,CAAC;EACzF;EACAC,sCAAsCA,CAACT,SAAS,EAAE;IAC9C,IAAIV,IAAI,CAACS,qBAAqB,CAAC,IAAI,CAACO,OAAO,CAAC,CAAC,EAAEN,SAAS,CAAC,EAAE;MACvD,MAAM,IAAIO,KAAK,CAAC,GAAGP,SAAS,uBAAuB,IAAI,CAACM,OAAO,CAAC,CAAC,CAACE,IAAI,GAAG,CAAC;IAC9E;IACA,MAAM,IAAID,KAAK,CAAC,GAAGP,SAAS,mCAAmC,IAAI,CAACM,OAAO,CAAC,CAAC,CAACE,IAAI,EAAE,CAAC;EACzF;EACAE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,gBAAgB,CAAC,cAAc,CAAC;EAChD;EACAM,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACN,gBAAgB,CAAC,aAAa,CAAC;EAC/C;EACAO,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,gBAAgB,CAAC,aAAa,CAAC;EAC/C;EACAQ,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,gBAAgB,CAAC,cAAc,CAAC;EAChD;EACAS,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACT,gBAAgB,CAAC,aAAa,CAAC;EAC/C;EACAU,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACV,gBAAgB,CAAC,WAAW,CAAC;EAC7C;EACAW,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACX,gBAAgB,CAAC,iBAAiB,CAAC;EACnD;EACAY,UAAUA,CAAA,EAAG;IACT,IAAI,CAACR,sCAAsC,CAAC,YAAY,CAAC;EAC7D;EACAS,MAAMA,CAAA,EAAG;IACL,IAAI,CAACT,sCAAsC,CAAC,QAAQ,CAAC;EACzD;EACAU,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,sCAAsC,CAAC,UAAU,CAAC;EAC3D;EACAP,GAAGA,CAAC,GAAGkB,YAAY,EAAE;IACjB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACf,OAAO,CAAC,CAAC,IAAInB,QAAQ,CAACK,IAAI,EAAE;MACjC,OAAO,MAAM;IACjB;IACA,OAAO,OAAO,GAAG,IAAI,CAACK,QAAQ,CAACW,IAAI;EACvC;EACAc,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI;EACf;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,SAAS,CAAC,IAAI,CAACrB,OAAO,CAAC,CAAC,CAAC;EACpC;EACAsB,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAYvC,IAAI,EAAE;MACvB6C,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK;IAC1B,CAAC,MACI,IAAI,CAACC,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAIL,KAAK,KAAK,IAAI,IAAI,IAAI,CAAChC,QAAQ,CAACW,IAAI,KAAK,MAAM,EAAE;QACjD,OAAO,IAAI;MACf;IACJ;IACA,IAAI,CAAC2B,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACtC,QAAQ,CAACW,IAAI,KAAK4B,cAAc,CAACvC,QAAQ,CAACW,IAAI;EAC9D;AACJ;AACAlB,IAAI,CAACW,6BAA6B,GAAG,IAAIoC,GAAG,CAAC,CACzC,CAAC,cAAc,EAAE,IAAIC,GAAG,CAAC,CAACnD,QAAQ,CAACoD,IAAI,CAAC,CAAC,CAAC,EAC1C,CAAC,aAAa,EAAE,IAAID,GAAG,CAAC,CAACnD,QAAQ,CAACqD,GAAG,EAAErD,QAAQ,CAACsD,KAAK,EAAEtD,QAAQ,CAACuD,OAAO,CAAC,CAAC,CAAC,EAC1E,CAAC,aAAa,EAAE,IAAIJ,GAAG,CAAC,CAACnD,QAAQ,CAACqD,GAAG,CAAC,CAAC,CAAC,EACxC,CAAC,cAAc,EAAE,IAAIF,GAAG,CAAC,CAACnD,QAAQ,CAACuD,OAAO,CAAC,CAAC,CAAC,EAC7C,CAAC,aAAa,EAAE,IAAIJ,GAAG,CAAC,CAACnD,QAAQ,CAACwD,MAAM,EAAExD,QAAQ,CAACyD,MAAM,CAAC,CAAC,CAAC,EAC5D,CAAC,WAAW,EAAE,IAAIN,GAAG,CAAC,CAACnD,QAAQ,CAAC0D,SAAS,CAAC,CAAC,CAAC,EAC5C,CAAC,gBAAgB,EAAE,IAAIP,GAAG,CAAC,CAACnD,QAAQ,CAAC0D,SAAS,CAAC,CAAC,CAAC,EACjD,CAAC,iBAAiB,EAAE,IAAIP,GAAG,CAAC,CAACnD,QAAQ,CAAC2D,IAAI,EAAE3D,QAAQ,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAC5D,CAAC,QAAQ,EAAE,IAAIT,GAAG,CAAC,CAACnD,QAAQ,CAAC6D,MAAM,CAAC,CAAC,CAAC,EACtC,CAAC,YAAY,EAAE,IAAIV,GAAG,CAAC,CAACnD,QAAQ,CAAC6D,MAAM,CAAC,CAAC,CAAC,EAC1C,CAAC,UAAU,EAAE,IAAIV,GAAG,CAAC,CAACnD,QAAQ,CAAC8D,IAAI,EAAE9D,QAAQ,CAAC+D,IAAI,EAAE/D,QAAQ,CAAC6D,MAAM,CAAC,CAAC,CAAC,CACzE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}