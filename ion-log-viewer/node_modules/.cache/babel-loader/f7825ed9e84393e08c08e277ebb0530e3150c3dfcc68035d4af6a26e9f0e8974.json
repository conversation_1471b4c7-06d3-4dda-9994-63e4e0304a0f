{"ast": null, "code": "const getWithKey = (obj, key) => obj[key];\nexport { getWithKey };", "map": {"version": 3, "names": ["getWithKey", "obj", "key"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/types/helpers.ts"], "sourcesContent": ["export const getWithKey = <T, K extends keyof T>(obj: T, key: K): T[K] => obj[key]\n"], "mappings": "AAAO,MAAMA,UAAA,GAAaA,CAAuBC,GAAA,EAAQC,GAAA,KAAiBD,GAAA,CAAIC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}