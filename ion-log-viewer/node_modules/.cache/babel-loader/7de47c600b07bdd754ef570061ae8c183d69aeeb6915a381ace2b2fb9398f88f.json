{"ast": null, "code": "import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */new Matrix4();\nconst obb = /* @__PURE__ */new OrientedBox();\nconst obb2 = /* @__PURE__ */new OrientedBox();\nconst temp1 = /* @__PURE__ */new Vector3();\nconst temp2 = /* @__PURE__ */new Vector3();\nconst temp3 = /* @__PURE__ */new Vector3();\nconst temp4 = /* @__PURE__ */new Vector3();\nfunction closestPointToGeometry(bvh, otherGeometry, geometryToBvh, target1 = {}, target2 = {}, minThreshold = 0, maxThreshold = Infinity) {\n  if (!otherGeometry.boundingBox) {\n    otherGeometry.computeBoundingBox();\n  }\n  obb.set(otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh);\n  obb.needsUpdate = true;\n  const geometry = bvh.geometry;\n  const pos = geometry.attributes.position;\n  const index = geometry.index;\n  const otherPos = otherGeometry.attributes.position;\n  const otherIndex = otherGeometry.index;\n  const triangle = ExtendedTrianglePool.getPrimitive();\n  const triangle2 = ExtendedTrianglePool.getPrimitive();\n  let tempTarget1 = temp1;\n  let tempTargetDest1 = temp2;\n  let tempTarget2 = null;\n  let tempTargetDest2 = null;\n  if (target2) {\n    tempTarget2 = temp3;\n    tempTargetDest2 = temp4;\n  }\n  let closestDistance = Infinity;\n  let closestDistanceTriIndex = null;\n  let closestDistanceOtherTriIndex = null;\n  tempMatrix.copy(geometryToBvh).invert();\n  obb2.matrix.copy(tempMatrix);\n  bvh.shapecast({\n    boundsTraverseOrder: box => {\n      return obb.distanceToBox(box);\n    },\n    intersectsBounds: (box, isLeaf, score) => {\n      if (score < closestDistance && score < maxThreshold) {\n        // if we know the triangles of this bounds will be intersected next then\n        // save the bounds to use during triangle checks.\n        if (isLeaf) {\n          obb2.min.copy(box.min);\n          obb2.max.copy(box.max);\n          obb2.needsUpdate = true;\n        }\n        return true;\n      }\n      return false;\n    },\n    intersectsRange: (offset, count) => {\n      if (otherGeometry.boundsTree) {\n        // if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n        // the closest bounds in the other geometry to check.\n        const otherBvh = otherGeometry.boundsTree;\n        return otherBvh.shapecast({\n          boundsTraverseOrder: box => {\n            return obb2.distanceToBox(box);\n          },\n          intersectsBounds: (box, isLeaf, score) => {\n            return score < closestDistance && score < maxThreshold;\n          },\n          intersectsRange: (otherOffset, otherCount) => {\n            for (let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2++) {\n              setTriangle(triangle2, 3 * i2, otherIndex, otherPos);\n              triangle2.a.applyMatrix4(geometryToBvh);\n              triangle2.b.applyMatrix4(geometryToBvh);\n              triangle2.c.applyMatrix4(geometryToBvh);\n              triangle2.needsUpdate = true;\n              for (let i = offset, l = offset + count; i < l; i++) {\n                setTriangle(triangle, 3 * i, index, pos);\n                triangle.needsUpdate = true;\n                const dist = triangle.distanceToTriangle(triangle2, tempTarget1, tempTarget2);\n                if (dist < closestDistance) {\n                  tempTargetDest1.copy(tempTarget1);\n                  if (tempTargetDest2) {\n                    tempTargetDest2.copy(tempTarget2);\n                  }\n                  closestDistance = dist;\n                  closestDistanceTriIndex = i;\n                  closestDistanceOtherTriIndex = i2;\n                }\n\n                // stop traversal if we find a point that's under the given threshold\n                if (dist < minThreshold) {\n                  return true;\n                }\n              }\n            }\n          }\n        });\n      } else {\n        // If no bounds tree then we'll just check every triangle.\n        const triCount = getTriCount(otherGeometry);\n        for (let i2 = 0, l2 = triCount; i2 < l2; i2++) {\n          setTriangle(triangle2, 3 * i2, otherIndex, otherPos);\n          triangle2.a.applyMatrix4(geometryToBvh);\n          triangle2.b.applyMatrix4(geometryToBvh);\n          triangle2.c.applyMatrix4(geometryToBvh);\n          triangle2.needsUpdate = true;\n          for (let i = offset, l = offset + count; i < l; i++) {\n            setTriangle(triangle, 3 * i, index, pos);\n            triangle.needsUpdate = true;\n            const dist = triangle.distanceToTriangle(triangle2, tempTarget1, tempTarget2);\n            if (dist < closestDistance) {\n              tempTargetDest1.copy(tempTarget1);\n              if (tempTargetDest2) {\n                tempTargetDest2.copy(tempTarget2);\n              }\n              closestDistance = dist;\n              closestDistanceTriIndex = i;\n              closestDistanceOtherTriIndex = i2;\n            }\n\n            // stop traversal if we find a point that's under the given threshold\n            if (dist < minThreshold) {\n              return true;\n            }\n          }\n        }\n      }\n    }\n  });\n  ExtendedTrianglePool.releasePrimitive(triangle);\n  ExtendedTrianglePool.releasePrimitive(triangle2);\n  if (closestDistance === Infinity) {\n    return null;\n  }\n  if (!target1.point) {\n    target1.point = tempTargetDest1.clone();\n  } else {\n    target1.point.copy(tempTargetDest1);\n  }\n  target1.distance = closestDistance, target1.faceIndex = closestDistanceTriIndex;\n  if (target2) {\n    if (!target2.point) target2.point = tempTargetDest2.clone();else target2.point.copy(tempTargetDest2);\n    target2.point.applyMatrix4(tempMatrix);\n    tempTargetDest1.applyMatrix4(tempMatrix);\n    target2.distance = tempTargetDest1.sub(target2.point).length();\n    target2.faceIndex = closestDistanceOtherTriIndex;\n  }\n  return target1;\n}\nexport { closestPointToGeometry };", "map": {"version": 3, "names": ["Matrix4", "Vector3", "OrientedBox", "set<PERSON>riangle", "getTriCount", "ExtendedTrianglePool", "tempMatrix", "obb", "obb2", "temp1", "temp2", "temp3", "temp4", "closestPointToGeometry", "bvh", "otherGeometry", "geometryToBvh", "target1", "target2", "min<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "Infinity", "boundingBox", "computeBoundingBox", "set", "min", "max", "needsUpdate", "geometry", "pos", "attributes", "position", "index", "otherPos", "otherIndex", "triangle", "getPrimitive", "triangle2", "tempTarget1", "tempTargetDest1", "tempTarget2", "tempTargetDest2", "closestDistance", "closestDistanceTriIndex", "closestDistanceOtherTriIndex", "copy", "invert", "matrix", "shapecast", "boundsTraverseOrder", "box", "distanceToBox", "intersectsBounds", "<PERSON><PERSON><PERSON><PERSON>", "score", "intersectsRange", "offset", "count", "boundsTree", "otherBvh", "otherOffset", "otherCount", "i2", "l2", "a", "applyMatrix4", "b", "c", "i", "l", "dist", "distanceToTriangle", "triCount", "releasePrimitive", "point", "clone", "distance", "faceIndex", "sub", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/cast/closestPointToGeometry.generated.js"], "sourcesContent": ["import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\n\nfunction closestPointToGeometry(\n\tbvh,\n\totherGeometry,\n\tgeometryToBvh,\n\ttarget1 = { },\n\ttarget2 = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\tif ( ! otherGeometry.boundingBox ) {\n\n\t\totherGeometry.computeBoundingBox();\n\n\t}\n\n\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\tobb.needsUpdate = true;\n\n\tconst geometry = bvh.geometry;\n\tconst pos = geometry.attributes.position;\n\tconst index = geometry.index;\n\tconst otherPos = otherGeometry.attributes.position;\n\tconst otherIndex = otherGeometry.index;\n\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\n\tlet tempTarget1 = temp1;\n\tlet tempTargetDest1 = temp2;\n\tlet tempTarget2 = null;\n\tlet tempTargetDest2 = null;\n\n\tif ( target2 ) {\n\n\t\ttempTarget2 = temp3;\n\t\ttempTargetDest2 = temp4;\n\n\t}\n\n\tlet closestDistance = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tlet closestDistanceOtherTriIndex = null;\n\ttempMatrix.copy( geometryToBvh ).invert();\n\tobb2.matrix.copy( tempMatrix );\n\tbvh.shapecast(\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\tconst otherBvh = otherGeometry.boundsTree;\n\t\t\t\t\treturn otherBvh.shapecast( {\n\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\tfor ( let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2 ++ ) {\n\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\n\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t},\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\tconst triCount = getTriCount( otherGeometry );\n\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tExtendedTrianglePool.releasePrimitive( triangle );\n\tExtendedTrianglePool.releasePrimitive( triangle2 );\n\n\tif ( closestDistance === Infinity ) {\n\n\t\treturn null;\n\n\t}\n\n\tif ( ! target1.point ) {\n\n\t\ttarget1.point = tempTargetDest1.clone();\n\n\t} else {\n\n\t\ttarget1.point.copy( tempTargetDest1 );\n\n\t}\n\n\ttarget1.distance = closestDistance,\n\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\tif ( target2 ) {\n\n\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\telse target2.point.copy( tempTargetDest2 );\n\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t}\n\n\treturn target1;\n\n}\n\nexport { closestPointToGeometry };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,oBAAoB,QAAQ,qCAAqC;;AAE1E;AACA;AACA;;AAEA,MAAMC,UAAU,GAAG,eAAgB,IAAIN,OAAO,CAAC,CAAC;AAChD,MAAMO,GAAG,GAAG,eAAgB,IAAIL,WAAW,CAAC,CAAC;AAC7C,MAAMM,IAAI,GAAG,eAAgB,IAAIN,WAAW,CAAC,CAAC;AAC9C,MAAMO,KAAK,GAAG,eAAgB,IAAIR,OAAO,CAAC,CAAC;AAC3C,MAAMS,KAAK,GAAG,eAAgB,IAAIT,OAAO,CAAC,CAAC;AAC3C,MAAMU,KAAK,GAAG,eAAgB,IAAIV,OAAO,CAAC,CAAC;AAC3C,MAAMW,KAAK,GAAG,eAAgB,IAAIX,OAAO,CAAC,CAAC;AAE3C,SAASY,sBAAsBA,CAC9BC,GAAG,EACHC,aAAa,EACbC,aAAa,EACbC,OAAO,GAAG,CAAE,CAAC,EACbC,OAAO,GAAG,CAAE,CAAC,EACbC,YAAY,GAAG,CAAC,EAChBC,YAAY,GAAGC,QAAQ,EACtB;EAED,IAAK,CAAEN,aAAa,CAACO,WAAW,EAAG;IAElCP,aAAa,CAACQ,kBAAkB,CAAC,CAAC;EAEnC;EAEAhB,GAAG,CAACiB,GAAG,CAAET,aAAa,CAACO,WAAW,CAACG,GAAG,EAAEV,aAAa,CAACO,WAAW,CAACI,GAAG,EAAEV,aAAc,CAAC;EACtFT,GAAG,CAACoB,WAAW,GAAG,IAAI;EAEtB,MAAMC,QAAQ,GAAGd,GAAG,CAACc,QAAQ;EAC7B,MAAMC,GAAG,GAAGD,QAAQ,CAACE,UAAU,CAACC,QAAQ;EACxC,MAAMC,KAAK,GAAGJ,QAAQ,CAACI,KAAK;EAC5B,MAAMC,QAAQ,GAAGlB,aAAa,CAACe,UAAU,CAACC,QAAQ;EAClD,MAAMG,UAAU,GAAGnB,aAAa,CAACiB,KAAK;EACtC,MAAMG,QAAQ,GAAG9B,oBAAoB,CAAC+B,YAAY,CAAC,CAAC;EACpD,MAAMC,SAAS,GAAGhC,oBAAoB,CAAC+B,YAAY,CAAC,CAAC;EAErD,IAAIE,WAAW,GAAG7B,KAAK;EACvB,IAAI8B,eAAe,GAAG7B,KAAK;EAC3B,IAAI8B,WAAW,GAAG,IAAI;EACtB,IAAIC,eAAe,GAAG,IAAI;EAE1B,IAAKvB,OAAO,EAAG;IAEdsB,WAAW,GAAG7B,KAAK;IACnB8B,eAAe,GAAG7B,KAAK;EAExB;EAEA,IAAI8B,eAAe,GAAGrB,QAAQ;EAC9B,IAAIsB,uBAAuB,GAAG,IAAI;EAClC,IAAIC,4BAA4B,GAAG,IAAI;EACvCtC,UAAU,CAACuC,IAAI,CAAE7B,aAAc,CAAC,CAAC8B,MAAM,CAAC,CAAC;EACzCtC,IAAI,CAACuC,MAAM,CAACF,IAAI,CAAEvC,UAAW,CAAC;EAC9BQ,GAAG,CAACkC,SAAS,CACZ;IAECC,mBAAmB,EAAEC,GAAG,IAAI;MAE3B,OAAO3C,GAAG,CAAC4C,aAAa,CAAED,GAAI,CAAC;IAEhC,CAAC;IAEDE,gBAAgB,EAAEA,CAAEF,GAAG,EAAEG,MAAM,EAAEC,KAAK,KAAM;MAE3C,IAAKA,KAAK,GAAGZ,eAAe,IAAIY,KAAK,GAAGlC,YAAY,EAAG;QAEtD;QACA;QACA,IAAKiC,MAAM,EAAG;UAEb7C,IAAI,CAACiB,GAAG,CAACoB,IAAI,CAAEK,GAAG,CAACzB,GAAI,CAAC;UACxBjB,IAAI,CAACkB,GAAG,CAACmB,IAAI,CAAEK,GAAG,CAACxB,GAAI,CAAC;UACxBlB,IAAI,CAACmB,WAAW,GAAG,IAAI;QAExB;QAEA,OAAO,IAAI;MAEZ;MAEA,OAAO,KAAK;IAEb,CAAC;IAED4B,eAAe,EAAEA,CAAEC,MAAM,EAAEC,KAAK,KAAM;MAErC,IAAK1C,aAAa,CAAC2C,UAAU,EAAG;QAE/B;QACA;QACA,MAAMC,QAAQ,GAAG5C,aAAa,CAAC2C,UAAU;QACzC,OAAOC,QAAQ,CAACX,SAAS,CAAE;UAC1BC,mBAAmB,EAAEC,GAAG,IAAI;YAE3B,OAAO1C,IAAI,CAAC2C,aAAa,CAAED,GAAI,CAAC;UAEjC,CAAC;UAEDE,gBAAgB,EAAEA,CAAEF,GAAG,EAAEG,MAAM,EAAEC,KAAK,KAAM;YAE3C,OAAOA,KAAK,GAAGZ,eAAe,IAAIY,KAAK,GAAGlC,YAAY;UAEvD,CAAC;UAEDmC,eAAe,EAAEA,CAAEK,WAAW,EAAEC,UAAU,KAAM;YAE/C,KAAM,IAAIC,EAAE,GAAGF,WAAW,EAAEG,EAAE,GAAGH,WAAW,GAAGC,UAAU,EAAEC,EAAE,GAAGC,EAAE,EAAED,EAAE,EAAG,EAAG;cAG3E3D,WAAW,CAAEkC,SAAS,EAAE,CAAC,GAAGyB,EAAE,EAAE5B,UAAU,EAAED,QAAS,CAAC;cAEtDI,SAAS,CAAC2B,CAAC,CAACC,YAAY,CAAEjD,aAAc,CAAC;cACzCqB,SAAS,CAAC6B,CAAC,CAACD,YAAY,CAAEjD,aAAc,CAAC;cACzCqB,SAAS,CAAC8B,CAAC,CAACF,YAAY,CAAEjD,aAAc,CAAC;cACzCqB,SAAS,CAACV,WAAW,GAAG,IAAI;cAE5B,KAAM,IAAIyC,CAAC,GAAGZ,MAAM,EAAEa,CAAC,GAAGb,MAAM,GAAGC,KAAK,EAAEW,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;gBAGvDjE,WAAW,CAAEgC,QAAQ,EAAE,CAAC,GAAGiC,CAAC,EAAEpC,KAAK,EAAEH,GAAI,CAAC;gBAE1CM,QAAQ,CAACR,WAAW,GAAG,IAAI;gBAE3B,MAAM2C,IAAI,GAAGnC,QAAQ,CAACoC,kBAAkB,CAAElC,SAAS,EAAEC,WAAW,EAAEE,WAAY,CAAC;gBAC/E,IAAK8B,IAAI,GAAG5B,eAAe,EAAG;kBAE7BH,eAAe,CAACM,IAAI,CAAEP,WAAY,CAAC;kBAEnC,IAAKG,eAAe,EAAG;oBAEtBA,eAAe,CAACI,IAAI,CAAEL,WAAY,CAAC;kBAEpC;kBAEAE,eAAe,GAAG4B,IAAI;kBACtB3B,uBAAuB,GAAGyB,CAAC;kBAC3BxB,4BAA4B,GAAGkB,EAAE;gBAElC;;gBAEA;gBACA,IAAKQ,IAAI,GAAGnD,YAAY,EAAG;kBAE1B,OAAO,IAAI;gBAEZ;cAED;YAED;UAED;QACD,CAAE,CAAC;MAEJ,CAAC,MAAM;QAEN;QACA,MAAMqD,QAAQ,GAAGpE,WAAW,CAAEW,aAAc,CAAC;QAC7C,KAAM,IAAI+C,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGS,QAAQ,EAAEV,EAAE,GAAGC,EAAE,EAAED,EAAE,EAAG,EAAG;UAEjD3D,WAAW,CAAEkC,SAAS,EAAE,CAAC,GAAGyB,EAAE,EAAE5B,UAAU,EAAED,QAAS,CAAC;UACtDI,SAAS,CAAC2B,CAAC,CAACC,YAAY,CAAEjD,aAAc,CAAC;UACzCqB,SAAS,CAAC6B,CAAC,CAACD,YAAY,CAAEjD,aAAc,CAAC;UACzCqB,SAAS,CAAC8B,CAAC,CAACF,YAAY,CAAEjD,aAAc,CAAC;UACzCqB,SAAS,CAACV,WAAW,GAAG,IAAI;UAE5B,KAAM,IAAIyC,CAAC,GAAGZ,MAAM,EAAEa,CAAC,GAAGb,MAAM,GAAGC,KAAK,EAAEW,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;YAGvDjE,WAAW,CAAEgC,QAAQ,EAAE,CAAC,GAAGiC,CAAC,EAAEpC,KAAK,EAAEH,GAAI,CAAC;YAE1CM,QAAQ,CAACR,WAAW,GAAG,IAAI;YAE3B,MAAM2C,IAAI,GAAGnC,QAAQ,CAACoC,kBAAkB,CAAElC,SAAS,EAAEC,WAAW,EAAEE,WAAY,CAAC;YAC/E,IAAK8B,IAAI,GAAG5B,eAAe,EAAG;cAE7BH,eAAe,CAACM,IAAI,CAAEP,WAAY,CAAC;cAEnC,IAAKG,eAAe,EAAG;gBAEtBA,eAAe,CAACI,IAAI,CAAEL,WAAY,CAAC;cAEpC;cAEAE,eAAe,GAAG4B,IAAI;cACtB3B,uBAAuB,GAAGyB,CAAC;cAC3BxB,4BAA4B,GAAGkB,EAAE;YAElC;;YAEA;YACA,IAAKQ,IAAI,GAAGnD,YAAY,EAAG;cAE1B,OAAO,IAAI;YAEZ;UAED;QAED;MAED;IAED;EAED,CAED,CAAC;EAEDd,oBAAoB,CAACoE,gBAAgB,CAAEtC,QAAS,CAAC;EACjD9B,oBAAoB,CAACoE,gBAAgB,CAAEpC,SAAU,CAAC;EAElD,IAAKK,eAAe,KAAKrB,QAAQ,EAAG;IAEnC,OAAO,IAAI;EAEZ;EAEA,IAAK,CAAEJ,OAAO,CAACyD,KAAK,EAAG;IAEtBzD,OAAO,CAACyD,KAAK,GAAGnC,eAAe,CAACoC,KAAK,CAAC,CAAC;EAExC,CAAC,MAAM;IAEN1D,OAAO,CAACyD,KAAK,CAAC7B,IAAI,CAAEN,eAAgB,CAAC;EAEtC;EAEAtB,OAAO,CAAC2D,QAAQ,GAAGlC,eAAe,EAClCzB,OAAO,CAAC4D,SAAS,GAAGlC,uBAAuB;EAE3C,IAAKzB,OAAO,EAAG;IAEd,IAAK,CAAEA,OAAO,CAACwD,KAAK,EAAGxD,OAAO,CAACwD,KAAK,GAAGjC,eAAe,CAACkC,KAAK,CAAC,CAAC,CAAC,KAC1DzD,OAAO,CAACwD,KAAK,CAAC7B,IAAI,CAAEJ,eAAgB,CAAC;IAC1CvB,OAAO,CAACwD,KAAK,CAACT,YAAY,CAAE3D,UAAW,CAAC;IACxCiC,eAAe,CAAC0B,YAAY,CAAE3D,UAAW,CAAC;IAC1CY,OAAO,CAAC0D,QAAQ,GAAGrC,eAAe,CAACuC,GAAG,CAAE5D,OAAO,CAACwD,KAAM,CAAC,CAACK,MAAM,CAAC,CAAC;IAChE7D,OAAO,CAAC2D,SAAS,GAAGjC,4BAA4B;EAEjD;EAEA,OAAO3B,OAAO;AAEf;AAEA,SAASJ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}