{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport function _sign(x) {\n  return x < 0 || x === 0 && 1 / x === -Infinity ? -1 : 1;\n}\nexport function _hasValue(v) {\n  return v !== undefined && v !== null;\n}\nexport function _assertDefined(value) {\n  if (value === undefined) {\n    throw new Error(\"Expected value to be defined\");\n  }\n}\nexport function isSafeInteger(value) {\n  return value >= Number.MIN_SAFE_INTEGER && value <= Number.MAX_SAFE_INTEGER;\n}", "map": {"version": 3, "names": ["_sign", "x", "Infinity", "_hasValue", "v", "undefined", "_assertDefined", "value", "Error", "isSafeInteger", "Number", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/util.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport function _sign(x) {\n    return x < 0 || (x === 0 && 1 / x === -Infinity) ? -1 : 1;\n}\nexport function _hasValue(v) {\n    return v !== undefined && v !== null;\n}\nexport function _assertDefined(value) {\n    if (value === undefined) {\n        throw new Error(\"Expected value to be defined\");\n    }\n}\nexport function isSafeInteger(value) {\n    return value >= Number.MIN_SAFE_INTEGER && value <= Number.MAX_SAFE_INTEGER;\n}\n//# sourceMappingURL=util.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,CAAC,EAAE;EACrB,OAAOA,CAAC,GAAG,CAAC,IAAKA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAACC,QAAS,GAAG,CAAC,CAAC,GAAG,CAAC;AAC7D;AACA,OAAO,SAASC,SAASA,CAACC,CAAC,EAAE;EACzB,OAAOA,CAAC,KAAKC,SAAS,IAAID,CAAC,KAAK,IAAI;AACxC;AACA,OAAO,SAASE,cAAcA,CAACC,KAAK,EAAE;EAClC,IAAIA,KAAK,KAAKF,SAAS,EAAE;IACrB,MAAM,IAAIG,KAAK,CAAC,8BAA8B,CAAC;EACnD;AACJ;AACA,OAAO,SAASC,aAAaA,CAACF,KAAK,EAAE;EACjC,OAAOA,KAAK,IAAIG,MAAM,CAACC,gBAAgB,IAAIJ,KAAK,IAAIG,MAAM,CAACE,gBAAgB;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}