{"ast": null, "code": "import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry, Mesh, Matrix4 } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\nconst boundingBox = /* @__PURE__ */new Box3();\nconst matrix = /* @__PURE__ */new Matrix4();\nclass MeshBVHRootHelper extends Object3D {\n  get isMesh() {\n    return !this.displayEdges;\n  }\n  get isLineSegments() {\n    return this.displayEdges;\n  }\n  get isLine() {\n    return this.displayEdges;\n  }\n  getVertexPosition(...args) {\n    // implement this function so it works with Box3.setFromObject\n    return Mesh.prototype.getVertexPosition.call(this, ...args);\n  }\n  constructor(bvh, material, depth = 10, group = 0) {\n    super();\n    this.material = material;\n    this.geometry = new BufferGeometry();\n    this.name = 'MeshBVHRootHelper';\n    this.depth = depth;\n    this.displayParents = false;\n    this.bvh = bvh;\n    this.displayEdges = true;\n    this._group = group;\n  }\n  raycast() {}\n  update() {\n    const geometry = this.geometry;\n    const boundsTree = this.bvh;\n    const group = this._group;\n    geometry.dispose();\n    this.visible = false;\n    if (boundsTree) {\n      // count the number of bounds required\n      const targetDepth = this.depth - 1;\n      const displayParents = this.displayParents;\n      let boundsCount = 0;\n      boundsTree.traverse((depth, isLeaf) => {\n        if (depth >= targetDepth || isLeaf) {\n          boundsCount++;\n          return true;\n        } else if (displayParents) {\n          boundsCount++;\n        }\n      }, group);\n\n      // fill in the position buffer with the bounds corners\n      let posIndex = 0;\n      const positionArray = new Float32Array(8 * 3 * boundsCount);\n      boundsTree.traverse((depth, isLeaf, boundingData) => {\n        const terminate = depth >= targetDepth || isLeaf;\n        if (terminate || displayParents) {\n          arrayToBox(0, boundingData, boundingBox);\n          const {\n            min,\n            max\n          } = boundingBox;\n          for (let x = -1; x <= 1; x += 2) {\n            const xVal = x < 0 ? min.x : max.x;\n            for (let y = -1; y <= 1; y += 2) {\n              const yVal = y < 0 ? min.y : max.y;\n              for (let z = -1; z <= 1; z += 2) {\n                const zVal = z < 0 ? min.z : max.z;\n                positionArray[posIndex + 0] = xVal;\n                positionArray[posIndex + 1] = yVal;\n                positionArray[posIndex + 2] = zVal;\n                posIndex += 3;\n              }\n            }\n          }\n          return terminate;\n        }\n      }, group);\n      let indexArray;\n      let indices;\n      if (this.displayEdges) {\n        // fill in the index buffer to point to the corner points\n        indices = new Uint8Array([\n        // x axis\n        0, 4, 1, 5, 2, 6, 3, 7,\n        // y axis\n        0, 2, 1, 3, 4, 6, 5, 7,\n        // z axis\n        0, 1, 2, 3, 4, 5, 6, 7]);\n      } else {\n        indices = new Uint8Array([\n        // X-, X+\n        0, 1, 2, 2, 1, 3, 4, 6, 5, 6, 7, 5,\n        // Y-, Y+\n        1, 4, 5, 0, 4, 1, 2, 3, 6, 3, 7, 6,\n        // Z-, Z+\n        0, 2, 4, 2, 6, 4, 1, 5, 3, 3, 5, 7]);\n      }\n      if (positionArray.length > 65535) {\n        indexArray = new Uint32Array(indices.length * boundsCount);\n      } else {\n        indexArray = new Uint16Array(indices.length * boundsCount);\n      }\n      const indexLength = indices.length;\n      for (let i = 0; i < boundsCount; i++) {\n        const posOffset = i * 8;\n        const indexOffset = i * indexLength;\n        for (let j = 0; j < indexLength; j++) {\n          indexArray[indexOffset + j] = posOffset + indices[j];\n        }\n      }\n\n      // update the geometry\n      geometry.setIndex(new BufferAttribute(indexArray, 1, false));\n      geometry.setAttribute('position', new BufferAttribute(positionArray, 3, false));\n      this.visible = true;\n    }\n  }\n}\nclass MeshBVHHelper extends Group {\n  get color() {\n    return this.edgeMaterial.color;\n  }\n  get opacity() {\n    return this.edgeMaterial.opacity;\n  }\n  set opacity(v) {\n    this.edgeMaterial.opacity = v;\n    this.meshMaterial.opacity = v;\n  }\n  constructor(mesh = null, bvh = null, depth = 10) {\n    // handle bvh, depth signature\n    if (mesh instanceof MeshBVH) {\n      depth = bvh || 10;\n      bvh = mesh;\n      mesh = null;\n    }\n\n    // handle mesh, depth signature\n    if (typeof bvh === 'number') {\n      depth = bvh;\n      bvh = null;\n    }\n    super();\n    this.name = 'MeshBVHHelper';\n    this.depth = depth;\n    this.mesh = mesh;\n    this.bvh = bvh;\n    this.displayParents = false;\n    this.displayEdges = true;\n    this.objectIndex = 0;\n    this._roots = [];\n    const edgeMaterial = new LineBasicMaterial({\n      color: 0x00FF88,\n      transparent: true,\n      opacity: 0.3,\n      depthWrite: false\n    });\n    const meshMaterial = new MeshBasicMaterial({\n      color: 0x00FF88,\n      transparent: true,\n      opacity: 0.3,\n      depthWrite: false\n    });\n    meshMaterial.color = edgeMaterial.color;\n    this.edgeMaterial = edgeMaterial;\n    this.meshMaterial = meshMaterial;\n    this.update();\n  }\n  update() {\n    const mesh = this.mesh;\n    let bvh = this.bvh || mesh.geometry.boundsTree || null;\n    if (mesh.isBatchedMesh && mesh.boundsTrees && !bvh) {\n      // get the bvh from a batchedMesh if not provided\n      // TODO: we should have an official way to get the geometry index cleanly\n      const drawInfo = mesh._drawInfo[this.objectIndex];\n      if (drawInfo) {\n        bvh = mesh.boundsTrees[drawInfo.geometryIndex] || bvh;\n      }\n    }\n    const totalRoots = bvh ? bvh._roots.length : 0;\n    while (this._roots.length > totalRoots) {\n      const root = this._roots.pop();\n      root.geometry.dispose();\n      this.remove(root);\n    }\n    for (let i = 0; i < totalRoots; i++) {\n      const {\n        depth,\n        edgeMaterial,\n        meshMaterial,\n        displayParents,\n        displayEdges\n      } = this;\n      if (i >= this._roots.length) {\n        const root = new MeshBVHRootHelper(bvh, edgeMaterial, depth, i);\n        this.add(root);\n        this._roots.push(root);\n      }\n      const root = this._roots[i];\n      root.bvh = bvh;\n      root.depth = depth;\n      root.displayParents = displayParents;\n      root.displayEdges = displayEdges;\n      root.material = displayEdges ? edgeMaterial : meshMaterial;\n      root.update();\n    }\n  }\n  updateMatrixWorld(...args) {\n    const mesh = this.mesh;\n    const parent = this.parent;\n    if (mesh !== null) {\n      mesh.updateWorldMatrix(true, false);\n      if (parent) {\n        this.matrix.copy(parent.matrixWorld).invert().multiply(mesh.matrixWorld);\n      } else {\n        this.matrix.copy(mesh.matrixWorld);\n      }\n\n      // handle batched and instanced mesh bvhs\n      if (mesh.isInstancedMesh || mesh.isBatchedMesh) {\n        mesh.getMatrixAt(this.objectIndex, matrix);\n        this.matrix.multiply(matrix);\n      }\n      this.matrix.decompose(this.position, this.quaternion, this.scale);\n    }\n    super.updateMatrixWorld(...args);\n  }\n  copy(source) {\n    this.depth = source.depth;\n    this.mesh = source.mesh;\n    this.bvh = source.bvh;\n    this.opacity = source.opacity;\n    this.color.copy(source.color);\n  }\n  clone() {\n    return new MeshBVHHelper(this.mesh, this.bvh, this.depth);\n  }\n  dispose() {\n    this.edgeMaterial.dispose();\n    this.meshMaterial.dispose();\n    const children = this.children;\n    for (let i = 0, l = children.length; i < l; i++) {\n      children[i].geometry.dispose();\n    }\n  }\n}\nexport class MeshBVHVisualizer extends MeshBVHHelper {\n  constructor(...args) {\n    super(...args);\n    console.warn('MeshBVHVisualizer: MeshBVHVisualizer has been deprecated. Use MeshBVHHelper, instead.');\n  }\n}\nexport { MeshBVHHelper };", "map": {"version": 3, "names": ["LineBasicMaterial", "BufferAttribute", "Box3", "Group", "MeshBasicMaterial", "Object3D", "BufferGeometry", "<PERSON><PERSON>", "Matrix4", "arrayToBox", "MeshBVH", "boundingBox", "matrix", "MeshBVHRootHelper", "<PERSON><PERSON><PERSON>", "displayEdges", "isLineSegments", "isLine", "getVertexPosition", "args", "prototype", "call", "constructor", "bvh", "material", "depth", "group", "geometry", "name", "displayParents", "_group", "raycast", "update", "boundsTree", "dispose", "visible", "targetDepth", "boundsCount", "traverse", "<PERSON><PERSON><PERSON><PERSON>", "posIndex", "positionArray", "Float32Array", "boundingData", "terminate", "min", "max", "x", "xVal", "y", "yVal", "z", "zVal", "indexArray", "indices", "Uint8Array", "length", "Uint32Array", "Uint16Array", "indexLength", "i", "posOffset", "indexOffset", "j", "setIndex", "setAttribute", "MeshBVHHelper", "color", "edgeMaterial", "opacity", "v", "meshMaterial", "mesh", "objectIndex", "_roots", "transparent", "depthWrite", "isBatchedMesh", "boundsTrees", "drawInfo", "_drawInfo", "geometryIndex", "totalRoots", "root", "pop", "remove", "add", "push", "updateMatrixWorld", "parent", "updateWorldMatrix", "copy", "matrixWorld", "invert", "multiply", "isInstancedMesh", "getMatrixAt", "decompose", "position", "quaternion", "scale", "source", "clone", "children", "l", "MeshBVHVisualizer", "console", "warn"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/objects/MeshBVHHelper.js"], "sourcesContent": ["import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry, Mesh, Matrix4 } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst matrix = /* @__PURE__ */ new Matrix4();\n\nclass MeshBVHRootHelper extends Object3D {\n\n\tget isMesh() {\n\n\t\treturn ! this.displayEdges;\n\n\t}\n\n\tget isLineSegments() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tget isLine() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tgetVertexPosition( ...args ) {\n\n\t\t// implement this function so it works with Box3.setFromObject\n\t\treturn Mesh.prototype.getVertexPosition.call( this, ...args );\n\n\t}\n\n\tconstructor( bvh, material, depth = 10, group = 0 ) {\n\n\t\tsuper();\n\n\t\tthis.material = material;\n\t\tthis.geometry = new BufferGeometry();\n\t\tthis.name = 'MeshBVHRootHelper';\n\t\tthis.depth = depth;\n\t\tthis.displayParents = false;\n\t\tthis.bvh = bvh;\n\t\tthis.displayEdges = true;\n\t\tthis._group = group;\n\n\t}\n\n\traycast() {}\n\n\tupdate() {\n\n\t\tconst geometry = this.geometry;\n\t\tconst boundsTree = this.bvh;\n\t\tconst group = this._group;\n\t\tgeometry.dispose();\n\t\tthis.visible = false;\n\t\tif ( boundsTree ) {\n\n\t\t\t// count the number of bounds required\n\t\t\tconst targetDepth = this.depth - 1;\n\t\t\tconst displayParents = this.displayParents;\n\t\t\tlet boundsCount = 0;\n\t\t\tboundsTree.traverse( ( depth, isLeaf ) => {\n\n\t\t\t\tif ( depth >= targetDepth || isLeaf ) {\n\n\t\t\t\t\tboundsCount ++;\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else if ( displayParents ) {\n\n\t\t\t\t\tboundsCount ++;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\t// fill in the position buffer with the bounds corners\n\t\t\tlet posIndex = 0;\n\t\t\tconst positionArray = new Float32Array( 8 * 3 * boundsCount );\n\t\t\tboundsTree.traverse( ( depth, isLeaf, boundingData ) => {\n\n\t\t\t\tconst terminate = depth >= targetDepth || isLeaf;\n\t\t\t\tif ( terminate || displayParents ) {\n\n\t\t\t\t\tarrayToBox( 0, boundingData, boundingBox );\n\n\t\t\t\t\tconst { min, max } = boundingBox;\n\t\t\t\t\tfor ( let x = - 1; x <= 1; x += 2 ) {\n\n\t\t\t\t\t\tconst xVal = x < 0 ? min.x : max.x;\n\t\t\t\t\t\tfor ( let y = - 1; y <= 1; y += 2 ) {\n\n\t\t\t\t\t\t\tconst yVal = y < 0 ? min.y : max.y;\n\t\t\t\t\t\t\tfor ( let z = - 1; z <= 1; z += 2 ) {\n\n\t\t\t\t\t\t\t\tconst zVal = z < 0 ? min.z : max.z;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 0 ] = xVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 1 ] = yVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 2 ] = zVal;\n\n\t\t\t\t\t\t\t\tposIndex += 3;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn terminate;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\tlet indexArray;\n\t\t\tlet indices;\n\t\t\tif ( this.displayEdges ) {\n\n\t\t\t\t// fill in the index buffer to point to the corner points\n\t\t\t\tindices = new Uint8Array( [\n\t\t\t\t\t// x axis\n\t\t\t\t\t0, 4,\n\t\t\t\t\t1, 5,\n\t\t\t\t\t2, 6,\n\t\t\t\t\t3, 7,\n\n\t\t\t\t\t// y axis\n\t\t\t\t\t0, 2,\n\t\t\t\t\t1, 3,\n\t\t\t\t\t4, 6,\n\t\t\t\t\t5, 7,\n\n\t\t\t\t\t// z axis\n\t\t\t\t\t0, 1,\n\t\t\t\t\t2, 3,\n\t\t\t\t\t4, 5,\n\t\t\t\t\t6, 7,\n\t\t\t\t] );\n\n\t\t\t} else {\n\n\t\t\t\tindices = new Uint8Array( [\n\n\t\t\t\t\t// X-, X+\n\t\t\t\t\t0, 1, 2,\n\t\t\t\t\t2, 1, 3,\n\n\t\t\t\t\t4, 6, 5,\n\t\t\t\t\t6, 7, 5,\n\n\t\t\t\t\t// Y-, Y+\n\t\t\t\t\t1, 4, 5,\n\t\t\t\t\t0, 4, 1,\n\n\t\t\t\t\t2, 3, 6,\n\t\t\t\t\t3, 7, 6,\n\n\t\t\t\t\t// Z-, Z+\n\t\t\t\t\t0, 2, 4,\n\t\t\t\t\t2, 6, 4,\n\n\t\t\t\t\t1, 5, 3,\n\t\t\t\t\t3, 5, 7,\n\n\t\t\t\t] );\n\n\t\t\t}\n\n\t\t\tif ( positionArray.length > 65535 ) {\n\n\t\t\t\tindexArray = new Uint32Array( indices.length * boundsCount );\n\n\t\t\t} else {\n\n\t\t\t\tindexArray = new Uint16Array( indices.length * boundsCount );\n\n\t\t\t}\n\n\t\t\tconst indexLength = indices.length;\n\t\t\tfor ( let i = 0; i < boundsCount; i ++ ) {\n\n\t\t\t\tconst posOffset = i * 8;\n\t\t\t\tconst indexOffset = i * indexLength;\n\t\t\t\tfor ( let j = 0; j < indexLength; j ++ ) {\n\n\t\t\t\t\tindexArray[ indexOffset + j ] = posOffset + indices[ j ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the geometry\n\t\t\tgeometry.setIndex(\n\t\t\t\tnew BufferAttribute( indexArray, 1, false ),\n\t\t\t);\n\t\t\tgeometry.setAttribute(\n\t\t\t\t'position',\n\t\t\t\tnew BufferAttribute( positionArray, 3, false ),\n\t\t\t);\n\t\t\tthis.visible = true;\n\n\t\t}\n\n\t}\n\n}\n\nclass MeshBVHHelper extends Group {\n\n\tget color() {\n\n\t\treturn this.edgeMaterial.color;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.edgeMaterial.opacity;\n\n\t}\n\n\tset opacity( v ) {\n\n\t\tthis.edgeMaterial.opacity = v;\n\t\tthis.meshMaterial.opacity = v;\n\n\t}\n\n\tconstructor( mesh = null, bvh = null, depth = 10 ) {\n\n\t\t// handle bvh, depth signature\n\t\tif ( mesh instanceof MeshBVH ) {\n\n\t\t\tdepth = bvh || 10;\n\t\t\tbvh = mesh;\n\t\t\tmesh = null;\n\n\t\t}\n\n\t\t// handle mesh, depth signature\n\t\tif ( typeof bvh === 'number' ) {\n\n\t\t\tdepth = bvh;\n\t\t\tbvh = null;\n\n\t\t}\n\n\t\tsuper();\n\n\t\tthis.name = 'MeshBVHHelper';\n\t\tthis.depth = depth;\n\t\tthis.mesh = mesh;\n\t\tthis.bvh = bvh;\n\t\tthis.displayParents = false;\n\t\tthis.displayEdges = true;\n\t\tthis.objectIndex = 0;\n\t\tthis._roots = [];\n\n\t\tconst edgeMaterial = new LineBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tconst meshMaterial = new MeshBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tmeshMaterial.color = edgeMaterial.color;\n\n\t\tthis.edgeMaterial = edgeMaterial;\n\t\tthis.meshMaterial = meshMaterial;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tlet bvh = this.bvh || mesh.geometry.boundsTree || null;\n\t\tif ( mesh.isBatchedMesh && mesh.boundsTrees && ! bvh ) {\n\n\t\t\t// get the bvh from a batchedMesh if not provided\n\t\t\t// TODO: we should have an official way to get the geometry index cleanly\n\t\t\tconst drawInfo = mesh._drawInfo[ this.objectIndex ];\n\t\t\tif ( drawInfo ) {\n\n\t\t\t\tbvh = mesh.boundsTrees[ drawInfo.geometryIndex ] || bvh;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst totalRoots = bvh ? bvh._roots.length : 0;\n\t\twhile ( this._roots.length > totalRoots ) {\n\n\t\t\tconst root = this._roots.pop();\n\t\t\troot.geometry.dispose();\n\t\t\tthis.remove( root );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < totalRoots; i ++ ) {\n\n\t\t\tconst { depth, edgeMaterial, meshMaterial, displayParents, displayEdges } = this;\n\n\t\t\tif ( i >= this._roots.length ) {\n\n\t\t\t\tconst root = new MeshBVHRootHelper( bvh, edgeMaterial, depth, i );\n\t\t\t\tthis.add( root );\n\t\t\t\tthis._roots.push( root );\n\n\t\t\t}\n\n\t\t\tconst root = this._roots[ i ];\n\t\t\troot.bvh = bvh;\n\t\t\troot.depth = depth;\n\t\t\troot.displayParents = displayParents;\n\t\t\troot.displayEdges = displayEdges;\n\t\t\troot.material = displayEdges ? edgeMaterial : meshMaterial;\n\t\t\troot.update();\n\n\t\t}\n\n\t}\n\n\tupdateMatrixWorld( ...args ) {\n\n\t\tconst mesh = this.mesh;\n\t\tconst parent = this.parent;\n\n\t\tif ( mesh !== null ) {\n\n\t\t\tmesh.updateWorldMatrix( true, false );\n\n\t\t\tif ( parent ) {\n\n\t\t\t\tthis.matrix\n\t\t\t\t\t.copy( parent.matrixWorld )\n\t\t\t\t\t.invert()\n\t\t\t\t\t.multiply( mesh.matrixWorld );\n\n\t\t\t} else {\n\n\t\t\t\tthis.matrix\n\t\t\t\t\t.copy( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\t// handle batched and instanced mesh bvhs\n\t\t\tif ( mesh.isInstancedMesh || mesh.isBatchedMesh ) {\n\n\t\t\t\tmesh.getMatrixAt( this.objectIndex, matrix );\n\t\t\t\tthis.matrix.multiply( matrix );\n\n\t\t\t}\n\n\t\t\tthis.matrix.decompose(\n\t\t\t\tthis.position,\n\t\t\t\tthis.quaternion,\n\t\t\t\tthis.scale,\n\t\t\t);\n\n\t\t}\n\n\t\tsuper.updateMatrixWorld( ...args );\n\n\t}\n\n\tcopy( source ) {\n\n\t\tthis.depth = source.depth;\n\t\tthis.mesh = source.mesh;\n\t\tthis.bvh = source.bvh;\n\t\tthis.opacity = source.opacity;\n\t\tthis.color.copy( source.color );\n\n\t}\n\n\tclone() {\n\n\t\treturn new MeshBVHHelper( this.mesh, this.bvh, this.depth );\n\n\t}\n\n\tdispose() {\n\n\t\tthis.edgeMaterial.dispose();\n\t\tthis.meshMaterial.dispose();\n\n\t\tconst children = this.children;\n\t\tfor ( let i = 0, l = children.length; i < l; i ++ ) {\n\n\t\t\tchildren[ i ].geometry.dispose();\n\n\t\t}\n\n\t}\n\n}\n\nexport class MeshBVHVisualizer extends MeshBVHHelper {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tconsole.warn( 'MeshBVHVisualizer: MeshBVHVisualizer has been deprecated. Use MeshBVHHelper, instead.' );\n\n\t}\n\n}\n\nexport { MeshBVHHelper };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,eAAe,EAAEC,IAAI,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,IAAI,EAAEC,OAAO,QAAQ,OAAO;AACnI,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,OAAO,QAAQ,oBAAoB;AAE5C,MAAMC,WAAW,GAAG,eAAgB,IAAIT,IAAI,CAAC,CAAC;AAC9C,MAAMU,MAAM,GAAG,eAAgB,IAAIJ,OAAO,CAAC,CAAC;AAE5C,MAAMK,iBAAiB,SAASR,QAAQ,CAAC;EAExC,IAAIS,MAAMA,CAAA,EAAG;IAEZ,OAAO,CAAE,IAAI,CAACC,YAAY;EAE3B;EAEA,IAAIC,cAAcA,CAAA,EAAG;IAEpB,OAAO,IAAI,CAACD,YAAY;EAEzB;EAEA,IAAIE,MAAMA,CAAA,EAAG;IAEZ,OAAO,IAAI,CAACF,YAAY;EAEzB;EAEAG,iBAAiBA,CAAE,GAAGC,IAAI,EAAG;IAE5B;IACA,OAAOZ,IAAI,CAACa,SAAS,CAACF,iBAAiB,CAACG,IAAI,CAAE,IAAI,EAAE,GAAGF,IAAK,CAAC;EAE9D;EAEAG,WAAWA,CAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,GAAG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAG;IAEnD,KAAK,CAAC,CAAC;IAEP,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACG,QAAQ,GAAG,IAAIrB,cAAc,CAAC,CAAC;IACpC,IAAI,CAACsB,IAAI,GAAG,mBAAmB;IAC/B,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACR,YAAY,GAAG,IAAI;IACxB,IAAI,CAACe,MAAM,GAAGJ,KAAK;EAEpB;EAEAK,OAAOA,CAAA,EAAG,CAAC;EAEXC,MAAMA,CAAA,EAAG;IAER,MAAML,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMM,UAAU,GAAG,IAAI,CAACV,GAAG;IAC3B,MAAMG,KAAK,GAAG,IAAI,CAACI,MAAM;IACzBH,QAAQ,CAACO,OAAO,CAAC,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAKF,UAAU,EAAG;MAEjB;MACA,MAAMG,WAAW,GAAG,IAAI,CAACX,KAAK,GAAG,CAAC;MAClC,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,IAAIQ,WAAW,GAAG,CAAC;MACnBJ,UAAU,CAACK,QAAQ,CAAE,CAAEb,KAAK,EAAEc,MAAM,KAAM;QAEzC,IAAKd,KAAK,IAAIW,WAAW,IAAIG,MAAM,EAAG;UAErCF,WAAW,EAAG;UACd,OAAO,IAAI;QAEZ,CAAC,MAAM,IAAKR,cAAc,EAAG;UAE5BQ,WAAW,EAAG;QAEf;MAED,CAAC,EAAEX,KAAM,CAAC;;MAEV;MACA,IAAIc,QAAQ,GAAG,CAAC;MAChB,MAAMC,aAAa,GAAG,IAAIC,YAAY,CAAE,CAAC,GAAG,CAAC,GAAGL,WAAY,CAAC;MAC7DJ,UAAU,CAACK,QAAQ,CAAE,CAAEb,KAAK,EAAEc,MAAM,EAAEI,YAAY,KAAM;QAEvD,MAAMC,SAAS,GAAGnB,KAAK,IAAIW,WAAW,IAAIG,MAAM;QAChD,IAAKK,SAAS,IAAIf,cAAc,EAAG;UAElCpB,UAAU,CAAE,CAAC,EAAEkC,YAAY,EAAEhC,WAAY,CAAC;UAE1C,MAAM;YAAEkC,GAAG;YAAEC;UAAI,CAAC,GAAGnC,WAAW;UAChC,KAAM,IAAIoC,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;YAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGD,GAAG,CAACC,CAAC;YAClC,KAAM,IAAIE,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;cAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGJ,GAAG,CAACI,CAAC,GAAGH,GAAG,CAACG,CAAC;cAClC,KAAM,IAAIE,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;gBAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAGL,GAAG,CAACK,CAAC;gBAClCV,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGQ,IAAI;gBACpCP,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGU,IAAI;gBACpCT,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGY,IAAI;gBAEpCZ,QAAQ,IAAI,CAAC;cAEd;YAED;UAED;UAEA,OAAOI,SAAS;QAEjB;MAED,CAAC,EAAElB,KAAM,CAAC;MAEV,IAAI2B,UAAU;MACd,IAAIC,OAAO;MACX,IAAK,IAAI,CAACvC,YAAY,EAAG;QAExB;QACAuC,OAAO,GAAG,IAAIC,UAAU,CAAE;QACzB;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC;QAEJ;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC;QAEJ;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,CACH,CAAC;MAEJ,CAAC,MAAM;QAEND,OAAO,GAAG,IAAIC,UAAU,CAAE;QAEzB;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC;QAEP;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC;QAEP;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,CAEN,CAAC;MAEJ;MAEA,IAAKd,aAAa,CAACe,MAAM,GAAG,KAAK,EAAG;QAEnCH,UAAU,GAAG,IAAII,WAAW,CAAEH,OAAO,CAACE,MAAM,GAAGnB,WAAY,CAAC;MAE7D,CAAC,MAAM;QAENgB,UAAU,GAAG,IAAIK,WAAW,CAAEJ,OAAO,CAACE,MAAM,GAAGnB,WAAY,CAAC;MAE7D;MAEA,MAAMsB,WAAW,GAAGL,OAAO,CAACE,MAAM;MAClC,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,WAAW,EAAEuB,CAAC,EAAG,EAAG;QAExC,MAAMC,SAAS,GAAGD,CAAC,GAAG,CAAC;QACvB,MAAME,WAAW,GAAGF,CAAC,GAAGD,WAAW;QACnC,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAG,EAAG;UAExCV,UAAU,CAAES,WAAW,GAAGC,CAAC,CAAE,GAAGF,SAAS,GAAGP,OAAO,CAAES,CAAC,CAAE;QAEzD;MAED;;MAEA;MACApC,QAAQ,CAACqC,QAAQ,CAChB,IAAI/D,eAAe,CAAEoD,UAAU,EAAE,CAAC,EAAE,KAAM,CAC3C,CAAC;MACD1B,QAAQ,CAACsC,YAAY,CACpB,UAAU,EACV,IAAIhE,eAAe,CAAEwC,aAAa,EAAE,CAAC,EAAE,KAAM,CAC9C,CAAC;MACD,IAAI,CAACN,OAAO,GAAG,IAAI;IAEpB;EAED;AAED;AAEA,MAAM+B,aAAa,SAAS/D,KAAK,CAAC;EAEjC,IAAIgE,KAAKA,CAAA,EAAG;IAEX,OAAO,IAAI,CAACC,YAAY,CAACD,KAAK;EAE/B;EAEA,IAAIE,OAAOA,CAAA,EAAG;IAEb,OAAO,IAAI,CAACD,YAAY,CAACC,OAAO;EAEjC;EAEA,IAAIA,OAAOA,CAAEC,CAAC,EAAG;IAEhB,IAAI,CAACF,YAAY,CAACC,OAAO,GAAGC,CAAC;IAC7B,IAAI,CAACC,YAAY,CAACF,OAAO,GAAGC,CAAC;EAE9B;EAEAhD,WAAWA,CAAEkD,IAAI,GAAG,IAAI,EAAEjD,GAAG,GAAG,IAAI,EAAEE,KAAK,GAAG,EAAE,EAAG;IAElD;IACA,IAAK+C,IAAI,YAAY9D,OAAO,EAAG;MAE9Be,KAAK,GAAGF,GAAG,IAAI,EAAE;MACjBA,GAAG,GAAGiD,IAAI;MACVA,IAAI,GAAG,IAAI;IAEZ;;IAEA;IACA,IAAK,OAAOjD,GAAG,KAAK,QAAQ,EAAG;MAE9BE,KAAK,GAAGF,GAAG;MACXA,GAAG,GAAG,IAAI;IAEX;IAEA,KAAK,CAAC,CAAC;IAEP,IAAI,CAACK,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACjD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC0D,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,MAAM,GAAG,EAAE;IAEhB,MAAMN,YAAY,GAAG,IAAIpE,iBAAiB,CAAE;MAC3CmE,KAAK,EAAE,QAAQ;MACfQ,WAAW,EAAE,IAAI;MACjBN,OAAO,EAAE,GAAG;MACZO,UAAU,EAAE;IACb,CAAE,CAAC;IAEH,MAAML,YAAY,GAAG,IAAInE,iBAAiB,CAAE;MAC3C+D,KAAK,EAAE,QAAQ;MACfQ,WAAW,EAAE,IAAI;MACjBN,OAAO,EAAE,GAAG;MACZO,UAAU,EAAE;IACb,CAAE,CAAC;IAEHL,YAAY,CAACJ,KAAK,GAAGC,YAAY,CAACD,KAAK;IAEvC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACvC,MAAM,CAAC,CAAC;EAEd;EAEAA,MAAMA,CAAA,EAAG;IAER,MAAMwC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIjD,GAAG,GAAG,IAAI,CAACA,GAAG,IAAIiD,IAAI,CAAC7C,QAAQ,CAACM,UAAU,IAAI,IAAI;IACtD,IAAKuC,IAAI,CAACK,aAAa,IAAIL,IAAI,CAACM,WAAW,IAAI,CAAEvD,GAAG,EAAG;MAEtD;MACA;MACA,MAAMwD,QAAQ,GAAGP,IAAI,CAACQ,SAAS,CAAE,IAAI,CAACP,WAAW,CAAE;MACnD,IAAKM,QAAQ,EAAG;QAEfxD,GAAG,GAAGiD,IAAI,CAACM,WAAW,CAAEC,QAAQ,CAACE,aAAa,CAAE,IAAI1D,GAAG;MAExD;IAED;IAEA,MAAM2D,UAAU,GAAG3D,GAAG,GAAGA,GAAG,CAACmD,MAAM,CAAClB,MAAM,GAAG,CAAC;IAC9C,OAAQ,IAAI,CAACkB,MAAM,CAAClB,MAAM,GAAG0B,UAAU,EAAG;MAEzC,MAAMC,IAAI,GAAG,IAAI,CAACT,MAAM,CAACU,GAAG,CAAC,CAAC;MAC9BD,IAAI,CAACxD,QAAQ,CAACO,OAAO,CAAC,CAAC;MACvB,IAAI,CAACmD,MAAM,CAAEF,IAAK,CAAC;IAEpB;IAEA,KAAM,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,UAAU,EAAEtB,CAAC,EAAG,EAAG;MAEvC,MAAM;QAAEnC,KAAK;QAAE2C,YAAY;QAAEG,YAAY;QAAE1C,cAAc;QAAEd;MAAa,CAAC,GAAG,IAAI;MAEhF,IAAK6C,CAAC,IAAI,IAAI,CAACc,MAAM,CAAClB,MAAM,EAAG;QAE9B,MAAM2B,IAAI,GAAG,IAAItE,iBAAiB,CAAEU,GAAG,EAAE6C,YAAY,EAAE3C,KAAK,EAAEmC,CAAE,CAAC;QACjE,IAAI,CAAC0B,GAAG,CAAEH,IAAK,CAAC;QAChB,IAAI,CAACT,MAAM,CAACa,IAAI,CAAEJ,IAAK,CAAC;MAEzB;MAEA,MAAMA,IAAI,GAAG,IAAI,CAACT,MAAM,CAAEd,CAAC,CAAE;MAC7BuB,IAAI,CAAC5D,GAAG,GAAGA,GAAG;MACd4D,IAAI,CAAC1D,KAAK,GAAGA,KAAK;MAClB0D,IAAI,CAACtD,cAAc,GAAGA,cAAc;MACpCsD,IAAI,CAACpE,YAAY,GAAGA,YAAY;MAChCoE,IAAI,CAAC3D,QAAQ,GAAGT,YAAY,GAAGqD,YAAY,GAAGG,YAAY;MAC1DY,IAAI,CAACnD,MAAM,CAAC,CAAC;IAEd;EAED;EAEAwD,iBAAiBA,CAAE,GAAGrE,IAAI,EAAG;IAE5B,MAAMqD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMiB,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,IAAKjB,IAAI,KAAK,IAAI,EAAG;MAEpBA,IAAI,CAACkB,iBAAiB,CAAE,IAAI,EAAE,KAAM,CAAC;MAErC,IAAKD,MAAM,EAAG;QAEb,IAAI,CAAC7E,MAAM,CACT+E,IAAI,CAAEF,MAAM,CAACG,WAAY,CAAC,CAC1BC,MAAM,CAAC,CAAC,CACRC,QAAQ,CAAEtB,IAAI,CAACoB,WAAY,CAAC;MAE/B,CAAC,MAAM;QAEN,IAAI,CAAChF,MAAM,CACT+E,IAAI,CAAEnB,IAAI,CAACoB,WAAY,CAAC;MAE3B;;MAEA;MACA,IAAKpB,IAAI,CAACuB,eAAe,IAAIvB,IAAI,CAACK,aAAa,EAAG;QAEjDL,IAAI,CAACwB,WAAW,CAAE,IAAI,CAACvB,WAAW,EAAE7D,MAAO,CAAC;QAC5C,IAAI,CAACA,MAAM,CAACkF,QAAQ,CAAElF,MAAO,CAAC;MAE/B;MAEA,IAAI,CAACA,MAAM,CAACqF,SAAS,CACpB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACC,UAAU,EACf,IAAI,CAACC,KACN,CAAC;IAEF;IAEA,KAAK,CAACZ,iBAAiB,CAAE,GAAGrE,IAAK,CAAC;EAEnC;EAEAwE,IAAIA,CAAEU,MAAM,EAAG;IAEd,IAAI,CAAC5E,KAAK,GAAG4E,MAAM,CAAC5E,KAAK;IACzB,IAAI,CAAC+C,IAAI,GAAG6B,MAAM,CAAC7B,IAAI;IACvB,IAAI,CAACjD,GAAG,GAAG8E,MAAM,CAAC9E,GAAG;IACrB,IAAI,CAAC8C,OAAO,GAAGgC,MAAM,CAAChC,OAAO;IAC7B,IAAI,CAACF,KAAK,CAACwB,IAAI,CAAEU,MAAM,CAAClC,KAAM,CAAC;EAEhC;EAEAmC,KAAKA,CAAA,EAAG;IAEP,OAAO,IAAIpC,aAAa,CAAE,IAAI,CAACM,IAAI,EAAE,IAAI,CAACjD,GAAG,EAAE,IAAI,CAACE,KAAM,CAAC;EAE5D;EAEAS,OAAOA,CAAA,EAAG;IAET,IAAI,CAACkC,YAAY,CAAClC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACqC,YAAY,CAACrC,OAAO,CAAC,CAAC;IAE3B,MAAMqE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,KAAM,IAAI3C,CAAC,GAAG,CAAC,EAAE4C,CAAC,GAAGD,QAAQ,CAAC/C,MAAM,EAAEI,CAAC,GAAG4C,CAAC,EAAE5C,CAAC,EAAG,EAAG;MAEnD2C,QAAQ,CAAE3C,CAAC,CAAE,CAACjC,QAAQ,CAACO,OAAO,CAAC,CAAC;IAEjC;EAED;AAED;AAEA,OAAO,MAAMuE,iBAAiB,SAASvC,aAAa,CAAC;EAEpD5C,WAAWA,CAAE,GAAGH,IAAI,EAAG;IAEtB,KAAK,CAAE,GAAGA,IAAK,CAAC;IAEhBuF,OAAO,CAACC,IAAI,CAAE,uFAAwF,CAAC;EAExG;AAED;AAEA,SAASzC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}