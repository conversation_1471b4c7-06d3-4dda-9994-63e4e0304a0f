{"ast": null, "code": "export { default } from \"./flexbox.js\";\nexport * from \"./flexbox.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@mui/system/esm/flexbox/index.js"], "sourcesContent": ["export { default } from \"./flexbox.js\";\nexport * from \"./flexbox.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}