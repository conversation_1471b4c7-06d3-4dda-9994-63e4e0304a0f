{"ast": null, "code": "import { LWO2Parser } from \"./LWO2Parser.js\";\nimport { LWO3Parser } from \"./LWO3Parser.js\";\nclass IFFParser {\n  constructor() {\n    this.debugger = new Debugger();\n  }\n  parse(buffer) {\n    this.reader = new DataViewReader(buffer);\n    this.tree = {\n      materials: {},\n      layers: [],\n      tags: [],\n      textures: []\n    };\n    this.currentLayer = this.tree;\n    this.currentForm = this.tree;\n    this.parseTopForm();\n    if (this.tree.format === void 0) return;\n    if (this.tree.format === \"LWO2\") {\n      this.parser = new LWO2Parser(this);\n      while (!this.reader.endOfFile()) this.parser.parseBlock();\n    } else if (this.tree.format === \"LWO3\") {\n      this.parser = new LWO3Parser(this);\n      while (!this.reader.endOfFile()) this.parser.parseBlock();\n    }\n    this.debugger.offset = this.reader.offset;\n    this.debugger.closeForms();\n    return this.tree;\n  }\n  parseTopForm() {\n    this.debugger.offset = this.reader.offset;\n    var topForm = this.reader.getIDTag();\n    if (topForm !== \"FORM\") {\n      console.warn(\"LWOLoader: Top-level FORM missing.\");\n      return;\n    }\n    var length = this.reader.getUint32();\n    this.debugger.dataOffset = this.reader.offset;\n    this.debugger.length = length;\n    var type = this.reader.getIDTag();\n    if (type === \"LWO2\") {\n      this.tree.format = type;\n    } else if (type === \"LWO3\") {\n      this.tree.format = type;\n    }\n    this.debugger.node = 0;\n    this.debugger.nodeID = type;\n    this.debugger.log();\n    return;\n  }\n  ///\n  // FORM PARSING METHODS\n  ///\n  // Forms are organisational and can contain any number of sub chunks and sub forms\n  // FORM ::= 'FORM'[ID4], length[U4], type[ID4], ( chunk[CHUNK] | form[FORM] ) * }\n  parseForm(length) {\n    var type = this.reader.getIDTag();\n    switch (type) {\n      case \"ISEQ\":\n      case \"ANIM\":\n      case \"STCC\":\n      case \"VPVL\":\n      case \"VPRM\":\n      case \"NROT\":\n      case \"WRPW\":\n      case \"WRPH\":\n      case \"FUNC\":\n      case \"FALL\":\n      case \"OPAC\":\n      case \"GRAD\":\n      case \"ENVS\":\n      case \"VMOP\":\n      case \"VMBG\":\n      case \"OMAX\":\n      case \"STEX\":\n      case \"CKBG\":\n      case \"CKEY\":\n      case \"VMLA\":\n      case \"VMLB\":\n        this.debugger.skipped = true;\n        this.skipForm(length);\n        break;\n      case \"META\":\n      case \"NNDS\":\n      case \"NODS\":\n      case \"NDTA\":\n      case \"ADAT\":\n      case \"AOVS\":\n      case \"BLOK\":\n      case \"IBGC\":\n      case \"IOPC\":\n      case \"IIMG\":\n      case \"TXTR\":\n        this.debugger.length = 4;\n        this.debugger.skipped = true;\n        break;\n      case \"IFAL\":\n      case \"ISCL\":\n      case \"IPOS\":\n      case \"IROT\":\n      case \"IBMP\":\n      case \"IUTD\":\n      case \"IVTD\":\n        this.parseTextureNodeAttribute(type);\n        break;\n      case \"ENVL\":\n        this.parseEnvelope(length);\n        break;\n      case \"CLIP\":\n        if (this.tree.format === \"LWO2\") {\n          this.parseForm(length);\n        } else {\n          this.parseClip(length);\n        }\n        break;\n      case \"STIL\":\n        this.parseImage();\n        break;\n      case \"XREF\":\n        this.reader.skip(8);\n        this.currentForm.referenceTexture = {\n          index: this.reader.getUint32(),\n          refName: this.reader.getString()\n          // internal unique ref\n        };\n        break;\n      case \"IMST\":\n        this.parseImageStateForm(length);\n        break;\n      case \"SURF\":\n        this.parseSurfaceForm(length);\n        break;\n      case \"VALU\":\n        this.parseValueForm(length);\n        break;\n      case \"NTAG\":\n        this.parseSubNode(length);\n        break;\n      case \"ATTR\":\n      case \"SATR\":\n        this.setupForm(\"attributes\", length);\n        break;\n      case \"NCON\":\n        this.parseConnections(length);\n        break;\n      case \"SSHA\":\n        this.parentForm = this.currentForm;\n        this.currentForm = this.currentSurface;\n        this.setupForm(\"surfaceShader\", length);\n        break;\n      case \"SSHD\":\n        this.setupForm(\"surfaceShaderData\", length);\n        break;\n      case \"ENTR\":\n        this.parseEntryForm(length);\n        break;\n      case \"IMAP\":\n        this.parseImageMap(length);\n        break;\n      case \"TAMP\":\n        this.parseXVAL(\"amplitude\", length);\n        break;\n      case \"TMAP\":\n        this.setupForm(\"textureMap\", length);\n        break;\n      case \"CNTR\":\n        this.parseXVAL3(\"center\", length);\n        break;\n      case \"SIZE\":\n        this.parseXVAL3(\"scale\", length);\n        break;\n      case \"ROTA\":\n        this.parseXVAL3(\"rotation\", length);\n        break;\n      default:\n        this.parseUnknownForm(type, length);\n    }\n    this.debugger.node = 0;\n    this.debugger.nodeID = type;\n    this.debugger.log();\n  }\n  setupForm(type, length) {\n    if (!this.currentForm) this.currentForm = this.currentNode;\n    this.currentFormEnd = this.reader.offset + length;\n    this.parentForm = this.currentForm;\n    if (!this.currentForm[type]) {\n      this.currentForm[type] = {};\n      this.currentForm = this.currentForm[type];\n    } else {\n      console.warn(\"LWOLoader: form already exists on parent: \", type, this.currentForm);\n      this.currentForm = this.currentForm[type];\n    }\n  }\n  skipForm(length) {\n    this.reader.skip(length - 4);\n  }\n  parseUnknownForm(type, length) {\n    console.warn(\"LWOLoader: unknown FORM encountered: \" + type, length);\n    printBuffer(this.reader.dv.buffer, this.reader.offset, length - 4);\n    this.reader.skip(length - 4);\n  }\n  parseSurfaceForm(length) {\n    this.reader.skip(8);\n    var name = this.reader.getString();\n    var surface = {\n      attributes: {},\n      // LWO2 style non-node attributes will go here\n      connections: {},\n      name,\n      inputName: name,\n      nodes: {},\n      source: this.reader.getString()\n    };\n    this.tree.materials[name] = surface;\n    this.currentSurface = surface;\n    this.parentForm = this.tree.materials;\n    this.currentForm = surface;\n    this.currentFormEnd = this.reader.offset + length;\n  }\n  parseSurfaceLwo2(length) {\n    var name = this.reader.getString();\n    var surface = {\n      attributes: {},\n      // LWO2 style non-node attributes will go here\n      connections: {},\n      name,\n      nodes: {},\n      source: this.reader.getString()\n    };\n    this.tree.materials[name] = surface;\n    this.currentSurface = surface;\n    this.parentForm = this.tree.materials;\n    this.currentForm = surface;\n    this.currentFormEnd = this.reader.offset + length;\n  }\n  parseSubNode(length) {\n    this.reader.skip(8);\n    var name = this.reader.getString();\n    var node = {\n      name\n    };\n    this.currentForm = node;\n    this.currentNode = node;\n    this.currentFormEnd = this.reader.offset + length;\n  }\n  // collect attributes from all nodes at the top level of a surface\n  parseConnections(length) {\n    this.currentFormEnd = this.reader.offset + length;\n    this.parentForm = this.currentForm;\n    this.currentForm = this.currentSurface.connections;\n  }\n  // surface node attribute data, e.g. specular, roughness etc\n  parseEntryForm(length) {\n    this.reader.skip(8);\n    var name = this.reader.getString();\n    this.currentForm = this.currentNode.attributes;\n    this.setupForm(name, length);\n  }\n  // parse values from material - doesn't match up to other LWO3 data types\n  // sub form of entry form\n  parseValueForm() {\n    this.reader.skip(8);\n    var valueType = this.reader.getString();\n    if (valueType === \"double\") {\n      this.currentForm.value = this.reader.getUint64();\n    } else if (valueType === \"int\") {\n      this.currentForm.value = this.reader.getUint32();\n    } else if (valueType === \"vparam\") {\n      this.reader.skip(24);\n      this.currentForm.value = this.reader.getFloat64();\n    } else if (valueType === \"vparam3\") {\n      this.reader.skip(24);\n      this.currentForm.value = this.reader.getFloat64Array(3);\n    }\n  }\n  // holds various data about texture node image state\n  // Data other thanmipMapLevel unknown\n  parseImageStateForm() {\n    this.reader.skip(8);\n    this.currentForm.mipMapLevel = this.reader.getFloat32();\n  }\n  // LWO2 style image data node OR LWO3 textures defined at top level in editor (not as SURF node)\n  parseImageMap(length) {\n    this.currentFormEnd = this.reader.offset + length;\n    this.parentForm = this.currentForm;\n    if (!this.currentForm.maps) this.currentForm.maps = [];\n    var map = {};\n    this.currentForm.maps.push(map);\n    this.currentForm = map;\n    this.reader.skip(10);\n  }\n  parseTextureNodeAttribute(type) {\n    this.reader.skip(28);\n    this.reader.skip(20);\n    switch (type) {\n      case \"ISCL\":\n        this.currentNode.scale = this.reader.getFloat32Array(3);\n        break;\n      case \"IPOS\":\n        this.currentNode.position = this.reader.getFloat32Array(3);\n        break;\n      case \"IROT\":\n        this.currentNode.rotation = this.reader.getFloat32Array(3);\n        break;\n      case \"IFAL\":\n        this.currentNode.falloff = this.reader.getFloat32Array(3);\n        break;\n      case \"IBMP\":\n        this.currentNode.amplitude = this.reader.getFloat32();\n        break;\n      case \"IUTD\":\n        this.currentNode.uTiles = this.reader.getFloat32();\n        break;\n      case \"IVTD\":\n        this.currentNode.vTiles = this.reader.getFloat32();\n        break;\n    }\n    this.reader.skip(2);\n  }\n  // ENVL forms are currently ignored\n  parseEnvelope(length) {\n    this.reader.skip(length - 4);\n  }\n  ///\n  // CHUNK PARSING METHODS\n  ///\n  // clips can either be defined inside a surface node, or at the top\n  // level and they have a different format in each case\n  parseClip(length) {\n    var tag = this.reader.getIDTag();\n    if (tag === \"FORM\") {\n      this.reader.skip(16);\n      this.currentNode.fileName = this.reader.getString();\n      return;\n    }\n    this.reader.setOffset(this.reader.offset - 4);\n    this.currentFormEnd = this.reader.offset + length;\n    this.parentForm = this.currentForm;\n    this.reader.skip(8);\n    var texture = {\n      index: this.reader.getUint32()\n    };\n    this.tree.textures.push(texture);\n    this.currentForm = texture;\n  }\n  parseClipLwo2(length) {\n    var texture = {\n      index: this.reader.getUint32(),\n      fileName: \"\"\n    };\n    while (true) {\n      var tag = this.reader.getIDTag();\n      var n_length = this.reader.getUint16();\n      if (tag === \"STIL\") {\n        texture.fileName = this.reader.getString();\n        break;\n      }\n      if (n_length >= length) {\n        break;\n      }\n    }\n    this.tree.textures.push(texture);\n    this.currentForm = texture;\n  }\n  parseImage() {\n    this.reader.skip(8);\n    this.currentForm.fileName = this.reader.getString();\n  }\n  parseXVAL(type, length) {\n    var endOffset = this.reader.offset + length - 4;\n    this.reader.skip(8);\n    this.currentForm[type] = this.reader.getFloat32();\n    this.reader.setOffset(endOffset);\n  }\n  parseXVAL3(type, length) {\n    var endOffset = this.reader.offset + length - 4;\n    this.reader.skip(8);\n    this.currentForm[type] = {\n      x: this.reader.getFloat32(),\n      y: this.reader.getFloat32(),\n      z: this.reader.getFloat32()\n    };\n    this.reader.setOffset(endOffset);\n  }\n  // Tags associated with an object\n  // OTAG { type[ID4], tag-string[S0] }\n  parseObjectTag() {\n    if (!this.tree.objectTags) this.tree.objectTags = {};\n    this.tree.objectTags[this.reader.getIDTag()] = {\n      tagString: this.reader.getString()\n    };\n  }\n  // Signals the start of a new layer. All the data chunks which follow will be included in this layer until another layer chunk is encountered.\n  // LAYR: number[U2], flags[U2], pivot[VEC12], name[S0], parent[U2]\n  parseLayer(length) {\n    var layer = {\n      number: this.reader.getUint16(),\n      flags: this.reader.getUint16(),\n      // If the least significant bit of flags is set, the layer is hidden.\n      pivot: this.reader.getFloat32Array(3),\n      // Note: this seems to be superflous, as the geometry is translated when pivot is present\n      name: this.reader.getString()\n    };\n    this.tree.layers.push(layer);\n    this.currentLayer = layer;\n    var parsedLength = 16 + stringOffset(this.currentLayer.name);\n    this.currentLayer.parent = parsedLength < length ? this.reader.getUint16() : -1;\n  }\n  // VEC12 * ( F4 + F4 + F4 ) array of x,y,z vectors\n  // Converting from left to right handed coordinate system:\n  // x -> -x and switch material FrontSide -> BackSide\n  parsePoints(length) {\n    this.currentPoints = [];\n    for (var i = 0; i < length / 4; i += 3) {\n      this.currentPoints.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32());\n    }\n  }\n  // parse VMAP or VMAD\n  // Associates a set of floating-point vectors with a set of points.\n  // VMAP: { type[ID4], dimension[U2], name[S0], ( vert[VX], value[F4] # dimension ) * }\n  // VMAD Associates a set of floating-point vectors with the vertices of specific polygons.\n  // Similar to VMAP UVs, but associates with polygon vertices rather than points\n  // to solve to problem of UV seams:  VMAD chunks are paired with VMAPs of the same name,\n  // if they exist. The vector values in the VMAD will then replace those in the\n  // corresponding VMAP, but only for calculations involving the specified polygons.\n  // VMAD { type[ID4], dimension[U2], name[S0], ( vert[VX], poly[VX], value[F4] # dimension ) * }\n  parseVertexMapping(length, discontinuous) {\n    var finalOffset = this.reader.offset + length;\n    var channelName = this.reader.getString();\n    if (this.reader.offset === finalOffset) {\n      this.currentForm.UVChannel = channelName;\n      return;\n    }\n    this.reader.setOffset(this.reader.offset - stringOffset(channelName));\n    var type = this.reader.getIDTag();\n    this.reader.getUint16();\n    var name = this.reader.getString();\n    var remainingLength = length - 6 - stringOffset(name);\n    switch (type) {\n      case \"TXUV\":\n        this.parseUVMapping(name, finalOffset, discontinuous);\n        break;\n      case \"MORF\":\n      case \"SPOT\":\n        this.parseMorphTargets(name, finalOffset, type);\n        break;\n      case \"APSL\":\n      case \"NORM\":\n      case \"WGHT\":\n      case \"MNVW\":\n      case \"PICK\":\n      case \"RGB \":\n      case \"RGBA\":\n        this.reader.skip(remainingLength);\n        break;\n      default:\n        console.warn(\"LWOLoader: unknown vertex map type: \" + type);\n        this.reader.skip(remainingLength);\n    }\n  }\n  parseUVMapping(name, finalOffset, discontinuous) {\n    var uvIndices = [];\n    var polyIndices = [];\n    var uvs = [];\n    while (this.reader.offset < finalOffset) {\n      uvIndices.push(this.reader.getVariableLengthIndex());\n      if (discontinuous) polyIndices.push(this.reader.getVariableLengthIndex());\n      uvs.push(this.reader.getFloat32(), this.reader.getFloat32());\n    }\n    if (discontinuous) {\n      if (!this.currentLayer.discontinuousUVs) this.currentLayer.discontinuousUVs = {};\n      this.currentLayer.discontinuousUVs[name] = {\n        uvIndices,\n        polyIndices,\n        uvs\n      };\n    } else {\n      if (!this.currentLayer.uvs) this.currentLayer.uvs = {};\n      this.currentLayer.uvs[name] = {\n        uvIndices,\n        uvs\n      };\n    }\n  }\n  parseMorphTargets(name, finalOffset, type) {\n    var indices = [];\n    var points = [];\n    type = type === \"MORF\" ? \"relative\" : \"absolute\";\n    while (this.reader.offset < finalOffset) {\n      indices.push(this.reader.getVariableLengthIndex());\n      points.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32());\n    }\n    if (!this.currentLayer.morphTargets) this.currentLayer.morphTargets = {};\n    this.currentLayer.morphTargets[name] = {\n      indices,\n      points,\n      type\n    };\n  }\n  // A list of polygons for the current layer.\n  // POLS { type[ID4], ( numvert+flags[U2], vert[VX] # numvert ) * }\n  parsePolygonList(length) {\n    var finalOffset = this.reader.offset + length;\n    var type = this.reader.getIDTag();\n    var indices = [];\n    var polygonDimensions = [];\n    while (this.reader.offset < finalOffset) {\n      var numverts = this.reader.getUint16();\n      numverts = numverts & 1023;\n      polygonDimensions.push(numverts);\n      for (var j = 0; j < numverts; j++) indices.push(this.reader.getVariableLengthIndex());\n    }\n    var geometryData = {\n      type,\n      vertexIndices: indices,\n      polygonDimensions,\n      points: this.currentPoints\n    };\n    if (polygonDimensions[0] === 1) geometryData.type = \"points\";else if (polygonDimensions[0] === 2) geometryData.type = \"lines\";\n    this.currentLayer.geometry = geometryData;\n  }\n  // Lists the tag strings that can be associated with polygons by the PTAG chunk.\n  // TAGS { tag-string[S0] * }\n  parseTagStrings(length) {\n    this.tree.tags = this.reader.getStringArray(length);\n  }\n  // Associates tags of a given type with polygons in the most recent POLS chunk.\n  // PTAG { type[ID4], ( poly[VX], tag[U2] ) * }\n  parsePolygonTagMapping(length) {\n    var finalOffset = this.reader.offset + length;\n    var type = this.reader.getIDTag();\n    if (type === \"SURF\") this.parseMaterialIndices(finalOffset);else {\n      this.reader.skip(length - 4);\n    }\n  }\n  parseMaterialIndices(finalOffset) {\n    this.currentLayer.geometry.materialIndices = [];\n    while (this.reader.offset < finalOffset) {\n      var polygonIndex = this.reader.getVariableLengthIndex();\n      var materialIndex = this.reader.getUint16();\n      this.currentLayer.geometry.materialIndices.push(polygonIndex, materialIndex);\n    }\n  }\n  parseUnknownCHUNK(blockID, length) {\n    console.warn(\"LWOLoader: unknown chunk type: \" + blockID + \" length: \" + length);\n    var data = this.reader.getString(length);\n    this.currentForm[blockID] = data;\n  }\n}\nclass DataViewReader {\n  constructor(buffer) {\n    this.dv = new DataView(buffer);\n    this.offset = 0;\n    this._textDecoder = new TextDecoder();\n    this._bytes = new Uint8Array(buffer);\n  }\n  size() {\n    return this.dv.buffer.byteLength;\n  }\n  setOffset(offset) {\n    if (offset > 0 && offset < this.dv.buffer.byteLength) {\n      this.offset = offset;\n    } else {\n      console.error(\"LWOLoader: invalid buffer offset\");\n    }\n  }\n  endOfFile() {\n    if (this.offset >= this.size()) return true;\n    return false;\n  }\n  skip(length) {\n    this.offset += length;\n  }\n  getUint8() {\n    var value = this.dv.getUint8(this.offset);\n    this.offset += 1;\n    return value;\n  }\n  getUint16() {\n    var value = this.dv.getUint16(this.offset);\n    this.offset += 2;\n    return value;\n  }\n  getInt32() {\n    var value = this.dv.getInt32(this.offset, false);\n    this.offset += 4;\n    return value;\n  }\n  getUint32() {\n    var value = this.dv.getUint32(this.offset, false);\n    this.offset += 4;\n    return value;\n  }\n  getUint64() {\n    var low, high;\n    high = this.getUint32();\n    low = this.getUint32();\n    return high * 4294967296 + low;\n  }\n  getFloat32() {\n    var value = this.dv.getFloat32(this.offset, false);\n    this.offset += 4;\n    return value;\n  }\n  getFloat32Array(size) {\n    var a = [];\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat32());\n    }\n    return a;\n  }\n  getFloat64() {\n    var value = this.dv.getFloat64(this.offset, this.littleEndian);\n    this.offset += 8;\n    return value;\n  }\n  getFloat64Array(size) {\n    var a = [];\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat64());\n    }\n    return a;\n  }\n  // get variable-length index data type\n  // VX ::= index[U2] | (index + 0xFF000000)[U4]\n  // If the index value is less than 65,280 (0xFF00),then VX === U2\n  // otherwise VX === U4 with bits 24-31 set\n  // When reading an index, if the first byte encountered is 255 (0xFF), then\n  // the four-byte form is being used and the first byte should be discarded or masked out.\n  getVariableLengthIndex() {\n    var firstByte = this.getUint8();\n    if (firstByte === 255) {\n      return this.getUint8() * 65536 + this.getUint8() * 256 + this.getUint8();\n    }\n    return firstByte * 256 + this.getUint8();\n  }\n  // An ID tag is a sequence of 4 bytes containing 7-bit ASCII values\n  getIDTag() {\n    return this.getString(4);\n  }\n  getString(size) {\n    if (size === 0) return;\n    const start = this.offset;\n    let result;\n    let length;\n    if (size) {\n      length = size;\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, size));\n    } else {\n      length = this._bytes.indexOf(0, start) - start;\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, length));\n      length++;\n      length += length % 2;\n    }\n    this.skip(length);\n    return result;\n  }\n  getStringArray(size) {\n    var a = this.getString(size);\n    a = a.split(\"\\0\");\n    return a.filter(Boolean);\n  }\n}\nclass Debugger {\n  constructor() {\n    this.active = false;\n    this.depth = 0;\n    this.formList = [];\n  }\n  enable() {\n    this.active = true;\n  }\n  log() {\n    if (!this.active) return;\n    var nodeType;\n    switch (this.node) {\n      case 0:\n        nodeType = \"FORM\";\n        break;\n      case 1:\n        nodeType = \"CHK\";\n        break;\n      case 2:\n        nodeType = \"S-CHK\";\n        break;\n    }\n    console.log(\"| \".repeat(this.depth) + nodeType, this.nodeID, `( ${this.offset} ) -> ( ${this.dataOffset + this.length} )`, this.node == 0 ? \" {\" : \"\", this.skipped ? \"SKIPPED\" : \"\", this.node == 0 && this.skipped ? \"}\" : \"\");\n    if (this.node == 0 && !this.skipped) {\n      this.depth += 1;\n      this.formList.push(this.dataOffset + this.length);\n    }\n    this.skipped = false;\n  }\n  closeForms() {\n    if (!this.active) return;\n    for (var i = this.formList.length - 1; i >= 0; i--) {\n      if (this.offset >= this.formList[i]) {\n        this.depth -= 1;\n        console.log(\"| \".repeat(this.depth) + \"}\");\n        this.formList.splice(-1, 1);\n      }\n    }\n  }\n}\nfunction isEven(num) {\n  return num % 2;\n}\nfunction stringOffset(string) {\n  return string.length + 1 + (isEven(string.length + 1) ? 1 : 0);\n}\nfunction printBuffer(buffer, from, to) {\n  console.log(new TextDecoder().decode(new Uint8Array(buffer, from, to)));\n}\nexport { IFFParser };", "map": {"version": 3, "names": ["IFFParser", "constructor", "debugger", "Debugger", "parse", "buffer", "reader", "DataViewReader", "tree", "materials", "layers", "tags", "textures", "<PERSON><PERSON><PERSON><PERSON>", "currentForm", "parseTopForm", "format", "parser", "LWO2Parser", "endOfFile", "parseBlock", "LWO3Parser", "offset", "closeForms", "topForm", "getIDTag", "console", "warn", "length", "getUint32", "dataOffset", "type", "node", "nodeID", "log", "parseForm", "skipped", "skipForm", "parseTextureNodeAttribute", "parseEnvelope", "parseClip", "parseImage", "skip", "referenceTexture", "index", "refName", "getString", "parseImageStateForm", "parseSurfaceForm", "parseValueForm", "parseSubNode", "setupForm", "parseConnections", "parentForm", "currentSurface", "parseEntryForm", "parseImageMap", "parseXVAL", "parseXVAL3", "parseUnknownForm", "currentNode", "currentFormEnd", "printBuffer", "dv", "name", "surface", "attributes", "connections", "inputName", "nodes", "source", "parseSurfaceLwo2", "valueType", "value", "getUint64", "getFloat64", "getFloat64Array", "mipMapLevel", "getFloat32", "maps", "map", "push", "scale", "getFloat32Array", "position", "rotation", "falloff", "amplitude", "uTiles", "vTiles", "tag", "fileName", "setOffset", "texture", "parseClipLwo2", "n_length", "getUint16", "endOffset", "x", "y", "z", "parseObjectTag", "objectTags", "tagString", "<PERSON>se<PERSON><PERSON><PERSON>", "layer", "number", "flags", "pivot", "parsed<PERSON><PERSON>th", "stringOffset", "parent", "parsePoints", "currentPoints", "i", "parseVertexMapping", "discontinuous", "finalOffset", "channelName", "UVChannel", "remaining<PERSON><PERSON>th", "parseUVMapping", "parseMorphTargets", "uvIndices", "polyIndices", "uvs", "getVariableLengthIndex", "discontinuousUVs", "indices", "points", "morphTargets", "parsePolygonList", "polygonDimensions", "numverts", "j", "geometryData", "vertexIndices", "geometry", "parseTagStrings", "getStringArray", "parsePolygonTagMapping", "parseMaterialIndices", "materialIndices", "polygonIndex", "materialIndex", "parseUnknownCHUNK", "blockID", "data", "DataView", "_textDecoder", "TextDecoder", "_bytes", "Uint8Array", "size", "byteLength", "error", "getUint8", "getInt32", "low", "high", "a", "littleEndian", "firstByte", "start", "result", "decode", "indexOf", "split", "filter", "Boolean", "active", "depth", "formList", "enable", "nodeType", "repeat", "splice", "isEven", "num", "string", "from", "to"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/lwo/IFFParser.js"], "sourcesContent": ["/**\n * === IFFParser ===\n * - Parses data from the IFF buffer.\n * - LWO3 files are in IFF format and can contain the following data types, referred to by shorthand codes\n *\n * ATOMIC DATA TYPES\n *  ID Tag - 4x 7 bit uppercase ASCII chars: ID4\n *  signed integer, 1, 2, or 4 byte length: I1, I2, I4\n *  unsigned integer, 1, 2, or 4 byte length: U1, U2, U4\n *  float, 4 byte length: F4\n *  string, series of ASCII chars followed by null byte (If the length of the string including the null terminating byte is odd, an extra null is added so that the data that follows will begin on an even byte boundary): S0\n *\n * COMPOUND DATA TYPES\n *  Variable-length Index (index into an array or collection): U2 or U4 : VX\n *  Color (RGB): F4 + F4 + F4: COL12\n *  Coordinate (x, y, z): F4 + F4 + F4: VEC12\n *  Percentage F4 data type from 0->1 with 1 = 100%: FP4\n *  Angle in radian F4: ANG4\n *  Filename (string) S0: FNAM0\n *  XValue F4 + index (VX) + optional envelope( ENVL ): XVAL\n *  XValue vector VEC12 + index (VX) + optional envelope( ENVL ): XVAL3\n *\n *  The IFF file is arranged in chunks:\n *  CHUNK = ID4 + length (U4) + length X bytes of data + optional 0 pad byte\n *  optional 0 pad byte is there to ensure chunk ends on even boundary, not counted in size\n *\n * COMPOUND DATA TYPES\n * - Chunks are combined in Forms (collections of chunks)\n * - FORM = string 'FORM' (ID4) + length (U4) + type (ID4) + optional ( CHUNK | FORM )\n * - CHUNKS and FORMS are collectively referred to as blocks\n * - The entire file is contained in one top level FORM\n *\n **/\n\nimport { LWO2Parser } from './LWO2Parser'\nimport { LWO3Parser } from './LWO3Parser'\n\nclass IFFParser {\n  constructor() {\n    this.debugger = new Debugger()\n    // this.debugger.enable(); // un-comment to log IFF hierarchy.\n  }\n\n  parse(buffer) {\n    this.reader = new DataViewReader(buffer)\n\n    this.tree = {\n      materials: {},\n      layers: [],\n      tags: [],\n      textures: [],\n    }\n\n    // start out at the top level to add any data before first layer is encountered\n    this.currentLayer = this.tree\n    this.currentForm = this.tree\n\n    this.parseTopForm()\n\n    if (this.tree.format === undefined) return\n\n    if (this.tree.format === 'LWO2') {\n      this.parser = new LWO2Parser(this)\n      while (!this.reader.endOfFile()) this.parser.parseBlock()\n    } else if (this.tree.format === 'LWO3') {\n      this.parser = new LWO3Parser(this)\n      while (!this.reader.endOfFile()) this.parser.parseBlock()\n    }\n\n    this.debugger.offset = this.reader.offset\n    this.debugger.closeForms()\n\n    return this.tree\n  }\n\n  parseTopForm() {\n    this.debugger.offset = this.reader.offset\n\n    var topForm = this.reader.getIDTag()\n\n    if (topForm !== 'FORM') {\n      console.warn('LWOLoader: Top-level FORM missing.')\n      return\n    }\n\n    var length = this.reader.getUint32()\n\n    this.debugger.dataOffset = this.reader.offset\n    this.debugger.length = length\n\n    var type = this.reader.getIDTag()\n\n    if (type === 'LWO2') {\n      this.tree.format = type\n    } else if (type === 'LWO3') {\n      this.tree.format = type\n    }\n\n    this.debugger.node = 0\n    this.debugger.nodeID = type\n    this.debugger.log()\n\n    return\n  }\n\n  ///\n  // FORM PARSING METHODS\n  ///\n\n  // Forms are organisational and can contain any number of sub chunks and sub forms\n  // FORM ::= 'FORM'[ID4], length[U4], type[ID4], ( chunk[CHUNK] | form[FORM] ) * }\n  parseForm(length) {\n    var type = this.reader.getIDTag()\n\n    switch (type) {\n      // SKIPPED FORMS\n      // if skipForm( length ) is called, the entire form and any sub forms and chunks are skipped\n\n      case 'ISEQ': // Image sequence\n      case 'ANIM': // plug in animation\n      case 'STCC': // Color-cycling Still\n      case 'VPVL':\n      case 'VPRM':\n      case 'NROT':\n      case 'WRPW': // image wrap w ( for cylindrical and spherical projections)\n      case 'WRPH': // image wrap h\n      case 'FUNC':\n      case 'FALL':\n      case 'OPAC':\n      case 'GRAD': // gradient texture\n      case 'ENVS':\n      case 'VMOP':\n      case 'VMBG':\n\n      // Car Material FORMS\n      case 'OMAX':\n      case 'STEX':\n      case 'CKBG':\n      case 'CKEY':\n      case 'VMLA':\n      case 'VMLB':\n        this.debugger.skipped = true\n        this.skipForm(length) // not currently supported\n        break\n\n      // if break; is called directly, the position in the lwoTree is not created\n      // any sub chunks and forms are added to the parent form instead\n      case 'META':\n      case 'NNDS':\n      case 'NODS':\n      case 'NDTA':\n      case 'ADAT':\n      case 'AOVS':\n      case 'BLOK':\n\n      // used by texture nodes\n      case 'IBGC': // imageBackgroundColor\n      case 'IOPC': // imageOpacity\n      case 'IIMG': // hold reference to image path\n      case 'TXTR':\n        // this.setupForm( type, length );\n        this.debugger.length = 4\n        this.debugger.skipped = true\n        break\n\n      case 'IFAL': // imageFallof\n      case 'ISCL': // imageScale\n      case 'IPOS': // imagePosition\n      case 'IROT': // imageRotation\n      case 'IBMP':\n      case 'IUTD':\n      case 'IVTD':\n        this.parseTextureNodeAttribute(type)\n        break\n\n      case 'ENVL':\n        this.parseEnvelope(length)\n        break\n\n      // CLIP FORM AND SUB FORMS\n\n      case 'CLIP':\n        if (this.tree.format === 'LWO2') {\n          this.parseForm(length)\n        } else {\n          this.parseClip(length)\n        }\n\n        break\n\n      case 'STIL':\n        this.parseImage()\n        break\n\n      case 'XREF': // clone of another STIL\n        this.reader.skip(8) // unknown\n        this.currentForm.referenceTexture = {\n          index: this.reader.getUint32(),\n          refName: this.reader.getString(), // internal unique ref\n        }\n        break\n\n      // Not in spec, used by texture nodes\n\n      case 'IMST':\n        this.parseImageStateForm(length)\n        break\n\n      // SURF FORM AND SUB FORMS\n\n      case 'SURF':\n        this.parseSurfaceForm(length)\n        break\n\n      case 'VALU': // Not in spec\n        this.parseValueForm(length)\n        break\n\n      case 'NTAG':\n        this.parseSubNode(length)\n        break\n\n      case 'ATTR': // BSDF Node Attributes\n      case 'SATR': // Standard Node Attributes\n        this.setupForm('attributes', length)\n        break\n\n      case 'NCON':\n        this.parseConnections(length)\n        break\n\n      case 'SSHA':\n        this.parentForm = this.currentForm\n        this.currentForm = this.currentSurface\n        this.setupForm('surfaceShader', length)\n        break\n\n      case 'SSHD':\n        this.setupForm('surfaceShaderData', length)\n        break\n\n      case 'ENTR': // Not in spec\n        this.parseEntryForm(length)\n        break\n\n      // Image Map Layer\n\n      case 'IMAP':\n        this.parseImageMap(length)\n        break\n\n      case 'TAMP':\n        this.parseXVAL('amplitude', length)\n        break\n\n      //Texture Mapping Form\n\n      case 'TMAP':\n        this.setupForm('textureMap', length)\n        break\n\n      case 'CNTR':\n        this.parseXVAL3('center', length)\n        break\n\n      case 'SIZE':\n        this.parseXVAL3('scale', length)\n        break\n\n      case 'ROTA':\n        this.parseXVAL3('rotation', length)\n        break\n\n      default:\n        this.parseUnknownForm(type, length)\n    }\n\n    this.debugger.node = 0\n    this.debugger.nodeID = type\n    this.debugger.log()\n  }\n\n  setupForm(type, length) {\n    if (!this.currentForm) this.currentForm = this.currentNode\n\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    if (!this.currentForm[type]) {\n      this.currentForm[type] = {}\n      this.currentForm = this.currentForm[type]\n    } else {\n      // should never see this unless there's a bug in the reader\n      console.warn('LWOLoader: form already exists on parent: ', type, this.currentForm)\n\n      this.currentForm = this.currentForm[type]\n    }\n  }\n\n  skipForm(length) {\n    this.reader.skip(length - 4)\n  }\n\n  parseUnknownForm(type, length) {\n    console.warn('LWOLoader: unknown FORM encountered: ' + type, length)\n\n    printBuffer(this.reader.dv.buffer, this.reader.offset, length - 4)\n    this.reader.skip(length - 4)\n  }\n\n  parseSurfaceForm(length) {\n    this.reader.skip(8) // unknown Uint32 x2\n\n    var name = this.reader.getString()\n\n    var surface = {\n      attributes: {}, // LWO2 style non-node attributes will go here\n      connections: {},\n      name: name,\n      inputName: name,\n      nodes: {},\n      source: this.reader.getString(),\n    }\n\n    this.tree.materials[name] = surface\n    this.currentSurface = surface\n\n    this.parentForm = this.tree.materials\n    this.currentForm = surface\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  parseSurfaceLwo2(length) {\n    var name = this.reader.getString()\n\n    var surface = {\n      attributes: {}, // LWO2 style non-node attributes will go here\n      connections: {},\n      name: name,\n      nodes: {},\n      source: this.reader.getString(),\n    }\n\n    this.tree.materials[name] = surface\n    this.currentSurface = surface\n\n    this.parentForm = this.tree.materials\n    this.currentForm = surface\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  parseSubNode(length) {\n    // parse the NRNM CHUNK of the subnode FORM to get\n    // a meaningful name for the subNode\n    // some subnodes can be renamed, but Input and Surface cannot\n\n    this.reader.skip(8) // NRNM + length\n    var name = this.reader.getString()\n\n    var node = {\n      name: name,\n    }\n    this.currentForm = node\n    this.currentNode = node\n\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  // collect attributes from all nodes at the top level of a surface\n  parseConnections(length) {\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    this.currentForm = this.currentSurface.connections\n  }\n\n  // surface node attribute data, e.g. specular, roughness etc\n  parseEntryForm(length) {\n    this.reader.skip(8) // NAME + length\n    var name = this.reader.getString()\n    this.currentForm = this.currentNode.attributes\n\n    this.setupForm(name, length)\n  }\n\n  // parse values from material - doesn't match up to other LWO3 data types\n  // sub form of entry form\n  parseValueForm() {\n    this.reader.skip(8) // unknown + length\n\n    var valueType = this.reader.getString()\n\n    if (valueType === 'double') {\n      this.currentForm.value = this.reader.getUint64()\n    } else if (valueType === 'int') {\n      this.currentForm.value = this.reader.getUint32()\n    } else if (valueType === 'vparam') {\n      this.reader.skip(24)\n      this.currentForm.value = this.reader.getFloat64()\n    } else if (valueType === 'vparam3') {\n      this.reader.skip(24)\n      this.currentForm.value = this.reader.getFloat64Array(3)\n    }\n  }\n\n  // holds various data about texture node image state\n  // Data other thanmipMapLevel unknown\n  parseImageStateForm() {\n    this.reader.skip(8) // unknown\n\n    this.currentForm.mipMapLevel = this.reader.getFloat32()\n  }\n\n  // LWO2 style image data node OR LWO3 textures defined at top level in editor (not as SURF node)\n  parseImageMap(length) {\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    if (!this.currentForm.maps) this.currentForm.maps = []\n\n    var map = {}\n    this.currentForm.maps.push(map)\n    this.currentForm = map\n\n    this.reader.skip(10) // unknown, could be an issue if it contains a VX\n  }\n\n  parseTextureNodeAttribute(type) {\n    this.reader.skip(28) // FORM + length + VPRM + unknown + Uint32 x2 + float32\n\n    this.reader.skip(20) // FORM + length + VPVL + float32 + Uint32\n\n    switch (type) {\n      case 'ISCL':\n        this.currentNode.scale = this.reader.getFloat32Array(3)\n        break\n      case 'IPOS':\n        this.currentNode.position = this.reader.getFloat32Array(3)\n        break\n      case 'IROT':\n        this.currentNode.rotation = this.reader.getFloat32Array(3)\n        break\n      case 'IFAL':\n        this.currentNode.falloff = this.reader.getFloat32Array(3)\n        break\n\n      case 'IBMP':\n        this.currentNode.amplitude = this.reader.getFloat32()\n        break\n      case 'IUTD':\n        this.currentNode.uTiles = this.reader.getFloat32()\n        break\n      case 'IVTD':\n        this.currentNode.vTiles = this.reader.getFloat32()\n        break\n    }\n\n    this.reader.skip(2) // unknown\n  }\n\n  // ENVL forms are currently ignored\n  parseEnvelope(length) {\n    this.reader.skip(length - 4) // skipping  entirely for now\n  }\n\n  ///\n  // CHUNK PARSING METHODS\n  ///\n\n  // clips can either be defined inside a surface node, or at the top\n  // level and they have a different format in each case\n  parseClip(length) {\n    var tag = this.reader.getIDTag()\n\n    // inside surface node\n    if (tag === 'FORM') {\n      this.reader.skip(16)\n\n      this.currentNode.fileName = this.reader.getString()\n\n      return\n    }\n\n    // otherwise top level\n    this.reader.setOffset(this.reader.offset - 4)\n\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    this.reader.skip(8) // unknown\n\n    var texture = {\n      index: this.reader.getUint32(),\n    }\n    this.tree.textures.push(texture)\n    this.currentForm = texture\n  }\n\n  parseClipLwo2(length) {\n    var texture = {\n      index: this.reader.getUint32(),\n      fileName: '',\n    }\n\n    // seach STIL block\n    while (true) {\n      var tag = this.reader.getIDTag()\n      var n_length = this.reader.getUint16()\n      if (tag === 'STIL') {\n        texture.fileName = this.reader.getString()\n        break\n      }\n\n      if (n_length >= length) {\n        break\n      }\n    }\n\n    this.tree.textures.push(texture)\n    this.currentForm = texture\n  }\n\n  parseImage() {\n    this.reader.skip(8) // unknown\n    this.currentForm.fileName = this.reader.getString()\n  }\n\n  parseXVAL(type, length) {\n    var endOffset = this.reader.offset + length - 4\n    this.reader.skip(8)\n\n    this.currentForm[type] = this.reader.getFloat32()\n\n    this.reader.setOffset(endOffset) // set end offset directly to skip optional envelope\n  }\n\n  parseXVAL3(type, length) {\n    var endOffset = this.reader.offset + length - 4\n    this.reader.skip(8)\n\n    this.currentForm[type] = {\n      x: this.reader.getFloat32(),\n      y: this.reader.getFloat32(),\n      z: this.reader.getFloat32(),\n    }\n\n    this.reader.setOffset(endOffset)\n  }\n\n  // Tags associated with an object\n  // OTAG { type[ID4], tag-string[S0] }\n  parseObjectTag() {\n    if (!this.tree.objectTags) this.tree.objectTags = {}\n\n    this.tree.objectTags[this.reader.getIDTag()] = {\n      tagString: this.reader.getString(),\n    }\n  }\n\n  // Signals the start of a new layer. All the data chunks which follow will be included in this layer until another layer chunk is encountered.\n  // LAYR: number[U2], flags[U2], pivot[VEC12], name[S0], parent[U2]\n  parseLayer(length) {\n    var layer = {\n      number: this.reader.getUint16(),\n      flags: this.reader.getUint16(), // If the least significant bit of flags is set, the layer is hidden.\n      pivot: this.reader.getFloat32Array(3), // Note: this seems to be superflous, as the geometry is translated when pivot is present\n      name: this.reader.getString(),\n    }\n\n    this.tree.layers.push(layer)\n    this.currentLayer = layer\n\n    var parsedLength = 16 + stringOffset(this.currentLayer.name) // index ( 2 ) + flags( 2 ) + pivot( 12 ) + stringlength\n\n    // if we have not reached then end of the layer block, there must be a parent defined\n    this.currentLayer.parent = parsedLength < length ? this.reader.getUint16() : -1 // omitted or -1 for no parent\n  }\n\n  // VEC12 * ( F4 + F4 + F4 ) array of x,y,z vectors\n  // Converting from left to right handed coordinate system:\n  // x -> -x and switch material FrontSide -> BackSide\n  parsePoints(length) {\n    this.currentPoints = []\n    for (var i = 0; i < length / 4; i += 3) {\n      // z -> -z to match three.js right handed coords\n      this.currentPoints.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32())\n    }\n  }\n\n  // parse VMAP or VMAD\n  // Associates a set of floating-point vectors with a set of points.\n  // VMAP: { type[ID4], dimension[U2], name[S0], ( vert[VX], value[F4] # dimension ) * }\n\n  // VMAD Associates a set of floating-point vectors with the vertices of specific polygons.\n  // Similar to VMAP UVs, but associates with polygon vertices rather than points\n  // to solve to problem of UV seams:  VMAD chunks are paired with VMAPs of the same name,\n  // if they exist. The vector values in the VMAD will then replace those in the\n  // corresponding VMAP, but only for calculations involving the specified polygons.\n  // VMAD { type[ID4], dimension[U2], name[S0], ( vert[VX], poly[VX], value[F4] # dimension ) * }\n  parseVertexMapping(length, discontinuous) {\n    var finalOffset = this.reader.offset + length\n\n    var channelName = this.reader.getString()\n\n    if (this.reader.offset === finalOffset) {\n      // then we are in a texture node and the VMAP chunk is just a reference to a UV channel name\n      this.currentForm.UVChannel = channelName\n      return\n    }\n\n    // otherwise reset to initial length and parse normal VMAP CHUNK\n    this.reader.setOffset(this.reader.offset - stringOffset(channelName))\n\n    var type = this.reader.getIDTag()\n\n    this.reader.getUint16() // dimension\n    var name = this.reader.getString()\n\n    var remainingLength = length - 6 - stringOffset(name)\n\n    switch (type) {\n      case 'TXUV':\n        this.parseUVMapping(name, finalOffset, discontinuous)\n        break\n      case 'MORF':\n      case 'SPOT':\n        this.parseMorphTargets(name, finalOffset, type) // can't be discontinuous\n        break\n      // unsupported VMAPs\n      case 'APSL':\n      case 'NORM':\n      case 'WGHT':\n      case 'MNVW':\n      case 'PICK':\n      case 'RGB ':\n      case 'RGBA':\n        this.reader.skip(remainingLength)\n        break\n      default:\n        console.warn('LWOLoader: unknown vertex map type: ' + type)\n        this.reader.skip(remainingLength)\n    }\n  }\n\n  parseUVMapping(name, finalOffset, discontinuous) {\n    var uvIndices = []\n    var polyIndices = []\n    var uvs = []\n\n    while (this.reader.offset < finalOffset) {\n      uvIndices.push(this.reader.getVariableLengthIndex())\n\n      if (discontinuous) polyIndices.push(this.reader.getVariableLengthIndex())\n\n      uvs.push(this.reader.getFloat32(), this.reader.getFloat32())\n    }\n\n    if (discontinuous) {\n      if (!this.currentLayer.discontinuousUVs) this.currentLayer.discontinuousUVs = {}\n\n      this.currentLayer.discontinuousUVs[name] = {\n        uvIndices: uvIndices,\n        polyIndices: polyIndices,\n        uvs: uvs,\n      }\n    } else {\n      if (!this.currentLayer.uvs) this.currentLayer.uvs = {}\n\n      this.currentLayer.uvs[name] = {\n        uvIndices: uvIndices,\n        uvs: uvs,\n      }\n    }\n  }\n\n  parseMorphTargets(name, finalOffset, type) {\n    var indices = []\n    var points = []\n\n    type = type === 'MORF' ? 'relative' : 'absolute'\n\n    while (this.reader.offset < finalOffset) {\n      indices.push(this.reader.getVariableLengthIndex())\n      // z -> -z to match three.js right handed coords\n      points.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32())\n    }\n\n    if (!this.currentLayer.morphTargets) this.currentLayer.morphTargets = {}\n\n    this.currentLayer.morphTargets[name] = {\n      indices: indices,\n      points: points,\n      type: type,\n    }\n  }\n\n  // A list of polygons for the current layer.\n  // POLS { type[ID4], ( numvert+flags[U2], vert[VX] # numvert ) * }\n  parsePolygonList(length) {\n    var finalOffset = this.reader.offset + length\n    var type = this.reader.getIDTag()\n\n    var indices = []\n\n    // hold a list of polygon sizes, to be split up later\n    var polygonDimensions = []\n\n    while (this.reader.offset < finalOffset) {\n      var numverts = this.reader.getUint16()\n\n      //var flags = numverts & 64512; // 6 high order bits are flags - ignoring for now\n      numverts = numverts & 1023 // remaining ten low order bits are vertex num\n      polygonDimensions.push(numverts)\n\n      for (var j = 0; j < numverts; j++) indices.push(this.reader.getVariableLengthIndex())\n    }\n\n    var geometryData = {\n      type: type,\n      vertexIndices: indices,\n      polygonDimensions: polygonDimensions,\n      points: this.currentPoints,\n    }\n\n    // Note: assuming that all polys will be lines or points if the first is\n    if (polygonDimensions[0] === 1) geometryData.type = 'points'\n    else if (polygonDimensions[0] === 2) geometryData.type = 'lines'\n\n    this.currentLayer.geometry = geometryData\n  }\n\n  // Lists the tag strings that can be associated with polygons by the PTAG chunk.\n  // TAGS { tag-string[S0] * }\n  parseTagStrings(length) {\n    this.tree.tags = this.reader.getStringArray(length)\n  }\n\n  // Associates tags of a given type with polygons in the most recent POLS chunk.\n  // PTAG { type[ID4], ( poly[VX], tag[U2] ) * }\n  parsePolygonTagMapping(length) {\n    var finalOffset = this.reader.offset + length\n    var type = this.reader.getIDTag()\n    if (type === 'SURF') this.parseMaterialIndices(finalOffset)\n    else {\n      //PART, SMGP, COLR not supported\n\n      this.reader.skip(length - 4)\n    }\n  }\n\n  parseMaterialIndices(finalOffset) {\n    // array holds polygon index followed by material index\n    this.currentLayer.geometry.materialIndices = []\n\n    while (this.reader.offset < finalOffset) {\n      var polygonIndex = this.reader.getVariableLengthIndex()\n      var materialIndex = this.reader.getUint16()\n\n      this.currentLayer.geometry.materialIndices.push(polygonIndex, materialIndex)\n    }\n  }\n\n  parseUnknownCHUNK(blockID, length) {\n    console.warn('LWOLoader: unknown chunk type: ' + blockID + ' length: ' + length)\n\n    // print the chunk plus some bytes padding either side\n    // printBuffer( this.reader.dv.buffer, this.reader.offset - 20, length + 40 );\n\n    var data = this.reader.getString(length)\n\n    this.currentForm[blockID] = data\n  }\n}\n\nclass DataViewReader {\n  constructor(buffer) {\n    this.dv = new DataView(buffer)\n    this.offset = 0\n    this._textDecoder = new TextDecoder()\n    this._bytes = new Uint8Array(buffer)\n  }\n\n  size() {\n    return this.dv.buffer.byteLength\n  }\n\n  setOffset(offset) {\n    if (offset > 0 && offset < this.dv.buffer.byteLength) {\n      this.offset = offset\n    } else {\n      console.error('LWOLoader: invalid buffer offset')\n    }\n  }\n\n  endOfFile() {\n    if (this.offset >= this.size()) return true\n    return false\n  }\n\n  skip(length) {\n    this.offset += length\n  }\n\n  getUint8() {\n    var value = this.dv.getUint8(this.offset)\n    this.offset += 1\n    return value\n  }\n\n  getUint16() {\n    var value = this.dv.getUint16(this.offset)\n    this.offset += 2\n    return value\n  }\n\n  getInt32() {\n    var value = this.dv.getInt32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getUint32() {\n    var value = this.dv.getUint32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getUint64() {\n    var low, high\n\n    high = this.getUint32()\n    low = this.getUint32()\n    return high * 0x100000000 + low\n  }\n\n  getFloat32() {\n    var value = this.dv.getFloat32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getFloat32Array(size) {\n    var a = []\n\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat32())\n    }\n\n    return a\n  }\n\n  getFloat64() {\n    var value = this.dv.getFloat64(this.offset, this.littleEndian)\n    this.offset += 8\n    return value\n  }\n\n  getFloat64Array(size) {\n    var a = []\n\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat64())\n    }\n\n    return a\n  }\n\n  // get variable-length index data type\n  // VX ::= index[U2] | (index + 0xFF000000)[U4]\n  // If the index value is less than 65,280 (0xFF00),then VX === U2\n  // otherwise VX === U4 with bits 24-31 set\n  // When reading an index, if the first byte encountered is 255 (0xFF), then\n  // the four-byte form is being used and the first byte should be discarded or masked out.\n  getVariableLengthIndex() {\n    var firstByte = this.getUint8()\n\n    if (firstByte === 255) {\n      return this.getUint8() * 65536 + this.getUint8() * 256 + this.getUint8()\n    }\n\n    return firstByte * 256 + this.getUint8()\n  }\n\n  // An ID tag is a sequence of 4 bytes containing 7-bit ASCII values\n  getIDTag() {\n    return this.getString(4)\n  }\n\n  getString(size) {\n    if (size === 0) return\n\n    const start = this.offset\n\n    let result\n    let length\n\n    if (size) {\n      length = size\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, size))\n    } else {\n      // use 1:1 mapping of buffer to avoid redundant new array creation.\n      length = this._bytes.indexOf(0, start) - start\n\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, length))\n\n      // account for null byte in length\n      length++\n\n      // if string with terminating nullbyte is uneven, extra nullbyte is added, skip that too\n      length += length % 2\n    }\n\n    this.skip(length)\n\n    return result\n  }\n\n  getStringArray(size) {\n    var a = this.getString(size)\n    a = a.split('\\0')\n\n    return a.filter(Boolean) // return array with any empty strings removed\n  }\n}\n\n// ************** DEBUGGER  **************\n\nclass Debugger {\n  constructor() {\n    this.active = false\n    this.depth = 0\n    this.formList = []\n  }\n\n  enable() {\n    this.active = true\n  }\n\n  log() {\n    if (!this.active) return\n\n    var nodeType\n\n    switch (this.node) {\n      case 0:\n        nodeType = 'FORM'\n        break\n\n      case 1:\n        nodeType = 'CHK'\n        break\n\n      case 2:\n        nodeType = 'S-CHK'\n        break\n    }\n\n    console.log(\n      '| '.repeat(this.depth) + nodeType,\n      this.nodeID,\n      `( ${this.offset} ) -> ( ${this.dataOffset + this.length} )`,\n      this.node == 0 ? ' {' : '',\n      this.skipped ? 'SKIPPED' : '',\n      this.node == 0 && this.skipped ? '}' : '',\n    )\n\n    if (this.node == 0 && !this.skipped) {\n      this.depth += 1\n      this.formList.push(this.dataOffset + this.length)\n    }\n\n    this.skipped = false\n  }\n\n  closeForms() {\n    if (!this.active) return\n\n    for (var i = this.formList.length - 1; i >= 0; i--) {\n      if (this.offset >= this.formList[i]) {\n        this.depth -= 1\n        console.log('| '.repeat(this.depth) + '}')\n        this.formList.splice(-1, 1)\n      }\n    }\n  }\n}\n\n// ************** UTILITY FUNCTIONS **************\n\nfunction isEven(num) {\n  return num % 2\n}\n\n// calculate the length of the string in the buffer\n// this will be string.length + nullbyte + optional padbyte to make the length even\nfunction stringOffset(string) {\n  return string.length + 1 + (isEven(string.length + 1) ? 1 : 0)\n}\n\n// for testing purposes, dump buffer to console\n// printBuffer( this.reader.dv.buffer, this.reader.offset, length );\nfunction printBuffer(buffer, from, to) {\n  console.log(new TextDecoder().decode(new Uint8Array(buffer, from, to)))\n}\n\nexport { IFFParser }\n"], "mappings": ";;AAqCA,MAAMA,SAAA,CAAU;EACdC,YAAA,EAAc;IACZ,KAAKC,QAAA,GAAW,IAAIC,QAAA,CAAU;EAE/B;EAEDC,MAAMC,MAAA,EAAQ;IACZ,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAeF,MAAM;IAEvC,KAAKG,IAAA,GAAO;MACVC,SAAA,EAAW,CAAE;MACbC,MAAA,EAAQ,EAAE;MACVC,IAAA,EAAM,EAAE;MACRC,QAAA,EAAU;IACX;IAGD,KAAKC,YAAA,GAAe,KAAKL,IAAA;IACzB,KAAKM,WAAA,GAAc,KAAKN,IAAA;IAExB,KAAKO,YAAA,CAAc;IAEnB,IAAI,KAAKP,IAAA,CAAKQ,MAAA,KAAW,QAAW;IAEpC,IAAI,KAAKR,IAAA,CAAKQ,MAAA,KAAW,QAAQ;MAC/B,KAAKC,MAAA,GAAS,IAAIC,UAAA,CAAW,IAAI;MACjC,OAAO,CAAC,KAAKZ,MAAA,CAAOa,SAAA,CAAW,GAAE,KAAKF,MAAA,CAAOG,UAAA,CAAY;IAC1D,WAAU,KAAKZ,IAAA,CAAKQ,MAAA,KAAW,QAAQ;MACtC,KAAKC,MAAA,GAAS,IAAII,UAAA,CAAW,IAAI;MACjC,OAAO,CAAC,KAAKf,MAAA,CAAOa,SAAA,CAAW,GAAE,KAAKF,MAAA,CAAOG,UAAA,CAAY;IAC1D;IAED,KAAKlB,QAAA,CAASoB,MAAA,GAAS,KAAKhB,MAAA,CAAOgB,MAAA;IACnC,KAAKpB,QAAA,CAASqB,UAAA,CAAY;IAE1B,OAAO,KAAKf,IAAA;EACb;EAEDO,aAAA,EAAe;IACb,KAAKb,QAAA,CAASoB,MAAA,GAAS,KAAKhB,MAAA,CAAOgB,MAAA;IAEnC,IAAIE,OAAA,GAAU,KAAKlB,MAAA,CAAOmB,QAAA,CAAU;IAEpC,IAAID,OAAA,KAAY,QAAQ;MACtBE,OAAA,CAAQC,IAAA,CAAK,oCAAoC;MACjD;IACD;IAED,IAAIC,MAAA,GAAS,KAAKtB,MAAA,CAAOuB,SAAA,CAAW;IAEpC,KAAK3B,QAAA,CAAS4B,UAAA,GAAa,KAAKxB,MAAA,CAAOgB,MAAA;IACvC,KAAKpB,QAAA,CAAS0B,MAAA,GAASA,MAAA;IAEvB,IAAIG,IAAA,GAAO,KAAKzB,MAAA,CAAOmB,QAAA,CAAU;IAEjC,IAAIM,IAAA,KAAS,QAAQ;MACnB,KAAKvB,IAAA,CAAKQ,MAAA,GAASe,IAAA;IACzB,WAAeA,IAAA,KAAS,QAAQ;MAC1B,KAAKvB,IAAA,CAAKQ,MAAA,GAASe,IAAA;IACpB;IAED,KAAK7B,QAAA,CAAS8B,IAAA,GAAO;IACrB,KAAK9B,QAAA,CAAS+B,MAAA,GAASF,IAAA;IACvB,KAAK7B,QAAA,CAASgC,GAAA,CAAK;IAEnB;EACD;EAAA;EAAA;EAAA;EAAA;EAAA;EAQDC,UAAUP,MAAA,EAAQ;IAChB,IAAIG,IAAA,GAAO,KAAKzB,MAAA,CAAOmB,QAAA,CAAU;IAEjC,QAAQM,IAAA;MAIN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAK7B,QAAA,CAASkC,OAAA,GAAU;QACxB,KAAKC,QAAA,CAAST,MAAM;QACpB;MAIF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MAGL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QAEH,KAAK1B,QAAA,CAAS0B,MAAA,GAAS;QACvB,KAAK1B,QAAA,CAASkC,OAAA,GAAU;QACxB;MAEF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKE,yBAAA,CAA0BP,IAAI;QACnC;MAEF,KAAK;QACH,KAAKQ,aAAA,CAAcX,MAAM;QACzB;MAIF,KAAK;QACH,IAAI,KAAKpB,IAAA,CAAKQ,MAAA,KAAW,QAAQ;UAC/B,KAAKmB,SAAA,CAAUP,MAAM;QAC/B,OAAe;UACL,KAAKY,SAAA,CAAUZ,MAAM;QACtB;QAED;MAEF,KAAK;QACH,KAAKa,UAAA,CAAY;QACjB;MAEF,KAAK;QACH,KAAKnC,MAAA,CAAOoC,IAAA,CAAK,CAAC;QAClB,KAAK5B,WAAA,CAAY6B,gBAAA,GAAmB;UAClCC,KAAA,EAAO,KAAKtC,MAAA,CAAOuB,SAAA,CAAW;UAC9BgB,OAAA,EAAS,KAAKvC,MAAA,CAAOwC,SAAA,CAAW;UAAA;QACjC;QACD;MAIF,KAAK;QACH,KAAKC,mBAAA,CAAoBnB,MAAM;QAC/B;MAIF,KAAK;QACH,KAAKoB,gBAAA,CAAiBpB,MAAM;QAC5B;MAEF,KAAK;QACH,KAAKqB,cAAA,CAAerB,MAAM;QAC1B;MAEF,KAAK;QACH,KAAKsB,YAAA,CAAatB,MAAM;QACxB;MAEF,KAAK;MACL,KAAK;QACH,KAAKuB,SAAA,CAAU,cAAcvB,MAAM;QACnC;MAEF,KAAK;QACH,KAAKwB,gBAAA,CAAiBxB,MAAM;QAC5B;MAEF,KAAK;QACH,KAAKyB,UAAA,GAAa,KAAKvC,WAAA;QACvB,KAAKA,WAAA,GAAc,KAAKwC,cAAA;QACxB,KAAKH,SAAA,CAAU,iBAAiBvB,MAAM;QACtC;MAEF,KAAK;QACH,KAAKuB,SAAA,CAAU,qBAAqBvB,MAAM;QAC1C;MAEF,KAAK;QACH,KAAK2B,cAAA,CAAe3B,MAAM;QAC1B;MAIF,KAAK;QACH,KAAK4B,aAAA,CAAc5B,MAAM;QACzB;MAEF,KAAK;QACH,KAAK6B,SAAA,CAAU,aAAa7B,MAAM;QAClC;MAIF,KAAK;QACH,KAAKuB,SAAA,CAAU,cAAcvB,MAAM;QACnC;MAEF,KAAK;QACH,KAAK8B,UAAA,CAAW,UAAU9B,MAAM;QAChC;MAEF,KAAK;QACH,KAAK8B,UAAA,CAAW,SAAS9B,MAAM;QAC/B;MAEF,KAAK;QACH,KAAK8B,UAAA,CAAW,YAAY9B,MAAM;QAClC;MAEF;QACE,KAAK+B,gBAAA,CAAiB5B,IAAA,EAAMH,MAAM;IACrC;IAED,KAAK1B,QAAA,CAAS8B,IAAA,GAAO;IACrB,KAAK9B,QAAA,CAAS+B,MAAA,GAASF,IAAA;IACvB,KAAK7B,QAAA,CAASgC,GAAA,CAAK;EACpB;EAEDiB,UAAUpB,IAAA,EAAMH,MAAA,EAAQ;IACtB,IAAI,CAAC,KAAKd,WAAA,EAAa,KAAKA,WAAA,GAAc,KAAK8C,WAAA;IAE/C,KAAKC,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;IAC3C,KAAKyB,UAAA,GAAa,KAAKvC,WAAA;IAEvB,IAAI,CAAC,KAAKA,WAAA,CAAYiB,IAAI,GAAG;MAC3B,KAAKjB,WAAA,CAAYiB,IAAI,IAAI,CAAE;MAC3B,KAAKjB,WAAA,GAAc,KAAKA,WAAA,CAAYiB,IAAI;IAC9C,OAAW;MAELL,OAAA,CAAQC,IAAA,CAAK,8CAA8CI,IAAA,EAAM,KAAKjB,WAAW;MAEjF,KAAKA,WAAA,GAAc,KAAKA,WAAA,CAAYiB,IAAI;IACzC;EACF;EAEDM,SAAST,MAAA,EAAQ;IACf,KAAKtB,MAAA,CAAOoC,IAAA,CAAKd,MAAA,GAAS,CAAC;EAC5B;EAED+B,iBAAiB5B,IAAA,EAAMH,MAAA,EAAQ;IAC7BF,OAAA,CAAQC,IAAA,CAAK,0CAA0CI,IAAA,EAAMH,MAAM;IAEnEkC,WAAA,CAAY,KAAKxD,MAAA,CAAOyD,EAAA,CAAG1D,MAAA,EAAQ,KAAKC,MAAA,CAAOgB,MAAA,EAAQM,MAAA,GAAS,CAAC;IACjE,KAAKtB,MAAA,CAAOoC,IAAA,CAAKd,MAAA,GAAS,CAAC;EAC5B;EAEDoB,iBAAiBpB,MAAA,EAAQ;IACvB,KAAKtB,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,IAAIsB,IAAA,GAAO,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAElC,IAAImB,OAAA,GAAU;MACZC,UAAA,EAAY,CAAE;MAAA;MACdC,WAAA,EAAa,CAAE;MACfH,IAAA;MACAI,SAAA,EAAWJ,IAAA;MACXK,KAAA,EAAO,CAAE;MACTC,MAAA,EAAQ,KAAKhE,MAAA,CAAOwC,SAAA,CAAW;IAChC;IAED,KAAKtC,IAAA,CAAKC,SAAA,CAAUuD,IAAI,IAAIC,OAAA;IAC5B,KAAKX,cAAA,GAAiBW,OAAA;IAEtB,KAAKZ,UAAA,GAAa,KAAK7C,IAAA,CAAKC,SAAA;IAC5B,KAAKK,WAAA,GAAcmD,OAAA;IACnB,KAAKJ,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;EAC5C;EAED2C,iBAAiB3C,MAAA,EAAQ;IACvB,IAAIoC,IAAA,GAAO,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAElC,IAAImB,OAAA,GAAU;MACZC,UAAA,EAAY,CAAE;MAAA;MACdC,WAAA,EAAa,CAAE;MACfH,IAAA;MACAK,KAAA,EAAO,CAAE;MACTC,MAAA,EAAQ,KAAKhE,MAAA,CAAOwC,SAAA,CAAW;IAChC;IAED,KAAKtC,IAAA,CAAKC,SAAA,CAAUuD,IAAI,IAAIC,OAAA;IAC5B,KAAKX,cAAA,GAAiBW,OAAA;IAEtB,KAAKZ,UAAA,GAAa,KAAK7C,IAAA,CAAKC,SAAA;IAC5B,KAAKK,WAAA,GAAcmD,OAAA;IACnB,KAAKJ,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;EAC5C;EAEDsB,aAAatB,MAAA,EAAQ;IAKnB,KAAKtB,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAClB,IAAIsB,IAAA,GAAO,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAElC,IAAId,IAAA,GAAO;MACTgC;IACD;IACD,KAAKlD,WAAA,GAAckB,IAAA;IACnB,KAAK4B,WAAA,GAAc5B,IAAA;IAEnB,KAAK6B,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;EAC5C;EAAA;EAGDwB,iBAAiBxB,MAAA,EAAQ;IACvB,KAAKiC,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;IAC3C,KAAKyB,UAAA,GAAa,KAAKvC,WAAA;IAEvB,KAAKA,WAAA,GAAc,KAAKwC,cAAA,CAAea,WAAA;EACxC;EAAA;EAGDZ,eAAe3B,MAAA,EAAQ;IACrB,KAAKtB,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAClB,IAAIsB,IAAA,GAAO,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAClC,KAAKhC,WAAA,GAAc,KAAK8C,WAAA,CAAYM,UAAA;IAEpC,KAAKf,SAAA,CAAUa,IAAA,EAAMpC,MAAM;EAC5B;EAAA;EAAA;EAIDqB,eAAA,EAAiB;IACf,KAAK3C,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,IAAI8B,SAAA,GAAY,KAAKlE,MAAA,CAAOwC,SAAA,CAAW;IAEvC,IAAI0B,SAAA,KAAc,UAAU;MAC1B,KAAK1D,WAAA,CAAY2D,KAAA,GAAQ,KAAKnE,MAAA,CAAOoE,SAAA,CAAW;IACtD,WAAeF,SAAA,KAAc,OAAO;MAC9B,KAAK1D,WAAA,CAAY2D,KAAA,GAAQ,KAAKnE,MAAA,CAAOuB,SAAA,CAAW;IACtD,WAAe2C,SAAA,KAAc,UAAU;MACjC,KAAKlE,MAAA,CAAOoC,IAAA,CAAK,EAAE;MACnB,KAAK5B,WAAA,CAAY2D,KAAA,GAAQ,KAAKnE,MAAA,CAAOqE,UAAA,CAAY;IACvD,WAAeH,SAAA,KAAc,WAAW;MAClC,KAAKlE,MAAA,CAAOoC,IAAA,CAAK,EAAE;MACnB,KAAK5B,WAAA,CAAY2D,KAAA,GAAQ,KAAKnE,MAAA,CAAOsE,eAAA,CAAgB,CAAC;IACvD;EACF;EAAA;EAAA;EAID7B,oBAAA,EAAsB;IACpB,KAAKzC,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,KAAK5B,WAAA,CAAY+D,WAAA,GAAc,KAAKvE,MAAA,CAAOwE,UAAA,CAAY;EACxD;EAAA;EAGDtB,cAAc5B,MAAA,EAAQ;IACpB,KAAKiC,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;IAC3C,KAAKyB,UAAA,GAAa,KAAKvC,WAAA;IAEvB,IAAI,CAAC,KAAKA,WAAA,CAAYiE,IAAA,EAAM,KAAKjE,WAAA,CAAYiE,IAAA,GAAO,EAAE;IAEtD,IAAIC,GAAA,GAAM,CAAE;IACZ,KAAKlE,WAAA,CAAYiE,IAAA,CAAKE,IAAA,CAAKD,GAAG;IAC9B,KAAKlE,WAAA,GAAckE,GAAA;IAEnB,KAAK1E,MAAA,CAAOoC,IAAA,CAAK,EAAE;EACpB;EAEDJ,0BAA0BP,IAAA,EAAM;IAC9B,KAAKzB,MAAA,CAAOoC,IAAA,CAAK,EAAE;IAEnB,KAAKpC,MAAA,CAAOoC,IAAA,CAAK,EAAE;IAEnB,QAAQX,IAAA;MACN,KAAK;QACH,KAAK6B,WAAA,CAAYsB,KAAA,GAAQ,KAAK5E,MAAA,CAAO6E,eAAA,CAAgB,CAAC;QACtD;MACF,KAAK;QACH,KAAKvB,WAAA,CAAYwB,QAAA,GAAW,KAAK9E,MAAA,CAAO6E,eAAA,CAAgB,CAAC;QACzD;MACF,KAAK;QACH,KAAKvB,WAAA,CAAYyB,QAAA,GAAW,KAAK/E,MAAA,CAAO6E,eAAA,CAAgB,CAAC;QACzD;MACF,KAAK;QACH,KAAKvB,WAAA,CAAY0B,OAAA,GAAU,KAAKhF,MAAA,CAAO6E,eAAA,CAAgB,CAAC;QACxD;MAEF,KAAK;QACH,KAAKvB,WAAA,CAAY2B,SAAA,GAAY,KAAKjF,MAAA,CAAOwE,UAAA,CAAY;QACrD;MACF,KAAK;QACH,KAAKlB,WAAA,CAAY4B,MAAA,GAAS,KAAKlF,MAAA,CAAOwE,UAAA,CAAY;QAClD;MACF,KAAK;QACH,KAAKlB,WAAA,CAAY6B,MAAA,GAAS,KAAKnF,MAAA,CAAOwE,UAAA,CAAY;QAClD;IACH;IAED,KAAKxE,MAAA,CAAOoC,IAAA,CAAK,CAAC;EACnB;EAAA;EAGDH,cAAcX,MAAA,EAAQ;IACpB,KAAKtB,MAAA,CAAOoC,IAAA,CAAKd,MAAA,GAAS,CAAC;EAC5B;EAAA;EAAA;EAAA;EAAA;EAAA;EAQDY,UAAUZ,MAAA,EAAQ;IAChB,IAAI8D,GAAA,GAAM,KAAKpF,MAAA,CAAOmB,QAAA,CAAU;IAGhC,IAAIiE,GAAA,KAAQ,QAAQ;MAClB,KAAKpF,MAAA,CAAOoC,IAAA,CAAK,EAAE;MAEnB,KAAKkB,WAAA,CAAY+B,QAAA,GAAW,KAAKrF,MAAA,CAAOwC,SAAA,CAAW;MAEnD;IACD;IAGD,KAAKxC,MAAA,CAAOsF,SAAA,CAAU,KAAKtF,MAAA,CAAOgB,MAAA,GAAS,CAAC;IAE5C,KAAKuC,cAAA,GAAiB,KAAKvD,MAAA,CAAOgB,MAAA,GAASM,MAAA;IAC3C,KAAKyB,UAAA,GAAa,KAAKvC,WAAA;IAEvB,KAAKR,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,IAAImD,OAAA,GAAU;MACZjD,KAAA,EAAO,KAAKtC,MAAA,CAAOuB,SAAA,CAAW;IAC/B;IACD,KAAKrB,IAAA,CAAKI,QAAA,CAASqE,IAAA,CAAKY,OAAO;IAC/B,KAAK/E,WAAA,GAAc+E,OAAA;EACpB;EAEDC,cAAclE,MAAA,EAAQ;IACpB,IAAIiE,OAAA,GAAU;MACZjD,KAAA,EAAO,KAAKtC,MAAA,CAAOuB,SAAA,CAAW;MAC9B8D,QAAA,EAAU;IACX;IAGD,OAAO,MAAM;MACX,IAAID,GAAA,GAAM,KAAKpF,MAAA,CAAOmB,QAAA,CAAU;MAChC,IAAIsE,QAAA,GAAW,KAAKzF,MAAA,CAAO0F,SAAA,CAAW;MACtC,IAAIN,GAAA,KAAQ,QAAQ;QAClBG,OAAA,CAAQF,QAAA,GAAW,KAAKrF,MAAA,CAAOwC,SAAA,CAAW;QAC1C;MACD;MAED,IAAIiD,QAAA,IAAYnE,MAAA,EAAQ;QACtB;MACD;IACF;IAED,KAAKpB,IAAA,CAAKI,QAAA,CAASqE,IAAA,CAAKY,OAAO;IAC/B,KAAK/E,WAAA,GAAc+E,OAAA;EACpB;EAEDpD,WAAA,EAAa;IACX,KAAKnC,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAClB,KAAK5B,WAAA,CAAY6E,QAAA,GAAW,KAAKrF,MAAA,CAAOwC,SAAA,CAAW;EACpD;EAEDW,UAAU1B,IAAA,EAAMH,MAAA,EAAQ;IACtB,IAAIqE,SAAA,GAAY,KAAK3F,MAAA,CAAOgB,MAAA,GAASM,MAAA,GAAS;IAC9C,KAAKtB,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,KAAK5B,WAAA,CAAYiB,IAAI,IAAI,KAAKzB,MAAA,CAAOwE,UAAA,CAAY;IAEjD,KAAKxE,MAAA,CAAOsF,SAAA,CAAUK,SAAS;EAChC;EAEDvC,WAAW3B,IAAA,EAAMH,MAAA,EAAQ;IACvB,IAAIqE,SAAA,GAAY,KAAK3F,MAAA,CAAOgB,MAAA,GAASM,MAAA,GAAS;IAC9C,KAAKtB,MAAA,CAAOoC,IAAA,CAAK,CAAC;IAElB,KAAK5B,WAAA,CAAYiB,IAAI,IAAI;MACvBmE,CAAA,EAAG,KAAK5F,MAAA,CAAOwE,UAAA,CAAY;MAC3BqB,CAAA,EAAG,KAAK7F,MAAA,CAAOwE,UAAA,CAAY;MAC3BsB,CAAA,EAAG,KAAK9F,MAAA,CAAOwE,UAAA,CAAY;IAC5B;IAED,KAAKxE,MAAA,CAAOsF,SAAA,CAAUK,SAAS;EAChC;EAAA;EAAA;EAIDI,eAAA,EAAiB;IACf,IAAI,CAAC,KAAK7F,IAAA,CAAK8F,UAAA,EAAY,KAAK9F,IAAA,CAAK8F,UAAA,GAAa,CAAE;IAEpD,KAAK9F,IAAA,CAAK8F,UAAA,CAAW,KAAKhG,MAAA,CAAOmB,QAAA,CAAQ,CAAE,IAAI;MAC7C8E,SAAA,EAAW,KAAKjG,MAAA,CAAOwC,SAAA,CAAW;IACnC;EACF;EAAA;EAAA;EAID0D,WAAW5E,MAAA,EAAQ;IACjB,IAAI6E,KAAA,GAAQ;MACVC,MAAA,EAAQ,KAAKpG,MAAA,CAAO0F,SAAA,CAAW;MAC/BW,KAAA,EAAO,KAAKrG,MAAA,CAAO0F,SAAA,CAAW;MAAA;MAC9BY,KAAA,EAAO,KAAKtG,MAAA,CAAO6E,eAAA,CAAgB,CAAC;MAAA;MACpCnB,IAAA,EAAM,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAC9B;IAED,KAAKtC,IAAA,CAAKE,MAAA,CAAOuE,IAAA,CAAKwB,KAAK;IAC3B,KAAK5F,YAAA,GAAe4F,KAAA;IAEpB,IAAII,YAAA,GAAe,KAAKC,YAAA,CAAa,KAAKjG,YAAA,CAAamD,IAAI;IAG3D,KAAKnD,YAAA,CAAakG,MAAA,GAASF,YAAA,GAAejF,MAAA,GAAS,KAAKtB,MAAA,CAAO0F,SAAA,CAAW,IAAG;EAC9E;EAAA;EAAA;EAAA;EAKDgB,YAAYpF,MAAA,EAAQ;IAClB,KAAKqF,aAAA,GAAgB,EAAE;IACvB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAItF,MAAA,GAAS,GAAGsF,CAAA,IAAK,GAAG;MAEtC,KAAKD,aAAA,CAAchC,IAAA,CAAK,KAAK3E,MAAA,CAAOwE,UAAA,CAAY,GAAE,KAAKxE,MAAA,CAAOwE,UAAA,CAAU,GAAI,CAAC,KAAKxE,MAAA,CAAOwE,UAAA,CAAU,CAAE;IACtG;EACF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAYDqC,mBAAmBvF,MAAA,EAAQwF,aAAA,EAAe;IACxC,IAAIC,WAAA,GAAc,KAAK/G,MAAA,CAAOgB,MAAA,GAASM,MAAA;IAEvC,IAAI0F,WAAA,GAAc,KAAKhH,MAAA,CAAOwC,SAAA,CAAW;IAEzC,IAAI,KAAKxC,MAAA,CAAOgB,MAAA,KAAW+F,WAAA,EAAa;MAEtC,KAAKvG,WAAA,CAAYyG,SAAA,GAAYD,WAAA;MAC7B;IACD;IAGD,KAAKhH,MAAA,CAAOsF,SAAA,CAAU,KAAKtF,MAAA,CAAOgB,MAAA,GAASwF,YAAA,CAAaQ,WAAW,CAAC;IAEpE,IAAIvF,IAAA,GAAO,KAAKzB,MAAA,CAAOmB,QAAA,CAAU;IAEjC,KAAKnB,MAAA,CAAO0F,SAAA,CAAW;IACvB,IAAIhC,IAAA,GAAO,KAAK1D,MAAA,CAAOwC,SAAA,CAAW;IAElC,IAAI0E,eAAA,GAAkB5F,MAAA,GAAS,IAAIkF,YAAA,CAAa9C,IAAI;IAEpD,QAAQjC,IAAA;MACN,KAAK;QACH,KAAK0F,cAAA,CAAezD,IAAA,EAAMqD,WAAA,EAAaD,aAAa;QACpD;MACF,KAAK;MACL,KAAK;QACH,KAAKM,iBAAA,CAAkB1D,IAAA,EAAMqD,WAAA,EAAatF,IAAI;QAC9C;MAEF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;QACH,KAAKzB,MAAA,CAAOoC,IAAA,CAAK8E,eAAe;QAChC;MACF;QACE9F,OAAA,CAAQC,IAAA,CAAK,yCAAyCI,IAAI;QAC1D,KAAKzB,MAAA,CAAOoC,IAAA,CAAK8E,eAAe;IACnC;EACF;EAEDC,eAAezD,IAAA,EAAMqD,WAAA,EAAaD,aAAA,EAAe;IAC/C,IAAIO,SAAA,GAAY,EAAE;IAClB,IAAIC,WAAA,GAAc,EAAE;IACpB,IAAIC,GAAA,GAAM,EAAE;IAEZ,OAAO,KAAKvH,MAAA,CAAOgB,MAAA,GAAS+F,WAAA,EAAa;MACvCM,SAAA,CAAU1C,IAAA,CAAK,KAAK3E,MAAA,CAAOwH,sBAAA,CAAsB,CAAE;MAEnD,IAAIV,aAAA,EAAeQ,WAAA,CAAY3C,IAAA,CAAK,KAAK3E,MAAA,CAAOwH,sBAAA,EAAwB;MAExED,GAAA,CAAI5C,IAAA,CAAK,KAAK3E,MAAA,CAAOwE,UAAA,CAAU,GAAI,KAAKxE,MAAA,CAAOwE,UAAA,EAAY;IAC5D;IAED,IAAIsC,aAAA,EAAe;MACjB,IAAI,CAAC,KAAKvG,YAAA,CAAakH,gBAAA,EAAkB,KAAKlH,YAAA,CAAakH,gBAAA,GAAmB,CAAE;MAEhF,KAAKlH,YAAA,CAAakH,gBAAA,CAAiB/D,IAAI,IAAI;QACzC2D,SAAA;QACAC,WAAA;QACAC;MACD;IACP,OAAW;MACL,IAAI,CAAC,KAAKhH,YAAA,CAAagH,GAAA,EAAK,KAAKhH,YAAA,CAAagH,GAAA,GAAM,CAAE;MAEtD,KAAKhH,YAAA,CAAagH,GAAA,CAAI7D,IAAI,IAAI;QAC5B2D,SAAA;QACAE;MACD;IACF;EACF;EAEDH,kBAAkB1D,IAAA,EAAMqD,WAAA,EAAatF,IAAA,EAAM;IACzC,IAAIiG,OAAA,GAAU,EAAE;IAChB,IAAIC,MAAA,GAAS,EAAE;IAEflG,IAAA,GAAOA,IAAA,KAAS,SAAS,aAAa;IAEtC,OAAO,KAAKzB,MAAA,CAAOgB,MAAA,GAAS+F,WAAA,EAAa;MACvCW,OAAA,CAAQ/C,IAAA,CAAK,KAAK3E,MAAA,CAAOwH,sBAAA,CAAsB,CAAE;MAEjDG,MAAA,CAAOhD,IAAA,CAAK,KAAK3E,MAAA,CAAOwE,UAAA,CAAU,GAAI,KAAKxE,MAAA,CAAOwE,UAAA,CAAU,GAAI,CAAC,KAAKxE,MAAA,CAAOwE,UAAA,CAAU,CAAE;IAC1F;IAED,IAAI,CAAC,KAAKjE,YAAA,CAAaqH,YAAA,EAAc,KAAKrH,YAAA,CAAaqH,YAAA,GAAe,CAAE;IAExE,KAAKrH,YAAA,CAAaqH,YAAA,CAAalE,IAAI,IAAI;MACrCgE,OAAA;MACAC,MAAA;MACAlG;IACD;EACF;EAAA;EAAA;EAIDoG,iBAAiBvG,MAAA,EAAQ;IACvB,IAAIyF,WAAA,GAAc,KAAK/G,MAAA,CAAOgB,MAAA,GAASM,MAAA;IACvC,IAAIG,IAAA,GAAO,KAAKzB,MAAA,CAAOmB,QAAA,CAAU;IAEjC,IAAIuG,OAAA,GAAU,EAAE;IAGhB,IAAII,iBAAA,GAAoB,EAAE;IAE1B,OAAO,KAAK9H,MAAA,CAAOgB,MAAA,GAAS+F,WAAA,EAAa;MACvC,IAAIgB,QAAA,GAAW,KAAK/H,MAAA,CAAO0F,SAAA,CAAW;MAGtCqC,QAAA,GAAWA,QAAA,GAAW;MACtBD,iBAAA,CAAkBnD,IAAA,CAAKoD,QAAQ;MAE/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,QAAA,EAAUC,CAAA,IAAKN,OAAA,CAAQ/C,IAAA,CAAK,KAAK3E,MAAA,CAAOwH,sBAAA,CAAsB,CAAE;IACrF;IAED,IAAIS,YAAA,GAAe;MACjBxG,IAAA;MACAyG,aAAA,EAAeR,OAAA;MACfI,iBAAA;MACAH,MAAA,EAAQ,KAAKhB;IACd;IAGD,IAAImB,iBAAA,CAAkB,CAAC,MAAM,GAAGG,YAAA,CAAaxG,IAAA,GAAO,kBAC3CqG,iBAAA,CAAkB,CAAC,MAAM,GAAGG,YAAA,CAAaxG,IAAA,GAAO;IAEzD,KAAKlB,YAAA,CAAa4H,QAAA,GAAWF,YAAA;EAC9B;EAAA;EAAA;EAIDG,gBAAgB9G,MAAA,EAAQ;IACtB,KAAKpB,IAAA,CAAKG,IAAA,GAAO,KAAKL,MAAA,CAAOqI,cAAA,CAAe/G,MAAM;EACnD;EAAA;EAAA;EAIDgH,uBAAuBhH,MAAA,EAAQ;IAC7B,IAAIyF,WAAA,GAAc,KAAK/G,MAAA,CAAOgB,MAAA,GAASM,MAAA;IACvC,IAAIG,IAAA,GAAO,KAAKzB,MAAA,CAAOmB,QAAA,CAAU;IACjC,IAAIM,IAAA,KAAS,QAAQ,KAAK8G,oBAAA,CAAqBxB,WAAW,OACrD;MAGH,KAAK/G,MAAA,CAAOoC,IAAA,CAAKd,MAAA,GAAS,CAAC;IAC5B;EACF;EAEDiH,qBAAqBxB,WAAA,EAAa;IAEhC,KAAKxG,YAAA,CAAa4H,QAAA,CAASK,eAAA,GAAkB,EAAE;IAE/C,OAAO,KAAKxI,MAAA,CAAOgB,MAAA,GAAS+F,WAAA,EAAa;MACvC,IAAI0B,YAAA,GAAe,KAAKzI,MAAA,CAAOwH,sBAAA,CAAwB;MACvD,IAAIkB,aAAA,GAAgB,KAAK1I,MAAA,CAAO0F,SAAA,CAAW;MAE3C,KAAKnF,YAAA,CAAa4H,QAAA,CAASK,eAAA,CAAgB7D,IAAA,CAAK8D,YAAA,EAAcC,aAAa;IAC5E;EACF;EAEDC,kBAAkBC,OAAA,EAAStH,MAAA,EAAQ;IACjCF,OAAA,CAAQC,IAAA,CAAK,oCAAoCuH,OAAA,GAAU,cAActH,MAAM;IAK/E,IAAIuH,IAAA,GAAO,KAAK7I,MAAA,CAAOwC,SAAA,CAAUlB,MAAM;IAEvC,KAAKd,WAAA,CAAYoI,OAAO,IAAIC,IAAA;EAC7B;AACH;AAEA,MAAM5I,cAAA,CAAe;EACnBN,YAAYI,MAAA,EAAQ;IAClB,KAAK0D,EAAA,GAAK,IAAIqF,QAAA,CAAS/I,MAAM;IAC7B,KAAKiB,MAAA,GAAS;IACd,KAAK+H,YAAA,GAAe,IAAIC,WAAA,CAAa;IACrC,KAAKC,MAAA,GAAS,IAAIC,UAAA,CAAWnJ,MAAM;EACpC;EAEDoJ,KAAA,EAAO;IACL,OAAO,KAAK1F,EAAA,CAAG1D,MAAA,CAAOqJ,UAAA;EACvB;EAED9D,UAAUtE,MAAA,EAAQ;IAChB,IAAIA,MAAA,GAAS,KAAKA,MAAA,GAAS,KAAKyC,EAAA,CAAG1D,MAAA,CAAOqJ,UAAA,EAAY;MACpD,KAAKpI,MAAA,GAASA,MAAA;IACpB,OAAW;MACLI,OAAA,CAAQiI,KAAA,CAAM,kCAAkC;IACjD;EACF;EAEDxI,UAAA,EAAY;IACV,IAAI,KAAKG,MAAA,IAAU,KAAKmI,IAAA,CAAI,GAAI,OAAO;IACvC,OAAO;EACR;EAED/G,KAAKd,MAAA,EAAQ;IACX,KAAKN,MAAA,IAAUM,MAAA;EAChB;EAEDgI,SAAA,EAAW;IACT,IAAInF,KAAA,GAAQ,KAAKV,EAAA,CAAG6F,QAAA,CAAS,KAAKtI,MAAM;IACxC,KAAKA,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAEDuB,UAAA,EAAY;IACV,IAAIvB,KAAA,GAAQ,KAAKV,EAAA,CAAGiC,SAAA,CAAU,KAAK1E,MAAM;IACzC,KAAKA,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAEDoF,SAAA,EAAW;IACT,IAAIpF,KAAA,GAAQ,KAAKV,EAAA,CAAG8F,QAAA,CAAS,KAAKvI,MAAA,EAAQ,KAAK;IAC/C,KAAKA,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAED5C,UAAA,EAAY;IACV,IAAI4C,KAAA,GAAQ,KAAKV,EAAA,CAAGlC,SAAA,CAAU,KAAKP,MAAA,EAAQ,KAAK;IAChD,KAAKA,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAEDC,UAAA,EAAY;IACV,IAAIoF,GAAA,EAAKC,IAAA;IAETA,IAAA,GAAO,KAAKlI,SAAA,CAAW;IACvBiI,GAAA,GAAM,KAAKjI,SAAA,CAAW;IACtB,OAAOkI,IAAA,GAAO,aAAcD,GAAA;EAC7B;EAEDhF,WAAA,EAAa;IACX,IAAIL,KAAA,GAAQ,KAAKV,EAAA,CAAGe,UAAA,CAAW,KAAKxD,MAAA,EAAQ,KAAK;IACjD,KAAKA,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAEDU,gBAAgBsE,IAAA,EAAM;IACpB,IAAIO,CAAA,GAAI,EAAE;IAEV,SAAS9C,CAAA,GAAI,GAAGA,CAAA,GAAIuC,IAAA,EAAMvC,CAAA,IAAK;MAC7B8C,CAAA,CAAE/E,IAAA,CAAK,KAAKH,UAAA,EAAY;IACzB;IAED,OAAOkF,CAAA;EACR;EAEDrF,WAAA,EAAa;IACX,IAAIF,KAAA,GAAQ,KAAKV,EAAA,CAAGY,UAAA,CAAW,KAAKrD,MAAA,EAAQ,KAAK2I,YAAY;IAC7D,KAAK3I,MAAA,IAAU;IACf,OAAOmD,KAAA;EACR;EAEDG,gBAAgB6E,IAAA,EAAM;IACpB,IAAIO,CAAA,GAAI,EAAE;IAEV,SAAS9C,CAAA,GAAI,GAAGA,CAAA,GAAIuC,IAAA,EAAMvC,CAAA,IAAK;MAC7B8C,CAAA,CAAE/E,IAAA,CAAK,KAAKN,UAAA,EAAY;IACzB;IAED,OAAOqF,CAAA;EACR;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAQDlC,uBAAA,EAAyB;IACvB,IAAIoC,SAAA,GAAY,KAAKN,QAAA,CAAU;IAE/B,IAAIM,SAAA,KAAc,KAAK;MACrB,OAAO,KAAKN,QAAA,CAAU,IAAG,QAAQ,KAAKA,QAAA,KAAa,MAAM,KAAKA,QAAA,CAAU;IACzE;IAED,OAAOM,SAAA,GAAY,MAAM,KAAKN,QAAA,CAAU;EACzC;EAAA;EAGDnI,SAAA,EAAW;IACT,OAAO,KAAKqB,SAAA,CAAU,CAAC;EACxB;EAEDA,UAAU2G,IAAA,EAAM;IACd,IAAIA,IAAA,KAAS,GAAG;IAEhB,MAAMU,KAAA,GAAQ,KAAK7I,MAAA;IAEnB,IAAI8I,MAAA;IACJ,IAAIxI,MAAA;IAEJ,IAAI6H,IAAA,EAAM;MACR7H,MAAA,GAAS6H,IAAA;MACTW,MAAA,GAAS,KAAKf,YAAA,CAAagB,MAAA,CAAO,IAAIb,UAAA,CAAW,KAAKzF,EAAA,CAAG1D,MAAA,EAAQ8J,KAAA,EAAOV,IAAI,CAAC;IACnF,OAAW;MAEL7H,MAAA,GAAS,KAAK2H,MAAA,CAAOe,OAAA,CAAQ,GAAGH,KAAK,IAAIA,KAAA;MAEzCC,MAAA,GAAS,KAAKf,YAAA,CAAagB,MAAA,CAAO,IAAIb,UAAA,CAAW,KAAKzF,EAAA,CAAG1D,MAAA,EAAQ8J,KAAA,EAAOvI,MAAM,CAAC;MAG/EA,MAAA;MAGAA,MAAA,IAAUA,MAAA,GAAS;IACpB;IAED,KAAKc,IAAA,CAAKd,MAAM;IAEhB,OAAOwI,MAAA;EACR;EAEDzB,eAAec,IAAA,EAAM;IACnB,IAAIO,CAAA,GAAI,KAAKlH,SAAA,CAAU2G,IAAI;IAC3BO,CAAA,GAAIA,CAAA,CAAEO,KAAA,CAAM,IAAI;IAEhB,OAAOP,CAAA,CAAEQ,MAAA,CAAOC,OAAO;EACxB;AACH;AAIA,MAAMtK,QAAA,CAAS;EACbF,YAAA,EAAc;IACZ,KAAKyK,MAAA,GAAS;IACd,KAAKC,KAAA,GAAQ;IACb,KAAKC,QAAA,GAAW,EAAE;EACnB;EAEDC,OAAA,EAAS;IACP,KAAKH,MAAA,GAAS;EACf;EAEDxI,IAAA,EAAM;IACJ,IAAI,CAAC,KAAKwI,MAAA,EAAQ;IAElB,IAAII,QAAA;IAEJ,QAAQ,KAAK9I,IAAA;MACX,KAAK;QACH8I,QAAA,GAAW;QACX;MAEF,KAAK;QACHA,QAAA,GAAW;QACX;MAEF,KAAK;QACHA,QAAA,GAAW;QACX;IACH;IAEDpJ,OAAA,CAAQQ,GAAA,CACN,KAAK6I,MAAA,CAAO,KAAKJ,KAAK,IAAIG,QAAA,EAC1B,KAAK7I,MAAA,EACL,KAAK,KAAKX,MAAA,WAAiB,KAAKQ,UAAA,GAAa,KAAKF,MAAA,MAClD,KAAKI,IAAA,IAAQ,IAAI,OAAO,IACxB,KAAKI,OAAA,GAAU,YAAY,IAC3B,KAAKJ,IAAA,IAAQ,KAAK,KAAKI,OAAA,GAAU,MAAM,EACxC;IAED,IAAI,KAAKJ,IAAA,IAAQ,KAAK,CAAC,KAAKI,OAAA,EAAS;MACnC,KAAKuI,KAAA,IAAS;MACd,KAAKC,QAAA,CAAS3F,IAAA,CAAK,KAAKnD,UAAA,GAAa,KAAKF,MAAM;IACjD;IAED,KAAKQ,OAAA,GAAU;EAChB;EAEDb,WAAA,EAAa;IACX,IAAI,CAAC,KAAKmJ,MAAA,EAAQ;IAElB,SAASxD,CAAA,GAAI,KAAK0D,QAAA,CAAShJ,MAAA,GAAS,GAAGsF,CAAA,IAAK,GAAGA,CAAA,IAAK;MAClD,IAAI,KAAK5F,MAAA,IAAU,KAAKsJ,QAAA,CAAS1D,CAAC,GAAG;QACnC,KAAKyD,KAAA,IAAS;QACdjJ,OAAA,CAAQQ,GAAA,CAAI,KAAK6I,MAAA,CAAO,KAAKJ,KAAK,IAAI,GAAG;QACzC,KAAKC,QAAA,CAASI,MAAA,CAAO,IAAI,CAAC;MAC3B;IACF;EACF;AACH;AAIA,SAASC,OAAOC,GAAA,EAAK;EACnB,OAAOA,GAAA,GAAM;AACf;AAIA,SAASpE,aAAaqE,MAAA,EAAQ;EAC5B,OAAOA,MAAA,CAAOvJ,MAAA,GAAS,KAAKqJ,MAAA,CAAOE,MAAA,CAAOvJ,MAAA,GAAS,CAAC,IAAI,IAAI;AAC9D;AAIA,SAASkC,YAAYzD,MAAA,EAAQ+K,IAAA,EAAMC,EAAA,EAAI;EACrC3J,OAAA,CAAQQ,GAAA,CAAI,IAAIoH,WAAA,CAAW,EAAGe,MAAA,CAAO,IAAIb,UAAA,CAAWnJ,MAAA,EAAQ+K,IAAA,EAAMC,EAAE,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}