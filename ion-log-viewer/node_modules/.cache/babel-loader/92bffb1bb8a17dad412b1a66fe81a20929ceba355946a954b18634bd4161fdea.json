{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { MOUSE, Vector3, Vector2, Quaternion } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nclass TrackballControls extends EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"enabled\", true);\n    __publicField(this, \"screen\", {\n      left: 0,\n      top: 0,\n      width: 0,\n      height: 0\n    });\n    __publicField(this, \"rotateSpeed\", 1);\n    __publicField(this, \"zoomSpeed\", 1.2);\n    __publicField(this, \"panSpeed\", 0.3);\n    __publicField(this, \"noRotate\", false);\n    __publicField(this, \"noZoom\", false);\n    __publicField(this, \"noPan\", false);\n    __publicField(this, \"staticMoving\", false);\n    __publicField(this, \"dynamicDampingFactor\", 0.2);\n    __publicField(this, \"minDistance\", 0);\n    __publicField(this, \"maxDistance\", Infinity);\n    __publicField(this, \"keys\", [\"KeyA\", \"KeyS\", \"KeyD\"\n    /*D*/]);\n    __publicField(this, \"mouseButtons\", {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN\n    });\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    __publicField(this, \"cursorZoom\", false);\n    __publicField(this, \"target\", new Vector3());\n    __publicField(this, \"mousePosition\", new Vector2());\n    // internals\n    __publicField(this, \"STATE\", {\n      NONE: -1,\n      ROTATE: 0,\n      ZOOM: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_ZOOM_PAN: 4\n    });\n    __publicField(this, \"EPS\", 1e-6);\n    __publicField(this, \"lastZoom\", 1);\n    __publicField(this, \"lastPosition\", new Vector3());\n    __publicField(this, \"cursorVector\", new Vector3());\n    __publicField(this, \"targetVector\", new Vector3());\n    __publicField(this, \"_state\", this.STATE.NONE);\n    __publicField(this, \"_keyState\", this.STATE.NONE);\n    __publicField(this, \"_eye\", new Vector3());\n    __publicField(this, \"_movePrev\", new Vector2());\n    __publicField(this, \"_moveCurr\", new Vector2());\n    __publicField(this, \"_lastAxis\", new Vector3());\n    __publicField(this, \"_lastAngle\", 0);\n    __publicField(this, \"_zoomStart\", new Vector2());\n    __publicField(this, \"_zoomEnd\", new Vector2());\n    __publicField(this, \"_touchZoomDistanceStart\", 0);\n    __publicField(this, \"_touchZoomDistanceEnd\", 0);\n    __publicField(this, \"_panStart\", new Vector2());\n    __publicField(this, \"_panEnd\", new Vector2());\n    __publicField(this, \"target0\");\n    __publicField(this, \"position0\");\n    __publicField(this, \"up0\");\n    __publicField(this, \"zoom0\");\n    // events\n    __publicField(this, \"changeEvent\", {\n      type: \"change\"\n    });\n    __publicField(this, \"startEvent\", {\n      type: \"start\"\n    });\n    __publicField(this, \"endEvent\", {\n      type: \"end\"\n    });\n    __publicField(this, \"onScreenVector\", new Vector2());\n    __publicField(this, \"getMouseOnScreen\", (pageX, pageY) => {\n      this.onScreenVector.set((pageX - this.screen.left) / this.screen.width, (pageY - this.screen.top) / this.screen.height);\n      return this.onScreenVector;\n    });\n    __publicField(this, \"onCircleVector\", new Vector2());\n    __publicField(this, \"getMouseOnCircle\", (pageX, pageY) => {\n      this.onCircleVector.set((pageX - this.screen.width * 0.5 - this.screen.left) / (this.screen.width * 0.5), (this.screen.height + 2 * (this.screen.top - pageY)) / this.screen.width\n      // screen.width intentional\n      );\n      return this.onCircleVector;\n    });\n    __publicField(this, \"axis\", new Vector3());\n    __publicField(this, \"quaternion\", new Quaternion());\n    __publicField(this, \"eyeDirection\", new Vector3());\n    __publicField(this, \"objectUpDirection\", new Vector3());\n    __publicField(this, \"objectSidewaysDirection\", new Vector3());\n    __publicField(this, \"moveDirection\", new Vector3());\n    __publicField(this, \"angle\", 0);\n    __publicField(this, \"rotateCamera\", () => {\n      this.moveDirection.set(this._moveCurr.x - this._movePrev.x, this._moveCurr.y - this._movePrev.y, 0);\n      this.angle = this.moveDirection.length();\n      if (this.angle) {\n        this._eye.copy(this.object.position).sub(this.target);\n        this.eyeDirection.copy(this._eye).normalize();\n        this.objectUpDirection.copy(this.object.up).normalize();\n        this.objectSidewaysDirection.crossVectors(this.objectUpDirection, this.eyeDirection).normalize();\n        this.objectUpDirection.setLength(this._moveCurr.y - this._movePrev.y);\n        this.objectSidewaysDirection.setLength(this._moveCurr.x - this._movePrev.x);\n        this.moveDirection.copy(this.objectUpDirection.add(this.objectSidewaysDirection));\n        this.axis.crossVectors(this.moveDirection, this._eye).normalize();\n        this.angle *= this.rotateSpeed;\n        this.quaternion.setFromAxisAngle(this.axis, this.angle);\n        this._eye.applyQuaternion(this.quaternion);\n        this.object.up.applyQuaternion(this.quaternion);\n        this._lastAxis.copy(this.axis);\n        this._lastAngle = this.angle;\n      } else if (!this.staticMoving && this._lastAngle) {\n        this._lastAngle *= Math.sqrt(1 - this.dynamicDampingFactor);\n        this._eye.copy(this.object.position).sub(this.target);\n        this.quaternion.setFromAxisAngle(this._lastAxis, this._lastAngle);\n        this._eye.applyQuaternion(this.quaternion);\n        this.object.up.applyQuaternion(this.quaternion);\n      }\n      this._movePrev.copy(this._moveCurr);\n    });\n    __publicField(this, \"zoomCamera\", () => {\n      let factor;\n      if (this._state === this.STATE.TOUCH_ZOOM_PAN) {\n        factor = this._touchZoomDistanceStart / this._touchZoomDistanceEnd;\n        this._touchZoomDistanceStart = this._touchZoomDistanceEnd;\n        if (this.object.isPerspectiveCamera) {\n          this._eye.multiplyScalar(factor);\n        } else if (this.object.isOrthographicCamera) {\n          this.object.zoom /= factor;\n          this.object.updateProjectionMatrix();\n        } else {\n          console.warn(\"THREE.TrackballControls: Unsupported camera type\");\n        }\n      } else {\n        factor = 1 + (this._zoomEnd.y - this._zoomStart.y) * this.zoomSpeed;\n        if (Math.abs(factor - 1) > this.EPS && factor > 0) {\n          if (this.object.isPerspectiveCamera) {\n            if (factor > 1 && this._eye.length() >= this.maxDistance - this.EPS) {\n              factor = 1;\n            }\n            this._eye.multiplyScalar(factor);\n          } else if (this.object.isOrthographicCamera) {\n            if (factor > 1 && this.object.zoom < this.maxDistance * this.maxDistance) {\n              factor = 1;\n            }\n            this.object.zoom /= factor;\n          } else {\n            console.warn(\"THREE.TrackballControls: Unsupported camera type\");\n          }\n        }\n        if (this.staticMoving) {\n          this._zoomStart.copy(this._zoomEnd);\n        } else {\n          this._zoomStart.y += (this._zoomEnd.y - this._zoomStart.y) * this.dynamicDampingFactor;\n        }\n        if (this.cursorZoom) {\n          this.targetVector.copy(this.target).project(this.object);\n          let worldPos = this.cursorVector.set(this.mousePosition.x, this.mousePosition.y, this.targetVector.z).unproject(this.object);\n          this.target.lerpVectors(worldPos, this.target, factor);\n        }\n        if (this.object.isOrthographicCamera) {\n          this.object.updateProjectionMatrix();\n        }\n      }\n    });\n    __publicField(this, \"mouseChange\", new Vector2());\n    __publicField(this, \"objectUp\", new Vector3());\n    __publicField(this, \"pan\", new Vector3());\n    __publicField(this, \"panCamera\", () => {\n      if (!this.domElement) return;\n      this.mouseChange.copy(this._panEnd).sub(this._panStart);\n      if (this.mouseChange.lengthSq() > this.EPS) {\n        if (this.object.isOrthographicCamera) {\n          const orthoObject = this.object;\n          const scale_x = (orthoObject.right - orthoObject.left) / this.object.zoom;\n          const scale_y = (orthoObject.top - orthoObject.bottom) / this.object.zoom;\n          this.mouseChange.x *= scale_x;\n          this.mouseChange.y *= scale_y;\n        } else {\n          this.mouseChange.multiplyScalar(this._eye.length() * this.panSpeed);\n        }\n        this.pan.copy(this._eye).cross(this.object.up).setLength(this.mouseChange.x);\n        this.pan.add(this.objectUp.copy(this.object.up).setLength(this.mouseChange.y));\n        this.object.position.add(this.pan);\n        this.target.add(this.pan);\n        if (this.staticMoving) {\n          this._panStart.copy(this._panEnd);\n        } else {\n          this._panStart.add(this.mouseChange.subVectors(this._panEnd, this._panStart).multiplyScalar(this.dynamicDampingFactor));\n        }\n      }\n    });\n    __publicField(this, \"checkDistances\", () => {\n      if (!this.noZoom || !this.noPan) {\n        if (this._eye.lengthSq() > this.maxDistance * this.maxDistance) {\n          this.object.position.addVectors(this.target, this._eye.setLength(this.maxDistance));\n          this._zoomStart.copy(this._zoomEnd);\n        }\n        if (this._eye.lengthSq() < this.minDistance * this.minDistance) {\n          this.object.position.addVectors(this.target, this._eye.setLength(this.minDistance));\n          this._zoomStart.copy(this._zoomEnd);\n        }\n      }\n    });\n    __publicField(this, \"handleResize\", () => {\n      if (!this.domElement) return;\n      const box = this.domElement.getBoundingClientRect();\n      const d = this.domElement.ownerDocument.documentElement;\n      this.screen.left = box.left + window.pageXOffset - d.clientLeft;\n      this.screen.top = box.top + window.pageYOffset - d.clientTop;\n      this.screen.width = box.width;\n      this.screen.height = box.height;\n    });\n    __publicField(this, \"update\", () => {\n      this._eye.subVectors(this.object.position, this.target);\n      if (!this.noRotate) {\n        this.rotateCamera();\n      }\n      if (!this.noZoom) {\n        this.zoomCamera();\n      }\n      if (!this.noPan) {\n        this.panCamera();\n      }\n      this.object.position.addVectors(this.target, this._eye);\n      if (this.object.isPerspectiveCamera) {\n        this.checkDistances();\n        this.object.lookAt(this.target);\n        if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS) {\n          this.dispatchEvent(this.changeEvent);\n          this.lastPosition.copy(this.object.position);\n        }\n      } else if (this.object.isOrthographicCamera) {\n        this.object.lookAt(this.target);\n        if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS || this.lastZoom !== this.object.zoom) {\n          this.dispatchEvent(this.changeEvent);\n          this.lastPosition.copy(this.object.position);\n          this.lastZoom = this.object.zoom;\n        }\n      } else {\n        console.warn(\"THREE.TrackballControls: Unsupported camera type\");\n      }\n    });\n    __publicField(this, \"reset\", () => {\n      this._state = this.STATE.NONE;\n      this._keyState = this.STATE.NONE;\n      this.target.copy(this.target0);\n      this.object.position.copy(this.position0);\n      this.object.up.copy(this.up0);\n      this.object.zoom = this.zoom0;\n      this.object.updateProjectionMatrix();\n      this._eye.subVectors(this.object.position, this.target);\n      this.object.lookAt(this.target);\n      this.dispatchEvent(this.changeEvent);\n      this.lastPosition.copy(this.object.position);\n      this.lastZoom = this.object.zoom;\n    });\n    __publicField(this, \"keydown\", event => {\n      if (this.enabled === false) return;\n      window.removeEventListener(\"keydown\", this.keydown);\n      if (this._keyState !== this.STATE.NONE) {\n        return;\n      } else if (event.code === this.keys[this.STATE.ROTATE] && !this.noRotate) {\n        this._keyState = this.STATE.ROTATE;\n      } else if (event.code === this.keys[this.STATE.ZOOM] && !this.noZoom) {\n        this._keyState = this.STATE.ZOOM;\n      } else if (event.code === this.keys[this.STATE.PAN] && !this.noPan) {\n        this._keyState = this.STATE.PAN;\n      }\n    });\n    __publicField(this, \"onPointerDown\", event => {\n      if (this.enabled === false) return;\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseDown(event);\n          break;\n      }\n    });\n    __publicField(this, \"onPointerMove\", event => {\n      if (this.enabled === false) return;\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseMove(event);\n          break;\n      }\n    });\n    __publicField(this, \"onPointerUp\", event => {\n      if (this.enabled === false) return;\n      switch (event.pointerType) {\n        case \"mouse\":\n        case \"pen\":\n          this.onMouseUp();\n          break;\n      }\n    });\n    __publicField(this, \"keyup\", () => {\n      if (this.enabled === false) return;\n      this._keyState = this.STATE.NONE;\n      window.addEventListener(\"keydown\", this.keydown);\n    });\n    __publicField(this, \"onMouseDown\", event => {\n      if (!this.domElement) return;\n      if (this._state === this.STATE.NONE) {\n        switch (event.button) {\n          case this.mouseButtons.LEFT:\n            this._state = this.STATE.ROTATE;\n            break;\n          case this.mouseButtons.MIDDLE:\n            this._state = this.STATE.ZOOM;\n            break;\n          case this.mouseButtons.RIGHT:\n            this._state = this.STATE.PAN;\n            break;\n        }\n      }\n      const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state;\n      if (state === this.STATE.ROTATE && !this.noRotate) {\n        this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY));\n        this._movePrev.copy(this._moveCurr);\n      } else if (state === this.STATE.ZOOM && !this.noZoom) {\n        this._zoomStart.copy(this.getMouseOnScreen(event.pageX, event.pageY));\n        this._zoomEnd.copy(this._zoomStart);\n      } else if (state === this.STATE.PAN && !this.noPan) {\n        this._panStart.copy(this.getMouseOnScreen(event.pageX, event.pageY));\n        this._panEnd.copy(this._panStart);\n      }\n      this.domElement.ownerDocument.addEventListener(\"pointermove\", this.onPointerMove);\n      this.domElement.ownerDocument.addEventListener(\"pointerup\", this.onPointerUp);\n      this.dispatchEvent(this.startEvent);\n    });\n    __publicField(this, \"onMouseMove\", event => {\n      if (this.enabled === false) return;\n      const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state;\n      if (state === this.STATE.ROTATE && !this.noRotate) {\n        this._movePrev.copy(this._moveCurr);\n        this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY));\n      } else if (state === this.STATE.ZOOM && !this.noZoom) {\n        this._zoomEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY));\n      } else if (state === this.STATE.PAN && !this.noPan) {\n        this._panEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY));\n      }\n    });\n    __publicField(this, \"onMouseUp\", () => {\n      if (!this.domElement) return;\n      if (this.enabled === false) return;\n      this._state = this.STATE.NONE;\n      this.domElement.ownerDocument.removeEventListener(\"pointermove\", this.onPointerMove);\n      this.domElement.ownerDocument.removeEventListener(\"pointerup\", this.onPointerUp);\n      this.dispatchEvent(this.endEvent);\n    });\n    __publicField(this, \"mousewheel\", event => {\n      if (this.enabled === false) return;\n      if (this.noZoom === true) return;\n      event.preventDefault();\n      switch (event.deltaMode) {\n        case 2:\n          this._zoomStart.y -= event.deltaY * 0.025;\n          break;\n        case 1:\n          this._zoomStart.y -= event.deltaY * 0.01;\n          break;\n        default:\n          this._zoomStart.y -= event.deltaY * 25e-5;\n          break;\n      }\n      this.mousePosition.x = event.offsetX / this.screen.width * 2 - 1;\n      this.mousePosition.y = -(event.offsetY / this.screen.height) * 2 + 1;\n      this.dispatchEvent(this.startEvent);\n      this.dispatchEvent(this.endEvent);\n    });\n    __publicField(this, \"touchstart\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      switch (event.touches.length) {\n        case 1:\n          this._state = this.STATE.TOUCH_ROTATE;\n          this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n          this._movePrev.copy(this._moveCurr);\n          break;\n        default:\n          this._state = this.STATE.TOUCH_ZOOM_PAN;\n          const dx = event.touches[0].pageX - event.touches[1].pageX;\n          const dy = event.touches[0].pageY - event.touches[1].pageY;\n          this._touchZoomDistanceEnd = this._touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy);\n          const x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n          const y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n          this._panStart.copy(this.getMouseOnScreen(x, y));\n          this._panEnd.copy(this._panStart);\n          break;\n      }\n      this.dispatchEvent(this.startEvent);\n    });\n    __publicField(this, \"touchmove\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n      switch (event.touches.length) {\n        case 1:\n          this._movePrev.copy(this._moveCurr);\n          this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n          break;\n        default:\n          const dx = event.touches[0].pageX - event.touches[1].pageX;\n          const dy = event.touches[0].pageY - event.touches[1].pageY;\n          this._touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy);\n          const x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n          const y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n          this._panEnd.copy(this.getMouseOnScreen(x, y));\n          break;\n      }\n    });\n    __publicField(this, \"touchend\", event => {\n      if (this.enabled === false) return;\n      switch (event.touches.length) {\n        case 0:\n          this._state = this.STATE.NONE;\n          break;\n        case 1:\n          this._state = this.STATE.TOUCH_ROTATE;\n          this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n          this._movePrev.copy(this._moveCurr);\n          break;\n      }\n      this.dispatchEvent(this.endEvent);\n    });\n    __publicField(this, \"contextmenu\", event => {\n      if (this.enabled === false) return;\n      event.preventDefault();\n    });\n    // https://github.com/mrdoob/three.js/issues/20575\n    __publicField(this, \"connect\", domElement => {\n      if (domElement === document) {\n        console.error('THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.');\n      }\n      this.domElement = domElement;\n      this.domElement.addEventListener(\"contextmenu\", this.contextmenu);\n      this.domElement.addEventListener(\"pointerdown\", this.onPointerDown);\n      this.domElement.addEventListener(\"wheel\", this.mousewheel);\n      this.domElement.addEventListener(\"touchstart\", this.touchstart);\n      this.domElement.addEventListener(\"touchend\", this.touchend);\n      this.domElement.addEventListener(\"touchmove\", this.touchmove);\n      this.domElement.ownerDocument.addEventListener(\"pointermove\", this.onPointerMove);\n      this.domElement.ownerDocument.addEventListener(\"pointerup\", this.onPointerUp);\n      window.addEventListener(\"keydown\", this.keydown);\n      window.addEventListener(\"keyup\", this.keyup);\n      this.handleResize();\n    });\n    __publicField(this, \"dispose\", () => {\n      if (!this.domElement) return;\n      this.domElement.removeEventListener(\"contextmenu\", this.contextmenu);\n      this.domElement.removeEventListener(\"pointerdown\", this.onPointerDown);\n      this.domElement.removeEventListener(\"wheel\", this.mousewheel);\n      this.domElement.removeEventListener(\"touchstart\", this.touchstart);\n      this.domElement.removeEventListener(\"touchend\", this.touchend);\n      this.domElement.removeEventListener(\"touchmove\", this.touchmove);\n      this.domElement.ownerDocument.removeEventListener(\"pointermove\", this.onPointerMove);\n      this.domElement.ownerDocument.removeEventListener(\"pointerup\", this.onPointerUp);\n      window.removeEventListener(\"keydown\", this.keydown);\n      window.removeEventListener(\"keyup\", this.keyup);\n    });\n    this.object = object;\n    this.target0 = this.target.clone();\n    this.position0 = this.object.position.clone();\n    this.up0 = this.object.up.clone();\n    this.zoom0 = this.object.zoom;\n    if (domElement !== void 0) this.connect(domElement);\n    this.update();\n  }\n}\nexport { TrackballControls };", "map": {"version": 3, "names": ["TrackballControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "dom<PERSON>lement", "__publicField", "left", "top", "width", "height", "Infinity", "LEFT", "MOUSE", "ROTATE", "MIDDLE", "DOLLY", "RIGHT", "PAN", "Vector3", "Vector2", "NONE", "ZOOM", "TOUCH_ROTATE", "TOUCH_ZOOM_PAN", "STATE", "type", "pageX", "pageY", "onScreenVector", "set", "screen", "onCircleVector", "Quaternion", "moveDirection", "_move<PERSON>urr", "x", "_movePrev", "y", "angle", "length", "_eye", "copy", "position", "sub", "target", "eyeDirection", "normalize", "objectUpDirection", "up", "objectSidewaysDirection", "crossVectors", "<PERSON><PERSON><PERSON><PERSON>", "add", "axis", "rotateSpeed", "quaternion", "setFromAxisAngle", "applyQuaternion", "_lastAxis", "_lastAngle", "staticMoving", "Math", "sqrt", "dynamicDampingFactor", "factor", "_state", "_touchZoomDistanceStart", "_touchZoomDistanceEnd", "isPerspectiveCamera", "multiplyScalar", "isOrthographicCamera", "zoom", "updateProjectionMatrix", "console", "warn", "_zoomEnd", "_zoomStart", "zoomSpeed", "abs", "EPS", "maxDistance", "cursor<PERSON><PERSON>", "targetVector", "project", "worldPos", "cursorVector", "mousePosition", "z", "unproject", "lerpVectors", "mouseChange", "_panEnd", "_panStart", "lengthSq", "orthoObject", "scale_x", "right", "scale_y", "bottom", "panSpeed", "pan", "cross", "objectUp", "subVectors", "noZoom", "noPan", "addVectors", "minDistance", "box", "getBoundingClientRect", "d", "ownerDocument", "documentElement", "window", "pageXOffset", "clientLeft", "pageYOffset", "clientTop", "noRotate", "rotateCamera", "zoomCamera", "panCamera", "checkDistances", "lookAt", "lastPosition", "distanceToSquared", "dispatchEvent", "changeEvent", "lastZoom", "_keyState", "target0", "position0", "up0", "zoom0", "event", "enabled", "removeEventListener", "keydown", "code", "keys", "pointerType", "onMouseDown", "onMouseMove", "onMouseUp", "addEventListener", "button", "mouseButtons", "state", "getMouseOnCircle", "getMouseOnScreen", "onPointerMove", "onPointerUp", "startEvent", "endEvent", "preventDefault", "deltaMode", "deltaY", "offsetX", "offsetY", "touches", "dx", "dy", "document", "error", "contextmenu", "onPointerDown", "mousewheel", "touchstart", "touchend", "touchmove", "keyup", "handleResize", "clone", "connect", "update"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/controls/TrackballControls.ts"], "sourcesContent": ["import { MOUS<PERSON>, Quatern<PERSON>, Vector2, Vector3, PerspectiveCamera, OrthographicCamera } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nclass TrackballControls extends EventDispatcher<StandardControlsEventMap> {\n  public enabled = true\n\n  public screen = { left: 0, top: 0, width: 0, height: 0 }\n\n  public rotateSpeed = 1.0\n  public zoomSpeed = 1.2\n  public panSpeed = 0.3\n\n  public noRotate = false\n  public noZoom = false\n  public noPan = false\n\n  public staticMoving = false\n  public dynamicDampingFactor = 0.2\n\n  public minDistance = 0\n  public maxDistance = Infinity\n\n  public keys: [string, string, string] = ['KeyA' /*A*/, 'KeyS' /*S*/, 'KeyD' /*D*/]\n\n  public mouseButtons = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n\n  public object: PerspectiveCamera | OrthographicCamera\n  public domElement: HTMLElement | undefined\n  public cursorZoom: boolean = false\n\n  readonly target = new Vector3()\n  private mousePosition = new Vector2()\n\n  // internals\n  private STATE = {\n    NONE: -1,\n    ROTATE: 0,\n    ZOOM: 1,\n    PAN: 2,\n    TOUCH_ROTATE: 3,\n    TOUCH_ZOOM_PAN: 4,\n  }\n\n  private EPS = 0.000001\n  private lastZoom = 1\n\n  private lastPosition = new Vector3()\n  private cursorVector = new Vector3()\n  private targetVector = new Vector3()\n\n  private _state = this.STATE.NONE\n  private _keyState = this.STATE.NONE\n  private _eye = new Vector3()\n  private _movePrev = new Vector2()\n  private _moveCurr = new Vector2()\n  private _lastAxis = new Vector3()\n  private _lastAngle = 0\n  private _zoomStart = new Vector2()\n  private _zoomEnd = new Vector2()\n  private _touchZoomDistanceStart = 0\n  private _touchZoomDistanceEnd = 0\n  private _panStart = new Vector2()\n  private _panEnd = new Vector2()\n\n  private target0: Vector3\n  private position0: Vector3\n  private up0: Vector3\n  private zoom0: number\n\n  // events\n\n  private changeEvent = { type: 'change' }\n  private startEvent = { type: 'start' }\n  private endEvent = { type: 'end' }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n    this.object = object\n\n    // for reset\n\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.up0 = this.object.up.clone()\n    this.zoom0 = this.object.zoom\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n\n    // force an update at start\n    this.update()\n  }\n\n  private onScreenVector = new Vector2()\n\n  private getMouseOnScreen = (pageX: number, pageY: number): Vector2 => {\n    this.onScreenVector.set(\n      (pageX - this.screen.left) / this.screen.width,\n      (pageY - this.screen.top) / this.screen.height,\n    )\n\n    return this.onScreenVector\n  }\n\n  private onCircleVector = new Vector2()\n\n  private getMouseOnCircle = (pageX: number, pageY: number): Vector2 => {\n    this.onCircleVector.set(\n      (pageX - this.screen.width * 0.5 - this.screen.left) / (this.screen.width * 0.5),\n      (this.screen.height + 2 * (this.screen.top - pageY)) / this.screen.width, // screen.width intentional\n    )\n\n    return this.onCircleVector\n  }\n\n  private axis = new Vector3()\n  private quaternion = new Quaternion()\n  private eyeDirection = new Vector3()\n  private objectUpDirection = new Vector3()\n  private objectSidewaysDirection = new Vector3()\n  private moveDirection = new Vector3()\n  private angle: number = 0\n\n  private rotateCamera = (): void => {\n    this.moveDirection.set(this._moveCurr.x - this._movePrev.x, this._moveCurr.y - this._movePrev.y, 0)\n    this.angle = this.moveDirection.length()\n\n    if (this.angle) {\n      this._eye.copy(this.object.position).sub(this.target)\n\n      this.eyeDirection.copy(this._eye).normalize()\n      this.objectUpDirection.copy(this.object.up).normalize()\n      this.objectSidewaysDirection.crossVectors(this.objectUpDirection, this.eyeDirection).normalize()\n\n      this.objectUpDirection.setLength(this._moveCurr.y - this._movePrev.y)\n      this.objectSidewaysDirection.setLength(this._moveCurr.x - this._movePrev.x)\n\n      this.moveDirection.copy(this.objectUpDirection.add(this.objectSidewaysDirection))\n\n      this.axis.crossVectors(this.moveDirection, this._eye).normalize()\n\n      this.angle *= this.rotateSpeed\n      this.quaternion.setFromAxisAngle(this.axis, this.angle)\n\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n\n      this._lastAxis.copy(this.axis)\n      this._lastAngle = this.angle\n    } else if (!this.staticMoving && this._lastAngle) {\n      this._lastAngle *= Math.sqrt(1.0 - this.dynamicDampingFactor)\n      this._eye.copy(this.object.position).sub(this.target)\n      this.quaternion.setFromAxisAngle(this._lastAxis, this._lastAngle)\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n    }\n\n    this._movePrev.copy(this._moveCurr)\n  }\n\n  private zoomCamera = (): void => {\n    let factor\n\n    if (this._state === this.STATE.TOUCH_ZOOM_PAN) {\n      factor = this._touchZoomDistanceStart / this._touchZoomDistanceEnd\n      this._touchZoomDistanceStart = this._touchZoomDistanceEnd\n\n      if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n        this._eye.multiplyScalar(factor)\n      } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.zoom /= factor\n        this.object.updateProjectionMatrix()\n      } else {\n        console.warn('THREE.TrackballControls: Unsupported camera type')\n      }\n    } else {\n      factor = 1.0 + (this._zoomEnd.y - this._zoomStart.y) * this.zoomSpeed\n\n      if (Math.abs(factor - 1.0) > this.EPS && factor > 0.0) {\n        if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n          if (factor > 1.0 && this._eye.length() >= this.maxDistance - this.EPS) {\n            factor = 1.0\n          }\n          this._eye.multiplyScalar(factor)\n        } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n          if (factor > 1.0 && this.object.zoom < this.maxDistance * this.maxDistance) {\n            factor = 1.0\n          }\n          this.object.zoom /= factor\n        } else {\n          console.warn('THREE.TrackballControls: Unsupported camera type')\n        }\n      }\n\n      if (this.staticMoving) {\n        this._zoomStart.copy(this._zoomEnd)\n      } else {\n        this._zoomStart.y += (this._zoomEnd.y - this._zoomStart.y) * this.dynamicDampingFactor\n      }\n\n      if (this.cursorZoom) {\n        //determine 3D position of mouse cursor (on target plane)\n        this.targetVector.copy(this.target).project(this.object)\n        let worldPos = this.cursorVector\n          .set(this.mousePosition.x, this.mousePosition.y, this.targetVector.z)\n          .unproject(this.object)\n\n        //adjust target point so that \"point\" stays in place\n        this.target.lerpVectors(worldPos, this.target, factor)\n      }\n\n      // Update the projection matrix after all properties are changed\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.updateProjectionMatrix()\n      }\n    }\n  }\n\n  private mouseChange = new Vector2()\n  private objectUp = new Vector3()\n  private pan = new Vector3()\n\n  private panCamera = (): void => {\n    if (!this.domElement) return\n    this.mouseChange.copy(this._panEnd).sub(this._panStart)\n\n    if (this.mouseChange.lengthSq() > this.EPS) {\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        const orthoObject = this.object as OrthographicCamera\n        const scale_x = (orthoObject.right - orthoObject.left) / this.object.zoom\n        const scale_y = (orthoObject.top - orthoObject.bottom) / this.object.zoom\n\n        this.mouseChange.x *= scale_x\n        this.mouseChange.y *= scale_y\n      } else {\n        this.mouseChange.multiplyScalar(this._eye.length() * this.panSpeed)\n      }\n\n      this.pan.copy(this._eye).cross(this.object.up).setLength(this.mouseChange.x)\n      this.pan.add(this.objectUp.copy(this.object.up).setLength(this.mouseChange.y))\n\n      this.object.position.add(this.pan)\n      this.target.add(this.pan)\n\n      if (this.staticMoving) {\n        this._panStart.copy(this._panEnd)\n      } else {\n        this._panStart.add(\n          this.mouseChange.subVectors(this._panEnd, this._panStart).multiplyScalar(this.dynamicDampingFactor),\n        )\n      }\n    }\n  }\n\n  private checkDistances = (): void => {\n    if (!this.noZoom || !this.noPan) {\n      if (this._eye.lengthSq() > this.maxDistance * this.maxDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.maxDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n\n      if (this._eye.lengthSq() < this.minDistance * this.minDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.minDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n    }\n  }\n\n  public handleResize = (): void => {\n    if (!this.domElement) return\n    const box = this.domElement.getBoundingClientRect()\n    // adjustments come from similar code in the jquery offset() function\n    const d = this.domElement.ownerDocument.documentElement\n    this.screen.left = box.left + window.pageXOffset - d.clientLeft\n    this.screen.top = box.top + window.pageYOffset - d.clientTop\n    this.screen.width = box.width\n    this.screen.height = box.height\n  }\n\n  public update = (): void => {\n    this._eye.subVectors(this.object.position, this.target)\n\n    if (!this.noRotate) {\n      this.rotateCamera()\n    }\n\n    if (!this.noZoom) {\n      this.zoomCamera()\n    }\n\n    if (!this.noPan) {\n      this.panCamera()\n    }\n\n    this.object.position.addVectors(this.target, this._eye)\n\n    if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n      this.checkDistances()\n\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n      }\n    } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS || this.lastZoom !== this.object.zoom) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n        this.lastZoom = this.object.zoom\n      }\n    } else {\n      console.warn('THREE.TrackballControls: Unsupported camera type')\n    }\n  }\n\n  public reset = (): void => {\n    this._state = this.STATE.NONE\n    this._keyState = this.STATE.NONE\n\n    this.target.copy(this.target0)\n    this.object.position.copy(this.position0)\n    this.object.up.copy(this.up0)\n    this.object.zoom = this.zoom0\n\n    this.object.updateProjectionMatrix()\n\n    this._eye.subVectors(this.object.position, this.target)\n\n    this.object.lookAt(this.target)\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n\n    this.lastPosition.copy(this.object.position)\n    this.lastZoom = this.object.zoom\n  }\n\n  private keydown = (event: KeyboardEvent): void => {\n    if (this.enabled === false) return\n\n    window.removeEventListener('keydown', this.keydown)\n\n    if (this._keyState !== this.STATE.NONE) {\n      return\n    } else if (event.code === this.keys[this.STATE.ROTATE] && !this.noRotate) {\n      this._keyState = this.STATE.ROTATE\n    } else if (event.code === this.keys[this.STATE.ZOOM] && !this.noZoom) {\n      this._keyState = this.STATE.ZOOM\n    } else if (event.code === this.keys[this.STATE.PAN] && !this.noPan) {\n      this._keyState = this.STATE.PAN\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseDown(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseMove(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseUp()\n        break\n\n      // TODO touch\n    }\n  }\n\n  private keyup = (): void => {\n    if (this.enabled === false) return\n\n    this._keyState = this.STATE.NONE\n\n    window.addEventListener('keydown', this.keydown)\n  }\n\n  private onMouseDown = (event: MouseEvent): void => {\n    if (!this.domElement) return\n    if (this._state === this.STATE.NONE) {\n      switch (event.button) {\n        case this.mouseButtons.LEFT:\n          this._state = this.STATE.ROTATE\n          break\n\n        case this.mouseButtons.MIDDLE:\n          this._state = this.STATE.ZOOM\n          break\n\n        case this.mouseButtons.RIGHT:\n          this._state = this.STATE.PAN\n          break\n      }\n    }\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n      this._movePrev.copy(this._moveCurr)\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._zoomEnd.copy(this._zoomStart)\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._panEnd.copy(this._panStart)\n    }\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._movePrev.copy(this._moveCurr)\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    }\n  }\n\n  private onMouseUp = (): void => {\n    if (!this.domElement) return\n    if (this.enabled === false) return\n\n    this._state = this.STATE.NONE\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private mousewheel = (event: WheelEvent): void => {\n    if (this.enabled === false) return\n\n    if (this.noZoom === true) return\n\n    event.preventDefault()\n\n    switch (event.deltaMode) {\n      case 2:\n        // Zoom in pages\n        this._zoomStart.y -= event.deltaY * 0.025\n        break\n\n      case 1:\n        // Zoom in lines\n        this._zoomStart.y -= event.deltaY * 0.01\n        break\n\n      default:\n        // undefined, 0, assume pixels\n        this._zoomStart.y -= event.deltaY * 0.00025\n        break\n    }\n\n    this.mousePosition.x = (event.offsetX / this.screen.width) * 2 - 1\n    this.mousePosition.y = -(event.offsetY / this.screen.height) * 2 + 1\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private touchstart = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n\n      default:\n        // 2 or more\n        this._state = this.STATE.TOUCH_ZOOM_PAN\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = this._touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panStart.copy(this.getMouseOnScreen(x, y))\n        this._panEnd.copy(this._panStart)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private touchmove = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._movePrev.copy(this._moveCurr)\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        break\n\n      default:\n        // 2 or more\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panEnd.copy(this.getMouseOnScreen(x, y))\n        break\n    }\n  }\n\n  private touchend = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.touches.length) {\n      case 0:\n        this._state = this.STATE.NONE\n        break\n\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private contextmenu = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    this.domElement.addEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('wheel', this.mousewheel)\n\n    this.domElement.addEventListener('touchstart', this.touchstart)\n    this.domElement.addEventListener('touchend', this.touchend)\n    this.domElement.addEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    window.addEventListener('keydown', this.keydown)\n    window.addEventListener('keyup', this.keyup)\n\n    this.handleResize()\n  }\n\n  public dispose = (): void => {\n    if (!this.domElement) return\n    this.domElement.removeEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement.removeEventListener('wheel', this.mousewheel)\n\n    this.domElement.removeEventListener('touchstart', this.touchstart)\n    this.domElement.removeEventListener('touchend', this.touchend)\n    this.domElement.removeEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('keydown', this.keydown)\n    window.removeEventListener('keyup', this.keyup)\n  }\n}\n\nexport { TrackballControls }\n"], "mappings": ";;;;;;;;;;;;;AAIA,MAAMA,iBAAA,SAA0BC,eAAA,CAA0C;EA4ExEC,YAAYC,MAAA,EAAgDC,UAAA,EAA0B;IAC9E;IA5EDC,aAAA,kBAAU;IAEVA,aAAA,iBAAS;MAAEC,IAAA,EAAM;MAAGC,GAAA,EAAK;MAAGC,KAAA,EAAO;MAAGC,MAAA,EAAQ;IAAA;IAE9CJ,aAAA,sBAAc;IACdA,aAAA,oBAAY;IACZA,aAAA,mBAAW;IAEXA,aAAA,mBAAW;IACXA,aAAA,iBAAS;IACTA,aAAA,gBAAQ;IAERA,aAAA,uBAAe;IACfA,aAAA,+BAAuB;IAEvBA,aAAA,sBAAc;IACdA,aAAA,sBAAcK,QAAA;IAEdL,aAAA,eAAiC,CAAC,QAAc,QAAc;IAAA;IAE9DA,aAAA,uBAAe;MACpBM,IAAA,EAAMC,KAAA,CAAMC,MAAA;MACZC,MAAA,EAAQF,KAAA,CAAMG,KAAA;MACdC,KAAA,EAAOJ,KAAA,CAAMK;IAAA;IAGRZ,aAAA;IACAA,aAAA;IACAA,aAAA,qBAAsB;IAEpBA,aAAA,iBAAS,IAAIa,OAAA;IACdb,aAAA,wBAAgB,IAAIc,OAAA;IAGpB;IAAAd,aAAA,gBAAQ;MACde,IAAA,EAAM;MACNP,MAAA,EAAQ;MACRQ,IAAA,EAAM;MACNJ,GAAA,EAAK;MACLK,YAAA,EAAc;MACdC,cAAA,EAAgB;IAAA;IAGVlB,aAAA,cAAM;IACNA,aAAA,mBAAW;IAEXA,aAAA,uBAAe,IAAIa,OAAA;IACnBb,aAAA,uBAAe,IAAIa,OAAA;IACnBb,aAAA,uBAAe,IAAIa,OAAA;IAEnBb,aAAA,iBAAS,KAAKmB,KAAA,CAAMJ,IAAA;IACpBf,aAAA,oBAAY,KAAKmB,KAAA,CAAMJ,IAAA;IACvBf,aAAA,eAAO,IAAIa,OAAA;IACXb,aAAA,oBAAY,IAAIc,OAAA;IAChBd,aAAA,oBAAY,IAAIc,OAAA;IAChBd,aAAA,oBAAY,IAAIa,OAAA;IAChBb,aAAA,qBAAa;IACbA,aAAA,qBAAa,IAAIc,OAAA;IACjBd,aAAA,mBAAW,IAAIc,OAAA;IACfd,aAAA,kCAA0B;IAC1BA,aAAA,gCAAwB;IACxBA,aAAA,oBAAY,IAAIc,OAAA;IAChBd,aAAA,kBAAU,IAAIc,OAAA;IAEdd,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAIA;IAAAA,aAAA,sBAAc;MAAEoB,IAAA,EAAM;IAAA;IACtBpB,aAAA,qBAAa;MAAEoB,IAAA,EAAM;IAAA;IACrBpB,aAAA,mBAAW;MAAEoB,IAAA,EAAM;IAAA;IAoBnBpB,aAAA,yBAAiB,IAAIc,OAAA;IAErBd,aAAA,2BAAmB,CAACqB,KAAA,EAAeC,KAAA,KAA2B;MACpE,KAAKC,cAAA,CAAeC,GAAA,EACjBH,KAAA,GAAQ,KAAKI,MAAA,CAAOxB,IAAA,IAAQ,KAAKwB,MAAA,CAAOtB,KAAA,GACxCmB,KAAA,GAAQ,KAAKG,MAAA,CAAOvB,GAAA,IAAO,KAAKuB,MAAA,CAAOrB,MAAA;MAG1C,OAAO,KAAKmB,cAAA;IAAA;IAGNvB,aAAA,yBAAiB,IAAIc,OAAA;IAErBd,aAAA,2BAAmB,CAACqB,KAAA,EAAeC,KAAA,KAA2B;MACpE,KAAKI,cAAA,CAAeF,GAAA,EACjBH,KAAA,GAAQ,KAAKI,MAAA,CAAOtB,KAAA,GAAQ,MAAM,KAAKsB,MAAA,CAAOxB,IAAA,KAAS,KAAKwB,MAAA,CAAOtB,KAAA,GAAQ,OAC3E,KAAKsB,MAAA,CAAOrB,MAAA,GAAS,KAAK,KAAKqB,MAAA,CAAOvB,GAAA,GAAMoB,KAAA,KAAU,KAAKG,MAAA,CAAOtB;MAAA;MAAA;MAGrE,OAAO,KAAKuB,cAAA;IAAA;IAGN1B,aAAA,eAAO,IAAIa,OAAA;IACXb,aAAA,qBAAa,IAAI2B,UAAA;IACjB3B,aAAA,uBAAe,IAAIa,OAAA;IACnBb,aAAA,4BAAoB,IAAIa,OAAA;IACxBb,aAAA,kCAA0B,IAAIa,OAAA;IAC9Bb,aAAA,wBAAgB,IAAIa,OAAA;IACpBb,aAAA,gBAAgB;IAEhBA,aAAA,uBAAe,MAAY;MACjC,KAAK4B,aAAA,CAAcJ,GAAA,CAAI,KAAKK,SAAA,CAAUC,CAAA,GAAI,KAAKC,SAAA,CAAUD,CAAA,EAAG,KAAKD,SAAA,CAAUG,CAAA,GAAI,KAAKD,SAAA,CAAUC,CAAA,EAAG,CAAC;MAC7F,KAAAC,KAAA,GAAQ,KAAKL,aAAA,CAAcM,MAAA,CAAO;MAEvC,IAAI,KAAKD,KAAA,EAAO;QACT,KAAAE,IAAA,CAAKC,IAAA,CAAK,KAAKtC,MAAA,CAAOuC,QAAQ,EAAEC,GAAA,CAAI,KAAKC,MAAM;QAEpD,KAAKC,YAAA,CAAaJ,IAAA,CAAK,KAAKD,IAAI,EAAEM,SAAA;QAClC,KAAKC,iBAAA,CAAkBN,IAAA,CAAK,KAAKtC,MAAA,CAAO6C,EAAE,EAAEF,SAAA;QAC5C,KAAKG,uBAAA,CAAwBC,YAAA,CAAa,KAAKH,iBAAA,EAAmB,KAAKF,YAAY,EAAEC,SAAA;QAErF,KAAKC,iBAAA,CAAkBI,SAAA,CAAU,KAAKjB,SAAA,CAAUG,CAAA,GAAI,KAAKD,SAAA,CAAUC,CAAC;QACpE,KAAKY,uBAAA,CAAwBE,SAAA,CAAU,KAAKjB,SAAA,CAAUC,CAAA,GAAI,KAAKC,SAAA,CAAUD,CAAC;QAE1E,KAAKF,aAAA,CAAcQ,IAAA,CAAK,KAAKM,iBAAA,CAAkBK,GAAA,CAAI,KAAKH,uBAAuB,CAAC;QAEhF,KAAKI,IAAA,CAAKH,YAAA,CAAa,KAAKjB,aAAA,EAAe,KAAKO,IAAI,EAAEM,SAAA;QAEtD,KAAKR,KAAA,IAAS,KAAKgB,WAAA;QACnB,KAAKC,UAAA,CAAWC,gBAAA,CAAiB,KAAKH,IAAA,EAAM,KAAKf,KAAK;QAEjD,KAAAE,IAAA,CAAKiB,eAAA,CAAgB,KAAKF,UAAU;QACzC,KAAKpD,MAAA,CAAO6C,EAAA,CAAGS,eAAA,CAAgB,KAAKF,UAAU;QAEzC,KAAAG,SAAA,CAAUjB,IAAA,CAAK,KAAKY,IAAI;QAC7B,KAAKM,UAAA,GAAa,KAAKrB,KAAA;MACd,YAAC,KAAKsB,YAAA,IAAgB,KAAKD,UAAA,EAAY;QAChD,KAAKA,UAAA,IAAcE,IAAA,CAAKC,IAAA,CAAK,IAAM,KAAKC,oBAAoB;QACvD,KAAAvB,IAAA,CAAKC,IAAA,CAAK,KAAKtC,MAAA,CAAOuC,QAAQ,EAAEC,GAAA,CAAI,KAAKC,MAAM;QACpD,KAAKW,UAAA,CAAWC,gBAAA,CAAiB,KAAKE,SAAA,EAAW,KAAKC,UAAU;QAC3D,KAAAnB,IAAA,CAAKiB,eAAA,CAAgB,KAAKF,UAAU;QACzC,KAAKpD,MAAA,CAAO6C,EAAA,CAAGS,eAAA,CAAgB,KAAKF,UAAU;MAChD;MAEK,KAAAnB,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;IAAA;IAG5B7B,aAAA,qBAAa,MAAY;MAC3B,IAAA2D,MAAA;MAEJ,IAAI,KAAKC,MAAA,KAAW,KAAKzC,KAAA,CAAMD,cAAA,EAAgB;QACpCyC,MAAA,QAAKE,uBAAA,GAA0B,KAAKC,qBAAA;QAC7C,KAAKD,uBAAA,GAA0B,KAAKC,qBAAA;QAE/B,SAAKhE,MAAA,CAA6BiE,mBAAA,EAAqB;UACrD,KAAA5B,IAAA,CAAK6B,cAAA,CAAeL,MAAM;QAAA,WACrB,KAAK7D,MAAA,CAA8BmE,oBAAA,EAAsB;UACnE,KAAKnE,MAAA,CAAOoE,IAAA,IAAQP,MAAA;UACpB,KAAK7D,MAAA,CAAOqE,sBAAA;QAAuB,OAC9B;UACLC,OAAA,CAAQC,IAAA,CAAK,kDAAkD;QACjE;MAAA,OACK;QACLV,MAAA,GAAS,KAAO,KAAKW,QAAA,CAAStC,CAAA,GAAI,KAAKuC,UAAA,CAAWvC,CAAA,IAAK,KAAKwC,SAAA;QAExD,IAAAhB,IAAA,CAAKiB,GAAA,CAAId,MAAA,GAAS,CAAG,IAAI,KAAKe,GAAA,IAAOf,MAAA,GAAS,GAAK;UAChD,SAAK7D,MAAA,CAA6BiE,mBAAA,EAAqB;YACtD,IAAAJ,MAAA,GAAS,KAAO,KAAKxB,IAAA,CAAKD,MAAA,CAAY,UAAKyC,WAAA,GAAc,KAAKD,GAAA,EAAK;cAC5Df,MAAA;YACX;YACK,KAAAxB,IAAA,CAAK6B,cAAA,CAAeL,MAAM;UAAA,WACrB,KAAK7D,MAAA,CAA8BmE,oBAAA,EAAsB;YAC/D,IAAAN,MAAA,GAAS,KAAO,KAAK7D,MAAA,CAAOoE,IAAA,GAAO,KAAKS,WAAA,GAAc,KAAKA,WAAA,EAAa;cACjEhB,MAAA;YACX;YACA,KAAK7D,MAAA,CAAOoE,IAAA,IAAQP,MAAA;UAAA,OACf;YACLS,OAAA,CAAQC,IAAA,CAAK,kDAAkD;UACjE;QACF;QAEA,IAAI,KAAKd,YAAA,EAAc;UAChB,KAAAgB,UAAA,CAAWnC,IAAA,CAAK,KAAKkC,QAAQ;QAAA,OAC7B;UACA,KAAAC,UAAA,CAAWvC,CAAA,KAAM,KAAKsC,QAAA,CAAStC,CAAA,GAAI,KAAKuC,UAAA,CAAWvC,CAAA,IAAK,KAAK0B,oBAAA;QACpE;QAEA,IAAI,KAAKkB,UAAA,EAAY;UAEnB,KAAKC,YAAA,CAAazC,IAAA,CAAK,KAAKG,MAAM,EAAEuC,OAAA,CAAQ,KAAKhF,MAAM;UACvD,IAAIiF,QAAA,GAAW,KAAKC,YAAA,CACjBxD,GAAA,CAAI,KAAKyD,aAAA,CAAcnD,CAAA,EAAG,KAAKmD,aAAA,CAAcjD,CAAA,EAAG,KAAK6C,YAAA,CAAaK,CAAC,EACnEC,SAAA,CAAU,KAAKrF,MAAM;UAGxB,KAAKyC,MAAA,CAAO6C,WAAA,CAAYL,QAAA,EAAU,KAAKxC,MAAA,EAAQoB,MAAM;QACvD;QAGK,SAAK7D,MAAA,CAA8BmE,oBAAA,EAAsB;UAC5D,KAAKnE,MAAA,CAAOqE,sBAAA;QACd;MACF;IAAA;IAGMnE,aAAA,sBAAc,IAAIc,OAAA;IAClBd,aAAA,mBAAW,IAAIa,OAAA;IACfb,aAAA,cAAM,IAAIa,OAAA;IAEVb,aAAA,oBAAY,MAAY;MAC9B,IAAI,CAAC,KAAKD,UAAA,EAAY;MACtB,KAAKsF,WAAA,CAAYjD,IAAA,CAAK,KAAKkD,OAAO,EAAEhD,GAAA,CAAI,KAAKiD,SAAS;MAEtD,IAAI,KAAKF,WAAA,CAAYG,QAAA,CAAS,IAAI,KAAKd,GAAA,EAAK;QACrC,SAAK5E,MAAA,CAA8BmE,oBAAA,EAAsB;UAC5D,MAAMwB,WAAA,GAAc,KAAK3F,MAAA;UACzB,MAAM4F,OAAA,IAAWD,WAAA,CAAYE,KAAA,GAAQF,WAAA,CAAYxF,IAAA,IAAQ,KAAKH,MAAA,CAAOoE,IAAA;UACrE,MAAM0B,OAAA,IAAWH,WAAA,CAAYvF,GAAA,GAAMuF,WAAA,CAAYI,MAAA,IAAU,KAAK/F,MAAA,CAAOoE,IAAA;UAErE,KAAKmB,WAAA,CAAYvD,CAAA,IAAK4D,OAAA;UACtB,KAAKL,WAAA,CAAYrD,CAAA,IAAK4D,OAAA;QAAA,OACjB;UACL,KAAKP,WAAA,CAAYrB,cAAA,CAAe,KAAK7B,IAAA,CAAKD,MAAA,CAAO,IAAI,KAAK4D,QAAQ;QACpE;QAEA,KAAKC,GAAA,CAAI3D,IAAA,CAAK,KAAKD,IAAI,EAAE6D,KAAA,CAAM,KAAKlG,MAAA,CAAO6C,EAAE,EAAEG,SAAA,CAAU,KAAKuC,WAAA,CAAYvD,CAAC;QAC3E,KAAKiE,GAAA,CAAIhD,GAAA,CAAI,KAAKkD,QAAA,CAAS7D,IAAA,CAAK,KAAKtC,MAAA,CAAO6C,EAAE,EAAEG,SAAA,CAAU,KAAKuC,WAAA,CAAYrD,CAAC,CAAC;QAE7E,KAAKlC,MAAA,CAAOuC,QAAA,CAASU,GAAA,CAAI,KAAKgD,GAAG;QAC5B,KAAAxD,MAAA,CAAOQ,GAAA,CAAI,KAAKgD,GAAG;QAExB,IAAI,KAAKxC,YAAA,EAAc;UAChB,KAAAgC,SAAA,CAAUnD,IAAA,CAAK,KAAKkD,OAAO;QAAA,OAC3B;UACL,KAAKC,SAAA,CAAUxC,GAAA,CACb,KAAKsC,WAAA,CAAYa,UAAA,CAAW,KAAKZ,OAAA,EAAS,KAAKC,SAAS,EAAEvB,cAAA,CAAe,KAAKN,oBAAoB;QAEtG;MACF;IAAA;IAGM1D,aAAA,yBAAiB,MAAY;MACnC,IAAI,CAAC,KAAKmG,MAAA,IAAU,CAAC,KAAKC,KAAA,EAAO;QAC/B,IAAI,KAAKjE,IAAA,CAAKqD,QAAA,KAAa,KAAKb,WAAA,GAAc,KAAKA,WAAA,EAAa;UACzD,KAAA7E,MAAA,CAAOuC,QAAA,CAASgE,UAAA,CAAW,KAAK9D,MAAA,EAAQ,KAAKJ,IAAA,CAAKW,SAAA,CAAU,KAAK6B,WAAW,CAAC;UAC7E,KAAAJ,UAAA,CAAWnC,IAAA,CAAK,KAAKkC,QAAQ;QACpC;QAEA,IAAI,KAAKnC,IAAA,CAAKqD,QAAA,KAAa,KAAKc,WAAA,GAAc,KAAKA,WAAA,EAAa;UACzD,KAAAxG,MAAA,CAAOuC,QAAA,CAASgE,UAAA,CAAW,KAAK9D,MAAA,EAAQ,KAAKJ,IAAA,CAAKW,SAAA,CAAU,KAAKwD,WAAW,CAAC;UAC7E,KAAA/B,UAAA,CAAWnC,IAAA,CAAK,KAAKkC,QAAQ;QACpC;MACF;IAAA;IAGKtE,aAAA,uBAAe,MAAY;MAChC,IAAI,CAAC,KAAKD,UAAA,EAAY;MAChB,MAAAwG,GAAA,GAAM,KAAKxG,UAAA,CAAWyG,qBAAA,CAAsB;MAE5C,MAAAC,CAAA,GAAI,KAAK1G,UAAA,CAAW2G,aAAA,CAAcC,eAAA;MACxC,KAAKlF,MAAA,CAAOxB,IAAA,GAAOsG,GAAA,CAAItG,IAAA,GAAO2G,MAAA,CAAOC,WAAA,GAAcJ,CAAA,CAAEK,UAAA;MACrD,KAAKrF,MAAA,CAAOvB,GAAA,GAAMqG,GAAA,CAAIrG,GAAA,GAAM0G,MAAA,CAAOG,WAAA,GAAcN,CAAA,CAAEO,SAAA;MAC9C,KAAAvF,MAAA,CAAOtB,KAAA,GAAQoG,GAAA,CAAIpG,KAAA;MACnB,KAAAsB,MAAA,CAAOrB,MAAA,GAASmG,GAAA,CAAInG,MAAA;IAAA;IAGpBJ,aAAA,iBAAS,MAAY;MAC1B,KAAKmC,IAAA,CAAK+D,UAAA,CAAW,KAAKpG,MAAA,CAAOuC,QAAA,EAAU,KAAKE,MAAM;MAElD,KAAC,KAAK0E,QAAA,EAAU;QAClB,KAAKC,YAAA,CAAa;MACpB;MAEI,KAAC,KAAKf,MAAA,EAAQ;QAChB,KAAKgB,UAAA,CAAW;MAClB;MAEI,KAAC,KAAKf,KAAA,EAAO;QACf,KAAKgB,SAAA,CAAU;MACjB;MAEA,KAAKtH,MAAA,CAAOuC,QAAA,CAASgE,UAAA,CAAW,KAAK9D,MAAA,EAAQ,KAAKJ,IAAI;MAEjD,SAAKrC,MAAA,CAA6BiE,mBAAA,EAAqB;QAC1D,KAAKsD,cAAA,CAAe;QAEf,KAAAvH,MAAA,CAAOwH,MAAA,CAAO,KAAK/E,MAAM;QAE1B,SAAKgF,YAAA,CAAaC,iBAAA,CAAkB,KAAK1H,MAAA,CAAOuC,QAAQ,IAAI,KAAKqC,GAAA,EAAK;UAEnE,KAAA+C,aAAA,CAAc,KAAKC,WAAW;UAEnC,KAAKH,YAAA,CAAanF,IAAA,CAAK,KAAKtC,MAAA,CAAOuC,QAAQ;QAC7C;MAAA,WACU,KAAKvC,MAAA,CAA8BmE,oBAAA,EAAsB;QAC9D,KAAAnE,MAAA,CAAOwH,MAAA,CAAO,KAAK/E,MAAM;QAE9B,IAAI,KAAKgF,YAAA,CAAaC,iBAAA,CAAkB,KAAK1H,MAAA,CAAOuC,QAAQ,IAAI,KAAKqC,GAAA,IAAO,KAAKiD,QAAA,KAAa,KAAK7H,MAAA,CAAOoE,IAAA,EAAM;UAEzG,KAAAuD,aAAA,CAAc,KAAKC,WAAW;UAEnC,KAAKH,YAAA,CAAanF,IAAA,CAAK,KAAKtC,MAAA,CAAOuC,QAAQ;UACtC,KAAAsF,QAAA,GAAW,KAAK7H,MAAA,CAAOoE,IAAA;QAC9B;MAAA,OACK;QACLE,OAAA,CAAQC,IAAA,CAAK,kDAAkD;MACjE;IAAA;IAGKrE,aAAA,gBAAQ,MAAY;MACpB,KAAA4D,MAAA,GAAS,KAAKzC,KAAA,CAAMJ,IAAA;MACpB,KAAA6G,SAAA,GAAY,KAAKzG,KAAA,CAAMJ,IAAA;MAEvB,KAAAwB,MAAA,CAAOH,IAAA,CAAK,KAAKyF,OAAO;MAC7B,KAAK/H,MAAA,CAAOuC,QAAA,CAASD,IAAA,CAAK,KAAK0F,SAAS;MACxC,KAAKhI,MAAA,CAAO6C,EAAA,CAAGP,IAAA,CAAK,KAAK2F,GAAG;MACvB,KAAAjI,MAAA,CAAOoE,IAAA,GAAO,KAAK8D,KAAA;MAExB,KAAKlI,MAAA,CAAOqE,sBAAA;MAEZ,KAAKhC,IAAA,CAAK+D,UAAA,CAAW,KAAKpG,MAAA,CAAOuC,QAAA,EAAU,KAAKE,MAAM;MAEjD,KAAAzC,MAAA,CAAOwH,MAAA,CAAO,KAAK/E,MAAM;MAGzB,KAAAkF,aAAA,CAAc,KAAKC,WAAW;MAEnC,KAAKH,YAAA,CAAanF,IAAA,CAAK,KAAKtC,MAAA,CAAOuC,QAAQ;MACtC,KAAAsF,QAAA,GAAW,KAAK7H,MAAA,CAAOoE,IAAA;IAAA;IAGtBlE,aAAA,kBAAWiI,KAAA,IAA+B;MAChD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAErBtB,MAAA,CAAAuB,mBAAA,CAAoB,WAAW,KAAKC,OAAO;MAElD,IAAI,KAAKR,SAAA,KAAc,KAAKzG,KAAA,CAAMJ,IAAA,EAAM;QACtC;MACF,WAAWkH,KAAA,CAAMI,IAAA,KAAS,KAAKC,IAAA,CAAK,KAAKnH,KAAA,CAAMX,MAAM,KAAK,CAAC,KAAKyG,QAAA,EAAU;QACnE,KAAAW,SAAA,GAAY,KAAKzG,KAAA,CAAMX,MAAA;MAC9B,WAAWyH,KAAA,CAAMI,IAAA,KAAS,KAAKC,IAAA,CAAK,KAAKnH,KAAA,CAAMH,IAAI,KAAK,CAAC,KAAKmF,MAAA,EAAQ;QAC/D,KAAAyB,SAAA,GAAY,KAAKzG,KAAA,CAAMH,IAAA;MAC9B,WAAWiH,KAAA,CAAMI,IAAA,KAAS,KAAKC,IAAA,CAAK,KAAKnH,KAAA,CAAMP,GAAG,KAAK,CAAC,KAAKwF,KAAA,EAAO;QAC7D,KAAAwB,SAAA,GAAY,KAAKzG,KAAA,CAAMP,GAAA;MAC9B;IAAA;IAGMZ,aAAA,wBAAiBiI,KAAA,IAA8B;MACrD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5B,QAAQD,KAAA,CAAMM,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKC,WAAA,CAAYP,KAAK;UACtB;MAGJ;IAAA;IAGMjI,aAAA,wBAAiBiI,KAAA,IAA8B;MACrD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5B,QAAQD,KAAA,CAAMM,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKE,WAAA,CAAYR,KAAK;UACtB;MAGJ;IAAA;IAGMjI,aAAA,sBAAeiI,KAAA,IAA8B;MACnD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5B,QAAQD,KAAA,CAAMM,WAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKG,SAAA,CAAU;UACf;MAGJ;IAAA;IAGM1I,aAAA,gBAAQ,MAAY;MAC1B,IAAI,KAAKkI,OAAA,KAAY,OAAO;MAEvB,KAAAN,SAAA,GAAY,KAAKzG,KAAA,CAAMJ,IAAA;MAErB6F,MAAA,CAAA+B,gBAAA,CAAiB,WAAW,KAAKP,OAAO;IAAA;IAGzCpI,aAAA,sBAAeiI,KAAA,IAA4B;MACjD,IAAI,CAAC,KAAKlI,UAAA,EAAY;MACtB,IAAI,KAAK6D,MAAA,KAAW,KAAKzC,KAAA,CAAMJ,IAAA,EAAM;QACnC,QAAQkH,KAAA,CAAMW,MAAA;UACZ,KAAK,KAAKC,YAAA,CAAavI,IAAA;YAChB,KAAAsD,MAAA,GAAS,KAAKzC,KAAA,CAAMX,MAAA;YACzB;UAEF,KAAK,KAAKqI,YAAA,CAAapI,MAAA;YAChB,KAAAmD,MAAA,GAAS,KAAKzC,KAAA,CAAMH,IAAA;YACzB;UAEF,KAAK,KAAK6H,YAAA,CAAalI,KAAA;YAChB,KAAAiD,MAAA,GAAS,KAAKzC,KAAA,CAAMP,GAAA;YACzB;QACJ;MACF;MAEM,MAAAkI,KAAA,GAAQ,KAAKlB,SAAA,KAAc,KAAKzG,KAAA,CAAMJ,IAAA,GAAO,KAAK6G,SAAA,GAAY,KAAKhE,MAAA;MAEzE,IAAIkF,KAAA,KAAU,KAAK3H,KAAA,CAAMX,MAAA,IAAU,CAAC,KAAKyG,QAAA,EAAU;QAC5C,KAAApF,SAAA,CAAUO,IAAA,CAAK,KAAK2G,gBAAA,CAAiBd,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;QAC9D,KAAAS,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;MAAA,WACzBiH,KAAA,KAAU,KAAK3H,KAAA,CAAMH,IAAA,IAAQ,CAAC,KAAKmF,MAAA,EAAQ;QAC/C,KAAA5B,UAAA,CAAWnC,IAAA,CAAK,KAAK4G,gBAAA,CAAiBf,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;QAC/D,KAAAgD,QAAA,CAASlC,IAAA,CAAK,KAAKmC,UAAU;MAAA,WACzBuE,KAAA,KAAU,KAAK3H,KAAA,CAAMP,GAAA,IAAO,CAAC,KAAKwF,KAAA,EAAO;QAC7C,KAAAb,SAAA,CAAUnD,IAAA,CAAK,KAAK4G,gBAAA,CAAiBf,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;QAC9D,KAAAgE,OAAA,CAAQlD,IAAA,CAAK,KAAKmD,SAAS;MAClC;MAEA,KAAKxF,UAAA,CAAW2G,aAAA,CAAciC,gBAAA,CAAiB,eAAe,KAAKM,aAAa;MAChF,KAAKlJ,UAAA,CAAW2G,aAAA,CAAciC,gBAAA,CAAiB,aAAa,KAAKO,WAAW;MAGvE,KAAAzB,aAAA,CAAc,KAAK0B,UAAU;IAAA;IAG5BnJ,aAAA,sBAAeiI,KAAA,IAA4B;MACjD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAEtB,MAAAY,KAAA,GAAQ,KAAKlB,SAAA,KAAc,KAAKzG,KAAA,CAAMJ,IAAA,GAAO,KAAK6G,SAAA,GAAY,KAAKhE,MAAA;MAEzE,IAAIkF,KAAA,KAAU,KAAK3H,KAAA,CAAMX,MAAA,IAAU,CAAC,KAAKyG,QAAA,EAAU;QAC5C,KAAAlF,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;QAC7B,KAAAA,SAAA,CAAUO,IAAA,CAAK,KAAK2G,gBAAA,CAAiBd,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;MAAA,WAC1DwH,KAAA,KAAU,KAAK3H,KAAA,CAAMH,IAAA,IAAQ,CAAC,KAAKmF,MAAA,EAAQ;QAC/C,KAAA7B,QAAA,CAASlC,IAAA,CAAK,KAAK4G,gBAAA,CAAiBf,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;MAAA,WACzDwH,KAAA,KAAU,KAAK3H,KAAA,CAAMP,GAAA,IAAO,CAAC,KAAKwF,KAAA,EAAO;QAC7C,KAAAd,OAAA,CAAQlD,IAAA,CAAK,KAAK4G,gBAAA,CAAiBf,KAAA,CAAM5G,KAAA,EAAO4G,KAAA,CAAM3G,KAAK,CAAC;MACnE;IAAA;IAGMtB,aAAA,oBAAY,MAAY;MAC9B,IAAI,CAAC,KAAKD,UAAA,EAAY;MACtB,IAAI,KAAKmI,OAAA,KAAY,OAAO;MAEvB,KAAAtE,MAAA,GAAS,KAAKzC,KAAA,CAAMJ,IAAA;MAEzB,KAAKhB,UAAA,CAAW2G,aAAA,CAAcyB,mBAAA,CAAoB,eAAe,KAAKc,aAAa;MACnF,KAAKlJ,UAAA,CAAW2G,aAAA,CAAcyB,mBAAA,CAAoB,aAAa,KAAKe,WAAW;MAG1E,KAAAzB,aAAA,CAAc,KAAK2B,QAAQ;IAAA;IAG1BpJ,aAAA,qBAAciI,KAAA,IAA4B;MAChD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5B,IAAI,KAAK/B,MAAA,KAAW,MAAM;MAE1B8B,KAAA,CAAMoB,cAAA,CAAe;MAErB,QAAQpB,KAAA,CAAMqB,SAAA;QACZ,KAAK;UAEE,KAAA/E,UAAA,CAAWvC,CAAA,IAAKiG,KAAA,CAAMsB,MAAA,GAAS;UACpC;QAEF,KAAK;UAEE,KAAAhF,UAAA,CAAWvC,CAAA,IAAKiG,KAAA,CAAMsB,MAAA,GAAS;UACpC;QAEF;UAEO,KAAAhF,UAAA,CAAWvC,CAAA,IAAKiG,KAAA,CAAMsB,MAAA,GAAS;UACpC;MACJ;MAEA,KAAKtE,aAAA,CAAcnD,CAAA,GAAKmG,KAAA,CAAMuB,OAAA,GAAU,KAAK/H,MAAA,CAAOtB,KAAA,GAAS,IAAI;MAC5D,KAAA8E,aAAA,CAAcjD,CAAA,GAAI,EAAEiG,KAAA,CAAMwB,OAAA,GAAU,KAAKhI,MAAA,CAAOrB,MAAA,IAAU,IAAI;MAG9D,KAAAqH,aAAA,CAAc,KAAK0B,UAAU;MAE7B,KAAA1B,aAAA,CAAc,KAAK2B,QAAQ;IAAA;IAG1BpJ,aAAA,qBAAciI,KAAA,IAA4B;MAChD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5BD,KAAA,CAAMoB,cAAA,CAAe;MAEb,QAAApB,KAAA,CAAMyB,OAAA,CAAQxH,MAAA;QACpB,KAAK;UACE,KAAA0B,MAAA,GAAS,KAAKzC,KAAA,CAAMF,YAAA;UACzB,KAAKY,SAAA,CAAUO,IAAA,CAAK,KAAK2G,gBAAA,CAAiBd,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,EAAO4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAK,CAAC;UACpF,KAAAS,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;UAClC;QAEF;UAEO,KAAA+B,MAAA,GAAS,KAAKzC,KAAA,CAAMD,cAAA;UACnB,MAAAyI,EAAA,GAAK1B,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,GAAQ4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA;UAC/C,MAAAuI,EAAA,GAAK3B,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,GAAQ2G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA;UAChD,KAAAwC,qBAAA,GAAwB,KAAKD,uBAAA,GAA0BL,IAAA,CAAKC,IAAA,CAAKkG,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;UAEjF,MAAA9H,CAAA,IAAKmG,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,GAAQ4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,IAAS;UACxD,MAAAW,CAAA,IAAKiG,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,GAAQ2G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,IAAS;UAC9D,KAAKiE,SAAA,CAAUnD,IAAA,CAAK,KAAK4G,gBAAA,CAAiBlH,CAAA,EAAGE,CAAC,CAAC;UAC1C,KAAAsD,OAAA,CAAQlD,IAAA,CAAK,KAAKmD,SAAS;UAChC;MACJ;MAGK,KAAAkC,aAAA,CAAc,KAAK0B,UAAU;IAAA;IAG5BnJ,aAAA,oBAAaiI,KAAA,IAA4B;MAC/C,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5BD,KAAA,CAAMoB,cAAA,CAAe;MAEb,QAAApB,KAAA,CAAMyB,OAAA,CAAQxH,MAAA;QACpB,KAAK;UACE,KAAAH,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;UAClC,KAAKA,SAAA,CAAUO,IAAA,CAAK,KAAK2G,gBAAA,CAAiBd,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,EAAO4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAK,CAAC;UACzF;QAEF;UAEQ,MAAAqI,EAAA,GAAK1B,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,GAAQ4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA;UAC/C,MAAAuI,EAAA,GAAK3B,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,GAAQ2G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA;UACrD,KAAKwC,qBAAA,GAAwBN,IAAA,CAAKC,IAAA,CAAKkG,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;UAElD,MAAA9H,CAAA,IAAKmG,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,GAAQ4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,IAAS;UACxD,MAAAW,CAAA,IAAKiG,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,GAAQ2G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAA,IAAS;UAC9D,KAAKgE,OAAA,CAAQlD,IAAA,CAAK,KAAK4G,gBAAA,CAAiBlH,CAAA,EAAGE,CAAC,CAAC;UAC7C;MACJ;IAAA;IAGMhC,aAAA,mBAAYiI,KAAA,IAA4B;MAC9C,IAAI,KAAKC,OAAA,KAAY,OAAO;MAEpB,QAAAD,KAAA,CAAMyB,OAAA,CAAQxH,MAAA;QACpB,KAAK;UACE,KAAA0B,MAAA,GAAS,KAAKzC,KAAA,CAAMJ,IAAA;UACzB;QAEF,KAAK;UACE,KAAA6C,MAAA,GAAS,KAAKzC,KAAA,CAAMF,YAAA;UACzB,KAAKY,SAAA,CAAUO,IAAA,CAAK,KAAK2G,gBAAA,CAAiBd,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAErI,KAAA,EAAO4G,KAAA,CAAMyB,OAAA,CAAQ,CAAC,EAAEpI,KAAK,CAAC;UACpF,KAAAS,SAAA,CAAUK,IAAA,CAAK,KAAKP,SAAS;UAClC;MACJ;MAGK,KAAA4F,aAAA,CAAc,KAAK2B,QAAQ;IAAA;IAG1BpJ,aAAA,sBAAeiI,KAAA,IAA4B;MACjD,IAAI,KAAKC,OAAA,KAAY,OAAO;MAE5BD,KAAA,CAAMoB,cAAA,CAAe;IAAA;IAIhB;IAAArJ,aAAA,kBAAWD,UAAA,IAAkC;MAClD,IAAKA,UAAA,KAAuB8J,QAAA,EAAU;QAC5BzF,OAAA,CAAA0F,KAAA,CACN;MAEJ;MACA,KAAK/J,UAAA,GAAaA,UAAA;MAClB,KAAKA,UAAA,CAAW4I,gBAAA,CAAiB,eAAe,KAAKoB,WAAW;MAEhE,KAAKhK,UAAA,CAAW4I,gBAAA,CAAiB,eAAe,KAAKqB,aAAa;MAClE,KAAKjK,UAAA,CAAW4I,gBAAA,CAAiB,SAAS,KAAKsB,UAAU;MAEzD,KAAKlK,UAAA,CAAW4I,gBAAA,CAAiB,cAAc,KAAKuB,UAAU;MAC9D,KAAKnK,UAAA,CAAW4I,gBAAA,CAAiB,YAAY,KAAKwB,QAAQ;MAC1D,KAAKpK,UAAA,CAAW4I,gBAAA,CAAiB,aAAa,KAAKyB,SAAS;MAE5D,KAAKrK,UAAA,CAAW2G,aAAA,CAAciC,gBAAA,CAAiB,eAAe,KAAKM,aAAa;MAChF,KAAKlJ,UAAA,CAAW2G,aAAA,CAAciC,gBAAA,CAAiB,aAAa,KAAKO,WAAW;MAErEtC,MAAA,CAAA+B,gBAAA,CAAiB,WAAW,KAAKP,OAAO;MACxCxB,MAAA,CAAA+B,gBAAA,CAAiB,SAAS,KAAK0B,KAAK;MAE3C,KAAKC,YAAA,CAAa;IAAA;IAGbtK,aAAA,kBAAU,MAAY;MAC3B,IAAI,CAAC,KAAKD,UAAA,EAAY;MACtB,KAAKA,UAAA,CAAWoI,mBAAA,CAAoB,eAAe,KAAK4B,WAAW;MAEnE,KAAKhK,UAAA,CAAWoI,mBAAA,CAAoB,eAAe,KAAK6B,aAAa;MACrE,KAAKjK,UAAA,CAAWoI,mBAAA,CAAoB,SAAS,KAAK8B,UAAU;MAE5D,KAAKlK,UAAA,CAAWoI,mBAAA,CAAoB,cAAc,KAAK+B,UAAU;MACjE,KAAKnK,UAAA,CAAWoI,mBAAA,CAAoB,YAAY,KAAKgC,QAAQ;MAC7D,KAAKpK,UAAA,CAAWoI,mBAAA,CAAoB,aAAa,KAAKiC,SAAS;MAE/D,KAAKrK,UAAA,CAAW2G,aAAA,CAAcyB,mBAAA,CAAoB,eAAe,KAAKc,aAAa;MACnF,KAAKlJ,UAAA,CAAW2G,aAAA,CAAcyB,mBAAA,CAAoB,aAAa,KAAKe,WAAW;MAExEtC,MAAA,CAAAuB,mBAAA,CAAoB,WAAW,KAAKC,OAAO;MAC3CxB,MAAA,CAAAuB,mBAAA,CAAoB,SAAS,KAAKkC,KAAK;IAAA;IAriB9C,KAAKvK,MAAA,GAASA,MAAA;IAIT,KAAA+H,OAAA,GAAU,KAAKtF,MAAA,CAAOgI,KAAA,CAAM;IACjC,KAAKzC,SAAA,GAAY,KAAKhI,MAAA,CAAOuC,QAAA,CAASkI,KAAA,CAAM;IAC5C,KAAKxC,GAAA,GAAM,KAAKjI,MAAA,CAAO6C,EAAA,CAAG4H,KAAA,CAAM;IAC3B,KAAAvC,KAAA,GAAQ,KAAKlI,MAAA,CAAOoE,IAAA;IAGzB,IAAInE,UAAA,KAAe,QAAW,KAAKyK,OAAA,CAAQzK,UAAU;IAGrD,KAAK0K,MAAA,CAAO;EACd;AAyhBF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}