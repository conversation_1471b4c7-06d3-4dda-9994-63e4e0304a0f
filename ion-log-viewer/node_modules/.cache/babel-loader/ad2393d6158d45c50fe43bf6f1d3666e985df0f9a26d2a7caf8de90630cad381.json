{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { BigIntSerde } from \"./BigIntSerde\";\nexport class LowLevelBinaryWriter {\n  constructor(writeable) {\n    this.writeable = writeable;\n  }\n  static getSignedIntSize(value) {\n    if (value === 0) {\n      return 1;\n    }\n    const numberOfSignBits = 1;\n    const magnitude = Math.abs(value);\n    const numberOfMagnitudeBits = Math.ceil(Math.log2(magnitude + 1));\n    const numberOfBits = numberOfMagnitudeBits + numberOfSignBits;\n    return Math.ceil(numberOfBits / 8);\n  }\n  static getUnsignedIntSize(value) {\n    if (typeof value === \"bigint\") {\n      return BigIntSerde.getUnsignedIntSizeInBytes(value);\n    }\n    if (value === 0) {\n      return 1;\n    }\n    const numberOfBits = Math.floor(Math.log2(value)) + 1;\n    const numberOfBytes = Math.ceil(numberOfBits / 8);\n    return numberOfBytes;\n  }\n  static getVariableLengthSignedIntSize(value) {\n    const absoluteValue = Math.abs(value);\n    if (absoluteValue === 0) {\n      return 1;\n    }\n    const valueBits = Math.floor(Math.log2(absoluteValue)) + 1;\n    const trailingStopBits = Math.floor(valueBits / 7);\n    const leadingStopBit = 1;\n    const signBit = 1;\n    return Math.ceil((valueBits + trailingStopBits + leadingStopBit + signBit) / 8);\n  }\n  static getVariableLengthUnsignedIntSize(value) {\n    if (value === 0) {\n      return 1;\n    }\n    const valueBits = Math.floor(Math.log2(value)) + 1;\n    const stopBits = Math.ceil(valueBits / 7);\n    return Math.ceil((valueBits + stopBits) / 8);\n  }\n  writeSignedInt(originalValue) {\n    const length = LowLevelBinaryWriter.getSignedIntSize(originalValue);\n    let value = Math.abs(originalValue);\n    const tempBuf = new Uint8Array(length);\n    let i = tempBuf.length;\n    while (value >= 128) {\n      tempBuf[--i] = value & 0xff;\n      value >>>= 8;\n    }\n    tempBuf[--i] = value & 0xff;\n    if (1 / originalValue < 0) {\n      tempBuf[0] |= 0x80;\n    }\n    this.writeable.writeBytes(tempBuf);\n  }\n  writeUnsignedInt(originalValue) {\n    if (typeof originalValue === \"bigint\") {\n      const encodedBytes = BigIntSerde.toUnsignedIntBytes(originalValue);\n      this.writeable.writeBytes(encodedBytes);\n      return;\n    }\n    const length = LowLevelBinaryWriter.getUnsignedIntSize(originalValue);\n    const tempBuf = new Uint8Array(length);\n    let value = originalValue;\n    let i = tempBuf.length;\n    while (value > 0) {\n      tempBuf[--i] = value % 256;\n      value = Math.trunc(value / 256);\n    }\n    this.writeable.writeBytes(tempBuf);\n  }\n  writeVariableLengthSignedInt(originalValue) {\n    const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthSignedIntSize(originalValue));\n    let value = Math.abs(originalValue);\n    let i = tempBuf.length - 1;\n    while (value >= 64) {\n      tempBuf[i--] = value & 0x7f;\n      value >>>= 7;\n    }\n    tempBuf[i] = value;\n    if (1 / originalValue < 0) {\n      tempBuf[i] |= 0x40;\n    }\n    tempBuf[tempBuf.length - 1] |= 0x80;\n    this.writeable.writeBytes(tempBuf);\n  }\n  writeVariableLengthUnsignedInt(originalValue) {\n    const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(originalValue));\n    let value = originalValue;\n    let i = tempBuf.length;\n    tempBuf[--i] = value & 0x7f | 0x80;\n    value >>>= 7;\n    while (value > 0) {\n      tempBuf[--i] = value & 0x7f;\n      value >>>= 7;\n    }\n    this.writeable.writeBytes(tempBuf);\n  }\n  writeByte(byte) {\n    this.writeable.writeByte(byte);\n  }\n  writeBytes(bytes) {\n    this.writeable.writeBytes(bytes);\n  }\n  getBytes() {\n    return this.writeable.getBytes();\n  }\n}", "map": {"version": 3, "names": ["BigIntSerde", "LowLevelBinaryWriter", "constructor", "writeable", "getSignedIntSize", "value", "numberOfSignBits", "magnitude", "Math", "abs", "numberOfMagnitudeBits", "ceil", "log2", "numberOfBits", "getUnsignedIntSize", "getUnsignedIntSizeInBytes", "floor", "numberOfBytes", "getVariableLengthSignedIntSize", "absoluteValue", "valueBits", "trailingStopBits", "leadingStopBit", "signBit", "getVariableLengthUnsignedIntSize", "stopBits", "writeSignedInt", "originalValue", "length", "tempBuf", "Uint8Array", "i", "writeBytes", "writeUnsignedInt", "encodedBytes", "toUnsignedIntBytes", "trunc", "writeVariableLengthSignedInt", "writeVariableLengthUnsignedInt", "writeByte", "byte", "bytes", "getBytes"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonLowLevelBinaryWriter.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { BigIntSerde } from \"./BigIntSerde\";\nexport class LowLevelBinaryWriter {\n    constructor(writeable) {\n        this.writeable = writeable;\n    }\n    static getSignedIntSize(value) {\n        if (value === 0) {\n            return 1;\n        }\n        const numberOfSignBits = 1;\n        const magnitude = Math.abs(value);\n        const numberOfMagnitudeBits = Math.ceil(Math.log2(magnitude + 1));\n        const numberOfBits = numberOfMagnitudeBits + numberOfSignBits;\n        return Math.ceil(numberOfBits / 8);\n    }\n    static getUnsignedIntSize(value) {\n        if (typeof value === \"bigint\") {\n            return BigIntSerde.getUnsignedIntSizeInBytes(value);\n        }\n        if (value === 0) {\n            return 1;\n        }\n        const numberOfBits = Math.floor(Math.log2(value)) + 1;\n        const numberOfBytes = Math.ceil(numberOfBits / 8);\n        return numberOfBytes;\n    }\n    static getVariableLengthSignedIntSize(value) {\n        const absoluteValue = Math.abs(value);\n        if (absoluteValue === 0) {\n            return 1;\n        }\n        const valueBits = Math.floor(Math.log2(absoluteValue)) + 1;\n        const trailingStopBits = Math.floor(valueBits / 7);\n        const leadingStopBit = 1;\n        const signBit = 1;\n        return Math.ceil((valueBits + trailingStopBits + leadingStopBit + signBit) / 8);\n    }\n    static getVariableLengthUnsignedIntSize(value) {\n        if (value === 0) {\n            return 1;\n        }\n        const valueBits = Math.floor(Math.log2(value)) + 1;\n        const stopBits = Math.ceil(valueBits / 7);\n        return Math.ceil((valueBits + stopBits) / 8);\n    }\n    writeSignedInt(originalValue) {\n        const length = LowLevelBinaryWriter.getSignedIntSize(originalValue);\n        let value = Math.abs(originalValue);\n        const tempBuf = new Uint8Array(length);\n        let i = tempBuf.length;\n        while (value >= 128) {\n            tempBuf[--i] = value & 0xff;\n            value >>>= 8;\n        }\n        tempBuf[--i] = value & 0xff;\n        if (1 / originalValue < 0) {\n            tempBuf[0] |= 0x80;\n        }\n        this.writeable.writeBytes(tempBuf);\n    }\n    writeUnsignedInt(originalValue) {\n        if (typeof originalValue === \"bigint\") {\n            const encodedBytes = BigIntSerde.toUnsignedIntBytes(originalValue);\n            this.writeable.writeBytes(encodedBytes);\n            return;\n        }\n        const length = LowLevelBinaryWriter.getUnsignedIntSize(originalValue);\n        const tempBuf = new Uint8Array(length);\n        let value = originalValue;\n        let i = tempBuf.length;\n        while (value > 0) {\n            tempBuf[--i] = value % 256;\n            value = Math.trunc(value / 256);\n        }\n        this.writeable.writeBytes(tempBuf);\n    }\n    writeVariableLengthSignedInt(originalValue) {\n        const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthSignedIntSize(originalValue));\n        let value = Math.abs(originalValue);\n        let i = tempBuf.length - 1;\n        while (value >= 64) {\n            tempBuf[i--] = value & 0x7f;\n            value >>>= 7;\n        }\n        tempBuf[i] = value;\n        if (1 / originalValue < 0) {\n            tempBuf[i] |= 0x40;\n        }\n        tempBuf[tempBuf.length - 1] |= 0x80;\n        this.writeable.writeBytes(tempBuf);\n    }\n    writeVariableLengthUnsignedInt(originalValue) {\n        const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(originalValue));\n        let value = originalValue;\n        let i = tempBuf.length;\n        tempBuf[--i] = (value & 0x7f) | 0x80;\n        value >>>= 7;\n        while (value > 0) {\n            tempBuf[--i] = value & 0x7f;\n            value >>>= 7;\n        }\n        this.writeable.writeBytes(tempBuf);\n    }\n    writeByte(byte) {\n        this.writeable.writeByte(byte);\n    }\n    writeBytes(bytes) {\n        this.writeable.writeBytes(bytes);\n    }\n    getBytes() {\n        return this.writeable.getBytes();\n    }\n}\n//# sourceMappingURL=IonLowLevelBinaryWriter.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,eAAe;AAC3C,OAAO,MAAMC,oBAAoB,CAAC;EAC9BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA,OAAOC,gBAAgBA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACA,MAAMC,gBAAgB,GAAG,CAAC;IAC1B,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC;IACjC,MAAMK,qBAAqB,GAAGF,IAAI,CAACG,IAAI,CAACH,IAAI,CAACI,IAAI,CAACL,SAAS,GAAG,CAAC,CAAC,CAAC;IACjE,MAAMM,YAAY,GAAGH,qBAAqB,GAAGJ,gBAAgB;IAC7D,OAAOE,IAAI,CAACG,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;EACtC;EACA,OAAOC,kBAAkBA,CAACT,KAAK,EAAE;IAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOL,WAAW,CAACe,yBAAyB,CAACV,KAAK,CAAC;IACvD;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACA,MAAMQ,YAAY,GAAGL,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACI,IAAI,CAACP,KAAK,CAAC,CAAC,GAAG,CAAC;IACrD,MAAMY,aAAa,GAAGT,IAAI,CAACG,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;IACjD,OAAOI,aAAa;EACxB;EACA,OAAOC,8BAA8BA,CAACb,KAAK,EAAE;IACzC,MAAMc,aAAa,GAAGX,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC;IACrC,IAAIc,aAAa,KAAK,CAAC,EAAE;MACrB,OAAO,CAAC;IACZ;IACA,MAAMC,SAAS,GAAGZ,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACI,IAAI,CAACO,aAAa,CAAC,CAAC,GAAG,CAAC;IAC1D,MAAME,gBAAgB,GAAGb,IAAI,CAACQ,KAAK,CAACI,SAAS,GAAG,CAAC,CAAC;IAClD,MAAME,cAAc,GAAG,CAAC;IACxB,MAAMC,OAAO,GAAG,CAAC;IACjB,OAAOf,IAAI,CAACG,IAAI,CAAC,CAACS,SAAS,GAAGC,gBAAgB,GAAGC,cAAc,GAAGC,OAAO,IAAI,CAAC,CAAC;EACnF;EACA,OAAOC,gCAAgCA,CAACnB,KAAK,EAAE;IAC3C,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACA,MAAMe,SAAS,GAAGZ,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACI,IAAI,CAACP,KAAK,CAAC,CAAC,GAAG,CAAC;IAClD,MAAMoB,QAAQ,GAAGjB,IAAI,CAACG,IAAI,CAACS,SAAS,GAAG,CAAC,CAAC;IACzC,OAAOZ,IAAI,CAACG,IAAI,CAAC,CAACS,SAAS,GAAGK,QAAQ,IAAI,CAAC,CAAC;EAChD;EACAC,cAAcA,CAACC,aAAa,EAAE;IAC1B,MAAMC,MAAM,GAAG3B,oBAAoB,CAACG,gBAAgB,CAACuB,aAAa,CAAC;IACnE,IAAItB,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACkB,aAAa,CAAC;IACnC,MAAME,OAAO,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;IACtC,IAAIG,CAAC,GAAGF,OAAO,CAACD,MAAM;IACtB,OAAOvB,KAAK,IAAI,GAAG,EAAE;MACjBwB,OAAO,CAAC,EAAEE,CAAC,CAAC,GAAG1B,KAAK,GAAG,IAAI;MAC3BA,KAAK,MAAM,CAAC;IAChB;IACAwB,OAAO,CAAC,EAAEE,CAAC,CAAC,GAAG1B,KAAK,GAAG,IAAI;IAC3B,IAAI,CAAC,GAAGsB,aAAa,GAAG,CAAC,EAAE;MACvBE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IACtB;IACA,IAAI,CAAC1B,SAAS,CAAC6B,UAAU,CAACH,OAAO,CAAC;EACtC;EACAI,gBAAgBA,CAACN,aAAa,EAAE;IAC5B,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACnC,MAAMO,YAAY,GAAGlC,WAAW,CAACmC,kBAAkB,CAACR,aAAa,CAAC;MAClE,IAAI,CAACxB,SAAS,CAAC6B,UAAU,CAACE,YAAY,CAAC;MACvC;IACJ;IACA,MAAMN,MAAM,GAAG3B,oBAAoB,CAACa,kBAAkB,CAACa,aAAa,CAAC;IACrE,MAAME,OAAO,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;IACtC,IAAIvB,KAAK,GAAGsB,aAAa;IACzB,IAAII,CAAC,GAAGF,OAAO,CAACD,MAAM;IACtB,OAAOvB,KAAK,GAAG,CAAC,EAAE;MACdwB,OAAO,CAAC,EAAEE,CAAC,CAAC,GAAG1B,KAAK,GAAG,GAAG;MAC1BA,KAAK,GAAGG,IAAI,CAAC4B,KAAK,CAAC/B,KAAK,GAAG,GAAG,CAAC;IACnC;IACA,IAAI,CAACF,SAAS,CAAC6B,UAAU,CAACH,OAAO,CAAC;EACtC;EACAQ,4BAA4BA,CAACV,aAAa,EAAE;IACxC,MAAME,OAAO,GAAG,IAAIC,UAAU,CAAC7B,oBAAoB,CAACiB,8BAA8B,CAACS,aAAa,CAAC,CAAC;IAClG,IAAItB,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACkB,aAAa,CAAC;IACnC,IAAII,CAAC,GAAGF,OAAO,CAACD,MAAM,GAAG,CAAC;IAC1B,OAAOvB,KAAK,IAAI,EAAE,EAAE;MAChBwB,OAAO,CAACE,CAAC,EAAE,CAAC,GAAG1B,KAAK,GAAG,IAAI;MAC3BA,KAAK,MAAM,CAAC;IAChB;IACAwB,OAAO,CAACE,CAAC,CAAC,GAAG1B,KAAK;IAClB,IAAI,CAAC,GAAGsB,aAAa,GAAG,CAAC,EAAE;MACvBE,OAAO,CAACE,CAAC,CAAC,IAAI,IAAI;IACtB;IACAF,OAAO,CAACA,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;IACnC,IAAI,CAACzB,SAAS,CAAC6B,UAAU,CAACH,OAAO,CAAC;EACtC;EACAS,8BAA8BA,CAACX,aAAa,EAAE;IAC1C,MAAME,OAAO,GAAG,IAAIC,UAAU,CAAC7B,oBAAoB,CAACuB,gCAAgC,CAACG,aAAa,CAAC,CAAC;IACpG,IAAItB,KAAK,GAAGsB,aAAa;IACzB,IAAII,CAAC,GAAGF,OAAO,CAACD,MAAM;IACtBC,OAAO,CAAC,EAAEE,CAAC,CAAC,GAAI1B,KAAK,GAAG,IAAI,GAAI,IAAI;IACpCA,KAAK,MAAM,CAAC;IACZ,OAAOA,KAAK,GAAG,CAAC,EAAE;MACdwB,OAAO,CAAC,EAAEE,CAAC,CAAC,GAAG1B,KAAK,GAAG,IAAI;MAC3BA,KAAK,MAAM,CAAC;IAChB;IACA,IAAI,CAACF,SAAS,CAAC6B,UAAU,CAACH,OAAO,CAAC;EACtC;EACAU,SAASA,CAACC,IAAI,EAAE;IACZ,IAAI,CAACrC,SAAS,CAACoC,SAAS,CAACC,IAAI,CAAC;EAClC;EACAR,UAAUA,CAACS,KAAK,EAAE;IACd,IAAI,CAACtC,SAAS,CAAC6B,UAAU,CAACS,KAAK,CAAC;EACpC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvC,SAAS,CAACuC,QAAQ,CAAC,CAAC;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}