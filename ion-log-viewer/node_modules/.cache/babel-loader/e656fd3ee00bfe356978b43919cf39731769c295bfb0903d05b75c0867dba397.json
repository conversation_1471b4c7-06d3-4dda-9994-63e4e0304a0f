{"ast": null, "code": "import { IonTypes, toBase64 } from \"../Ion\";\nimport { Lob } from \"./Lob\";\nexport class <PERSON><PERSON>b extends Lob(IonTypes.BLOB) {\n  constructor(data, annotations = []) {\n    super(data, annotations);\n  }\n  toJSON() {\n    return toBase64(this);\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeBlob(this);\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "toBase64", "<PERSON><PERSON>", "Blob", "BLOB", "constructor", "data", "annotations", "toJSON", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeBlob"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Blob.js"], "sourcesContent": ["import { IonTypes, toBase64 } from \"../Ion\";\nimport { Lob } from \"./Lob\";\nexport class <PERSON><PERSON>b extends Lob(IonTypes.BLOB) {\n    constructor(data, annotations = []) {\n        super(data, annotations);\n    }\n    toJSON() {\n        return toBase64(this);\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeBlob(this);\n    }\n}\n//# sourceMappingURL=Blob.js.map"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,QAAQ;AAC3C,SAASC,GAAG,QAAQ,OAAO;AAC3B,OAAO,MAAMC,IAAI,SAASD,GAAG,CAACF,QAAQ,CAACI,IAAI,CAAC,CAAC;EACzCC,WAAWA,CAACC,IAAI,EAAEC,WAAW,GAAG,EAAE,EAAE;IAChC,KAAK,CAACD,IAAI,EAAEC,WAAW,CAAC;EAC5B;EACAC,MAAMA,CAAA,EAAG;IACL,OAAOP,QAAQ,CAAC,IAAI,CAAC;EACzB;EACAQ,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,SAAS,CAAC,IAAI,CAAC;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}