{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { IonTypes } from \"./IonTypes\";\nexport class AbstractWriter {\n  constructor() {\n    this._annotations = [];\n  }\n  addAnnotation(annotation) {\n    if (!this._isString(annotation)) {\n      throw new Error(\"Annotation must be of type string.\");\n    }\n    this._annotations.push(annotation);\n  }\n  setAnnotations(annotations) {\n    if (annotations === undefined || annotations === null) {\n      throw new Error(\"Annotations were undefined or null.\");\n    } else if (!this._validateAnnotations(annotations)) {\n      throw new Error(\"Annotations must be of type string[].\");\n    } else {\n      this._annotations = annotations;\n    }\n  }\n  writeValues(reader) {\n    this._writeValues(reader);\n  }\n  writeValue(reader) {\n    this._writeValue(reader);\n  }\n  _clearAnnotations() {\n    this._annotations = [];\n  }\n  _writeValues(reader) {\n    let type = reader.type();\n    if (type === null) {\n      type = reader.next();\n    }\n    while (type !== null) {\n      this._writeValue(reader);\n      type = reader.next();\n    }\n  }\n  _writeValue(reader) {\n    const type = reader.type();\n    if (type === null) {\n      return;\n    }\n    if (this._isInStruct()) {\n      const fieldName = reader.fieldName();\n      if (fieldName === null) {\n        throw new Error(\"Cannot call writeValue() when the Writer is in a Struct but the Reader is not.\");\n      }\n      this.writeFieldName(fieldName);\n    }\n    this.setAnnotations(reader.annotations());\n    if (reader.isNull()) {\n      this.writeNull(type);\n      return;\n    }\n    switch (type) {\n      case IonTypes.BOOL:\n        this.writeBoolean(reader.booleanValue());\n        break;\n      case IonTypes.INT:\n        this.writeInt(reader.bigIntValue());\n        break;\n      case IonTypes.FLOAT:\n        this.writeFloat64(reader.numberValue());\n        break;\n      case IonTypes.DECIMAL:\n        this.writeDecimal(reader.decimalValue());\n        break;\n      case IonTypes.TIMESTAMP:\n        this.writeTimestamp(reader.timestampValue());\n        break;\n      case IonTypes.SYMBOL:\n        this.writeSymbol(reader.stringValue());\n        break;\n      case IonTypes.STRING:\n        this.writeString(reader.stringValue());\n        break;\n      case IonTypes.CLOB:\n        this.writeClob(reader.uInt8ArrayValue());\n        break;\n      case IonTypes.BLOB:\n        this.writeBlob(reader.uInt8ArrayValue());\n        break;\n      case IonTypes.LIST:\n        this.stepIn(IonTypes.LIST);\n        break;\n      case IonTypes.SEXP:\n        this.stepIn(IonTypes.SEXP);\n        break;\n      case IonTypes.STRUCT:\n        this.stepIn(IonTypes.STRUCT);\n        break;\n      default:\n        throw new Error(\"Unrecognized type \" + (type !== null ? type.name : type));\n    }\n    if (type.isContainer) {\n      reader.stepIn();\n      this._writeValues(reader);\n      this.stepOut();\n      reader.stepOut();\n    }\n  }\n  _validateAnnotations(input) {\n    if (!Array.isArray(input)) {\n      return false;\n    }\n    for (let i = 0; i < input.length; i++) {\n      if (!this._isString(input[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  _isString(input) {\n    return typeof input === \"string\";\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "AbstractWriter", "constructor", "_annotations", "addAnnotation", "annotation", "_isString", "Error", "push", "setAnnotations", "annotations", "undefined", "_validateAnnotations", "writeValues", "reader", "_writeValues", "writeValue", "_writeValue", "_clearAnnotations", "type", "next", "_isInStruct", "fieldName", "writeFieldName", "isNull", "writeNull", "BOOL", "writeBoolean", "booleanValue", "INT", "writeInt", "bigIntValue", "FLOAT", "writeFloat64", "numberValue", "DECIMAL", "writeDecimal", "decimalValue", "TIMESTAMP", "writeTimestamp", "timestampValue", "SYMBOL", "writeSymbol", "stringValue", "STRING", "writeString", "CLOB", "writeClob", "uInt8ArrayValue", "BLOB", "writeBlob", "LIST", "stepIn", "SEXP", "STRUCT", "name", "<PERSON><PERSON><PERSON><PERSON>", "stepOut", "input", "Array", "isArray", "i", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/AbstractWriter.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { IonTypes } from \"./IonTypes\";\nexport class AbstractWriter {\n    constructor() {\n        this._annotations = [];\n    }\n    addAnnotation(annotation) {\n        if (!this._isString(annotation)) {\n            throw new Error(\"Annotation must be of type string.\");\n        }\n        this._annotations.push(annotation);\n    }\n    setAnnotations(annotations) {\n        if (annotations === undefined || annotations === null) {\n            throw new Error(\"Annotations were undefined or null.\");\n        }\n        else if (!this._validateAnnotations(annotations)) {\n            throw new Error(\"Annotations must be of type string[].\");\n        }\n        else {\n            this._annotations = annotations;\n        }\n    }\n    writeValues(reader) {\n        this._writeValues(reader);\n    }\n    writeValue(reader) {\n        this._writeValue(reader);\n    }\n    _clearAnnotations() {\n        this._annotations = [];\n    }\n    _writeValues(reader) {\n        let type = reader.type();\n        if (type === null) {\n            type = reader.next();\n        }\n        while (type !== null) {\n            this._writeValue(reader);\n            type = reader.next();\n        }\n    }\n    _writeValue(reader) {\n        const type = reader.type();\n        if (type === null) {\n            return;\n        }\n        if (this._isInStruct()) {\n            const fieldName = reader.fieldName();\n            if (fieldName === null) {\n                throw new Error(\"Cannot call writeValue() when the Writer is in a Struct but the Reader is not.\");\n            }\n            this.writeFieldName(fieldName);\n        }\n        this.setAnnotations(reader.annotations());\n        if (reader.isNull()) {\n            this.writeNull(type);\n            return;\n        }\n        switch (type) {\n            case IonTypes.BOOL:\n                this.writeBoolean(reader.booleanValue());\n                break;\n            case IonTypes.INT:\n                this.writeInt(reader.bigIntValue());\n                break;\n            case IonTypes.FLOAT:\n                this.writeFloat64(reader.numberValue());\n                break;\n            case IonTypes.DECIMAL:\n                this.writeDecimal(reader.decimalValue());\n                break;\n            case IonTypes.TIMESTAMP:\n                this.writeTimestamp(reader.timestampValue());\n                break;\n            case IonTypes.SYMBOL:\n                this.writeSymbol(reader.stringValue());\n                break;\n            case IonTypes.STRING:\n                this.writeString(reader.stringValue());\n                break;\n            case IonTypes.CLOB:\n                this.writeClob(reader.uInt8ArrayValue());\n                break;\n            case IonTypes.BLOB:\n                this.writeBlob(reader.uInt8ArrayValue());\n                break;\n            case IonTypes.LIST:\n                this.stepIn(IonTypes.LIST);\n                break;\n            case IonTypes.SEXP:\n                this.stepIn(IonTypes.SEXP);\n                break;\n            case IonTypes.STRUCT:\n                this.stepIn(IonTypes.STRUCT);\n                break;\n            default:\n                throw new Error(\"Unrecognized type \" + (type !== null ? type.name : type));\n        }\n        if (type.isContainer) {\n            reader.stepIn();\n            this._writeValues(reader);\n            this.stepOut();\n            reader.stepOut();\n        }\n    }\n    _validateAnnotations(input) {\n        if (!Array.isArray(input)) {\n            return false;\n        }\n        for (let i = 0; i < input.length; i++) {\n            if (!this._isString(input[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    _isString(input) {\n        return typeof input === \"string\";\n    }\n}\n//# sourceMappingURL=AbstractWriter.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAAC,IAAI,CAACC,SAAS,CAACD,UAAU,CAAC,EAAE;MAC7B,MAAM,IAAIE,KAAK,CAAC,oCAAoC,CAAC;IACzD;IACA,IAAI,CAACJ,YAAY,CAACK,IAAI,CAACH,UAAU,CAAC;EACtC;EACAI,cAAcA,CAACC,WAAW,EAAE;IACxB,IAAIA,WAAW,KAAKC,SAAS,IAAID,WAAW,KAAK,IAAI,EAAE;MACnD,MAAM,IAAIH,KAAK,CAAC,qCAAqC,CAAC;IAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAACK,oBAAoB,CAACF,WAAW,CAAC,EAAE;MAC9C,MAAM,IAAIH,KAAK,CAAC,uCAAuC,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACJ,YAAY,GAAGO,WAAW;IACnC;EACJ;EACAG,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;EAC7B;EACAE,UAAUA,CAACF,MAAM,EAAE;IACf,IAAI,CAACG,WAAW,CAACH,MAAM,CAAC;EAC5B;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACf,YAAY,GAAG,EAAE;EAC1B;EACAY,YAAYA,CAACD,MAAM,EAAE;IACjB,IAAIK,IAAI,GAAGL,MAAM,CAACK,IAAI,CAAC,CAAC;IACxB,IAAIA,IAAI,KAAK,IAAI,EAAE;MACfA,IAAI,GAAGL,MAAM,CAACM,IAAI,CAAC,CAAC;IACxB;IACA,OAAOD,IAAI,KAAK,IAAI,EAAE;MAClB,IAAI,CAACF,WAAW,CAACH,MAAM,CAAC;MACxBK,IAAI,GAAGL,MAAM,CAACM,IAAI,CAAC,CAAC;IACxB;EACJ;EACAH,WAAWA,CAACH,MAAM,EAAE;IAChB,MAAMK,IAAI,GAAGL,MAAM,CAACK,IAAI,CAAC,CAAC;IAC1B,IAAIA,IAAI,KAAK,IAAI,EAAE;MACf;IACJ;IACA,IAAI,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;MACpB,MAAMC,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAAC,CAAC;MACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;QACpB,MAAM,IAAIf,KAAK,CAAC,gFAAgF,CAAC;MACrG;MACA,IAAI,CAACgB,cAAc,CAACD,SAAS,CAAC;IAClC;IACA,IAAI,CAACb,cAAc,CAACK,MAAM,CAACJ,WAAW,CAAC,CAAC,CAAC;IACzC,IAAII,MAAM,CAACU,MAAM,CAAC,CAAC,EAAE;MACjB,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC;MACpB;IACJ;IACA,QAAQA,IAAI;MACR,KAAKnB,QAAQ,CAAC0B,IAAI;QACd,IAAI,CAACC,YAAY,CAACb,MAAM,CAACc,YAAY,CAAC,CAAC,CAAC;QACxC;MACJ,KAAK5B,QAAQ,CAAC6B,GAAG;QACb,IAAI,CAACC,QAAQ,CAAChB,MAAM,CAACiB,WAAW,CAAC,CAAC,CAAC;QACnC;MACJ,KAAK/B,QAAQ,CAACgC,KAAK;QACf,IAAI,CAACC,YAAY,CAACnB,MAAM,CAACoB,WAAW,CAAC,CAAC,CAAC;QACvC;MACJ,KAAKlC,QAAQ,CAACmC,OAAO;QACjB,IAAI,CAACC,YAAY,CAACtB,MAAM,CAACuB,YAAY,CAAC,CAAC,CAAC;QACxC;MACJ,KAAKrC,QAAQ,CAACsC,SAAS;QACnB,IAAI,CAACC,cAAc,CAACzB,MAAM,CAAC0B,cAAc,CAAC,CAAC,CAAC;QAC5C;MACJ,KAAKxC,QAAQ,CAACyC,MAAM;QAChB,IAAI,CAACC,WAAW,CAAC5B,MAAM,CAAC6B,WAAW,CAAC,CAAC,CAAC;QACtC;MACJ,KAAK3C,QAAQ,CAAC4C,MAAM;QAChB,IAAI,CAACC,WAAW,CAAC/B,MAAM,CAAC6B,WAAW,CAAC,CAAC,CAAC;QACtC;MACJ,KAAK3C,QAAQ,CAAC8C,IAAI;QACd,IAAI,CAACC,SAAS,CAACjC,MAAM,CAACkC,eAAe,CAAC,CAAC,CAAC;QACxC;MACJ,KAAKhD,QAAQ,CAACiD,IAAI;QACd,IAAI,CAACC,SAAS,CAACpC,MAAM,CAACkC,eAAe,CAAC,CAAC,CAAC;QACxC;MACJ,KAAKhD,QAAQ,CAACmD,IAAI;QACd,IAAI,CAACC,MAAM,CAACpD,QAAQ,CAACmD,IAAI,CAAC;QAC1B;MACJ,KAAKnD,QAAQ,CAACqD,IAAI;QACd,IAAI,CAACD,MAAM,CAACpD,QAAQ,CAACqD,IAAI,CAAC;QAC1B;MACJ,KAAKrD,QAAQ,CAACsD,MAAM;QAChB,IAAI,CAACF,MAAM,CAACpD,QAAQ,CAACsD,MAAM,CAAC;QAC5B;MACJ;QACI,MAAM,IAAI/C,KAAK,CAAC,oBAAoB,IAAIY,IAAI,KAAK,IAAI,GAAGA,IAAI,CAACoC,IAAI,GAAGpC,IAAI,CAAC,CAAC;IAClF;IACA,IAAIA,IAAI,CAACqC,WAAW,EAAE;MAClB1C,MAAM,CAACsC,MAAM,CAAC,CAAC;MACf,IAAI,CAACrC,YAAY,CAACD,MAAM,CAAC;MACzB,IAAI,CAAC2C,OAAO,CAAC,CAAC;MACd3C,MAAM,CAAC2C,OAAO,CAAC,CAAC;IACpB;EACJ;EACA7C,oBAAoBA,CAAC8C,KAAK,EAAE;IACxB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACoD,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;QAC3B,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACAvD,SAASA,CAACoD,KAAK,EAAE;IACb,OAAO,OAAOA,KAAK,KAAK,QAAQ;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}