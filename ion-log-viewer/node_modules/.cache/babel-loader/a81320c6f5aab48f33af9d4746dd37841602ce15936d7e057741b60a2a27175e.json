{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/RobotInfoPanel.tsx\";\nimport React from 'react';\nimport { Paper, Typography, Box, Table, TableBody, TableCell, TableContainer, TableRow, Chip, Alert } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RobotInfoPanel = ({\n  data\n}) => {\n  const {\n    robotInfo,\n    metadata\n  } = data;\n  const formatBytes = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // Try to extract robot info from metadata if not found in robotInfo\n  const extractedModel = robotInfo.model || metadata && (metadata.robotModel || metadata.robot_model || metadata.model || metadata.platform) || 'Unknown';\n  const extractedName = robotInfo.name || metadata && (metadata.robotName || metadata.robot_name || metadata.name) || 'Unknown';\n  const extractedVersion = robotInfo.version || metadata && (metadata.robotVersion || metadata.robot_version || metadata.version) || 'Unknown';\n  const robotRows = [{\n    label: 'Model',\n    value: extractedModel\n  }, {\n    label: 'Name',\n    value: extractedName\n  }, {\n    label: 'Version',\n    value: extractedVersion\n  }];\n\n  // Add 3D model information if available\n  if (robotInfo.botModel) {\n    robotRows.push({\n      label: '3D Model Size',\n      value: formatBytes(robotInfo.botModel.length)\n    });\n  }\n\n  // Add any additional robot info fields\n  Object.entries(robotInfo).forEach(([key, value]) => {\n    if (!['model', 'name', 'version', 'botModel'].includes(key)) {\n      robotRows.push({\n        label: key.charAt(0).toUpperCase() + key.slice(1),\n        value: typeof value === 'object' ? JSON.stringify(value) : String(value)\n      });\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"h2\",\n        gutterBottom: true,\n        children: \"Robot Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: \"Question 1\",\n        color: \"primary\",\n        size: \"small\",\n        sx: {\n          mb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(TableBody, {\n          children: robotRows.map((row, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              component: \"th\",\n              scope: \"row\",\n              sx: {\n                fontWeight: 'bold',\n                width: '40%'\n              },\n              children: row.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: row.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), robotInfo.botModel ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mt: 2\n      },\n      children: \"3D model available for visualization\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mt: 2\n      },\n      children: \"No 3D model data found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Capabilities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 1\n        },\n        children: [robotInfo.botModel && /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"3D Model\",\n          size: \"small\",\n          color: \"success\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), robotInfo.model && /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Model Info\",\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), robotInfo.version && /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Version Info\",\n          size: \"small\",\n          color: \"secondary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_c = RobotInfoPanel;\nexport default RobotInfoPanel;\nvar _c;\n$RefreshReg$(_c, \"RobotInfoPanel\");", "map": {"version": 3, "names": ["React", "Paper", "Typography", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableRow", "Chip", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "RobotInfoPanel", "data", "robotInfo", "metadata", "formatBytes", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "extractedModel", "model", "robotModel", "robot_model", "platform", "extractedName", "name", "robotName", "robot_name", "extractedVersion", "version", "robotVersion", "robot_version", "robotRows", "label", "value", "botModel", "push", "length", "Object", "entries", "for<PERSON>ach", "key", "includes", "char<PERSON>t", "toUpperCase", "slice", "JSON", "stringify", "String", "sx", "p", "height", "children", "mb", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "size", "map", "row", "index", "scope", "fontWeight", "width", "severity", "mt", "display", "flexWrap", "gap", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/RobotInfoPanel.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Paper,\n  Typography,\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableRow,\n  Chip,\n  Alert\n} from '@mui/material';\nimport { IonLogData } from '../utils/ionParser';\n\ninterface RobotInfoPanelProps {\n  data: IonLogData;\n}\n\nconst RobotInfoPanel: React.FC<RobotInfoPanelProps> = ({ data }) => {\n  const { robotInfo, metadata } = data;\n\n  const formatBytes = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // Try to extract robot info from metadata if not found in robotInfo\n  const extractedModel = robotInfo.model ||\n                         (metadata && (metadata.robotModel || metadata.robot_model || metadata.model || metadata.platform)) ||\n                         'Unknown';\n  const extractedName = robotInfo.name ||\n                        (metadata && (metadata.robotName || metadata.robot_name || metadata.name)) ||\n                        'Unknown';\n  const extractedVersion = robotInfo.version ||\n                           (metadata && (metadata.robotVersion || metadata.robot_version || metadata.version)) ||\n                           'Unknown';\n\n  const robotRows = [\n    {\n      label: 'Model',\n      value: extractedModel\n    },\n    {\n      label: 'Name',\n      value: extractedName\n    },\n    {\n      label: 'Version',\n      value: extractedVersion\n    }\n  ];\n\n  // Add 3D model information if available\n  if (robotInfo.botModel) {\n    robotRows.push({\n      label: '3D Model Size',\n      value: formatBytes(robotInfo.botModel.length)\n    });\n  }\n\n  // Add any additional robot info fields\n  Object.entries(robotInfo).forEach(([key, value]) => {\n    if (!['model', 'name', 'version', 'botModel'].includes(key)) {\n      robotRows.push({\n        label: key.charAt(0).toUpperCase() + key.slice(1),\n        value: typeof value === 'object' ? JSON.stringify(value) : String(value)\n      });\n    }\n  });\n\n  return (\n    <Paper sx={{ p: 2, height: '100%' }}>\n      <Box sx={{ mb: 2 }}>\n        <Typography variant=\"h6\" component=\"h2\" gutterBottom>\n          Robot Information\n        </Typography>\n        <Chip\n          label=\"Question 1\"\n          color=\"primary\"\n          size=\"small\"\n          sx={{ mb: 1 }}\n        />\n      </Box>\n\n      <TableContainer>\n        <Table size=\"small\">\n          <TableBody>\n            {robotRows.map((row, index) => (\n              <TableRow key={index}>\n                <TableCell component=\"th\" scope=\"row\" sx={{ fontWeight: 'bold', width: '40%' }}>\n                  {row.label}\n                </TableCell>\n                <TableCell>\n                  {row.value}\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* 3D Model Status */}\n      {robotInfo.botModel ? (\n        <Alert severity=\"success\" sx={{ mt: 2 }}>\n          3D model available for visualization\n        </Alert>\n      ) : (\n        <Alert severity=\"info\" sx={{ mt: 2 }}>\n          No 3D model data found\n        </Alert>\n      )}\n\n      {/* Additional Robot Capabilities */}\n      <Box sx={{ mt: 2 }}>\n        <Typography variant=\"subtitle2\" gutterBottom>\n          Capabilities\n        </Typography>\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n          {robotInfo.botModel && (\n            <Chip\n              label=\"3D Model\"\n              size=\"small\"\n              color=\"success\"\n              variant=\"outlined\"\n            />\n          )}\n          {robotInfo.model && (\n            <Chip\n              label=\"Model Info\"\n              size=\"small\"\n              color=\"primary\"\n              variant=\"outlined\"\n            />\n          )}\n          {robotInfo.version && (\n            <Chip\n              label=\"Version Info\"\n              size=\"small\"\n              color=\"secondary\"\n              variant=\"outlined\"\n            />\n          )}\n        </Box>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default RobotInfoPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,IAAI,EACJC,KAAK,QACA,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOvB,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClE,MAAM;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGF,IAAI;EAEpC,MAAMG,WAAW,GAAIC,KAAa,IAAa;IAC7C,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGb,SAAS,CAACc,KAAK,IACdb,QAAQ,KAAKA,QAAQ,CAACc,UAAU,IAAId,QAAQ,CAACe,WAAW,IAAIf,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACgB,QAAQ,CAAE,IAClG,SAAS;EAChC,MAAMC,aAAa,GAAGlB,SAAS,CAACmB,IAAI,IACblB,QAAQ,KAAKA,QAAQ,CAACmB,SAAS,IAAInB,QAAQ,CAACoB,UAAU,IAAIpB,QAAQ,CAACkB,IAAI,CAAE,IAC1E,SAAS;EAC/B,MAAMG,gBAAgB,GAAGtB,SAAS,CAACuB,OAAO,IAChBtB,QAAQ,KAAKA,QAAQ,CAACuB,YAAY,IAAIvB,QAAQ,CAACwB,aAAa,IAAIxB,QAAQ,CAACsB,OAAO,CAAE,IACnF,SAAS;EAElC,MAAMG,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAEf;EACT,CAAC,EACD;IACEc,KAAK,EAAE,MAAM;IACbC,KAAK,EAAEV;EACT,CAAC,EACD;IACES,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAEN;EACT,CAAC,CACF;;EAED;EACA,IAAItB,SAAS,CAAC6B,QAAQ,EAAE;IACtBH,SAAS,CAACI,IAAI,CAAC;MACbH,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE1B,WAAW,CAACF,SAAS,CAAC6B,QAAQ,CAACE,MAAM;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACAC,MAAM,CAACC,OAAO,CAACjC,SAAS,CAAC,CAACkC,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEP,KAAK,CAAC,KAAK;IAClD,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAACQ,QAAQ,CAACD,GAAG,CAAC,EAAE;MAC3DT,SAAS,CAACI,IAAI,CAAC;QACbH,KAAK,EAAEQ,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC;QACjDX,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGY,IAAI,CAACC,SAAS,CAACb,KAAK,CAAC,GAAGc,MAAM,CAACd,KAAK;MACzE,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,oBACE/B,OAAA,CAACX,KAAK;IAACyD,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAClCjD,OAAA,CAACT,GAAG;MAACuD,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBjD,OAAA,CAACV,UAAU;QAAC6D,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAJ,QAAA,EAAC;MAErD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzD,OAAA,CAACH,IAAI;QACHiC,KAAK,EAAC,YAAY;QAClB4B,KAAK,EAAC,SAAS;QACfC,IAAI,EAAC,OAAO;QACZb,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENzD,OAAA,CAACL,cAAc;MAAAsD,QAAA,eACbjD,OAAA,CAACR,KAAK;QAACmE,IAAI,EAAC,OAAO;QAAAV,QAAA,eACjBjD,OAAA,CAACP,SAAS;UAAAwD,QAAA,EACPpB,SAAS,CAAC+B,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACxB9D,OAAA,CAACJ,QAAQ;YAAAqD,QAAA,gBACPjD,OAAA,CAACN,SAAS;cAAC0D,SAAS,EAAC,IAAI;cAACW,KAAK,EAAC,KAAK;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAM,CAAE;cAAAhB,QAAA,EAC5EY,GAAG,CAAC/B;YAAK;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACZzD,OAAA,CAACN,SAAS;cAAAuD,QAAA,EACPY,GAAG,CAAC9B;YAAK;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GANCK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAGhBtD,SAAS,CAAC6B,QAAQ,gBACjBhC,OAAA,CAACF,KAAK;MAACoE,QAAQ,EAAC,SAAS;MAACpB,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAlB,QAAA,EAAC;IAEzC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAERzD,OAAA,CAACF,KAAK;MAACoE,QAAQ,EAAC,MAAM;MAACpB,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAlB,QAAA,EAAC;IAEtC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAGDzD,OAAA,CAACT,GAAG;MAACuD,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAlB,QAAA,gBACjBjD,OAAA,CAACV,UAAU;QAAC6D,OAAO,EAAC,WAAW;QAACE,YAAY;QAAAJ,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzD,OAAA,CAACT,GAAG;QAACuD,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAArB,QAAA,GACpD9C,SAAS,CAAC6B,QAAQ,iBACjBhC,OAAA,CAACH,IAAI;UACHiC,KAAK,EAAC,UAAU;UAChB6B,IAAI,EAAC,OAAO;UACZD,KAAK,EAAC,SAAS;UACfP,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF,EACAtD,SAAS,CAACc,KAAK,iBACdjB,OAAA,CAACH,IAAI;UACHiC,KAAK,EAAC,YAAY;UAClB6B,IAAI,EAAC,OAAO;UACZD,KAAK,EAAC,SAAS;UACfP,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF,EACAtD,SAAS,CAACuB,OAAO,iBAChB1B,OAAA,CAACH,IAAI;UACHiC,KAAK,EAAC,cAAc;UACpB6B,IAAI,EAAC,OAAO;UACZD,KAAK,EAAC,WAAW;UACjBP,OAAO,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACc,EAAA,GAnIItE,cAA6C;AAqInD,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}