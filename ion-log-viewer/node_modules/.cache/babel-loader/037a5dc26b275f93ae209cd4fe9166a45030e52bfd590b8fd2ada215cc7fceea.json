{"ast": null, "code": "import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\n\n// ION value handlers for both ION DOM and plain JavaScript objects\nexport class IonValueHandler {\n  static handleValue(value) {\n    var _value$timestampValue;\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        switch (ionType) {\n          case ion.IonTypes.NULL:\n            return null;\n          case ion.IonTypes.BOOL:\n            return value.booleanValue();\n          case ion.IonTypes.INT:\n            return value.numberValue();\n          case ion.IonTypes.FLOAT:\n            return value.numberValue();\n          case ion.IonTypes.DECIMAL:\n            return value.numberValue();\n          case ion.IonTypes.TIMESTAMP:\n            return ((_value$timestampValue = value.timestampValue()) === null || _value$timestampValue === void 0 ? void 0 : _value$timestampValue.getTime()) || value.numberValue();\n          case ion.IonTypes.STRING:\n            return value.stringValue();\n          case ion.IonTypes.SYMBOL:\n            return value.stringValue();\n          case ion.IonTypes.BLOB:\n            return value.uInt8ArrayValue();\n          case ion.IonTypes.CLOB:\n            return value.stringValue();\n          case ion.IonTypes.LIST:\n            return value.elements().map(item => this.handleValue(item));\n          case ion.IonTypes.SEXP:\n            return value.elements().map(item => this.handleValue(item));\n          case ion.IonTypes.STRUCT:\n            const result = {};\n            for (const field of value.fields()) {\n              const fieldName = field.fieldName();\n              if (fieldName) {\n                result[fieldName] = this.handleValue(field);\n              }\n            }\n            return result;\n          default:\n            console.warn('Unknown ION type:', ionType);\n            return value.value ? value.value() : value;\n        }\n      } catch (error) {\n        console.warn('Error processing ION value:', error);\n        // Fall back to trying to get the raw value\n        try {\n          return value.value ? value.value() : value;\n        } catch {\n          return String(value);\n        }\n      }\n    }\n\n    // Handle plain JavaScript values\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n    if (typeof value === 'object') {\n      const result = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n    return value;\n  }\n  static isHumanReadable(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n        if (ionType === ion.IonTypes.CLOB) return true;\n        if (ionType === ion.IonTypes.LIST || ionType === ion.IonTypes.SEXP) {\n          return value.elements().every(item => this.isHumanReadable(item));\n        }\n        if (ionType === ion.IonTypes.STRUCT) {\n          return value.fields().every(field => this.isHumanReadable(field));\n        }\n        return true;\n      } catch {\n        return true; // Default to human readable if we can't determine\n      }\n    }\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data) {\n  try {\n    let parsedData;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    const processedData = IonValueHandler.handleValue(parsedData);\n    console.log('Processed data:', processedData);\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo = extractSessionInfo(processedData);\n\n    // Extract robot information\n    const robotInfo = extractRobotInfo(processedData);\n\n    // Extract topics\n    const topics = extractTopics(processedData);\n\n    // Calculate time bounds\n    const {\n      startTime,\n      endTime,\n      totalDuration\n    } = calculateTimeBounds(topics);\n    console.log('Extraction complete:', {\n      sessionInfo,\n      robotInfo,\n      topicCount: topics.length\n    });\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\nfunction extractSessionInfo(data) {\n  const sessionInfo = {};\n\n  // Look for session-related fields in various possible locations\n  if (data.session) {\n    Object.assign(sessionInfo, data.session);\n  }\n  if (data.metadata) {\n    const metadata = data.metadata;\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  // Look for session info in root level\n  if (data.startTime) sessionInfo.startTime = data.startTime;\n  if (data.endTime) sessionInfo.endTime = data.endTime;\n  if (data.duration) sessionInfo.duration = data.duration;\n  if (data.recordingDate) sessionInfo.recordingDate = data.recordingDate;\n  if (data.version) sessionInfo.version = data.version;\n\n  // Look for common ION log fields\n  if (data.header) {\n    const header = data.header;\n    if (header.startTime) sessionInfo.startTime = header.startTime;\n    if (header.endTime) sessionInfo.endTime = header.endTime;\n    if (header.duration) sessionInfo.duration = header.duration;\n  }\n  return sessionInfo;\n}\nfunction extractRobotInfo(data) {\n  const robotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, data.robot);\n  }\n  if (data.metadata) {\n    const metadata = data.metadata;\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n\n  // Look for robot info in root level\n  if (data.robotModel) robotInfo.model = data.robotModel;\n  if (data.robotName) robotInfo.name = data.robotName;\n  if (data.robotVersion) robotInfo.version = data.robotVersion;\n  if (data.botModel) robotInfo.botModel = data.botModel;\n\n  // Look for common robot fields\n  if (data.model) robotInfo.model = data.model;\n  if (data.name) robotInfo.name = data.name;\n  return robotInfo;\n}\nfunction extractTopics(data) {\n  const topics = [];\n\n  // Try different possible structures for topics\n  let topicsData = [];\n  if (data.topics && Array.isArray(data.topics)) {\n    topicsData = data.topics;\n  } else if (data.messages && Array.isArray(data.messages)) {\n    // Some ION logs might have messages directly\n    topicsData = data.messages;\n  } else if (Array.isArray(data)) {\n    // The root might be an array of topics/messages\n    topicsData = data;\n  } else {\n    // Try to find topic-like structures in the data\n    for (const [key, value] of Object.entries(data)) {\n      if (Array.isArray(value) && value.length > 0) {\n        // Check if this looks like a topic\n        const firstItem = value[0];\n        if (firstItem && typeof firstItem === 'object' && (firstItem.timestamp || firstItem.time || firstItem.header)) {\n          // This looks like a topic with messages\n          topicsData.push({\n            name: key,\n            type: 'unknown',\n            messages: value\n          });\n        }\n      }\n    }\n  }\n  for (const topicData of topicsData) {\n    const topic = topicData;\n\n    // Extract topic name and type\n    let topicName = topic.name || topic.topic || 'unknown';\n    let topicType = topic.type || topic.messageType || 'unknown';\n\n    // If this is a direct message array, use the key as topic name\n    if (!topic.name && !topic.topic && topicData === data[topicName]) {\n      // This case is handled above\n    }\n    const messages = [];\n\n    // Extract messages\n    let messagesArray = topic.messages || topic.data || [];\n    if (!Array.isArray(messagesArray) && topic.timestamp) {\n      // This might be a single message\n      messagesArray = [topic];\n    }\n    if (Array.isArray(messagesArray)) {\n      for (const msgData of messagesArray) {\n        const message = msgData;\n\n        // Extract timestamp from various possible fields\n        let timestamp = message.timestamp || message.time || message.stamp || 0;\n        if (message.header && message.header.stamp) {\n          timestamp = message.header.stamp.sec * 1000 + (message.header.stamp.nsec || 0) / 1000000;\n        }\n        messages.push({\n          timestamp: timestamp,\n          publishTime: message.publishTime || message.publish_time,\n          content: message.content || message.data || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 || messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n    if (topicName && messages.length > 0) {\n      topics.push({\n        name: topicName,\n        type: topicType,\n        frequency: topic.frequency,\n        messages,\n        isHumanReadable\n      });\n    }\n  }\n  console.log(`Extracted ${topics.length} topics:`, topics.map(t => ({\n    name: t.name,\n    messageCount: t.messages.length\n  })));\n  return topics;\n}\nfunction calculateTimeBounds(topics) {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n  const totalDuration = endTime - startTime;\n  return {\n    startTime,\n    endTime,\n    totalDuration\n  };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages, targetTime) {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages, targetTime) {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}", "map": {"version": 3, "names": ["ion", "IonValueHandler", "handleValue", "value", "_value$timestampValue", "undefined", "getType", "ionType", "IonTypes", "NULL", "BOOL", "booleanValue", "INT", "numberValue", "FLOAT", "DECIMAL", "TIMESTAMP", "timestampValue", "getTime", "STRING", "stringValue", "SYMBOL", "BLOB", "uInt8ArrayValue", "CLOB", "LIST", "elements", "map", "item", "SEXP", "STRUCT", "result", "field", "fields", "fieldName", "console", "warn", "error", "String", "Array", "isArray", "key", "val", "Object", "entries", "isHumanReadable", "every", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "values", "parseIonLog", "data", "parsedData", "log", "load", "ionError", "textData", "TextDecoder", "decode", "ionTextError", "JSON", "parse", "jsonError", "Error", "message", "processedData", "sessionInfo", "extractSessionInfo", "robotInfo", "extractRobotInfo", "topics", "extractTopics", "startTime", "endTime", "totalDuration", "calculateTimeBounds", "topicCount", "length", "metadata", "session", "assign", "duration", "recordingDate", "version", "header", "robot", "robotModel", "model", "robotName", "name", "robotVersion", "botModel", "topicsData", "messages", "firstItem", "timestamp", "time", "push", "topicData", "topic", "topicName", "topicType", "messageType", "messagesArray", "msgData", "stamp", "sec", "nsec", "publishTime", "publish_time", "content", "sort", "a", "b", "msg", "frequency", "t", "messageCount", "Infinity", "findMessageAtTime", "targetTime", "left", "right", "closest", "mid", "Math", "floor", "abs", "getMessagesUpToTime", "filter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/utils/ionParser.ts"], "sourcesContent": ["import * as ion from 'ion-js';\n\n// Define interfaces for the ION log structure\nexport interface IonMessage {\n  timestamp: number;\n  publishTime?: number;\n  content: any;\n}\n\nexport interface IonTopic {\n  name: string;\n  type: string;\n  frequency?: number;\n  messages: IonMessage[];\n  isHumanReadable: boolean;\n}\n\nexport interface IonSessionInfo {\n  startTime?: number;\n  endTime?: number;\n  duration?: number;\n  recordingDate?: string;\n  version?: string;\n  [key: string]: any;\n}\n\nexport interface IonRobotInfo {\n  model?: string;\n  name?: string;\n  version?: string;\n  botModel?: Uint8Array; // 3D model data\n  [key: string]: any;\n}\n\nexport interface IonLogData {\n  sessionInfo: IonSessionInfo;\n  robotInfo: IonRobotInfo;\n  topics: IonTopic[];\n  metadata: any;\n  totalDuration: number;\n  startTime: number;\n  endTime: number;\n}\n\n// ION value handlers for both ION DOM and plain JavaScript objects\nexport class IonValueHandler {\n  static handleValue(value: any): any {\n    if (value === null || value === undefined) {\n      return null;\n    }\n\n    // Handle ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n\n        switch (ionType) {\n          case ion.IonTypes.NULL:\n            return null;\n          case ion.IonTypes.BOOL:\n            return value.booleanValue();\n          case ion.IonTypes.INT:\n            return value.numberValue();\n          case ion.IonTypes.FLOAT:\n            return value.numberValue();\n          case ion.IonTypes.DECIMAL:\n            return value.numberValue();\n          case ion.IonTypes.TIMESTAMP:\n            return value.timestampValue()?.getTime() || value.numberValue();\n          case ion.IonTypes.STRING:\n            return value.stringValue();\n          case ion.IonTypes.SYMBOL:\n            return value.stringValue();\n          case ion.IonTypes.BLOB:\n            return value.uInt8ArrayValue();\n          case ion.IonTypes.CLOB:\n            return value.stringValue();\n          case ion.IonTypes.LIST:\n            return value.elements().map((item: any) => this.handleValue(item));\n          case ion.IonTypes.SEXP:\n            return value.elements().map((item: any) => this.handleValue(item));\n          case ion.IonTypes.STRUCT:\n            const result: any = {};\n            for (const field of value.fields()) {\n              const fieldName = field.fieldName();\n              if (fieldName) {\n                result[fieldName] = this.handleValue(field);\n              }\n            }\n            return result;\n          default:\n            console.warn('Unknown ION type:', ionType);\n            return value.value ? value.value() : value;\n        }\n      } catch (error) {\n        console.warn('Error processing ION value:', error);\n        // Fall back to trying to get the raw value\n        try {\n          return value.value ? value.value() : value;\n        } catch {\n          return String(value);\n        }\n      }\n    }\n\n    // Handle plain JavaScript values\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {\n      return value;\n    }\n\n    if (Array.isArray(value)) {\n      return value.map(item => this.handleValue(item));\n    }\n\n    if (typeof value === 'object') {\n      const result: any = {};\n      for (const [key, val] of Object.entries(value)) {\n        result[key] = this.handleValue(val);\n      }\n      return result;\n    }\n\n    return value;\n  }\n\n  static isHumanReadable(value: any): boolean {\n    if (value === null || value === undefined) return true;\n    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;\n\n    // Check for ION DOM objects\n    if (value && typeof value === 'object' && typeof value.getType === 'function') {\n      try {\n        const ionType = value.getType();\n        if (ionType === ion.IonTypes.BLOB) return false; // Binary data\n        if (ionType === ion.IonTypes.CLOB) return true;\n        if (ionType === ion.IonTypes.LIST || ionType === ion.IonTypes.SEXP) {\n          return value.elements().every((item: any) => this.isHumanReadable(item));\n        }\n        if (ionType === ion.IonTypes.STRUCT) {\n          return value.fields().every((field: any) => this.isHumanReadable(field));\n        }\n        return true;\n      } catch {\n        return true; // Default to human readable if we can't determine\n      }\n    }\n\n    // Check for binary data patterns\n    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;\n    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer\n\n    if (Array.isArray(value)) {\n      return value.every(item => this.isHumanReadable(item));\n    }\n\n    if (typeof value === 'object') {\n      return Object.values(value).every(val => this.isHumanReadable(val));\n    }\n\n    return true;\n  }\n}\n\n// Main parser function\nexport async function parseIonLog(data: Uint8Array): Promise<IonLogData> {\n  try {\n    let parsedData: any;\n\n    // First try to parse as ION binary format\n    try {\n      console.log('Attempting to parse as ION binary format...');\n      parsedData = ion.load(data);\n      console.log('Successfully parsed as ION binary');\n    } catch (ionError) {\n      console.log('ION binary parsing failed, trying text format...', ionError);\n\n      // Try as ION text format\n      try {\n        const textData = new TextDecoder().decode(data);\n        parsedData = ion.load(textData);\n        console.log('Successfully parsed as ION text');\n      } catch (ionTextError) {\n        console.log('ION text parsing failed, trying JSON...', ionTextError);\n\n        // Finally try as JSON\n        try {\n          const textData = new TextDecoder().decode(data);\n          parsedData = JSON.parse(textData);\n          console.log('Successfully parsed as JSON');\n        } catch (jsonError) {\n          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);\n        }\n      }\n    }\n\n    if (!parsedData) {\n      throw new Error('Parsed data is null or undefined');\n    }\n\n    console.log('Parsed data structure:', parsedData);\n\n    // Convert ION DOM to plain JavaScript objects\n    const processedData = IonValueHandler.handleValue(parsedData);\n    console.log('Processed data:', processedData);\n\n    if (!processedData || typeof processedData !== 'object') {\n      throw new Error('Invalid log format: root must be a struct/object');\n    }\n\n    // Extract session information\n    const sessionInfo: IonSessionInfo = extractSessionInfo(processedData);\n\n    // Extract robot information\n    const robotInfo: IonRobotInfo = extractRobotInfo(processedData);\n\n    // Extract topics\n    const topics: IonTopic[] = extractTopics(processedData);\n\n    // Calculate time bounds\n    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);\n\n    console.log('Extraction complete:', { sessionInfo, robotInfo, topicCount: topics.length });\n\n    return {\n      sessionInfo,\n      robotInfo,\n      topics,\n      metadata: processedData,\n      totalDuration,\n      startTime,\n      endTime\n    };\n  } catch (error) {\n    console.error('Parsing error:', error);\n    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\nfunction extractSessionInfo(data: any): IonSessionInfo {\n  const sessionInfo: IonSessionInfo = {};\n\n  // Look for session-related fields in various possible locations\n  if (data.session) {\n    Object.assign(sessionInfo, data.session);\n  }\n\n  if (data.metadata) {\n    const metadata = data.metadata;\n    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;\n    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;\n    if (metadata.duration) sessionInfo.duration = metadata.duration;\n    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;\n    if (metadata.version) sessionInfo.version = metadata.version;\n  }\n\n  // Look for session info in root level\n  if (data.startTime) sessionInfo.startTime = data.startTime;\n  if (data.endTime) sessionInfo.endTime = data.endTime;\n  if (data.duration) sessionInfo.duration = data.duration;\n  if (data.recordingDate) sessionInfo.recordingDate = data.recordingDate;\n  if (data.version) sessionInfo.version = data.version;\n\n  // Look for common ION log fields\n  if (data.header) {\n    const header = data.header;\n    if (header.startTime) sessionInfo.startTime = header.startTime;\n    if (header.endTime) sessionInfo.endTime = header.endTime;\n    if (header.duration) sessionInfo.duration = header.duration;\n  }\n\n  return sessionInfo;\n}\n\nfunction extractRobotInfo(data: any): IonRobotInfo {\n  const robotInfo: IonRobotInfo = {};\n\n  // Look for robot-related fields\n  if (data.robot) {\n    Object.assign(robotInfo, data.robot);\n  }\n\n  if (data.metadata) {\n    const metadata = data.metadata;\n    if (metadata.robotModel) robotInfo.model = metadata.robotModel;\n    if (metadata.robotName) robotInfo.name = metadata.robotName;\n    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;\n    if (metadata.botModel) robotInfo.botModel = metadata.botModel;\n  }\n\n  // Look for robot info in root level\n  if (data.robotModel) robotInfo.model = data.robotModel;\n  if (data.robotName) robotInfo.name = data.robotName;\n  if (data.robotVersion) robotInfo.version = data.robotVersion;\n  if (data.botModel) robotInfo.botModel = data.botModel;\n\n  // Look for common robot fields\n  if (data.model) robotInfo.model = data.model;\n  if (data.name) robotInfo.name = data.name;\n\n  return robotInfo;\n}\n\nfunction extractTopics(data: any): IonTopic[] {\n  const topics: IonTopic[] = [];\n\n  // Try different possible structures for topics\n  let topicsData: any[] = [];\n\n  if (data.topics && Array.isArray(data.topics)) {\n    topicsData = data.topics;\n  } else if (data.messages && Array.isArray(data.messages)) {\n    // Some ION logs might have messages directly\n    topicsData = data.messages;\n  } else if (Array.isArray(data)) {\n    // The root might be an array of topics/messages\n    topicsData = data;\n  } else {\n    // Try to find topic-like structures in the data\n    for (const [key, value] of Object.entries(data)) {\n      if (Array.isArray(value) && value.length > 0) {\n        // Check if this looks like a topic\n        const firstItem = value[0];\n        if (firstItem && typeof firstItem === 'object' &&\n            (firstItem.timestamp || firstItem.time || firstItem.header)) {\n          // This looks like a topic with messages\n          topicsData.push({\n            name: key,\n            type: 'unknown',\n            messages: value\n          });\n        }\n      }\n    }\n  }\n\n  for (const topicData of topicsData) {\n    const topic = topicData;\n\n    // Extract topic name and type\n    let topicName = topic.name || topic.topic || 'unknown';\n    let topicType = topic.type || topic.messageType || 'unknown';\n\n    // If this is a direct message array, use the key as topic name\n    if (!topic.name && !topic.topic && topicData === data[topicName]) {\n      // This case is handled above\n    }\n\n    const messages: IonMessage[] = [];\n\n    // Extract messages\n    let messagesArray = topic.messages || topic.data || [];\n    if (!Array.isArray(messagesArray) && topic.timestamp) {\n      // This might be a single message\n      messagesArray = [topic];\n    }\n\n    if (Array.isArray(messagesArray)) {\n      for (const msgData of messagesArray) {\n        const message = msgData;\n\n        // Extract timestamp from various possible fields\n        let timestamp = message.timestamp || message.time || message.stamp || 0;\n        if (message.header && message.header.stamp) {\n          timestamp = message.header.stamp.sec * 1000 + (message.header.stamp.nsec || 0) / 1000000;\n        }\n\n        messages.push({\n          timestamp: timestamp,\n          publishTime: message.publishTime || message.publish_time,\n          content: message.content || message.data || message\n        });\n      }\n    }\n\n    // Sort messages by timestamp\n    messages.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Determine if topic is human readable\n    const isHumanReadable = messages.length === 0 ||\n      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));\n\n    if (topicName && messages.length > 0) {\n      topics.push({\n        name: topicName,\n        type: topicType,\n        frequency: topic.frequency,\n        messages,\n        isHumanReadable\n      });\n    }\n  }\n\n  console.log(`Extracted ${topics.length} topics:`, topics.map(t => ({ name: t.name, messageCount: t.messages.length })));\n  return topics;\n}\n\nfunction calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {\n  let startTime = Infinity;\n  let endTime = -Infinity;\n\n  for (const topic of topics) {\n    for (const message of topic.messages) {\n      if (message.timestamp < startTime) {\n        startTime = message.timestamp;\n      }\n      if (message.timestamp > endTime) {\n        endTime = message.timestamp;\n      }\n    }\n  }\n\n  if (startTime === Infinity) {\n    startTime = 0;\n    endTime = 0;\n  }\n\n  const totalDuration = endTime - startTime;\n\n  return { startTime, endTime, totalDuration };\n}\n\n// Utility function to find messages at a specific time\nexport function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {\n  if (messages.length === 0) return null;\n\n  // Binary search for the closest message\n  let left = 0;\n  let right = messages.length - 1;\n  let closest = messages[0];\n\n  while (left <= right) {\n    const mid = Math.floor((left + right) / 2);\n    const message = messages[mid];\n\n    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {\n      closest = message;\n    }\n\n    if (message.timestamp < targetTime) {\n      left = mid + 1;\n    } else if (message.timestamp > targetTime) {\n      right = mid - 1;\n    } else {\n      return message;\n    }\n  }\n\n  return closest;\n}\n\n// Utility function to get all messages up to a specific time\nexport function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {\n  return messages.filter(msg => msg.timestamp <= targetTime);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;;AAE7B;;AA0CA;AACA,OAAO,MAAMC,eAAe,CAAC;EAC3B,OAAOC,WAAWA,CAACC,KAAU,EAAO;IAAA,IAAAC,qBAAA;IAClC,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;;IAEA;IACA,IAAIF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACG,OAAO,KAAK,UAAU,EAAE;MAC7E,IAAI;QACF,MAAMC,OAAO,GAAGJ,KAAK,CAACG,OAAO,CAAC,CAAC;QAE/B,QAAQC,OAAO;UACb,KAAKP,GAAG,CAACQ,QAAQ,CAACC,IAAI;YACpB,OAAO,IAAI;UACb,KAAKT,GAAG,CAACQ,QAAQ,CAACE,IAAI;YACpB,OAAOP,KAAK,CAACQ,YAAY,CAAC,CAAC;UAC7B,KAAKX,GAAG,CAACQ,QAAQ,CAACI,GAAG;YACnB,OAAOT,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACM,KAAK;YACrB,OAAOX,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACO,OAAO;YACvB,OAAOZ,KAAK,CAACU,WAAW,CAAC,CAAC;UAC5B,KAAKb,GAAG,CAACQ,QAAQ,CAACQ,SAAS;YACzB,OAAO,EAAAZ,qBAAA,GAAAD,KAAK,CAACc,cAAc,CAAC,CAAC,cAAAb,qBAAA,uBAAtBA,qBAAA,CAAwBc,OAAO,CAAC,CAAC,KAAIf,KAAK,CAACU,WAAW,CAAC,CAAC;UACjE,KAAKb,GAAG,CAACQ,QAAQ,CAACW,MAAM;YACtB,OAAOhB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACa,MAAM;YACtB,OAAOlB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACc,IAAI;YACpB,OAAOnB,KAAK,CAACoB,eAAe,CAAC,CAAC;UAChC,KAAKvB,GAAG,CAACQ,QAAQ,CAACgB,IAAI;YACpB,OAAOrB,KAAK,CAACiB,WAAW,CAAC,CAAC;UAC5B,KAAKpB,GAAG,CAACQ,QAAQ,CAACiB,IAAI;YACpB,OAAOtB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAS,IAAK,IAAI,CAAC1B,WAAW,CAAC0B,IAAI,CAAC,CAAC;UACpE,KAAK5B,GAAG,CAACQ,QAAQ,CAACqB,IAAI;YACpB,OAAO1B,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAS,IAAK,IAAI,CAAC1B,WAAW,CAAC0B,IAAI,CAAC,CAAC;UACpE,KAAK5B,GAAG,CAACQ,QAAQ,CAACsB,MAAM;YACtB,MAAMC,MAAW,GAAG,CAAC,CAAC;YACtB,KAAK,MAAMC,KAAK,IAAI7B,KAAK,CAAC8B,MAAM,CAAC,CAAC,EAAE;cAClC,MAAMC,SAAS,GAAGF,KAAK,CAACE,SAAS,CAAC,CAAC;cACnC,IAAIA,SAAS,EAAE;gBACbH,MAAM,CAACG,SAAS,CAAC,GAAG,IAAI,CAAChC,WAAW,CAAC8B,KAAK,CAAC;cAC7C;YACF;YACA,OAAOD,MAAM;UACf;YACEI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAE7B,OAAO,CAAC;YAC1C,OAAOJ,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC,GAAGA,KAAK;QAC9C;MACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACdF,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEC,KAAK,CAAC;QAClD;QACA,IAAI;UACF,OAAOlC,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC,GAAGA,KAAK;QAC5C,CAAC,CAAC,MAAM;UACN,OAAOmC,MAAM,CAACnC,KAAK,CAAC;QACtB;MACF;IACF;;IAEA;IACA,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxF,OAAOA,KAAK;IACd;IAEA,IAAIoC,KAAK,CAACC,OAAO,CAACrC,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACwB,GAAG,CAACC,IAAI,IAAI,IAAI,CAAC1B,WAAW,CAAC0B,IAAI,CAAC,CAAC;IAClD;IAEA,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM4B,MAAW,GAAG,CAAC,CAAC;MACtB,KAAK,MAAM,CAACU,GAAG,EAAEC,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACzC,KAAK,CAAC,EAAE;QAC9C4B,MAAM,CAACU,GAAG,CAAC,GAAG,IAAI,CAACvC,WAAW,CAACwC,GAAG,CAAC;MACrC;MACA,OAAOX,MAAM;IACf;IAEA,OAAO5B,KAAK;EACd;EAEA,OAAO0C,eAAeA,CAAC1C,KAAU,EAAW;IAC1C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE,OAAO,IAAI;IACtD,IAAI,OAAOF,KAAK,KAAK,SAAS,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErG;IACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACG,OAAO,KAAK,UAAU,EAAE;MAC7E,IAAI;QACF,MAAMC,OAAO,GAAGJ,KAAK,CAACG,OAAO,CAAC,CAAC;QAC/B,IAAIC,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACc,IAAI,EAAE,OAAO,KAAK,CAAC,CAAC;QACjD,IAAIf,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACgB,IAAI,EAAE,OAAO,IAAI;QAC9C,IAAIjB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACiB,IAAI,IAAIlB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACqB,IAAI,EAAE;UAClE,OAAO1B,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAACoB,KAAK,CAAElB,IAAS,IAAK,IAAI,CAACiB,eAAe,CAACjB,IAAI,CAAC,CAAC;QAC1E;QACA,IAAIrB,OAAO,KAAKP,GAAG,CAACQ,QAAQ,CAACsB,MAAM,EAAE;UACnC,OAAO3B,KAAK,CAAC8B,MAAM,CAAC,CAAC,CAACa,KAAK,CAAEd,KAAU,IAAK,IAAI,CAACa,eAAe,CAACb,KAAK,CAAC,CAAC;QAC1E;QACA,OAAO,IAAI;MACb,CAAC,CAAC,MAAM;QACN,OAAO,IAAI,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI7B,KAAK,YAAY4C,UAAU,IAAI5C,KAAK,YAAY6C,WAAW,EAAE,OAAO,KAAK;IAC7E,IAAI,OAAO7C,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC8C,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;IAExE,IAAIV,KAAK,CAACC,OAAO,CAACrC,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAAC2C,KAAK,CAAClB,IAAI,IAAI,IAAI,CAACiB,eAAe,CAACjB,IAAI,CAAC,CAAC;IACxD;IAEA,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOwC,MAAM,CAACO,MAAM,CAAC/C,KAAK,CAAC,CAAC2C,KAAK,CAACJ,GAAG,IAAI,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC,CAAC;IACrE;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,eAAeS,WAAWA,CAACC,IAAgB,EAAuB;EACvE,IAAI;IACF,IAAIC,UAAe;;IAEnB;IACA,IAAI;MACFlB,OAAO,CAACmB,GAAG,CAAC,6CAA6C,CAAC;MAC1DD,UAAU,GAAGrD,GAAG,CAACuD,IAAI,CAACH,IAAI,CAAC;MAC3BjB,OAAO,CAACmB,GAAG,CAAC,mCAAmC,CAAC;IAClD,CAAC,CAAC,OAAOE,QAAQ,EAAE;MACjBrB,OAAO,CAACmB,GAAG,CAAC,kDAAkD,EAAEE,QAAQ,CAAC;;MAEzE;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACP,IAAI,CAAC;QAC/CC,UAAU,GAAGrD,GAAG,CAACuD,IAAI,CAACE,QAAQ,CAAC;QAC/BtB,OAAO,CAACmB,GAAG,CAAC,iCAAiC,CAAC;MAChD,CAAC,CAAC,OAAOM,YAAY,EAAE;QACrBzB,OAAO,CAACmB,GAAG,CAAC,yCAAyC,EAAEM,YAAY,CAAC;;QAEpE;QACA,IAAI;UACF,MAAMH,QAAQ,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACP,IAAI,CAAC;UAC/CC,UAAU,GAAGQ,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;UACjCtB,OAAO,CAACmB,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,CAAC,OAAOS,SAAS,EAAE;UAClB,MAAM,IAAIC,KAAK,CAAC,mFAAmFD,SAAS,YAAYC,KAAK,GAAGD,SAAS,CAACE,OAAO,GAAG,eAAe,EAAE,CAAC;QACxK;MACF;IACF;IAEA,IAAI,CAACZ,UAAU,EAAE;MACf,MAAM,IAAIW,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEA7B,OAAO,CAACmB,GAAG,CAAC,wBAAwB,EAAED,UAAU,CAAC;;IAEjD;IACA,MAAMa,aAAa,GAAGjE,eAAe,CAACC,WAAW,CAACmD,UAAU,CAAC;IAC7DlB,OAAO,CAACmB,GAAG,CAAC,iBAAiB,EAAEY,aAAa,CAAC;IAE7C,IAAI,CAACA,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACvD,MAAM,IAAIF,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACA,MAAMG,WAA2B,GAAGC,kBAAkB,CAACF,aAAa,CAAC;;IAErE;IACA,MAAMG,SAAuB,GAAGC,gBAAgB,CAACJ,aAAa,CAAC;;IAE/D;IACA,MAAMK,MAAkB,GAAGC,aAAa,CAACN,aAAa,CAAC;;IAEvD;IACA,MAAM;MAAEO,SAAS;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGC,mBAAmB,CAACL,MAAM,CAAC;IAEzEpC,OAAO,CAACmB,GAAG,CAAC,sBAAsB,EAAE;MAAEa,WAAW;MAAEE,SAAS;MAAEQ,UAAU,EAAEN,MAAM,CAACO;IAAO,CAAC,CAAC;IAE1F,OAAO;MACLX,WAAW;MACXE,SAAS;MACTE,MAAM;MACNQ,QAAQ,EAAEb,aAAa;MACvBS,aAAa;MACbF,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC,OAAOrC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACtC,MAAM,IAAI2B,KAAK,CAAC,wBAAwB3B,KAAK,YAAY2B,KAAK,GAAG3B,KAAK,CAAC4B,OAAO,GAAG,eAAe,EAAE,CAAC;EACrG;AACF;AAEA,SAASG,kBAAkBA,CAAChB,IAAS,EAAkB;EACrD,MAAMe,WAA2B,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAIf,IAAI,CAAC4B,OAAO,EAAE;IAChBrC,MAAM,CAACsC,MAAM,CAACd,WAAW,EAAEf,IAAI,CAAC4B,OAAO,CAAC;EAC1C;EAEA,IAAI5B,IAAI,CAAC2B,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ;IAC9B,IAAIA,QAAQ,CAACN,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGM,QAAQ,CAACN,SAAS;IAClE,IAAIM,QAAQ,CAACL,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGK,QAAQ,CAACL,OAAO;IAC5D,IAAIK,QAAQ,CAACG,QAAQ,EAAEf,WAAW,CAACe,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;IAC/D,IAAIH,QAAQ,CAACI,aAAa,EAAEhB,WAAW,CAACgB,aAAa,GAAGJ,QAAQ,CAACI,aAAa;IAC9E,IAAIJ,QAAQ,CAACK,OAAO,EAAEjB,WAAW,CAACiB,OAAO,GAAGL,QAAQ,CAACK,OAAO;EAC9D;;EAEA;EACA,IAAIhC,IAAI,CAACqB,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGrB,IAAI,CAACqB,SAAS;EAC1D,IAAIrB,IAAI,CAACsB,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGtB,IAAI,CAACsB,OAAO;EACpD,IAAItB,IAAI,CAAC8B,QAAQ,EAAEf,WAAW,CAACe,QAAQ,GAAG9B,IAAI,CAAC8B,QAAQ;EACvD,IAAI9B,IAAI,CAAC+B,aAAa,EAAEhB,WAAW,CAACgB,aAAa,GAAG/B,IAAI,CAAC+B,aAAa;EACtE,IAAI/B,IAAI,CAACgC,OAAO,EAAEjB,WAAW,CAACiB,OAAO,GAAGhC,IAAI,CAACgC,OAAO;;EAEpD;EACA,IAAIhC,IAAI,CAACiC,MAAM,EAAE;IACf,MAAMA,MAAM,GAAGjC,IAAI,CAACiC,MAAM;IAC1B,IAAIA,MAAM,CAACZ,SAAS,EAAEN,WAAW,CAACM,SAAS,GAAGY,MAAM,CAACZ,SAAS;IAC9D,IAAIY,MAAM,CAACX,OAAO,EAAEP,WAAW,CAACO,OAAO,GAAGW,MAAM,CAACX,OAAO;IACxD,IAAIW,MAAM,CAACH,QAAQ,EAAEf,WAAW,CAACe,QAAQ,GAAGG,MAAM,CAACH,QAAQ;EAC7D;EAEA,OAAOf,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAAClB,IAAS,EAAgB;EACjD,MAAMiB,SAAuB,GAAG,CAAC,CAAC;;EAElC;EACA,IAAIjB,IAAI,CAACkC,KAAK,EAAE;IACd3C,MAAM,CAACsC,MAAM,CAACZ,SAAS,EAAEjB,IAAI,CAACkC,KAAK,CAAC;EACtC;EAEA,IAAIlC,IAAI,CAAC2B,QAAQ,EAAE;IACjB,MAAMA,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ;IAC9B,IAAIA,QAAQ,CAACQ,UAAU,EAAElB,SAAS,CAACmB,KAAK,GAAGT,QAAQ,CAACQ,UAAU;IAC9D,IAAIR,QAAQ,CAACU,SAAS,EAAEpB,SAAS,CAACqB,IAAI,GAAGX,QAAQ,CAACU,SAAS;IAC3D,IAAIV,QAAQ,CAACY,YAAY,EAAEtB,SAAS,CAACe,OAAO,GAAGL,QAAQ,CAACY,YAAY;IACpE,IAAIZ,QAAQ,CAACa,QAAQ,EAAEvB,SAAS,CAACuB,QAAQ,GAAGb,QAAQ,CAACa,QAAQ;EAC/D;;EAEA;EACA,IAAIxC,IAAI,CAACmC,UAAU,EAAElB,SAAS,CAACmB,KAAK,GAAGpC,IAAI,CAACmC,UAAU;EACtD,IAAInC,IAAI,CAACqC,SAAS,EAAEpB,SAAS,CAACqB,IAAI,GAAGtC,IAAI,CAACqC,SAAS;EACnD,IAAIrC,IAAI,CAACuC,YAAY,EAAEtB,SAAS,CAACe,OAAO,GAAGhC,IAAI,CAACuC,YAAY;EAC5D,IAAIvC,IAAI,CAACwC,QAAQ,EAAEvB,SAAS,CAACuB,QAAQ,GAAGxC,IAAI,CAACwC,QAAQ;;EAErD;EACA,IAAIxC,IAAI,CAACoC,KAAK,EAAEnB,SAAS,CAACmB,KAAK,GAAGpC,IAAI,CAACoC,KAAK;EAC5C,IAAIpC,IAAI,CAACsC,IAAI,EAAErB,SAAS,CAACqB,IAAI,GAAGtC,IAAI,CAACsC,IAAI;EAEzC,OAAOrB,SAAS;AAClB;AAEA,SAASG,aAAaA,CAACpB,IAAS,EAAc;EAC5C,MAAMmB,MAAkB,GAAG,EAAE;;EAE7B;EACA,IAAIsB,UAAiB,GAAG,EAAE;EAE1B,IAAIzC,IAAI,CAACmB,MAAM,IAAIhC,KAAK,CAACC,OAAO,CAACY,IAAI,CAACmB,MAAM,CAAC,EAAE;IAC7CsB,UAAU,GAAGzC,IAAI,CAACmB,MAAM;EAC1B,CAAC,MAAM,IAAInB,IAAI,CAAC0C,QAAQ,IAAIvD,KAAK,CAACC,OAAO,CAACY,IAAI,CAAC0C,QAAQ,CAAC,EAAE;IACxD;IACAD,UAAU,GAAGzC,IAAI,CAAC0C,QAAQ;EAC5B,CAAC,MAAM,IAAIvD,KAAK,CAACC,OAAO,CAACY,IAAI,CAAC,EAAE;IAC9B;IACAyC,UAAU,GAAGzC,IAAI;EACnB,CAAC,MAAM;IACL;IACA,KAAK,MAAM,CAACX,GAAG,EAAEtC,KAAK,CAAC,IAAIwC,MAAM,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;MAC/C,IAAIb,KAAK,CAACC,OAAO,CAACrC,KAAK,CAAC,IAAIA,KAAK,CAAC2E,MAAM,GAAG,CAAC,EAAE;QAC5C;QACA,MAAMiB,SAAS,GAAG5F,KAAK,CAAC,CAAC,CAAC;QAC1B,IAAI4F,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,KACzCA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACV,MAAM,CAAC,EAAE;UAC/D;UACAQ,UAAU,CAACK,IAAI,CAAC;YACdR,IAAI,EAAEjD,GAAG;YACTQ,IAAI,EAAE,SAAS;YACf6C,QAAQ,EAAE3F;UACZ,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA,KAAK,MAAMgG,SAAS,IAAIN,UAAU,EAAE;IAClC,MAAMO,KAAK,GAAGD,SAAS;;IAEvB;IACA,IAAIE,SAAS,GAAGD,KAAK,CAACV,IAAI,IAAIU,KAAK,CAACA,KAAK,IAAI,SAAS;IACtD,IAAIE,SAAS,GAAGF,KAAK,CAACnD,IAAI,IAAImD,KAAK,CAACG,WAAW,IAAI,SAAS;;IAE5D;IACA,IAAI,CAACH,KAAK,CAACV,IAAI,IAAI,CAACU,KAAK,CAACA,KAAK,IAAID,SAAS,KAAK/C,IAAI,CAACiD,SAAS,CAAC,EAAE;MAChE;IAAA;IAGF,MAAMP,QAAsB,GAAG,EAAE;;IAEjC;IACA,IAAIU,aAAa,GAAGJ,KAAK,CAACN,QAAQ,IAAIM,KAAK,CAAChD,IAAI,IAAI,EAAE;IACtD,IAAI,CAACb,KAAK,CAACC,OAAO,CAACgE,aAAa,CAAC,IAAIJ,KAAK,CAACJ,SAAS,EAAE;MACpD;MACAQ,aAAa,GAAG,CAACJ,KAAK,CAAC;IACzB;IAEA,IAAI7D,KAAK,CAACC,OAAO,CAACgE,aAAa,CAAC,EAAE;MAChC,KAAK,MAAMC,OAAO,IAAID,aAAa,EAAE;QACnC,MAAMvC,OAAO,GAAGwC,OAAO;;QAEvB;QACA,IAAIT,SAAS,GAAG/B,OAAO,CAAC+B,SAAS,IAAI/B,OAAO,CAACgC,IAAI,IAAIhC,OAAO,CAACyC,KAAK,IAAI,CAAC;QACvE,IAAIzC,OAAO,CAACoB,MAAM,IAAIpB,OAAO,CAACoB,MAAM,CAACqB,KAAK,EAAE;UAC1CV,SAAS,GAAG/B,OAAO,CAACoB,MAAM,CAACqB,KAAK,CAACC,GAAG,GAAG,IAAI,GAAG,CAAC1C,OAAO,CAACoB,MAAM,CAACqB,KAAK,CAACE,IAAI,IAAI,CAAC,IAAI,OAAO;QAC1F;QAEAd,QAAQ,CAACI,IAAI,CAAC;UACZF,SAAS,EAAEA,SAAS;UACpBa,WAAW,EAAE5C,OAAO,CAAC4C,WAAW,IAAI5C,OAAO,CAAC6C,YAAY;UACxDC,OAAO,EAAE9C,OAAO,CAAC8C,OAAO,IAAI9C,OAAO,CAACb,IAAI,IAAIa;QAC9C,CAAC,CAAC;MACJ;IACF;;IAEA;IACA6B,QAAQ,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,SAAS,GAAGkB,CAAC,CAAClB,SAAS,CAAC;;IAElD;IACA,MAAMnD,eAAe,GAAGiD,QAAQ,CAAChB,MAAM,KAAK,CAAC,IAC3CgB,QAAQ,CAAChD,KAAK,CAACqE,GAAG,IAAIlH,eAAe,CAAC4C,eAAe,CAACsE,GAAG,CAACJ,OAAO,CAAC,CAAC;IAErE,IAAIV,SAAS,IAAIP,QAAQ,CAAChB,MAAM,GAAG,CAAC,EAAE;MACpCP,MAAM,CAAC2B,IAAI,CAAC;QACVR,IAAI,EAAEW,SAAS;QACfpD,IAAI,EAAEqD,SAAS;QACfc,SAAS,EAAEhB,KAAK,CAACgB,SAAS;QAC1BtB,QAAQ;QACRjD;MACF,CAAC,CAAC;IACJ;EACF;EAEAV,OAAO,CAACmB,GAAG,CAAC,aAAaiB,MAAM,CAACO,MAAM,UAAU,EAAEP,MAAM,CAAC5C,GAAG,CAAC0F,CAAC,KAAK;IAAE3B,IAAI,EAAE2B,CAAC,CAAC3B,IAAI;IAAE4B,YAAY,EAAED,CAAC,CAACvB,QAAQ,CAAChB;EAAO,CAAC,CAAC,CAAC,CAAC;EACvH,OAAOP,MAAM;AACf;AAEA,SAASK,mBAAmBA,CAACL,MAAkB,EAAiE;EAC9G,IAAIE,SAAS,GAAG8C,QAAQ;EACxB,IAAI7C,OAAO,GAAG,CAAC6C,QAAQ;EAEvB,KAAK,MAAMnB,KAAK,IAAI7B,MAAM,EAAE;IAC1B,KAAK,MAAMN,OAAO,IAAImC,KAAK,CAACN,QAAQ,EAAE;MACpC,IAAI7B,OAAO,CAAC+B,SAAS,GAAGvB,SAAS,EAAE;QACjCA,SAAS,GAAGR,OAAO,CAAC+B,SAAS;MAC/B;MACA,IAAI/B,OAAO,CAAC+B,SAAS,GAAGtB,OAAO,EAAE;QAC/BA,OAAO,GAAGT,OAAO,CAAC+B,SAAS;MAC7B;IACF;EACF;EAEA,IAAIvB,SAAS,KAAK8C,QAAQ,EAAE;IAC1B9C,SAAS,GAAG,CAAC;IACbC,OAAO,GAAG,CAAC;EACb;EAEA,MAAMC,aAAa,GAAGD,OAAO,GAAGD,SAAS;EAEzC,OAAO;IAAEA,SAAS;IAAEC,OAAO;IAAEC;EAAc,CAAC;AAC9C;;AAEA;AACA,OAAO,SAAS6C,iBAAiBA,CAAC1B,QAAsB,EAAE2B,UAAkB,EAAqB;EAC/F,IAAI3B,QAAQ,CAAChB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEtC;EACA,IAAI4C,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAG7B,QAAQ,CAAChB,MAAM,GAAG,CAAC;EAC/B,IAAI8C,OAAO,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EAEzB,OAAO4B,IAAI,IAAIC,KAAK,EAAE;IACpB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC1C,MAAM1D,OAAO,GAAG6B,QAAQ,CAAC+B,GAAG,CAAC;IAE7B,IAAIC,IAAI,CAACE,GAAG,CAAC/D,OAAO,CAAC+B,SAAS,GAAGyB,UAAU,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACJ,OAAO,CAAC5B,SAAS,GAAGyB,UAAU,CAAC,EAAE;MACvFG,OAAO,GAAG3D,OAAO;IACnB;IAEA,IAAIA,OAAO,CAAC+B,SAAS,GAAGyB,UAAU,EAAE;MAClCC,IAAI,GAAGG,GAAG,GAAG,CAAC;IAChB,CAAC,MAAM,IAAI5D,OAAO,CAAC+B,SAAS,GAAGyB,UAAU,EAAE;MACzCE,KAAK,GAAGE,GAAG,GAAG,CAAC;IACjB,CAAC,MAAM;MACL,OAAO5D,OAAO;IAChB;EACF;EAEA,OAAO2D,OAAO;AAChB;;AAEA;AACA,OAAO,SAASK,mBAAmBA,CAACnC,QAAsB,EAAE2B,UAAkB,EAAgB;EAC5F,OAAO3B,QAAQ,CAACoC,MAAM,CAACf,GAAG,IAAIA,GAAG,CAACnB,SAAS,IAAIyB,UAAU,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}