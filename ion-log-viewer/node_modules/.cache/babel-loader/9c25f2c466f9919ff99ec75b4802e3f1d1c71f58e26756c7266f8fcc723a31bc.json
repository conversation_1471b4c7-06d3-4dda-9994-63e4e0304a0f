{"ast": null, "code": "import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris_indirect(bvh, side, ray, offset, count, intersections, near, far) {\n  const {\n    geometry,\n    _indirectBuffer\n  } = bvh;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    let vi = _indirectBuffer ? _indirectBuffer[i] : i;\n    intersectTri(geometry, side, ray, vi, intersections, near, far);\n  }\n}\nfunction intersectClosestTri_indirect(bvh, side, ray, offset, count, near, far) {\n  const {\n    geometry,\n    _indirectBuffer\n  } = bvh;\n  let dist = Infinity;\n  let res = null;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    let intersection;\n    intersection = intersectTri(geometry, side, ray, _indirectBuffer ? _indirectBuffer[i] : i, null, near, far);\n    if (intersection && intersection.distance < dist) {\n      res = intersection;\n      dist = intersection.distance;\n    }\n  }\n  return res;\n}\nfunction iterateOverTriangles_indirect(offset, count, bvh, intersectsTriangleFunc, contained, depth, triangle) {\n  const {\n    geometry\n  } = bvh;\n  const {\n    index\n  } = geometry;\n  const pos = geometry.attributes.position;\n  for (let i = offset, l = count + offset; i < l; i++) {\n    let tri;\n    tri = bvh.resolveTriangleIndex(i);\n    setTriangle(triangle, tri * 3, index, pos);\n    triangle.needsUpdate = true;\n    if (intersectsTriangleFunc(triangle, tri, contained, depth)) {\n      return true;\n    }\n  }\n  return false;\n}\nexport { intersectClosestTri_indirect, intersectTris_indirect, iterateOverTriangles_indirect };", "map": {"version": 3, "names": ["intersectTri", "set<PERSON>riangle", "intersectTris_indirect", "bvh", "side", "ray", "offset", "count", "intersections", "near", "far", "geometry", "_<PERSON><PERSON><PERSON>er", "i", "end", "vi", "intersectClosestTri_indirect", "dist", "Infinity", "res", "intersection", "distance", "iterateOverTriangles_indirect", "intersectsTriangleFunc", "contained", "depth", "triangle", "index", "pos", "attributes", "position", "l", "tri", "resolveTriangleIndex", "needsUpdate"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/utils/iterationUtils_indirect.generated.js"], "sourcesContent": ["import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris_indirect( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet vi = _indirectBuffer ? _indirectBuffer[ i ] : i;\n\t\tintersectTri( geometry, side, ray, vi, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\t\tintersection = intersectTri( geometry, side, ray, _indirectBuffer ? _indirectBuffer[ i ] : i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles_indirect(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\t\ttri = bvh.resolveTriangleIndex( i );\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri_indirect, intersectTris_indirect, iterateOverTriangles_indirect };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,2CAA2C;AACxE,SAASC,WAAW,QAAQ,kCAAkC;;AAE9D;AACA;AACA;AACA;;AAEA,SAASC,sBAAsBA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAE1F,MAAM;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGT,GAAG;EACzC,KAAM,IAAIU,CAAC,GAAGP,MAAM,EAAEQ,GAAG,GAAGR,MAAM,GAAGC,KAAK,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAE3D,IAAIE,EAAE,GAAGH,eAAe,GAAGA,eAAe,CAAEC,CAAC,CAAE,GAAGA,CAAC;IACnDb,YAAY,CAAEW,QAAQ,EAAEP,IAAI,EAAEC,GAAG,EAAEU,EAAE,EAAEP,aAAa,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAGlE;AAED;AAEA,SAASM,4BAA4BA,CAAEb,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEE,IAAI,EAAEC,GAAG,EAAG;EAEjF,MAAM;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGT,GAAG;EACzC,IAAIc,IAAI,GAAGC,QAAQ;EACnB,IAAIC,GAAG,GAAG,IAAI;EACd,KAAM,IAAIN,CAAC,GAAGP,MAAM,EAAEQ,GAAG,GAAGR,MAAM,GAAGC,KAAK,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAE3D,IAAIO,YAAY;IAChBA,YAAY,GAAGpB,YAAY,CAAEW,QAAQ,EAAEP,IAAI,EAAEC,GAAG,EAAEO,eAAe,GAAGA,eAAe,CAAEC,CAAC,CAAE,GAAGA,CAAC,EAAE,IAAI,EAAEJ,IAAI,EAAEC,GAAI,CAAC;IAG/G,IAAKU,YAAY,IAAIA,YAAY,CAACC,QAAQ,GAAGJ,IAAI,EAAG;MAEnDE,GAAG,GAAGC,YAAY;MAClBH,IAAI,GAAGG,YAAY,CAACC,QAAQ;IAE7B;EAED;EAEA,OAAOF,GAAG;AAEX;AAEA,SAASG,6BAA6BA,CACrChB,MAAM,EACNC,KAAK,EACLJ,GAAG,EACHoB,sBAAsB,EACtBC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACP;EAED,MAAM;IAAEf;EAAS,CAAC,GAAGR,GAAG;EACxB,MAAM;IAAEwB;EAAM,CAAC,GAAGhB,QAAQ;EAC1B,MAAMiB,GAAG,GAAGjB,QAAQ,CAACkB,UAAU,CAACC,QAAQ;EACxC,KAAM,IAAIjB,CAAC,GAAGP,MAAM,EAAEyB,CAAC,GAAGxB,KAAK,GAAGD,MAAM,EAAEO,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAG,EAAG;IAEvD,IAAImB,GAAG;IACPA,GAAG,GAAG7B,GAAG,CAAC8B,oBAAoB,CAAEpB,CAAE,CAAC;IAEnCZ,WAAW,CAAEyB,QAAQ,EAAEM,GAAG,GAAG,CAAC,EAAEL,KAAK,EAAEC,GAAI,CAAC;IAC5CF,QAAQ,CAACQ,WAAW,GAAG,IAAI;IAE3B,IAAKX,sBAAsB,CAAEG,QAAQ,EAAEM,GAAG,EAAER,SAAS,EAAEC,KAAM,CAAC,EAAG;MAEhE,OAAO,IAAI;IAEZ;EAED;EAEA,OAAO,KAAK;AAEb;AAEA,SAAST,4BAA4B,EAAEd,sBAAsB,EAAEoB,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}