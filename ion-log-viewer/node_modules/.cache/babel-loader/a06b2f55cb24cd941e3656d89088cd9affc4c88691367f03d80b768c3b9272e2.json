{"ast": null, "code": "import { BufferGeometry, InterleavedBuffer, InterleavedBufferAttribute, Mesh, MeshBasicMaterial, Vector3, Texture, NearestFilter, RawShaderMaterial, Color, Vector2, AdditiveBlending, Box2, Vector4 } from \"three\";\nconst Lensflare = /* @__PURE__ */(() => {\n  class Lensflare2 extends Mesh {\n    constructor() {\n      super(Lensflare2.Geometry, new MeshBasicMaterial({\n        opacity: 0,\n        transparent: true\n      }));\n      this.isLensflare = true;\n      this.type = \"Lensflare\";\n      this.frustumCulled = false;\n      this.renderOrder = Infinity;\n      const positionScreen = new Vector3();\n      const positionView = new Vector3();\n      const tempMap = new Texture({\n        width: 16,\n        height: 16\n      });\n      tempMap.isFramebufferTexture = true;\n      tempMap.magFilter = NearestFilter;\n      tempMap.minFilter = NearestFilter;\n      tempMap.generateMipmaps = false;\n      tempMap.needsUpdate = true;\n      const occlusionMap = new Texture({\n        width: 16,\n        height: 16\n      });\n      occlusionMap.isFramebufferTexture = true;\n      occlusionMap.magFilter = NearestFilter;\n      occlusionMap.minFilter = NearestFilter;\n      occlusionMap.generateMipmaps = false;\n      occlusionMap.needsUpdate = true;\n      const geometry = Lensflare2.Geometry;\n      const material1a = new RawShaderMaterial({\n        uniforms: {\n          scale: {\n            value: null\n          },\n          screenPosition: {\n            value: null\n          }\n        },\n        vertexShader: (/* glsl */\n        `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`),\n        fragmentShader: (/* glsl */\n        `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = vec4( 1.0, 0.0, 1.0, 1.0 );\n\n\t\t\t\t}`),\n        depthTest: true,\n        depthWrite: false,\n        transparent: false\n      });\n      const material1b = new RawShaderMaterial({\n        uniforms: {\n          map: {\n            value: tempMap\n          },\n          scale: {\n            value: null\n          },\n          screenPosition: {\n            value: null\n          }\n        },\n        vertexShader: (/* glsl */\n        `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\t\t\t\tattribute vec2 uv;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUV = uv;\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`),\n        fragmentShader: (/* glsl */\n        `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform sampler2D map;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = texture2D( map, vUV );\n\n\t\t\t\t}`),\n        depthTest: false,\n        depthWrite: false,\n        transparent: false\n      });\n      const mesh1 = new Mesh(geometry, material1a);\n      const elements = [];\n      const shader = LensflareElement.Shader;\n      const material2 = new RawShaderMaterial({\n        uniforms: {\n          map: {\n            value: null\n          },\n          occlusionMap: {\n            value: occlusionMap\n          },\n          color: {\n            value: new Color(16777215)\n          },\n          scale: {\n            value: new Vector2()\n          },\n          screenPosition: {\n            value: new Vector3()\n          }\n        },\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        blending: AdditiveBlending,\n        transparent: true,\n        depthWrite: false\n      });\n      const mesh2 = new Mesh(geometry, material2);\n      this.addElement = function (element) {\n        elements.push(element);\n      };\n      const scale = new Vector2();\n      const screenPositionPixels = new Vector2();\n      const validArea = new Box2();\n      const viewport = new Vector4();\n      this.onBeforeRender = function (renderer, scene, camera) {\n        renderer.getCurrentViewport(viewport);\n        const invAspect = viewport.w / viewport.z;\n        const halfViewportWidth = viewport.z / 2;\n        const halfViewportHeight = viewport.w / 2;\n        let size = 16 / viewport.w;\n        scale.set(size * invAspect, size);\n        validArea.min.set(viewport.x, viewport.y);\n        validArea.max.set(viewport.x + (viewport.z - 16), viewport.y + (viewport.w - 16));\n        positionView.setFromMatrixPosition(this.matrixWorld);\n        positionView.applyMatrix4(camera.matrixWorldInverse);\n        if (positionView.z > 0) return;\n        positionScreen.copy(positionView).applyMatrix4(camera.projectionMatrix);\n        screenPositionPixels.x = viewport.x + positionScreen.x * halfViewportWidth + halfViewportWidth - 8;\n        screenPositionPixels.y = viewport.y + positionScreen.y * halfViewportHeight + halfViewportHeight - 8;\n        if (validArea.containsPoint(screenPositionPixels)) {\n          renderer.copyFramebufferToTexture(screenPositionPixels, tempMap);\n          let uniforms = material1a.uniforms;\n          uniforms[\"scale\"].value = scale;\n          uniforms[\"screenPosition\"].value = positionScreen;\n          renderer.renderBufferDirect(camera, null, geometry, material1a, mesh1, null);\n          renderer.copyFramebufferToTexture(screenPositionPixels, occlusionMap);\n          uniforms = material1b.uniforms;\n          uniforms[\"scale\"].value = scale;\n          uniforms[\"screenPosition\"].value = positionScreen;\n          renderer.renderBufferDirect(camera, null, geometry, material1b, mesh1, null);\n          const vecX = -positionScreen.x * 2;\n          const vecY = -positionScreen.y * 2;\n          for (let i = 0, l = elements.length; i < l; i++) {\n            const element = elements[i];\n            const uniforms2 = material2.uniforms;\n            uniforms2[\"color\"].value.copy(element.color);\n            uniforms2[\"map\"].value = element.texture;\n            uniforms2[\"screenPosition\"].value.x = positionScreen.x + vecX * element.distance;\n            uniforms2[\"screenPosition\"].value.y = positionScreen.y + vecY * element.distance;\n            size = element.size / viewport.w;\n            const invAspect2 = viewport.w / viewport.z;\n            uniforms2[\"scale\"].value.set(size * invAspect2, size);\n            material2.uniformsNeedUpdate = true;\n            renderer.renderBufferDirect(camera, null, geometry, material2, mesh2, null);\n          }\n        }\n      };\n      this.dispose = function () {\n        material1a.dispose();\n        material1b.dispose();\n        material2.dispose();\n        tempMap.dispose();\n        occlusionMap.dispose();\n        for (let i = 0, l = elements.length; i < l; i++) {\n          elements[i].texture.dispose();\n        }\n      };\n    }\n  }\n  const _geometry = new BufferGeometry();\n  const interleavedBuffer = new InterleavedBuffer(new Float32Array([-1, -1, 0, 0, 0, 1, -1, 0, 1, 0, 1, 1, 0, 1, 1, -1, 1, 0, 0, 1]), 5);\n  _geometry.setIndex([0, 1, 2, 0, 2, 3]);\n  _geometry.setAttribute(\"position\", new InterleavedBufferAttribute(interleavedBuffer, 3, 0, false));\n  _geometry.setAttribute(\"uv\", new InterleavedBufferAttribute(interleavedBuffer, 2, 3, false));\n  Lensflare2.Geometry = _geometry;\n  return Lensflare2;\n})();\nconst LensflareElement = /* @__PURE__ */(() => {\n  class LensflareElement2 {\n    constructor(texture, size = 1, distance = 0, color = new Color(16777215)) {\n      this.texture = texture;\n      this.size = size;\n      this.distance = distance;\n      this.color = color;\n    }\n  }\n  LensflareElement2.Shader = {\n    uniforms: {\n      map: {\n        value: null\n      },\n      occlusionMap: {\n        value: null\n      },\n      color: {\n        value: null\n      },\n      scale: {\n        value: null\n      },\n      screenPosition: {\n        value: null\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\n      precision highp float;\n\n      uniform vec3 screenPosition;\n      uniform vec2 scale;\n\n      uniform sampler2D occlusionMap;\n\n      attribute vec3 position;\n      attribute vec2 uv;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vUV = uv;\n\n        vec2 pos = position.xy;\n\n        vec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.5 ) );\n\n        vVisibility =        visibility.r / 9.0;\n        vVisibility *= 1.0 - visibility.g / 9.0;\n        vVisibility *=       visibility.b / 9.0;\n\n        gl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n\n      }\n    `),\n    fragmentShader: (/* glsl */\n    `\n\n      precision highp float;\n\n      uniform sampler2D map;\n      uniform vec3 color;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vec4 texture = texture2D( map, vUV );\n        texture.a *= vVisibility;\n        gl_FragColor = texture;\n        gl_FragColor.rgb *= color;\n\n      }\n    `)\n  };\n  return LensflareElement2;\n})();\nexport { Lensflare, LensflareElement };", "map": {"version": 3, "names": ["Lensflare", "Lensflare2", "<PERSON><PERSON>", "constructor", "Geometry", "MeshBasicMaterial", "opacity", "transparent", "isLensflare", "type", "frustumCulled", "renderOrder", "Infinity", "positionScreen", "Vector3", "position<PERSON>iew", "tempMap", "Texture", "width", "height", "isFramebufferTexture", "magFilter", "NearestFilter", "minFilter", "generateMipmaps", "needsUpdate", "occlusionMap", "geometry", "material1a", "RawShaderMaterial", "uniforms", "scale", "value", "screenPosition", "vertexShader", "fragmentShader", "depthTest", "depthWrite", "material1b", "map", "mesh1", "elements", "shader", "LensflareElement", "Shader", "material2", "color", "Color", "Vector2", "blending", "AdditiveBlending", "mesh2", "addElement", "element", "push", "screenPositionPixels", "validArea", "Box2", "viewport", "Vector4", "onBeforeRender", "renderer", "scene", "camera", "getCurrentViewport", "invAspect", "w", "z", "halfViewportWidth", "halfViewportHeight", "size", "set", "min", "x", "y", "max", "setFromMatrixPosition", "matrixWorld", "applyMatrix4", "matrixWorldInverse", "copy", "projectionMatrix", "containsPoint", "copyFramebufferToTexture", "renderBufferDirect", "vecX", "vecY", "i", "l", "length", "uniforms2", "texture", "distance", "invAspect2", "uniformsNeedUpdate", "dispose", "_geometry", "BufferGeometry", "interleavedBuffer", "InterleavedBuffer", "Float32Array", "setIndex", "setAttribute", "InterleavedBufferAttribute", "LensflareElement2"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/objects/Lensflare.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  Box2,\n  BufferGeometry,\n  Color,\n  Texture,\n  NearestFilter,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  Mesh,\n  MeshBasicMaterial,\n  RawShaderMaterial,\n  Vector2,\n  Vector3,\n  Vector4,\n} from 'three'\n\nconst Lensflare = /* @__PURE__ */ (() => {\n  class Lensflare extends Mesh {\n    constructor() {\n      super(Lensflare.Geometry, new MeshBasicMaterial({ opacity: 0, transparent: true }))\n\n      this.isLensflare = true\n\n      this.type = 'Lensflare'\n      this.frustumCulled = false\n      this.renderOrder = Infinity\n\n      //\n\n      const positionScreen = new Vector3()\n      const positionView = new Vector3()\n\n      // textures\n      const tempMap = new Texture({ width: 16, height: 16 })\n      tempMap.isFramebufferTexture = true\n      tempMap.magFilter = NearestFilter\n      tempMap.minFilter = NearestFilter\n      tempMap.generateMipmaps = false\n      tempMap.needsUpdate = true\n\n      const occlusionMap = new Texture({ width: 16, height: 16 })\n      occlusionMap.isFramebufferTexture = true\n      occlusionMap.magFilter = NearestFilter\n      occlusionMap.minFilter = NearestFilter\n      occlusionMap.generateMipmaps = false\n      occlusionMap.needsUpdate = true\n\n      // material\n\n      const geometry = Lensflare.Geometry\n\n      const material1a = new RawShaderMaterial({\n        uniforms: {\n          scale: { value: null },\n          screenPosition: { value: null },\n        },\n        vertexShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n        fragmentShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = vec4( 1.0, 0.0, 1.0, 1.0 );\n\n\t\t\t\t}`,\n        depthTest: true,\n        depthWrite: false,\n        transparent: false,\n      })\n\n      const material1b = new RawShaderMaterial({\n        uniforms: {\n          map: { value: tempMap },\n          scale: { value: null },\n          screenPosition: { value: null },\n        },\n        vertexShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\t\t\t\tattribute vec2 uv;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUV = uv;\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n        fragmentShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform sampler2D map;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = texture2D( map, vUV );\n\n\t\t\t\t}`,\n        depthTest: false,\n        depthWrite: false,\n        transparent: false,\n      })\n\n      // the following object is used for occlusionMap generation\n\n      const mesh1 = new Mesh(geometry, material1a)\n\n      //\n\n      const elements = []\n\n      const shader = LensflareElement.Shader\n\n      const material2 = new RawShaderMaterial({\n        uniforms: {\n          map: { value: null },\n          occlusionMap: { value: occlusionMap },\n          color: { value: new Color(0xffffff) },\n          scale: { value: new Vector2() },\n          screenPosition: { value: new Vector3() },\n        },\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        blending: AdditiveBlending,\n        transparent: true,\n        depthWrite: false,\n      })\n\n      const mesh2 = new Mesh(geometry, material2)\n\n      this.addElement = function (element) {\n        elements.push(element)\n      }\n\n      //\n\n      const scale = new Vector2()\n      const screenPositionPixels = new Vector2()\n      const validArea = new Box2()\n      const viewport = new Vector4()\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        renderer.getCurrentViewport(viewport)\n\n        const invAspect = viewport.w / viewport.z\n        const halfViewportWidth = viewport.z / 2.0\n        const halfViewportHeight = viewport.w / 2.0\n\n        let size = 16 / viewport.w\n        scale.set(size * invAspect, size)\n\n        validArea.min.set(viewport.x, viewport.y)\n        validArea.max.set(viewport.x + (viewport.z - 16), viewport.y + (viewport.w - 16))\n\n        // calculate position in screen space\n\n        positionView.setFromMatrixPosition(this.matrixWorld)\n        positionView.applyMatrix4(camera.matrixWorldInverse)\n\n        if (positionView.z > 0) return // lensflare is behind the camera\n\n        positionScreen.copy(positionView).applyMatrix4(camera.projectionMatrix)\n\n        // horizontal and vertical coordinate of the lower left corner of the pixels to copy\n\n        screenPositionPixels.x = viewport.x + positionScreen.x * halfViewportWidth + halfViewportWidth - 8\n        screenPositionPixels.y = viewport.y + positionScreen.y * halfViewportHeight + halfViewportHeight - 8\n\n        // screen cull\n\n        if (validArea.containsPoint(screenPositionPixels)) {\n          // save current RGB to temp texture\n\n          renderer.copyFramebufferToTexture(screenPositionPixels, tempMap)\n\n          // render pink quad\n\n          let uniforms = material1a.uniforms\n          uniforms['scale'].value = scale\n          uniforms['screenPosition'].value = positionScreen\n\n          renderer.renderBufferDirect(camera, null, geometry, material1a, mesh1, null)\n\n          // copy result to occlusionMap\n\n          renderer.copyFramebufferToTexture(screenPositionPixels, occlusionMap)\n\n          // restore graphics\n\n          uniforms = material1b.uniforms\n          uniforms['scale'].value = scale\n          uniforms['screenPosition'].value = positionScreen\n\n          renderer.renderBufferDirect(camera, null, geometry, material1b, mesh1, null)\n\n          // render elements\n\n          const vecX = -positionScreen.x * 2\n          const vecY = -positionScreen.y * 2\n\n          for (let i = 0, l = elements.length; i < l; i++) {\n            const element = elements[i]\n\n            const uniforms = material2.uniforms\n\n            uniforms['color'].value.copy(element.color)\n            uniforms['map'].value = element.texture\n            uniforms['screenPosition'].value.x = positionScreen.x + vecX * element.distance\n            uniforms['screenPosition'].value.y = positionScreen.y + vecY * element.distance\n\n            size = element.size / viewport.w\n            const invAspect = viewport.w / viewport.z\n\n            uniforms['scale'].value.set(size * invAspect, size)\n\n            material2.uniformsNeedUpdate = true\n\n            renderer.renderBufferDirect(camera, null, geometry, material2, mesh2, null)\n          }\n        }\n      }\n\n      this.dispose = function () {\n        material1a.dispose()\n        material1b.dispose()\n        material2.dispose()\n\n        tempMap.dispose()\n        occlusionMap.dispose()\n\n        for (let i = 0, l = elements.length; i < l; i++) {\n          elements[i].texture.dispose()\n        }\n      }\n    }\n  }\n\n  const _geometry = new BufferGeometry()\n  const interleavedBuffer = new InterleavedBuffer(\n    new Float32Array([-1, -1, 0, 0, 0, 1, -1, 0, 1, 0, 1, 1, 0, 1, 1, -1, 1, 0, 0, 1]),\n    5,\n  )\n\n  _geometry.setIndex([0, 1, 2, 0, 2, 3])\n  _geometry.setAttribute('position', new InterleavedBufferAttribute(interleavedBuffer, 3, 0, false))\n  _geometry.setAttribute('uv', new InterleavedBufferAttribute(interleavedBuffer, 2, 3, false))\n\n  Lensflare.Geometry = _geometry\n\n  return Lensflare\n})()\n\n//\n\nconst LensflareElement = /* @__PURE__ */ (() => {\n  class LensflareElement {\n    constructor(texture, size = 1, distance = 0, color = new Color(0xffffff)) {\n      this.texture = texture\n      this.size = size\n      this.distance = distance\n      this.color = color\n    }\n  }\n\n  LensflareElement.Shader = {\n    uniforms: {\n      map: { value: null },\n      occlusionMap: { value: null },\n      color: { value: null },\n      scale: { value: null },\n      screenPosition: { value: null },\n    },\n\n    vertexShader: /* glsl */ `\n\n      precision highp float;\n\n      uniform vec3 screenPosition;\n      uniform vec2 scale;\n\n      uniform sampler2D occlusionMap;\n\n      attribute vec3 position;\n      attribute vec2 uv;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vUV = uv;\n\n        vec2 pos = position.xy;\n\n        vec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.5 ) );\n\n        vVisibility =        visibility.r / 9.0;\n        vVisibility *= 1.0 - visibility.g / 9.0;\n        vVisibility *=       visibility.b / 9.0;\n\n        gl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n\n      }\n    `,\n\n    fragmentShader: /* glsl */ `\n\n      precision highp float;\n\n      uniform sampler2D map;\n      uniform vec3 color;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vec4 texture = texture2D( map, vUV );\n        texture.a *= vVisibility;\n        gl_FragColor = texture;\n        gl_FragColor.rgb *= color;\n\n      }\n    `,\n  }\n\n  return LensflareElement\n})()\n\nexport { Lensflare, LensflareElement }\n"], "mappings": ";AAiBK,MAACA,SAAA,GAA6B,sBAAM;EACvC,MAAMC,UAAA,SAAkBC,IAAA,CAAK;IAC3BC,YAAA,EAAc;MACZ,MAAMF,UAAA,CAAUG,QAAA,EAAU,IAAIC,iBAAA,CAAkB;QAAEC,OAAA,EAAS;QAAGC,WAAA,EAAa;MAAI,CAAE,CAAC;MAElF,KAAKC,WAAA,GAAc;MAEnB,KAAKC,IAAA,GAAO;MACZ,KAAKC,aAAA,GAAgB;MACrB,KAAKC,WAAA,GAAcC,QAAA;MAInB,MAAMC,cAAA,GAAiB,IAAIC,OAAA,CAAS;MACpC,MAAMC,YAAA,GAAe,IAAID,OAAA,CAAS;MAGlC,MAAME,OAAA,GAAU,IAAIC,OAAA,CAAQ;QAAEC,KAAA,EAAO;QAAIC,MAAA,EAAQ;MAAA,CAAI;MACrDH,OAAA,CAAQI,oBAAA,GAAuB;MAC/BJ,OAAA,CAAQK,SAAA,GAAYC,aAAA;MACpBN,OAAA,CAAQO,SAAA,GAAYD,aAAA;MACpBN,OAAA,CAAQQ,eAAA,GAAkB;MAC1BR,OAAA,CAAQS,WAAA,GAAc;MAEtB,MAAMC,YAAA,GAAe,IAAIT,OAAA,CAAQ;QAAEC,KAAA,EAAO;QAAIC,MAAA,EAAQ;MAAA,CAAI;MAC1DO,YAAA,CAAaN,oBAAA,GAAuB;MACpCM,YAAA,CAAaL,SAAA,GAAYC,aAAA;MACzBI,YAAA,CAAaH,SAAA,GAAYD,aAAA;MACzBI,YAAA,CAAaF,eAAA,GAAkB;MAC/BE,YAAA,CAAaD,WAAA,GAAc;MAI3B,MAAME,QAAA,GAAW1B,UAAA,CAAUG,QAAA;MAE3B,MAAMwB,UAAA,GAAa,IAAIC,iBAAA,CAAkB;QACvCC,QAAA,EAAU;UACRC,KAAA,EAAO;YAAEC,KAAA,EAAO;UAAM;UACtBC,cAAA,EAAgB;YAAED,KAAA,EAAO;UAAM;QAChC;QACDE,YAAA;QAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;QAezBC,cAAA;QAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;QAS3BC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZ9B,WAAA,EAAa;MACrB,CAAO;MAED,MAAM+B,UAAA,GAAa,IAAIT,iBAAA,CAAkB;QACvCC,QAAA,EAAU;UACRS,GAAA,EAAK;YAAEP,KAAA,EAAOhB;UAAS;UACvBe,KAAA,EAAO;YAAEC,KAAA,EAAO;UAAM;UACtBC,cAAA,EAAgB;YAAED,KAAA,EAAO;UAAM;QAChC;QACDE,YAAA;QAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;QAoBzBC,cAAA;QAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;QAa3BC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZ9B,WAAA,EAAa;MACrB,CAAO;MAID,MAAMiC,KAAA,GAAQ,IAAItC,IAAA,CAAKyB,QAAA,EAAUC,UAAU;MAI3C,MAAMa,QAAA,GAAW,EAAE;MAEnB,MAAMC,MAAA,GAASC,gBAAA,CAAiBC,MAAA;MAEhC,MAAMC,SAAA,GAAY,IAAIhB,iBAAA,CAAkB;QACtCC,QAAA,EAAU;UACRS,GAAA,EAAK;YAAEP,KAAA,EAAO;UAAM;UACpBN,YAAA,EAAc;YAAEM,KAAA,EAAON;UAAc;UACrCoB,KAAA,EAAO;YAAEd,KAAA,EAAO,IAAIe,KAAA,CAAM,QAAQ;UAAG;UACrChB,KAAA,EAAO;YAAEC,KAAA,EAAO,IAAIgB,OAAA;UAAW;UAC/Bf,cAAA,EAAgB;YAAED,KAAA,EAAO,IAAIlB,OAAA;UAAW;QACzC;QACDoB,YAAA,EAAcQ,MAAA,CAAOR,YAAA;QACrBC,cAAA,EAAgBO,MAAA,CAAOP,cAAA;QACvBc,QAAA,EAAUC,gBAAA;QACV3C,WAAA,EAAa;QACb8B,UAAA,EAAY;MACpB,CAAO;MAED,MAAMc,KAAA,GAAQ,IAAIjD,IAAA,CAAKyB,QAAA,EAAUkB,SAAS;MAE1C,KAAKO,UAAA,GAAa,UAAUC,OAAA,EAAS;QACnCZ,QAAA,CAASa,IAAA,CAAKD,OAAO;MACtB;MAID,MAAMtB,KAAA,GAAQ,IAAIiB,OAAA,CAAS;MAC3B,MAAMO,oBAAA,GAAuB,IAAIP,OAAA,CAAS;MAC1C,MAAMQ,SAAA,GAAY,IAAIC,IAAA,CAAM;MAC5B,MAAMC,QAAA,GAAW,IAAIC,OAAA,CAAS;MAE9B,KAAKC,cAAA,GAAiB,UAAUC,QAAA,EAAUC,KAAA,EAAOC,MAAA,EAAQ;QACvDF,QAAA,CAASG,kBAAA,CAAmBN,QAAQ;QAEpC,MAAMO,SAAA,GAAYP,QAAA,CAASQ,CAAA,GAAIR,QAAA,CAASS,CAAA;QACxC,MAAMC,iBAAA,GAAoBV,QAAA,CAASS,CAAA,GAAI;QACvC,MAAME,kBAAA,GAAqBX,QAAA,CAASQ,CAAA,GAAI;QAExC,IAAII,IAAA,GAAO,KAAKZ,QAAA,CAASQ,CAAA;QACzBnC,KAAA,CAAMwC,GAAA,CAAID,IAAA,GAAOL,SAAA,EAAWK,IAAI;QAEhCd,SAAA,CAAUgB,GAAA,CAAID,GAAA,CAAIb,QAAA,CAASe,CAAA,EAAGf,QAAA,CAASgB,CAAC;QACxClB,SAAA,CAAUmB,GAAA,CAAIJ,GAAA,CAAIb,QAAA,CAASe,CAAA,IAAKf,QAAA,CAASS,CAAA,GAAI,KAAKT,QAAA,CAASgB,CAAA,IAAKhB,QAAA,CAASQ,CAAA,GAAI,GAAG;QAIhFnD,YAAA,CAAa6D,qBAAA,CAAsB,KAAKC,WAAW;QACnD9D,YAAA,CAAa+D,YAAA,CAAaf,MAAA,CAAOgB,kBAAkB;QAEnD,IAAIhE,YAAA,CAAaoD,CAAA,GAAI,GAAG;QAExBtD,cAAA,CAAemE,IAAA,CAAKjE,YAAY,EAAE+D,YAAA,CAAaf,MAAA,CAAOkB,gBAAgB;QAItE1B,oBAAA,CAAqBkB,CAAA,GAAIf,QAAA,CAASe,CAAA,GAAI5D,cAAA,CAAe4D,CAAA,GAAIL,iBAAA,GAAoBA,iBAAA,GAAoB;QACjGb,oBAAA,CAAqBmB,CAAA,GAAIhB,QAAA,CAASgB,CAAA,GAAI7D,cAAA,CAAe6D,CAAA,GAAIL,kBAAA,GAAqBA,kBAAA,GAAqB;QAInG,IAAIb,SAAA,CAAU0B,aAAA,CAAc3B,oBAAoB,GAAG;UAGjDM,QAAA,CAASsB,wBAAA,CAAyB5B,oBAAA,EAAsBvC,OAAO;UAI/D,IAAIc,QAAA,GAAWF,UAAA,CAAWE,QAAA;UAC1BA,QAAA,CAAS,OAAO,EAAEE,KAAA,GAAQD,KAAA;UAC1BD,QAAA,CAAS,gBAAgB,EAAEE,KAAA,GAAQnB,cAAA;UAEnCgD,QAAA,CAASuB,kBAAA,CAAmBrB,MAAA,EAAQ,MAAMpC,QAAA,EAAUC,UAAA,EAAYY,KAAA,EAAO,IAAI;UAI3EqB,QAAA,CAASsB,wBAAA,CAAyB5B,oBAAA,EAAsB7B,YAAY;UAIpEI,QAAA,GAAWQ,UAAA,CAAWR,QAAA;UACtBA,QAAA,CAAS,OAAO,EAAEE,KAAA,GAAQD,KAAA;UAC1BD,QAAA,CAAS,gBAAgB,EAAEE,KAAA,GAAQnB,cAAA;UAEnCgD,QAAA,CAASuB,kBAAA,CAAmBrB,MAAA,EAAQ,MAAMpC,QAAA,EAAUW,UAAA,EAAYE,KAAA,EAAO,IAAI;UAI3E,MAAM6C,IAAA,GAAO,CAACxE,cAAA,CAAe4D,CAAA,GAAI;UACjC,MAAMa,IAAA,GAAO,CAACzE,cAAA,CAAe6D,CAAA,GAAI;UAEjC,SAASa,CAAA,GAAI,GAAGC,CAAA,GAAI/C,QAAA,CAASgD,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YAC/C,MAAMlC,OAAA,GAAUZ,QAAA,CAAS8C,CAAC;YAE1B,MAAMG,SAAA,GAAW7C,SAAA,CAAUf,QAAA;YAE3B4D,SAAA,CAAS,OAAO,EAAE1D,KAAA,CAAMgD,IAAA,CAAK3B,OAAA,CAAQP,KAAK;YAC1C4C,SAAA,CAAS,KAAK,EAAE1D,KAAA,GAAQqB,OAAA,CAAQsC,OAAA;YAChCD,SAAA,CAAS,gBAAgB,EAAE1D,KAAA,CAAMyC,CAAA,GAAI5D,cAAA,CAAe4D,CAAA,GAAIY,IAAA,GAAOhC,OAAA,CAAQuC,QAAA;YACvEF,SAAA,CAAS,gBAAgB,EAAE1D,KAAA,CAAM0C,CAAA,GAAI7D,cAAA,CAAe6D,CAAA,GAAIY,IAAA,GAAOjC,OAAA,CAAQuC,QAAA;YAEvEtB,IAAA,GAAOjB,OAAA,CAAQiB,IAAA,GAAOZ,QAAA,CAASQ,CAAA;YAC/B,MAAM2B,UAAA,GAAYnC,QAAA,CAASQ,CAAA,GAAIR,QAAA,CAASS,CAAA;YAExCuB,SAAA,CAAS,OAAO,EAAE1D,KAAA,CAAMuC,GAAA,CAAID,IAAA,GAAOuB,UAAA,EAAWvB,IAAI;YAElDzB,SAAA,CAAUiD,kBAAA,GAAqB;YAE/BjC,QAAA,CAASuB,kBAAA,CAAmBrB,MAAA,EAAQ,MAAMpC,QAAA,EAAUkB,SAAA,EAAWM,KAAA,EAAO,IAAI;UAC3E;QACF;MACF;MAED,KAAK4C,OAAA,GAAU,YAAY;QACzBnE,UAAA,CAAWmE,OAAA,CAAS;QACpBzD,UAAA,CAAWyD,OAAA,CAAS;QACpBlD,SAAA,CAAUkD,OAAA,CAAS;QAEnB/E,OAAA,CAAQ+E,OAAA,CAAS;QACjBrE,YAAA,CAAaqE,OAAA,CAAS;QAEtB,SAASR,CAAA,GAAI,GAAGC,CAAA,GAAI/C,QAAA,CAASgD,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAC/C9C,QAAA,CAAS8C,CAAC,EAAEI,OAAA,CAAQI,OAAA,CAAS;QAC9B;MACF;IACF;EACF;EAED,MAAMC,SAAA,GAAY,IAAIC,cAAA,CAAgB;EACtC,MAAMC,iBAAA,GAAoB,IAAIC,iBAAA,CAC5B,IAAIC,YAAA,CAAa,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,GACjF,CACD;EAEDJ,SAAA,CAAUK,QAAA,CAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EACrCL,SAAA,CAAUM,YAAA,CAAa,YAAY,IAAIC,0BAAA,CAA2BL,iBAAA,EAAmB,GAAG,GAAG,KAAK,CAAC;EACjGF,SAAA,CAAUM,YAAA,CAAa,MAAM,IAAIC,0BAAA,CAA2BL,iBAAA,EAAmB,GAAG,GAAG,KAAK,CAAC;EAE3FjG,UAAA,CAAUG,QAAA,GAAW4F,SAAA;EAErB,OAAO/F,UAAA;AACT,GAAI;AAIC,MAAC0C,gBAAA,GAAoC,sBAAM;EAC9C,MAAM6D,iBAAA,CAAiB;IACrBrG,YAAYwF,OAAA,EAASrB,IAAA,GAAO,GAAGsB,QAAA,GAAW,GAAG9C,KAAA,GAAQ,IAAIC,KAAA,CAAM,QAAQ,GAAG;MACxE,KAAK4C,OAAA,GAAUA,OAAA;MACf,KAAKrB,IAAA,GAAOA,IAAA;MACZ,KAAKsB,QAAA,GAAWA,QAAA;MAChB,KAAK9C,KAAA,GAAQA,KAAA;IACd;EACF;EAED0D,iBAAA,CAAiB5D,MAAA,GAAS;IACxBd,QAAA,EAAU;MACRS,GAAA,EAAK;QAAEP,KAAA,EAAO;MAAM;MACpBN,YAAA,EAAc;QAAEM,KAAA,EAAO;MAAM;MAC7Bc,KAAA,EAAO;QAAEd,KAAA,EAAO;MAAM;MACtBD,KAAA,EAAO;QAAEC,KAAA,EAAO;MAAM;MACtBC,cAAA,EAAgB;QAAED,KAAA,EAAO;MAAM;IAChC;IAEDE,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAwCzBC,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmB5B;EAED,OAAOqE,iBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}