{"ast": null, "code": "const KaleidoShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    sides: {\n      value: 6\n    },\n    angle: {\n      value: 0\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform float sides;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv - 0.5;\n    \tfloat r = length(p);\n    \tfloat a = atan(p.y, p.x) + angle;\n    \tfloat tau = 2. * 3.1416 ;\n    \ta = mod(a, tau/sides);\n    \ta = abs(a - tau/sides/2.) ;\n    \tp = r * vec2(cos(a), sin(a));\n    \tvec4 color = texture2D(tDiffuse, p + 0.5);\n    \tgl_FragColor = color;\n\n    }\n  `)\n};\nexport { KaleidoShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "tDiffuse", "value", "sides", "angle", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/shaders/KaleidoShader.ts"], "sourcesContent": ["/**\n * Kaleidoscope Shader\n * Radial reflection around center point\n * Ported from: http://pixelshaders.com/editor/\n * by <PERSON> / http://tobyschachman.com/\n *\n * sides: number of reflections\n * angle: initial angle in radians\n */\n\nexport const KaleidoShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    sides: { value: 6.0 },\n    angle: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float sides;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv - 0.5;\n    \tfloat r = length(p);\n    \tfloat a = atan(p.y, p.x) + angle;\n    \tfloat tau = 2. * 3.1416 ;\n    \ta = mod(a, tau/sides);\n    \ta = abs(a - tau/sides/2.) ;\n    \tp = r * vec2(cos(a), sin(a));\n    \tvec4 color = texture2D(tDiffuse, p + 0.5);\n    \tgl_FragColor = color;\n\n    }\n  `,\n}\n"], "mappings": "AAUO,MAAMA,aAAA,GAAgB;EAC3BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,KAAA,EAAO;MAAED,KAAA,EAAO;IAAI;IACpBE,KAAA,EAAO;MAAEF,KAAA,EAAO;IAAI;EACtB;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}