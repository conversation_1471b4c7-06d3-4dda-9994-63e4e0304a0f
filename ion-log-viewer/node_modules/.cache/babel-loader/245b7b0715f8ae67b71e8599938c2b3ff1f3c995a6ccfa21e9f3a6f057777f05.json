{"ast": null, "code": "import { Mesh, ShaderMaterial, SphereGeometry } from \"three\";\nclass LightProbeHelper extends Mesh {\n  constructor(lightProbe, size) {\n    const material = new ShaderMaterial({\n      type: \"LightProbeHelperMaterial\",\n      uniforms: {\n        sh: {\n          value: lightProbe.sh.coefficients\n        },\n        // by reference\n        intensity: {\n          value: lightProbe.intensity\n        }\n      },\n      vertexShader: [\"varying vec3 vNormal;\", \"void main() {\", \"\tvNormal = normalize( normalMatrix * normal );\", \"\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\", \"}\"].join(\"\\n\"),\n      fragmentShader: [\"#define RECIPROCAL_PI 0.318309886\", \"vec3 inverseTransformDirection( in vec3 normal, in mat4 matrix ) {\", \"\t// matrix is assumed to be orthogonal\", \"\treturn normalize( ( vec4( normal, 0.0 ) * matrix ).xyz );\", \"}\", \"// source: https://graphics.stanford.edu/papers/envmap/envmap.pdf\", \"vec3 shGetIrradianceAt( in vec3 normal, in vec3 shCoefficients[ 9 ] ) {\", \"\t// normal is assumed to have unit length\", \"\tfloat x = normal.x, y = normal.y, z = normal.z;\", \"\t// band 0\", \"\tvec3 result = shCoefficients[ 0 ] * 0.886227;\", \"\t// band 1\", \"\tresult += shCoefficients[ 1 ] * 2.0 * 0.511664 * y;\", \"\tresult += shCoefficients[ 2 ] * 2.0 * 0.511664 * z;\", \"\tresult += shCoefficients[ 3 ] * 2.0 * 0.511664 * x;\", \"\t// band 2\", \"\tresult += shCoefficients[ 4 ] * 2.0 * 0.429043 * x * y;\", \"\tresult += shCoefficients[ 5 ] * 2.0 * 0.429043 * y * z;\", \"\tresult += shCoefficients[ 6 ] * ( 0.743125 * z * z - 0.247708 );\", \"\tresult += shCoefficients[ 7 ] * 2.0 * 0.429043 * x * z;\", \"\tresult += shCoefficients[ 8 ] * 0.429043 * ( x * x - y * y );\", \"\treturn result;\", \"}\", \"uniform vec3 sh[ 9 ]; // sh coefficients\", \"uniform float intensity; // light probe intensity\", \"varying vec3 vNormal;\", \"void main() {\", \"\tvec3 normal = normalize( vNormal );\", \"\tvec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\", \"\tvec3 irradiance = shGetIrradianceAt( worldNormal, sh );\", \"\tvec3 outgoingLight = RECIPROCAL_PI * irradiance * intensity;\", \"\tgl_FragColor = linearToOutputTexel( vec4( outgoingLight, 1.0 ) );\", \"}\"].join(\"\\n\")\n    });\n    const geometry = new SphereGeometry(1, 32, 16);\n    super(geometry, material);\n    this.lightProbe = lightProbe;\n    this.size = size;\n    this.type = \"LightProbeHelper\";\n    this.onBeforeRender();\n  }\n  dispose() {\n    this.geometry.dispose();\n    this.material.dispose();\n  }\n  onBeforeRender() {\n    this.position.copy(this.lightProbe.position);\n    this.scale.set(1, 1, 1).multiplyScalar(this.size);\n    this.material.uniforms.intensity.value = this.lightProbe.intensity;\n  }\n}\nexport { LightProbeHelper };", "map": {"version": 3, "names": ["LightProbeHelper", "<PERSON><PERSON>", "constructor", "lightProbe", "size", "material", "ShaderMaterial", "type", "uniforms", "sh", "value", "coefficients", "intensity", "vertexShader", "join", "fragmentShader", "geometry", "SphereGeometry", "onBeforeRender", "dispose", "position", "copy", "scale", "set", "multiplyScalar"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/helpers/LightProbeHelper.js"], "sourcesContent": ["import { Mesh, ShaderMaterial, SphereGeometry } from 'three'\n\nclass LightProbeHelper extends Mesh {\n  constructor(lightProbe, size) {\n    const material = new ShaderMaterial({\n      type: 'LightProbeHelperMaterial',\n\n      uniforms: {\n        sh: { value: lightProbe.sh.coefficients }, // by reference\n\n        intensity: { value: lightProbe.intensity },\n      },\n\n      vertexShader: [\n        'varying vec3 vNormal;',\n\n        'void main() {',\n\n        '\tvNormal = normalize( normalMatrix * normal );',\n\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        '#define RECIPROCAL_PI 0.318309886',\n\n        'vec3 inverseTransformDirection( in vec3 normal, in mat4 matrix ) {',\n\n        '\t// matrix is assumed to be orthogonal',\n\n        '\treturn normalize( ( vec4( normal, 0.0 ) * matrix ).xyz );',\n\n        '}',\n\n        '// source: https://graphics.stanford.edu/papers/envmap/envmap.pdf',\n        'vec3 shGetIrradianceAt( in vec3 normal, in vec3 shCoefficients[ 9 ] ) {',\n\n        '\t// normal is assumed to have unit length',\n\n        '\tfloat x = normal.x, y = normal.y, z = normal.z;',\n\n        '\t// band 0',\n        '\tvec3 result = shCoefficients[ 0 ] * 0.886227;',\n\n        '\t// band 1',\n        '\tresult += shCoefficients[ 1 ] * 2.0 * 0.511664 * y;',\n        '\tresult += shCoefficients[ 2 ] * 2.0 * 0.511664 * z;',\n        '\tresult += shCoefficients[ 3 ] * 2.0 * 0.511664 * x;',\n\n        '\t// band 2',\n        '\tresult += shCoefficients[ 4 ] * 2.0 * 0.429043 * x * y;',\n        '\tresult += shCoefficients[ 5 ] * 2.0 * 0.429043 * y * z;',\n        '\tresult += shCoefficients[ 6 ] * ( 0.743125 * z * z - 0.247708 );',\n        '\tresult += shCoefficients[ 7 ] * 2.0 * 0.429043 * x * z;',\n        '\tresult += shCoefficients[ 8 ] * 0.429043 * ( x * x - y * y );',\n\n        '\treturn result;',\n\n        '}',\n\n        'uniform vec3 sh[ 9 ]; // sh coefficients',\n\n        'uniform float intensity; // light probe intensity',\n\n        'varying vec3 vNormal;',\n\n        'void main() {',\n\n        '\tvec3 normal = normalize( vNormal );',\n\n        '\tvec3 worldNormal = inverseTransformDirection( normal, viewMatrix );',\n\n        '\tvec3 irradiance = shGetIrradianceAt( worldNormal, sh );',\n\n        '\tvec3 outgoingLight = RECIPROCAL_PI * irradiance * intensity;',\n\n        '\tgl_FragColor = linearToOutputTexel( vec4( outgoingLight, 1.0 ) );',\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const geometry = new SphereGeometry(1, 32, 16)\n\n    super(geometry, material)\n\n    this.lightProbe = lightProbe\n    this.size = size\n    this.type = 'LightProbeHelper'\n\n    this.onBeforeRender()\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n\n  onBeforeRender() {\n    this.position.copy(this.lightProbe.position)\n\n    this.scale.set(1, 1, 1).multiplyScalar(this.size)\n\n    this.material.uniforms.intensity.value = this.lightProbe.intensity\n  }\n}\n\nexport { LightProbeHelper }\n"], "mappings": ";AAEA,MAAMA,gBAAA,SAAyBC,IAAA,CAAK;EAClCC,YAAYC,UAAA,EAAYC,IAAA,EAAM;IAC5B,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAe;MAClCC,IAAA,EAAM;MAENC,QAAA,EAAU;QACRC,EAAA,EAAI;UAAEC,KAAA,EAAOP,UAAA,CAAWM,EAAA,CAAGE;QAAc;QAAA;QAEzCC,SAAA,EAAW;UAAEF,KAAA,EAAOP,UAAA,CAAWS;QAAW;MAC3C;MAEDC,YAAA,EAAc,CACZ,yBAEA,iBAEA,kDAEA,8EAEA,IACR,CAAQC,IAAA,CAAK,IAAI;MAEXC,cAAA,EAAgB,CACd,qCAEA,sEAEA,0CAEA,8DAEA,KAEA,qEACA,2EAEA,6CAEA,oDAEA,cACA,kDAEA,cACA,wDACA,wDACA,wDAEA,cACA,4DACA,4DACA,qEACA,4DACA,kEAEA,mBAEA,KAEA,4CAEA,qDAEA,yBAEA,iBAEA,wCAEA,wEAEA,4DAEA,iEAEA,sEAEA,IACR,CAAQD,IAAA,CAAK,IAAI;IACjB,CAAK;IAED,MAAME,QAAA,GAAW,IAAIC,cAAA,CAAe,GAAG,IAAI,EAAE;IAE7C,MAAMD,QAAA,EAAUX,QAAQ;IAExB,KAAKF,UAAA,GAAaA,UAAA;IAClB,KAAKC,IAAA,GAAOA,IAAA;IACZ,KAAKG,IAAA,GAAO;IAEZ,KAAKW,cAAA,CAAgB;EACtB;EAEDC,QAAA,EAAU;IACR,KAAKH,QAAA,CAASG,OAAA,CAAS;IACvB,KAAKd,QAAA,CAASc,OAAA,CAAS;EACxB;EAEDD,eAAA,EAAiB;IACf,KAAKE,QAAA,CAASC,IAAA,CAAK,KAAKlB,UAAA,CAAWiB,QAAQ;IAE3C,KAAKE,KAAA,CAAMC,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEC,cAAA,CAAe,KAAKpB,IAAI;IAEhD,KAAKC,QAAA,CAASG,QAAA,CAASI,SAAA,CAAUF,KAAA,GAAQ,KAAKP,UAAA,CAAWS,SAAA;EAC1D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}