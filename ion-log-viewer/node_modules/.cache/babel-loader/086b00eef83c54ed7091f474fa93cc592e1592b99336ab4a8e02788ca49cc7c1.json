{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withPrimitives(Primitives.String).withClassesToUnbox(String).build();\nexport class Symbol extends Value(String, IonTypes.SYMBOL, _fromJsConstructor) {\n  constructor(symbolText, annotations = []) {\n    super(symbolText);\n    this._setAnnotations(annotations);\n  }\n  stringValue() {\n    return this.toString();\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeSymbol(this.stringValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Symbol) {\n      isSupportedType = true;\n      valueToCompare = other.stringValue();\n    } else if (!options.onlyCompareIon) {\n      if (typeof other === \"string\" || other instanceof String) {\n        isSupportedType = true;\n        valueToCompare = other.valueOf();\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    return this.compareValue(valueToCompare) === 0;\n  }\n  compareValue(expectedValue) {\n    return this.stringValue().localeCompare(expectedValue);\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Primitives", "Value", "_fromJsConstructor", "withPrimitives", "String", "withClassesToUnbox", "build", "Symbol", "SYMBOL", "constructor", "symbolText", "annotations", "_setAnnotations", "stringValue", "toString", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeSymbol", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "valueOf", "compareValue", "expectedValue", "localeCompare"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Symbol.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withPrimitives(Primitives.String)\n    .withClassesToUnbox(String)\n    .build();\nexport class Symbol extends Value(String, IonTypes.SYMBOL, _fromJsConstructor) {\n    constructor(symbolText, annotations = []) {\n        super(symbolText);\n        this._setAnnotations(annotations);\n    }\n    stringValue() {\n        return this.toString();\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeSymbol(this.stringValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Symbol) {\n            isSupportedType = true;\n            valueToCompare = other.stringValue();\n        }\n        else if (!options.onlyCompareIon) {\n            if (typeof other === \"string\" || other instanceof String) {\n                isSupportedType = true;\n                valueToCompare = other.valueOf();\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        return this.compareValue(valueToCompare) === 0;\n    }\n    compareValue(expectedValue) {\n        return this.stringValue().localeCompare(expectedValue);\n    }\n}\n//# sourceMappingURL=Symbol.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,EAAEC,UAAU,QAAS,qBAAqB;AAC3E,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIH,wBAAwB,CAAC,CAAC,CACpDI,cAAc,CAACH,UAAU,CAACI,MAAM,CAAC,CACjCC,kBAAkB,CAACD,MAAM,CAAC,CAC1BE,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMC,MAAM,SAASN,KAAK,CAACG,MAAM,EAAEN,QAAQ,CAACU,MAAM,EAAEN,kBAAkB,CAAC,CAAC;EAC3EO,WAAWA,CAACC,UAAU,EAAEC,WAAW,GAAG,EAAE,EAAE;IACtC,KAAK,CAACD,UAAU,CAAC;IACjB,IAAI,CAACE,eAAe,CAACD,WAAW,CAAC;EACrC;EACAE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,WAAW,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC;EAC1C;EACAO,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAYd,MAAM,EAAE;MACzBoB,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK,CAACR,WAAW,CAAC,CAAC;IACxC,CAAC,MACI,IAAI,CAACS,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYjB,MAAM,EAAE;QACtDuB,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGP,KAAK,CAACQ,OAAO,CAAC,CAAC;MACpC;IACJ;IACA,IAAI,CAACF,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACG,YAAY,CAACF,cAAc,CAAC,KAAK,CAAC;EAClD;EACAE,YAAYA,CAACC,aAAa,EAAE;IACxB,OAAO,IAAI,CAAClB,WAAW,CAAC,CAAC,CAACmB,aAAa,CAACD,aAAa,CAAC;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}