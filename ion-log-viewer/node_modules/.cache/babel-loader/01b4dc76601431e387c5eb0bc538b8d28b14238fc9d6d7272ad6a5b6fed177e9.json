{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass SimplexNoise {\n  /**\n   * You can pass in a random number generator object if you like.\n   * It is assumed to have a random() method.\n   */\n  constructor(r = Math) {\n    __publicField(this, \"grad3\", [[1, 1, 0], [-1, 1, 0], [1, -1, 0], [-1, -1, 0], [1, 0, 1], [-1, 0, 1], [1, 0, -1], [-1, 0, -1], [0, 1, 1], [0, -1, 1], [0, 1, -1], [0, -1, -1]]);\n    __publicField(this, \"grad4\", [[0, 1, 1, 1], [0, 1, 1, -1], [0, 1, -1, 1], [0, 1, -1, -1], [0, -1, 1, 1], [0, -1, 1, -1], [0, -1, -1, 1], [0, -1, -1, -1], [1, 0, 1, 1], [1, 0, 1, -1], [1, 0, -1, 1], [1, 0, -1, -1], [-1, 0, 1, 1], [-1, 0, 1, -1], [-1, 0, -1, 1], [-1, 0, -1, -1], [1, 1, 0, 1], [1, 1, 0, -1], [1, -1, 0, 1], [1, -1, 0, -1], [-1, 1, 0, 1], [-1, 1, 0, -1], [-1, -1, 0, 1], [-1, -1, 0, -1], [1, 1, 1, 0], [1, 1, -1, 0], [1, -1, 1, 0], [1, -1, -1, 0], [-1, 1, 1, 0], [-1, 1, -1, 0], [-1, -1, 1, 0], [-1, -1, -1, 0]]);\n    __publicField(this, \"p\", []);\n    // To remove the need for index wrapping, double the permutation table length\n    __publicField(this, \"perm\", []);\n    // A lookup table to traverse the simplex around a given point in 4D.\n    // Details can be found where this table is used, in the 4D noise method.\n    __publicField(this, \"simplex\", [[0, 1, 2, 3], [0, 1, 3, 2], [0, 0, 0, 0], [0, 2, 3, 1], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [1, 2, 3, 0], [0, 2, 1, 3], [0, 0, 0, 0], [0, 3, 1, 2], [0, 3, 2, 1], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [1, 3, 2, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [1, 2, 0, 3], [0, 0, 0, 0], [1, 3, 0, 2], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [2, 3, 0, 1], [2, 3, 1, 0], [1, 0, 2, 3], [1, 0, 3, 2], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [2, 0, 3, 1], [0, 0, 0, 0], [2, 1, 3, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [2, 0, 1, 3], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [3, 0, 1, 2], [3, 0, 2, 1], [0, 0, 0, 0], [3, 1, 2, 0], [2, 1, 0, 3], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [3, 1, 0, 2], [0, 0, 0, 0], [3, 2, 0, 1], [3, 2, 1, 0]]);\n    __publicField(this, \"dot\", (g, x, y) => {\n      return g[0] * x + g[1] * y;\n    });\n    __publicField(this, \"dot3\", (g, x, y, z) => {\n      return g[0] * x + g[1] * y + g[2] * z;\n    });\n    __publicField(this, \"dot4\", (g, x, y, z, w) => {\n      return g[0] * x + g[1] * y + g[2] * z + g[3] * w;\n    });\n    __publicField(this, \"noise\", (xin, yin) => {\n      let n0;\n      let n1;\n      let n2;\n      const F2 = 0.5 * (Math.sqrt(3) - 1);\n      const s = (xin + yin) * F2;\n      const i = Math.floor(xin + s);\n      const j = Math.floor(yin + s);\n      const G2 = (3 - Math.sqrt(3)) / 6;\n      const t = (i + j) * G2;\n      const X0 = i - t;\n      const Y0 = j - t;\n      const x0 = xin - X0;\n      const y0 = yin - Y0;\n      let i1 = 0;\n      let j1 = 1;\n      if (x0 > y0) {\n        i1 = 1;\n        j1 = 0;\n      }\n      const x1 = x0 - i1 + G2;\n      const y1 = y0 - j1 + G2;\n      const x2 = x0 - 1 + 2 * G2;\n      const y2 = y0 - 1 + 2 * G2;\n      const ii = i & 255;\n      const jj = j & 255;\n      const gi0 = this.perm[ii + this.perm[jj]] % 12;\n      const gi1 = this.perm[ii + i1 + this.perm[jj + j1]] % 12;\n      const gi2 = this.perm[ii + 1 + this.perm[jj + 1]] % 12;\n      let t0 = 0.5 - x0 * x0 - y0 * y0;\n      if (t0 < 0) {\n        n0 = 0;\n      } else {\n        t0 *= t0;\n        n0 = t0 * t0 * this.dot(this.grad3[gi0], x0, y0);\n      }\n      let t1 = 0.5 - x1 * x1 - y1 * y1;\n      if (t1 < 0) {\n        n1 = 0;\n      } else {\n        t1 *= t1;\n        n1 = t1 * t1 * this.dot(this.grad3[gi1], x1, y1);\n      }\n      let t2 = 0.5 - x2 * x2 - y2 * y2;\n      if (t2 < 0) {\n        n2 = 0;\n      } else {\n        t2 *= t2;\n        n2 = t2 * t2 * this.dot(this.grad3[gi2], x2, y2);\n      }\n      return 70 * (n0 + n1 + n2);\n    });\n    // 3D simplex noise\n    __publicField(this, \"noise3d\", (xin, yin, zin) => {\n      let n0;\n      let n1;\n      let n2;\n      let n3;\n      const F3 = 1 / 3;\n      const s = (xin + yin + zin) * F3;\n      const i = Math.floor(xin + s);\n      const j = Math.floor(yin + s);\n      const k = Math.floor(zin + s);\n      const G3 = 1 / 6;\n      const t = (i + j + k) * G3;\n      const X0 = i - t;\n      const Y0 = j - t;\n      const Z0 = k - t;\n      const x0 = xin - X0;\n      const y0 = yin - Y0;\n      const z0 = zin - Z0;\n      let i1;\n      let j1;\n      let k1;\n      let i2;\n      let j2;\n      let k2;\n      if (x0 >= y0) {\n        if (y0 >= z0) {\n          i1 = 1;\n          j1 = 0;\n          k1 = 0;\n          i2 = 1;\n          j2 = 1;\n          k2 = 0;\n        } else if (x0 >= z0) {\n          i1 = 1;\n          j1 = 0;\n          k1 = 0;\n          i2 = 1;\n          j2 = 0;\n          k2 = 1;\n        } else {\n          i1 = 0;\n          j1 = 0;\n          k1 = 1;\n          i2 = 1;\n          j2 = 0;\n          k2 = 1;\n        }\n      } else {\n        if (y0 < z0) {\n          i1 = 0;\n          j1 = 0;\n          k1 = 1;\n          i2 = 0;\n          j2 = 1;\n          k2 = 1;\n        } else if (x0 < z0) {\n          i1 = 0;\n          j1 = 1;\n          k1 = 0;\n          i2 = 0;\n          j2 = 1;\n          k2 = 1;\n        } else {\n          i1 = 0;\n          j1 = 1;\n          k1 = 0;\n          i2 = 1;\n          j2 = 1;\n          k2 = 0;\n        }\n      }\n      const x1 = x0 - i1 + G3;\n      const y1 = y0 - j1 + G3;\n      const z1 = z0 - k1 + G3;\n      const x2 = x0 - i2 + 2 * G3;\n      const y2 = y0 - j2 + 2 * G3;\n      const z2 = z0 - k2 + 2 * G3;\n      const x3 = x0 - 1 + 3 * G3;\n      const y3 = y0 - 1 + 3 * G3;\n      const z3 = z0 - 1 + 3 * G3;\n      const ii = i & 255;\n      const jj = j & 255;\n      const kk = k & 255;\n      const gi0 = this.perm[ii + this.perm[jj + this.perm[kk]]] % 12;\n      const gi1 = this.perm[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]] % 12;\n      const gi2 = this.perm[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]] % 12;\n      const gi3 = this.perm[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]] % 12;\n      let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n      if (t0 < 0) {\n        n0 = 0;\n      } else {\n        t0 *= t0;\n        n0 = t0 * t0 * this.dot3(this.grad3[gi0], x0, y0, z0);\n      }\n      let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n      if (t1 < 0) {\n        n1 = 0;\n      } else {\n        t1 *= t1;\n        n1 = t1 * t1 * this.dot3(this.grad3[gi1], x1, y1, z1);\n      }\n      let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n      if (t2 < 0) {\n        n2 = 0;\n      } else {\n        t2 *= t2;\n        n2 = t2 * t2 * this.dot3(this.grad3[gi2], x2, y2, z2);\n      }\n      let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n      if (t3 < 0) {\n        n3 = 0;\n      } else {\n        t3 *= t3;\n        n3 = t3 * t3 * this.dot3(this.grad3[gi3], x3, y3, z3);\n      }\n      return 32 * (n0 + n1 + n2 + n3);\n    });\n    // 4D simplex noise\n    __publicField(this, \"noise4d\", (x, y, z, w) => {\n      const grad4 = this.grad4;\n      const simplex = this.simplex;\n      const perm = this.perm;\n      const F4 = (Math.sqrt(5) - 1) / 4;\n      const G4 = (5 - Math.sqrt(5)) / 20;\n      let n0;\n      let n1;\n      let n2;\n      let n3;\n      let n4;\n      const s = (x + y + z + w) * F4;\n      const i = Math.floor(x + s);\n      const j = Math.floor(y + s);\n      const k = Math.floor(z + s);\n      const l = Math.floor(w + s);\n      const t = (i + j + k + l) * G4;\n      const X0 = i - t;\n      const Y0 = j - t;\n      const Z0 = k - t;\n      const W0 = l - t;\n      const x0 = x - X0;\n      const y0 = y - Y0;\n      const z0 = z - Z0;\n      const w0 = w - W0;\n      const c1 = x0 > y0 ? 32 : 0;\n      const c2 = x0 > z0 ? 16 : 0;\n      const c3 = y0 > z0 ? 8 : 0;\n      const c4 = x0 > w0 ? 4 : 0;\n      const c5 = y0 > w0 ? 2 : 0;\n      const c6 = z0 > w0 ? 1 : 0;\n      const c = c1 + c2 + c3 + c4 + c5 + c6;\n      let i1;\n      let j1;\n      let k1;\n      let l1;\n      let i2;\n      let j2;\n      let k2;\n      let l2;\n      let i3;\n      let j3;\n      let k3;\n      let l3;\n      i1 = simplex[c][0] >= 3 ? 1 : 0;\n      j1 = simplex[c][1] >= 3 ? 1 : 0;\n      k1 = simplex[c][2] >= 3 ? 1 : 0;\n      l1 = simplex[c][3] >= 3 ? 1 : 0;\n      i2 = simplex[c][0] >= 2 ? 1 : 0;\n      j2 = simplex[c][1] >= 2 ? 1 : 0;\n      k2 = simplex[c][2] >= 2 ? 1 : 0;\n      l2 = simplex[c][3] >= 2 ? 1 : 0;\n      i3 = simplex[c][0] >= 1 ? 1 : 0;\n      j3 = simplex[c][1] >= 1 ? 1 : 0;\n      k3 = simplex[c][2] >= 1 ? 1 : 0;\n      l3 = simplex[c][3] >= 1 ? 1 : 0;\n      const x1 = x0 - i1 + G4;\n      const y1 = y0 - j1 + G4;\n      const z1 = z0 - k1 + G4;\n      const w1 = w0 - l1 + G4;\n      const x2 = x0 - i2 + 2 * G4;\n      const y2 = y0 - j2 + 2 * G4;\n      const z2 = z0 - k2 + 2 * G4;\n      const w2 = w0 - l2 + 2 * G4;\n      const x3 = x0 - i3 + 3 * G4;\n      const y3 = y0 - j3 + 3 * G4;\n      const z3 = z0 - k3 + 3 * G4;\n      const w3 = w0 - l3 + 3 * G4;\n      const x4 = x0 - 1 + 4 * G4;\n      const y4 = y0 - 1 + 4 * G4;\n      const z4 = z0 - 1 + 4 * G4;\n      const w4 = w0 - 1 + 4 * G4;\n      const ii = i & 255;\n      const jj = j & 255;\n      const kk = k & 255;\n      const ll = l & 255;\n      const gi0 = perm[ii + perm[jj + perm[kk + perm[ll]]]] % 32;\n      const gi1 = perm[ii + i1 + perm[jj + j1 + perm[kk + k1 + perm[ll + l1]]]] % 32;\n      const gi2 = perm[ii + i2 + perm[jj + j2 + perm[kk + k2 + perm[ll + l2]]]] % 32;\n      const gi3 = perm[ii + i3 + perm[jj + j3 + perm[kk + k3 + perm[ll + l3]]]] % 32;\n      const gi4 = perm[ii + 1 + perm[jj + 1 + perm[kk + 1 + perm[ll + 1]]]] % 32;\n      let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0 - w0 * w0;\n      if (t0 < 0) {\n        n0 = 0;\n      } else {\n        t0 *= t0;\n        n0 = t0 * t0 * this.dot4(grad4[gi0], x0, y0, z0, w0);\n      }\n      let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1 - w1 * w1;\n      if (t1 < 0) {\n        n1 = 0;\n      } else {\n        t1 *= t1;\n        n1 = t1 * t1 * this.dot4(grad4[gi1], x1, y1, z1, w1);\n      }\n      let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2 - w2 * w2;\n      if (t2 < 0) {\n        n2 = 0;\n      } else {\n        t2 *= t2;\n        n2 = t2 * t2 * this.dot4(grad4[gi2], x2, y2, z2, w2);\n      }\n      let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3 - w3 * w3;\n      if (t3 < 0) {\n        n3 = 0;\n      } else {\n        t3 *= t3;\n        n3 = t3 * t3 * this.dot4(grad4[gi3], x3, y3, z3, w3);\n      }\n      let t4 = 0.6 - x4 * x4 - y4 * y4 - z4 * z4 - w4 * w4;\n      if (t4 < 0) {\n        n4 = 0;\n      } else {\n        t4 *= t4;\n        n4 = t4 * t4 * this.dot4(grad4[gi4], x4, y4, z4, w4);\n      }\n      return 27 * (n0 + n1 + n2 + n3 + n4);\n    });\n    for (let i = 0; i < 256; i++) {\n      this.p[i] = Math.floor(r.random() * 256);\n    }\n    for (let i = 0; i < 512; i++) {\n      this.perm[i] = this.p[i & 255];\n    }\n  }\n}\nexport { SimplexNoise };", "map": {"version": 3, "names": ["SimplexNoise", "constructor", "r", "Math", "__publicField", "g", "x", "y", "z", "w", "xin", "yin", "n0", "n1", "n2", "F2", "sqrt", "s", "i", "floor", "j", "G2", "t", "X0", "Y0", "x0", "y0", "i1", "j1", "x1", "y1", "x2", "y2", "ii", "jj", "gi0", "perm", "gi1", "gi2", "t0", "dot", "grad3", "t1", "t2", "zin", "n3", "F3", "k", "G3", "Z0", "z0", "k1", "i2", "j2", "k2", "z1", "z2", "x3", "y3", "z3", "kk", "gi3", "dot3", "t3", "grad4", "simplex", "F4", "G4", "n4", "l", "W0", "w0", "c1", "c2", "c3", "c4", "c5", "c6", "c", "l1", "l2", "i3", "j3", "k3", "l3", "w1", "w2", "w3", "x4", "y4", "z4", "w4", "ll", "gi4", "dot4", "t4", "p", "random"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/math/SimplexNoise.ts"], "sourcesContent": ["// Ported from <PERSON>'s java implementation\n// http://staffwww.itn.liu.se/~stegu/simplexnoise/simplexnoise.pdf\n// Read <PERSON>'s excellent paper for details on how this code works.\n//\n// <PERSON> <EMAIL>\n//\n\nexport interface NumberGenerator {\n  random: () => number\n}\n\n// Added 4D noise\nexport class SimplexNoise {\n  private grad3 = [\n    [1, 1, 0],\n    [-1, 1, 0],\n    [1, -1, 0],\n    [-1, -1, 0],\n    [1, 0, 1],\n    [-1, 0, 1],\n    [1, 0, -1],\n    [-1, 0, -1],\n    [0, 1, 1],\n    [0, -1, 1],\n    [0, 1, -1],\n    [0, -1, -1],\n  ]\n\n  private grad4 = [\n    [0, 1, 1, 1],\n    [0, 1, 1, -1],\n    [0, 1, -1, 1],\n    [0, 1, -1, -1],\n    [0, -1, 1, 1],\n    [0, -1, 1, -1],\n    [0, -1, -1, 1],\n    [0, -1, -1, -1],\n    [1, 0, 1, 1],\n    [1, 0, 1, -1],\n    [1, 0, -1, 1],\n    [1, 0, -1, -1],\n    [-1, 0, 1, 1],\n    [-1, 0, 1, -1],\n    [-1, 0, -1, 1],\n    [-1, 0, -1, -1],\n    [1, 1, 0, 1],\n    [1, 1, 0, -1],\n    [1, -1, 0, 1],\n    [1, -1, 0, -1],\n    [-1, 1, 0, 1],\n    [-1, 1, 0, -1],\n    [-1, -1, 0, 1],\n    [-1, -1, 0, -1],\n    [1, 1, 1, 0],\n    [1, 1, -1, 0],\n    [1, -1, 1, 0],\n    [1, -1, -1, 0],\n    [-1, 1, 1, 0],\n    [-1, 1, -1, 0],\n    [-1, -1, 1, 0],\n    [-1, -1, -1, 0],\n  ]\n\n  private p: number[] = []\n\n  // To remove the need for index wrapping, double the permutation table length\n  private perm: number[] = []\n\n  // A lookup table to traverse the simplex around a given point in 4D.\n  // Details can be found where this table is used, in the 4D noise method.\n  private simplex = [\n    [0, 1, 2, 3],\n    [0, 1, 3, 2],\n    [0, 0, 0, 0],\n    [0, 2, 3, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 3, 0],\n    [0, 2, 1, 3],\n    [0, 0, 0, 0],\n    [0, 3, 1, 2],\n    [0, 3, 2, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 3, 2, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 0, 3],\n    [0, 0, 0, 0],\n    [1, 3, 0, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 3, 0, 1],\n    [2, 3, 1, 0],\n    [1, 0, 2, 3],\n    [1, 0, 3, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 3, 1],\n    [0, 0, 0, 0],\n    [2, 1, 3, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 1, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 0, 1, 2],\n    [3, 0, 2, 1],\n    [0, 0, 0, 0],\n    [3, 1, 2, 0],\n    [2, 1, 0, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 1, 0, 2],\n    [0, 0, 0, 0],\n    [3, 2, 0, 1],\n    [3, 2, 1, 0],\n  ]\n\n  /**\n   * You can pass in a random number generator object if you like.\n   * It is assumed to have a random() method.\n   */\n  constructor(r: NumberGenerator = Math) {\n    for (let i = 0; i < 256; i++) {\n      this.p[i] = Math.floor(r.random() * 256)\n    }\n\n    for (let i = 0; i < 512; i++) {\n      this.perm[i] = this.p[i & 255]\n    }\n  }\n\n  public dot = (g: number[], x: number, y: number): number => {\n    return g[0] * x + g[1] * y\n  }\n\n  public dot3 = (g: number[], x: number, y: number, z: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z\n  }\n\n  public dot4 = (g: number[], x: number, y: number, z: number, w: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z + g[3] * w\n  }\n\n  public noise = (xin: number, yin: number): number => {\n    let n0\n    let n1\n    let n2 // Noise contributions from the three corners\n    // Skew the input space to determine which simplex cell we're in\n    const F2 = 0.5 * (Math.sqrt(3.0) - 1.0)\n    const s = (xin + yin) * F2 // Hairy factor for 2D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const G2 = (3.0 - Math.sqrt(3.0)) / 6.0\n    const t = (i + j) * G2\n    const X0 = i - t // Unskew the cell origin back to (x,y) space\n    const Y0 = j - t\n    const x0 = xin - X0 // The x,y distances from the cell origin\n    const y0 = yin - Y0\n    // For the 2D case, the simplex shape is an equilateral triangle.\n    // Determine which simplex we are in.\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    let i1 = 0\n    // Offsets for second (middle) corner of simplex in (i,j) coords\n    let j1 = 1\n    if (x0 > y0) {\n      i1 = 1\n      j1 = 0\n    }\n\n    // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n    // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n    // c = (3-sqrt(3))/6\n    const x1 = x0 - i1 + G2 // Offsets for middle corner in (x,y) unskewed coords\n    const y1 = y0 - j1 + G2\n    const x2 = x0 - 1.0 + 2.0 * G2 // Offsets for last corner in (x,y) unskewed coords\n    const y2 = y0 - 1.0 + 2.0 * G2\n    // Work out the hashed gradient indices of the three simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const gi0 = this.perm[ii + this.perm[jj]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1]] % 12\n    const gi2 = this.perm[ii + 1 + this.perm[jj + 1]] % 12\n    // Calculate the contribution from the three corners\n    let t0 = 0.5 - x0 * x0 - y0 * y0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot(this.grad3[gi0], x0, y0) // (x,y) of grad3 used for 2D gradient\n    }\n\n    let t1 = 0.5 - x1 * x1 - y1 * y1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot(this.grad3[gi1], x1, y1)\n    }\n\n    let t2 = 0.5 - x2 * x2 - y2 * y2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot(this.grad3[gi2], x2, y2)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to return values in the interval [-1,1].\n    return 70.0 * (n0 + n1 + n2)\n  }\n\n  // 3D simplex noise\n  private noise3d = (xin: number, yin: number, zin: number): number => {\n    // Noise contributions from the four corners\n    let n0\n    let n1\n    let n2\n    let n3\n    // Skew the input space to determine which simplex cell we're in\n    const F3 = 1.0 / 3.0\n    const s = (xin + yin + zin) * F3 // Very nice and simple skew factor for 3D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const k = Math.floor(zin + s)\n    const G3 = 1.0 / 6.0 // Very nice and simple unskew factor, too\n    const t = (i + j + k) * G3\n    const X0 = i - t // Unskew the cell origin back to (x,y,z) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const x0 = xin - X0 // The x,y,z distances from the cell origin\n    const y0 = yin - Y0\n    const z0 = zin - Z0\n    // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n    // Determine which simplex we are in.\n    let i1\n    let j1\n    let k1 // Offsets for second corner of simplex in (i,j,k) coords\n    let i2\n    let j2\n    let k2 // Offsets for third corner of simplex in (i,j,k) coords\n    if (x0 >= y0) {\n      if (y0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n\n        // X Y Z order\n      } else if (x0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 0\n        k2 = 1\n\n        // X Z Y order\n      } else {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 1\n        j2 = 0\n        k2 = 1\n      } // Z X Y order\n    } else {\n      // x0<y0\n\n      if (y0 < z0) {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Z Y X order\n      } else if (x0 < z0) {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Y Z X order\n      } else {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n      } // Y X Z order\n    }\n\n    // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n    // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n    // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n    // c = 1/6.\n    const x1 = x0 - i1 + G3 // Offsets for second corner in (x,y,z) coords\n    const y1 = y0 - j1 + G3\n    const z1 = z0 - k1 + G3\n    const x2 = x0 - i2 + 2.0 * G3 // Offsets for third corner in (x,y,z) coords\n    const y2 = y0 - j2 + 2.0 * G3\n    const z2 = z0 - k2 + 2.0 * G3\n    const x3 = x0 - 1.0 + 3.0 * G3 // Offsets for last corner in (x,y,z) coords\n    const y3 = y0 - 1.0 + 3.0 * G3\n    const z3 = z0 - 1.0 + 3.0 * G3\n    // Work out the hashed gradient indices of the four simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const gi0 = this.perm[ii + this.perm[jj + this.perm[kk]]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]] % 12\n    const gi2 = this.perm[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]] % 12\n    const gi3 = this.perm[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]] % 12\n    // Calculate the contribution from the four corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot3(this.grad3[gi0], x0, y0, z0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot3(this.grad3[gi1], x1, y1, z1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot3(this.grad3[gi2], x2, y2, z2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot3(this.grad3[gi3], x3, y3, z3)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to stay just inside [-1,1]\n    return 32.0 * (n0 + n1 + n2 + n3)\n  }\n\n  // 4D simplex noise\n  public noise4d = (x: number, y: number, z: number, w: number): number => {\n    // For faster and easier lookups\n    const grad4 = this.grad4\n    const simplex = this.simplex\n    const perm = this.perm\n\n    // The skewing and unskewing factors are hairy again for the 4D case\n    const F4 = (Math.sqrt(5.0) - 1.0) / 4.0\n    const G4 = (5.0 - Math.sqrt(5.0)) / 20.0\n    let n0\n    let n1\n    let n2\n    let n3\n    let n4 // Noise contributions from the five corners\n    // Skew the (x,y,z,w) space to determine which cell of 24 simplices we're in\n    const s = (x + y + z + w) * F4 // Factor for 4D skewing\n    const i = Math.floor(x + s)\n    const j = Math.floor(y + s)\n    const k = Math.floor(z + s)\n    const l = Math.floor(w + s)\n    const t = (i + j + k + l) * G4 // Factor for 4D unskewing\n    const X0 = i - t // Unskew the cell origin back to (x,y,z,w) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const W0 = l - t\n    const x0 = x - X0 // The x,y,z,w distances from the cell origin\n    const y0 = y - Y0\n    const z0 = z - Z0\n    const w0 = w - W0\n\n    // For the 4D case, the simplex is a 4D shape I won't even try to describe.\n    // To find out which of the 24 possible simplices we're in, we need to\n    // determine the magnitude ordering of x0, y0, z0 and w0.\n    // The method below is a good way of finding the ordering of x,y,z,w and\n    // then find the correct traversal order for the simplex we’re in.\n    // First, six pair-wise comparisons are performed between each possible pair\n    // of the four coordinates, and the results are used to add up binary bits\n    // for an integer index.\n    const c1 = x0 > y0 ? 32 : 0\n    const c2 = x0 > z0 ? 16 : 0\n    const c3 = y0 > z0 ? 8 : 0\n    const c4 = x0 > w0 ? 4 : 0\n    const c5 = y0 > w0 ? 2 : 0\n    const c6 = z0 > w0 ? 1 : 0\n    const c = c1 + c2 + c3 + c4 + c5 + c6\n    // The integer offsets for the second simplex corner\n    let i1\n    let j1\n    let k1\n    let l1\n\n    // The integer offsets for the third simplex corner\n    let i2\n    let j2\n    let k2\n    let l2\n\n    // The integer offsets for the fourth simplex corner\n    let i3\n    let j3\n    let k3\n    let l3\n    // simplex[c] is a 4-vector with the numbers 0, 1, 2 and 3 in some order.\n    // Many values of c will never occur, since e.g. x>y>z>w makes x<z, y<w and x<w\n    // impossible. Only the 24 indices which have non-zero entries make any sense.\n    // We use a thresholding to set the coordinates in turn from the largest magnitude.\n    // The number 3 in the \"simplex\" array is at the position of the largest coordinate.\n    i1 = simplex[c][0] >= 3 ? 1 : 0\n    j1 = simplex[c][1] >= 3 ? 1 : 0\n    k1 = simplex[c][2] >= 3 ? 1 : 0\n    l1 = simplex[c][3] >= 3 ? 1 : 0\n    // The number 2 in the \"simplex\" array is at the second largest coordinate.\n    i2 = simplex[c][0] >= 2 ? 1 : 0\n    j2 = simplex[c][1] >= 2 ? 1 : 0\n    k2 = simplex[c][2] >= 2 ? 1 : 0\n    l2 = simplex[c][3] >= 2 ? 1 : 0\n    // The number 1 in the \"simplex\" array is at the second smallest coordinate.\n    i3 = simplex[c][0] >= 1 ? 1 : 0\n    j3 = simplex[c][1] >= 1 ? 1 : 0\n    k3 = simplex[c][2] >= 1 ? 1 : 0\n    l3 = simplex[c][3] >= 1 ? 1 : 0\n    // The fifth corner has all coordinate offsets = 1, so no need to look that up.\n    const x1 = x0 - i1 + G4 // Offsets for second corner in (x,y,z,w) coords\n    const y1 = y0 - j1 + G4\n    const z1 = z0 - k1 + G4\n    const w1 = w0 - l1 + G4\n    const x2 = x0 - i2 + 2.0 * G4 // Offsets for third corner in (x,y,z,w) coords\n    const y2 = y0 - j2 + 2.0 * G4\n    const z2 = z0 - k2 + 2.0 * G4\n    const w2 = w0 - l2 + 2.0 * G4\n    const x3 = x0 - i3 + 3.0 * G4 // Offsets for fourth corner in (x,y,z,w) coords\n    const y3 = y0 - j3 + 3.0 * G4\n    const z3 = z0 - k3 + 3.0 * G4\n    const w3 = w0 - l3 + 3.0 * G4\n    const x4 = x0 - 1.0 + 4.0 * G4 // Offsets for last corner in (x,y,z,w) coords\n    const y4 = y0 - 1.0 + 4.0 * G4\n    const z4 = z0 - 1.0 + 4.0 * G4\n    const w4 = w0 - 1.0 + 4.0 * G4\n    // Work out the hashed gradient indices of the five simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const ll = l & 255\n    const gi0 = perm[ii + perm[jj + perm[kk + perm[ll]]]] % 32\n    const gi1 = perm[ii + i1 + perm[jj + j1 + perm[kk + k1 + perm[ll + l1]]]] % 32\n    const gi2 = perm[ii + i2 + perm[jj + j2 + perm[kk + k2 + perm[ll + l2]]]] % 32\n    const gi3 = perm[ii + i3 + perm[jj + j3 + perm[kk + k3 + perm[ll + l3]]]] % 32\n    const gi4 = perm[ii + 1 + perm[jj + 1 + perm[kk + 1 + perm[ll + 1]]]] % 32\n    // Calculate the contribution from the five corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0 - w0 * w0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot4(grad4[gi0], x0, y0, z0, w0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1 - w1 * w1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot4(grad4[gi1], x1, y1, z1, w1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2 - w2 * w2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot4(grad4[gi2], x2, y2, z2, w2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3 - w3 * w3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot4(grad4[gi3], x3, y3, z3, w3)\n    }\n\n    let t4 = 0.6 - x4 * x4 - y4 * y4 - z4 * z4 - w4 * w4\n    if (t4 < 0) {\n      n4 = 0.0\n    } else {\n      t4 *= t4\n      n4 = t4 * t4 * this.dot4(grad4[gi4], x4, y4, z4, w4)\n    }\n\n    // Sum up and scale the result to cover the range [-1,1]\n    return 27.0 * (n0 + n1 + n2 + n3 + n4)\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAYO,MAAMA,YAAA,CAAa;EAAA;AAAA;AAAA;AAAA;EAiIxBC,YAAYC,CAAA,GAAqBC,IAAA,EAAM;IAhI/BC,aAAA,gBAAQ,CACd,CAAC,GAAG,GAAG,CAAC,GACR,CAAC,IAAI,GAAG,CAAC,GACT,CAAC,GAAG,IAAI,CAAC,GACT,CAAC,IAAI,IAAI,CAAC,GACV,CAAC,GAAG,GAAG,CAAC,GACR,CAAC,IAAI,GAAG,CAAC,GACT,CAAC,GAAG,GAAG,EAAE,GACT,CAAC,IAAI,GAAG,EAAE,GACV,CAAC,GAAG,GAAG,CAAC,GACR,CAAC,GAAG,IAAI,CAAC,GACT,CAAC,GAAG,GAAG,EAAE,GACT,CAAC,GAAG,IAAI,EAAE;IAGJA,aAAA,gBAAQ,CACd,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,EAAE,GACZ,CAAC,GAAG,GAAG,IAAI,CAAC,GACZ,CAAC,GAAG,GAAG,IAAI,EAAE,GACb,CAAC,GAAG,IAAI,GAAG,CAAC,GACZ,CAAC,GAAG,IAAI,GAAG,EAAE,GACb,CAAC,GAAG,IAAI,IAAI,CAAC,GACb,CAAC,GAAG,IAAI,IAAI,EAAE,GACd,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,EAAE,GACZ,CAAC,GAAG,GAAG,IAAI,CAAC,GACZ,CAAC,GAAG,GAAG,IAAI,EAAE,GACb,CAAC,IAAI,GAAG,GAAG,CAAC,GACZ,CAAC,IAAI,GAAG,GAAG,EAAE,GACb,CAAC,IAAI,GAAG,IAAI,CAAC,GACb,CAAC,IAAI,GAAG,IAAI,EAAE,GACd,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,EAAE,GACZ,CAAC,GAAG,IAAI,GAAG,CAAC,GACZ,CAAC,GAAG,IAAI,GAAG,EAAE,GACb,CAAC,IAAI,GAAG,GAAG,CAAC,GACZ,CAAC,IAAI,GAAG,GAAG,EAAE,GACb,CAAC,IAAI,IAAI,GAAG,CAAC,GACb,CAAC,IAAI,IAAI,GAAG,EAAE,GACd,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,IAAI,CAAC,GACZ,CAAC,GAAG,IAAI,GAAG,CAAC,GACZ,CAAC,GAAG,IAAI,IAAI,CAAC,GACb,CAAC,IAAI,GAAG,GAAG,CAAC,GACZ,CAAC,IAAI,GAAG,IAAI,CAAC,GACb,CAAC,IAAI,IAAI,GAAG,CAAC,GACb,CAAC,IAAI,IAAI,IAAI,CAAC;IAGRA,aAAA,YAAc;IAGd;IAAAA,aAAA,eAAiB;IAIjB;IAAA;IAAAA,aAAA,kBAAU,CAChB,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC,GACX,CAAC,GAAG,GAAG,GAAG,CAAC;IAiBNA,aAAA,cAAM,CAACC,CAAA,EAAaC,CAAA,EAAWC,CAAA,KAAsB;MAC1D,OAAOF,CAAA,CAAE,CAAC,IAAIC,CAAA,GAAID,CAAA,CAAE,CAAC,IAAIE,CAAA;IAAA;IAGpBH,aAAA,eAAO,CAACC,CAAA,EAAaC,CAAA,EAAWC,CAAA,EAAWC,CAAA,KAAsB;MAC/D,OAAAH,CAAA,CAAE,CAAC,IAAIC,CAAA,GAAID,CAAA,CAAE,CAAC,IAAIE,CAAA,GAAIF,CAAA,CAAE,CAAC,IAAIG,CAAA;IAAA;IAG/BJ,aAAA,eAAO,CAACC,CAAA,EAAaC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAWC,CAAA,KAAsB;MACjF,OAAOJ,CAAA,CAAE,CAAC,IAAIC,CAAA,GAAID,CAAA,CAAE,CAAC,IAAIE,CAAA,GAAIF,CAAA,CAAE,CAAC,IAAIG,CAAA,GAAIH,CAAA,CAAE,CAAC,IAAII,CAAA;IAAA;IAG1CL,aAAA,gBAAQ,CAACM,GAAA,EAAaC,GAAA,KAAwB;MAC/C,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MAEJ,MAAMC,EAAA,GAAK,OAAOZ,IAAA,CAAKa,IAAA,CAAK,CAAG,IAAI;MAC7B,MAAAC,CAAA,IAAKP,GAAA,GAAMC,GAAA,IAAOI,EAAA;MACxB,MAAMG,CAAA,GAAIf,IAAA,CAAKgB,KAAA,CAAMT,GAAA,GAAMO,CAAC;MAC5B,MAAMG,CAAA,GAAIjB,IAAA,CAAKgB,KAAA,CAAMR,GAAA,GAAMM,CAAC;MAC5B,MAAMI,EAAA,IAAM,IAAMlB,IAAA,CAAKa,IAAA,CAAK,CAAG,KAAK;MAC9B,MAAAM,CAAA,IAAKJ,CAAA,GAAIE,CAAA,IAAKC,EAAA;MACpB,MAAME,EAAA,GAAKL,CAAA,GAAII,CAAA;MACf,MAAME,EAAA,GAAKJ,CAAA,GAAIE,CAAA;MACf,MAAMG,EAAA,GAAKf,GAAA,GAAMa,EAAA;MACjB,MAAMG,EAAA,GAAKf,GAAA,GAAMa,EAAA;MAIjB,IAAIG,EAAA,GAAK;MAET,IAAIC,EAAA,GAAK;MACT,IAAIH,EAAA,GAAKC,EAAA,EAAI;QACNC,EAAA;QACAC,EAAA;MACP;MAKM,MAAAC,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKN,EAAA;MACf,MAAAS,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKP,EAAA;MACf,MAAAU,EAAA,GAAKN,EAAA,GAAK,IAAM,IAAMJ,EAAA;MACtB,MAAAW,EAAA,GAAKN,EAAA,GAAK,IAAM,IAAML,EAAA;MAE5B,MAAMY,EAAA,GAAKf,CAAA,GAAI;MACf,MAAMgB,EAAA,GAAKd,CAAA,GAAI;MACT,MAAAe,GAAA,GAAM,KAAKC,IAAA,CAAKH,EAAA,GAAK,KAAKG,IAAA,CAAKF,EAAE,CAAC,IAAI;MACtC,MAAAG,GAAA,GAAM,KAAKD,IAAA,CAAKH,EAAA,GAAKN,EAAA,GAAK,KAAKS,IAAA,CAAKF,EAAA,GAAKN,EAAE,CAAC,IAAI;MAChD,MAAAU,GAAA,GAAM,KAAKF,IAAA,CAAKH,EAAA,GAAK,IAAI,KAAKG,IAAA,CAAKF,EAAA,GAAK,CAAC,CAAC,IAAI;MAEpD,IAAIK,EAAA,GAAK,MAAMd,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA;MAC9B,IAAIa,EAAA,GAAK,GAAG;QACL3B,EAAA;MAAA,OACA;QACC2B,EAAA,IAAAA,EAAA;QACD3B,EAAA,GAAA2B,EAAA,GAAKA,EAAA,GAAK,KAAKC,GAAA,CAAI,KAAKC,KAAA,CAAMN,GAAG,GAAGV,EAAA,EAAIC,EAAE;MACjD;MAEA,IAAIgB,EAAA,GAAK,MAAMb,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA;MAC9B,IAAIY,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKF,GAAA,CAAI,KAAKC,KAAA,CAAMJ,GAAG,GAAGR,EAAA,EAAIC,EAAE;MACjD;MAEA,IAAIa,EAAA,GAAK,MAAMZ,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA;MAC9B,IAAIW,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKH,GAAA,CAAI,KAAKC,KAAA,CAAMH,GAAG,GAAGP,EAAA,EAAIC,EAAE;MACjD;MAIO,aAAQpB,EAAA,GAAKC,EAAA,GAAKC,EAAA;IAAA;IAInB;IAAAV,aAAA,kBAAU,CAACM,GAAA,EAAaC,GAAA,EAAaiC,GAAA,KAAwB;MAE/D,IAAAhC,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAA+B,EAAA;MAEJ,MAAMC,EAAA,GAAK,IAAM;MACX,MAAA7B,CAAA,IAAKP,GAAA,GAAMC,GAAA,GAAMiC,GAAA,IAAOE,EAAA;MAC9B,MAAM5B,CAAA,GAAIf,IAAA,CAAKgB,KAAA,CAAMT,GAAA,GAAMO,CAAC;MAC5B,MAAMG,CAAA,GAAIjB,IAAA,CAAKgB,KAAA,CAAMR,GAAA,GAAMM,CAAC;MAC5B,MAAM8B,CAAA,GAAI5C,IAAA,CAAKgB,KAAA,CAAMyB,GAAA,GAAM3B,CAAC;MAC5B,MAAM+B,EAAA,GAAK,IAAM;MACX,MAAA1B,CAAA,IAAKJ,CAAA,GAAIE,CAAA,GAAI2B,CAAA,IAAKC,EAAA;MACxB,MAAMzB,EAAA,GAAKL,CAAA,GAAII,CAAA;MACf,MAAME,EAAA,GAAKJ,CAAA,GAAIE,CAAA;MACf,MAAM2B,EAAA,GAAKF,CAAA,GAAIzB,CAAA;MACf,MAAMG,EAAA,GAAKf,GAAA,GAAMa,EAAA;MACjB,MAAMG,EAAA,GAAKf,GAAA,GAAMa,EAAA;MACjB,MAAM0B,EAAA,GAAKN,GAAA,GAAMK,EAAA;MAGb,IAAAtB,EAAA;MACA,IAAAC,EAAA;MACA,IAAAuB,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACJ,IAAI7B,EAAA,IAAMC,EAAA,EAAI;QACZ,IAAIA,EAAA,IAAMwB,EAAA,EAAI;UACPvB,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QAAA,WAGI7B,EAAA,IAAMyB,EAAA,EAAI;UACdvB,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QAAA,OAGA;UACA3B,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QACP;MAAA,OACK;QAGL,IAAI5B,EAAA,GAAKwB,EAAA,EAAI;UACNvB,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QAAA,WAGI7B,EAAA,GAAKyB,EAAA,EAAI;UACbvB,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QAAA,OAGA;UACA3B,EAAA;UACAC,EAAA;UACAuB,EAAA;UACAC,EAAA;UACAC,EAAA;UACAC,EAAA;QACP;MACF;MAMM,MAAAzB,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKqB,EAAA;MACf,MAAAlB,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKoB,EAAA;MACf,MAAAO,EAAA,GAAKL,EAAA,GAAKC,EAAA,GAAKH,EAAA;MACf,MAAAjB,EAAA,GAAKN,EAAA,GAAK2B,EAAA,GAAK,IAAMJ,EAAA;MACrB,MAAAhB,EAAA,GAAKN,EAAA,GAAK2B,EAAA,GAAK,IAAML,EAAA;MACrB,MAAAQ,EAAA,GAAKN,EAAA,GAAKI,EAAA,GAAK,IAAMN,EAAA;MACrB,MAAAS,EAAA,GAAKhC,EAAA,GAAK,IAAM,IAAMuB,EAAA;MACtB,MAAAU,EAAA,GAAKhC,EAAA,GAAK,IAAM,IAAMsB,EAAA;MACtB,MAAAW,EAAA,GAAKT,EAAA,GAAK,IAAM,IAAMF,EAAA;MAE5B,MAAMf,EAAA,GAAKf,CAAA,GAAI;MACf,MAAMgB,EAAA,GAAKd,CAAA,GAAI;MACf,MAAMwC,EAAA,GAAKb,CAAA,GAAI;MACf,MAAMZ,GAAA,GAAM,KAAKC,IAAA,CAAKH,EAAA,GAAK,KAAKG,IAAA,CAAKF,EAAA,GAAK,KAAKE,IAAA,CAAKwB,EAAE,CAAC,CAAC,IAAI;MAC5D,MAAMvB,GAAA,GAAM,KAAKD,IAAA,CAAKH,EAAA,GAAKN,EAAA,GAAK,KAAKS,IAAA,CAAKF,EAAA,GAAKN,EAAA,GAAK,KAAKQ,IAAA,CAAKwB,EAAA,GAAKT,EAAE,CAAC,CAAC,IAAI;MAC3E,MAAMb,GAAA,GAAM,KAAKF,IAAA,CAAKH,EAAA,GAAKmB,EAAA,GAAK,KAAKhB,IAAA,CAAKF,EAAA,GAAKmB,EAAA,GAAK,KAAKjB,IAAA,CAAKwB,EAAA,GAAKN,EAAE,CAAC,CAAC,IAAI;MAC3E,MAAMO,GAAA,GAAM,KAAKzB,IAAA,CAAKH,EAAA,GAAK,IAAI,KAAKG,IAAA,CAAKF,EAAA,GAAK,IAAI,KAAKE,IAAA,CAAKwB,EAAA,GAAK,CAAC,CAAC,CAAC,IAAI;MAExE,IAAIrB,EAAA,GAAK,MAAMd,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKwB,EAAA,GAAKA,EAAA;MACxC,IAAIX,EAAA,GAAK,GAAG;QACL3B,EAAA;MAAA,OACA;QACC2B,EAAA,IAAAA,EAAA;QACD3B,EAAA,GAAA2B,EAAA,GAAKA,EAAA,GAAK,KAAKuB,IAAA,CAAK,KAAKrB,KAAA,CAAMN,GAAG,GAAGV,EAAA,EAAIC,EAAA,EAAIwB,EAAE;MACtD;MAEA,IAAIR,EAAA,GAAK,MAAMb,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKyB,EAAA,GAAKA,EAAA;MACxC,IAAIb,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKoB,IAAA,CAAK,KAAKrB,KAAA,CAAMJ,GAAG,GAAGR,EAAA,EAAIC,EAAA,EAAIyB,EAAE;MACtD;MAEA,IAAIZ,EAAA,GAAK,MAAMZ,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKwB,EAAA,GAAKA,EAAA;MACxC,IAAIb,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKmB,IAAA,CAAK,KAAKrB,KAAA,CAAMH,GAAG,GAAGP,EAAA,EAAIC,EAAA,EAAIwB,EAAE;MACtD;MAEA,IAAIO,EAAA,GAAK,MAAMN,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA;MACxC,IAAII,EAAA,GAAK,GAAG;QACLlB,EAAA;MAAA,OACA;QACCkB,EAAA,IAAAA,EAAA;QACDlB,EAAA,GAAAkB,EAAA,GAAKA,EAAA,GAAK,KAAKD,IAAA,CAAK,KAAKrB,KAAA,CAAMoB,GAAG,GAAGJ,EAAA,EAAIC,EAAA,EAAIC,EAAE;MACtD;MAIO,aAAQ/C,EAAA,GAAKC,EAAA,GAAKC,EAAA,GAAK+B,EAAA;IAAA;IAIzB;IAAAzC,aAAA,kBAAU,CAACE,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAWC,CAAA,KAAsB;MAEvE,MAAMuD,KAAA,GAAQ,KAAKA,KAAA;MACnB,MAAMC,OAAA,GAAU,KAAKA,OAAA;MACrB,MAAM7B,IAAA,GAAO,KAAKA,IAAA;MAGlB,MAAM8B,EAAA,IAAM/D,IAAA,CAAKa,IAAA,CAAK,CAAG,IAAI,KAAO;MACpC,MAAMmD,EAAA,IAAM,IAAMhE,IAAA,CAAKa,IAAA,CAAK,CAAG,KAAK;MAChC,IAAAJ,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAA+B,EAAA;MACA,IAAAuB,EAAA;MAEJ,MAAMnD,CAAA,IAAKX,CAAA,GAAIC,CAAA,GAAIC,CAAA,GAAIC,CAAA,IAAKyD,EAAA;MAC5B,MAAMhD,CAAA,GAAIf,IAAA,CAAKgB,KAAA,CAAMb,CAAA,GAAIW,CAAC;MAC1B,MAAMG,CAAA,GAAIjB,IAAA,CAAKgB,KAAA,CAAMZ,CAAA,GAAIU,CAAC;MAC1B,MAAM8B,CAAA,GAAI5C,IAAA,CAAKgB,KAAA,CAAMX,CAAA,GAAIS,CAAC;MAC1B,MAAMoD,CAAA,GAAIlE,IAAA,CAAKgB,KAAA,CAAMV,CAAA,GAAIQ,CAAC;MAC1B,MAAMK,CAAA,IAAKJ,CAAA,GAAIE,CAAA,GAAI2B,CAAA,GAAIsB,CAAA,IAAKF,EAAA;MAC5B,MAAM5C,EAAA,GAAKL,CAAA,GAAII,CAAA;MACf,MAAME,EAAA,GAAKJ,CAAA,GAAIE,CAAA;MACf,MAAM2B,EAAA,GAAKF,CAAA,GAAIzB,CAAA;MACf,MAAMgD,EAAA,GAAKD,CAAA,GAAI/C,CAAA;MACf,MAAMG,EAAA,GAAKnB,CAAA,GAAIiB,EAAA;MACf,MAAMG,EAAA,GAAKnB,CAAA,GAAIiB,EAAA;MACf,MAAM0B,EAAA,GAAK1C,CAAA,GAAIyC,EAAA;MACf,MAAMsB,EAAA,GAAK9D,CAAA,GAAI6D,EAAA;MAUT,MAAAE,EAAA,GAAK/C,EAAA,GAAKC,EAAA,GAAK,KAAK;MACpB,MAAA+C,EAAA,GAAKhD,EAAA,GAAKyB,EAAA,GAAK,KAAK;MACpB,MAAAwB,EAAA,GAAKhD,EAAA,GAAKwB,EAAA,GAAK,IAAI;MACnB,MAAAyB,EAAA,GAAKlD,EAAA,GAAK8C,EAAA,GAAK,IAAI;MACnB,MAAAK,EAAA,GAAKlD,EAAA,GAAK6C,EAAA,GAAK,IAAI;MACnB,MAAAM,EAAA,GAAK3B,EAAA,GAAKqB,EAAA,GAAK,IAAI;MACzB,MAAMO,CAAA,GAAIN,EAAA,GAAKC,EAAA,GAAKC,EAAA,GAAKC,EAAA,GAAKC,EAAA,GAAKC,EAAA;MAE/B,IAAAlD,EAAA;MACA,IAAAC,EAAA;MACA,IAAAuB,EAAA;MACA,IAAA4B,EAAA;MAGA,IAAA3B,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAA0B,EAAA;MAGA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MACA,IAAAC,EAAA;MAMJzD,EAAA,GAAKsC,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BlD,EAAA,GAAKqC,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9B3B,EAAA,GAAKc,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BC,EAAA,GAAKd,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAE9B1B,EAAA,GAAKa,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BzB,EAAA,GAAKY,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BxB,EAAA,GAAKW,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BE,EAAA,GAAKf,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAE9BG,EAAA,GAAKhB,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BI,EAAA,GAAKjB,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BK,EAAA,GAAKlB,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAC9BM,EAAA,GAAKnB,OAAA,CAAQa,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI;MAExB,MAAAjD,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKwC,EAAA;MACf,MAAArC,EAAA,GAAKJ,EAAA,GAAKE,EAAA,GAAKuC,EAAA;MACf,MAAAZ,EAAA,GAAKL,EAAA,GAAKC,EAAA,GAAKgB,EAAA;MACf,MAAAkB,EAAA,GAAKd,EAAA,GAAKQ,EAAA,GAAKZ,EAAA;MACf,MAAApC,EAAA,GAAKN,EAAA,GAAK2B,EAAA,GAAK,IAAMe,EAAA;MACrB,MAAAnC,EAAA,GAAKN,EAAA,GAAK2B,EAAA,GAAK,IAAMc,EAAA;MACrB,MAAAX,EAAA,GAAKN,EAAA,GAAKI,EAAA,GAAK,IAAMa,EAAA;MACrB,MAAAmB,EAAA,GAAKf,EAAA,GAAKS,EAAA,GAAK,IAAMb,EAAA;MACrB,MAAAV,EAAA,GAAKhC,EAAA,GAAKwD,EAAA,GAAK,IAAMd,EAAA;MACrB,MAAAT,EAAA,GAAKhC,EAAA,GAAKwD,EAAA,GAAK,IAAMf,EAAA;MACrB,MAAAR,EAAA,GAAKT,EAAA,GAAKiC,EAAA,GAAK,IAAMhB,EAAA;MACrB,MAAAoB,EAAA,GAAKhB,EAAA,GAAKa,EAAA,GAAK,IAAMjB,EAAA;MACrB,MAAAqB,EAAA,GAAK/D,EAAA,GAAK,IAAM,IAAM0C,EAAA;MACtB,MAAAsB,EAAA,GAAK/D,EAAA,GAAK,IAAM,IAAMyC,EAAA;MACtB,MAAAuB,EAAA,GAAKxC,EAAA,GAAK,IAAM,IAAMiB,EAAA;MACtB,MAAAwB,EAAA,GAAKpB,EAAA,GAAK,IAAM,IAAMJ,EAAA;MAE5B,MAAMlC,EAAA,GAAKf,CAAA,GAAI;MACf,MAAMgB,EAAA,GAAKd,CAAA,GAAI;MACf,MAAMwC,EAAA,GAAKb,CAAA,GAAI;MACf,MAAM6C,EAAA,GAAKvB,CAAA,GAAI;MACf,MAAMlC,GAAA,GAAMC,IAAA,CAAKH,EAAA,GAAKG,IAAA,CAAKF,EAAA,GAAKE,IAAA,CAAKwB,EAAA,GAAKxB,IAAA,CAAKwD,EAAE,CAAC,CAAC,CAAC,IAAI;MACxD,MAAMvD,GAAA,GAAMD,IAAA,CAAKH,EAAA,GAAKN,EAAA,GAAKS,IAAA,CAAKF,EAAA,GAAKN,EAAA,GAAKQ,IAAA,CAAKwB,EAAA,GAAKT,EAAA,GAAKf,IAAA,CAAKwD,EAAA,GAAKb,EAAE,CAAC,CAAC,CAAC,IAAI;MAC5E,MAAMzC,GAAA,GAAMF,IAAA,CAAKH,EAAA,GAAKmB,EAAA,GAAKhB,IAAA,CAAKF,EAAA,GAAKmB,EAAA,GAAKjB,IAAA,CAAKwB,EAAA,GAAKN,EAAA,GAAKlB,IAAA,CAAKwD,EAAA,GAAKZ,EAAE,CAAC,CAAC,CAAC,IAAI;MAC5E,MAAMnB,GAAA,GAAMzB,IAAA,CAAKH,EAAA,GAAKgD,EAAA,GAAK7C,IAAA,CAAKF,EAAA,GAAKgD,EAAA,GAAK9C,IAAA,CAAKwB,EAAA,GAAKuB,EAAA,GAAK/C,IAAA,CAAKwD,EAAA,GAAKR,EAAE,CAAC,CAAC,CAAC,IAAI;MAC5E,MAAMS,GAAA,GAAMzD,IAAA,CAAKH,EAAA,GAAK,IAAIG,IAAA,CAAKF,EAAA,GAAK,IAAIE,IAAA,CAAKwB,EAAA,GAAK,IAAIxB,IAAA,CAAKwD,EAAA,GAAK,CAAC,CAAC,CAAC,CAAC,IAAI;MAEpE,IAAArD,EAAA,GAAK,MAAMd,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKwB,EAAA,GAAKA,EAAA,GAAKqB,EAAA,GAAKA,EAAA;MAClD,IAAIhC,EAAA,GAAK,GAAG;QACL3B,EAAA;MAAA,OACA;QACC2B,EAAA,IAAAA,EAAA;QACD3B,EAAA,GAAA2B,EAAA,GAAKA,EAAA,GAAK,KAAKuD,IAAA,CAAK9B,KAAA,CAAM7B,GAAG,GAAGV,EAAA,EAAIC,EAAA,EAAIwB,EAAA,EAAIqB,EAAE;MACrD;MAEI,IAAA7B,EAAA,GAAK,MAAMb,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKyB,EAAA,GAAKA,EAAA,GAAK8B,EAAA,GAAKA,EAAA;MAClD,IAAI3C,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKoD,IAAA,CAAK9B,KAAA,CAAM3B,GAAG,GAAGR,EAAA,EAAIC,EAAA,EAAIyB,EAAA,EAAI8B,EAAE;MACrD;MAEI,IAAA1C,EAAA,GAAK,MAAMZ,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKwB,EAAA,GAAKA,EAAA,GAAK8B,EAAA,GAAKA,EAAA;MAClD,IAAI3C,EAAA,GAAK,GAAG;QACL7B,EAAA;MAAA,OACA;QACC6B,EAAA,IAAAA,EAAA;QACD7B,EAAA,GAAA6B,EAAA,GAAKA,EAAA,GAAK,KAAKmD,IAAA,CAAK9B,KAAA,CAAM1B,GAAG,GAAGP,EAAA,EAAIC,EAAA,EAAIwB,EAAA,EAAI8B,EAAE;MACrD;MAEI,IAAAvB,EAAA,GAAK,MAAMN,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAK4B,EAAA,GAAKA,EAAA;MAClD,IAAIxB,EAAA,GAAK,GAAG;QACLlB,EAAA;MAAA,OACA;QACCkB,EAAA,IAAAA,EAAA;QACDlB,EAAA,GAAAkB,EAAA,GAAKA,EAAA,GAAK,KAAK+B,IAAA,CAAK9B,KAAA,CAAMH,GAAG,GAAGJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI4B,EAAE;MACrD;MAEI,IAAAQ,EAAA,GAAK,MAAMP,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA;MAClD,IAAII,EAAA,GAAK,GAAG;QACL3B,EAAA;MAAA,OACA;QACC2B,EAAA,IAAAA,EAAA;QACD3B,EAAA,GAAA2B,EAAA,GAAKA,EAAA,GAAK,KAAKD,IAAA,CAAK9B,KAAA,CAAM6B,GAAG,GAAGL,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAE;MACrD;MAGA,OAAO,MAAQ/E,EAAA,GAAKC,EAAA,GAAKC,EAAA,GAAK+B,EAAA,GAAKuB,EAAA;IAAA;IAnYnC,SAASlD,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKA,CAAA,IAAK;MACvB,KAAA8E,CAAA,CAAE9E,CAAC,IAAIf,IAAA,CAAKgB,KAAA,CAAMjB,CAAA,CAAE+F,MAAA,KAAW,GAAG;IACzC;IAEA,SAAS/E,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKA,CAAA,IAAK;MAC5B,KAAKkB,IAAA,CAAKlB,CAAC,IAAI,KAAK8E,CAAA,CAAE9E,CAAA,GAAI,GAAG;IAC/B;EACF;AA8XF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}