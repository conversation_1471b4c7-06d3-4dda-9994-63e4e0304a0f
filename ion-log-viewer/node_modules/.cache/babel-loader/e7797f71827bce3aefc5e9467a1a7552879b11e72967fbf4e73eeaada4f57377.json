{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport IntSize from \"./IntSize\";\nimport { Catalog } from \"./IonCatalog\";\nimport { Decimal } from \"./IonDecimal\";\nimport { defaultLocalSymbolTable } from \"./IonLocalSymbolTable\";\nimport { get_ion_type, ParserTextRaw } from \"./IonParserTextRaw\";\nimport { ion_symbol_table, makeSymbolTable } from \"./IonSymbols\";\nimport { fromBase64 } from \"./IonText\";\nimport { Timestamp } from \"./IonTimestamp\";\nimport { IonTypes } from \"./IonTypes\";\nimport { isSafeInteger } from \"./util\";\nconst BEGINNING_OF_CONTAINER = -2;\nconst EOF = -1;\nconst T_IDENTIFIER = 9;\nconst T_STRING1 = 11;\nconst T_CLOB2 = 14;\nconst T_CLOB3 = 15;\nconst T_STRUCT = 19;\nexport class TextReader {\n  constructor(source, catalog) {\n    if (!source) {\n      throw new Error(\"a source Span is required to make a reader\");\n    }\n    this._parser = new ParserTextRaw(source);\n    this._depth = 0;\n    this._cat = catalog ? catalog : new Catalog();\n    this._symtab = defaultLocalSymbolTable();\n    this._type = null;\n    this._raw_type = undefined;\n    this._raw = undefined;\n  }\n  load_raw() {\n    const t = this;\n    if (t._raw !== undefined) {\n      return;\n    }\n    if (t._raw_type === T_CLOB2 || t._raw_type === T_CLOB3) {\n      t._raw = t._parser.get_value_as_uint8array(t._raw_type);\n    } else {\n      t._raw = t._parser.get_value_as_string(t._raw_type);\n    }\n  }\n  skip_past_container() {\n    let type;\n    const d = this.depth();\n    this.stepIn();\n    while (this.depth() > d) {\n      type = this.next();\n      if (type === null) {\n        this.stepOut();\n      } else if (type.isContainer && !this.isNull()) {\n        this.stepIn();\n      }\n    }\n  }\n  isIVM(input, depth, annotations) {\n    if (depth > 0) {\n      return false;\n    }\n    const ivm = \"$ion_1_0\";\n    const prefix = \"$ion_\";\n    if (input.length < ivm.length || annotations.length > 0) {\n      return false;\n    }\n    let i = 0;\n    while (i < prefix.length) {\n      if (prefix.charAt(i) !== input.charAt(i)) {\n        return false;\n      }\n      i++;\n    }\n    while (i < input.length && input.charAt(i) != \"_\") {\n      const ch = input.charAt(i);\n      if (ch < \"0\" || ch > \"9\") {\n        return false;\n      }\n      i++;\n    }\n    i++;\n    while (i < input.length) {\n      const ch = input.charAt(i);\n      if (ch < \"0\" || ch > \"9\") {\n        return false;\n      }\n      i++;\n    }\n    if (input !== ivm) {\n      throw new Error(\"Only Ion version 1.0 is supported.\");\n    }\n    return true;\n  }\n  isLikeIVM() {\n    return false;\n  }\n  position() {\n    return this._parser.source().position();\n  }\n  next() {\n    this._raw = undefined;\n    if (this._raw_type === EOF) {\n      return null;\n    }\n    if (this._raw_type !== BEGINNING_OF_CONTAINER && !this.isNull() && this._type && this._type.isContainer) {\n      this.skip_past_container();\n    }\n    const p = this._parser;\n    for (;;) {\n      this._raw_type = p.next();\n      if (this._raw_type === T_IDENTIFIER) {\n        if (this._depth > 0) {\n          break;\n        }\n        this.load_raw();\n        if (!this.isIVM(this._raw, this.depth(), this.annotations())) {\n          break;\n        }\n        this._symtab = defaultLocalSymbolTable();\n        this._raw = undefined;\n        this._raw_type = undefined;\n      } else if (this._raw_type === T_STRING1) {\n        if (this._depth > 0) {\n          break;\n        }\n        this.load_raw();\n        if (this._raw !== \"$ion_1_0\") {\n          break;\n        }\n        this._raw = undefined;\n        this._raw_type = undefined;\n      } else if (this._raw_type === T_STRUCT) {\n        if (p.annotations().length !== 1) {\n          break;\n        }\n        if (p.annotations()[0].getText() != ion_symbol_table) {\n          break;\n        }\n        this._type = get_ion_type(this._raw_type);\n        this._symtab = makeSymbolTable(this._cat, this, this._symtab);\n        this._raw = undefined;\n        this._raw_type = undefined;\n      } else {\n        break;\n      }\n    }\n    this._type = get_ion_type(this._raw_type);\n    return this._type;\n  }\n  stepIn() {\n    if (!this._type.isContainer) {\n      throw new Error(\"can't step in to a scalar value\");\n    }\n    if (this.isNull()) {\n      throw new Error(\"Can't step into a null container\");\n    }\n    this._parser.clearFieldName();\n    this._type = null;\n    this._raw_type = BEGINNING_OF_CONTAINER;\n    this._depth++;\n  }\n  stepOut() {\n    this._parser.clearFieldName();\n    while (this._raw_type != EOF) {\n      this.next();\n    }\n    this._raw_type = undefined;\n    if (this._depth <= 0) {\n      throw new Error(\"Cannot stepOut any further, already at top level\");\n    }\n    this._depth--;\n  }\n  type() {\n    return this._type;\n  }\n  depth() {\n    return this._depth;\n  }\n  fieldName() {\n    const str = this._parser.fieldName();\n    if (str !== null) {\n      const raw_type = this._parser.fieldNameType();\n      if (raw_type === T_IDENTIFIER && str.length > 1 && str[0] === \"$\") {\n        const tempStr = str.substr(1, str.length);\n        if (+tempStr === +tempStr) {\n          const symbol = this._symtab.getSymbolText(Number(tempStr));\n          if (symbol === undefined) {\n            throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n          }\n          return symbol;\n        }\n      }\n    }\n    return str;\n  }\n  annotations() {\n    return this._parser.annotations().map(st => {\n      const text = st.getText();\n      if (text !== null) {\n        return text;\n      } else {\n        const symbol = this._symtab.getSymbolText(st.getSid());\n        if (symbol === undefined || symbol === null) {\n          throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n        }\n        return symbol;\n      }\n    });\n  }\n  isNull() {\n    if (this._type === IonTypes.NULL) {\n      return true;\n    }\n    return this._parser.isNull();\n  }\n  _stringRepresentation() {\n    this.load_raw();\n    if (this.isNull()) {\n      return this._type === IonTypes.NULL ? \"null\" : \"null.\" + this._type.name;\n    }\n    return this._raw;\n  }\n  booleanValue() {\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.BOOL:\n        return this._parser.booleanValue();\n    }\n    throw new Error(\"Current value is not a Boolean.\");\n  }\n  uInt8ArrayValue() {\n    this.load_raw();\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.BLOB:\n        if (this.isNull()) {\n          return null;\n        }\n        return fromBase64(this._raw);\n      case IonTypes.CLOB:\n        if (this.isNull()) {\n          return null;\n        }\n        return this._raw;\n    }\n    throw new Error(\"Current value is not a blob or clob.\");\n  }\n  decimalValue() {\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.DECIMAL:\n        return Decimal.parse(this._stringRepresentation());\n    }\n    throw new Error(\"Current value is not a decimal.\");\n  }\n  bigIntValue() {\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.INT:\n        return this._parser.bigIntValue();\n    }\n    throw new Error(\"bigIntValue() was called when the current value was a(n) \" + this._type.name);\n  }\n  intSize() {\n    if (isSafeInteger(this.bigIntValue())) {\n      return IntSize.Number;\n    }\n    return IntSize.BigInt;\n  }\n  numberValue() {\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.FLOAT:\n      case IonTypes.INT:\n        return this._parser.numberValue();\n    }\n    throw new Error(\"Current value is not a float or int.\");\n  }\n  stringValue() {\n    this.load_raw();\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.STRING:\n        if (this._parser.isNull()) {\n          return null;\n        }\n        return this._raw;\n      case IonTypes.SYMBOL:\n        if (this._parser.isNull()) {\n          return null;\n        }\n        if (this._raw_type === T_IDENTIFIER && this._raw.length > 1 && this._raw.charAt(0) === \"$\".charAt(0)) {\n          const tempStr = this._raw.substr(1, this._raw.length);\n          if (+tempStr === +tempStr) {\n            const symbolId = Number(tempStr);\n            const symbol = this._symtab.getSymbolText(symbolId);\n            if (symbol === undefined) {\n              throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n            }\n            return symbol;\n          }\n        }\n        return this._raw;\n    }\n    throw new Error(\"Current value is not a string or symbol.\");\n  }\n  timestampValue() {\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.TIMESTAMP:\n        return Timestamp.parse(this._stringRepresentation());\n    }\n    throw new Error(\"Current value is not a timestamp.\");\n  }\n  value() {\n    if (this._type && this._type.isContainer) {\n      if (this.isNull()) {\n        return null;\n      }\n      throw new Error(\"Unable to provide a value for \" + this._type.name + \" containers.\");\n    }\n    switch (this._type) {\n      case IonTypes.NULL:\n        return null;\n      case IonTypes.BLOB:\n      case IonTypes.CLOB:\n        return this.uInt8ArrayValue();\n      case IonTypes.BOOL:\n        return this.booleanValue();\n      case IonTypes.DECIMAL:\n        return this.decimalValue();\n      case IonTypes.INT:\n        return this.bigIntValue();\n      case IonTypes.FLOAT:\n        return this.numberValue();\n      case IonTypes.STRING:\n      case IonTypes.SYMBOL:\n        return this.stringValue();\n      case IonTypes.TIMESTAMP:\n        return this.timestampValue();\n      default:\n        throw new Error(\"There is no current value.\");\n    }\n  }\n}", "map": {"version": 3, "names": ["IntSize", "Catalog", "Decimal", "defaultLocalSymbolTable", "get_ion_type", "ParserTextRaw", "ion_symbol_table", "makeSymbolTable", "fromBase64", "Timestamp", "IonTypes", "isSafeInteger", "BEGINNING_OF_CONTAINER", "EOF", "T_IDENTIFIER", "T_STRING1", "T_CLOB2", "T_CLOB3", "T_STRUCT", "TextReader", "constructor", "source", "catalog", "Error", "_parser", "_depth", "_cat", "_symtab", "_type", "_raw_type", "undefined", "_raw", "load_raw", "t", "get_value_as_uint8array", "get_value_as_string", "skip_past_container", "type", "d", "depth", "stepIn", "next", "stepOut", "<PERSON><PERSON><PERSON><PERSON>", "isNull", "isIVM", "input", "annotations", "ivm", "prefix", "length", "i", "char<PERSON>t", "ch", "isLikeIVM", "position", "p", "getText", "clearFieldName", "fieldName", "str", "raw_type", "fieldNameType", "tempStr", "substr", "symbol", "getSymbolText", "Number", "map", "st", "text", "getSid", "NULL", "_stringRepresentation", "name", "booleanValue", "BOOL", "uInt8ArrayValue", "BLOB", "CLOB", "decimalValue", "DECIMAL", "parse", "bigIntValue", "INT", "intSize", "BigInt", "numberValue", "FLOAT", "stringValue", "STRING", "SYMBOL", "symbolId", "timestampValue", "TIMESTAMP", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonTextReader.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport IntSize from \"./IntSize\";\nimport { Catalog } from \"./IonCatalog\";\nimport { Decimal } from \"./IonDecimal\";\nimport { defaultLocalSymbolTable, } from \"./IonLocalSymbolTable\";\nimport { get_ion_type, ParserTextRaw } from \"./IonParserTextRaw\";\nimport { ion_symbol_table, makeSymbolTable } from \"./IonSymbols\";\nimport { fromBase64 } from \"./IonText\";\nimport { Timestamp } from \"./IonTimestamp\";\nimport { IonTypes } from \"./IonTypes\";\nimport { isSafeInteger } from \"./util\";\nconst BEGINNING_OF_CONTAINER = -2;\nconst EOF = -1;\nconst T_IDENTIFIER = 9;\nconst T_STRING1 = 11;\nconst T_CLOB2 = 14;\nconst T_CLOB3 = 15;\nconst T_STRUCT = 19;\nexport class TextReader {\n    constructor(source, catalog) {\n        if (!source) {\n            throw new Error(\"a source Span is required to make a reader\");\n        }\n        this._parser = new ParserTextRaw(source);\n        this._depth = 0;\n        this._cat = catalog ? catalog : new Catalog();\n        this._symtab = defaultLocalSymbolTable();\n        this._type = null;\n        this._raw_type = undefined;\n        this._raw = undefined;\n    }\n    load_raw() {\n        const t = this;\n        if (t._raw !== undefined) {\n            return;\n        }\n        if (t._raw_type === T_CLOB2 || t._raw_type === T_CLOB3) {\n            t._raw = t._parser.get_value_as_uint8array(t._raw_type);\n        }\n        else {\n            t._raw = t._parser.get_value_as_string(t._raw_type);\n        }\n    }\n    skip_past_container() {\n        let type;\n        const d = this.depth();\n        this.stepIn();\n        while (this.depth() > d) {\n            type = this.next();\n            if (type === null) {\n                this.stepOut();\n            }\n            else if (type.isContainer && !this.isNull()) {\n                this.stepIn();\n            }\n        }\n    }\n    isIVM(input, depth, annotations) {\n        if (depth > 0) {\n            return false;\n        }\n        const ivm = \"$ion_1_0\";\n        const prefix = \"$ion_\";\n        if (input.length < ivm.length || annotations.length > 0) {\n            return false;\n        }\n        let i = 0;\n        while (i < prefix.length) {\n            if (prefix.charAt(i) !== input.charAt(i)) {\n                return false;\n            }\n            i++;\n        }\n        while (i < input.length && input.charAt(i) != \"_\") {\n            const ch = input.charAt(i);\n            if (ch < \"0\" || ch > \"9\") {\n                return false;\n            }\n            i++;\n        }\n        i++;\n        while (i < input.length) {\n            const ch = input.charAt(i);\n            if (ch < \"0\" || ch > \"9\") {\n                return false;\n            }\n            i++;\n        }\n        if (input !== ivm) {\n            throw new Error(\"Only Ion version 1.0 is supported.\");\n        }\n        return true;\n    }\n    isLikeIVM() {\n        return false;\n    }\n    position() {\n        return this._parser.source().position();\n    }\n    next() {\n        this._raw = undefined;\n        if (this._raw_type === EOF) {\n            return null;\n        }\n        if (this._raw_type !== BEGINNING_OF_CONTAINER &&\n            !this.isNull() &&\n            this._type &&\n            this._type.isContainer) {\n            this.skip_past_container();\n        }\n        const p = this._parser;\n        for (;;) {\n            this._raw_type = p.next();\n            if (this._raw_type === T_IDENTIFIER) {\n                if (this._depth > 0) {\n                    break;\n                }\n                this.load_raw();\n                if (!this.isIVM(this._raw, this.depth(), this.annotations())) {\n                    break;\n                }\n                this._symtab = defaultLocalSymbolTable();\n                this._raw = undefined;\n                this._raw_type = undefined;\n            }\n            else if (this._raw_type === T_STRING1) {\n                if (this._depth > 0) {\n                    break;\n                }\n                this.load_raw();\n                if (this._raw !== \"$ion_1_0\") {\n                    break;\n                }\n                this._raw = undefined;\n                this._raw_type = undefined;\n            }\n            else if (this._raw_type === T_STRUCT) {\n                if (p.annotations().length !== 1) {\n                    break;\n                }\n                if (p.annotations()[0].getText() != ion_symbol_table) {\n                    break;\n                }\n                this._type = get_ion_type(this._raw_type);\n                this._symtab = makeSymbolTable(this._cat, this, this._symtab);\n                this._raw = undefined;\n                this._raw_type = undefined;\n            }\n            else {\n                break;\n            }\n        }\n        this._type = get_ion_type(this._raw_type);\n        return this._type;\n    }\n    stepIn() {\n        if (!this._type.isContainer) {\n            throw new Error(\"can't step in to a scalar value\");\n        }\n        if (this.isNull()) {\n            throw new Error(\"Can't step into a null container\");\n        }\n        this._parser.clearFieldName();\n        this._type = null;\n        this._raw_type = BEGINNING_OF_CONTAINER;\n        this._depth++;\n    }\n    stepOut() {\n        this._parser.clearFieldName();\n        while (this._raw_type != EOF) {\n            this.next();\n        }\n        this._raw_type = undefined;\n        if (this._depth <= 0) {\n            throw new Error(\"Cannot stepOut any further, already at top level\");\n        }\n        this._depth--;\n    }\n    type() {\n        return this._type;\n    }\n    depth() {\n        return this._depth;\n    }\n    fieldName() {\n        const str = this._parser.fieldName();\n        if (str !== null) {\n            const raw_type = this._parser.fieldNameType();\n            if (raw_type === T_IDENTIFIER && str.length > 1 && str[0] === \"$\") {\n                const tempStr = str.substr(1, str.length);\n                if (+tempStr === +tempStr) {\n                    const symbol = this._symtab.getSymbolText(Number(tempStr));\n                    if (symbol === undefined) {\n                        throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n                    }\n                    return symbol;\n                }\n            }\n        }\n        return str;\n    }\n    annotations() {\n        return this._parser.annotations().map((st) => {\n            const text = st.getText();\n            if (text !== null) {\n                return text;\n            }\n            else {\n                const symbol = this._symtab.getSymbolText(st.getSid());\n                if (symbol === undefined || symbol === null) {\n                    throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n                }\n                return symbol;\n            }\n        });\n    }\n    isNull() {\n        if (this._type === IonTypes.NULL) {\n            return true;\n        }\n        return this._parser.isNull();\n    }\n    _stringRepresentation() {\n        this.load_raw();\n        if (this.isNull()) {\n            return this._type === IonTypes.NULL ? \"null\" : \"null.\" + this._type.name;\n        }\n        return this._raw;\n    }\n    booleanValue() {\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.BOOL:\n                return this._parser.booleanValue();\n        }\n        throw new Error(\"Current value is not a Boolean.\");\n    }\n    uInt8ArrayValue() {\n        this.load_raw();\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.BLOB:\n                if (this.isNull()) {\n                    return null;\n                }\n                return fromBase64(this._raw);\n            case IonTypes.CLOB:\n                if (this.isNull()) {\n                    return null;\n                }\n                return this._raw;\n        }\n        throw new Error(\"Current value is not a blob or clob.\");\n    }\n    decimalValue() {\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.DECIMAL:\n                return Decimal.parse(this._stringRepresentation());\n        }\n        throw new Error(\"Current value is not a decimal.\");\n    }\n    bigIntValue() {\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.INT:\n                return this._parser.bigIntValue();\n        }\n        throw new Error(\"bigIntValue() was called when the current value was a(n) \" +\n            this._type.name);\n    }\n    intSize() {\n        if (isSafeInteger(this.bigIntValue())) {\n            return IntSize.Number;\n        }\n        return IntSize.BigInt;\n    }\n    numberValue() {\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.FLOAT:\n            case IonTypes.INT:\n                return this._parser.numberValue();\n        }\n        throw new Error(\"Current value is not a float or int.\");\n    }\n    stringValue() {\n        this.load_raw();\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.STRING:\n                if (this._parser.isNull()) {\n                    return null;\n                }\n                return this._raw;\n            case IonTypes.SYMBOL:\n                if (this._parser.isNull()) {\n                    return null;\n                }\n                if (this._raw_type === T_IDENTIFIER &&\n                    this._raw.length > 1 &&\n                    this._raw.charAt(0) === \"$\".charAt(0)) {\n                    const tempStr = this._raw.substr(1, this._raw.length);\n                    if (+tempStr === +tempStr) {\n                        const symbolId = Number(tempStr);\n                        const symbol = this._symtab.getSymbolText(symbolId);\n                        if (symbol === undefined) {\n                            throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n                        }\n                        return symbol;\n                    }\n                }\n                return this._raw;\n        }\n        throw new Error(\"Current value is not a string or symbol.\");\n    }\n    timestampValue() {\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.TIMESTAMP:\n                return Timestamp.parse(this._stringRepresentation());\n        }\n        throw new Error(\"Current value is not a timestamp.\");\n    }\n    value() {\n        if (this._type && this._type.isContainer) {\n            if (this.isNull()) {\n                return null;\n            }\n            throw new Error(\"Unable to provide a value for \" + this._type.name + \" containers.\");\n        }\n        switch (this._type) {\n            case IonTypes.NULL:\n                return null;\n            case IonTypes.BLOB:\n            case IonTypes.CLOB:\n                return this.uInt8ArrayValue();\n            case IonTypes.BOOL:\n                return this.booleanValue();\n            case IonTypes.DECIMAL:\n                return this.decimalValue();\n            case IonTypes.INT:\n                return this.bigIntValue();\n            case IonTypes.FLOAT:\n                return this.numberValue();\n            case IonTypes.STRING:\n            case IonTypes.SYMBOL:\n                return this.stringValue();\n            case IonTypes.TIMESTAMP:\n                return this.timestampValue();\n            default:\n                throw new Error(\"There is no current value.\");\n        }\n    }\n}\n//# sourceMappingURL=IonTextReader.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,WAAW;AAC/B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,uBAAuB,QAAS,uBAAuB;AAChE,SAASC,YAAY,EAAEC,aAAa,QAAQ,oBAAoB;AAChE,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,cAAc;AAChE,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,aAAa,QAAQ,QAAQ;AACtC,MAAMC,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAMC,GAAG,GAAG,CAAC,CAAC;AACd,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,QAAQ,GAAG,EAAE;AACnB,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACzB,IAAI,CAACD,MAAM,EAAE;MACT,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,IAAI,CAACC,OAAO,GAAG,IAAInB,aAAa,CAACgB,MAAM,CAAC;IACxC,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAGJ,OAAO,GAAGA,OAAO,GAAG,IAAIrB,OAAO,CAAC,CAAC;IAC7C,IAAI,CAAC0B,OAAO,GAAGxB,uBAAuB,CAAC,CAAC;IACxC,IAAI,CAACyB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGD,SAAS;EACzB;EACAE,QAAQA,CAAA,EAAG;IACP,MAAMC,CAAC,GAAG,IAAI;IACd,IAAIA,CAAC,CAACF,IAAI,KAAKD,SAAS,EAAE;MACtB;IACJ;IACA,IAAIG,CAAC,CAACJ,SAAS,KAAKb,OAAO,IAAIiB,CAAC,CAACJ,SAAS,KAAKZ,OAAO,EAAE;MACpDgB,CAAC,CAACF,IAAI,GAAGE,CAAC,CAACT,OAAO,CAACU,uBAAuB,CAACD,CAAC,CAACJ,SAAS,CAAC;IAC3D,CAAC,MACI;MACDI,CAAC,CAACF,IAAI,GAAGE,CAAC,CAACT,OAAO,CAACW,mBAAmB,CAACF,CAAC,CAACJ,SAAS,CAAC;IACvD;EACJ;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAIC,IAAI;IACR,MAAMC,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IACtB,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,OAAO,IAAI,CAACD,KAAK,CAAC,CAAC,GAAGD,CAAC,EAAE;MACrBD,IAAI,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC;MAClB,IAAIJ,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,CAACK,OAAO,CAAC,CAAC;MAClB,CAAC,MACI,IAAIL,IAAI,CAACM,WAAW,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QACzC,IAAI,CAACJ,MAAM,CAAC,CAAC;MACjB;IACJ;EACJ;EACAK,KAAKA,CAACC,KAAK,EAAEP,KAAK,EAAEQ,WAAW,EAAE;IAC7B,IAAIR,KAAK,GAAG,CAAC,EAAE;MACX,OAAO,KAAK;IAChB;IACA,MAAMS,GAAG,GAAG,UAAU;IACtB,MAAMC,MAAM,GAAG,OAAO;IACtB,IAAIH,KAAK,CAACI,MAAM,GAAGF,GAAG,CAACE,MAAM,IAAIH,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;MACrD,OAAO,KAAK;IAChB;IACA,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGF,MAAM,CAACC,MAAM,EAAE;MACtB,IAAID,MAAM,CAACG,MAAM,CAACD,CAAC,CAAC,KAAKL,KAAK,CAACM,MAAM,CAACD,CAAC,CAAC,EAAE;QACtC,OAAO,KAAK;MAChB;MACAA,CAAC,EAAE;IACP;IACA,OAAOA,CAAC,GAAGL,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACM,MAAM,CAACD,CAAC,CAAC,IAAI,GAAG,EAAE;MAC/C,MAAME,EAAE,GAAGP,KAAK,CAACM,MAAM,CAACD,CAAC,CAAC;MAC1B,IAAIE,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;QACtB,OAAO,KAAK;MAChB;MACAF,CAAC,EAAE;IACP;IACAA,CAAC,EAAE;IACH,OAAOA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAE;MACrB,MAAMG,EAAE,GAAGP,KAAK,CAACM,MAAM,CAACD,CAAC,CAAC;MAC1B,IAAIE,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;QACtB,OAAO,KAAK;MAChB;MACAF,CAAC,EAAE;IACP;IACA,IAAIL,KAAK,KAAKE,GAAG,EAAE;MACf,MAAM,IAAIzB,KAAK,CAAC,oCAAoC,CAAC;IACzD;IACA,OAAO,IAAI;EACf;EACA+B,SAASA,CAAA,EAAG;IACR,OAAO,KAAK;EAChB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/B,OAAO,CAACH,MAAM,CAAC,CAAC,CAACkC,QAAQ,CAAC,CAAC;EAC3C;EACAd,IAAIA,CAAA,EAAG;IACH,IAAI,CAACV,IAAI,GAAGD,SAAS;IACrB,IAAI,IAAI,CAACD,SAAS,KAAKhB,GAAG,EAAE;MACxB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACgB,SAAS,KAAKjB,sBAAsB,IACzC,CAAC,IAAI,CAACgC,MAAM,CAAC,CAAC,IACd,IAAI,CAAChB,KAAK,IACV,IAAI,CAACA,KAAK,CAACe,WAAW,EAAE;MACxB,IAAI,CAACP,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMoB,CAAC,GAAG,IAAI,CAAChC,OAAO;IACtB,SAAS;MACL,IAAI,CAACK,SAAS,GAAG2B,CAAC,CAACf,IAAI,CAAC,CAAC;MACzB,IAAI,IAAI,CAACZ,SAAS,KAAKf,YAAY,EAAE;QACjC,IAAI,IAAI,CAACW,MAAM,GAAG,CAAC,EAAE;UACjB;QACJ;QACA,IAAI,CAACO,QAAQ,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,CAACa,KAAK,CAAC,IAAI,CAACd,IAAI,EAAE,IAAI,CAACQ,KAAK,CAAC,CAAC,EAAE,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC,EAAE;UAC1D;QACJ;QACA,IAAI,CAACpB,OAAO,GAAGxB,uBAAuB,CAAC,CAAC;QACxC,IAAI,CAAC4B,IAAI,GAAGD,SAAS;QACrB,IAAI,CAACD,SAAS,GAAGC,SAAS;MAC9B,CAAC,MACI,IAAI,IAAI,CAACD,SAAS,KAAKd,SAAS,EAAE;QACnC,IAAI,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UACjB;QACJ;QACA,IAAI,CAACO,QAAQ,CAAC,CAAC;QACf,IAAI,IAAI,CAACD,IAAI,KAAK,UAAU,EAAE;UAC1B;QACJ;QACA,IAAI,CAACA,IAAI,GAAGD,SAAS;QACrB,IAAI,CAACD,SAAS,GAAGC,SAAS;MAC9B,CAAC,MACI,IAAI,IAAI,CAACD,SAAS,KAAKX,QAAQ,EAAE;QAClC,IAAIsC,CAAC,CAACT,WAAW,CAAC,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;UAC9B;QACJ;QACA,IAAIM,CAAC,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,CAAC,IAAInD,gBAAgB,EAAE;UAClD;QACJ;QACA,IAAI,CAACsB,KAAK,GAAGxB,YAAY,CAAC,IAAI,CAACyB,SAAS,CAAC;QACzC,IAAI,CAACF,OAAO,GAAGpB,eAAe,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC;QAC7D,IAAI,CAACI,IAAI,GAAGD,SAAS;QACrB,IAAI,CAACD,SAAS,GAAGC,SAAS;MAC9B,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAI,CAACF,KAAK,GAAGxB,YAAY,CAAC,IAAI,CAACyB,SAAS,CAAC;IACzC,OAAO,IAAI,CAACD,KAAK;EACrB;EACAY,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACZ,KAAK,CAACe,WAAW,EAAE;MACzB,MAAM,IAAIpB,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAI,IAAI,CAACqB,MAAM,CAAC,CAAC,EAAE;MACf,MAAM,IAAIrB,KAAK,CAAC,kCAAkC,CAAC;IACvD;IACA,IAAI,CAACC,OAAO,CAACkC,cAAc,CAAC,CAAC;IAC7B,IAAI,CAAC9B,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,SAAS,GAAGjB,sBAAsB;IACvC,IAAI,CAACa,MAAM,EAAE;EACjB;EACAiB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClB,OAAO,CAACkC,cAAc,CAAC,CAAC;IAC7B,OAAO,IAAI,CAAC7B,SAAS,IAAIhB,GAAG,EAAE;MAC1B,IAAI,CAAC4B,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACZ,SAAS,GAAGC,SAAS;IAC1B,IAAI,IAAI,CAACL,MAAM,IAAI,CAAC,EAAE;MAClB,MAAM,IAAIF,KAAK,CAAC,kDAAkD,CAAC;IACvE;IACA,IAAI,CAACE,MAAM,EAAE;EACjB;EACAY,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACT,KAAK;EACrB;EACAW,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACd,MAAM;EACtB;EACAkC,SAASA,CAAA,EAAG;IACR,MAAMC,GAAG,GAAG,IAAI,CAACpC,OAAO,CAACmC,SAAS,CAAC,CAAC;IACpC,IAAIC,GAAG,KAAK,IAAI,EAAE;MACd,MAAMC,QAAQ,GAAG,IAAI,CAACrC,OAAO,CAACsC,aAAa,CAAC,CAAC;MAC7C,IAAID,QAAQ,KAAK/C,YAAY,IAAI8C,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/D,MAAMG,OAAO,GAAGH,GAAG,CAACI,MAAM,CAAC,CAAC,EAAEJ,GAAG,CAACV,MAAM,CAAC;QACzC,IAAI,CAACa,OAAO,KAAK,CAACA,OAAO,EAAE;UACvB,MAAME,MAAM,GAAG,IAAI,CAACtC,OAAO,CAACuC,aAAa,CAACC,MAAM,CAACJ,OAAO,CAAC,CAAC;UAC1D,IAAIE,MAAM,KAAKnC,SAAS,EAAE;YACtB,MAAM,IAAIP,KAAK,CAAC,mDAAmD,CAAC;UACxE;UACA,OAAO0C,MAAM;QACjB;MACJ;IACJ;IACA,OAAOL,GAAG;EACd;EACAb,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvB,OAAO,CAACuB,WAAW,CAAC,CAAC,CAACqB,GAAG,CAAEC,EAAE,IAAK;MAC1C,MAAMC,IAAI,GAAGD,EAAE,CAACZ,OAAO,CAAC,CAAC;MACzB,IAAIa,IAAI,KAAK,IAAI,EAAE;QACf,OAAOA,IAAI;MACf,CAAC,MACI;QACD,MAAML,MAAM,GAAG,IAAI,CAACtC,OAAO,CAACuC,aAAa,CAACG,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;QACtD,IAAIN,MAAM,KAAKnC,SAAS,IAAImC,MAAM,KAAK,IAAI,EAAE;UACzC,MAAM,IAAI1C,KAAK,CAAC,mDAAmD,CAAC;QACxE;QACA,OAAO0C,MAAM;MACjB;IACJ,CAAC,CAAC;EACN;EACArB,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAChB,KAAK,KAAKlB,QAAQ,CAAC8D,IAAI,EAAE;MAC9B,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAChD,OAAO,CAACoB,MAAM,CAAC,CAAC;EAChC;EACA6B,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACzC,QAAQ,CAAC,CAAC;IACf,IAAI,IAAI,CAACY,MAAM,CAAC,CAAC,EAAE;MACf,OAAO,IAAI,CAAChB,KAAK,KAAKlB,QAAQ,CAAC8D,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC5C,KAAK,CAAC8C,IAAI;IAC5E;IACA,OAAO,IAAI,CAAC3C,IAAI;EACpB;EACA4C,YAAYA,CAAA,EAAG;IACX,QAAQ,IAAI,CAAC/C,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACkE,IAAI;QACd,OAAO,IAAI,CAACpD,OAAO,CAACmD,YAAY,CAAC,CAAC;IAC1C;IACA,MAAM,IAAIpD,KAAK,CAAC,iCAAiC,CAAC;EACtD;EACAsD,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7C,QAAQ,CAAC,CAAC;IACf,QAAQ,IAAI,CAACJ,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACoE,IAAI;QACd,IAAI,IAAI,CAAClC,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,OAAOpC,UAAU,CAAC,IAAI,CAACuB,IAAI,CAAC;MAChC,KAAKrB,QAAQ,CAACqE,IAAI;QACd,IAAI,IAAI,CAACnC,MAAM,CAAC,CAAC,EAAE;UACf,OAAO,IAAI;QACf;QACA,OAAO,IAAI,CAACb,IAAI;IACxB;IACA,MAAM,IAAIR,KAAK,CAAC,sCAAsC,CAAC;EAC3D;EACAyD,YAAYA,CAAA,EAAG;IACX,QAAQ,IAAI,CAACpD,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACuE,OAAO;QACjB,OAAO/E,OAAO,CAACgF,KAAK,CAAC,IAAI,CAACT,qBAAqB,CAAC,CAAC,CAAC;IAC1D;IACA,MAAM,IAAIlD,KAAK,CAAC,iCAAiC,CAAC;EACtD;EACA4D,WAAWA,CAAA,EAAG;IACV,QAAQ,IAAI,CAACvD,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAAC0E,GAAG;QACb,OAAO,IAAI,CAAC5D,OAAO,CAAC2D,WAAW,CAAC,CAAC;IACzC;IACA,MAAM,IAAI5D,KAAK,CAAC,2DAA2D,GACvE,IAAI,CAACK,KAAK,CAAC8C,IAAI,CAAC;EACxB;EACAW,OAAOA,CAAA,EAAG;IACN,IAAI1E,aAAa,CAAC,IAAI,CAACwE,WAAW,CAAC,CAAC,CAAC,EAAE;MACnC,OAAOnF,OAAO,CAACmE,MAAM;IACzB;IACA,OAAOnE,OAAO,CAACsF,MAAM;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,QAAQ,IAAI,CAAC3D,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAAC8E,KAAK;MACnB,KAAK9E,QAAQ,CAAC0E,GAAG;QACb,OAAO,IAAI,CAAC5D,OAAO,CAAC+D,WAAW,CAAC,CAAC;IACzC;IACA,MAAM,IAAIhE,KAAK,CAAC,sCAAsC,CAAC;EAC3D;EACAkE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzD,QAAQ,CAAC,CAAC;IACf,QAAQ,IAAI,CAACJ,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACgF,MAAM;QAChB,IAAI,IAAI,CAAClE,OAAO,CAACoB,MAAM,CAAC,CAAC,EAAE;UACvB,OAAO,IAAI;QACf;QACA,OAAO,IAAI,CAACb,IAAI;MACpB,KAAKrB,QAAQ,CAACiF,MAAM;QAChB,IAAI,IAAI,CAACnE,OAAO,CAACoB,MAAM,CAAC,CAAC,EAAE;UACvB,OAAO,IAAI;QACf;QACA,IAAI,IAAI,CAACf,SAAS,KAAKf,YAAY,IAC/B,IAAI,CAACiB,IAAI,CAACmB,MAAM,GAAG,CAAC,IACpB,IAAI,CAACnB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE;UACvC,MAAMW,OAAO,GAAG,IAAI,CAAChC,IAAI,CAACiC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACjC,IAAI,CAACmB,MAAM,CAAC;UACrD,IAAI,CAACa,OAAO,KAAK,CAACA,OAAO,EAAE;YACvB,MAAM6B,QAAQ,GAAGzB,MAAM,CAACJ,OAAO,CAAC;YAChC,MAAME,MAAM,GAAG,IAAI,CAACtC,OAAO,CAACuC,aAAa,CAAC0B,QAAQ,CAAC;YACnD,IAAI3B,MAAM,KAAKnC,SAAS,EAAE;cACtB,MAAM,IAAIP,KAAK,CAAC,mDAAmD,CAAC;YACxE;YACA,OAAO0C,MAAM;UACjB;QACJ;QACA,OAAO,IAAI,CAAClC,IAAI;IACxB;IACA,MAAM,IAAIR,KAAK,CAAC,0CAA0C,CAAC;EAC/D;EACAsE,cAAcA,CAAA,EAAG;IACb,QAAQ,IAAI,CAACjE,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACoF,SAAS;QACnB,OAAOrF,SAAS,CAACyE,KAAK,CAAC,IAAI,CAACT,qBAAqB,CAAC,CAAC,CAAC;IAC5D;IACA,MAAM,IAAIlD,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACAwE,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACnE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACe,WAAW,EAAE;MACtC,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QACf,OAAO,IAAI;MACf;MACA,MAAM,IAAIrB,KAAK,CAAC,gCAAgC,GAAG,IAAI,CAACK,KAAK,CAAC8C,IAAI,GAAG,cAAc,CAAC;IACxF;IACA,QAAQ,IAAI,CAAC9C,KAAK;MACd,KAAKlB,QAAQ,CAAC8D,IAAI;QACd,OAAO,IAAI;MACf,KAAK9D,QAAQ,CAACoE,IAAI;MAClB,KAAKpE,QAAQ,CAACqE,IAAI;QACd,OAAO,IAAI,CAACF,eAAe,CAAC,CAAC;MACjC,KAAKnE,QAAQ,CAACkE,IAAI;QACd,OAAO,IAAI,CAACD,YAAY,CAAC,CAAC;MAC9B,KAAKjE,QAAQ,CAACuE,OAAO;QACjB,OAAO,IAAI,CAACD,YAAY,CAAC,CAAC;MAC9B,KAAKtE,QAAQ,CAAC0E,GAAG;QACb,OAAO,IAAI,CAACD,WAAW,CAAC,CAAC;MAC7B,KAAKzE,QAAQ,CAAC8E,KAAK;QACf,OAAO,IAAI,CAACD,WAAW,CAAC,CAAC;MAC7B,KAAK7E,QAAQ,CAACgF,MAAM;MACpB,KAAKhF,QAAQ,CAACiF,MAAM;QAChB,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC;MAC7B,KAAK/E,QAAQ,CAACoF,SAAS;QACnB,OAAO,IAAI,CAACD,cAAc,CAAC,CAAC;MAChC;QACI,MAAM,IAAItE,KAAK,CAAC,4BAA4B,CAAC;IACrD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}