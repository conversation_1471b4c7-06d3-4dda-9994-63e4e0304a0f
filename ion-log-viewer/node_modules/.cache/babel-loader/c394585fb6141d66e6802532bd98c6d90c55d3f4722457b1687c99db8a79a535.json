{"ast": null, "code": "export class PrimitivePool {\n  constructor(getNewPrimitive) {\n    this._getNewPrimitive = getNewPrimitive;\n    this._primitives = [];\n  }\n  getPrimitive() {\n    const primitives = this._primitives;\n    if (primitives.length === 0) {\n      return this._getNewPrimitive();\n    } else {\n      return primitives.pop();\n    }\n  }\n  releasePrimitive(primitive) {\n    this._primitives.push(primitive);\n  }\n}", "map": {"version": 3, "names": ["PrimitivePool", "constructor", "getNewPrimitive", "_getNewPrimitive", "_primitives", "getPrimitive", "primitives", "length", "pop", "releasePrimitive", "primitive", "push"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/utils/PrimitivePool.js"], "sourcesContent": ["export class PrimitivePool {\n\n\tconstructor( getNewPrimitive ) {\n\n\t\tthis._getNewPrimitive = getNewPrimitive;\n\t\tthis._primitives = [];\n\n\t}\n\n\tgetPrimitive() {\n\n\t\tconst primitives = this._primitives;\n\t\tif ( primitives.length === 0 ) {\n\n\t\t\treturn this._getNewPrimitive();\n\n\t\t} else {\n\n\t\t\treturn primitives.pop();\n\n\t\t}\n\n\t}\n\n\treleasePrimitive( primitive ) {\n\n\t\tthis._primitives.push( primitive );\n\n\t}\n\n}\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,CAAC;EAE1BC,WAAWA,CAAEC,eAAe,EAAG;IAE9B,IAAI,CAACC,gBAAgB,GAAGD,eAAe;IACvC,IAAI,CAACE,WAAW,GAAG,EAAE;EAEtB;EAEAC,YAAYA,CAAA,EAAG;IAEd,MAAMC,UAAU,GAAG,IAAI,CAACF,WAAW;IACnC,IAAKE,UAAU,CAACC,MAAM,KAAK,CAAC,EAAG;MAE9B,OAAO,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IAE/B,CAAC,MAAM;MAEN,OAAOG,UAAU,CAACE,GAAG,CAAC,CAAC;IAExB;EAED;EAEAC,gBAAgBA,CAAEC,SAAS,EAAG;IAE7B,IAAI,CAACN,WAAW,CAACO,IAAI,CAAED,SAAU,CAAC;EAEnC;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}