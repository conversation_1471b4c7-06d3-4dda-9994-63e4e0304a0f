{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { BufferGeometry, Vector3, Uint32BufferAttribute, Float32BufferAttribute, DynamicDrawUsage, MathUtils } from \"three\";\nimport { SimplexNoise } from \"../math/SimplexNoise.js\";\nconst LightningStrike = /* @__PURE__ */(() => {\n  const _LightningStrike = class extends BufferGeometry {\n    constructor(rayParameters = {}) {\n      super();\n      this.isLightningStrike = true;\n      this.type = \"LightningStrike\";\n      this.init(_LightningStrike.copyParameters(rayParameters, rayParameters));\n      this.createMesh();\n    }\n    static createRandomGenerator() {\n      const numSeeds = 2053;\n      const seeds = [];\n      for (let i = 0; i < numSeeds; i++) {\n        seeds.push(Math.random());\n      }\n      const generator = {\n        currentSeed: 0,\n        random: function () {\n          const value = seeds[generator.currentSeed];\n          generator.currentSeed = (generator.currentSeed + 1) % numSeeds;\n          return value;\n        },\n        getSeed: function () {\n          return generator.currentSeed / numSeeds;\n        },\n        setSeed: function (seed) {\n          generator.currentSeed = Math.floor(seed * numSeeds) % numSeeds;\n        }\n      };\n      return generator;\n    }\n    static copyParameters(dest = {}, source = {}) {\n      const vecCopy = function (v) {\n        if (source === dest) {\n          return v;\n        } else {\n          return v.clone();\n        }\n      };\n      dest.sourceOffset = source.sourceOffset !== void 0 ? vecCopy(source.sourceOffset) : new Vector3(0, 100, 0), dest.destOffset = source.destOffset !== void 0 ? vecCopy(source.destOffset) : new Vector3(0, 0, 0), dest.timeScale = source.timeScale !== void 0 ? source.timeScale : 1, dest.roughness = source.roughness !== void 0 ? source.roughness : 0.9, dest.straightness = source.straightness !== void 0 ? source.straightness : 0.7, dest.up0 = source.up0 !== void 0 ? vecCopy(source.up0) : new Vector3(0, 0, 1);\n      dest.up1 = source.up1 !== void 0 ? vecCopy(source.up1) : new Vector3(0, 0, 1), dest.radius0 = source.radius0 !== void 0 ? source.radius0 : 1, dest.radius1 = source.radius1 !== void 0 ? source.radius1 : 1, dest.radius0Factor = source.radius0Factor !== void 0 ? source.radius0Factor : 0.5, dest.radius1Factor = source.radius1Factor !== void 0 ? source.radius1Factor : 0.2, dest.minRadius = source.minRadius !== void 0 ? source.minRadius : 0.2,\n      // These parameters should not be changed after lightning creation. They can be changed but the ray will change its form abruptly:\n      dest.isEternal = source.isEternal !== void 0 ? source.isEternal : source.birthTime === void 0 || source.deathTime === void 0, dest.birthTime = source.birthTime, dest.deathTime = source.deathTime, dest.propagationTimeFactor = source.propagationTimeFactor !== void 0 ? source.propagationTimeFactor : 0.1, dest.vanishingTimeFactor = source.vanishingTimeFactor !== void 0 ? source.vanishingTimeFactor : 0.9, dest.subrayPeriod = source.subrayPeriod !== void 0 ? source.subrayPeriod : 4, dest.subrayDutyCycle = source.subrayDutyCycle !== void 0 ? source.subrayDutyCycle : 0.6;\n      dest.maxIterations = source.maxIterations !== void 0 ? source.maxIterations : 9;\n      dest.isStatic = source.isStatic !== void 0 ? source.isStatic : false;\n      dest.ramification = source.ramification !== void 0 ? source.ramification : 5;\n      dest.maxSubrayRecursion = source.maxSubrayRecursion !== void 0 ? source.maxSubrayRecursion : 3;\n      dest.recursionProbability = source.recursionProbability !== void 0 ? source.recursionProbability : 0.6;\n      dest.generateUVs = source.generateUVs !== void 0 ? source.generateUVs : false;\n      dest.randomGenerator = source.randomGenerator, dest.noiseSeed = source.noiseSeed, dest.onDecideSubrayCreation = source.onDecideSubrayCreation, dest.onSubrayCreation = source.onSubrayCreation;\n      return dest;\n    }\n    update(time) {\n      if (this.isStatic) return;\n      if (this.rayParameters.isEternal || this.rayParameters.birthTime <= time && time <= this.rayParameters.deathTime) {\n        this.updateMesh(time);\n        if (time < this.subrays[0].endPropagationTime) {\n          this.state = _LightningStrike.RAY_PROPAGATING;\n        } else if (time > this.subrays[0].beginVanishingTime) {\n          this.state = _LightningStrike.RAY_VANISHING;\n        } else {\n          this.state = _LightningStrike.RAY_STEADY;\n        }\n        this.visible = true;\n      } else {\n        this.visible = false;\n        if (time < this.rayParameters.birthTime) {\n          this.state = _LightningStrike.RAY_UNBORN;\n        } else {\n          this.state = _LightningStrike.RAY_EXTINGUISHED;\n        }\n      }\n    }\n    init(rayParameters) {\n      this.rayParameters = rayParameters;\n      this.maxIterations = rayParameters.maxIterations !== void 0 ? Math.floor(rayParameters.maxIterations) : 9;\n      rayParameters.maxIterations = this.maxIterations;\n      this.isStatic = rayParameters.isStatic !== void 0 ? rayParameters.isStatic : false;\n      rayParameters.isStatic = this.isStatic;\n      this.ramification = rayParameters.ramification !== void 0 ? Math.floor(rayParameters.ramification) : 5;\n      rayParameters.ramification = this.ramification;\n      this.maxSubrayRecursion = rayParameters.maxSubrayRecursion !== void 0 ? Math.floor(rayParameters.maxSubrayRecursion) : 3;\n      rayParameters.maxSubrayRecursion = this.maxSubrayRecursion;\n      this.recursionProbability = rayParameters.recursionProbability !== void 0 ? rayParameters.recursionProbability : 0.6;\n      rayParameters.recursionProbability = this.recursionProbability;\n      this.generateUVs = rayParameters.generateUVs !== void 0 ? rayParameters.generateUVs : false;\n      rayParameters.generateUVs = this.generateUVs;\n      if (rayParameters.randomGenerator !== void 0) {\n        this.randomGenerator = rayParameters.randomGenerator;\n        this.seedGenerator = rayParameters.randomGenerator;\n        if (rayParameters.noiseSeed !== void 0) {\n          this.seedGenerator.setSeed(rayParameters.noiseSeed);\n        }\n      } else {\n        this.randomGenerator = _LightningStrike.createRandomGenerator();\n        this.seedGenerator = Math;\n      }\n      if (rayParameters.onDecideSubrayCreation !== void 0) {\n        this.onDecideSubrayCreation = rayParameters.onDecideSubrayCreation;\n      } else {\n        this.createDefaultSubrayCreationCallbacks();\n        if (rayParameters.onSubrayCreation !== void 0) {\n          this.onSubrayCreation = rayParameters.onSubrayCreation;\n        }\n      }\n      this.state = _LightningStrike.RAY_INITIALIZED;\n      this.maxSubrays = Math.ceil(1 + Math.pow(this.ramification, Math.max(0, this.maxSubrayRecursion - 1)));\n      rayParameters.maxSubrays = this.maxSubrays;\n      this.maxRaySegments = 2 * (1 << this.maxIterations);\n      this.subrays = [];\n      for (let i = 0; i < this.maxSubrays; i++) {\n        this.subrays.push(this.createSubray());\n      }\n      this.raySegments = [];\n      for (let i = 0; i < this.maxRaySegments; i++) {\n        this.raySegments.push(this.createSegment());\n      }\n      this.time = 0;\n      this.timeFraction = 0;\n      this.currentSegmentCallback = null;\n      this.currentCreateTriangleVertices = this.generateUVs ? this.createTriangleVerticesWithUVs : this.createTriangleVerticesWithoutUVs;\n      this.numSubrays = 0;\n      this.currentSubray = null;\n      this.currentSegmentIndex = 0;\n      this.isInitialSegment = false;\n      this.subrayProbability = 0;\n      this.currentVertex = 0;\n      this.currentIndex = 0;\n      this.currentCoordinate = 0;\n      this.currentUVCoordinate = 0;\n      this.vertices = null;\n      this.uvs = null;\n      this.indices = null;\n      this.positionAttribute = null;\n      this.uvsAttribute = null;\n      this.simplexX = new SimplexNoise(this.seedGenerator);\n      this.simplexY = new SimplexNoise(this.seedGenerator);\n      this.simplexZ = new SimplexNoise(this.seedGenerator);\n      this.forwards = new Vector3();\n      this.forwardsFill = new Vector3();\n      this.side = new Vector3();\n      this.down = new Vector3();\n      this.middlePos = new Vector3();\n      this.middleLinPos = new Vector3();\n      this.newPos = new Vector3();\n      this.vPos = new Vector3();\n      this.cross1 = new Vector3();\n    }\n    createMesh() {\n      const maxDrawableSegmentsPerSubRay = 1 << this.maxIterations;\n      const maxVerts = 3 * (maxDrawableSegmentsPerSubRay + 1) * this.maxSubrays;\n      const maxIndices = 18 * maxDrawableSegmentsPerSubRay * this.maxSubrays;\n      this.vertices = new Float32Array(maxVerts * 3);\n      this.indices = new Uint32Array(maxIndices);\n      if (this.generateUVs) {\n        this.uvs = new Float32Array(maxVerts * 2);\n      }\n      this.fillMesh(0);\n      this.setIndex(new Uint32BufferAttribute(this.indices, 1));\n      this.positionAttribute = new Float32BufferAttribute(this.vertices, 3);\n      this.setAttribute(\"position\", this.positionAttribute);\n      if (this.generateUVs) {\n        this.uvsAttribute = new Float32BufferAttribute(new Float32Array(this.uvs), 2);\n        this.setAttribute(\"uv\", this.uvsAttribute);\n      }\n      if (!this.isStatic) {\n        this.index.usage = DynamicDrawUsage;\n        this.positionAttribute.usage = DynamicDrawUsage;\n        if (this.generateUVs) {\n          this.uvsAttribute.usage = DynamicDrawUsage;\n        }\n      }\n      this.vertices = this.positionAttribute.array;\n      this.indices = this.index.array;\n      if (this.generateUVs) {\n        this.uvs = this.uvsAttribute.array;\n      }\n    }\n    updateMesh(time) {\n      this.fillMesh(time);\n      this.drawRange.count = this.currentIndex;\n      this.index.needsUpdate = true;\n      this.positionAttribute.needsUpdate = true;\n      if (this.generateUVs) {\n        this.uvsAttribute.needsUpdate = true;\n      }\n    }\n    fillMesh(time) {\n      const scope = this;\n      this.currentVertex = 0;\n      this.currentIndex = 0;\n      this.currentCoordinate = 0;\n      this.currentUVCoordinate = 0;\n      this.fractalRay(time, function fillVertices(segment) {\n        const subray = scope.currentSubray;\n        if (time < subray.birthTime) {\n          return;\n        } else if (this.rayParameters.isEternal && scope.currentSubray.recursion == 0) {\n          scope.createPrism(segment);\n          scope.onDecideSubrayCreation(segment, scope);\n        } else if (time < subray.endPropagationTime) {\n          if (scope.timeFraction >= segment.fraction0 * subray.propagationTimeFactor) {\n            scope.createPrism(segment);\n            scope.onDecideSubrayCreation(segment, scope);\n          }\n        } else if (time < subray.beginVanishingTime) {\n          scope.createPrism(segment);\n          scope.onDecideSubrayCreation(segment, scope);\n        } else {\n          if (scope.timeFraction <= subray.vanishingTimeFactor + segment.fraction1 * (1 - subray.vanishingTimeFactor)) {\n            scope.createPrism(segment);\n          }\n          scope.onDecideSubrayCreation(segment, scope);\n        }\n      });\n    }\n    addNewSubray() {\n      return this.subrays[this.numSubrays++];\n    }\n    initSubray(subray, rayParameters) {\n      subray.pos0.copy(rayParameters.sourceOffset);\n      subray.pos1.copy(rayParameters.destOffset);\n      subray.up0.copy(rayParameters.up0);\n      subray.up1.copy(rayParameters.up1);\n      subray.radius0 = rayParameters.radius0;\n      subray.radius1 = rayParameters.radius1;\n      subray.birthTime = rayParameters.birthTime;\n      subray.deathTime = rayParameters.deathTime;\n      subray.timeScale = rayParameters.timeScale;\n      subray.roughness = rayParameters.roughness;\n      subray.straightness = rayParameters.straightness;\n      subray.propagationTimeFactor = rayParameters.propagationTimeFactor;\n      subray.vanishingTimeFactor = rayParameters.vanishingTimeFactor;\n      subray.maxIterations = this.maxIterations;\n      subray.seed = rayParameters.noiseSeed !== void 0 ? rayParameters.noiseSeed : 0;\n      subray.recursion = 0;\n    }\n    fractalRay(time, segmentCallback) {\n      this.time = time;\n      this.currentSegmentCallback = segmentCallback;\n      this.numSubrays = 0;\n      this.initSubray(this.addNewSubray(), this.rayParameters);\n      for (let subrayIndex = 0; subrayIndex < this.numSubrays; subrayIndex++) {\n        const subray = this.subrays[subrayIndex];\n        this.currentSubray = subray;\n        this.randomGenerator.setSeed(subray.seed);\n        subray.endPropagationTime = MathUtils.lerp(subray.birthTime, subray.deathTime, subray.propagationTimeFactor);\n        subray.beginVanishingTime = MathUtils.lerp(subray.deathTime, subray.birthTime, 1 - subray.vanishingTimeFactor);\n        const random1 = this.randomGenerator.random;\n        subray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1e3);\n        subray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1e3);\n        this.timeFraction = (time - subray.birthTime) / (subray.deathTime - subray.birthTime);\n        this.currentSegmentIndex = 0;\n        this.isInitialSegment = true;\n        const segment = this.getNewSegment();\n        segment.iteration = 0;\n        segment.pos0.copy(subray.pos0);\n        segment.pos1.copy(subray.pos1);\n        segment.linPos0.copy(subray.linPos0);\n        segment.linPos1.copy(subray.linPos1);\n        segment.up0.copy(subray.up0);\n        segment.up1.copy(subray.up1);\n        segment.radius0 = subray.radius0;\n        segment.radius1 = subray.radius1;\n        segment.fraction0 = 0;\n        segment.fraction1 = 1;\n        segment.positionVariationFactor = 1 - subray.straightness;\n        this.subrayProbability = this.ramification * Math.pow(this.recursionProbability, subray.recursion) / (1 << subray.maxIterations);\n        this.fractalRayRecursive(segment);\n      }\n      this.currentSegmentCallback = null;\n      this.currentSubray = null;\n    }\n    fractalRayRecursive(segment) {\n      if (segment.iteration >= this.currentSubray.maxIterations) {\n        this.currentSegmentCallback(segment);\n        return;\n      }\n      this.forwards.subVectors(segment.pos1, segment.pos0);\n      let lForwards = this.forwards.length();\n      if (lForwards < 1e-6) {\n        this.forwards.set(0, 0, 0.01);\n        lForwards = this.forwards.length();\n      }\n      const middleRadius = (segment.radius0 + segment.radius1) * 0.5;\n      const middleFraction = (segment.fraction0 + segment.fraction1) * 0.5;\n      const timeDimension = this.time * this.currentSubray.timeScale * Math.pow(2, segment.iteration);\n      this.middlePos.lerpVectors(segment.pos0, segment.pos1, 0.5);\n      this.middleLinPos.lerpVectors(segment.linPos0, segment.linPos1, 0.5);\n      const p = this.middleLinPos;\n      this.newPos.set(this.simplexX.noise4d(p.x, p.y, p.z, timeDimension), this.simplexY.noise4d(p.x, p.y, p.z, timeDimension), this.simplexZ.noise4d(p.x, p.y, p.z, timeDimension));\n      this.newPos.multiplyScalar(segment.positionVariationFactor * lForwards);\n      this.newPos.add(this.middlePos);\n      const newSegment1 = this.getNewSegment();\n      newSegment1.pos0.copy(segment.pos0);\n      newSegment1.pos1.copy(this.newPos);\n      newSegment1.linPos0.copy(segment.linPos0);\n      newSegment1.linPos1.copy(this.middleLinPos);\n      newSegment1.up0.copy(segment.up0);\n      newSegment1.up1.copy(segment.up1);\n      newSegment1.radius0 = segment.radius0;\n      newSegment1.radius1 = middleRadius;\n      newSegment1.fraction0 = segment.fraction0;\n      newSegment1.fraction1 = middleFraction;\n      newSegment1.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness;\n      newSegment1.iteration = segment.iteration + 1;\n      const newSegment2 = this.getNewSegment();\n      newSegment2.pos0.copy(this.newPos);\n      newSegment2.pos1.copy(segment.pos1);\n      newSegment2.linPos0.copy(this.middleLinPos);\n      newSegment2.linPos1.copy(segment.linPos1);\n      this.cross1.crossVectors(segment.up0, this.forwards.normalize());\n      newSegment2.up0.crossVectors(this.forwards, this.cross1).normalize();\n      newSegment2.up1.copy(segment.up1);\n      newSegment2.radius0 = middleRadius;\n      newSegment2.radius1 = segment.radius1;\n      newSegment2.fraction0 = middleFraction;\n      newSegment2.fraction1 = segment.fraction1;\n      newSegment2.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness;\n      newSegment2.iteration = segment.iteration + 1;\n      this.fractalRayRecursive(newSegment1);\n      this.fractalRayRecursive(newSegment2);\n    }\n    createPrism(segment) {\n      this.forwardsFill.subVectors(segment.pos1, segment.pos0).normalize();\n      if (this.isInitialSegment) {\n        this.currentCreateTriangleVertices(segment.pos0, segment.up0, this.forwardsFill, segment.radius0, 0);\n        this.isInitialSegment = false;\n      }\n      this.currentCreateTriangleVertices(segment.pos1, segment.up0, this.forwardsFill, segment.radius1, segment.fraction1);\n      this.createPrismFaces();\n    }\n    createTriangleVerticesWithoutUVs(pos, up, forwards, radius) {\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * _LightningStrike.COS30DEG);\n      this.down.copy(up).multiplyScalar(-radius * _LightningStrike.SIN30DEG);\n      const p = this.vPos;\n      const v = this.vertices;\n      p.copy(pos).sub(this.side).add(this.down);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      p.copy(pos).add(this.side).add(this.down);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      p.copy(up).multiplyScalar(radius).add(pos);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      this.currentVertex += 3;\n    }\n    createTriangleVerticesWithUVs(pos, up, forwards, radius, u) {\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * _LightningStrike.COS30DEG);\n      this.down.copy(up).multiplyScalar(-radius * _LightningStrike.SIN30DEG);\n      const p = this.vPos;\n      const v = this.vertices;\n      const uv = this.uvs;\n      p.copy(pos).sub(this.side).add(this.down);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      uv[this.currentUVCoordinate++] = u;\n      uv[this.currentUVCoordinate++] = 0;\n      p.copy(pos).add(this.side).add(this.down);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      uv[this.currentUVCoordinate++] = u;\n      uv[this.currentUVCoordinate++] = 0.5;\n      p.copy(up).multiplyScalar(radius).add(pos);\n      v[this.currentCoordinate++] = p.x;\n      v[this.currentCoordinate++] = p.y;\n      v[this.currentCoordinate++] = p.z;\n      uv[this.currentUVCoordinate++] = u;\n      uv[this.currentUVCoordinate++] = 1;\n      this.currentVertex += 3;\n    }\n    createPrismFaces(vertex) {\n      const indices = this.indices;\n      vertex = this.currentVertex - 6;\n      indices[this.currentIndex++] = vertex + 1;\n      indices[this.currentIndex++] = vertex + 2;\n      indices[this.currentIndex++] = vertex + 5;\n      indices[this.currentIndex++] = vertex + 1;\n      indices[this.currentIndex++] = vertex + 5;\n      indices[this.currentIndex++] = vertex + 4;\n      indices[this.currentIndex++] = vertex + 0;\n      indices[this.currentIndex++] = vertex + 1;\n      indices[this.currentIndex++] = vertex + 4;\n      indices[this.currentIndex++] = vertex + 0;\n      indices[this.currentIndex++] = vertex + 4;\n      indices[this.currentIndex++] = vertex + 3;\n      indices[this.currentIndex++] = vertex + 2;\n      indices[this.currentIndex++] = vertex + 0;\n      indices[this.currentIndex++] = vertex + 3;\n      indices[this.currentIndex++] = vertex + 2;\n      indices[this.currentIndex++] = vertex + 3;\n      indices[this.currentIndex++] = vertex + 5;\n    }\n    createDefaultSubrayCreationCallbacks() {\n      const random1 = this.randomGenerator.random;\n      this.onDecideSubrayCreation = function (segment, lightningStrike) {\n        const subray = lightningStrike.currentSubray;\n        const period = lightningStrike.rayParameters.subrayPeriod;\n        const dutyCycle = lightningStrike.rayParameters.subrayDutyCycle;\n        const phase0 = lightningStrike.rayParameters.isEternal && subray.recursion == 0 ? -random1() * period : MathUtils.lerp(subray.birthTime, subray.endPropagationTime, segment.fraction0) - random1() * period;\n        const phase = lightningStrike.time - phase0;\n        const currentCycle = Math.floor(phase / period);\n        const childSubraySeed = random1() * (currentCycle + 1);\n        const isActive = phase % period <= dutyCycle * period;\n        let probability = 0;\n        if (isActive) {\n          probability = lightningStrike.subrayProbability;\n        }\n        if (subray.recursion < lightningStrike.maxSubrayRecursion && lightningStrike.numSubrays < lightningStrike.maxSubrays && random1() < probability) {\n          const childSubray = lightningStrike.addNewSubray();\n          const parentSeed = lightningStrike.randomGenerator.getSeed();\n          childSubray.seed = childSubraySeed;\n          lightningStrike.randomGenerator.setSeed(childSubraySeed);\n          childSubray.recursion = subray.recursion + 1;\n          childSubray.maxIterations = Math.max(1, subray.maxIterations - 1);\n          childSubray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1e3);\n          childSubray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1e3);\n          childSubray.up0.copy(subray.up0);\n          childSubray.up1.copy(subray.up1);\n          childSubray.radius0 = segment.radius0 * lightningStrike.rayParameters.radius0Factor;\n          childSubray.radius1 = Math.min(lightningStrike.rayParameters.minRadius, segment.radius1 * lightningStrike.rayParameters.radius1Factor);\n          childSubray.birthTime = phase0 + currentCycle * period;\n          childSubray.deathTime = childSubray.birthTime + period * dutyCycle;\n          if (!lightningStrike.rayParameters.isEternal && subray.recursion == 0) {\n            childSubray.birthTime = Math.max(childSubray.birthTime, subray.birthTime);\n            childSubray.deathTime = Math.min(childSubray.deathTime, subray.deathTime);\n          }\n          childSubray.timeScale = subray.timeScale * 2;\n          childSubray.roughness = subray.roughness;\n          childSubray.straightness = subray.straightness;\n          childSubray.propagationTimeFactor = subray.propagationTimeFactor;\n          childSubray.vanishingTimeFactor = subray.vanishingTimeFactor;\n          lightningStrike.onSubrayCreation(segment, subray, childSubray, lightningStrike);\n          lightningStrike.randomGenerator.setSeed(parentSeed);\n        }\n      };\n      const vec1Pos = new Vector3();\n      const vec2Forward = new Vector3();\n      const vec3Side = new Vector3();\n      const vec4Up = new Vector3();\n      this.onSubrayCreation = function (segment, parentSubray, childSubray, lightningStrike) {\n        lightningStrike.subrayCylinderPosition(segment, parentSubray, childSubray, 0.5, 0.6, 0.2);\n      };\n      this.subrayConePosition = function (segment, parentSubray, childSubray, heightFactor, sideWidthFactor, minSideWidthFactor) {\n        childSubray.pos0.copy(segment.pos0);\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0);\n        vec2Forward.copy(vec1Pos).normalize();\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * (random1() * heightFactor));\n        const length = vec1Pos.length();\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward);\n        const angle = 2 * Math.PI * random1();\n        vec3Side.multiplyScalar(Math.cos(angle));\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle));\n        childSubray.pos1.copy(vec3Side).add(vec4Up).multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor))).add(vec1Pos).add(parentSubray.pos0);\n      };\n      this.subrayCylinderPosition = function (segment, parentSubray, childSubray, heightFactor, sideWidthFactor, minSideWidthFactor) {\n        childSubray.pos0.copy(segment.pos0);\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0);\n        vec2Forward.copy(vec1Pos).normalize();\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * ((2 * random1() - 1) * heightFactor));\n        const length = vec1Pos.length();\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward);\n        const angle = 2 * Math.PI * random1();\n        vec3Side.multiplyScalar(Math.cos(angle));\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle));\n        childSubray.pos1.copy(vec3Side).add(vec4Up).multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor))).add(vec1Pos).add(parentSubray.pos0);\n      };\n    }\n    createSubray() {\n      return {\n        seed: 0,\n        maxIterations: 0,\n        recursion: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        birthTime: 0,\n        deathTime: 0,\n        timeScale: 0,\n        roughness: 0,\n        straightness: 0,\n        propagationTimeFactor: 0,\n        vanishingTimeFactor: 0,\n        endPropagationTime: 0,\n        beginVanishingTime: 0\n      };\n    }\n    createSegment() {\n      return {\n        iteration: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        fraction0: 0,\n        fraction1: 0,\n        positionVariationFactor: 0\n      };\n    }\n    getNewSegment() {\n      return this.raySegments[this.currentSegmentIndex++];\n    }\n    copy(source) {\n      super.copy(source);\n      this.init(_LightningStrike.copyParameters({}, source.rayParameters));\n      return this;\n    }\n    clone() {\n      return new this.constructor(_LightningStrike.copyParameters({}, this.rayParameters));\n    }\n  };\n  let LightningStrike2 = _LightningStrike;\n  // Ray states\n  __publicField(LightningStrike2, \"RAY_INITIALIZED\", 0);\n  __publicField(LightningStrike2, \"RAY_UNBORN\", 1);\n  __publicField(LightningStrike2, \"RAY_PROPAGATING\", 2);\n  __publicField(LightningStrike2, \"RAY_STEADY\", 3);\n  __publicField(LightningStrike2, \"RAY_VANISHING\", 4);\n  __publicField(LightningStrike2, \"RAY_EXTINGUISHED\", 5);\n  __publicField(LightningStrike2, \"COS30DEG\", Math.cos(30 * Math.PI / 180));\n  __publicField(LightningStrike2, \"SIN30DEG\", Math.sin(30 * Math.PI / 180));\n  return LightningStrike2;\n})();\nexport { LightningStrike };", "map": {"version": 3, "names": ["LightningStrike", "_LightningStrike", "BufferGeometry", "constructor", "rayParameters", "isLightningStrike", "type", "init", "copyParameters", "<PERSON><PERSON><PERSON>", "createRandomGenerator", "numSeeds", "seeds", "i", "push", "Math", "random", "generator", "currentSeed", "value", "getSeed", "setSeed", "seed", "floor", "dest", "source", "vecCopy", "v", "clone", "sourceOffset", "Vector3", "destOffset", "timeScale", "roughness", "straightness", "up0", "up1", "radius0", "radius1", "radius0Factor", "radius1Factor", "minRadius", "isEternal", "birthTime", "deathTime", "propagationTimeFactor", "vanishingTimeFactor", "subrayPeriod", "subrayDutyCycle", "maxIterations", "isStatic", "ramification", "maxSubrayRecursion", "recursionProbability", "generateUVs", "randomGenerator", "noiseSeed", "onDecideSubrayCreation", "onSubrayCreation", "update", "time", "updateMesh", "subrays", "endPropagationTime", "state", "RAY_PROPAGATING", "beginVanishingTime", "RAY_VANISHING", "RAY_STEADY", "visible", "RAY_UNBORN", "RAY_EXTINGUISHED", "seedGenerator", "createDefaultSubrayCreationCallbacks", "RAY_INITIALIZED", "max<PERSON><PERSON><PERSON>s", "ceil", "pow", "max", "maxRaySegments", "createSubray", "raySegments", "createSegment", "timeFraction", "currentSegmentCallback", "currentCreateTriangleVertices", "createTriangleVerticesWithUVs", "createTriangleVerticesWithoutUVs", "numSubrays", "currentS<PERSON>ray", "currentSegmentIndex", "isInitialSegment", "subrayProbability", "currentVertex", "currentIndex", "currentCoordinate", "currentUVCoordinate", "vertices", "uvs", "indices", "positionAttribute", "uvsAttribute", "simplexX", "SimplexNoise", "simplexY", "simplexZ", "forwards", "forwardsFill", "side", "down", "middlePos", "middleLinPos", "newPos", "vPos", "cross1", "maxDrawableSegmentsPerSubRay", "max<PERSON><PERSON><PERSON>", "maxIndices", "Float32Array", "Uint32Array", "<PERSON><PERSON><PERSON>", "setIndex", "Uint32BufferAttribute", "Float32BufferAttribute", "setAttribute", "index", "usage", "DynamicDrawUsage", "array", "drawRange", "count", "needsUpdate", "scope", "fractalRay", "fillVertices", "segment", "subray", "recursion", "createPrism", "fraction0", "fraction1", "addNewSubray", "initSubray", "pos0", "copy", "pos1", "segmentCallback", "subrayIndex", "MathUtils", "lerp", "random1", "linPos0", "set", "multiplyScalar", "linPos1", "getNewSegment", "iteration", "positionVariationFactor", "fractalRayRecursive", "subVectors", "lForwards", "length", "middleRadius", "middleFraction", "timeDimension", "lerpVectors", "p", "noise4d", "x", "y", "z", "add", "newSegment1", "newSegment2", "crossVectors", "normalize", "createPrismFaces", "pos", "up", "radius", "COS30DEG", "SIN30DEG", "sub", "u", "uv", "vertex", "lightningStrike", "period", "dutyCycle", "phase0", "phase", "currentCycle", "childSubraySeed", "isActive", "probability", "<PERSON><PERSON><PERSON><PERSON>", "parentSeed", "min", "vec1Pos", "vec2Forward", "vec3Side", "vec4Up", "parentSubray", "subrayCylinderPosition", "subrayConePosition", "heightFactor", "sideWidthFactor", "minSideWidthFactor", "angle", "PI", "cos", "sin", "LightningStrike2", "__publicField"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/geometries/LightningStrike.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  DynamicDrawUsage,\n  Float32BufferAttribute,\n  MathUtils,\n  Uint32BufferAttribute,\n  Vector3,\n} from 'three'\nimport { SimplexNoise } from '../math/SimplexNoise'\n\n/**\n * @fileoverview LightningStrike object for creating lightning strikes and voltaic arcs.\n *\n *\n * Usage\n *\n * var myRay = new LightningStrike( paramsObject );\n * var myRayMesh = new THREE.Mesh( myRay, myMaterial );\n * scene.add( myRayMesh );\n * ...\n * myRay.update( currentTime );\n *\n * The \"currentTime\" can vary its rate, go forwards, backwards or even jump, but it cannot be negative.\n *\n * You should normally leave the ray position to (0, 0, 0). You should control it by changing the sourceOffset and destOffset parameters.\n *\n *\n * LightningStrike parameters\n *\n * The paramsObject can contain any of the following parameters.\n *\n * Legend:\n * 'LightningStrike' (also called 'ray'): An independent voltaic arc with its ramifications and defined with a set of parameters.\n * 'Subray': A ramification of the ray. It is not a LightningStrike object.\n * 'Segment': A linear segment piece of a subray.\n * 'Leaf segment': A ray segment which cannot be smaller.\n *\n *\n * The following parameters can be changed any time and if they vary smoothly, the ray form will also change smoothly:\n *\n * @param {Vector3} sourceOffset The point where the ray starts.\n *\n * @param {Vector3} destOffset The point where the ray ends.\n *\n * @param {double} timeScale The rate at wich the ray form changes in time. Default: 1\n *\n * @param {double} roughness From 0 to 1. The higher the value, the more wrinkled is the ray. Default: 0.9\n *\n * @param {double} straightness From 0 to 1. The higher the value, the more straight will be a subray path. Default: 0.7\n *\n * @param {Vector3} up0 Ray 'up' direction at the ray starting point. Must be normalized. It should be perpendicular to the ray forward direction but it doesn't matter much.\n *\n * @param {Vector3} up1 Like the up0 parameter but at the end of the ray. Must be normalized.\n *\n * @param {double} radius0 Radius of the main ray trunk at the start point. Default: 1\n *\n * @param {double} radius1 Radius of the main ray trunk at the end point. Default: 1\n *\n * @param {double} radius0Factor The radius0 of a subray is this factor times the radius0 of its parent subray. Default: 0.5\n *\n * @param {double} radius1Factor The radius1 of a subray is this factor times the radius1 of its parent subray. Default: 0.2\n *\n * @param {minRadius} Minimum value a subray radius0 or radius1 can get. Default: 0.1\n *\n *\n * The following parameters should not be changed after lightning creation. They can be changed but the ray will change its form abruptly:\n *\n * @param {boolean} isEternal If true the ray never extinguishes. Otherwise its life is controlled by the 'birthTime' and 'deathTime' parameters. Default: true if any of those two parameters is undefined.\n *\n * @param {double} birthTime The time at which the ray starts its life and begins propagating. Only if isEternal is false. Default: None.\n *\n * @param {double} deathTime The time at which the ray ends vanishing and its life. Only if isEternal is false. Default: None.\n *\n * @param {double} propagationTimeFactor From 0 to 1. Lifetime factor at which the ray ends propagating and enters the steady phase. For example, 0.1 means it is propagating 1/10 of its lifetime. Default: 0.1\n *\n * @param {double} vanishingTimeFactor From 0 to 1. Lifetime factor at which the ray ends the steady phase and begins vanishing. For example, 0.9 means it is vanishing 1/10 of its lifetime. Default: 0.9\n *\n * @param {double} subrayPeriod Subrays cycle periodically. This is their time period. Default: 4\n *\n * @param {double} subrayDutyCycle From 0 to 1. This is the fraction of time a subray is active. Default: 0.6\n *\n *\n * These parameters cannot change after lightning creation:\n *\n * @param {integer} maxIterations: Greater than 0. The number of ray's leaf segments is 2**maxIterations. Default: 9\n *\n * @param {boolean} isStatic Set to true only for rays which won't change over time and are not attached to moving objects (Rare case). It is used to set the vertex buffers non-dynamic. You can omit calling update() for these rays.\n *\n * @param {integer} ramification Greater than 0. Maximum number of child subrays a subray can have. Default: 5\n *\n * @param {integer} maxSubrayRecursion Greater than 0. Maximum level of recursion (subray descendant generations). Default: 3\n *\n * @param {double} recursionProbability From 0 to 1. The lower the value, the less chance each new generation of subrays has to generate new subrays. Default: 0.6\n *\n * @param {boolean} generateUVs If true, the ray geometry will have uv coordinates generated. u runs along the ray, and v across its perimeter. Default: false.\n *\n * @param {Object} randomGenerator Set here your random number generator which will seed the SimplexNoise and other decisions during ray tree creation.\n * It can be used to generate repeatable rays. For that, set also the noiseSeed parameter, and each ray created with that generator and seed pair will be identical in time.\n * The randomGenerator parameter should be an object with a random() function similar to Math.random, but seedable.\n * It must have also a getSeed() method, which returns the current seed, and a setSeed( seed ) method, which accepts as seed a fractional number from 0 to 1, as well as any other number.\n * The default value is an internal generator for some uses and Math.random for others (It is non-repeatable even if noiseSeed is supplied)\n *\n * @param {double} noiseSeed Seed used to make repeatable rays (see the randomGenerator)\n *\n * @param {function} onDecideSubrayCreation Set this to change the callback which decides subray creation. You can look at the default callback in the code (createDefaultSubrayCreationCallbacks)for more info.\n *\n * @param {function} onSubrayCreation This is another callback, more simple than the previous one. It can be used to adapt the form of subrays or other parameters once a subray has been created and initialized. It is used in the examples to adapt subrays to a sphere or to a plane.\n *\n *\n */\n\nconst LightningStrike = /* @__PURE__ */ (() => {\n  class LightningStrike extends BufferGeometry {\n    // Ray states\n    static RAY_INITIALIZED = 0\n    static RAY_UNBORN = 1\n    static RAY_PROPAGATING = 2\n    static RAY_STEADY = 3\n    static RAY_VANISHING = 4\n    static RAY_EXTINGUISHED = 5\n\n    static COS30DEG = Math.cos((30 * Math.PI) / 180)\n    static SIN30DEG = Math.sin((30 * Math.PI) / 180)\n\n    constructor(rayParameters = {}) {\n      super()\n\n      this.isLightningStrike = true\n\n      this.type = 'LightningStrike'\n\n      // Set parameters, and set undefined parameters to default values\n      this.init(LightningStrike.copyParameters(rayParameters, rayParameters))\n\n      // Creates and populates the mesh\n      this.createMesh()\n    }\n\n    static createRandomGenerator() {\n      const numSeeds = 2053\n      const seeds = []\n\n      for (let i = 0; i < numSeeds; i++) {\n        seeds.push(Math.random())\n      }\n\n      const generator = {\n        currentSeed: 0,\n\n        random: function () {\n          const value = seeds[generator.currentSeed]\n\n          generator.currentSeed = (generator.currentSeed + 1) % numSeeds\n\n          return value\n        },\n\n        getSeed: function () {\n          return generator.currentSeed / numSeeds\n        },\n\n        setSeed: function (seed) {\n          generator.currentSeed = Math.floor(seed * numSeeds) % numSeeds\n        },\n      }\n\n      return generator\n    }\n\n    static copyParameters(dest = {}, source = {}) {\n      const vecCopy = function (v) {\n        if (source === dest) {\n          return v\n        } else {\n          return v.clone()\n        }\n      }\n\n      ;(dest.sourceOffset = source.sourceOffset !== undefined ? vecCopy(source.sourceOffset) : new Vector3(0, 100, 0)),\n        (dest.destOffset = source.destOffset !== undefined ? vecCopy(source.destOffset) : new Vector3(0, 0, 0)),\n        (dest.timeScale = source.timeScale !== undefined ? source.timeScale : 1),\n        (dest.roughness = source.roughness !== undefined ? source.roughness : 0.9),\n        (dest.straightness = source.straightness !== undefined ? source.straightness : 0.7),\n        (dest.up0 = source.up0 !== undefined ? vecCopy(source.up0) : new Vector3(0, 0, 1))\n      ;(dest.up1 = source.up1 !== undefined ? vecCopy(source.up1) : new Vector3(0, 0, 1)),\n        (dest.radius0 = source.radius0 !== undefined ? source.radius0 : 1),\n        (dest.radius1 = source.radius1 !== undefined ? source.radius1 : 1),\n        (dest.radius0Factor = source.radius0Factor !== undefined ? source.radius0Factor : 0.5),\n        (dest.radius1Factor = source.radius1Factor !== undefined ? source.radius1Factor : 0.2),\n        (dest.minRadius = source.minRadius !== undefined ? source.minRadius : 0.2),\n        // These parameters should not be changed after lightning creation. They can be changed but the ray will change its form abruptly:\n\n        (dest.isEternal =\n          source.isEternal !== undefined\n            ? source.isEternal\n            : source.birthTime === undefined || source.deathTime === undefined),\n        (dest.birthTime = source.birthTime),\n        (dest.deathTime = source.deathTime),\n        (dest.propagationTimeFactor = source.propagationTimeFactor !== undefined ? source.propagationTimeFactor : 0.1),\n        (dest.vanishingTimeFactor = source.vanishingTimeFactor !== undefined ? source.vanishingTimeFactor : 0.9),\n        (dest.subrayPeriod = source.subrayPeriod !== undefined ? source.subrayPeriod : 4),\n        (dest.subrayDutyCycle = source.subrayDutyCycle !== undefined ? source.subrayDutyCycle : 0.6)\n\n      // These parameters cannot change after lightning creation:\n\n      dest.maxIterations = source.maxIterations !== undefined ? source.maxIterations : 9\n      dest.isStatic = source.isStatic !== undefined ? source.isStatic : false\n      dest.ramification = source.ramification !== undefined ? source.ramification : 5\n      dest.maxSubrayRecursion = source.maxSubrayRecursion !== undefined ? source.maxSubrayRecursion : 3\n      dest.recursionProbability = source.recursionProbability !== undefined ? source.recursionProbability : 0.6\n      dest.generateUVs = source.generateUVs !== undefined ? source.generateUVs : false\n      ;(dest.randomGenerator = source.randomGenerator),\n        (dest.noiseSeed = source.noiseSeed),\n        (dest.onDecideSubrayCreation = source.onDecideSubrayCreation),\n        (dest.onSubrayCreation = source.onSubrayCreation)\n\n      return dest\n    }\n\n    update(time) {\n      if (this.isStatic) return\n\n      if (\n        this.rayParameters.isEternal ||\n        (this.rayParameters.birthTime <= time && time <= this.rayParameters.deathTime)\n      ) {\n        this.updateMesh(time)\n\n        if (time < this.subrays[0].endPropagationTime) {\n          this.state = LightningStrike.RAY_PROPAGATING\n        } else if (time > this.subrays[0].beginVanishingTime) {\n          this.state = LightningStrike.RAY_VANISHING\n        } else {\n          this.state = LightningStrike.RAY_STEADY\n        }\n\n        this.visible = true\n      } else {\n        this.visible = false\n\n        if (time < this.rayParameters.birthTime) {\n          this.state = LightningStrike.RAY_UNBORN\n        } else {\n          this.state = LightningStrike.RAY_EXTINGUISHED\n        }\n      }\n    }\n\n    init(rayParameters) {\n      // Init all the state from the parameters\n\n      this.rayParameters = rayParameters\n\n      // These parameters cannot change after lightning creation:\n\n      this.maxIterations = rayParameters.maxIterations !== undefined ? Math.floor(rayParameters.maxIterations) : 9\n      rayParameters.maxIterations = this.maxIterations\n      this.isStatic = rayParameters.isStatic !== undefined ? rayParameters.isStatic : false\n      rayParameters.isStatic = this.isStatic\n      this.ramification = rayParameters.ramification !== undefined ? Math.floor(rayParameters.ramification) : 5\n      rayParameters.ramification = this.ramification\n      this.maxSubrayRecursion =\n        rayParameters.maxSubrayRecursion !== undefined ? Math.floor(rayParameters.maxSubrayRecursion) : 3\n      rayParameters.maxSubrayRecursion = this.maxSubrayRecursion\n      this.recursionProbability =\n        rayParameters.recursionProbability !== undefined ? rayParameters.recursionProbability : 0.6\n      rayParameters.recursionProbability = this.recursionProbability\n      this.generateUVs = rayParameters.generateUVs !== undefined ? rayParameters.generateUVs : false\n      rayParameters.generateUVs = this.generateUVs\n\n      // Random generator\n      if (rayParameters.randomGenerator !== undefined) {\n        this.randomGenerator = rayParameters.randomGenerator\n        this.seedGenerator = rayParameters.randomGenerator\n\n        if (rayParameters.noiseSeed !== undefined) {\n          this.seedGenerator.setSeed(rayParameters.noiseSeed)\n        }\n      } else {\n        this.randomGenerator = LightningStrike.createRandomGenerator()\n        this.seedGenerator = Math\n      }\n\n      // Ray creation callbacks\n      if (rayParameters.onDecideSubrayCreation !== undefined) {\n        this.onDecideSubrayCreation = rayParameters.onDecideSubrayCreation\n      } else {\n        this.createDefaultSubrayCreationCallbacks()\n\n        if (rayParameters.onSubrayCreation !== undefined) {\n          this.onSubrayCreation = rayParameters.onSubrayCreation\n        }\n      }\n\n      // Internal state\n\n      this.state = LightningStrike.RAY_INITIALIZED\n\n      this.maxSubrays = Math.ceil(1 + Math.pow(this.ramification, Math.max(0, this.maxSubrayRecursion - 1)))\n      rayParameters.maxSubrays = this.maxSubrays\n\n      this.maxRaySegments = 2 * (1 << this.maxIterations)\n\n      this.subrays = []\n\n      for (let i = 0; i < this.maxSubrays; i++) {\n        this.subrays.push(this.createSubray())\n      }\n\n      this.raySegments = []\n\n      for (let i = 0; i < this.maxRaySegments; i++) {\n        this.raySegments.push(this.createSegment())\n      }\n\n      this.time = 0\n      this.timeFraction = 0\n      this.currentSegmentCallback = null\n      this.currentCreateTriangleVertices = this.generateUVs\n        ? this.createTriangleVerticesWithUVs\n        : this.createTriangleVerticesWithoutUVs\n      this.numSubrays = 0\n      this.currentSubray = null\n      this.currentSegmentIndex = 0\n      this.isInitialSegment = false\n      this.subrayProbability = 0\n\n      this.currentVertex = 0\n      this.currentIndex = 0\n      this.currentCoordinate = 0\n      this.currentUVCoordinate = 0\n      this.vertices = null\n      this.uvs = null\n      this.indices = null\n      this.positionAttribute = null\n      this.uvsAttribute = null\n\n      this.simplexX = new SimplexNoise(this.seedGenerator)\n      this.simplexY = new SimplexNoise(this.seedGenerator)\n      this.simplexZ = new SimplexNoise(this.seedGenerator)\n\n      // Temp vectors\n      this.forwards = new Vector3()\n      this.forwardsFill = new Vector3()\n      this.side = new Vector3()\n      this.down = new Vector3()\n      this.middlePos = new Vector3()\n      this.middleLinPos = new Vector3()\n      this.newPos = new Vector3()\n      this.vPos = new Vector3()\n      this.cross1 = new Vector3()\n    }\n\n    createMesh() {\n      const maxDrawableSegmentsPerSubRay = 1 << this.maxIterations\n\n      const maxVerts = 3 * (maxDrawableSegmentsPerSubRay + 1) * this.maxSubrays\n      const maxIndices = 18 * maxDrawableSegmentsPerSubRay * this.maxSubrays\n\n      this.vertices = new Float32Array(maxVerts * 3)\n      this.indices = new Uint32Array(maxIndices)\n\n      if (this.generateUVs) {\n        this.uvs = new Float32Array(maxVerts * 2)\n      }\n\n      // Populate the mesh\n      this.fillMesh(0)\n\n      this.setIndex(new Uint32BufferAttribute(this.indices, 1))\n\n      this.positionAttribute = new Float32BufferAttribute(this.vertices, 3)\n      this.setAttribute('position', this.positionAttribute)\n\n      if (this.generateUVs) {\n        this.uvsAttribute = new Float32BufferAttribute(new Float32Array(this.uvs), 2)\n        this.setAttribute('uv', this.uvsAttribute)\n      }\n\n      if (!this.isStatic) {\n        this.index.usage = DynamicDrawUsage\n        this.positionAttribute.usage = DynamicDrawUsage\n\n        if (this.generateUVs) {\n          this.uvsAttribute.usage = DynamicDrawUsage\n        }\n      }\n\n      // Store buffers for later modification\n      this.vertices = this.positionAttribute.array\n      this.indices = this.index.array\n\n      if (this.generateUVs) {\n        this.uvs = this.uvsAttribute.array\n      }\n    }\n\n    updateMesh(time) {\n      this.fillMesh(time)\n\n      this.drawRange.count = this.currentIndex\n\n      this.index.needsUpdate = true\n\n      this.positionAttribute.needsUpdate = true\n\n      if (this.generateUVs) {\n        this.uvsAttribute.needsUpdate = true\n      }\n    }\n\n    fillMesh(time) {\n      const scope = this\n\n      this.currentVertex = 0\n      this.currentIndex = 0\n      this.currentCoordinate = 0\n      this.currentUVCoordinate = 0\n\n      this.fractalRay(time, function fillVertices(segment) {\n        const subray = scope.currentSubray\n\n        if (time < subray.birthTime) {\n          //&& ( ! this.rayParameters.isEternal || scope.currentSubray.recursion > 0 ) ) {\n\n          return\n        } else if (this.rayParameters.isEternal && scope.currentSubray.recursion == 0) {\n          // Eternal rays don't propagate nor vanish, but its subrays do\n\n          scope.createPrism(segment)\n\n          scope.onDecideSubrayCreation(segment, scope)\n        } else if (time < subray.endPropagationTime) {\n          if (scope.timeFraction >= segment.fraction0 * subray.propagationTimeFactor) {\n            // Ray propagation has arrived to this segment\n\n            scope.createPrism(segment)\n\n            scope.onDecideSubrayCreation(segment, scope)\n          }\n        } else if (time < subray.beginVanishingTime) {\n          // Ray is steady (nor propagating nor vanishing)\n\n          scope.createPrism(segment)\n\n          scope.onDecideSubrayCreation(segment, scope)\n        } else {\n          if (scope.timeFraction <= subray.vanishingTimeFactor + segment.fraction1 * (1 - subray.vanishingTimeFactor)) {\n            // Segment has not yet vanished\n\n            scope.createPrism(segment)\n          }\n\n          scope.onDecideSubrayCreation(segment, scope)\n        }\n      })\n    }\n\n    addNewSubray(/*rayParameters*/) {\n      return this.subrays[this.numSubrays++]\n    }\n\n    initSubray(subray, rayParameters) {\n      subray.pos0.copy(rayParameters.sourceOffset)\n      subray.pos1.copy(rayParameters.destOffset)\n      subray.up0.copy(rayParameters.up0)\n      subray.up1.copy(rayParameters.up1)\n      subray.radius0 = rayParameters.radius0\n      subray.radius1 = rayParameters.radius1\n      subray.birthTime = rayParameters.birthTime\n      subray.deathTime = rayParameters.deathTime\n      subray.timeScale = rayParameters.timeScale\n      subray.roughness = rayParameters.roughness\n      subray.straightness = rayParameters.straightness\n      subray.propagationTimeFactor = rayParameters.propagationTimeFactor\n      subray.vanishingTimeFactor = rayParameters.vanishingTimeFactor\n\n      subray.maxIterations = this.maxIterations\n      subray.seed = rayParameters.noiseSeed !== undefined ? rayParameters.noiseSeed : 0\n      subray.recursion = 0\n    }\n\n    fractalRay(time, segmentCallback) {\n      this.time = time\n      this.currentSegmentCallback = segmentCallback\n      this.numSubrays = 0\n\n      // Add the top level subray\n      this.initSubray(this.addNewSubray(), this.rayParameters)\n\n      // Process all subrays that are being generated until consuming all of them\n      for (let subrayIndex = 0; subrayIndex < this.numSubrays; subrayIndex++) {\n        const subray = this.subrays[subrayIndex]\n        this.currentSubray = subray\n\n        this.randomGenerator.setSeed(subray.seed)\n\n        subray.endPropagationTime = MathUtils.lerp(subray.birthTime, subray.deathTime, subray.propagationTimeFactor)\n        subray.beginVanishingTime = MathUtils.lerp(subray.deathTime, subray.birthTime, 1 - subray.vanishingTimeFactor)\n\n        const random1 = this.randomGenerator.random\n        subray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1000)\n        subray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1000)\n\n        this.timeFraction = (time - subray.birthTime) / (subray.deathTime - subray.birthTime)\n\n        this.currentSegmentIndex = 0\n        this.isInitialSegment = true\n\n        const segment = this.getNewSegment()\n        segment.iteration = 0\n        segment.pos0.copy(subray.pos0)\n        segment.pos1.copy(subray.pos1)\n        segment.linPos0.copy(subray.linPos0)\n        segment.linPos1.copy(subray.linPos1)\n        segment.up0.copy(subray.up0)\n        segment.up1.copy(subray.up1)\n        segment.radius0 = subray.radius0\n        segment.radius1 = subray.radius1\n        segment.fraction0 = 0\n        segment.fraction1 = 1\n        segment.positionVariationFactor = 1 - subray.straightness\n\n        this.subrayProbability =\n          (this.ramification * Math.pow(this.recursionProbability, subray.recursion)) / (1 << subray.maxIterations)\n\n        this.fractalRayRecursive(segment)\n      }\n\n      this.currentSegmentCallback = null\n      this.currentSubray = null\n    }\n\n    fractalRayRecursive(segment) {\n      // Leave recursion condition\n      if (segment.iteration >= this.currentSubray.maxIterations) {\n        this.currentSegmentCallback(segment)\n\n        return\n      }\n\n      // Interpolation\n      this.forwards.subVectors(segment.pos1, segment.pos0)\n      let lForwards = this.forwards.length()\n\n      if (lForwards < 0.000001) {\n        this.forwards.set(0, 0, 0.01)\n        lForwards = this.forwards.length()\n      }\n\n      const middleRadius = (segment.radius0 + segment.radius1) * 0.5\n      const middleFraction = (segment.fraction0 + segment.fraction1) * 0.5\n\n      const timeDimension = this.time * this.currentSubray.timeScale * Math.pow(2, segment.iteration)\n\n      this.middlePos.lerpVectors(segment.pos0, segment.pos1, 0.5)\n      this.middleLinPos.lerpVectors(segment.linPos0, segment.linPos1, 0.5)\n      const p = this.middleLinPos\n\n      // Noise\n      this.newPos.set(\n        this.simplexX.noise4d(p.x, p.y, p.z, timeDimension),\n        this.simplexY.noise4d(p.x, p.y, p.z, timeDimension),\n        this.simplexZ.noise4d(p.x, p.y, p.z, timeDimension),\n      )\n\n      this.newPos.multiplyScalar(segment.positionVariationFactor * lForwards)\n      this.newPos.add(this.middlePos)\n\n      // Recursion\n\n      const newSegment1 = this.getNewSegment()\n      newSegment1.pos0.copy(segment.pos0)\n      newSegment1.pos1.copy(this.newPos)\n      newSegment1.linPos0.copy(segment.linPos0)\n      newSegment1.linPos1.copy(this.middleLinPos)\n      newSegment1.up0.copy(segment.up0)\n      newSegment1.up1.copy(segment.up1)\n      newSegment1.radius0 = segment.radius0\n      newSegment1.radius1 = middleRadius\n      newSegment1.fraction0 = segment.fraction0\n      newSegment1.fraction1 = middleFraction\n      newSegment1.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness\n      newSegment1.iteration = segment.iteration + 1\n\n      const newSegment2 = this.getNewSegment()\n      newSegment2.pos0.copy(this.newPos)\n      newSegment2.pos1.copy(segment.pos1)\n      newSegment2.linPos0.copy(this.middleLinPos)\n      newSegment2.linPos1.copy(segment.linPos1)\n      this.cross1.crossVectors(segment.up0, this.forwards.normalize())\n      newSegment2.up0.crossVectors(this.forwards, this.cross1).normalize()\n      newSegment2.up1.copy(segment.up1)\n      newSegment2.radius0 = middleRadius\n      newSegment2.radius1 = segment.radius1\n      newSegment2.fraction0 = middleFraction\n      newSegment2.fraction1 = segment.fraction1\n      newSegment2.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness\n      newSegment2.iteration = segment.iteration + 1\n\n      this.fractalRayRecursive(newSegment1)\n\n      this.fractalRayRecursive(newSegment2)\n    }\n\n    createPrism(segment) {\n      // Creates one triangular prism and its vertices at the segment\n\n      this.forwardsFill.subVectors(segment.pos1, segment.pos0).normalize()\n\n      if (this.isInitialSegment) {\n        this.currentCreateTriangleVertices(segment.pos0, segment.up0, this.forwardsFill, segment.radius0, 0)\n\n        this.isInitialSegment = false\n      }\n\n      this.currentCreateTriangleVertices(\n        segment.pos1,\n        segment.up0,\n        this.forwardsFill,\n        segment.radius1,\n        segment.fraction1,\n      )\n\n      this.createPrismFaces()\n    }\n\n    createTriangleVerticesWithoutUVs(pos, up, forwards, radius) {\n      // Create an equilateral triangle (only vertices)\n\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * LightningStrike.COS30DEG)\n      this.down.copy(up).multiplyScalar(-radius * LightningStrike.SIN30DEG)\n\n      const p = this.vPos\n      const v = this.vertices\n\n      p.copy(pos).sub(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      p.copy(pos).add(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      p.copy(up).multiplyScalar(radius).add(pos)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      this.currentVertex += 3\n    }\n\n    createTriangleVerticesWithUVs(pos, up, forwards, radius, u) {\n      // Create an equilateral triangle (only vertices)\n\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * LightningStrike.COS30DEG)\n      this.down.copy(up).multiplyScalar(-radius * LightningStrike.SIN30DEG)\n\n      const p = this.vPos\n      const v = this.vertices\n      const uv = this.uvs\n\n      p.copy(pos).sub(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 0\n\n      p.copy(pos).add(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 0.5\n\n      p.copy(up).multiplyScalar(radius).add(pos)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 1\n\n      this.currentVertex += 3\n    }\n\n    createPrismFaces(vertex /*, index*/) {\n      const indices = this.indices\n      vertex = this.currentVertex - 6\n\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 5\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 5\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 5\n    }\n\n    createDefaultSubrayCreationCallbacks() {\n      const random1 = this.randomGenerator.random\n\n      this.onDecideSubrayCreation = function (segment, lightningStrike) {\n        // Decide subrays creation at parent (sub)ray segment\n\n        const subray = lightningStrike.currentSubray\n\n        const period = lightningStrike.rayParameters.subrayPeriod\n        const dutyCycle = lightningStrike.rayParameters.subrayDutyCycle\n\n        const phase0 =\n          lightningStrike.rayParameters.isEternal && subray.recursion == 0\n            ? -random1() * period\n            : MathUtils.lerp(subray.birthTime, subray.endPropagationTime, segment.fraction0) - random1() * period\n\n        const phase = lightningStrike.time - phase0\n        const currentCycle = Math.floor(phase / period)\n\n        const childSubraySeed = random1() * (currentCycle + 1)\n\n        const isActive = phase % period <= dutyCycle * period\n\n        let probability = 0\n\n        if (isActive) {\n          probability = lightningStrike.subrayProbability\n          // Distribution test: probability *= segment.fraction0 > 0.5 && segment.fraction0 < 0.9 ? 1 / 0.4 : 0;\n        }\n\n        if (\n          subray.recursion < lightningStrike.maxSubrayRecursion &&\n          lightningStrike.numSubrays < lightningStrike.maxSubrays &&\n          random1() < probability\n        ) {\n          const childSubray = lightningStrike.addNewSubray()\n\n          const parentSeed = lightningStrike.randomGenerator.getSeed()\n          childSubray.seed = childSubraySeed\n          lightningStrike.randomGenerator.setSeed(childSubraySeed)\n\n          childSubray.recursion = subray.recursion + 1\n          childSubray.maxIterations = Math.max(1, subray.maxIterations - 1)\n\n          childSubray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1000)\n          childSubray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1000)\n          childSubray.up0.copy(subray.up0)\n          childSubray.up1.copy(subray.up1)\n          childSubray.radius0 = segment.radius0 * lightningStrike.rayParameters.radius0Factor\n          childSubray.radius1 = Math.min(\n            lightningStrike.rayParameters.minRadius,\n            segment.radius1 * lightningStrike.rayParameters.radius1Factor,\n          )\n\n          childSubray.birthTime = phase0 + currentCycle * period\n          childSubray.deathTime = childSubray.birthTime + period * dutyCycle\n\n          if (!lightningStrike.rayParameters.isEternal && subray.recursion == 0) {\n            childSubray.birthTime = Math.max(childSubray.birthTime, subray.birthTime)\n            childSubray.deathTime = Math.min(childSubray.deathTime, subray.deathTime)\n          }\n\n          childSubray.timeScale = subray.timeScale * 2\n          childSubray.roughness = subray.roughness\n          childSubray.straightness = subray.straightness\n          childSubray.propagationTimeFactor = subray.propagationTimeFactor\n          childSubray.vanishingTimeFactor = subray.vanishingTimeFactor\n\n          lightningStrike.onSubrayCreation(segment, subray, childSubray, lightningStrike)\n\n          lightningStrike.randomGenerator.setSeed(parentSeed)\n        }\n      }\n\n      const vec1Pos = new Vector3()\n      const vec2Forward = new Vector3()\n      const vec3Side = new Vector3()\n      const vec4Up = new Vector3()\n\n      this.onSubrayCreation = function (segment, parentSubray, childSubray, lightningStrike) {\n        // Decide childSubray origin and destination positions (pos0 and pos1) and possibly other properties of childSubray\n\n        // Just use the default cone position generator\n        lightningStrike.subrayCylinderPosition(segment, parentSubray, childSubray, 0.5, 0.6, 0.2)\n      }\n\n      this.subrayConePosition = function (\n        segment,\n        parentSubray,\n        childSubray,\n        heightFactor,\n        sideWidthFactor,\n        minSideWidthFactor,\n      ) {\n        // Sets childSubray pos0 and pos1 in a cone\n\n        childSubray.pos0.copy(segment.pos0)\n\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0)\n        vec2Forward.copy(vec1Pos).normalize()\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * (random1() * heightFactor))\n        const length = vec1Pos.length()\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward)\n        const angle = 2 * Math.PI * random1()\n        vec3Side.multiplyScalar(Math.cos(angle))\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle))\n\n        childSubray.pos1\n          .copy(vec3Side)\n          .add(vec4Up)\n          .multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor)))\n          .add(vec1Pos)\n          .add(parentSubray.pos0)\n      }\n\n      this.subrayCylinderPosition = function (\n        segment,\n        parentSubray,\n        childSubray,\n        heightFactor,\n        sideWidthFactor,\n        minSideWidthFactor,\n      ) {\n        // Sets childSubray pos0 and pos1 in a cylinder\n\n        childSubray.pos0.copy(segment.pos0)\n\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0)\n        vec2Forward.copy(vec1Pos).normalize()\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * ((2 * random1() - 1) * heightFactor))\n        const length = vec1Pos.length()\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward)\n        const angle = 2 * Math.PI * random1()\n        vec3Side.multiplyScalar(Math.cos(angle))\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle))\n\n        childSubray.pos1\n          .copy(vec3Side)\n          .add(vec4Up)\n          .multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor)))\n          .add(vec1Pos)\n          .add(parentSubray.pos0)\n      }\n    }\n\n    createSubray() {\n      return {\n        seed: 0,\n        maxIterations: 0,\n        recursion: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        birthTime: 0,\n        deathTime: 0,\n        timeScale: 0,\n        roughness: 0,\n        straightness: 0,\n        propagationTimeFactor: 0,\n        vanishingTimeFactor: 0,\n        endPropagationTime: 0,\n        beginVanishingTime: 0,\n      }\n    }\n\n    createSegment() {\n      return {\n        iteration: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        fraction0: 0,\n        fraction1: 0,\n        positionVariationFactor: 0,\n      }\n    }\n\n    getNewSegment() {\n      return this.raySegments[this.currentSegmentIndex++]\n    }\n\n    copy(source) {\n      super.copy(source)\n\n      this.init(LightningStrike.copyParameters({}, source.rayParameters))\n\n      return this\n    }\n\n    clone() {\n      return new this.constructor(LightningStrike.copyParameters({}, this.rayParameters))\n    }\n  }\n\n  return LightningStrike\n})()\n\nexport { LightningStrike }\n"], "mappings": ";;;;;;;;;;;;;AA+GK,MAACA,eAAA,GAAmC,sBAAM;EAC7C,MAAMC,gBAAA,GAAN,cAA8BC,cAAA,CAAe;IAY3CC,YAAYC,aAAA,GAAgB,IAAI;MAC9B,MAAO;MAEP,KAAKC,iBAAA,GAAoB;MAEzB,KAAKC,IAAA,GAAO;MAGZ,KAAKC,IAAA,CAAKN,gBAAA,CAAgBO,cAAA,CAAeJ,aAAA,EAAeA,aAAa,CAAC;MAGtE,KAAKK,UAAA,CAAY;IAClB;IAED,OAAOC,sBAAA,EAAwB;MAC7B,MAAMC,QAAA,GAAW;MACjB,MAAMC,KAAA,GAAQ,EAAE;MAEhB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,QAAA,EAAUE,CAAA,IAAK;QACjCD,KAAA,CAAME,IAAA,CAAKC,IAAA,CAAKC,MAAA,EAAQ;MACzB;MAED,MAAMC,SAAA,GAAY;QAChBC,WAAA,EAAa;QAEbF,MAAA,EAAQ,SAAAA,CAAA,EAAY;UAClB,MAAMG,KAAA,GAAQP,KAAA,CAAMK,SAAA,CAAUC,WAAW;UAEzCD,SAAA,CAAUC,WAAA,IAAeD,SAAA,CAAUC,WAAA,GAAc,KAAKP,QAAA;UAEtD,OAAOQ,KAAA;QACR;QAEDC,OAAA,EAAS,SAAAA,CAAA,EAAY;UACnB,OAAOH,SAAA,CAAUC,WAAA,GAAcP,QAAA;QAChC;QAEDU,OAAA,EAAS,SAAAA,CAAUC,IAAA,EAAM;UACvBL,SAAA,CAAUC,WAAA,GAAcH,IAAA,CAAKQ,KAAA,CAAMD,IAAA,GAAOX,QAAQ,IAAIA,QAAA;QACvD;MACF;MAED,OAAOM,SAAA;IACR;IAED,OAAOT,eAAegB,IAAA,GAAO,IAAIC,MAAA,GAAS,IAAI;MAC5C,MAAMC,OAAA,GAAU,SAAAA,CAAUC,CAAA,EAAG;QAC3B,IAAIF,MAAA,KAAWD,IAAA,EAAM;UACnB,OAAOG,CAAA;QACjB,OAAe;UACL,OAAOA,CAAA,CAAEC,KAAA,CAAO;QACjB;MACF;MAECJ,IAAA,CAAKK,YAAA,GAAeJ,MAAA,CAAOI,YAAA,KAAiB,SAAYH,OAAA,CAAQD,MAAA,CAAOI,YAAY,IAAI,IAAIC,OAAA,CAAQ,GAAG,KAAK,CAAC,GAC3GN,IAAA,CAAKO,UAAA,GAAaN,MAAA,CAAOM,UAAA,KAAe,SAAYL,OAAA,CAAQD,MAAA,CAAOM,UAAU,IAAI,IAAID,OAAA,CAAQ,GAAG,GAAG,CAAC,GACpGN,IAAA,CAAKQ,SAAA,GAAYP,MAAA,CAAOO,SAAA,KAAc,SAAYP,MAAA,CAAOO,SAAA,GAAY,GACrER,IAAA,CAAKS,SAAA,GAAYR,MAAA,CAAOQ,SAAA,KAAc,SAAYR,MAAA,CAAOQ,SAAA,GAAY,KACrET,IAAA,CAAKU,YAAA,GAAeT,MAAA,CAAOS,YAAA,KAAiB,SAAYT,MAAA,CAAOS,YAAA,GAAe,KAC9EV,IAAA,CAAKW,GAAA,GAAMV,MAAA,CAAOU,GAAA,KAAQ,SAAYT,OAAA,CAAQD,MAAA,CAAOU,GAAG,IAAI,IAAIL,OAAA,CAAQ,GAAG,GAAG,CAAC;MAChFN,IAAA,CAAKY,GAAA,GAAMX,MAAA,CAAOW,GAAA,KAAQ,SAAYV,OAAA,CAAQD,MAAA,CAAOW,GAAG,IAAI,IAAIN,OAAA,CAAQ,GAAG,GAAG,CAAC,GAC9EN,IAAA,CAAKa,OAAA,GAAUZ,MAAA,CAAOY,OAAA,KAAY,SAAYZ,MAAA,CAAOY,OAAA,GAAU,GAC/Db,IAAA,CAAKc,OAAA,GAAUb,MAAA,CAAOa,OAAA,KAAY,SAAYb,MAAA,CAAOa,OAAA,GAAU,GAC/Dd,IAAA,CAAKe,aAAA,GAAgBd,MAAA,CAAOc,aAAA,KAAkB,SAAYd,MAAA,CAAOc,aAAA,GAAgB,KACjFf,IAAA,CAAKgB,aAAA,GAAgBf,MAAA,CAAOe,aAAA,KAAkB,SAAYf,MAAA,CAAOe,aAAA,GAAgB,KACjFhB,IAAA,CAAKiB,SAAA,GAAYhB,MAAA,CAAOgB,SAAA,KAAc,SAAYhB,MAAA,CAAOgB,SAAA,GAAY;MAAA;MAGrEjB,IAAA,CAAKkB,SAAA,GACJjB,MAAA,CAAOiB,SAAA,KAAc,SACjBjB,MAAA,CAAOiB,SAAA,GACPjB,MAAA,CAAOkB,SAAA,KAAc,UAAalB,MAAA,CAAOmB,SAAA,KAAc,QAC5DpB,IAAA,CAAKmB,SAAA,GAAYlB,MAAA,CAAOkB,SAAA,EACxBnB,IAAA,CAAKoB,SAAA,GAAYnB,MAAA,CAAOmB,SAAA,EACxBpB,IAAA,CAAKqB,qBAAA,GAAwBpB,MAAA,CAAOoB,qBAAA,KAA0B,SAAYpB,MAAA,CAAOoB,qBAAA,GAAwB,KACzGrB,IAAA,CAAKsB,mBAAA,GAAsBrB,MAAA,CAAOqB,mBAAA,KAAwB,SAAYrB,MAAA,CAAOqB,mBAAA,GAAsB,KACnGtB,IAAA,CAAKuB,YAAA,GAAetB,MAAA,CAAOsB,YAAA,KAAiB,SAAYtB,MAAA,CAAOsB,YAAA,GAAe,GAC9EvB,IAAA,CAAKwB,eAAA,GAAkBvB,MAAA,CAAOuB,eAAA,KAAoB,SAAYvB,MAAA,CAAOuB,eAAA,GAAkB;MAI1FxB,IAAA,CAAKyB,aAAA,GAAgBxB,MAAA,CAAOwB,aAAA,KAAkB,SAAYxB,MAAA,CAAOwB,aAAA,GAAgB;MACjFzB,IAAA,CAAK0B,QAAA,GAAWzB,MAAA,CAAOyB,QAAA,KAAa,SAAYzB,MAAA,CAAOyB,QAAA,GAAW;MAClE1B,IAAA,CAAK2B,YAAA,GAAe1B,MAAA,CAAO0B,YAAA,KAAiB,SAAY1B,MAAA,CAAO0B,YAAA,GAAe;MAC9E3B,IAAA,CAAK4B,kBAAA,GAAqB3B,MAAA,CAAO2B,kBAAA,KAAuB,SAAY3B,MAAA,CAAO2B,kBAAA,GAAqB;MAChG5B,IAAA,CAAK6B,oBAAA,GAAuB5B,MAAA,CAAO4B,oBAAA,KAAyB,SAAY5B,MAAA,CAAO4B,oBAAA,GAAuB;MACtG7B,IAAA,CAAK8B,WAAA,GAAc7B,MAAA,CAAO6B,WAAA,KAAgB,SAAY7B,MAAA,CAAO6B,WAAA,GAAc;MACzE9B,IAAA,CAAK+B,eAAA,GAAkB9B,MAAA,CAAO8B,eAAA,EAC7B/B,IAAA,CAAKgC,SAAA,GAAY/B,MAAA,CAAO+B,SAAA,EACxBhC,IAAA,CAAKiC,sBAAA,GAAyBhC,MAAA,CAAOgC,sBAAA,EACrCjC,IAAA,CAAKkC,gBAAA,GAAmBjC,MAAA,CAAOiC,gBAAA;MAElC,OAAOlC,IAAA;IACR;IAEDmC,OAAOC,IAAA,EAAM;MACX,IAAI,KAAKV,QAAA,EAAU;MAEnB,IACE,KAAK9C,aAAA,CAAcsC,SAAA,IAClB,KAAKtC,aAAA,CAAcuC,SAAA,IAAaiB,IAAA,IAAQA,IAAA,IAAQ,KAAKxD,aAAA,CAAcwC,SAAA,EACpE;QACA,KAAKiB,UAAA,CAAWD,IAAI;QAEpB,IAAIA,IAAA,GAAO,KAAKE,OAAA,CAAQ,CAAC,EAAEC,kBAAA,EAAoB;UAC7C,KAAKC,KAAA,GAAQ/D,gBAAA,CAAgBgE,eAAA;QACvC,WAAmBL,IAAA,GAAO,KAAKE,OAAA,CAAQ,CAAC,EAAEI,kBAAA,EAAoB;UACpD,KAAKF,KAAA,GAAQ/D,gBAAA,CAAgBkE,aAAA;QACvC,OAAe;UACL,KAAKH,KAAA,GAAQ/D,gBAAA,CAAgBmE,UAAA;QAC9B;QAED,KAAKC,OAAA,GAAU;MACvB,OAAa;QACL,KAAKA,OAAA,GAAU;QAEf,IAAIT,IAAA,GAAO,KAAKxD,aAAA,CAAcuC,SAAA,EAAW;UACvC,KAAKqB,KAAA,GAAQ/D,gBAAA,CAAgBqE,UAAA;QACvC,OAAe;UACL,KAAKN,KAAA,GAAQ/D,gBAAA,CAAgBsE,gBAAA;QAC9B;MACF;IACF;IAEDhE,KAAKH,aAAA,EAAe;MAGlB,KAAKA,aAAA,GAAgBA,aAAA;MAIrB,KAAK6C,aAAA,GAAgB7C,aAAA,CAAc6C,aAAA,KAAkB,SAAYlC,IAAA,CAAKQ,KAAA,CAAMnB,aAAA,CAAc6C,aAAa,IAAI;MAC3G7C,aAAA,CAAc6C,aAAA,GAAgB,KAAKA,aAAA;MACnC,KAAKC,QAAA,GAAW9C,aAAA,CAAc8C,QAAA,KAAa,SAAY9C,aAAA,CAAc8C,QAAA,GAAW;MAChF9C,aAAA,CAAc8C,QAAA,GAAW,KAAKA,QAAA;MAC9B,KAAKC,YAAA,GAAe/C,aAAA,CAAc+C,YAAA,KAAiB,SAAYpC,IAAA,CAAKQ,KAAA,CAAMnB,aAAA,CAAc+C,YAAY,IAAI;MACxG/C,aAAA,CAAc+C,YAAA,GAAe,KAAKA,YAAA;MAClC,KAAKC,kBAAA,GACHhD,aAAA,CAAcgD,kBAAA,KAAuB,SAAYrC,IAAA,CAAKQ,KAAA,CAAMnB,aAAA,CAAcgD,kBAAkB,IAAI;MAClGhD,aAAA,CAAcgD,kBAAA,GAAqB,KAAKA,kBAAA;MACxC,KAAKC,oBAAA,GACHjD,aAAA,CAAciD,oBAAA,KAAyB,SAAYjD,aAAA,CAAciD,oBAAA,GAAuB;MAC1FjD,aAAA,CAAciD,oBAAA,GAAuB,KAAKA,oBAAA;MAC1C,KAAKC,WAAA,GAAclD,aAAA,CAAckD,WAAA,KAAgB,SAAYlD,aAAA,CAAckD,WAAA,GAAc;MACzFlD,aAAA,CAAckD,WAAA,GAAc,KAAKA,WAAA;MAGjC,IAAIlD,aAAA,CAAcmD,eAAA,KAAoB,QAAW;QAC/C,KAAKA,eAAA,GAAkBnD,aAAA,CAAcmD,eAAA;QACrC,KAAKiB,aAAA,GAAgBpE,aAAA,CAAcmD,eAAA;QAEnC,IAAInD,aAAA,CAAcoD,SAAA,KAAc,QAAW;UACzC,KAAKgB,aAAA,CAAcnD,OAAA,CAAQjB,aAAA,CAAcoD,SAAS;QACnD;MACT,OAAa;QACL,KAAKD,eAAA,GAAkBtD,gBAAA,CAAgBS,qBAAA,CAAuB;QAC9D,KAAK8D,aAAA,GAAgBzD,IAAA;MACtB;MAGD,IAAIX,aAAA,CAAcqD,sBAAA,KAA2B,QAAW;QACtD,KAAKA,sBAAA,GAAyBrD,aAAA,CAAcqD,sBAAA;MACpD,OAAa;QACL,KAAKgB,oCAAA,CAAsC;QAE3C,IAAIrE,aAAA,CAAcsD,gBAAA,KAAqB,QAAW;UAChD,KAAKA,gBAAA,GAAmBtD,aAAA,CAAcsD,gBAAA;QACvC;MACF;MAID,KAAKM,KAAA,GAAQ/D,gBAAA,CAAgByE,eAAA;MAE7B,KAAKC,UAAA,GAAa5D,IAAA,CAAK6D,IAAA,CAAK,IAAI7D,IAAA,CAAK8D,GAAA,CAAI,KAAK1B,YAAA,EAAcpC,IAAA,CAAK+D,GAAA,CAAI,GAAG,KAAK1B,kBAAA,GAAqB,CAAC,CAAC,CAAC;MACrGhD,aAAA,CAAcuE,UAAA,GAAa,KAAKA,UAAA;MAEhC,KAAKI,cAAA,GAAiB,KAAK,KAAK,KAAK9B,aAAA;MAErC,KAAKa,OAAA,GAAU,EAAE;MAEjB,SAASjD,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK8D,UAAA,EAAY9D,CAAA,IAAK;QACxC,KAAKiD,OAAA,CAAQhD,IAAA,CAAK,KAAKkE,YAAA,CAAY,CAAE;MACtC;MAED,KAAKC,WAAA,GAAc,EAAE;MAErB,SAASpE,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKkE,cAAA,EAAgBlE,CAAA,IAAK;QAC5C,KAAKoE,WAAA,CAAYnE,IAAA,CAAK,KAAKoE,aAAA,CAAa,CAAE;MAC3C;MAED,KAAKtB,IAAA,GAAO;MACZ,KAAKuB,YAAA,GAAe;MACpB,KAAKC,sBAAA,GAAyB;MAC9B,KAAKC,6BAAA,GAAgC,KAAK/B,WAAA,GACtC,KAAKgC,6BAAA,GACL,KAAKC,gCAAA;MACT,KAAKC,UAAA,GAAa;MAClB,KAAKC,aAAA,GAAgB;MACrB,KAAKC,mBAAA,GAAsB;MAC3B,KAAKC,gBAAA,GAAmB;MACxB,KAAKC,iBAAA,GAAoB;MAEzB,KAAKC,aAAA,GAAgB;MACrB,KAAKC,YAAA,GAAe;MACpB,KAAKC,iBAAA,GAAoB;MACzB,KAAKC,mBAAA,GAAsB;MAC3B,KAAKC,QAAA,GAAW;MAChB,KAAKC,GAAA,GAAM;MACX,KAAKC,OAAA,GAAU;MACf,KAAKC,iBAAA,GAAoB;MACzB,KAAKC,YAAA,GAAe;MAEpB,KAAKC,QAAA,GAAW,IAAIC,YAAA,CAAa,KAAK/B,aAAa;MACnD,KAAKgC,QAAA,GAAW,IAAID,YAAA,CAAa,KAAK/B,aAAa;MACnD,KAAKiC,QAAA,GAAW,IAAIF,YAAA,CAAa,KAAK/B,aAAa;MAGnD,KAAKkC,QAAA,GAAW,IAAI5E,OAAA,CAAS;MAC7B,KAAK6E,YAAA,GAAe,IAAI7E,OAAA,CAAS;MACjC,KAAK8E,IAAA,GAAO,IAAI9E,OAAA,CAAS;MACzB,KAAK+E,IAAA,GAAO,IAAI/E,OAAA,CAAS;MACzB,KAAKgF,SAAA,GAAY,IAAIhF,OAAA,CAAS;MAC9B,KAAKiF,YAAA,GAAe,IAAIjF,OAAA,CAAS;MACjC,KAAKkF,MAAA,GAAS,IAAIlF,OAAA,CAAS;MAC3B,KAAKmF,IAAA,GAAO,IAAInF,OAAA,CAAS;MACzB,KAAKoF,MAAA,GAAS,IAAIpF,OAAA,CAAS;IAC5B;IAEDrB,WAAA,EAAa;MACX,MAAM0G,4BAAA,GAA+B,KAAK,KAAKlE,aAAA;MAE/C,MAAMmE,QAAA,GAAW,KAAKD,4BAAA,GAA+B,KAAK,KAAKxC,UAAA;MAC/D,MAAM0C,UAAA,GAAa,KAAKF,4BAAA,GAA+B,KAAKxC,UAAA;MAE5D,KAAKsB,QAAA,GAAW,IAAIqB,YAAA,CAAaF,QAAA,GAAW,CAAC;MAC7C,KAAKjB,OAAA,GAAU,IAAIoB,WAAA,CAAYF,UAAU;MAEzC,IAAI,KAAK/D,WAAA,EAAa;QACpB,KAAK4C,GAAA,GAAM,IAAIoB,YAAA,CAAaF,QAAA,GAAW,CAAC;MACzC;MAGD,KAAKI,QAAA,CAAS,CAAC;MAEf,KAAKC,QAAA,CAAS,IAAIC,qBAAA,CAAsB,KAAKvB,OAAA,EAAS,CAAC,CAAC;MAExD,KAAKC,iBAAA,GAAoB,IAAIuB,sBAAA,CAAuB,KAAK1B,QAAA,EAAU,CAAC;MACpE,KAAK2B,YAAA,CAAa,YAAY,KAAKxB,iBAAiB;MAEpD,IAAI,KAAK9C,WAAA,EAAa;QACpB,KAAK+C,YAAA,GAAe,IAAIsB,sBAAA,CAAuB,IAAIL,YAAA,CAAa,KAAKpB,GAAG,GAAG,CAAC;QAC5E,KAAK0B,YAAA,CAAa,MAAM,KAAKvB,YAAY;MAC1C;MAED,IAAI,CAAC,KAAKnD,QAAA,EAAU;QAClB,KAAK2E,KAAA,CAAMC,KAAA,GAAQC,gBAAA;QACnB,KAAK3B,iBAAA,CAAkB0B,KAAA,GAAQC,gBAAA;QAE/B,IAAI,KAAKzE,WAAA,EAAa;UACpB,KAAK+C,YAAA,CAAayB,KAAA,GAAQC,gBAAA;QAC3B;MACF;MAGD,KAAK9B,QAAA,GAAW,KAAKG,iBAAA,CAAkB4B,KAAA;MACvC,KAAK7B,OAAA,GAAU,KAAK0B,KAAA,CAAMG,KAAA;MAE1B,IAAI,KAAK1E,WAAA,EAAa;QACpB,KAAK4C,GAAA,GAAM,KAAKG,YAAA,CAAa2B,KAAA;MAC9B;IACF;IAEDnE,WAAWD,IAAA,EAAM;MACf,KAAK4D,QAAA,CAAS5D,IAAI;MAElB,KAAKqE,SAAA,CAAUC,KAAA,GAAQ,KAAKpC,YAAA;MAE5B,KAAK+B,KAAA,CAAMM,WAAA,GAAc;MAEzB,KAAK/B,iBAAA,CAAkB+B,WAAA,GAAc;MAErC,IAAI,KAAK7E,WAAA,EAAa;QACpB,KAAK+C,YAAA,CAAa8B,WAAA,GAAc;MACjC;IACF;IAEDX,SAAS5D,IAAA,EAAM;MACb,MAAMwE,KAAA,GAAQ;MAEd,KAAKvC,aAAA,GAAgB;MACrB,KAAKC,YAAA,GAAe;MACpB,KAAKC,iBAAA,GAAoB;MACzB,KAAKC,mBAAA,GAAsB;MAE3B,KAAKqC,UAAA,CAAWzE,IAAA,EAAM,SAAS0E,aAAaC,OAAA,EAAS;QACnD,MAAMC,MAAA,GAASJ,KAAA,CAAM3C,aAAA;QAErB,IAAI7B,IAAA,GAAO4E,MAAA,CAAO7F,SAAA,EAAW;UAG3B;QACV,WAAmB,KAAKvC,aAAA,CAAcsC,SAAA,IAAa0F,KAAA,CAAM3C,aAAA,CAAcgD,SAAA,IAAa,GAAG;UAG7EL,KAAA,CAAMM,WAAA,CAAYH,OAAO;UAEzBH,KAAA,CAAM3E,sBAAA,CAAuB8E,OAAA,EAASH,KAAK;QACrD,WAAmBxE,IAAA,GAAO4E,MAAA,CAAOzE,kBAAA,EAAoB;UAC3C,IAAIqE,KAAA,CAAMjD,YAAA,IAAgBoD,OAAA,CAAQI,SAAA,GAAYH,MAAA,CAAO3F,qBAAA,EAAuB;YAG1EuF,KAAA,CAAMM,WAAA,CAAYH,OAAO;YAEzBH,KAAA,CAAM3E,sBAAA,CAAuB8E,OAAA,EAASH,KAAK;UAC5C;QACX,WAAmBxE,IAAA,GAAO4E,MAAA,CAAOtE,kBAAA,EAAoB;UAG3CkE,KAAA,CAAMM,WAAA,CAAYH,OAAO;UAEzBH,KAAA,CAAM3E,sBAAA,CAAuB8E,OAAA,EAASH,KAAK;QACrD,OAAe;UACL,IAAIA,KAAA,CAAMjD,YAAA,IAAgBqD,MAAA,CAAO1F,mBAAA,GAAsByF,OAAA,CAAQK,SAAA,IAAa,IAAIJ,MAAA,CAAO1F,mBAAA,GAAsB;YAG3GsF,KAAA,CAAMM,WAAA,CAAYH,OAAO;UAC1B;UAEDH,KAAA,CAAM3E,sBAAA,CAAuB8E,OAAA,EAASH,KAAK;QAC5C;MACT,CAAO;IACF;IAEDS,aAAA,EAAgC;MAC9B,OAAO,KAAK/E,OAAA,CAAQ,KAAK0B,UAAA,EAAY;IACtC;IAEDsD,WAAWN,MAAA,EAAQpI,aAAA,EAAe;MAChCoI,MAAA,CAAOO,IAAA,CAAKC,IAAA,CAAK5I,aAAA,CAAcyB,YAAY;MAC3C2G,MAAA,CAAOS,IAAA,CAAKD,IAAA,CAAK5I,aAAA,CAAc2B,UAAU;MACzCyG,MAAA,CAAOrG,GAAA,CAAI6G,IAAA,CAAK5I,aAAA,CAAc+B,GAAG;MACjCqG,MAAA,CAAOpG,GAAA,CAAI4G,IAAA,CAAK5I,aAAA,CAAcgC,GAAG;MACjCoG,MAAA,CAAOnG,OAAA,GAAUjC,aAAA,CAAciC,OAAA;MAC/BmG,MAAA,CAAOlG,OAAA,GAAUlC,aAAA,CAAckC,OAAA;MAC/BkG,MAAA,CAAO7F,SAAA,GAAYvC,aAAA,CAAcuC,SAAA;MACjC6F,MAAA,CAAO5F,SAAA,GAAYxC,aAAA,CAAcwC,SAAA;MACjC4F,MAAA,CAAOxG,SAAA,GAAY5B,aAAA,CAAc4B,SAAA;MACjCwG,MAAA,CAAOvG,SAAA,GAAY7B,aAAA,CAAc6B,SAAA;MACjCuG,MAAA,CAAOtG,YAAA,GAAe9B,aAAA,CAAc8B,YAAA;MACpCsG,MAAA,CAAO3F,qBAAA,GAAwBzC,aAAA,CAAcyC,qBAAA;MAC7C2F,MAAA,CAAO1F,mBAAA,GAAsB1C,aAAA,CAAc0C,mBAAA;MAE3C0F,MAAA,CAAOvF,aAAA,GAAgB,KAAKA,aAAA;MAC5BuF,MAAA,CAAOlH,IAAA,GAAOlB,aAAA,CAAcoD,SAAA,KAAc,SAAYpD,aAAA,CAAcoD,SAAA,GAAY;MAChFgF,MAAA,CAAOC,SAAA,GAAY;IACpB;IAEDJ,WAAWzE,IAAA,EAAMsF,eAAA,EAAiB;MAChC,KAAKtF,IAAA,GAAOA,IAAA;MACZ,KAAKwB,sBAAA,GAAyB8D,eAAA;MAC9B,KAAK1D,UAAA,GAAa;MAGlB,KAAKsD,UAAA,CAAW,KAAKD,YAAA,CAAY,GAAI,KAAKzI,aAAa;MAGvD,SAAS+I,WAAA,GAAc,GAAGA,WAAA,GAAc,KAAK3D,UAAA,EAAY2D,WAAA,IAAe;QACtE,MAAMX,MAAA,GAAS,KAAK1E,OAAA,CAAQqF,WAAW;QACvC,KAAK1D,aAAA,GAAgB+C,MAAA;QAErB,KAAKjF,eAAA,CAAgBlC,OAAA,CAAQmH,MAAA,CAAOlH,IAAI;QAExCkH,MAAA,CAAOzE,kBAAA,GAAqBqF,SAAA,CAAUC,IAAA,CAAKb,MAAA,CAAO7F,SAAA,EAAW6F,MAAA,CAAO5F,SAAA,EAAW4F,MAAA,CAAO3F,qBAAqB;QAC3G2F,MAAA,CAAOtE,kBAAA,GAAqBkF,SAAA,CAAUC,IAAA,CAAKb,MAAA,CAAO5F,SAAA,EAAW4F,MAAA,CAAO7F,SAAA,EAAW,IAAI6F,MAAA,CAAO1F,mBAAmB;QAE7G,MAAMwG,OAAA,GAAU,KAAK/F,eAAA,CAAgBvC,MAAA;QACrCwH,MAAA,CAAOe,OAAA,CAAQC,GAAA,CAAIF,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEG,cAAA,CAAe,GAAI;QACvEjB,MAAA,CAAOkB,OAAA,CAAQF,GAAA,CAAIF,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEG,cAAA,CAAe,GAAI;QAEvE,KAAKtE,YAAA,IAAgBvB,IAAA,GAAO4E,MAAA,CAAO7F,SAAA,KAAc6F,MAAA,CAAO5F,SAAA,GAAY4F,MAAA,CAAO7F,SAAA;QAE3E,KAAK+C,mBAAA,GAAsB;QAC3B,KAAKC,gBAAA,GAAmB;QAExB,MAAM4C,OAAA,GAAU,KAAKoB,aAAA,CAAe;QACpCpB,OAAA,CAAQqB,SAAA,GAAY;QACpBrB,OAAA,CAAQQ,IAAA,CAAKC,IAAA,CAAKR,MAAA,CAAOO,IAAI;QAC7BR,OAAA,CAAQU,IAAA,CAAKD,IAAA,CAAKR,MAAA,CAAOS,IAAI;QAC7BV,OAAA,CAAQgB,OAAA,CAAQP,IAAA,CAAKR,MAAA,CAAOe,OAAO;QACnChB,OAAA,CAAQmB,OAAA,CAAQV,IAAA,CAAKR,MAAA,CAAOkB,OAAO;QACnCnB,OAAA,CAAQpG,GAAA,CAAI6G,IAAA,CAAKR,MAAA,CAAOrG,GAAG;QAC3BoG,OAAA,CAAQnG,GAAA,CAAI4G,IAAA,CAAKR,MAAA,CAAOpG,GAAG;QAC3BmG,OAAA,CAAQlG,OAAA,GAAUmG,MAAA,CAAOnG,OAAA;QACzBkG,OAAA,CAAQjG,OAAA,GAAUkG,MAAA,CAAOlG,OAAA;QACzBiG,OAAA,CAAQI,SAAA,GAAY;QACpBJ,OAAA,CAAQK,SAAA,GAAY;QACpBL,OAAA,CAAQsB,uBAAA,GAA0B,IAAIrB,MAAA,CAAOtG,YAAA;QAE7C,KAAK0D,iBAAA,GACF,KAAKzC,YAAA,GAAepC,IAAA,CAAK8D,GAAA,CAAI,KAAKxB,oBAAA,EAAsBmF,MAAA,CAAOC,SAAS,KAAM,KAAKD,MAAA,CAAOvF,aAAA;QAE7F,KAAK6G,mBAAA,CAAoBvB,OAAO;MACjC;MAED,KAAKnD,sBAAA,GAAyB;MAC9B,KAAKK,aAAA,GAAgB;IACtB;IAEDqE,oBAAoBvB,OAAA,EAAS;MAE3B,IAAIA,OAAA,CAAQqB,SAAA,IAAa,KAAKnE,aAAA,CAAcxC,aAAA,EAAe;QACzD,KAAKmC,sBAAA,CAAuBmD,OAAO;QAEnC;MACD;MAGD,KAAK7B,QAAA,CAASqD,UAAA,CAAWxB,OAAA,CAAQU,IAAA,EAAMV,OAAA,CAAQQ,IAAI;MACnD,IAAIiB,SAAA,GAAY,KAAKtD,QAAA,CAASuD,MAAA,CAAQ;MAEtC,IAAID,SAAA,GAAY,MAAU;QACxB,KAAKtD,QAAA,CAAS8C,GAAA,CAAI,GAAG,GAAG,IAAI;QAC5BQ,SAAA,GAAY,KAAKtD,QAAA,CAASuD,MAAA,CAAQ;MACnC;MAED,MAAMC,YAAA,IAAgB3B,OAAA,CAAQlG,OAAA,GAAUkG,OAAA,CAAQjG,OAAA,IAAW;MAC3D,MAAM6H,cAAA,IAAkB5B,OAAA,CAAQI,SAAA,GAAYJ,OAAA,CAAQK,SAAA,IAAa;MAEjE,MAAMwB,aAAA,GAAgB,KAAKxG,IAAA,GAAO,KAAK6B,aAAA,CAAczD,SAAA,GAAYjB,IAAA,CAAK8D,GAAA,CAAI,GAAG0D,OAAA,CAAQqB,SAAS;MAE9F,KAAK9C,SAAA,CAAUuD,WAAA,CAAY9B,OAAA,CAAQQ,IAAA,EAAMR,OAAA,CAAQU,IAAA,EAAM,GAAG;MAC1D,KAAKlC,YAAA,CAAasD,WAAA,CAAY9B,OAAA,CAAQgB,OAAA,EAAShB,OAAA,CAAQmB,OAAA,EAAS,GAAG;MACnE,MAAMY,CAAA,GAAI,KAAKvD,YAAA;MAGf,KAAKC,MAAA,CAAOwC,GAAA,CACV,KAAKlD,QAAA,CAASiE,OAAA,CAAQD,CAAA,CAAEE,CAAA,EAAGF,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAA,EAAGN,aAAa,GAClD,KAAK5D,QAAA,CAAS+D,OAAA,CAAQD,CAAA,CAAEE,CAAA,EAAGF,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAA,EAAGN,aAAa,GAClD,KAAK3D,QAAA,CAAS8D,OAAA,CAAQD,CAAA,CAAEE,CAAA,EAAGF,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAA,EAAGN,aAAa,CACnD;MAED,KAAKpD,MAAA,CAAOyC,cAAA,CAAelB,OAAA,CAAQsB,uBAAA,GAA0BG,SAAS;MACtE,KAAKhD,MAAA,CAAO2D,GAAA,CAAI,KAAK7D,SAAS;MAI9B,MAAM8D,WAAA,GAAc,KAAKjB,aAAA,CAAe;MACxCiB,WAAA,CAAY7B,IAAA,CAAKC,IAAA,CAAKT,OAAA,CAAQQ,IAAI;MAClC6B,WAAA,CAAY3B,IAAA,CAAKD,IAAA,CAAK,KAAKhC,MAAM;MACjC4D,WAAA,CAAYrB,OAAA,CAAQP,IAAA,CAAKT,OAAA,CAAQgB,OAAO;MACxCqB,WAAA,CAAYlB,OAAA,CAAQV,IAAA,CAAK,KAAKjC,YAAY;MAC1C6D,WAAA,CAAYzI,GAAA,CAAI6G,IAAA,CAAKT,OAAA,CAAQpG,GAAG;MAChCyI,WAAA,CAAYxI,GAAA,CAAI4G,IAAA,CAAKT,OAAA,CAAQnG,GAAG;MAChCwI,WAAA,CAAYvI,OAAA,GAAUkG,OAAA,CAAQlG,OAAA;MAC9BuI,WAAA,CAAYtI,OAAA,GAAU4H,YAAA;MACtBU,WAAA,CAAYjC,SAAA,GAAYJ,OAAA,CAAQI,SAAA;MAChCiC,WAAA,CAAYhC,SAAA,GAAYuB,cAAA;MACxBS,WAAA,CAAYf,uBAAA,GAA0BtB,OAAA,CAAQsB,uBAAA,GAA0B,KAAKpE,aAAA,CAAcxD,SAAA;MAC3F2I,WAAA,CAAYhB,SAAA,GAAYrB,OAAA,CAAQqB,SAAA,GAAY;MAE5C,MAAMiB,WAAA,GAAc,KAAKlB,aAAA,CAAe;MACxCkB,WAAA,CAAY9B,IAAA,CAAKC,IAAA,CAAK,KAAKhC,MAAM;MACjC6D,WAAA,CAAY5B,IAAA,CAAKD,IAAA,CAAKT,OAAA,CAAQU,IAAI;MAClC4B,WAAA,CAAYtB,OAAA,CAAQP,IAAA,CAAK,KAAKjC,YAAY;MAC1C8D,WAAA,CAAYnB,OAAA,CAAQV,IAAA,CAAKT,OAAA,CAAQmB,OAAO;MACxC,KAAKxC,MAAA,CAAO4D,YAAA,CAAavC,OAAA,CAAQpG,GAAA,EAAK,KAAKuE,QAAA,CAASqE,SAAA,EAAW;MAC/DF,WAAA,CAAY1I,GAAA,CAAI2I,YAAA,CAAa,KAAKpE,QAAA,EAAU,KAAKQ,MAAM,EAAE6D,SAAA,CAAW;MACpEF,WAAA,CAAYzI,GAAA,CAAI4G,IAAA,CAAKT,OAAA,CAAQnG,GAAG;MAChCyI,WAAA,CAAYxI,OAAA,GAAU6H,YAAA;MACtBW,WAAA,CAAYvI,OAAA,GAAUiG,OAAA,CAAQjG,OAAA;MAC9BuI,WAAA,CAAYlC,SAAA,GAAYwB,cAAA;MACxBU,WAAA,CAAYjC,SAAA,GAAYL,OAAA,CAAQK,SAAA;MAChCiC,WAAA,CAAYhB,uBAAA,GAA0BtB,OAAA,CAAQsB,uBAAA,GAA0B,KAAKpE,aAAA,CAAcxD,SAAA;MAC3F4I,WAAA,CAAYjB,SAAA,GAAYrB,OAAA,CAAQqB,SAAA,GAAY;MAE5C,KAAKE,mBAAA,CAAoBc,WAAW;MAEpC,KAAKd,mBAAA,CAAoBe,WAAW;IACrC;IAEDnC,YAAYH,OAAA,EAAS;MAGnB,KAAK5B,YAAA,CAAaoD,UAAA,CAAWxB,OAAA,CAAQU,IAAA,EAAMV,OAAA,CAAQQ,IAAI,EAAEgC,SAAA,CAAW;MAEpE,IAAI,KAAKpF,gBAAA,EAAkB;QACzB,KAAKN,6BAAA,CAA8BkD,OAAA,CAAQQ,IAAA,EAAMR,OAAA,CAAQpG,GAAA,EAAK,KAAKwE,YAAA,EAAc4B,OAAA,CAAQlG,OAAA,EAAS,CAAC;QAEnG,KAAKsD,gBAAA,GAAmB;MACzB;MAED,KAAKN,6BAAA,CACHkD,OAAA,CAAQU,IAAA,EACRV,OAAA,CAAQpG,GAAA,EACR,KAAKwE,YAAA,EACL4B,OAAA,CAAQjG,OAAA,EACRiG,OAAA,CAAQK,SACT;MAED,KAAKoC,gBAAA,CAAkB;IACxB;IAEDzF,iCAAiC0F,GAAA,EAAKC,EAAA,EAAIxE,QAAA,EAAUyE,MAAA,EAAQ;MAG1D,KAAKvE,IAAA,CAAKkE,YAAA,CAAaI,EAAA,EAAIxE,QAAQ,EAAE+C,cAAA,CAAe0B,MAAA,GAASlL,gBAAA,CAAgBmL,QAAQ;MACrF,KAAKvE,IAAA,CAAKmC,IAAA,CAAKkC,EAAE,EAAEzB,cAAA,CAAe,CAAC0B,MAAA,GAASlL,gBAAA,CAAgBoL,QAAQ;MAEpE,MAAMf,CAAA,GAAI,KAAKrD,IAAA;MACf,MAAMtF,CAAA,GAAI,KAAKsE,QAAA;MAEfqE,CAAA,CAAEtB,IAAA,CAAKiC,GAAG,EAAEK,GAAA,CAAI,KAAK1E,IAAI,EAAE+D,GAAA,CAAI,KAAK9D,IAAI;MAExClF,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhCJ,CAAA,CAAEtB,IAAA,CAAKiC,GAAG,EAAEN,GAAA,CAAI,KAAK/D,IAAI,EAAE+D,GAAA,CAAI,KAAK9D,IAAI;MAExClF,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhCJ,CAAA,CAAEtB,IAAA,CAAKkC,EAAE,EAAEzB,cAAA,CAAe0B,MAAM,EAAER,GAAA,CAAIM,GAAG;MAEzCtJ,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhC,KAAK7E,aAAA,IAAiB;IACvB;IAEDP,8BAA8B2F,GAAA,EAAKC,EAAA,EAAIxE,QAAA,EAAUyE,MAAA,EAAQI,CAAA,EAAG;MAG1D,KAAK3E,IAAA,CAAKkE,YAAA,CAAaI,EAAA,EAAIxE,QAAQ,EAAE+C,cAAA,CAAe0B,MAAA,GAASlL,gBAAA,CAAgBmL,QAAQ;MACrF,KAAKvE,IAAA,CAAKmC,IAAA,CAAKkC,EAAE,EAAEzB,cAAA,CAAe,CAAC0B,MAAA,GAASlL,gBAAA,CAAgBoL,QAAQ;MAEpE,MAAMf,CAAA,GAAI,KAAKrD,IAAA;MACf,MAAMtF,CAAA,GAAI,KAAKsE,QAAA;MACf,MAAMuF,EAAA,GAAK,KAAKtF,GAAA;MAEhBoE,CAAA,CAAEtB,IAAA,CAAKiC,GAAG,EAAEK,GAAA,CAAI,KAAK1E,IAAI,EAAE+D,GAAA,CAAI,KAAK9D,IAAI;MAExClF,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhCc,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAIuF,CAAA;MACjCC,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAI;MAEjCsE,CAAA,CAAEtB,IAAA,CAAKiC,GAAG,EAAEN,GAAA,CAAI,KAAK/D,IAAI,EAAE+D,GAAA,CAAI,KAAK9D,IAAI;MAExClF,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhCc,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAIuF,CAAA;MACjCC,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAI;MAEjCsE,CAAA,CAAEtB,IAAA,CAAKkC,EAAE,EAAEzB,cAAA,CAAe0B,MAAM,EAAER,GAAA,CAAIM,GAAG;MAEzCtJ,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEE,CAAA;MAChC7I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEG,CAAA;MAChC9I,CAAA,CAAE,KAAKoE,iBAAA,EAAmB,IAAIuE,CAAA,CAAEI,CAAA;MAEhCc,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAIuF,CAAA;MACjCC,EAAA,CAAG,KAAKxF,mBAAA,EAAqB,IAAI;MAEjC,KAAKH,aAAA,IAAiB;IACvB;IAEDmF,iBAAiBS,MAAA,EAAoB;MACnC,MAAMtF,OAAA,GAAU,KAAKA,OAAA;MACrBsF,MAAA,GAAS,KAAK5F,aAAA,GAAgB;MAE9BM,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;MACxCtF,OAAA,CAAQ,KAAKL,YAAA,EAAc,IAAI2F,MAAA,GAAS;IACzC;IAEDhH,qCAAA,EAAuC;MACrC,MAAM6E,OAAA,GAAU,KAAK/F,eAAA,CAAgBvC,MAAA;MAErC,KAAKyC,sBAAA,GAAyB,UAAU8E,OAAA,EAASmD,eAAA,EAAiB;QAGhE,MAAMlD,MAAA,GAASkD,eAAA,CAAgBjG,aAAA;QAE/B,MAAMkG,MAAA,GAASD,eAAA,CAAgBtL,aAAA,CAAc2C,YAAA;QAC7C,MAAM6I,SAAA,GAAYF,eAAA,CAAgBtL,aAAA,CAAc4C,eAAA;QAEhD,MAAM6I,MAAA,GACJH,eAAA,CAAgBtL,aAAA,CAAcsC,SAAA,IAAa8F,MAAA,CAAOC,SAAA,IAAa,IAC3D,CAACa,OAAA,CAAS,IAAGqC,MAAA,GACbvC,SAAA,CAAUC,IAAA,CAAKb,MAAA,CAAO7F,SAAA,EAAW6F,MAAA,CAAOzE,kBAAA,EAAoBwE,OAAA,CAAQI,SAAS,IAAIW,OAAA,CAAO,IAAKqC,MAAA;QAEnG,MAAMG,KAAA,GAAQJ,eAAA,CAAgB9H,IAAA,GAAOiI,MAAA;QACrC,MAAME,YAAA,GAAehL,IAAA,CAAKQ,KAAA,CAAMuK,KAAA,GAAQH,MAAM;QAE9C,MAAMK,eAAA,GAAkB1C,OAAA,MAAayC,YAAA,GAAe;QAEpD,MAAME,QAAA,GAAWH,KAAA,GAAQH,MAAA,IAAUC,SAAA,GAAYD,MAAA;QAE/C,IAAIO,WAAA,GAAc;QAElB,IAAID,QAAA,EAAU;UACZC,WAAA,GAAcR,eAAA,CAAgB9F,iBAAA;QAE/B;QAED,IACE4C,MAAA,CAAOC,SAAA,GAAYiD,eAAA,CAAgBtI,kBAAA,IACnCsI,eAAA,CAAgBlG,UAAA,GAAakG,eAAA,CAAgB/G,UAAA,IAC7C2E,OAAA,CAAS,IAAG4C,WAAA,EACZ;UACA,MAAMC,WAAA,GAAcT,eAAA,CAAgB7C,YAAA,CAAc;UAElD,MAAMuD,UAAA,GAAaV,eAAA,CAAgBnI,eAAA,CAAgBnC,OAAA,CAAS;UAC5D+K,WAAA,CAAY7K,IAAA,GAAO0K,eAAA;UACnBN,eAAA,CAAgBnI,eAAA,CAAgBlC,OAAA,CAAQ2K,eAAe;UAEvDG,WAAA,CAAY1D,SAAA,GAAYD,MAAA,CAAOC,SAAA,GAAY;UAC3C0D,WAAA,CAAYlJ,aAAA,GAAgBlC,IAAA,CAAK+D,GAAA,CAAI,GAAG0D,MAAA,CAAOvF,aAAA,GAAgB,CAAC;UAEhEkJ,WAAA,CAAY5C,OAAA,CAAQC,GAAA,CAAIF,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEG,cAAA,CAAe,GAAI;UAC5E0C,WAAA,CAAYzC,OAAA,CAAQF,GAAA,CAAIF,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEA,OAAA,CAAS,GAAEG,cAAA,CAAe,GAAI;UAC5E0C,WAAA,CAAYhK,GAAA,CAAI6G,IAAA,CAAKR,MAAA,CAAOrG,GAAG;UAC/BgK,WAAA,CAAY/J,GAAA,CAAI4G,IAAA,CAAKR,MAAA,CAAOpG,GAAG;UAC/B+J,WAAA,CAAY9J,OAAA,GAAUkG,OAAA,CAAQlG,OAAA,GAAUqJ,eAAA,CAAgBtL,aAAA,CAAcmC,aAAA;UACtE4J,WAAA,CAAY7J,OAAA,GAAUvB,IAAA,CAAKsL,GAAA,CACzBX,eAAA,CAAgBtL,aAAA,CAAcqC,SAAA,EAC9B8F,OAAA,CAAQjG,OAAA,GAAUoJ,eAAA,CAAgBtL,aAAA,CAAcoC,aACjD;UAED2J,WAAA,CAAYxJ,SAAA,GAAYkJ,MAAA,GAASE,YAAA,GAAeJ,MAAA;UAChDQ,WAAA,CAAYvJ,SAAA,GAAYuJ,WAAA,CAAYxJ,SAAA,GAAYgJ,MAAA,GAASC,SAAA;UAEzD,IAAI,CAACF,eAAA,CAAgBtL,aAAA,CAAcsC,SAAA,IAAa8F,MAAA,CAAOC,SAAA,IAAa,GAAG;YACrE0D,WAAA,CAAYxJ,SAAA,GAAY5B,IAAA,CAAK+D,GAAA,CAAIqH,WAAA,CAAYxJ,SAAA,EAAW6F,MAAA,CAAO7F,SAAS;YACxEwJ,WAAA,CAAYvJ,SAAA,GAAY7B,IAAA,CAAKsL,GAAA,CAAIF,WAAA,CAAYvJ,SAAA,EAAW4F,MAAA,CAAO5F,SAAS;UACzE;UAEDuJ,WAAA,CAAYnK,SAAA,GAAYwG,MAAA,CAAOxG,SAAA,GAAY;UAC3CmK,WAAA,CAAYlK,SAAA,GAAYuG,MAAA,CAAOvG,SAAA;UAC/BkK,WAAA,CAAYjK,YAAA,GAAesG,MAAA,CAAOtG,YAAA;UAClCiK,WAAA,CAAYtJ,qBAAA,GAAwB2F,MAAA,CAAO3F,qBAAA;UAC3CsJ,WAAA,CAAYrJ,mBAAA,GAAsB0F,MAAA,CAAO1F,mBAAA;UAEzC4I,eAAA,CAAgBhI,gBAAA,CAAiB6E,OAAA,EAASC,MAAA,EAAQ2D,WAAA,EAAaT,eAAe;UAE9EA,eAAA,CAAgBnI,eAAA,CAAgBlC,OAAA,CAAQ+K,UAAU;QACnD;MACF;MAED,MAAME,OAAA,GAAU,IAAIxK,OAAA,CAAS;MAC7B,MAAMyK,WAAA,GAAc,IAAIzK,OAAA,CAAS;MACjC,MAAM0K,QAAA,GAAW,IAAI1K,OAAA,CAAS;MAC9B,MAAM2K,MAAA,GAAS,IAAI3K,OAAA,CAAS;MAE5B,KAAK4B,gBAAA,GAAmB,UAAU6E,OAAA,EAASmE,YAAA,EAAcP,WAAA,EAAaT,eAAA,EAAiB;QAIrFA,eAAA,CAAgBiB,sBAAA,CAAuBpE,OAAA,EAASmE,YAAA,EAAcP,WAAA,EAAa,KAAK,KAAK,GAAG;MACzF;MAED,KAAKS,kBAAA,GAAqB,UACxBrE,OAAA,EACAmE,YAAA,EACAP,WAAA,EACAU,YAAA,EACAC,eAAA,EACAC,kBAAA,EACA;QAGAZ,WAAA,CAAYpD,IAAA,CAAKC,IAAA,CAAKT,OAAA,CAAQQ,IAAI;QAElCuD,OAAA,CAAQvC,UAAA,CAAW2C,YAAA,CAAazD,IAAA,EAAMyD,YAAA,CAAa3D,IAAI;QACvDwD,WAAA,CAAYvD,IAAA,CAAKsD,OAAO,EAAEvB,SAAA,CAAW;QACrCuB,OAAA,CAAQ7C,cAAA,CAAelB,OAAA,CAAQI,SAAA,IAAa,IAAIJ,OAAA,CAAQI,SAAA,KAAcW,OAAA,KAAYuD,YAAA,CAAa;QAC/F,MAAM5C,MAAA,GAASqC,OAAA,CAAQrC,MAAA,CAAQ;QAC/BuC,QAAA,CAAS1B,YAAA,CAAa4B,YAAA,CAAavK,GAAA,EAAKoK,WAAW;QACnD,MAAMS,KAAA,GAAQ,IAAIjM,IAAA,CAAKkM,EAAA,GAAK3D,OAAA,CAAS;QACrCkD,QAAA,CAAS/C,cAAA,CAAe1I,IAAA,CAAKmM,GAAA,CAAIF,KAAK,CAAC;QACvCP,MAAA,CAAOzD,IAAA,CAAK0D,YAAA,CAAavK,GAAG,EAAEsH,cAAA,CAAe1I,IAAA,CAAKoM,GAAA,CAAIH,KAAK,CAAC;QAE5Db,WAAA,CAAYlD,IAAA,CACTD,IAAA,CAAKwD,QAAQ,EACb7B,GAAA,CAAI8B,MAAM,EACVhD,cAAA,CAAeQ,MAAA,GAAS6C,eAAA,IAAmBC,kBAAA,GAAqBzD,OAAA,CAAO,KAAM,IAAIyD,kBAAA,EAAoB,EACrGpC,GAAA,CAAI2B,OAAO,EACX3B,GAAA,CAAI+B,YAAA,CAAa3D,IAAI;MACzB;MAED,KAAK4D,sBAAA,GAAyB,UAC5BpE,OAAA,EACAmE,YAAA,EACAP,WAAA,EACAU,YAAA,EACAC,eAAA,EACAC,kBAAA,EACA;QAGAZ,WAAA,CAAYpD,IAAA,CAAKC,IAAA,CAAKT,OAAA,CAAQQ,IAAI;QAElCuD,OAAA,CAAQvC,UAAA,CAAW2C,YAAA,CAAazD,IAAA,EAAMyD,YAAA,CAAa3D,IAAI;QACvDwD,WAAA,CAAYvD,IAAA,CAAKsD,OAAO,EAAEvB,SAAA,CAAW;QACrCuB,OAAA,CAAQ7C,cAAA,CAAelB,OAAA,CAAQI,SAAA,IAAa,IAAIJ,OAAA,CAAQI,SAAA,MAAe,IAAIW,OAAA,CAAS,IAAG,KAAKuD,YAAA,CAAa;QACzG,MAAM5C,MAAA,GAASqC,OAAA,CAAQrC,MAAA,CAAQ;QAC/BuC,QAAA,CAAS1B,YAAA,CAAa4B,YAAA,CAAavK,GAAA,EAAKoK,WAAW;QACnD,MAAMS,KAAA,GAAQ,IAAIjM,IAAA,CAAKkM,EAAA,GAAK3D,OAAA,CAAS;QACrCkD,QAAA,CAAS/C,cAAA,CAAe1I,IAAA,CAAKmM,GAAA,CAAIF,KAAK,CAAC;QACvCP,MAAA,CAAOzD,IAAA,CAAK0D,YAAA,CAAavK,GAAG,EAAEsH,cAAA,CAAe1I,IAAA,CAAKoM,GAAA,CAAIH,KAAK,CAAC;QAE5Db,WAAA,CAAYlD,IAAA,CACTD,IAAA,CAAKwD,QAAQ,EACb7B,GAAA,CAAI8B,MAAM,EACVhD,cAAA,CAAeQ,MAAA,GAAS6C,eAAA,IAAmBC,kBAAA,GAAqBzD,OAAA,CAAO,KAAM,IAAIyD,kBAAA,EAAoB,EACrGpC,GAAA,CAAI2B,OAAO,EACX3B,GAAA,CAAI+B,YAAA,CAAa3D,IAAI;MACzB;IACF;IAED/D,aAAA,EAAe;MACb,OAAO;QACL1D,IAAA,EAAM;QACN2B,aAAA,EAAe;QACfwF,SAAA,EAAW;QACXM,IAAA,EAAM,IAAIjH,OAAA,CAAS;QACnBmH,IAAA,EAAM,IAAInH,OAAA,CAAS;QACnByH,OAAA,EAAS,IAAIzH,OAAA,CAAS;QACtB4H,OAAA,EAAS,IAAI5H,OAAA,CAAS;QACtBK,GAAA,EAAK,IAAIL,OAAA,CAAS;QAClBM,GAAA,EAAK,IAAIN,OAAA,CAAS;QAClBO,OAAA,EAAS;QACTC,OAAA,EAAS;QACTK,SAAA,EAAW;QACXC,SAAA,EAAW;QACXZ,SAAA,EAAW;QACXC,SAAA,EAAW;QACXC,YAAA,EAAc;QACdW,qBAAA,EAAuB;QACvBC,mBAAA,EAAqB;QACrBiB,kBAAA,EAAoB;QACpBG,kBAAA,EAAoB;MACrB;IACF;IAEDgB,cAAA,EAAgB;MACd,OAAO;QACL0E,SAAA,EAAW;QACXb,IAAA,EAAM,IAAIjH,OAAA,CAAS;QACnBmH,IAAA,EAAM,IAAInH,OAAA,CAAS;QACnByH,OAAA,EAAS,IAAIzH,OAAA,CAAS;QACtB4H,OAAA,EAAS,IAAI5H,OAAA,CAAS;QACtBK,GAAA,EAAK,IAAIL,OAAA,CAAS;QAClBM,GAAA,EAAK,IAAIN,OAAA,CAAS;QAClBO,OAAA,EAAS;QACTC,OAAA,EAAS;QACTqG,SAAA,EAAW;QACXC,SAAA,EAAW;QACXiB,uBAAA,EAAyB;MAC1B;IACF;IAEDF,cAAA,EAAgB;MACd,OAAO,KAAK1E,WAAA,CAAY,KAAKS,mBAAA,EAAqB;IACnD;IAEDsD,KAAKvH,MAAA,EAAQ;MACX,MAAMuH,IAAA,CAAKvH,MAAM;MAEjB,KAAKlB,IAAA,CAAKN,gBAAA,CAAgBO,cAAA,CAAe,IAAIiB,MAAA,CAAOrB,aAAa,CAAC;MAElE,OAAO;IACR;IAEDwB,MAAA,EAAQ;MACN,OAAO,IAAI,KAAKzB,WAAA,CAAYF,gBAAA,CAAgBO,cAAA,CAAe,IAAI,KAAKJ,aAAa,CAAC;IACnF;EACF;EA3yBD,IAAMgN,gBAAA,GAANnN,gBAAA;EAEE;EAAAoN,aAAA,CAFID,gBAAA,EAEG,mBAAkB;EACzBC,aAAA,CAHID,gBAAA,EAGG,cAAa;EACpBC,aAAA,CAJID,gBAAA,EAIG,mBAAkB;EACzBC,aAAA,CALID,gBAAA,EAKG,cAAa;EACpBC,aAAA,CANID,gBAAA,EAMG,iBAAgB;EACvBC,aAAA,CAPID,gBAAA,EAOG,oBAAmB;EAE1BC,aAAA,CATID,gBAAA,EASG,YAAWrM,IAAA,CAAKmM,GAAA,CAAK,KAAKnM,IAAA,CAAKkM,EAAA,GAAM,GAAG;EAC/CI,aAAA,CAVID,gBAAA,EAUG,YAAWrM,IAAA,CAAKoM,GAAA,CAAK,KAAKpM,IAAA,CAAKkM,EAAA,GAAM,GAAG;EAmyBjD,OAAOG,gBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}