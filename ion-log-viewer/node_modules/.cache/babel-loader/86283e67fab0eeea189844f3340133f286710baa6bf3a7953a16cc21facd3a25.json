{"ast": null, "code": "import { FLOAT32_EPSILON } from '../Constants.js';\nimport { getTriCount } from './geometryUtils.js';\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in \"target\".\n// A bounding box is computed for the centroids of the triangles, as well, and placed in \"centroidTarget\".\n// These are computed together to avoid redundant accesses to bounds array.\nexport function getBounds(triangleBounds, offset, count, target, centroidTarget) {\n  let minx = Infinity;\n  let miny = Infinity;\n  let minz = Infinity;\n  let maxx = -Infinity;\n  let maxy = -Infinity;\n  let maxz = -Infinity;\n  let cminx = Infinity;\n  let cminy = Infinity;\n  let cminz = Infinity;\n  let cmaxx = -Infinity;\n  let cmaxy = -Infinity;\n  let cmaxz = -Infinity;\n  for (let i = offset * 6, end = (offset + count) * 6; i < end; i += 6) {\n    const cx = triangleBounds[i + 0];\n    const hx = triangleBounds[i + 1];\n    const lx = cx - hx;\n    const rx = cx + hx;\n    if (lx < minx) minx = lx;\n    if (rx > maxx) maxx = rx;\n    if (cx < cminx) cminx = cx;\n    if (cx > cmaxx) cmaxx = cx;\n    const cy = triangleBounds[i + 2];\n    const hy = triangleBounds[i + 3];\n    const ly = cy - hy;\n    const ry = cy + hy;\n    if (ly < miny) miny = ly;\n    if (ry > maxy) maxy = ry;\n    if (cy < cminy) cminy = cy;\n    if (cy > cmaxy) cmaxy = cy;\n    const cz = triangleBounds[i + 4];\n    const hz = triangleBounds[i + 5];\n    const lz = cz - hz;\n    const rz = cz + hz;\n    if (lz < minz) minz = lz;\n    if (rz > maxz) maxz = rz;\n    if (cz < cminz) cminz = cz;\n    if (cz > cmaxz) cmaxz = cz;\n  }\n  target[0] = minx;\n  target[1] = miny;\n  target[2] = minz;\n  target[3] = maxx;\n  target[4] = maxy;\n  target[5] = maxz;\n  centroidTarget[0] = cminx;\n  centroidTarget[1] = cminy;\n  centroidTarget[2] = cminz;\n  centroidTarget[3] = cmaxx;\n  centroidTarget[4] = cmaxy;\n  centroidTarget[5] = cmaxz;\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nexport function computeTriangleBounds(geo, target = null, offset = null, count = null) {\n  const posAttr = geo.attributes.position;\n  const index = geo.index ? geo.index.array : null;\n  const triCount = getTriCount(geo);\n  const normalized = posAttr.normalized;\n  let triangleBounds;\n  if (target === null) {\n    triangleBounds = new Float32Array(triCount * 6);\n    offset = 0;\n    count = triCount;\n  } else {\n    triangleBounds = target;\n    offset = offset || 0;\n    count = count || triCount;\n  }\n\n  // used for non-normalized positions\n  const posArr = posAttr.array;\n\n  // support for an interleaved position buffer\n  const bufferOffset = posAttr.offset || 0;\n  let stride = 3;\n  if (posAttr.isInterleavedBufferAttribute) {\n    stride = posAttr.data.stride;\n  }\n\n  // used for normalized positions\n  const getters = ['getX', 'getY', 'getZ'];\n  for (let tri = offset; tri < offset + count; tri++) {\n    const tri3 = tri * 3;\n    const tri6 = tri * 6;\n    let ai = tri3 + 0;\n    let bi = tri3 + 1;\n    let ci = tri3 + 2;\n    if (index) {\n      ai = index[ai];\n      bi = index[bi];\n      ci = index[ci];\n    }\n\n    // we add the stride and offset here since we access the array directly\n    // below for the sake of performance\n    if (!normalized) {\n      ai = ai * stride + bufferOffset;\n      bi = bi * stride + bufferOffset;\n      ci = ci * stride + bufferOffset;\n    }\n    for (let el = 0; el < 3; el++) {\n      let a, b, c;\n      if (normalized) {\n        a = posAttr[getters[el]](ai);\n        b = posAttr[getters[el]](bi);\n        c = posAttr[getters[el]](ci);\n      } else {\n        a = posArr[ai + el];\n        b = posArr[bi + el];\n        c = posArr[ci + el];\n      }\n      let min = a;\n      if (b < min) min = b;\n      if (c < min) min = c;\n      let max = a;\n      if (b > max) max = b;\n      if (c > max) max = c;\n\n      // Increase the bounds size by float32 epsilon to avoid precision errors when\n      // converting to 32 bit float. Scale the epsilon by the size of the numbers being\n      // worked with.\n      const halfExtents = (max - min) / 2;\n      const el2 = el * 2;\n      triangleBounds[tri6 + el2 + 0] = min + halfExtents;\n      triangleBounds[tri6 + el2 + 1] = halfExtents + (Math.abs(min) + halfExtents) * FLOAT32_EPSILON;\n    }\n  }\n  return triangleBounds;\n}", "map": {"version": 3, "names": ["FLOAT32_EPSILON", "getTriCount", "getBounds", "triangleBounds", "offset", "count", "target", "centroid<PERSON>arget", "minx", "Infinity", "miny", "minz", "maxx", "maxy", "maxz", "cminx", "cminy", "cminz", "cmaxx", "cmaxy", "cmaxz", "i", "end", "cx", "hx", "lx", "rx", "cy", "hy", "ly", "ry", "cz", "hz", "lz", "rz", "computeTriangleBounds", "geo", "posAttr", "attributes", "position", "index", "array", "triCount", "normalized", "Float32Array", "posArr", "bufferOffset", "stride", "isInterleavedBufferAttribute", "data", "getters", "tri", "tri3", "tri6", "ai", "bi", "ci", "el", "a", "b", "c", "min", "max", "halfExtents", "el2", "Math", "abs"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/build/computeBoundsUtils.js"], "sourcesContent": ["import { FLOAT32_EPSILON } from '../Constants.js';\nimport { getTriCount } from './geometryUtils.js';\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in \"target\".\n// A bounding box is computed for the centroids of the triangles, as well, and placed in \"centroidTarget\".\n// These are computed together to avoid redundant accesses to bounds array.\nexport function getBounds( triangleBounds, offset, count, target, centroidTarget ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nexport function computeTriangleBounds( geo, target = null, offset = null, count = null ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index ? geo.index.array : null;\n\tconst triCount = getTriCount( geo );\n\tconst normalized = posAttr.normalized;\n\tlet triangleBounds;\n\tif ( target === null ) {\n\n\t\ttriangleBounds = new Float32Array( triCount * 6 );\n\t\toffset = 0;\n\t\tcount = triCount;\n\n\t} else {\n\n\t\ttriangleBounds = target;\n\t\toffset = offset || 0;\n\t\tcount = count || triCount;\n\n\t}\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = offset; tri < offset + count; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai = tri3 + 0;\n\t\tlet bi = tri3 + 1;\n\t\tlet ci = tri3 + 2;\n\n\t\tif ( index ) {\n\n\t\t\tai = index[ ai ];\n\t\t\tbi = index[ bi ];\n\t\t\tci = index[ ci ];\n\n\t\t}\n\n\t\t// we add the stride and offset here since we access the array directly\n\t\t// below for the sake of performance\n\t\tif ( ! normalized ) {\n\n\t\t\tai = ai * stride + bufferOffset;\n\t\t\tbi = bi * stride + bufferOffset;\n\t\t\tci = ci * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,WAAW,QAAQ,oBAAoB;;AAEhD;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,cAAc,EAAG;EAElF,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAGF,QAAQ;EACnB,IAAIG,IAAI,GAAG,CAAEH,QAAQ;EACrB,IAAII,IAAI,GAAG,CAAEJ,QAAQ;EACrB,IAAIK,IAAI,GAAG,CAAEL,QAAQ;EAErB,IAAIM,KAAK,GAAGN,QAAQ;EACpB,IAAIO,KAAK,GAAGP,QAAQ;EACpB,IAAIQ,KAAK,GAAGR,QAAQ;EACpB,IAAIS,KAAK,GAAG,CAAET,QAAQ;EACtB,IAAIU,KAAK,GAAG,CAAEV,QAAQ;EACtB,IAAIW,KAAK,GAAG,CAAEX,QAAQ;EAEtB,KAAM,IAAIY,CAAC,GAAGjB,MAAM,GAAG,CAAC,EAAEkB,GAAG,GAAG,CAAElB,MAAM,GAAGC,KAAK,IAAK,CAAC,EAAEgB,CAAC,GAAGC,GAAG,EAAED,CAAC,IAAI,CAAC,EAAG;IAEzE,MAAME,EAAE,GAAGpB,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMG,EAAE,GAAGrB,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMI,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGjB,IAAI,EAAGA,IAAI,GAAGiB,EAAE;IAC1B,IAAKC,EAAE,GAAGd,IAAI,EAAGA,IAAI,GAAGc,EAAE;IAC1B,IAAKH,EAAE,GAAGR,KAAK,EAAGA,KAAK,GAAGQ,EAAE;IAC5B,IAAKA,EAAE,GAAGL,KAAK,EAAGA,KAAK,GAAGK,EAAE;IAE5B,MAAMI,EAAE,GAAGxB,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMO,EAAE,GAAGzB,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMQ,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGnB,IAAI,EAAGA,IAAI,GAAGmB,EAAE;IAC1B,IAAKC,EAAE,GAAGjB,IAAI,EAAGA,IAAI,GAAGiB,EAAE;IAC1B,IAAKH,EAAE,GAAGX,KAAK,EAAGA,KAAK,GAAGW,EAAE;IAC5B,IAAKA,EAAE,GAAGR,KAAK,EAAGA,KAAK,GAAGQ,EAAE;IAE5B,MAAMI,EAAE,GAAG5B,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMW,EAAE,GAAG7B,cAAc,CAAEkB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMY,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGtB,IAAI,EAAGA,IAAI,GAAGsB,EAAE;IAC1B,IAAKC,EAAE,GAAGpB,IAAI,EAAGA,IAAI,GAAGoB,EAAE;IAC1B,IAAKH,EAAE,GAAGd,KAAK,EAAGA,KAAK,GAAGc,EAAE;IAC5B,IAAKA,EAAE,GAAGX,KAAK,EAAGA,KAAK,GAAGW,EAAE;EAE7B;EAEAzB,MAAM,CAAE,CAAC,CAAE,GAAGE,IAAI;EAClBF,MAAM,CAAE,CAAC,CAAE,GAAGI,IAAI;EAClBJ,MAAM,CAAE,CAAC,CAAE,GAAGK,IAAI;EAElBL,MAAM,CAAE,CAAC,CAAE,GAAGM,IAAI;EAClBN,MAAM,CAAE,CAAC,CAAE,GAAGO,IAAI;EAClBP,MAAM,CAAE,CAAC,CAAE,GAAGQ,IAAI;EAElBP,cAAc,CAAE,CAAC,CAAE,GAAGQ,KAAK;EAC3BR,cAAc,CAAE,CAAC,CAAE,GAAGS,KAAK;EAC3BT,cAAc,CAAE,CAAC,CAAE,GAAGU,KAAK;EAE3BV,cAAc,CAAE,CAAC,CAAE,GAAGW,KAAK;EAC3BX,cAAc,CAAE,CAAC,CAAE,GAAGY,KAAK;EAC3BZ,cAAc,CAAE,CAAC,CAAE,GAAGa,KAAK;AAE5B;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASe,qBAAqBA,CAAEC,GAAG,EAAE9B,MAAM,GAAG,IAAI,EAAEF,MAAM,GAAG,IAAI,EAAEC,KAAK,GAAG,IAAI,EAAG;EAExF,MAAMgC,OAAO,GAAGD,GAAG,CAACE,UAAU,CAACC,QAAQ;EACvC,MAAMC,KAAK,GAAGJ,GAAG,CAACI,KAAK,GAAGJ,GAAG,CAACI,KAAK,CAACC,KAAK,GAAG,IAAI;EAChD,MAAMC,QAAQ,GAAGzC,WAAW,CAAEmC,GAAI,CAAC;EACnC,MAAMO,UAAU,GAAGN,OAAO,CAACM,UAAU;EACrC,IAAIxC,cAAc;EAClB,IAAKG,MAAM,KAAK,IAAI,EAAG;IAEtBH,cAAc,GAAG,IAAIyC,YAAY,CAAEF,QAAQ,GAAG,CAAE,CAAC;IACjDtC,MAAM,GAAG,CAAC;IACVC,KAAK,GAAGqC,QAAQ;EAEjB,CAAC,MAAM;IAENvC,cAAc,GAAGG,MAAM;IACvBF,MAAM,GAAGA,MAAM,IAAI,CAAC;IACpBC,KAAK,GAAGA,KAAK,IAAIqC,QAAQ;EAE1B;;EAEA;EACA,MAAMG,MAAM,GAAGR,OAAO,CAACI,KAAK;;EAE5B;EACA,MAAMK,YAAY,GAAGT,OAAO,CAACjC,MAAM,IAAI,CAAC;EACxC,IAAI2C,MAAM,GAAG,CAAC;EACd,IAAKV,OAAO,CAACW,4BAA4B,EAAG;IAE3CD,MAAM,GAAGV,OAAO,CAACY,IAAI,CAACF,MAAM;EAE7B;;EAEA;EACA,MAAMG,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;EAE1C,KAAM,IAAIC,GAAG,GAAG/C,MAAM,EAAE+C,GAAG,GAAG/C,MAAM,GAAGC,KAAK,EAAE8C,GAAG,EAAG,EAAG;IAEtD,MAAMC,IAAI,GAAGD,GAAG,GAAG,CAAC;IACpB,MAAME,IAAI,GAAGF,GAAG,GAAG,CAAC;IAEpB,IAAIG,EAAE,GAAGF,IAAI,GAAG,CAAC;IACjB,IAAIG,EAAE,GAAGH,IAAI,GAAG,CAAC;IACjB,IAAII,EAAE,GAAGJ,IAAI,GAAG,CAAC;IAEjB,IAAKZ,KAAK,EAAG;MAEZc,EAAE,GAAGd,KAAK,CAAEc,EAAE,CAAE;MAChBC,EAAE,GAAGf,KAAK,CAAEe,EAAE,CAAE;MAChBC,EAAE,GAAGhB,KAAK,CAAEgB,EAAE,CAAE;IAEjB;;IAEA;IACA;IACA,IAAK,CAAEb,UAAU,EAAG;MAEnBW,EAAE,GAAGA,EAAE,GAAGP,MAAM,GAAGD,YAAY;MAC/BS,EAAE,GAAGA,EAAE,GAAGR,MAAM,GAAGD,YAAY;MAC/BU,EAAE,GAAGA,EAAE,GAAGT,MAAM,GAAGD,YAAY;IAEhC;IAEA,KAAM,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAG,EAAG;MAEjC,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC;MAEX,IAAKjB,UAAU,EAAG;QAEjBe,CAAC,GAAGrB,OAAO,CAAEa,OAAO,CAAEO,EAAE,CAAE,CAAE,CAAEH,EAAG,CAAC;QAClCK,CAAC,GAAGtB,OAAO,CAAEa,OAAO,CAAEO,EAAE,CAAE,CAAE,CAAEF,EAAG,CAAC;QAClCK,CAAC,GAAGvB,OAAO,CAAEa,OAAO,CAAEO,EAAE,CAAE,CAAE,CAAED,EAAG,CAAC;MAEnC,CAAC,MAAM;QAENE,CAAC,GAAGb,MAAM,CAAES,EAAE,GAAGG,EAAE,CAAE;QACrBE,CAAC,GAAGd,MAAM,CAAEU,EAAE,GAAGE,EAAE,CAAE;QACrBG,CAAC,GAAGf,MAAM,CAAEW,EAAE,GAAGC,EAAE,CAAE;MAEtB;MAEA,IAAII,GAAG,GAAGH,CAAC;MACX,IAAKC,CAAC,GAAGE,GAAG,EAAGA,GAAG,GAAGF,CAAC;MACtB,IAAKC,CAAC,GAAGC,GAAG,EAAGA,GAAG,GAAGD,CAAC;MAEtB,IAAIE,GAAG,GAAGJ,CAAC;MACX,IAAKC,CAAC,GAAGG,GAAG,EAAGA,GAAG,GAAGH,CAAC;MACtB,IAAKC,CAAC,GAAGE,GAAG,EAAGA,GAAG,GAAGF,CAAC;;MAEtB;MACA;MACA;MACA,MAAMG,WAAW,GAAG,CAAED,GAAG,GAAGD,GAAG,IAAK,CAAC;MACrC,MAAMG,GAAG,GAAGP,EAAE,GAAG,CAAC;MAClBtD,cAAc,CAAEkD,IAAI,GAAGW,GAAG,GAAG,CAAC,CAAE,GAAGH,GAAG,GAAGE,WAAW;MACpD5D,cAAc,CAAEkD,IAAI,GAAGW,GAAG,GAAG,CAAC,CAAE,GAAGD,WAAW,GAAG,CAAEE,IAAI,CAACC,GAAG,CAAEL,GAAI,CAAC,GAAGE,WAAW,IAAK/D,eAAe;IAErG;EAED;EAEA,OAAOG,cAAc;AAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}