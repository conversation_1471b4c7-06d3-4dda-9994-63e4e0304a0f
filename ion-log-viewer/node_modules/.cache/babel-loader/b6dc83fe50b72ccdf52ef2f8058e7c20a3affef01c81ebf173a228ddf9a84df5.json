{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}), 'NavigateNext');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "d"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@mui/material/esm/internal/svg-icons/NavigateNext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}), 'NavigateNext');"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}