{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { AbstractWriter } from \"./AbstractWriter\";\nimport { CharCodes, ClobEscapes, escape, isIdentifier, isOperator, is_keyword, StringEscapes, SymbolEscapes, toBase64 } from \"./IonText\";\nimport { IonTypes } from \"./IonTypes\";\nimport { encodeUtf8 } from \"./IonUnicode\";\nimport { _assertDefined } from \"./util\";\nexport var State;\n(function (State) {\n  State[State[\"VALUE\"] = 0] = \"VALUE\";\n  State[State[\"STRUCT_FIELD\"] = 1] = \"STRUCT_FIELD\";\n})(State || (State = {}));\nexport class Context {\n  constructor(myType) {\n    this.state = myType === IonTypes.STRUCT ? State.STRUCT_FIELD : State.VALUE;\n    this.clean = true;\n    this.containerType = myType;\n  }\n}\nexport class TextWriter extends AbstractWriter {\n  constructor(writeable) {\n    super();\n    this.writeable = writeable;\n    this._floatSerializer = value => {\n      TextWriter._serializeFloat(this, value);\n    };\n    this.containerContext = [new Context(null)];\n  }\n  get isTopLevel() {\n    return this.depth() === 0;\n  }\n  get currentContainer() {\n    return this.containerContext[this.depth()];\n  }\n  static _serializeFloat(writer, value) {\n    let text;\n    if (value === Number.POSITIVE_INFINITY) {\n      text = \"+inf\";\n    } else if (value === Number.NEGATIVE_INFINITY) {\n      text = \"-inf\";\n    } else if (Object.is(value, Number.NaN)) {\n      text = \"nan\";\n    } else if (Object.is(value, -0)) {\n      text = \"-0e0\";\n    } else {\n      text = value.toExponential();\n      const plusSignIndex = text.lastIndexOf(\"+\");\n      if (plusSignIndex > -1) {\n        text = text.slice(0, plusSignIndex) + text.slice(plusSignIndex + 1);\n      }\n    }\n    writer.writeUtf8(text);\n  }\n  getBytes() {\n    return this.writeable.getBytes();\n  }\n  writeBlob(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.BLOB, value, value => {\n      this.writeable.writeBytes(encodeUtf8(\"{{\" + toBase64(value) + \"}}\"));\n    });\n  }\n  writeBoolean(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.BOOL, value, value => {\n      this.writeUtf8(value ? \"true\" : \"false\");\n    });\n  }\n  writeClob(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.CLOB, value, value => {\n      let hexStr;\n      this.writeUtf8('{{\"');\n      for (let i = 0; i < value.length; i++) {\n        const c = value[i];\n        if (c > 127 && c < 256) {\n          hexStr = \"\\\\x\" + c.toString(16);\n          for (let j = 0; j < hexStr.length; j++) {\n            this.writeable.writeByte(hexStr.charCodeAt(j));\n          }\n        } else {\n          const escape = ClobEscapes[c];\n          if (escape === undefined) {\n            if (c < 32) {\n              hexStr = \"\\\\x\" + c.toString(16);\n              for (let j = 0; j < hexStr.length; j++) {\n                this.writeable.writeByte(hexStr.charCodeAt(j));\n              }\n            } else {\n              this.writeable.writeByte(c);\n            }\n          } else {\n            this.writeable.writeBytes(new Uint8Array(escape));\n          }\n        }\n      }\n      this.writeUtf8('\"}}');\n    });\n  }\n  writeDecimal(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.DECIMAL, value, value => {\n      let s = \"\";\n      let coefficient = value.getCoefficient();\n      if (coefficient < 0n) {\n        coefficient = -coefficient;\n      }\n      if (value.isNegative()) {\n        s += \"-\";\n      }\n      const exponent = value.getExponent();\n      const scale = -exponent;\n      if (exponent == 0) {\n        s += coefficient.toString() + \".\";\n      } else if (exponent < 0) {\n        const significantDigits = coefficient.toString().length;\n        const adjustedExponent = significantDigits - 1 - scale;\n        if (adjustedExponent >= 0) {\n          const wholeDigits = significantDigits - scale;\n          s += coefficient.toString().substring(0, wholeDigits);\n          s += \".\";\n          s += coefficient.toString().substring(wholeDigits, significantDigits);\n        } else if (adjustedExponent >= -6) {\n          s += \"0.\";\n          s += \"00000\".substring(0, scale - significantDigits);\n          s += coefficient.toString();\n        } else {\n          s += coefficient.toString();\n          s += \"d-\";\n          s += scale.toString();\n        }\n      } else {\n        s += coefficient.toString() + \"d\" + exponent;\n      }\n      this.writeUtf8(s);\n    });\n  }\n  _isInStruct() {\n    return this.currentContainer.containerType === IonTypes.STRUCT;\n  }\n  writeFieldName(fieldName) {\n    _assertDefined(fieldName);\n    if (this.currentContainer.containerType !== IonTypes.STRUCT) {\n      throw new Error(\"Cannot write field name outside of a struct\");\n    }\n    if (this.currentContainer.state !== State.STRUCT_FIELD) {\n      throw new Error(\"Expecting a struct value\");\n    }\n    if (!this.currentContainer.clean) {\n      this.writeable.writeByte(CharCodes.COMMA);\n    }\n    this.writeSymbolToken(fieldName);\n    this.writeable.writeByte(CharCodes.COLON);\n    this.currentContainer.state = State.VALUE;\n  }\n  writeFloat32(value) {\n    _assertDefined(value);\n    this._writeFloat(value);\n  }\n  writeFloat64(value) {\n    _assertDefined(value);\n    this._writeFloat(value);\n  }\n  writeInt(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.INT, value, value => {\n      this.writeUtf8(value.toString(10));\n    });\n  }\n  _writeNull(type) {\n    if (type === IonTypes.NULL) {\n      this.writeUtf8(\"null\");\n    } else {\n      this.writeUtf8(\"null.\" + type.name);\n    }\n  }\n  writeNull(type) {\n    if (type === undefined || type === null) {\n      type = IonTypes.NULL;\n    }\n    this.handleSeparator();\n    this.writeAnnotations();\n    this._writeNull(type);\n    if (this.currentContainer.containerType === IonTypes.STRUCT) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n  }\n  writeString(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.STRING, value, value => {\n      this.writeable.writeBytes(encodeUtf8('\"' + escape(value, StringEscapes) + '\"'));\n    });\n  }\n  writeSymbol(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.SYMBOL, value, value => {\n      this.writeSymbolToken(value);\n    });\n  }\n  writeTimestamp(value) {\n    _assertDefined(value);\n    this._serializeValue(IonTypes.TIMESTAMP, value, value => {\n      this.writeUtf8(value.toString());\n    });\n  }\n  stepIn(type) {\n    if (this.currentContainer.state === State.STRUCT_FIELD) {\n      throw new Error(`Started writing a ${this.currentContainer.containerType.name} inside a struct\"\n                + \" without writing the field name first. Call writeFieldName(string) with the desired name\"\n                + \" before calling stepIn(${this.currentContainer.containerType.name}).`);\n    }\n    switch (type) {\n      case IonTypes.LIST:\n        this.writeContainer(type, CharCodes.LEFT_BRACKET);\n        break;\n      case IonTypes.SEXP:\n        this.writeContainer(type, CharCodes.LEFT_PARENTHESIS);\n        break;\n      case IonTypes.STRUCT:\n        if (this._annotations !== undefined && this._annotations[0] === \"$ion_symbol_table\" && this.depth() === 0) {\n          throw new Error(\"Unable to alter symbol table context, it allows invalid ion to be written.\");\n        }\n        this.writeContainer(type, CharCodes.LEFT_BRACE);\n        break;\n      default:\n        throw new Error(\"Unrecognized container type\");\n    }\n  }\n  stepOut() {\n    const currentContainer = this.containerContext.pop();\n    if (!currentContainer || !currentContainer.containerType) {\n      throw new Error(\"Can't step out when not in a container\");\n    } else if (currentContainer.containerType === IonTypes.STRUCT && currentContainer.state === State.VALUE) {\n      throw new Error(\"Expecting a struct value\");\n    }\n    switch (currentContainer.containerType) {\n      case IonTypes.LIST:\n        this.writeable.writeByte(CharCodes.RIGHT_BRACKET);\n        break;\n      case IonTypes.SEXP:\n        this.writeable.writeByte(CharCodes.RIGHT_PARENTHESIS);\n        break;\n      case IonTypes.STRUCT:\n        this.writeable.writeByte(CharCodes.RIGHT_BRACE);\n        break;\n      default:\n        throw new Error(\"Unexpected container TypeCode\");\n    }\n  }\n  close() {\n    if (this.depth() > 0) {\n      throw new Error(\"Writer has one or more open containers; call stepOut() for each container prior to close()\");\n    }\n  }\n  depth() {\n    return this.containerContext.length - 1;\n  }\n  _serializeValue(type, value, serialize) {\n    if (this.currentContainer.state === State.STRUCT_FIELD) {\n      throw new Error(\"Expecting a struct field\");\n    }\n    if (value === null) {\n      this.writeNull(type);\n      return;\n    }\n    this.handleSeparator();\n    this.writeAnnotations();\n    serialize(value);\n    if (this.currentContainer.containerType === IonTypes.STRUCT) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n  }\n  writeContainer(type, openingCharacter) {\n    if (this.currentContainer.containerType === IonTypes.STRUCT && this.currentContainer.state === State.VALUE) {\n      this.currentContainer.state = State.STRUCT_FIELD;\n    }\n    this.handleSeparator();\n    this.writeAnnotations();\n    this.writeable.writeByte(openingCharacter);\n    this._stepIn(type);\n  }\n  handleSeparator() {\n    if (this.depth() === 0) {\n      if (this.currentContainer.clean) {\n        this.currentContainer.clean = false;\n      } else {\n        this.writeable.writeByte(CharCodes.LINE_FEED);\n      }\n    } else {\n      if (this.currentContainer.clean) {\n        this.currentContainer.clean = false;\n      } else {\n        switch (this.currentContainer.containerType) {\n          case IonTypes.LIST:\n            this.writeable.writeByte(CharCodes.COMMA);\n            break;\n          case IonTypes.SEXP:\n            this.writeable.writeByte(CharCodes.SPACE);\n            break;\n          default:\n        }\n      }\n    }\n  }\n  writeUtf8(s) {\n    this.writeable.writeBytes(encodeUtf8(s));\n  }\n  writeAnnotations() {\n    for (const annotation of this._annotations) {\n      this.writeSymbolToken(annotation);\n      this.writeUtf8(\"::\");\n    }\n    this._clearAnnotations();\n  }\n  _stepIn(container) {\n    this.containerContext.push(new Context(container));\n  }\n  writeSymbolToken(s) {\n    if (s.length === 0 || is_keyword(s) || this.isSid(s) || !isIdentifier(s) && !isOperator(s) || isOperator(s) && this.currentContainer.containerType != IonTypes.SEXP) {\n      this.writeable.writeBytes(encodeUtf8(\"'\" + escape(s, SymbolEscapes) + \"'\"));\n    } else {\n      this.writeUtf8(s);\n    }\n  }\n  _writeFloat(value) {\n    this._serializeValue(IonTypes.FLOAT, value, this._floatSerializer);\n  }\n  isSid(s) {\n    if (s.length > 1 && s.charAt(0) === \"$\".charAt(0)) {\n      const t = s.substr(1, s.length);\n      return +t === +t;\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["AbstractWriter", "CharCodes", "ClobEscapes", "escape", "isIdentifier", "isOperator", "is_keyword", "StringEscapes", "SymbolEscapes", "toBase64", "IonTypes", "encodeUtf8", "_assertDefined", "State", "Context", "constructor", "myType", "state", "STRUCT", "STRUCT_FIELD", "VALUE", "clean", "containerType", "TextWriter", "writeable", "_floatSerializer", "value", "_serializeFloat", "containerContext", "isTopLevel", "depth", "currentC<PERSON><PERSON>", "writer", "text", "Number", "POSITIVE_INFINITY", "NEGATIVE_INFINITY", "Object", "is", "NaN", "toExponential", "plusSignIndex", "lastIndexOf", "slice", "writeUtf8", "getBytes", "writeBlob", "_serializeValue", "BLOB", "writeBytes", "writeBoolean", "BOOL", "writeClob", "CLOB", "hexStr", "i", "length", "c", "toString", "j", "writeByte", "charCodeAt", "undefined", "Uint8Array", "writeDecimal", "DECIMAL", "s", "coefficient", "getCoefficient", "isNegative", "exponent", "getExponent", "scale", "significantDigits", "adjustedExponent", "wholeDigits", "substring", "_isInStruct", "writeFieldName", "fieldName", "Error", "COMMA", "writeSymbolToken", "COLON", "writeFloat32", "_writeFloat", "writeFloat64", "writeInt", "INT", "_writeNull", "type", "NULL", "name", "writeNull", "handleSeparator", "writeAnnotations", "writeString", "STRING", "writeSymbol", "SYMBOL", "writeTimestamp", "TIMESTAMP", "stepIn", "LIST", "writeContainer", "LEFT_BRACKET", "SEXP", "LEFT_PARENTHESIS", "_annotations", "LEFT_BRACE", "stepOut", "pop", "RIGHT_BRACKET", "RIGHT_PARENTHESIS", "RIGHT_BRACE", "close", "serialize", "openingCharacter", "_stepIn", "LINE_FEED", "SPACE", "annotation", "_clearAnnotations", "container", "push", "isSid", "FLOAT", "char<PERSON>t", "t", "substr"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonTextWriter.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { AbstractWriter } from \"./AbstractWriter\";\nimport { CharCodes, ClobEscapes, escape, isIdentifier, isOperator, is_keyword, StringEscapes, SymbolEscapes, toBase64, } from \"./IonText\";\nimport { IonTypes } from \"./IonTypes\";\nimport { encodeUtf8 } from \"./IonUnicode\";\nimport { _assertDefined } from \"./util\";\nexport var State;\n(function (State) {\n    State[State[\"VALUE\"] = 0] = \"VALUE\";\n    State[State[\"STRUCT_FIELD\"] = 1] = \"STRUCT_FIELD\";\n})(State || (State = {}));\nexport class Context {\n    constructor(myType) {\n        this.state = myType === IonTypes.STRUCT ? State.STRUCT_FIELD : State.VALUE;\n        this.clean = true;\n        this.containerType = myType;\n    }\n}\nexport class TextWriter extends AbstractWriter {\n    constructor(writeable) {\n        super();\n        this.writeable = writeable;\n        this._floatSerializer = (value) => {\n            TextWriter._serializeFloat(this, value);\n        };\n        this.containerContext = [new Context(null)];\n    }\n    get isTopLevel() {\n        return this.depth() === 0;\n    }\n    get currentContainer() {\n        return this.containerContext[this.depth()];\n    }\n    static _serializeFloat(writer, value) {\n        let text;\n        if (value === Number.POSITIVE_INFINITY) {\n            text = \"+inf\";\n        }\n        else if (value === Number.NEGATIVE_INFINITY) {\n            text = \"-inf\";\n        }\n        else if (Object.is(value, Number.NaN)) {\n            text = \"nan\";\n        }\n        else if (Object.is(value, -0)) {\n            text = \"-0e0\";\n        }\n        else {\n            text = value.toExponential();\n            const plusSignIndex = text.lastIndexOf(\"+\");\n            if (plusSignIndex > -1) {\n                text = text.slice(0, plusSignIndex) + text.slice(plusSignIndex + 1);\n            }\n        }\n        writer.writeUtf8(text);\n    }\n    getBytes() {\n        return this.writeable.getBytes();\n    }\n    writeBlob(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.BLOB, value, (value) => {\n            this.writeable.writeBytes(encodeUtf8(\"{{\" + toBase64(value) + \"}}\"));\n        });\n    }\n    writeBoolean(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.BOOL, value, (value) => {\n            this.writeUtf8(value ? \"true\" : \"false\");\n        });\n    }\n    writeClob(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.CLOB, value, (value) => {\n            let hexStr;\n            this.writeUtf8('{{\"');\n            for (let i = 0; i < value.length; i++) {\n                const c = value[i];\n                if (c > 127 && c < 256) {\n                    hexStr = \"\\\\x\" + c.toString(16);\n                    for (let j = 0; j < hexStr.length; j++) {\n                        this.writeable.writeByte(hexStr.charCodeAt(j));\n                    }\n                }\n                else {\n                    const escape = ClobEscapes[c];\n                    if (escape === undefined) {\n                        if (c < 32) {\n                            hexStr = \"\\\\x\" + c.toString(16);\n                            for (let j = 0; j < hexStr.length; j++) {\n                                this.writeable.writeByte(hexStr.charCodeAt(j));\n                            }\n                        }\n                        else {\n                            this.writeable.writeByte(c);\n                        }\n                    }\n                    else {\n                        this.writeable.writeBytes(new Uint8Array(escape));\n                    }\n                }\n            }\n            this.writeUtf8('\"}}');\n        });\n    }\n    writeDecimal(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.DECIMAL, value, (value) => {\n            let s = \"\";\n            let coefficient = value.getCoefficient();\n            if (coefficient < 0n) {\n                coefficient = -coefficient;\n            }\n            if (value.isNegative()) {\n                s += \"-\";\n            }\n            const exponent = value.getExponent();\n            const scale = -exponent;\n            if (exponent == 0) {\n                s += coefficient.toString() + \".\";\n            }\n            else if (exponent < 0) {\n                const significantDigits = coefficient.toString().length;\n                const adjustedExponent = significantDigits - 1 - scale;\n                if (adjustedExponent >= 0) {\n                    const wholeDigits = significantDigits - scale;\n                    s += coefficient.toString().substring(0, wholeDigits);\n                    s += \".\";\n                    s += coefficient.toString().substring(wholeDigits, significantDigits);\n                }\n                else if (adjustedExponent >= -6) {\n                    s += \"0.\";\n                    s += \"00000\".substring(0, scale - significantDigits);\n                    s += coefficient.toString();\n                }\n                else {\n                    s += coefficient.toString();\n                    s += \"d-\";\n                    s += scale.toString();\n                }\n            }\n            else {\n                s += coefficient.toString() + \"d\" + exponent;\n            }\n            this.writeUtf8(s);\n        });\n    }\n    _isInStruct() {\n        return this.currentContainer.containerType === IonTypes.STRUCT;\n    }\n    writeFieldName(fieldName) {\n        _assertDefined(fieldName);\n        if (this.currentContainer.containerType !== IonTypes.STRUCT) {\n            throw new Error(\"Cannot write field name outside of a struct\");\n        }\n        if (this.currentContainer.state !== State.STRUCT_FIELD) {\n            throw new Error(\"Expecting a struct value\");\n        }\n        if (!this.currentContainer.clean) {\n            this.writeable.writeByte(CharCodes.COMMA);\n        }\n        this.writeSymbolToken(fieldName);\n        this.writeable.writeByte(CharCodes.COLON);\n        this.currentContainer.state = State.VALUE;\n    }\n    writeFloat32(value) {\n        _assertDefined(value);\n        this._writeFloat(value);\n    }\n    writeFloat64(value) {\n        _assertDefined(value);\n        this._writeFloat(value);\n    }\n    writeInt(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.INT, value, (value) => {\n            this.writeUtf8(value.toString(10));\n        });\n    }\n    _writeNull(type) {\n        if (type === IonTypes.NULL) {\n            this.writeUtf8(\"null\");\n        }\n        else {\n            this.writeUtf8(\"null.\" + type.name);\n        }\n    }\n    writeNull(type) {\n        if (type === undefined || type === null) {\n            type = IonTypes.NULL;\n        }\n        this.handleSeparator();\n        this.writeAnnotations();\n        this._writeNull(type);\n        if (this.currentContainer.containerType === IonTypes.STRUCT) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n    }\n    writeString(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.STRING, value, (value) => {\n            this.writeable.writeBytes(encodeUtf8('\"' + escape(value, StringEscapes) + '\"'));\n        });\n    }\n    writeSymbol(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.SYMBOL, value, (value) => {\n            this.writeSymbolToken(value);\n        });\n    }\n    writeTimestamp(value) {\n        _assertDefined(value);\n        this._serializeValue(IonTypes.TIMESTAMP, value, (value) => {\n            this.writeUtf8(value.toString());\n        });\n    }\n    stepIn(type) {\n        if (this.currentContainer.state === State.STRUCT_FIELD) {\n            throw new Error(`Started writing a ${this.currentContainer.containerType.name} inside a struct\"\n                + \" without writing the field name first. Call writeFieldName(string) with the desired name\"\n                + \" before calling stepIn(${this.currentContainer.containerType.name}).`);\n        }\n        switch (type) {\n            case IonTypes.LIST:\n                this.writeContainer(type, CharCodes.LEFT_BRACKET);\n                break;\n            case IonTypes.SEXP:\n                this.writeContainer(type, CharCodes.LEFT_PARENTHESIS);\n                break;\n            case IonTypes.STRUCT:\n                if (this._annotations !== undefined &&\n                    this._annotations[0] === \"$ion_symbol_table\" &&\n                    this.depth() === 0) {\n                    throw new Error(\"Unable to alter symbol table context, it allows invalid ion to be written.\");\n                }\n                this.writeContainer(type, CharCodes.LEFT_BRACE);\n                break;\n            default:\n                throw new Error(\"Unrecognized container type\");\n        }\n    }\n    stepOut() {\n        const currentContainer = this.containerContext.pop();\n        if (!currentContainer || !currentContainer.containerType) {\n            throw new Error(\"Can't step out when not in a container\");\n        }\n        else if (currentContainer.containerType === IonTypes.STRUCT &&\n            currentContainer.state === State.VALUE) {\n            throw new Error(\"Expecting a struct value\");\n        }\n        switch (currentContainer.containerType) {\n            case IonTypes.LIST:\n                this.writeable.writeByte(CharCodes.RIGHT_BRACKET);\n                break;\n            case IonTypes.SEXP:\n                this.writeable.writeByte(CharCodes.RIGHT_PARENTHESIS);\n                break;\n            case IonTypes.STRUCT:\n                this.writeable.writeByte(CharCodes.RIGHT_BRACE);\n                break;\n            default:\n                throw new Error(\"Unexpected container TypeCode\");\n        }\n    }\n    close() {\n        if (this.depth() > 0) {\n            throw new Error(\"Writer has one or more open containers; call stepOut() for each container prior to close()\");\n        }\n    }\n    depth() {\n        return this.containerContext.length - 1;\n    }\n    _serializeValue(type, value, serialize) {\n        if (this.currentContainer.state === State.STRUCT_FIELD) {\n            throw new Error(\"Expecting a struct field\");\n        }\n        if (value === null) {\n            this.writeNull(type);\n            return;\n        }\n        this.handleSeparator();\n        this.writeAnnotations();\n        serialize(value);\n        if (this.currentContainer.containerType === IonTypes.STRUCT) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n    }\n    writeContainer(type, openingCharacter) {\n        if (this.currentContainer.containerType === IonTypes.STRUCT &&\n            this.currentContainer.state === State.VALUE) {\n            this.currentContainer.state = State.STRUCT_FIELD;\n        }\n        this.handleSeparator();\n        this.writeAnnotations();\n        this.writeable.writeByte(openingCharacter);\n        this._stepIn(type);\n    }\n    handleSeparator() {\n        if (this.depth() === 0) {\n            if (this.currentContainer.clean) {\n                this.currentContainer.clean = false;\n            }\n            else {\n                this.writeable.writeByte(CharCodes.LINE_FEED);\n            }\n        }\n        else {\n            if (this.currentContainer.clean) {\n                this.currentContainer.clean = false;\n            }\n            else {\n                switch (this.currentContainer.containerType) {\n                    case IonTypes.LIST:\n                        this.writeable.writeByte(CharCodes.COMMA);\n                        break;\n                    case IonTypes.SEXP:\n                        this.writeable.writeByte(CharCodes.SPACE);\n                        break;\n                    default:\n                }\n            }\n        }\n    }\n    writeUtf8(s) {\n        this.writeable.writeBytes(encodeUtf8(s));\n    }\n    writeAnnotations() {\n        for (const annotation of this._annotations) {\n            this.writeSymbolToken(annotation);\n            this.writeUtf8(\"::\");\n        }\n        this._clearAnnotations();\n    }\n    _stepIn(container) {\n        this.containerContext.push(new Context(container));\n    }\n    writeSymbolToken(s) {\n        if (s.length === 0 ||\n            is_keyword(s) ||\n            this.isSid(s) ||\n            (!isIdentifier(s) && !isOperator(s)) ||\n            (isOperator(s) && this.currentContainer.containerType != IonTypes.SEXP)) {\n            this.writeable.writeBytes(encodeUtf8(\"'\" + escape(s, SymbolEscapes) + \"'\"));\n        }\n        else {\n            this.writeUtf8(s);\n        }\n    }\n    _writeFloat(value) {\n        this._serializeValue(IonTypes.FLOAT, value, this._floatSerializer);\n    }\n    isSid(s) {\n        if (s.length > 1 && s.charAt(0) === \"$\".charAt(0)) {\n            const t = s.substr(1, s.length);\n            return +t === +t;\n        }\n        return false;\n    }\n}\n//# sourceMappingURL=IonTextWriter.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,QAAS,WAAW;AACzI,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,QAAQ;AACvC,OAAO,IAAIC,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;AACrD,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,KAAK,GAAGD,MAAM,KAAKN,QAAQ,CAACQ,MAAM,GAAGL,KAAK,CAACM,YAAY,GAAGN,KAAK,CAACO,KAAK;IAC1E,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,aAAa,GAAGN,MAAM;EAC/B;AACJ;AACA,OAAO,MAAMO,UAAU,SAASvB,cAAc,CAAC;EAC3Ce,WAAWA,CAACS,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAIC,KAAK,IAAK;MAC/BH,UAAU,CAACI,eAAe,CAAC,IAAI,EAAED,KAAK,CAAC;IAC3C,CAAC;IACD,IAAI,CAACE,gBAAgB,GAAG,CAAC,IAAId,OAAO,CAAC,IAAI,CAAC,CAAC;EAC/C;EACA,IAAIe,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC,KAAK,CAAC;EAC7B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACH,gBAAgB,CAAC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EAC9C;EACA,OAAOH,eAAeA,CAACK,MAAM,EAAEN,KAAK,EAAE;IAClC,IAAIO,IAAI;IACR,IAAIP,KAAK,KAAKQ,MAAM,CAACC,iBAAiB,EAAE;MACpCF,IAAI,GAAG,MAAM;IACjB,CAAC,MACI,IAAIP,KAAK,KAAKQ,MAAM,CAACE,iBAAiB,EAAE;MACzCH,IAAI,GAAG,MAAM;IACjB,CAAC,MACI,IAAII,MAAM,CAACC,EAAE,CAACZ,KAAK,EAAEQ,MAAM,CAACK,GAAG,CAAC,EAAE;MACnCN,IAAI,GAAG,KAAK;IAChB,CAAC,MACI,IAAII,MAAM,CAACC,EAAE,CAACZ,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;MAC3BO,IAAI,GAAG,MAAM;IACjB,CAAC,MACI;MACDA,IAAI,GAAGP,KAAK,CAACc,aAAa,CAAC,CAAC;MAC5B,MAAMC,aAAa,GAAGR,IAAI,CAACS,WAAW,CAAC,GAAG,CAAC;MAC3C,IAAID,aAAa,GAAG,CAAC,CAAC,EAAE;QACpBR,IAAI,GAAGA,IAAI,CAACU,KAAK,CAAC,CAAC,EAAEF,aAAa,CAAC,GAAGR,IAAI,CAACU,KAAK,CAACF,aAAa,GAAG,CAAC,CAAC;MACvE;IACJ;IACAT,MAAM,CAACY,SAAS,CAACX,IAAI,CAAC;EAC1B;EACAY,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACrB,SAAS,CAACqB,QAAQ,CAAC,CAAC;EACpC;EACAC,SAASA,CAACpB,KAAK,EAAE;IACbd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACsC,IAAI,EAAEtB,KAAK,EAAGA,KAAK,IAAK;MAClD,IAAI,CAACF,SAAS,CAACyB,UAAU,CAACtC,UAAU,CAAC,IAAI,GAAGF,QAAQ,CAACiB,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IACxE,CAAC,CAAC;EACN;EACAwB,YAAYA,CAACxB,KAAK,EAAE;IAChBd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACyC,IAAI,EAAEzB,KAAK,EAAGA,KAAK,IAAK;MAClD,IAAI,CAACkB,SAAS,CAAClB,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;IAC5C,CAAC,CAAC;EACN;EACA0B,SAASA,CAAC1B,KAAK,EAAE;IACbd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAAC2C,IAAI,EAAE3B,KAAK,EAAGA,KAAK,IAAK;MAClD,IAAI4B,MAAM;MACV,IAAI,CAACV,SAAS,CAAC,KAAK,CAAC;MACrB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,KAAK,CAAC8B,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAME,CAAC,GAAG/B,KAAK,CAAC6B,CAAC,CAAC;QAClB,IAAIE,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,EAAE;UACpBH,MAAM,GAAG,KAAK,GAAGG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;UAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACE,MAAM,EAAEG,CAAC,EAAE,EAAE;YACpC,IAAI,CAACnC,SAAS,CAACoC,SAAS,CAACN,MAAM,CAACO,UAAU,CAACF,CAAC,CAAC,CAAC;UAClD;QACJ,CAAC,MACI;UACD,MAAMxD,MAAM,GAAGD,WAAW,CAACuD,CAAC,CAAC;UAC7B,IAAItD,MAAM,KAAK2D,SAAS,EAAE;YACtB,IAAIL,CAAC,GAAG,EAAE,EAAE;cACRH,MAAM,GAAG,KAAK,GAAGG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;cAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACE,MAAM,EAAEG,CAAC,EAAE,EAAE;gBACpC,IAAI,CAACnC,SAAS,CAACoC,SAAS,CAACN,MAAM,CAACO,UAAU,CAACF,CAAC,CAAC,CAAC;cAClD;YACJ,CAAC,MACI;cACD,IAAI,CAACnC,SAAS,CAACoC,SAAS,CAACH,CAAC,CAAC;YAC/B;UACJ,CAAC,MACI;YACD,IAAI,CAACjC,SAAS,CAACyB,UAAU,CAAC,IAAIc,UAAU,CAAC5D,MAAM,CAAC,CAAC;UACrD;QACJ;MACJ;MACA,IAAI,CAACyC,SAAS,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC;EACN;EACAoB,YAAYA,CAACtC,KAAK,EAAE;IAChBd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACuD,OAAO,EAAEvC,KAAK,EAAGA,KAAK,IAAK;MACrD,IAAIwC,CAAC,GAAG,EAAE;MACV,IAAIC,WAAW,GAAGzC,KAAK,CAAC0C,cAAc,CAAC,CAAC;MACxC,IAAID,WAAW,GAAG,EAAE,EAAE;QAClBA,WAAW,GAAG,CAACA,WAAW;MAC9B;MACA,IAAIzC,KAAK,CAAC2C,UAAU,CAAC,CAAC,EAAE;QACpBH,CAAC,IAAI,GAAG;MACZ;MACA,MAAMI,QAAQ,GAAG5C,KAAK,CAAC6C,WAAW,CAAC,CAAC;MACpC,MAAMC,KAAK,GAAG,CAACF,QAAQ;MACvB,IAAIA,QAAQ,IAAI,CAAC,EAAE;QACfJ,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC,GAAG,GAAG;MACrC,CAAC,MACI,IAAIY,QAAQ,GAAG,CAAC,EAAE;QACnB,MAAMG,iBAAiB,GAAGN,WAAW,CAACT,QAAQ,CAAC,CAAC,CAACF,MAAM;QACvD,MAAMkB,gBAAgB,GAAGD,iBAAiB,GAAG,CAAC,GAAGD,KAAK;QACtD,IAAIE,gBAAgB,IAAI,CAAC,EAAE;UACvB,MAAMC,WAAW,GAAGF,iBAAiB,GAAGD,KAAK;UAC7CN,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC,CAACkB,SAAS,CAAC,CAAC,EAAED,WAAW,CAAC;UACrDT,CAAC,IAAI,GAAG;UACRA,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC,CAACkB,SAAS,CAACD,WAAW,EAAEF,iBAAiB,CAAC;QACzE,CAAC,MACI,IAAIC,gBAAgB,IAAI,CAAC,CAAC,EAAE;UAC7BR,CAAC,IAAI,IAAI;UACTA,CAAC,IAAI,OAAO,CAACU,SAAS,CAAC,CAAC,EAAEJ,KAAK,GAAGC,iBAAiB,CAAC;UACpDP,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC;QAC/B,CAAC,MACI;UACDQ,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC;UAC3BQ,CAAC,IAAI,IAAI;UACTA,CAAC,IAAIM,KAAK,CAACd,QAAQ,CAAC,CAAC;QACzB;MACJ,CAAC,MACI;QACDQ,CAAC,IAAIC,WAAW,CAACT,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGY,QAAQ;MAChD;MACA,IAAI,CAAC1B,SAAS,CAACsB,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACAW,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9C,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM;EAClE;EACA4D,cAAcA,CAACC,SAAS,EAAE;IACtBnE,cAAc,CAACmE,SAAS,CAAC;IACzB,IAAI,IAAI,CAAChD,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM,EAAE;MACzD,MAAM,IAAI8D,KAAK,CAAC,6CAA6C,CAAC;IAClE;IACA,IAAI,IAAI,CAACjD,gBAAgB,CAACd,KAAK,KAAKJ,KAAK,CAACM,YAAY,EAAE;MACpD,MAAM,IAAI6D,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAI,CAAC,IAAI,CAACjD,gBAAgB,CAACV,KAAK,EAAE;MAC9B,IAAI,CAACG,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACgF,KAAK,CAAC;IAC7C;IACA,IAAI,CAACC,gBAAgB,CAACH,SAAS,CAAC;IAChC,IAAI,CAACvD,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACkF,KAAK,CAAC;IACzC,IAAI,CAACpD,gBAAgB,CAACd,KAAK,GAAGJ,KAAK,CAACO,KAAK;EAC7C;EACAgE,YAAYA,CAAC1D,KAAK,EAAE;IAChBd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAAC2D,WAAW,CAAC3D,KAAK,CAAC;EAC3B;EACA4D,YAAYA,CAAC5D,KAAK,EAAE;IAChBd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAAC2D,WAAW,CAAC3D,KAAK,CAAC;EAC3B;EACA6D,QAAQA,CAAC7D,KAAK,EAAE;IACZd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAAC8E,GAAG,EAAE9D,KAAK,EAAGA,KAAK,IAAK;MACjD,IAAI,CAACkB,SAAS,CAAClB,KAAK,CAACgC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC;EACN;EACA+B,UAAUA,CAACC,IAAI,EAAE;IACb,IAAIA,IAAI,KAAKhF,QAAQ,CAACiF,IAAI,EAAE;MACxB,IAAI,CAAC/C,SAAS,CAAC,MAAM,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,SAAS,CAAC,OAAO,GAAG8C,IAAI,CAACE,IAAI,CAAC;IACvC;EACJ;EACAC,SAASA,CAACH,IAAI,EAAE;IACZ,IAAIA,IAAI,KAAK5B,SAAS,IAAI4B,IAAI,KAAK,IAAI,EAAE;MACrCA,IAAI,GAAGhF,QAAQ,CAACiF,IAAI;IACxB;IACA,IAAI,CAACG,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACN,UAAU,CAACC,IAAI,CAAC;IACrB,IAAI,IAAI,CAAC3D,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM,EAAE;MACzD,IAAI,CAACa,gBAAgB,CAACd,KAAK,GAAGJ,KAAK,CAACM,YAAY;IACpD;EACJ;EACA6E,WAAWA,CAACtE,KAAK,EAAE;IACfd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACuF,MAAM,EAAEvE,KAAK,EAAGA,KAAK,IAAK;MACpD,IAAI,CAACF,SAAS,CAACyB,UAAU,CAACtC,UAAU,CAAC,GAAG,GAAGR,MAAM,CAACuB,KAAK,EAAEnB,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC;IACnF,CAAC,CAAC;EACN;EACA2F,WAAWA,CAACxE,KAAK,EAAE;IACfd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACyF,MAAM,EAAEzE,KAAK,EAAGA,KAAK,IAAK;MACpD,IAAI,CAACwD,gBAAgB,CAACxD,KAAK,CAAC;IAChC,CAAC,CAAC;EACN;EACA0E,cAAcA,CAAC1E,KAAK,EAAE;IAClBd,cAAc,CAACc,KAAK,CAAC;IACrB,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAAC2F,SAAS,EAAE3E,KAAK,EAAGA,KAAK,IAAK;MACvD,IAAI,CAACkB,SAAS,CAAClB,KAAK,CAACgC,QAAQ,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACN;EACA4C,MAAMA,CAACZ,IAAI,EAAE;IACT,IAAI,IAAI,CAAC3D,gBAAgB,CAACd,KAAK,KAAKJ,KAAK,CAACM,YAAY,EAAE;MACpD,MAAM,IAAI6D,KAAK,CAAC,qBAAqB,IAAI,CAACjD,gBAAgB,CAACT,aAAa,CAACsE,IAAI;AACzF;AACA,4CAA4C,IAAI,CAAC7D,gBAAgB,CAACT,aAAa,CAACsE,IAAI,IAAI,CAAC;IACjF;IACA,QAAQF,IAAI;MACR,KAAKhF,QAAQ,CAAC6F,IAAI;QACd,IAAI,CAACC,cAAc,CAACd,IAAI,EAAEzF,SAAS,CAACwG,YAAY,CAAC;QACjD;MACJ,KAAK/F,QAAQ,CAACgG,IAAI;QACd,IAAI,CAACF,cAAc,CAACd,IAAI,EAAEzF,SAAS,CAAC0G,gBAAgB,CAAC;QACrD;MACJ,KAAKjG,QAAQ,CAACQ,MAAM;QAChB,IAAI,IAAI,CAAC0F,YAAY,KAAK9C,SAAS,IAC/B,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAC,KAAK,mBAAmB,IAC5C,IAAI,CAAC9E,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;UACpB,MAAM,IAAIkD,KAAK,CAAC,4EAA4E,CAAC;QACjG;QACA,IAAI,CAACwB,cAAc,CAACd,IAAI,EAAEzF,SAAS,CAAC4G,UAAU,CAAC;QAC/C;MACJ;QACI,MAAM,IAAI7B,KAAK,CAAC,6BAA6B,CAAC;IACtD;EACJ;EACA8B,OAAOA,CAAA,EAAG;IACN,MAAM/E,gBAAgB,GAAG,IAAI,CAACH,gBAAgB,CAACmF,GAAG,CAAC,CAAC;IACpD,IAAI,CAAChF,gBAAgB,IAAI,CAACA,gBAAgB,CAACT,aAAa,EAAE;MACtD,MAAM,IAAI0D,KAAK,CAAC,wCAAwC,CAAC;IAC7D,CAAC,MACI,IAAIjD,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM,IACvDa,gBAAgB,CAACd,KAAK,KAAKJ,KAAK,CAACO,KAAK,EAAE;MACxC,MAAM,IAAI4D,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,QAAQjD,gBAAgB,CAACT,aAAa;MAClC,KAAKZ,QAAQ,CAAC6F,IAAI;QACd,IAAI,CAAC/E,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAAC+G,aAAa,CAAC;QACjD;MACJ,KAAKtG,QAAQ,CAACgG,IAAI;QACd,IAAI,CAAClF,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACgH,iBAAiB,CAAC;QACrD;MACJ,KAAKvG,QAAQ,CAACQ,MAAM;QAChB,IAAI,CAACM,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACiH,WAAW,CAAC;QAC/C;MACJ;QACI,MAAM,IAAIlC,KAAK,CAAC,+BAA+B,CAAC;IACxD;EACJ;EACAmC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACrF,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;MAClB,MAAM,IAAIkD,KAAK,CAAC,4FAA4F,CAAC;IACjH;EACJ;EACAlD,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACF,gBAAgB,CAAC4B,MAAM,GAAG,CAAC;EAC3C;EACAT,eAAeA,CAAC2C,IAAI,EAAEhE,KAAK,EAAE0F,SAAS,EAAE;IACpC,IAAI,IAAI,CAACrF,gBAAgB,CAACd,KAAK,KAAKJ,KAAK,CAACM,YAAY,EAAE;MACpD,MAAM,IAAI6D,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAItD,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAACmE,SAAS,CAACH,IAAI,CAAC;MACpB;IACJ;IACA,IAAI,CAACI,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvBqB,SAAS,CAAC1F,KAAK,CAAC;IAChB,IAAI,IAAI,CAACK,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM,EAAE;MACzD,IAAI,CAACa,gBAAgB,CAACd,KAAK,GAAGJ,KAAK,CAACM,YAAY;IACpD;EACJ;EACAqF,cAAcA,CAACd,IAAI,EAAE2B,gBAAgB,EAAE;IACnC,IAAI,IAAI,CAACtF,gBAAgB,CAACT,aAAa,KAAKZ,QAAQ,CAACQ,MAAM,IACvD,IAAI,CAACa,gBAAgB,CAACd,KAAK,KAAKJ,KAAK,CAACO,KAAK,EAAE;MAC7C,IAAI,CAACW,gBAAgB,CAACd,KAAK,GAAGJ,KAAK,CAACM,YAAY;IACpD;IACA,IAAI,CAAC2E,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACvE,SAAS,CAACoC,SAAS,CAACyD,gBAAgB,CAAC;IAC1C,IAAI,CAACC,OAAO,CAAC5B,IAAI,CAAC;EACtB;EACAI,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAChE,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;MACpB,IAAI,IAAI,CAACC,gBAAgB,CAACV,KAAK,EAAE;QAC7B,IAAI,CAACU,gBAAgB,CAACV,KAAK,GAAG,KAAK;MACvC,CAAC,MACI;QACD,IAAI,CAACG,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACsH,SAAS,CAAC;MACjD;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACxF,gBAAgB,CAACV,KAAK,EAAE;QAC7B,IAAI,CAACU,gBAAgB,CAACV,KAAK,GAAG,KAAK;MACvC,CAAC,MACI;QACD,QAAQ,IAAI,CAACU,gBAAgB,CAACT,aAAa;UACvC,KAAKZ,QAAQ,CAAC6F,IAAI;YACd,IAAI,CAAC/E,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACgF,KAAK,CAAC;YACzC;UACJ,KAAKvE,QAAQ,CAACgG,IAAI;YACd,IAAI,CAAClF,SAAS,CAACoC,SAAS,CAAC3D,SAAS,CAACuH,KAAK,CAAC;YACzC;UACJ;QACJ;MACJ;IACJ;EACJ;EACA5E,SAASA,CAACsB,CAAC,EAAE;IACT,IAAI,CAAC1C,SAAS,CAACyB,UAAU,CAACtC,UAAU,CAACuD,CAAC,CAAC,CAAC;EAC5C;EACA6B,gBAAgBA,CAAA,EAAG;IACf,KAAK,MAAM0B,UAAU,IAAI,IAAI,CAACb,YAAY,EAAE;MACxC,IAAI,CAAC1B,gBAAgB,CAACuC,UAAU,CAAC;MACjC,IAAI,CAAC7E,SAAS,CAAC,IAAI,CAAC;IACxB;IACA,IAAI,CAAC8E,iBAAiB,CAAC,CAAC;EAC5B;EACAJ,OAAOA,CAACK,SAAS,EAAE;IACf,IAAI,CAAC/F,gBAAgB,CAACgG,IAAI,CAAC,IAAI9G,OAAO,CAAC6G,SAAS,CAAC,CAAC;EACtD;EACAzC,gBAAgBA,CAAChB,CAAC,EAAE;IAChB,IAAIA,CAAC,CAACV,MAAM,KAAK,CAAC,IACdlD,UAAU,CAAC4D,CAAC,CAAC,IACb,IAAI,CAAC2D,KAAK,CAAC3D,CAAC,CAAC,IACZ,CAAC9D,YAAY,CAAC8D,CAAC,CAAC,IAAI,CAAC7D,UAAU,CAAC6D,CAAC,CAAE,IACnC7D,UAAU,CAAC6D,CAAC,CAAC,IAAI,IAAI,CAACnC,gBAAgB,CAACT,aAAa,IAAIZ,QAAQ,CAACgG,IAAK,EAAE;MACzE,IAAI,CAAClF,SAAS,CAACyB,UAAU,CAACtC,UAAU,CAAC,GAAG,GAAGR,MAAM,CAAC+D,CAAC,EAAE1D,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/E,CAAC,MACI;MACD,IAAI,CAACoC,SAAS,CAACsB,CAAC,CAAC;IACrB;EACJ;EACAmB,WAAWA,CAAC3D,KAAK,EAAE;IACf,IAAI,CAACqB,eAAe,CAACrC,QAAQ,CAACoH,KAAK,EAAEpG,KAAK,EAAE,IAAI,CAACD,gBAAgB,CAAC;EACtE;EACAoG,KAAKA,CAAC3D,CAAC,EAAE;IACL,IAAIA,CAAC,CAACV,MAAM,GAAG,CAAC,IAAIU,CAAC,CAAC6D,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE;MAC/C,MAAMC,CAAC,GAAG9D,CAAC,CAAC+D,MAAM,CAAC,CAAC,EAAE/D,CAAC,CAACV,MAAM,CAAC;MAC/B,OAAO,CAACwE,CAAC,KAAK,CAACA,CAAC;IACpB;IACA,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}