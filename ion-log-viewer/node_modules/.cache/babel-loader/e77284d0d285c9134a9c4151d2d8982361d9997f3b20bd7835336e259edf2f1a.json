{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, MOUSE, TOUCH, Quaternion, PerspectiveCamera, OrthographicCamera, Spherical, Vector2, Ray, Plane } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nconst _ray = /* @__PURE__ */new Ray();\nconst _plane = /* @__PURE__ */new Plane();\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180));\nconst moduloWrapAround = (offset, capacity) => (offset % capacity + capacity) % capacity;\nclass OrbitControls extends EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    // Set to false to disable this control\n    __publicField(this, \"enabled\", true);\n    // \"target\" sets the location of focus, where the object orbits around\n    __publicField(this, \"target\", new Vector3());\n    // How far you can dolly in and out ( PerspectiveCamera only )\n    __publicField(this, \"minDistance\", 0);\n    __publicField(this, \"maxDistance\", Infinity);\n    // How far you can zoom in and out ( OrthographicCamera only )\n    __publicField(this, \"minZoom\", 0);\n    __publicField(this, \"maxZoom\", Infinity);\n    // How far you can orbit vertically, upper and lower limits.\n    // Range is 0 to Math.PI radians.\n    __publicField(this, \"minPolarAngle\", 0);\n    // radians\n    __publicField(this, \"maxPolarAngle\", Math.PI);\n    // radians\n    // How far you can orbit horizontally, upper and lower limits.\n    // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n    __publicField(this, \"minAzimuthAngle\", -Infinity);\n    // radians\n    __publicField(this, \"maxAzimuthAngle\", Infinity);\n    // radians\n    // Set to true to enable damping (inertia)\n    // If damping is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"enableDamping\", false);\n    __publicField(this, \"dampingFactor\", 0.05);\n    // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n    // Set to false to disable zooming\n    __publicField(this, \"enableZoom\", true);\n    __publicField(this, \"zoomSpeed\", 1);\n    // Set to false to disable rotating\n    __publicField(this, \"enableRotate\", true);\n    __publicField(this, \"rotateSpeed\", 1);\n    // Set to false to disable panning\n    __publicField(this, \"enablePan\", true);\n    __publicField(this, \"panSpeed\", 1);\n    __publicField(this, \"screenSpacePanning\", true);\n    // if false, pan orthogonal to world-space direction camera.up\n    __publicField(this, \"keyPanSpeed\", 7);\n    // pixels moved per arrow key push\n    __publicField(this, \"zoomToCursor\", false);\n    // Set to true to automatically rotate around the target\n    // If auto-rotate is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"autoRotate\", false);\n    __publicField(this, \"autoRotateSpeed\", 2);\n    // 30 seconds per orbit when fps is 60\n    __publicField(this, \"reverseOrbit\", false);\n    // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n    __publicField(this, \"reverseHorizontalOrbit\", false);\n    // true if you want to reverse the horizontal orbit direction\n    __publicField(this, \"reverseVerticalOrbit\", false);\n    // true if you want to reverse the vertical orbit direction\n    // The four arrow keys\n    __publicField(this, \"keys\", {\n      LEFT: \"ArrowLeft\",\n      UP: \"ArrowUp\",\n      RIGHT: \"ArrowRight\",\n      BOTTOM: \"ArrowDown\"\n    });\n    // Mouse buttons\n    __publicField(this, \"mouseButtons\", {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN\n    });\n    // Touch fingers\n    __publicField(this, \"touches\", {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN\n    });\n    __publicField(this, \"target0\");\n    __publicField(this, \"position0\");\n    __publicField(this, \"zoom0\");\n    // the target DOM element for key events\n    __publicField(this, \"_domElementKeyEvents\", null);\n    __publicField(this, \"getPolarAngle\");\n    __publicField(this, \"getAzimuthalAngle\");\n    __publicField(this, \"setPolarAngle\");\n    __publicField(this, \"setAzimuthalAngle\");\n    __publicField(this, \"getDistance\");\n    // Not used in most scenarios, however they can be useful for specific use cases\n    __publicField(this, \"getZoomScale\");\n    __publicField(this, \"listenToKeyEvents\");\n    __publicField(this, \"stopListenToKeyEvents\");\n    __publicField(this, \"saveState\");\n    __publicField(this, \"reset\");\n    __publicField(this, \"update\");\n    __publicField(this, \"connect\");\n    __publicField(this, \"dispose\");\n    // Dolly in programmatically\n    __publicField(this, \"dollyIn\");\n    // Dolly out programmatically\n    __publicField(this, \"dollyOut\");\n    // Get the current scale\n    __publicField(this, \"getScale\");\n    // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n    __publicField(this, \"setScale\");\n    this.object = object;\n    this.domElement = domElement;\n    this.target0 = this.target.clone();\n    this.position0 = this.object.position.clone();\n    this.zoom0 = this.object.zoom;\n    this.getPolarAngle = () => spherical.phi;\n    this.getAzimuthalAngle = () => spherical.theta;\n    this.setPolarAngle = value => {\n      let phi = moduloWrapAround(value, 2 * Math.PI);\n      let currentPhi = spherical.phi;\n      if (currentPhi < 0) currentPhi += 2 * Math.PI;\n      if (phi < 0) phi += 2 * Math.PI;\n      let phiDist = Math.abs(phi - currentPhi);\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI;\n        } else {\n          currentPhi += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi;\n      scope.update();\n    };\n    this.setAzimuthalAngle = value => {\n      let theta = moduloWrapAround(value, 2 * Math.PI);\n      let currentTheta = spherical.theta;\n      if (currentTheta < 0) currentTheta += 2 * Math.PI;\n      if (theta < 0) theta += 2 * Math.PI;\n      let thetaDist = Math.abs(theta - currentTheta);\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI;\n        } else {\n          currentTheta += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta;\n      scope.update();\n    };\n    this.getDistance = () => scope.object.position.distanceTo(scope.target);\n    this.listenToKeyEvents = domElement2 => {\n      domElement2.addEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = domElement2;\n    };\n    this.stopListenToKeyEvents = () => {\n      this._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = null;\n    };\n    this.saveState = () => {\n      scope.target0.copy(scope.target);\n      scope.position0.copy(scope.object.position);\n      scope.zoom0 = scope.object.zoom;\n    };\n    this.reset = () => {\n      scope.target.copy(scope.target0);\n      scope.object.position.copy(scope.position0);\n      scope.object.zoom = scope.zoom0;\n      scope.object.updateProjectionMatrix();\n      scope.dispatchEvent(changeEvent);\n      scope.update();\n      state = STATE.NONE;\n    };\n    this.update = (() => {\n      const offset = new Vector3();\n      const up = new Vector3(0, 1, 0);\n      const quat = new Quaternion().setFromUnitVectors(object.up, up);\n      const quatInverse = quat.clone().invert();\n      const lastPosition = new Vector3();\n      const lastQuaternion = new Quaternion();\n      const twoPI = 2 * Math.PI;\n      return function update() {\n        const position = scope.object.position;\n        quat.setFromUnitVectors(object.up, up);\n        quatInverse.copy(quat).invert();\n        offset.copy(position).sub(scope.target);\n        offset.applyQuaternion(quat);\n        spherical.setFromVector3(offset);\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle());\n        }\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor;\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor;\n        } else {\n          spherical.theta += sphericalDelta.theta;\n          spherical.phi += sphericalDelta.phi;\n        }\n        let min = scope.minAzimuthAngle;\n        let max = scope.maxAzimuthAngle;\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI;else if (min > Math.PI) min -= twoPI;\n          if (max < -Math.PI) max += twoPI;else if (max > Math.PI) max -= twoPI;\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta));\n          } else {\n            spherical.theta = spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta);\n          }\n        }\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi));\n        spherical.makeSafe();\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor);\n        } else {\n          scope.target.add(panOffset);\n        }\n        if (scope.zoomToCursor && performCursorZoom || scope.object.isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius);\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale);\n        }\n        offset.setFromSpherical(spherical);\n        offset.applyQuaternion(quatInverse);\n        position.copy(scope.target).add(offset);\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix();\n        scope.object.lookAt(scope.target);\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor;\n          sphericalDelta.phi *= 1 - scope.dampingFactor;\n          panOffset.multiplyScalar(1 - scope.dampingFactor);\n        } else {\n          sphericalDelta.set(0, 0, 0);\n          panOffset.set(0, 0, 0);\n        }\n        let zoomChanged = false;\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null;\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            const prevRadius = offset.length();\n            newRadius = clampDistance(prevRadius * scale);\n            const radiusDelta = prevRadius - newRadius;\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta);\n            scope.object.updateMatrixWorld();\n          } else if (scope.object.isOrthographicCamera) {\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0);\n            mouseBefore.unproject(scope.object);\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n            zoomChanged = true;\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0);\n            mouseAfter.unproject(scope.object);\n            scope.object.position.sub(mouseAfter).add(mouseBefore);\n            scope.object.updateMatrixWorld();\n            newRadius = offset.length();\n          } else {\n            console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.\");\n            scope.zoomToCursor = false;\n          }\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              scope.target.set(0, 0, -1).transformDirection(scope.object.matrix).multiplyScalar(newRadius).add(scope.object.position);\n            } else {\n              _ray.origin.copy(scope.object.position);\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix);\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target);\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target);\n                _ray.intersectPlane(_plane, scope.target);\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1;\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n          }\n        }\n        scale = 1;\n        performCursorZoom = false;\n        if (zoomChanged || lastPosition.distanceToSquared(scope.object.position) > EPS || 8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS) {\n          scope.dispatchEvent(changeEvent);\n          lastPosition.copy(scope.object.position);\n          lastQuaternion.copy(scope.object.quaternion);\n          zoomChanged = false;\n          return true;\n        }\n        return false;\n      };\n    })();\n    this.connect = domElement2 => {\n      scope.domElement = domElement2;\n      scope.domElement.style.touchAction = \"none\";\n      scope.domElement.addEventListener(\"contextmenu\", onContextMenu);\n      scope.domElement.addEventListener(\"pointerdown\", onPointerDown);\n      scope.domElement.addEventListener(\"pointercancel\", onPointerUp);\n      scope.domElement.addEventListener(\"wheel\", onMouseWheel);\n    };\n    this.dispose = () => {\n      var _a, _b, _c, _d, _e, _f;\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = \"auto\";\n      }\n      (_a = scope.domElement) == null ? void 0 : _a.removeEventListener(\"contextmenu\", onContextMenu);\n      (_b = scope.domElement) == null ? void 0 : _b.removeEventListener(\"pointerdown\", onPointerDown);\n      (_c = scope.domElement) == null ? void 0 : _c.removeEventListener(\"pointercancel\", onPointerUp);\n      (_d = scope.domElement) == null ? void 0 : _d.removeEventListener(\"wheel\", onMouseWheel);\n      (_e = scope.domElement) == null ? void 0 : _e.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n      (_f = scope.domElement) == null ? void 0 : _f.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      }\n    };\n    const scope = this;\n    const changeEvent = {\n      type: \"change\"\n    };\n    const startEvent = {\n      type: \"start\"\n    };\n    const endEvent = {\n      type: \"end\"\n    };\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6\n    };\n    let state = STATE.NONE;\n    const EPS = 1e-6;\n    const spherical = new Spherical();\n    const sphericalDelta = new Spherical();\n    let scale = 1;\n    const panOffset = new Vector3();\n    const rotateStart = new Vector2();\n    const rotateEnd = new Vector2();\n    const rotateDelta = new Vector2();\n    const panStart = new Vector2();\n    const panEnd = new Vector2();\n    const panDelta = new Vector2();\n    const dollyStart = new Vector2();\n    const dollyEnd = new Vector2();\n    const dollyDelta = new Vector2();\n    const dollyDirection = new Vector3();\n    const mouse = new Vector2();\n    let performCursorZoom = false;\n    const pointers = [];\n    const pointerPositions = {};\n    function getAutoRotationAngle() {\n      return 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed;\n    }\n    function getZoomScale() {\n      return Math.pow(0.95, scope.zoomSpeed);\n    }\n    function rotateLeft(angle) {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle;\n      } else {\n        sphericalDelta.theta -= angle;\n      }\n    }\n    function rotateUp(angle) {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle;\n      } else {\n        sphericalDelta.phi -= angle;\n      }\n    }\n    const panLeft = (() => {\n      const v = new Vector3();\n      return function panLeft2(distance, objectMatrix) {\n        v.setFromMatrixColumn(objectMatrix, 0);\n        v.multiplyScalar(-distance);\n        panOffset.add(v);\n      };\n    })();\n    const panUp = (() => {\n      const v = new Vector3();\n      return function panUp2(distance, objectMatrix) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1);\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0);\n          v.crossVectors(scope.object.up, v);\n        }\n        v.multiplyScalar(distance);\n        panOffset.add(v);\n      };\n    })();\n    const pan = (() => {\n      const offset = new Vector3();\n      return function pan2(deltaX, deltaY) {\n        const element = scope.domElement;\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          const position = scope.object.position;\n          offset.copy(position).sub(scope.target);\n          let targetDistance = offset.length();\n          targetDistance *= Math.tan(scope.object.fov / 2 * Math.PI / 180);\n          panLeft(2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix);\n          panUp(2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix);\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          panLeft(deltaX * (scope.object.right - scope.object.left) / scope.object.zoom / element.clientWidth, scope.object.matrix);\n          panUp(deltaY * (scope.object.top - scope.object.bottom) / scope.object.zoom / element.clientHeight, scope.object.matrix);\n        } else {\n          console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.\");\n          scope.enablePan = false;\n        }\n      };\n    })();\n    function setScale(newScale) {\n      if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera || scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n        scale = newScale;\n      } else {\n        console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.\");\n        scope.enableZoom = false;\n      }\n    }\n    function dollyOut(dollyScale) {\n      setScale(scale / dollyScale);\n    }\n    function dollyIn(dollyScale) {\n      setScale(scale * dollyScale);\n    }\n    function updateMouseParameters(event) {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return;\n      }\n      performCursorZoom = true;\n      const rect = scope.domElement.getBoundingClientRect();\n      const x = event.clientX - rect.left;\n      const y = event.clientY - rect.top;\n      const w = rect.width;\n      const h = rect.height;\n      mouse.x = x / w * 2 - 1;\n      mouse.y = -(y / h) * 2 + 1;\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize();\n    }\n    function clampDistance(dist) {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));\n    }\n    function handleMouseDownRotate(event) {\n      rotateStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownDolly(event) {\n      updateMouseParameters(event);\n      dollyStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownPan(event) {\n      panStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseMoveRotate(event) {\n      rotateEnd.set(event.clientX, event.clientY);\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n      scope.update();\n    }\n    function handleMouseMoveDolly(event) {\n      dollyEnd.set(event.clientX, event.clientY);\n      dollyDelta.subVectors(dollyEnd, dollyStart);\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale());\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale());\n      }\n      dollyStart.copy(dollyEnd);\n      scope.update();\n    }\n    function handleMouseMovePan(event) {\n      panEnd.set(event.clientX, event.clientY);\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n      scope.update();\n    }\n    function handleMouseWheel(event) {\n      updateMouseParameters(event);\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale());\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale());\n      }\n      scope.update();\n    }\n    function handleKeyDown(event) {\n      let needsUpdate = false;\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n      }\n      if (needsUpdate) {\n        event.preventDefault();\n        scope.update();\n      }\n    }\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        rotateStart.set(x, y);\n      }\n    }\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        panStart.set(x, y);\n      }\n    }\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX;\n      const dy = pointers[0].pageY - pointers[1].pageY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyStart.set(0, distance);\n    }\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly();\n      if (scope.enablePan) handleTouchStartPan();\n    }\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly();\n      if (scope.enableRotate) handleTouchStartRotate();\n    }\n    function handleTouchMoveRotate(event) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        rotateEnd.set(x, y);\n      }\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n    }\n    function handleTouchMovePan(event) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        panEnd.set(x, y);\n      }\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n    }\n    function handleTouchMoveDolly(event) {\n      const position = getSecondPointerPosition(event);\n      const dx = event.pageX - position.x;\n      const dy = event.pageY - position.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyEnd.set(0, distance);\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));\n      dollyOut(dollyDelta.y);\n      dollyStart.copy(dollyEnd);\n    }\n    function handleTouchMoveDollyPan(event) {\n      if (scope.enableZoom) handleTouchMoveDolly(event);\n      if (scope.enablePan) handleTouchMovePan(event);\n    }\n    function handleTouchMoveDollyRotate(event) {\n      if (scope.enableZoom) handleTouchMoveDolly(event);\n      if (scope.enableRotate) handleTouchMoveRotate(event);\n    }\n    function onPointerDown(event) {\n      var _a, _b;\n      if (scope.enabled === false) return;\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.ownerDocument.addEventListener(\"pointermove\", onPointerMove);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.addEventListener(\"pointerup\", onPointerUp);\n      }\n      addPointer(event);\n      if (event.pointerType === \"touch\") {\n        onTouchStart(event);\n      } else {\n        onMouseDown(event);\n      }\n    }\n    function onPointerMove(event) {\n      if (scope.enabled === false) return;\n      if (event.pointerType === \"touch\") {\n        onTouchMove(event);\n      } else {\n        onMouseMove(event);\n      }\n    }\n    function onPointerUp(event) {\n      var _a, _b, _c;\n      removePointer(event);\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.releasePointerCapture(event.pointerId);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n        (_c = scope.domElement) == null ? void 0 : _c.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      }\n      scope.dispatchEvent(endEvent);\n      state = STATE.NONE;\n    }\n    function onMouseDown(event) {\n      let mouseAction;\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT;\n          break;\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE;\n          break;\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT;\n          break;\n        default:\n          mouseAction = -1;\n      }\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return;\n          handleMouseDownDolly(event);\n          state = STATE.DOLLY;\n          break;\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          } else {\n            if (scope.enableRotate === false) return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          }\n          break;\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          } else {\n            if (scope.enablePan === false) return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onMouseMove(event) {\n      if (scope.enabled === false) return;\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return;\n          handleMouseMoveRotate(event);\n          break;\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return;\n          handleMouseMoveDolly(event);\n          break;\n        case STATE.PAN:\n          if (scope.enablePan === false) return;\n          handleMouseMovePan(event);\n          break;\n      }\n    }\n    function onMouseWheel(event) {\n      if (scope.enabled === false || scope.enableZoom === false || state !== STATE.NONE && state !== STATE.ROTATE) {\n        return;\n      }\n      event.preventDefault();\n      scope.dispatchEvent(startEvent);\n      handleMouseWheel(event);\n      scope.dispatchEvent(endEvent);\n    }\n    function onKeyDown(event) {\n      if (scope.enabled === false || scope.enablePan === false) return;\n      handleKeyDown(event);\n    }\n    function onTouchStart(event) {\n      trackPointer(event);\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return;\n              handleTouchStartRotate();\n              state = STATE.TOUCH_ROTATE;\n              break;\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return;\n              handleTouchStartPan();\n              state = STATE.TOUCH_PAN;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return;\n              handleTouchStartDollyPan();\n              state = STATE.TOUCH_DOLLY_PAN;\n              break;\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return;\n              handleTouchStartDollyRotate();\n              state = STATE.TOUCH_DOLLY_ROTATE;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onTouchMove(event) {\n      trackPointer(event);\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return;\n          handleTouchMoveRotate(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return;\n          handleTouchMovePan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return;\n          handleTouchMoveDollyPan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return;\n          handleTouchMoveDollyRotate(event);\n          scope.update();\n          break;\n        default:\n          state = STATE.NONE;\n      }\n    }\n    function onContextMenu(event) {\n      if (scope.enabled === false) return;\n      event.preventDefault();\n    }\n    function addPointer(event) {\n      pointers.push(event);\n    }\n    function removePointer(event) {\n      delete pointerPositions[event.pointerId];\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1);\n          return;\n        }\n      }\n    }\n    function trackPointer(event) {\n      let position = pointerPositions[event.pointerId];\n      if (position === void 0) {\n        position = new Vector2();\n        pointerPositions[event.pointerId] = position;\n      }\n      position.set(event.pageX, event.pageY);\n    }\n    function getSecondPointerPosition(event) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0];\n      return pointerPositions[pointer.pointerId];\n    }\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale);\n      scope.update();\n    };\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale);\n      scope.update();\n    };\n    this.getScale = () => {\n      return scale;\n    };\n    this.setScale = newScale => {\n      setScale(newScale);\n      scope.update();\n    };\n    this.getZoomScale = () => {\n      return getZoomScale();\n    };\n    if (domElement !== void 0) this.connect(domElement);\n    this.update();\n  }\n}\nclass MapControls extends OrbitControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    this.screenSpacePanning = false;\n    this.mouseButtons.LEFT = MOUSE.PAN;\n    this.mouseButtons.RIGHT = MOUSE.ROTATE;\n    this.touches.ONE = TOUCH.PAN;\n    this.touches.TWO = TOUCH.DOLLY_ROTATE;\n  }\n}\nexport { MapControls, OrbitControls };", "map": {"version": 3, "names": ["_ray", "<PERSON>", "_plane", "Plane", "TILT_LIMIT", "Math", "cos", "PI", "moduloWrapAround", "offset", "capacity", "OrbitControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "dom<PERSON>lement", "__publicField", "Vector3", "Infinity", "LEFT", "UP", "RIGHT", "BOTTOM", "MOUSE", "ROTATE", "MIDDLE", "DOLLY", "PAN", "ONE", "TOUCH", "TWO", "DOLLY_PAN", "target0", "target", "clone", "position0", "position", "zoom0", "zoom", "getPolarAngle", "spherical", "phi", "getAzimuthalAngle", "theta", "setPolarAngle", "value", "currentPhi", "phiDist", "abs", "sphericalDel<PERSON>", "scope", "update", "setAzimuthalAngle", "currentTheta", "thetaDist", "getDistance", "distanceTo", "listenToKeyEvents", "domElement2", "addEventListener", "onKeyDown", "_domElementKeyEvents", "stopListenToKeyEvents", "removeEventListener", "saveState", "copy", "reset", "updateProjectionMatrix", "dispatchEvent", "changeEvent", "state", "STATE", "NONE", "up", "quat", "Quaternion", "setFromUnitVectors", "quatInverse", "invert", "lastPosition", "lastQuaternion", "twoPI", "sub", "applyQuaternion", "setFromVector3", "autoRotate", "rotateLeft", "getAutoRotationAngle", "enableDamping", "dampingFactor", "min", "minAzimuthAngle", "max", "maxAzimuthAngle", "isFinite", "minPolarAngle", "maxPolarAngle", "makeSafe", "addScaledVector", "panOffset", "add", "zoomToCursor", "performCursorZoom", "isOrthographicCamera", "radius", "clampDistance", "scale", "setFromSpherical", "matrixAutoUpdate", "updateMatrix", "lookAt", "multiplyScalar", "set", "zoomChanged", "newRadius", "PerspectiveCamera", "isPerspectiveCamera", "prevRadius", "length", "radiusDel<PERSON>", "dollyDirection", "updateMatrixWorld", "mouseBefore", "mouse", "x", "y", "unproject", "minZoom", "max<PERSON><PERSON>", "mouseAfter", "console", "warn", "screenSpacePanning", "transformDirection", "matrix", "origin", "direction", "dot", "setFromNormalAndCoplanarPoint", "intersectPlane", "OrthographicCamera", "distanceToSquared", "EPS", "quaternion", "connect", "style", "touchAction", "onContextMenu", "onPointerDown", "onPointerUp", "onMouseWheel", "dispose", "_a", "_b", "_c", "_d", "_e", "ownerDocument", "onPointerMove", "_f", "type", "startEvent", "endEvent", "TOUCH_ROTATE", "TOUCH_PAN", "TOUCH_DOLLY_PAN", "TOUCH_DOLLY_ROTATE", "Spherical", "rotateStart", "Vector2", "rotateEnd", "<PERSON><PERSON><PERSON><PERSON>", "panStart", "panEnd", "panDelta", "dolly<PERSON><PERSON><PERSON>", "dollyEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointers", "pointerPositions", "autoRotateSpeed", "getZoomScale", "pow", "zoomSpeed", "angle", "reverseOrbit", "reverseHorizontalOrbit", "rotateUp", "reverseVerticalOrbit", "panLeft", "v", "panLeft2", "distance", "objectMatrix", "setFromMatrixColumn", "panUp", "panUp2", "crossVectors", "pan", "pan2", "deltaX", "deltaY", "element", "targetDistance", "tan", "fov", "clientHeight", "right", "left", "clientWidth", "top", "bottom", "enablePan", "setScale", "newScale", "enableZoom", "dollyOut", "dollyScale", "dollyIn", "updateMouseParameters", "event", "rect", "getBoundingClientRect", "clientX", "clientY", "w", "width", "h", "height", "normalize", "dist", "minDistance", "maxDistance", "handleMouseDownRotate", "handleMouseDownDolly", "handleMouseDownPan", "handleMouseMoveRotate", "subVectors", "rotateSpeed", "handleMouseMoveDolly", "handleMouseMovePan", "panSpeed", "handleMouseWheel", "handleKeyDown", "needsUpdate", "code", "keys", "keyPanSpeed", "preventDefault", "handleTouchStartRotate", "pageX", "pageY", "handleTouchStartPan", "handleTouchStartDolly", "dx", "dy", "sqrt", "handleTouchStartDollyPan", "handleTouchStartDollyRotate", "enableRotate", "handleTouchMoveRotate", "getSecondPointerPosition", "handleTouchMovePan", "handleTouchMoveDolly", "handleTouchMoveDollyPan", "handleTouchMoveDollyRotate", "enabled", "addPointer", "pointerType", "onTouchStart", "onMouseDown", "onTouchMove", "onMouseMove", "removePointer", "releasePointerCapture", "pointerId", "mouseAction", "button", "mouseButtons", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "trackPointer", "touches", "DOLLY_ROTATE", "push", "i", "splice", "pointer", "getScale", "MapControls"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "mappings": ";;;;;;;;;;;;;AAgBA,MAAMA,IAAA,sBAA2BC,GAAA;AACjC,MAAMC,MAAA,sBAA6BC,KAAA;AACnC,MAAMC,UAAA,GAAaC,IAAA,CAAKC,GAAA,CAAI,MAAMD,IAAA,CAAKE,EAAA,GAAK,IAAI;AAShD,MAAMC,gBAAA,GAAmBA,CAACC,MAAA,EAAgBC,QAAA,MAAuBD,MAAA,GAASC,QAAA,GAAYA,QAAA,IAAYA,QAAA;AAElG,MAAMC,aAAA,SAAsBC,eAAA,CAA0C;EA6FpEC,YAAYC,MAAA,EAAgDC,UAAA,EAA0B;IAC9E;IA7FRC,aAAA;IACAA,aAAA;IAEA;IAAAA,aAAA,kBAAU;IAEV;IAAAA,aAAA,iBAAS,IAAIC,OAAA;IAEb;IAAAD,aAAA,sBAAc;IACdA,aAAA,sBAAcE,QAAA;IAEd;IAAAF,aAAA,kBAAU;IACVA,aAAA,kBAAUE,QAAA;IAGV;IAAA;IAAAF,aAAA,wBAAgB;IAChB;IAAAA,aAAA,wBAAgBX,IAAA,CAAKE,EAAA;IAGrB;IAAA;IAAA;IAAAS,aAAA,0BAAkB,CAAAE,QAAA;IAClB;IAAAF,aAAA,0BAAkBE,QAAA;IAGlB;IAAA;IAAA;IAAAF,aAAA,wBAAgB;IAChBA,aAAA,wBAAgB;IAGhB;IAAA;IAAAA,aAAA,qBAAa;IACbA,aAAA,oBAAY;IAEZ;IAAAA,aAAA,uBAAe;IACfA,aAAA,sBAAc;IAEd;IAAAA,aAAA,oBAAY;IACZA,aAAA,mBAAW;IACXA,aAAA,6BAAqB;IACrB;IAAAA,aAAA,sBAAc;IACd;IAAAA,aAAA,uBAAe;IAGf;IAAA;IAAAA,aAAA,qBAAa;IACbA,aAAA,0BAAkB;IAClB;IAAAA,aAAA,uBAAe;IACf;IAAAA,aAAA,iCAAyB;IACzB;IAAAA,aAAA,+BAAuB;IAEvB;IAAA;IAAAA,aAAA,eAAO;MAAEG,IAAA,EAAM;MAAaC,EAAA,EAAI;MAAWC,KAAA,EAAO;MAAcC,MAAA,EAAQ;IAAA;IAExE;IAAAN,aAAA,uBAIK;MACHG,IAAA,EAAMI,KAAA,CAAMC,MAAA;MACZC,MAAA,EAAQF,KAAA,CAAMG,KAAA;MACdL,KAAA,EAAOE,KAAA,CAAMI;IAAA;IAGf;IAAAX,aAAA,kBAGK;MAAEY,GAAA,EAAKC,KAAA,CAAML,MAAA;MAAQM,GAAA,EAAKD,KAAA,CAAME;IAAA;IACrCf,aAAA;IACAA,aAAA;IACAA,aAAA;IAEA;IAAAA,aAAA,+BAA4B;IAE5BA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEA;IAAAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAGA;IAAAA,aAAA;IAEA;IAAAA,aAAA;IAEA;IAAAA,aAAA;IAEA;IAAAA,aAAA;IAKE,KAAKF,MAAA,GAASA,MAAA;IACd,KAAKC,UAAA,GAAaA,UAAA;IAGb,KAAAiB,OAAA,GAAU,KAAKC,MAAA,CAAOC,KAAA,CAAM;IACjC,KAAKC,SAAA,GAAY,KAAKrB,MAAA,CAAOsB,QAAA,CAASF,KAAA,CAAM;IACvC,KAAAG,KAAA,GAAQ,KAAKvB,MAAA,CAAOwB,IAAA;IAMpB,KAAAC,aAAA,GAAgB,MAAcC,SAAA,CAAUC,GAAA;IAExC,KAAAC,iBAAA,GAAoB,MAAcF,SAAA,CAAUG,KAAA;IAE5C,KAAAC,aAAA,GAAiBC,KAAA,IAAwB;MAE5C,IAAIJ,GAAA,GAAMjC,gBAAA,CAAiBqC,KAAA,EAAO,IAAIxC,IAAA,CAAKE,EAAE;MAC7C,IAAIuC,UAAA,GAAaN,SAAA,CAAUC,GAAA;MAG3B,IAAIK,UAAA,GAAa,GAAGA,UAAA,IAAc,IAAIzC,IAAA,CAAKE,EAAA;MAC3C,IAAIkC,GAAA,GAAM,GAAGA,GAAA,IAAO,IAAIpC,IAAA,CAAKE,EAAA;MAC7B,IAAIwC,OAAA,GAAU1C,IAAA,CAAK2C,GAAA,CAAIP,GAAA,GAAMK,UAAU;MACvC,IAAI,IAAIzC,IAAA,CAAKE,EAAA,GAAKwC,OAAA,GAAUA,OAAA,EAAS;QACnC,IAAIN,GAAA,GAAMK,UAAA,EAAY;UACpBL,GAAA,IAAO,IAAIpC,IAAA,CAAKE,EAAA;QAAA,OACX;UACLuC,UAAA,IAAc,IAAIzC,IAAA,CAAKE,EAAA;QACzB;MACF;MACA0C,cAAA,CAAeR,GAAA,GAAMA,GAAA,GAAMK,UAAA;MAC3BI,KAAA,CAAMC,MAAA,CAAO;IAAA;IAGV,KAAAC,iBAAA,GAAqBP,KAAA,IAAwB;MAEhD,IAAIF,KAAA,GAAQnC,gBAAA,CAAiBqC,KAAA,EAAO,IAAIxC,IAAA,CAAKE,EAAE;MAC/C,IAAI8C,YAAA,GAAeb,SAAA,CAAUG,KAAA;MAG7B,IAAIU,YAAA,GAAe,GAAGA,YAAA,IAAgB,IAAIhD,IAAA,CAAKE,EAAA;MAC/C,IAAIoC,KAAA,GAAQ,GAAGA,KAAA,IAAS,IAAItC,IAAA,CAAKE,EAAA;MACjC,IAAI+C,SAAA,GAAYjD,IAAA,CAAK2C,GAAA,CAAIL,KAAA,GAAQU,YAAY;MAC7C,IAAI,IAAIhD,IAAA,CAAKE,EAAA,GAAK+C,SAAA,GAAYA,SAAA,EAAW;QACvC,IAAIX,KAAA,GAAQU,YAAA,EAAc;UACxBV,KAAA,IAAS,IAAItC,IAAA,CAAKE,EAAA;QAAA,OACb;UACL8C,YAAA,IAAgB,IAAIhD,IAAA,CAAKE,EAAA;QAC3B;MACF;MACA0C,cAAA,CAAeN,KAAA,GAAQA,KAAA,GAAQU,YAAA;MAC/BH,KAAA,CAAMC,MAAA,CAAO;IAAA;IAGf,KAAKI,WAAA,GAAc,MAAcL,KAAA,CAAMpC,MAAA,CAAOsB,QAAA,CAASoB,UAAA,CAAWN,KAAA,CAAMjB,MAAM;IAEzE,KAAAwB,iBAAA,GAAqBC,WAAA,IAAkC;MAC1DA,WAAA,CAAWC,gBAAA,CAAiB,WAAWC,SAAS;MAChD,KAAKC,oBAAA,GAAuBH,WAAA;IAAA;IAG9B,KAAKI,qBAAA,GAAwB,MAAY;MAClC,KAAAD,oBAAA,CAAqBE,mBAAA,CAAoB,WAAWH,SAAS;MAClE,KAAKC,oBAAA,GAAuB;IAAA;IAG9B,KAAKG,SAAA,GAAY,MAAY;MACrBd,KAAA,CAAAlB,OAAA,CAAQiC,IAAA,CAAKf,KAAA,CAAMjB,MAAM;MAC/BiB,KAAA,CAAMf,SAAA,CAAU8B,IAAA,CAAKf,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ;MACpCc,KAAA,CAAAb,KAAA,GAAQa,KAAA,CAAMpC,MAAA,CAAOwB,IAAA;IAAA;IAG7B,KAAK4B,KAAA,GAAQ,MAAY;MACjBhB,KAAA,CAAAjB,MAAA,CAAOgC,IAAA,CAAKf,KAAA,CAAMlB,OAAO;MAC/BkB,KAAA,CAAMpC,MAAA,CAAOsB,QAAA,CAAS6B,IAAA,CAAKf,KAAA,CAAMf,SAAS;MACpCe,KAAA,CAAApC,MAAA,CAAOwB,IAAA,GAAOY,KAAA,CAAMb,KAAA;MAC1Ba,KAAA,CAAMpC,MAAA,CAAOqD,sBAAA;MAGbjB,KAAA,CAAMkB,aAAA,CAAcC,WAAW;MAE/BnB,KAAA,CAAMC,MAAA,CAAO;MAEbmB,KAAA,GAAQC,KAAA,CAAMC,IAAA;IAAA;IAIhB,KAAKrB,MAAA,IAAU,MAAoB;MAC3B,MAAA1C,MAAA,GAAS,IAAIQ,OAAA;MACnB,MAAMwD,EAAA,GAAK,IAAIxD,OAAA,CAAQ,GAAG,GAAG,CAAC;MAG9B,MAAMyD,IAAA,GAAO,IAAIC,UAAA,GAAaC,kBAAA,CAAmB9D,MAAA,CAAO2D,EAAA,EAAIA,EAAE;MAC9D,MAAMI,WAAA,GAAcH,IAAA,CAAKxC,KAAA,CAAM,EAAE4C,MAAA,CAAO;MAElC,MAAAC,YAAA,GAAe,IAAI9D,OAAA;MACnB,MAAA+D,cAAA,GAAiB,IAAIL,UAAA;MAErB,MAAAM,KAAA,GAAQ,IAAI5E,IAAA,CAAKE,EAAA;MAEvB,OAAO,SAAS4C,OAAA,EAAkB;QAC1B,MAAAf,QAAA,GAAWc,KAAA,CAAMpC,MAAA,CAAOsB,QAAA;QAGzBsC,IAAA,CAAAE,kBAAA,CAAmB9D,MAAA,CAAO2D,EAAA,EAAIA,EAAE;QACzBI,WAAA,CAAAZ,IAAA,CAAKS,IAAI,EAAEI,MAAA,CAAO;QAE9BrE,MAAA,CAAOwD,IAAA,CAAK7B,QAAQ,EAAE8C,GAAA,CAAIhC,KAAA,CAAMjB,MAAM;QAGtCxB,MAAA,CAAO0E,eAAA,CAAgBT,IAAI;QAG3BlC,SAAA,CAAU4C,cAAA,CAAe3E,MAAM;QAE/B,IAAIyC,KAAA,CAAMmC,UAAA,IAAcf,KAAA,KAAUC,KAAA,CAAMC,IAAA,EAAM;UAC5Cc,UAAA,CAAWC,oBAAA,EAAsB;QACnC;QAEA,IAAIrC,KAAA,CAAMsC,aAAA,EAAe;UACbhD,SAAA,CAAAG,KAAA,IAASM,cAAA,CAAeN,KAAA,GAAQO,KAAA,CAAMuC,aAAA;UACtCjD,SAAA,CAAAC,GAAA,IAAOQ,cAAA,CAAeR,GAAA,GAAMS,KAAA,CAAMuC,aAAA;QAAA,OACvC;UACLjD,SAAA,CAAUG,KAAA,IAASM,cAAA,CAAeN,KAAA;UAClCH,SAAA,CAAUC,GAAA,IAAOQ,cAAA,CAAeR,GAAA;QAClC;QAIA,IAAIiD,GAAA,GAAMxC,KAAA,CAAMyC,eAAA;QAChB,IAAIC,GAAA,GAAM1C,KAAA,CAAM2C,eAAA;QAEhB,IAAIC,QAAA,CAASJ,GAAG,KAAKI,QAAA,CAASF,GAAG,GAAG;UAC9B,IAAAF,GAAA,GAAM,CAACrF,IAAA,CAAKE,EAAA,EAAWmF,GAAA,IAAAT,KAAA,UAClBS,GAAA,GAAMrF,IAAA,CAAKE,EAAA,EAAWmF,GAAA,IAAAT,KAAA;UAE3B,IAAAW,GAAA,GAAM,CAACvF,IAAA,CAAKE,EAAA,EAAWqF,GAAA,IAAAX,KAAA,UAClBW,GAAA,GAAMvF,IAAA,CAAKE,EAAA,EAAWqF,GAAA,IAAAX,KAAA;UAE/B,IAAIS,GAAA,IAAOE,GAAA,EAAK;YACJpD,SAAA,CAAAG,KAAA,GAAQtC,IAAA,CAAKuF,GAAA,CAAIF,GAAA,EAAKrF,IAAA,CAAKqF,GAAA,CAAIE,GAAA,EAAKpD,SAAA,CAAUG,KAAK,CAAC;UAAA,OACzD;YACLH,SAAA,CAAUG,KAAA,GACRH,SAAA,CAAUG,KAAA,IAAS+C,GAAA,GAAME,GAAA,IAAO,IAAIvF,IAAA,CAAKuF,GAAA,CAAIF,GAAA,EAAKlD,SAAA,CAAUG,KAAK,IAAItC,IAAA,CAAKqF,GAAA,CAAIE,GAAA,EAAKpD,SAAA,CAAUG,KAAK;UACtG;QACF;QAGUH,SAAA,CAAAC,GAAA,GAAMpC,IAAA,CAAKuF,GAAA,CAAI1C,KAAA,CAAM6C,aAAA,EAAe1F,IAAA,CAAKqF,GAAA,CAAIxC,KAAA,CAAM8C,aAAA,EAAexD,SAAA,CAAUC,GAAG,CAAC;QAC1FD,SAAA,CAAUyD,QAAA,CAAS;QAIf,IAAA/C,KAAA,CAAMsC,aAAA,KAAkB,MAAM;UAChCtC,KAAA,CAAMjB,MAAA,CAAOiE,eAAA,CAAgBC,SAAA,EAAWjD,KAAA,CAAMuC,aAAa;QAAA,OACtD;UACCvC,KAAA,CAAAjB,MAAA,CAAOmE,GAAA,CAAID,SAAS;QAC5B;QAIA,IAAKjD,KAAA,CAAMmD,YAAA,IAAgBC,iBAAA,IAAuBpD,KAAA,CAAMpC,MAAA,CAA8ByF,oBAAA,EAAsB;UAChG/D,SAAA,CAAAgE,MAAA,GAASC,aAAA,CAAcjE,SAAA,CAAUgE,MAAM;QAAA,OAC5C;UACLhE,SAAA,CAAUgE,MAAA,GAASC,aAAA,CAAcjE,SAAA,CAAUgE,MAAA,GAASE,KAAK;QAC3D;QAEAjG,MAAA,CAAOkG,gBAAA,CAAiBnE,SAAS;QAGjC/B,MAAA,CAAO0E,eAAA,CAAgBN,WAAW;QAElCzC,QAAA,CAAS6B,IAAA,CAAKf,KAAA,CAAMjB,MAAM,EAAEmE,GAAA,CAAI3F,MAAM;QAElC,KAACyC,KAAA,CAAMpC,MAAA,CAAO8F,gBAAA,EAAkB1D,KAAA,CAAMpC,MAAA,CAAO+F,YAAA;QAC3C3D,KAAA,CAAApC,MAAA,CAAOgG,MAAA,CAAO5D,KAAA,CAAMjB,MAAM;QAE5B,IAAAiB,KAAA,CAAMsC,aAAA,KAAkB,MAAM;UACjBvC,cAAA,CAAAN,KAAA,IAAS,IAAIO,KAAA,CAAMuC,aAAA;UACnBxC,cAAA,CAAAR,GAAA,IAAO,IAAIS,KAAA,CAAMuC,aAAA;UAEtBU,SAAA,CAAAY,cAAA,CAAe,IAAI7D,KAAA,CAAMuC,aAAa;QAAA,OAC3C;UACUxC,cAAA,CAAA+D,GAAA,CAAI,GAAG,GAAG,CAAC;UAEhBb,SAAA,CAAAa,GAAA,CAAI,GAAG,GAAG,CAAC;QACvB;QAGA,IAAIC,WAAA,GAAc;QACd,IAAA/D,KAAA,CAAMmD,YAAA,IAAgBC,iBAAA,EAAmB;UAC3C,IAAIY,SAAA,GAAY;UAChB,IAAIhE,KAAA,CAAMpC,MAAA,YAAkBqG,iBAAA,IAAqBjE,KAAA,CAAMpC,MAAA,CAAOsG,mBAAA,EAAqB;YAG3E,MAAAC,UAAA,GAAa5G,MAAA,CAAO6G,MAAA;YACdJ,SAAA,GAAAT,aAAA,CAAcY,UAAA,GAAaX,KAAK;YAE5C,MAAMa,WAAA,GAAcF,UAAA,GAAaH,SAAA;YACjChE,KAAA,CAAMpC,MAAA,CAAOsB,QAAA,CAAS8D,eAAA,CAAgBsB,cAAA,EAAgBD,WAAW;YACjErE,KAAA,CAAMpC,MAAA,CAAO2G,iBAAA;UAAkB,WACrBvE,KAAA,CAAMpC,MAAA,CAA8ByF,oBAAA,EAAsB;YAEpE,MAAMmB,WAAA,GAAc,IAAIzG,OAAA,CAAQ0G,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAG,CAAC;YACvCH,WAAA,CAAAI,SAAA,CAAU5E,KAAA,CAAMpC,MAAM;YAElCoC,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOjC,IAAA,CAAKuF,GAAA,CAAI1C,KAAA,CAAM6E,OAAA,EAAS1H,IAAA,CAAKqF,GAAA,CAAIxC,KAAA,CAAM8E,OAAA,EAAS9E,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOoE,KAAK,CAAC;YAC9FxD,KAAA,CAAMpC,MAAA,CAAOqD,sBAAA;YACC8C,WAAA;YAEd,MAAMgB,UAAA,GAAa,IAAIhH,OAAA,CAAQ0G,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAG,CAAC;YACvCI,UAAA,CAAAH,SAAA,CAAU5E,KAAA,CAAMpC,MAAM;YAEjCoC,KAAA,CAAMpC,MAAA,CAAOsB,QAAA,CAAS8C,GAAA,CAAI+C,UAAU,EAAE7B,GAAA,CAAIsB,WAAW;YACrDxE,KAAA,CAAMpC,MAAA,CAAO2G,iBAAA;YAEbP,SAAA,GAAYzG,MAAA,CAAO6G,MAAA;UAAO,OACrB;YACLY,OAAA,CAAQC,IAAA,CAAK,yFAAyF;YACtGjF,KAAA,CAAMmD,YAAA,GAAe;UACvB;UAGA,IAAIa,SAAA,KAAc,MAAM;YACtB,IAAIhE,KAAA,CAAMkF,kBAAA,EAAoB;cAE5BlF,KAAA,CAAMjB,MAAA,CACH+E,GAAA,CAAI,GAAG,GAAG,EAAE,EACZqB,kBAAA,CAAmBnF,KAAA,CAAMpC,MAAA,CAAOwH,MAAM,EACtCvB,cAAA,CAAeG,SAAS,EACxBd,GAAA,CAAIlD,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ;YAAA,OACvB;cAELpC,IAAA,CAAKuI,MAAA,CAAOtE,IAAA,CAAKf,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ;cACjCpC,IAAA,CAAAwI,SAAA,CAAUxB,GAAA,CAAI,GAAG,GAAG,EAAE,EAAEqB,kBAAA,CAAmBnF,KAAA,CAAMpC,MAAA,CAAOwH,MAAM;cAI/D,IAAAjI,IAAA,CAAK2C,GAAA,CAAIE,KAAA,CAAMpC,MAAA,CAAO2D,EAAA,CAAGgE,GAAA,CAAIzI,IAAA,CAAKwI,SAAS,CAAC,IAAIpI,UAAA,EAAY;gBACvDU,MAAA,CAAAgG,MAAA,CAAO5D,KAAA,CAAMjB,MAAM;cAAA,OACrB;gBACL/B,MAAA,CAAOwI,6BAAA,CAA8BxF,KAAA,CAAMpC,MAAA,CAAO2D,EAAA,EAAIvB,KAAA,CAAMjB,MAAM;gBAC7DjC,IAAA,CAAA2I,cAAA,CAAezI,MAAA,EAAQgD,KAAA,CAAMjB,MAAM;cAC1C;YACF;UACF;QAAA,WACSiB,KAAA,CAAMpC,MAAA,YAAkB8H,kBAAA,IAAsB1F,KAAA,CAAMpC,MAAA,CAAOyF,oBAAA,EAAsB;UAC1FU,WAAA,GAAcP,KAAA,KAAU;UAExB,IAAIO,WAAA,EAAa;YACf/D,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOjC,IAAA,CAAKuF,GAAA,CAAI1C,KAAA,CAAM6E,OAAA,EAAS1H,IAAA,CAAKqF,GAAA,CAAIxC,KAAA,CAAM8E,OAAA,EAAS9E,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOoE,KAAK,CAAC;YAC9FxD,KAAA,CAAMpC,MAAA,CAAOqD,sBAAA;UACf;QACF;QAEQuC,KAAA;QACYJ,iBAAA;QAMpB,IACEW,WAAA,IACAlC,YAAA,CAAa8D,iBAAA,CAAkB3F,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ,IAAI0G,GAAA,IACxD,KAAK,IAAI9D,cAAA,CAAeyD,GAAA,CAAIvF,KAAA,CAAMpC,MAAA,CAAOiI,UAAU,KAAKD,GAAA,EACxD;UAEA5F,KAAA,CAAMkB,aAAA,CAAcC,WAAW;UAElBU,YAAA,CAAAd,IAAA,CAAKf,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ;UACxB4C,cAAA,CAAAf,IAAA,CAAKf,KAAA,CAAMpC,MAAA,CAAOiI,UAAU;UAC7B9B,WAAA;UAEP;QACT;QAEO;MAAA;IACT;IAIG,KAAA+B,OAAA,GAAWtF,WAAA,IAAkC;MAChDR,KAAA,CAAMnC,UAAA,GAAa2C,WAAA;MAIbR,KAAA,CAAAnC,UAAA,CAAWkI,KAAA,CAAMC,WAAA,GAAc;MAC/BhG,KAAA,CAAAnC,UAAA,CAAW4C,gBAAA,CAAiB,eAAewF,aAAa;MACxDjG,KAAA,CAAAnC,UAAA,CAAW4C,gBAAA,CAAiB,eAAeyF,aAAa;MACxDlG,KAAA,CAAAnC,UAAA,CAAW4C,gBAAA,CAAiB,iBAAiB0F,WAAW;MACxDnG,KAAA,CAAAnC,UAAA,CAAW4C,gBAAA,CAAiB,SAAS2F,YAAY;IAAA;IAGzD,KAAKC,OAAA,GAAU,MAAY;;MAEzB,IAAIrG,KAAA,CAAMnC,UAAA,EAAY;QACdmC,KAAA,CAAAnC,UAAA,CAAWkI,KAAA,CAAMC,WAAA,GAAc;MACvC;MACM,CAAAM,EAAA,GAAAtG,KAAA,CAAAnC,UAAA,qBAAAyI,EAAA,CAAYzF,mBAAA,CAAoB,eAAeoF,aAAA;MAC/C,CAAAM,EAAA,GAAAvG,KAAA,CAAAnC,UAAA,qBAAA0I,EAAA,CAAY1F,mBAAA,CAAoB,eAAeqF,aAAA;MAC/C,CAAAM,EAAA,GAAAxG,KAAA,CAAAnC,UAAA,qBAAA2I,EAAA,CAAY3F,mBAAA,CAAoB,iBAAiBsF,WAAA;MACjD,CAAAM,EAAA,GAAAzG,KAAA,CAAAnC,UAAA,qBAAA4I,EAAA,CAAY5F,mBAAA,CAAoB,SAASuF,YAAA;MAC/C,CAAAM,EAAA,GAAA1G,KAAA,CAAMnC,UAAA,KAAN,gBAAA6I,EAAA,CAAkBC,aAAA,CAAc9F,mBAAA,CAAoB,eAAe+F,aAAA;MACnE,CAAAC,EAAA,GAAA7G,KAAA,CAAMnC,UAAA,KAAN,gBAAAgJ,EAAA,CAAkBF,aAAA,CAAc9F,mBAAA,CAAoB,aAAasF,WAAA;MAC7D,IAAAnG,KAAA,CAAMW,oBAAA,KAAyB,MAAM;QACjCX,KAAA,CAAAW,oBAAA,CAAqBE,mBAAA,CAAoB,WAAWH,SAAS;MACrE;IAAA;IAQF,MAAMV,KAAA,GAAQ;IAER,MAAAmB,WAAA,GAAc;MAAE2F,IAAA,EAAM;IAAA;IACtB,MAAAC,UAAA,GAAa;MAAED,IAAA,EAAM;IAAA;IACrB,MAAAE,QAAA,GAAW;MAAEF,IAAA,EAAM;IAAA;IAEzB,MAAMzF,KAAA,GAAQ;MACZC,IAAA,EAAM;MACNhD,MAAA,EAAQ;MACRE,KAAA,EAAO;MACPC,GAAA,EAAK;MACLwI,YAAA,EAAc;MACdC,SAAA,EAAW;MACXC,eAAA,EAAiB;MACjBC,kBAAA,EAAoB;IAAA;IAGtB,IAAIhG,KAAA,GAAQC,KAAA,CAAMC,IAAA;IAElB,MAAMsE,GAAA,GAAM;IAGN,MAAAtG,SAAA,GAAY,IAAI+H,SAAA;IAChB,MAAAtH,cAAA,GAAiB,IAAIsH,SAAA;IAE3B,IAAI7D,KAAA,GAAQ;IACN,MAAAP,SAAA,GAAY,IAAIlF,OAAA;IAEhB,MAAAuJ,WAAA,GAAc,IAAIC,OAAA;IAClB,MAAAC,SAAA,GAAY,IAAID,OAAA;IAChB,MAAAE,WAAA,GAAc,IAAIF,OAAA;IAElB,MAAAG,QAAA,GAAW,IAAIH,OAAA;IACf,MAAAI,MAAA,GAAS,IAAIJ,OAAA;IACb,MAAAK,QAAA,GAAW,IAAIL,OAAA;IAEf,MAAAM,UAAA,GAAa,IAAIN,OAAA;IACjB,MAAAO,QAAA,GAAW,IAAIP,OAAA;IACf,MAAAQ,UAAA,GAAa,IAAIR,OAAA;IAEjB,MAAAjD,cAAA,GAAiB,IAAIvG,OAAA;IACrB,MAAA0G,KAAA,GAAQ,IAAI8C,OAAA;IAClB,IAAInE,iBAAA,GAAoB;IAExB,MAAM4E,QAAA,GAA2B;IACjC,MAAMC,gBAAA,GAA+C;IAErD,SAAS5F,qBAAA,EAA+B;MACtC,OAAS,IAAIlF,IAAA,CAAKE,EAAA,GAAM,KAAK,KAAM2C,KAAA,CAAMkI,eAAA;IAC3C;IAEA,SAASC,aAAA,EAAuB;MAC9B,OAAOhL,IAAA,CAAKiL,GAAA,CAAI,MAAMpI,KAAA,CAAMqI,SAAS;IACvC;IAEA,SAASjG,WAAWkG,KAAA,EAAqB;MACnC,IAAAtI,KAAA,CAAMuI,YAAA,IAAgBvI,KAAA,CAAMwI,sBAAA,EAAwB;QACtDzI,cAAA,CAAeN,KAAA,IAAS6I,KAAA;MAAA,OACnB;QACLvI,cAAA,CAAeN,KAAA,IAAS6I,KAAA;MAC1B;IACF;IAEA,SAASG,SAASH,KAAA,EAAqB;MACjC,IAAAtI,KAAA,CAAMuI,YAAA,IAAgBvI,KAAA,CAAM0I,oBAAA,EAAsB;QACpD3I,cAAA,CAAeR,GAAA,IAAO+I,KAAA;MAAA,OACjB;QACLvI,cAAA,CAAeR,GAAA,IAAO+I,KAAA;MACxB;IACF;IAEA,MAAMK,OAAA,IAAW,MAAM;MACf,MAAAC,CAAA,GAAI,IAAI7K,OAAA;MAEP,gBAAS8K,SAAQC,QAAA,EAAkBC,YAAA,EAAuB;QAC7DH,CAAA,CAAAI,mBAAA,CAAoBD,YAAA,EAAc,CAAC;QACnCH,CAAA,CAAA/E,cAAA,CAAe,CAACiF,QAAQ;QAE1B7F,SAAA,CAAUC,GAAA,CAAI0F,CAAC;MAAA;IACjB;IAGF,MAAMK,KAAA,IAAS,MAAM;MACb,MAAAL,CAAA,GAAI,IAAI7K,OAAA;MAEP,gBAASmL,OAAMJ,QAAA,EAAkBC,YAAA,EAAuB;QACzD,IAAA/I,KAAA,CAAMkF,kBAAA,KAAuB,MAAM;UACnC0D,CAAA,CAAAI,mBAAA,CAAoBD,YAAA,EAAc,CAAC;QAAA,OAChC;UACHH,CAAA,CAAAI,mBAAA,CAAoBD,YAAA,EAAc,CAAC;UACrCH,CAAA,CAAEO,YAAA,CAAanJ,KAAA,CAAMpC,MAAA,CAAO2D,EAAA,EAAIqH,CAAC;QACnC;QAEAA,CAAA,CAAE/E,cAAA,CAAeiF,QAAQ;QAEzB7F,SAAA,CAAUC,GAAA,CAAI0F,CAAC;MAAA;IACjB;IAIF,MAAMQ,GAAA,IAAO,MAAM;MACX,MAAA7L,MAAA,GAAS,IAAIQ,OAAA;MAEZ,gBAASsL,KAAIC,MAAA,EAAgBC,MAAA,EAAgB;QAClD,MAAMC,OAAA,GAAUxJ,KAAA,CAAMnC,UAAA;QAEtB,IAAI2L,OAAA,IAAWxJ,KAAA,CAAMpC,MAAA,YAAkBqG,iBAAA,IAAqBjE,KAAA,CAAMpC,MAAA,CAAOsG,mBAAA,EAAqB;UAEtF,MAAAhF,QAAA,GAAWc,KAAA,CAAMpC,MAAA,CAAOsB,QAAA;UAC9B3B,MAAA,CAAOwD,IAAA,CAAK7B,QAAQ,EAAE8C,GAAA,CAAIhC,KAAA,CAAMjB,MAAM;UAClC,IAAA0K,cAAA,GAAiBlM,MAAA,CAAO6G,MAAA;UAGVqF,cAAA,IAAAtM,IAAA,CAAKuM,GAAA,CAAM1J,KAAA,CAAMpC,MAAA,CAAO+L,GAAA,GAAM,IAAKxM,IAAA,CAAKE,EAAA,GAAM,GAAK;UAGrEsL,OAAA,CAAS,IAAIW,MAAA,GAASG,cAAA,GAAkBD,OAAA,CAAQI,YAAA,EAAc5J,KAAA,CAAMpC,MAAA,CAAOwH,MAAM;UACjF6D,KAAA,CAAO,IAAIM,MAAA,GAASE,cAAA,GAAkBD,OAAA,CAAQI,YAAA,EAAc5J,KAAA,CAAMpC,MAAA,CAAOwH,MAAM;QAAA,WACtEoE,OAAA,IAAWxJ,KAAA,CAAMpC,MAAA,YAAkB8H,kBAAA,IAAsB1F,KAAA,CAAMpC,MAAA,CAAOyF,oBAAA,EAAsB;UAErGsF,OAAA,CACGW,MAAA,IAAUtJ,KAAA,CAAMpC,MAAA,CAAOiM,KAAA,GAAQ7J,KAAA,CAAMpC,MAAA,CAAOkM,IAAA,IAAS9J,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOoK,OAAA,CAAQO,WAAA,EAClF/J,KAAA,CAAMpC,MAAA,CAAOwH,MAAA;UAEf6D,KAAA,CACGM,MAAA,IAAUvJ,KAAA,CAAMpC,MAAA,CAAOoM,GAAA,GAAMhK,KAAA,CAAMpC,MAAA,CAAOqM,MAAA,IAAWjK,KAAA,CAAMpC,MAAA,CAAOwB,IAAA,GAAOoK,OAAA,CAAQI,YAAA,EAClF5J,KAAA,CAAMpC,MAAA,CAAOwH,MAAA;QACf,OACK;UAELJ,OAAA,CAAQC,IAAA,CAAK,8EAA8E;UAC3FjF,KAAA,CAAMkK,SAAA,GAAY;QACpB;MAAA;IACF;IAGF,SAASC,SAASC,QAAA,EAAkB;MAE/B,IAAApK,KAAA,CAAMpC,MAAA,YAAkBqG,iBAAA,IAAqBjE,KAAA,CAAMpC,MAAA,CAAOsG,mBAAA,IAC1DlE,KAAA,CAAMpC,MAAA,YAAkB8H,kBAAA,IAAsB1F,KAAA,CAAMpC,MAAA,CAAOyF,oBAAA,EAC5D;QACQG,KAAA,GAAA4G,QAAA;MAAA,OACH;QACLpF,OAAA,CAAQC,IAAA,CAAK,qFAAqF;QAClGjF,KAAA,CAAMqK,UAAA,GAAa;MACrB;IACF;IAEA,SAASC,SAASC,UAAA,EAAoB;MACpCJ,QAAA,CAAS3G,KAAA,GAAQ+G,UAAU;IAC7B;IAEA,SAASC,QAAQD,UAAA,EAAoB;MACnCJ,QAAA,CAAS3G,KAAA,GAAQ+G,UAAU;IAC7B;IAEA,SAASE,sBAAsBC,KAAA,EAAyB;MACtD,IAAI,CAAC1K,KAAA,CAAMmD,YAAA,IAAgB,CAACnD,KAAA,CAAMnC,UAAA,EAAY;QAC5C;MACF;MAEoBuF,iBAAA;MAEd,MAAAuH,IAAA,GAAO3K,KAAA,CAAMnC,UAAA,CAAW+M,qBAAA,CAAsB;MAC9C,MAAAlG,CAAA,GAAIgG,KAAA,CAAMG,OAAA,GAAUF,IAAA,CAAKb,IAAA;MACzB,MAAAnF,CAAA,GAAI+F,KAAA,CAAMI,OAAA,GAAUH,IAAA,CAAKX,GAAA;MAC/B,MAAMe,CAAA,GAAIJ,IAAA,CAAKK,KAAA;MACf,MAAMC,CAAA,GAAIN,IAAA,CAAKO,MAAA;MAETzG,KAAA,CAAAC,CAAA,GAAKA,CAAA,GAAIqG,CAAA,GAAK,IAAI;MACxBtG,KAAA,CAAME,CAAA,GAAI,EAAEA,CAAA,GAAIsG,CAAA,IAAK,IAAI;MAEzB3G,cAAA,CAAeR,GAAA,CAAIW,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAG,CAAC,EAAEC,SAAA,CAAU5E,KAAA,CAAMpC,MAAM,EAAEoE,GAAA,CAAIhC,KAAA,CAAMpC,MAAA,CAAOsB,QAAQ,EAAEiM,SAAA;IAC7F;IAEA,SAAS5H,cAAc6H,IAAA,EAAsB;MACpC,OAAAjO,IAAA,CAAKuF,GAAA,CAAI1C,KAAA,CAAMqL,WAAA,EAAalO,IAAA,CAAKqF,GAAA,CAAIxC,KAAA,CAAMsL,WAAA,EAAaF,IAAI,CAAC;IACtE;IAMA,SAASG,sBAAsBb,KAAA,EAAmB;MAChDpD,WAAA,CAAYxD,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;IAC9C;IAEA,SAASU,qBAAqBd,KAAA,EAAmB;MAC/CD,qBAAA,CAAsBC,KAAK;MAC3B7C,UAAA,CAAW/D,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;IAC7C;IAEA,SAASW,mBAAmBf,KAAA,EAAmB;MAC7ChD,QAAA,CAAS5D,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;IAC3C;IAEA,SAASY,sBAAsBhB,KAAA,EAAmB;MAChDlD,SAAA,CAAU1D,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;MAC1CrD,WAAA,CAAYkE,UAAA,CAAWnE,SAAA,EAAWF,WAAW,EAAEzD,cAAA,CAAe7D,KAAA,CAAM4L,WAAW;MAE/E,MAAMpC,OAAA,GAAUxJ,KAAA,CAAMnC,UAAA;MAEtB,IAAI2L,OAAA,EAAS;QACXpH,UAAA,CAAY,IAAIjF,IAAA,CAAKE,EAAA,GAAKoK,WAAA,CAAY/C,CAAA,GAAK8E,OAAA,CAAQI,YAAY;QAC/DnB,QAAA,CAAU,IAAItL,IAAA,CAAKE,EAAA,GAAKoK,WAAA,CAAY9C,CAAA,GAAK6E,OAAA,CAAQI,YAAY;MAC/D;MACAtC,WAAA,CAAYvG,IAAA,CAAKyG,SAAS;MAC1BxH,KAAA,CAAMC,MAAA,CAAO;IACf;IAEA,SAAS4L,qBAAqBnB,KAAA,EAAmB;MAC/C5C,QAAA,CAAShE,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;MAC9B/C,UAAA,CAAA4D,UAAA,CAAW7D,QAAA,EAAUD,UAAU;MAEtC,IAAAE,UAAA,CAAWpD,CAAA,GAAI,GAAG;QACpB2F,QAAA,CAASnC,YAAA,EAAc;MAAA,WACdJ,UAAA,CAAWpD,CAAA,GAAI,GAAG;QAC3B6F,OAAA,CAAQrC,YAAA,EAAc;MACxB;MAEAN,UAAA,CAAW9G,IAAA,CAAK+G,QAAQ;MACxB9H,KAAA,CAAMC,MAAA,CAAO;IACf;IAEA,SAAS6L,mBAAmBpB,KAAA,EAAmB;MAC7C/C,MAAA,CAAO7D,GAAA,CAAI4G,KAAA,CAAMG,OAAA,EAASH,KAAA,CAAMI,OAAO;MACvClD,QAAA,CAAS+D,UAAA,CAAWhE,MAAA,EAAQD,QAAQ,EAAE7D,cAAA,CAAe7D,KAAA,CAAM+L,QAAQ;MAC/D3C,GAAA,CAAAxB,QAAA,CAASlD,CAAA,EAAGkD,QAAA,CAASjD,CAAC;MAC1B+C,QAAA,CAAS3G,IAAA,CAAK4G,MAAM;MACpB3H,KAAA,CAAMC,MAAA,CAAO;IACf;IAEA,SAAS+L,iBAAiBtB,KAAA,EAAmB;MAC3CD,qBAAA,CAAsBC,KAAK;MAEvB,IAAAA,KAAA,CAAMnB,MAAA,GAAS,GAAG;QACpBiB,OAAA,CAAQrC,YAAA,EAAc;MAAA,WACbuC,KAAA,CAAMnB,MAAA,GAAS,GAAG;QAC3Be,QAAA,CAASnC,YAAA,EAAc;MACzB;MAEAnI,KAAA,CAAMC,MAAA,CAAO;IACf;IAEA,SAASgM,cAAcvB,KAAA,EAAsB;MAC3C,IAAIwB,WAAA,GAAc;MAElB,QAAQxB,KAAA,CAAMyB,IAAA;QACZ,KAAKnM,KAAA,CAAMoM,IAAA,CAAKlO,EAAA;UACVkL,GAAA,IAAGpJ,KAAA,CAAMqM,WAAW;UACVH,WAAA;UACd;QAEF,KAAKlM,KAAA,CAAMoM,IAAA,CAAKhO,MAAA;UACVgL,GAAA,IAAG,CAACpJ,KAAA,CAAMqM,WAAW;UACXH,WAAA;UACd;QAEF,KAAKlM,KAAA,CAAMoM,IAAA,CAAKnO,IAAA;UACVmL,GAAA,CAAApJ,KAAA,CAAMqM,WAAA,EAAa,CAAC;UACVH,WAAA;UACd;QAEF,KAAKlM,KAAA,CAAMoM,IAAA,CAAKjO,KAAA;UACViL,GAAA,EAACpJ,KAAA,CAAMqM,WAAA,EAAa,CAAC;UACXH,WAAA;UACd;MACJ;MAEA,IAAIA,WAAA,EAAa;QAEfxB,KAAA,CAAM4B,cAAA,CAAe;QACrBtM,KAAA,CAAMC,MAAA,CAAO;MACf;IACF;IAEA,SAASsM,uBAAA,EAAyB;MAC5B,IAAAvE,QAAA,CAAS5D,MAAA,IAAU,GAAG;QACZkD,WAAA,CAAAxD,GAAA,CAAIkE,QAAA,CAAS,CAAC,EAAEwE,KAAA,EAAOxE,QAAA,CAAS,CAAC,EAAEyE,KAAK;MAAA,OAC/C;QACC,MAAA/H,CAAA,GAAI,OAAOsD,QAAA,CAAS,CAAC,EAAEwE,KAAA,GAAQxE,QAAA,CAAS,CAAC,EAAEwE,KAAA;QAC3C,MAAA7H,CAAA,GAAI,OAAOqD,QAAA,CAAS,CAAC,EAAEyE,KAAA,GAAQzE,QAAA,CAAS,CAAC,EAAEyE,KAAA;QAErCnF,WAAA,CAAAxD,GAAA,CAAIY,CAAA,EAAGC,CAAC;MACtB;IACF;IAEA,SAAS+H,oBAAA,EAAsB;MACzB,IAAA1E,QAAA,CAAS5D,MAAA,IAAU,GAAG;QACfsD,QAAA,CAAA5D,GAAA,CAAIkE,QAAA,CAAS,CAAC,EAAEwE,KAAA,EAAOxE,QAAA,CAAS,CAAC,EAAEyE,KAAK;MAAA,OAC5C;QACC,MAAA/H,CAAA,GAAI,OAAOsD,QAAA,CAAS,CAAC,EAAEwE,KAAA,GAAQxE,QAAA,CAAS,CAAC,EAAEwE,KAAA;QAC3C,MAAA7H,CAAA,GAAI,OAAOqD,QAAA,CAAS,CAAC,EAAEyE,KAAA,GAAQzE,QAAA,CAAS,CAAC,EAAEyE,KAAA;QAExC/E,QAAA,CAAA5D,GAAA,CAAIY,CAAA,EAAGC,CAAC;MACnB;IACF;IAEA,SAASgI,sBAAA,EAAwB;MAC/B,MAAMC,EAAA,GAAK5E,QAAA,CAAS,CAAC,EAAEwE,KAAA,GAAQxE,QAAA,CAAS,CAAC,EAAEwE,KAAA;MAC3C,MAAMK,EAAA,GAAK7E,QAAA,CAAS,CAAC,EAAEyE,KAAA,GAAQzE,QAAA,CAAS,CAAC,EAAEyE,KAAA;MAC3C,MAAM3D,QAAA,GAAW3L,IAAA,CAAK2P,IAAA,CAAKF,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;MAEjChF,UAAA,CAAA/D,GAAA,CAAI,GAAGgF,QAAQ;IAC5B;IAEA,SAASiE,yBAAA,EAA2B;MAClC,IAAI/M,KAAA,CAAMqK,UAAA,EAAkCsC,qBAAA;MAC5C,IAAI3M,KAAA,CAAMkK,SAAA,EAA+BwC,mBAAA;IAC3C;IAEA,SAASM,4BAAA,EAA8B;MACrC,IAAIhN,KAAA,CAAMqK,UAAA,EAAkCsC,qBAAA;MAC5C,IAAI3M,KAAA,CAAMiN,YAAA,EAAqCV,sBAAA;IACjD;IAEA,SAASW,sBAAsBxC,KAAA,EAAqB;MAC9C,IAAA1C,QAAA,CAAS5D,MAAA,IAAU,GAAG;QACxBoD,SAAA,CAAU1D,GAAA,CAAI4G,KAAA,CAAM8B,KAAA,EAAO9B,KAAA,CAAM+B,KAAK;MAAA,OACjC;QACC,MAAAvN,QAAA,GAAWiO,wBAAA,CAAyBzC,KAAK;QAC/C,MAAMhG,CAAA,GAAI,OAAOgG,KAAA,CAAM8B,KAAA,GAAQtN,QAAA,CAASwF,CAAA;QACxC,MAAMC,CAAA,GAAI,OAAO+F,KAAA,CAAM+B,KAAA,GAAQvN,QAAA,CAASyF,CAAA;QAC9B6C,SAAA,CAAA1D,GAAA,CAAIY,CAAA,EAAGC,CAAC;MACpB;MAEA8C,WAAA,CAAYkE,UAAA,CAAWnE,SAAA,EAAWF,WAAW,EAAEzD,cAAA,CAAe7D,KAAA,CAAM4L,WAAW;MAE/E,MAAMpC,OAAA,GAAUxJ,KAAA,CAAMnC,UAAA;MAEtB,IAAI2L,OAAA,EAAS;QACXpH,UAAA,CAAY,IAAIjF,IAAA,CAAKE,EAAA,GAAKoK,WAAA,CAAY/C,CAAA,GAAK8E,OAAA,CAAQI,YAAY;QAC/DnB,QAAA,CAAU,IAAItL,IAAA,CAAKE,EAAA,GAAKoK,WAAA,CAAY9C,CAAA,GAAK6E,OAAA,CAAQI,YAAY;MAC/D;MACAtC,WAAA,CAAYvG,IAAA,CAAKyG,SAAS;IAC5B;IAEA,SAAS4F,mBAAmB1C,KAAA,EAAqB;MAC3C,IAAA1C,QAAA,CAAS5D,MAAA,IAAU,GAAG;QACxBuD,MAAA,CAAO7D,GAAA,CAAI4G,KAAA,CAAM8B,KAAA,EAAO9B,KAAA,CAAM+B,KAAK;MAAA,OAC9B;QACC,MAAAvN,QAAA,GAAWiO,wBAAA,CAAyBzC,KAAK;QAC/C,MAAMhG,CAAA,GAAI,OAAOgG,KAAA,CAAM8B,KAAA,GAAQtN,QAAA,CAASwF,CAAA;QACxC,MAAMC,CAAA,GAAI,OAAO+F,KAAA,CAAM+B,KAAA,GAAQvN,QAAA,CAASyF,CAAA;QACjCgD,MAAA,CAAA7D,GAAA,CAAIY,CAAA,EAAGC,CAAC;MACjB;MAEAiD,QAAA,CAAS+D,UAAA,CAAWhE,MAAA,EAAQD,QAAQ,EAAE7D,cAAA,CAAe7D,KAAA,CAAM+L,QAAQ;MAC/D3C,GAAA,CAAAxB,QAAA,CAASlD,CAAA,EAAGkD,QAAA,CAASjD,CAAC;MAC1B+C,QAAA,CAAS3G,IAAA,CAAK4G,MAAM;IACtB;IAEA,SAAS0F,qBAAqB3C,KAAA,EAAqB;MAC3C,MAAAxL,QAAA,GAAWiO,wBAAA,CAAyBzC,KAAK;MACzC,MAAAkC,EAAA,GAAKlC,KAAA,CAAM8B,KAAA,GAAQtN,QAAA,CAASwF,CAAA;MAC5B,MAAAmI,EAAA,GAAKnC,KAAA,CAAM+B,KAAA,GAAQvN,QAAA,CAASyF,CAAA;MAClC,MAAMmE,QAAA,GAAW3L,IAAA,CAAK2P,IAAA,CAAKF,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;MAEnC/E,QAAA,CAAAhE,GAAA,CAAI,GAAGgF,QAAQ;MACbf,UAAA,CAAAjE,GAAA,CAAI,GAAG3G,IAAA,CAAKiL,GAAA,CAAIN,QAAA,CAASnD,CAAA,GAAIkD,UAAA,CAAWlD,CAAA,EAAG3E,KAAA,CAAMqI,SAAS,CAAC;MACtEiC,QAAA,CAASvC,UAAA,CAAWpD,CAAC;MACrBkD,UAAA,CAAW9G,IAAA,CAAK+G,QAAQ;IAC1B;IAEA,SAASwF,wBAAwB5C,KAAA,EAAqB;MACpD,IAAI1K,KAAA,CAAMqK,UAAA,EAAYgD,oBAAA,CAAqB3C,KAAK;MAChD,IAAI1K,KAAA,CAAMkK,SAAA,EAAWkD,kBAAA,CAAmB1C,KAAK;IAC/C;IAEA,SAAS6C,2BAA2B7C,KAAA,EAAqB;MACvD,IAAI1K,KAAA,CAAMqK,UAAA,EAAYgD,oBAAA,CAAqB3C,KAAK;MAChD,IAAI1K,KAAA,CAAMiN,YAAA,EAAcC,qBAAA,CAAsBxC,KAAK;IACrD;IAMA,SAASxE,cAAcwE,KAAA,EAAqB;;MAC1C,IAAI1K,KAAA,CAAMwN,OAAA,KAAY,OAAO;MAEzB,IAAAxF,QAAA,CAAS5D,MAAA,KAAW,GAAG;QACzB,CAAAkC,EAAA,GAAAtG,KAAA,CAAMnC,UAAA,KAAN,gBAAAyI,EAAA,CAAkBK,aAAA,CAAclG,gBAAA,CAAiB,eAAemG,aAAA;QAChE,CAAAL,EAAA,GAAAvG,KAAA,CAAMnC,UAAA,KAAN,gBAAA0I,EAAA,CAAkBI,aAAA,CAAclG,gBAAA,CAAiB,aAAa0F,WAAA;MAChE;MAEAsH,UAAA,CAAW/C,KAAK;MAEZ,IAAAA,KAAA,CAAMgD,WAAA,KAAgB,SAAS;QACjCC,YAAA,CAAajD,KAAK;MAAA,OACb;QACLkD,WAAA,CAAYlD,KAAK;MACnB;IACF;IAEA,SAAS9D,cAAc8D,KAAA,EAAqB;MAC1C,IAAI1K,KAAA,CAAMwN,OAAA,KAAY,OAAO;MAEzB,IAAA9C,KAAA,CAAMgD,WAAA,KAAgB,SAAS;QACjCG,WAAA,CAAYnD,KAAK;MAAA,OACZ;QACLoD,WAAA,CAAYpD,KAAK;MACnB;IACF;IAEA,SAASvE,YAAYuE,KAAA,EAAqB;;MACxCqD,aAAA,CAAcrD,KAAK;MAEf,IAAA1C,QAAA,CAAS5D,MAAA,KAAW,GAAG;QACnB,CAAAkC,EAAA,GAAAtG,KAAA,CAAAnC,UAAA,qBAAAyI,EAAA,CAAY0H,qBAAA,CAAsBtD,KAAA,CAAMuD,SAAA;QAE9C,CAAA1H,EAAA,GAAAvG,KAAA,CAAMnC,UAAA,KAAN,gBAAA0I,EAAA,CAAkBI,aAAA,CAAc9F,mBAAA,CAAoB,eAAe+F,aAAA;QACnE,CAAAJ,EAAA,GAAAxG,KAAA,CAAMnC,UAAA,KAAN,gBAAA2I,EAAA,CAAkBG,aAAA,CAAc9F,mBAAA,CAAoB,aAAasF,WAAA;MACnE;MAGAnG,KAAA,CAAMkB,aAAA,CAAc8F,QAAQ;MAE5B5F,KAAA,GAAQC,KAAA,CAAMC,IAAA;IAChB;IAEA,SAASsM,YAAYlD,KAAA,EAAmB;MAClC,IAAAwD,WAAA;MAEJ,QAAQxD,KAAA,CAAMyD,MAAA;QACZ,KAAK;UACHD,WAAA,GAAclO,KAAA,CAAMoO,YAAA,CAAanQ,IAAA;UACjC;QAEF,KAAK;UACHiQ,WAAA,GAAclO,KAAA,CAAMoO,YAAA,CAAa7P,MAAA;UACjC;QAEF,KAAK;UACH2P,WAAA,GAAclO,KAAA,CAAMoO,YAAA,CAAajQ,KAAA;UACjC;QAEF;UACgB+P,WAAA;MAClB;MAEA,QAAQA,WAAA;QACN,KAAK7P,KAAA,CAAMG,KAAA;UACT,IAAIwB,KAAA,CAAMqK,UAAA,KAAe,OAAO;UAChCmB,oBAAA,CAAqBd,KAAK;UAC1BtJ,KAAA,GAAQC,KAAA,CAAM7C,KAAA;UACd;QAEF,KAAKH,KAAA,CAAMC,MAAA;UACT,IAAIoM,KAAA,CAAM2D,OAAA,IAAW3D,KAAA,CAAM4D,OAAA,IAAW5D,KAAA,CAAM6D,QAAA,EAAU;YACpD,IAAIvO,KAAA,CAAMkK,SAAA,KAAc,OAAO;YAC/BuB,kBAAA,CAAmBf,KAAK;YACxBtJ,KAAA,GAAQC,KAAA,CAAM5C,GAAA;UAAA,OACT;YACL,IAAIuB,KAAA,CAAMiN,YAAA,KAAiB,OAAO;YAClC1B,qBAAA,CAAsBb,KAAK;YAC3BtJ,KAAA,GAAQC,KAAA,CAAM/C,MAAA;UAChB;UACA;QAEF,KAAKD,KAAA,CAAMI,GAAA;UACT,IAAIiM,KAAA,CAAM2D,OAAA,IAAW3D,KAAA,CAAM4D,OAAA,IAAW5D,KAAA,CAAM6D,QAAA,EAAU;YACpD,IAAIvO,KAAA,CAAMiN,YAAA,KAAiB,OAAO;YAClC1B,qBAAA,CAAsBb,KAAK;YAC3BtJ,KAAA,GAAQC,KAAA,CAAM/C,MAAA;UAAA,OACT;YACL,IAAI0B,KAAA,CAAMkK,SAAA,KAAc,OAAO;YAC/BuB,kBAAA,CAAmBf,KAAK;YACxBtJ,KAAA,GAAQC,KAAA,CAAM5C,GAAA;UAChB;UACA;QAEF;UACE2C,KAAA,GAAQC,KAAA,CAAMC,IAAA;MAClB;MAEI,IAAAF,KAAA,KAAUC,KAAA,CAAMC,IAAA,EAAM;QAExBtB,KAAA,CAAMkB,aAAA,CAAc6F,UAAU;MAChC;IACF;IAEA,SAAS+G,YAAYpD,KAAA,EAAmB;MACtC,IAAI1K,KAAA,CAAMwN,OAAA,KAAY,OAAO;MAE7B,QAAQpM,KAAA;QACN,KAAKC,KAAA,CAAM/C,MAAA;UACT,IAAI0B,KAAA,CAAMiN,YAAA,KAAiB,OAAO;UAClCvB,qBAAA,CAAsBhB,KAAK;UAC3B;QAEF,KAAKrJ,KAAA,CAAM7C,KAAA;UACT,IAAIwB,KAAA,CAAMqK,UAAA,KAAe,OAAO;UAChCwB,oBAAA,CAAqBnB,KAAK;UAC1B;QAEF,KAAKrJ,KAAA,CAAM5C,GAAA;UACT,IAAIuB,KAAA,CAAMkK,SAAA,KAAc,OAAO;UAC/B4B,kBAAA,CAAmBpB,KAAK;UACxB;MACJ;IACF;IAEA,SAAStE,aAAasE,KAAA,EAAmB;MACnC,IAAA1K,KAAA,CAAMwN,OAAA,KAAY,SAASxN,KAAA,CAAMqK,UAAA,KAAe,SAAUjJ,KAAA,KAAUC,KAAA,CAAMC,IAAA,IAAQF,KAAA,KAAUC,KAAA,CAAM/C,MAAA,EAAS;QAC7G;MACF;MAEAoM,KAAA,CAAM4B,cAAA,CAAe;MAGrBtM,KAAA,CAAMkB,aAAA,CAAc6F,UAAU;MAE9BiF,gBAAA,CAAiBtB,KAAK;MAGtB1K,KAAA,CAAMkB,aAAA,CAAc8F,QAAQ;IAC9B;IAEA,SAAStG,UAAUgK,KAAA,EAAsB;MACvC,IAAI1K,KAAA,CAAMwN,OAAA,KAAY,SAASxN,KAAA,CAAMkK,SAAA,KAAc,OAAO;MAC1D+B,aAAA,CAAcvB,KAAK;IACrB;IAEA,SAASiD,aAAajD,KAAA,EAAqB;MACzC8D,YAAA,CAAa9D,KAAK;MAElB,QAAQ1C,QAAA,CAAS5D,MAAA;QACf,KAAK;UACK,QAAApE,KAAA,CAAMyO,OAAA,CAAQ/P,GAAA;YACpB,KAAKC,KAAA,CAAML,MAAA;cACT,IAAI0B,KAAA,CAAMiN,YAAA,KAAiB,OAAO;cACXV,sBAAA;cACvBnL,KAAA,GAAQC,KAAA,CAAM4F,YAAA;cACd;YAEF,KAAKtI,KAAA,CAAMF,GAAA;cACT,IAAIuB,KAAA,CAAMkK,SAAA,KAAc,OAAO;cACXwC,mBAAA;cACpBtL,KAAA,GAAQC,KAAA,CAAM6F,SAAA;cACd;YAEF;cACE9F,KAAA,GAAQC,KAAA,CAAMC,IAAA;UAClB;UAEA;QAEF,KAAK;UACK,QAAAtB,KAAA,CAAMyO,OAAA,CAAQ7P,GAAA;YACpB,KAAKD,KAAA,CAAME,SAAA;cACT,IAAImB,KAAA,CAAMqK,UAAA,KAAe,SAASrK,KAAA,CAAMkK,SAAA,KAAc,OAAO;cACpC6C,wBAAA;cACzB3L,KAAA,GAAQC,KAAA,CAAM8F,eAAA;cACd;YAEF,KAAKxI,KAAA,CAAM+P,YAAA;cACT,IAAI1O,KAAA,CAAMqK,UAAA,KAAe,SAASrK,KAAA,CAAMiN,YAAA,KAAiB,OAAO;cACpCD,2BAAA;cAC5B5L,KAAA,GAAQC,KAAA,CAAM+F,kBAAA;cACd;YAEF;cACEhG,KAAA,GAAQC,KAAA,CAAMC,IAAA;UAClB;UAEA;QAEF;UACEF,KAAA,GAAQC,KAAA,CAAMC,IAAA;MAClB;MAEI,IAAAF,KAAA,KAAUC,KAAA,CAAMC,IAAA,EAAM;QAExBtB,KAAA,CAAMkB,aAAA,CAAc6F,UAAU;MAChC;IACF;IAEA,SAAS8G,YAAYnD,KAAA,EAAqB;MACxC8D,YAAA,CAAa9D,KAAK;MAElB,QAAQtJ,KAAA;QACN,KAAKC,KAAA,CAAM4F,YAAA;UACT,IAAIjH,KAAA,CAAMiN,YAAA,KAAiB,OAAO;UAClCC,qBAAA,CAAsBxC,KAAK;UAC3B1K,KAAA,CAAMC,MAAA,CAAO;UACb;QAEF,KAAKoB,KAAA,CAAM6F,SAAA;UACT,IAAIlH,KAAA,CAAMkK,SAAA,KAAc,OAAO;UAC/BkD,kBAAA,CAAmB1C,KAAK;UACxB1K,KAAA,CAAMC,MAAA,CAAO;UACb;QAEF,KAAKoB,KAAA,CAAM8F,eAAA;UACT,IAAInH,KAAA,CAAMqK,UAAA,KAAe,SAASrK,KAAA,CAAMkK,SAAA,KAAc,OAAO;UAC7DoD,uBAAA,CAAwB5C,KAAK;UAC7B1K,KAAA,CAAMC,MAAA,CAAO;UACb;QAEF,KAAKoB,KAAA,CAAM+F,kBAAA;UACT,IAAIpH,KAAA,CAAMqK,UAAA,KAAe,SAASrK,KAAA,CAAMiN,YAAA,KAAiB,OAAO;UAChEM,0BAAA,CAA2B7C,KAAK;UAChC1K,KAAA,CAAMC,MAAA,CAAO;UACb;QAEF;UACEmB,KAAA,GAAQC,KAAA,CAAMC,IAAA;MAClB;IACF;IAEA,SAAS2E,cAAcyE,KAAA,EAAc;MACnC,IAAI1K,KAAA,CAAMwN,OAAA,KAAY,OAAO;MAC7B9C,KAAA,CAAM4B,cAAA,CAAe;IACvB;IAEA,SAASmB,WAAW/C,KAAA,EAAqB;MACvC1C,QAAA,CAAS2G,IAAA,CAAKjE,KAAK;IACrB;IAEA,SAASqD,cAAcrD,KAAA,EAAqB;MACnC,OAAAzC,gBAAA,CAAiByC,KAAA,CAAMuD,SAAS;MAEvC,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAI5G,QAAA,CAAS5D,MAAA,EAAQwK,CAAA,IAAK;QACxC,IAAI5G,QAAA,CAAS4G,CAAC,EAAEX,SAAA,IAAavD,KAAA,CAAMuD,SAAA,EAAW;UACnCjG,QAAA,CAAA6G,MAAA,CAAOD,CAAA,EAAG,CAAC;UACpB;QACF;MACF;IACF;IAEA,SAASJ,aAAa9D,KAAA,EAAqB;MACrC,IAAAxL,QAAA,GAAW+I,gBAAA,CAAiByC,KAAA,CAAMuD,SAAS;MAE/C,IAAI/O,QAAA,KAAa,QAAW;QAC1BA,QAAA,GAAW,IAAIqI,OAAA;QACEU,gBAAA,CAAAyC,KAAA,CAAMuD,SAAS,IAAI/O,QAAA;MACtC;MAEAA,QAAA,CAAS4E,GAAA,CAAI4G,KAAA,CAAM8B,KAAA,EAAO9B,KAAA,CAAM+B,KAAK;IACvC;IAEA,SAASU,yBAAyBzC,KAAA,EAAqB;MAC/C,MAAAoE,OAAA,GAAUpE,KAAA,CAAMuD,SAAA,KAAcjG,QAAA,CAAS,CAAC,EAAEiG,SAAA,GAAYjG,QAAA,CAAS,CAAC,IAAIA,QAAA,CAAS,CAAC;MAC7E,OAAAC,gBAAA,CAAiB6G,OAAA,CAAQb,SAAS;IAC3C;IAIA,KAAKzD,OAAA,GAAU,CAACD,UAAA,GAAapC,YAAA,OAAmB;MAC9CqC,OAAA,CAAQD,UAAU;MAClBvK,KAAA,CAAMC,MAAA,CAAO;IAAA;IAGf,KAAKqK,QAAA,GAAW,CAACC,UAAA,GAAapC,YAAA,OAAmB;MAC/CmC,QAAA,CAASC,UAAU;MACnBvK,KAAA,CAAMC,MAAA,CAAO;IAAA;IAGf,KAAK8O,QAAA,GAAW,MAAM;MACb,OAAAvL,KAAA;IAAA;IAGJ,KAAA2G,QAAA,GAAYC,QAAA,IAAa;MAC5BD,QAAA,CAASC,QAAQ;MACjBpK,KAAA,CAAMC,MAAA,CAAO;IAAA;IAGf,KAAKkI,YAAA,GAAe,MAAM;MACxB,OAAOA,YAAA,CAAa;IAAA;IAItB,IAAItK,UAAA,KAAe,QAAW,KAAKiI,OAAA,CAAQjI,UAAU;IAErD,KAAKoC,MAAA,CAAO;EACd;AACF;AAUA,MAAM+O,WAAA,SAAoBvR,aAAA,CAAc;EACtCE,YAAYC,MAAA,EAAgDC,UAAA,EAA0B;IACpF,MAAMD,MAAA,EAAQC,UAAU;IAExB,KAAKqH,kBAAA,GAAqB;IAErB,KAAAkJ,YAAA,CAAanQ,IAAA,GAAOI,KAAA,CAAMI,GAAA;IAC1B,KAAA2P,YAAA,CAAajQ,KAAA,GAAQE,KAAA,CAAMC,MAAA;IAE3B,KAAAmQ,OAAA,CAAQ/P,GAAA,GAAMC,KAAA,CAAMF,GAAA;IACpB,KAAAgQ,OAAA,CAAQ7P,GAAA,GAAMD,KAAA,CAAM+P,YAAA;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}