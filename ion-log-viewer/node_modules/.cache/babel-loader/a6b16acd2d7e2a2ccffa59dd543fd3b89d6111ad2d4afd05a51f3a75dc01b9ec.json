{"ast": null, "code": "import { ensureIndex, getFullGeometryRange, getRootIndexRanges, getTriCount, hasGroupGaps } from './geometryUtils.js';\nimport { getBounds, computeTriangleBounds } from './computeBoundsUtils.js';\nimport { getOptimalSplit } from './splitUtils.js';\nimport { MeshBVHNode } from '../MeshBVHNode.js';\nimport { BYTES_PER_NODE } from '../Constants.js';\nimport { partition } from './sortUtils.generated.js';\nimport { partition_indirect } from './sortUtils_indirect.generated.js';\nimport { countNodes, populateBuffer } from './buildUtils.js';\nexport function generateIndirectBuffer(geometry, useSharedArrayBuffer) {\n  const triCount = (geometry.index ? geometry.index.count : geometry.attributes.position.count) / 3;\n  const useUint32 = triCount > 2 ** 16;\n  const byteCount = useUint32 ? 4 : 2;\n  const buffer = useSharedArrayBuffer ? new SharedArrayBuffer(triCount * byteCount) : new ArrayBuffer(triCount * byteCount);\n  const indirectBuffer = useUint32 ? new Uint32Array(buffer) : new Uint16Array(buffer);\n  for (let i = 0, l = indirectBuffer.length; i < l; i++) {\n    indirectBuffer[i] = i;\n  }\n  return indirectBuffer;\n}\nexport function buildTree(bvh, triangleBounds, offset, count, options) {\n  // epxand variables\n  const {\n    maxDepth,\n    verbose,\n    maxLeafTris,\n    strategy,\n    onProgress,\n    indirect\n  } = options;\n  const indirectBuffer = bvh._indirectBuffer;\n  const geometry = bvh.geometry;\n  const indexArray = geometry.index ? geometry.index.array : null;\n  const partionFunc = indirect ? partition_indirect : partition;\n\n  // generate intermediate variables\n  const totalTriangles = getTriCount(geometry);\n  const cacheCentroidBoundingData = new Float32Array(6);\n  let reachedMaxDepth = false;\n  const root = new MeshBVHNode();\n  getBounds(triangleBounds, offset, count, root.boundingData, cacheCentroidBoundingData);\n  splitNode(root, offset, count, cacheCentroidBoundingData);\n  return root;\n  function triggerProgress(trianglesProcessed) {\n    if (onProgress) {\n      onProgress(trianglesProcessed / totalTriangles);\n    }\n  }\n\n  // either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n  // recording the offset and count of its triangles and writing them into the reordered geometry index.\n  function splitNode(node, offset, count, centroidBoundingData = null, depth = 0) {\n    if (!reachedMaxDepth && depth >= maxDepth) {\n      reachedMaxDepth = true;\n      if (verbose) {\n        console.warn(`MeshBVH: Max depth of ${maxDepth} reached when generating BVH. Consider increasing maxDepth.`);\n        console.warn(geometry);\n      }\n    }\n\n    // early out if we've met our capacity\n    if (count <= maxLeafTris || depth >= maxDepth) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n      return node;\n    }\n\n    // Find where to split the volume\n    const split = getOptimalSplit(node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy);\n    if (split.axis === -1) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n      return node;\n    }\n    const splitOffset = partionFunc(indirectBuffer, indexArray, triangleBounds, offset, count, split);\n\n    // create the two new child nodes\n    if (splitOffset === offset || splitOffset === offset + count) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n    } else {\n      node.splitAxis = split.axis;\n\n      // create the left child and compute its bounding box\n      const left = new MeshBVHNode();\n      const lstart = offset;\n      const lcount = splitOffset - offset;\n      node.left = left;\n      getBounds(triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData);\n      splitNode(left, lstart, lcount, cacheCentroidBoundingData, depth + 1);\n\n      // repeat for right\n      const right = new MeshBVHNode();\n      const rstart = splitOffset;\n      const rcount = count - lcount;\n      node.right = right;\n      getBounds(triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData);\n      splitNode(right, rstart, rcount, cacheCentroidBoundingData, depth + 1);\n    }\n    return node;\n  }\n}\nexport function buildPackedTree(bvh, options) {\n  const geometry = bvh.geometry;\n  if (options.indirect) {\n    bvh._indirectBuffer = generateIndirectBuffer(geometry, options.useSharedArrayBuffer);\n    if (hasGroupGaps(geometry, options.range) && !options.verbose) {\n      console.warn('MeshBVH: Provided geometry contains groups or a range that do not fully span the vertex contents while using the \"indirect\" option. ' + 'BVH may incorrectly report intersections on unrendered portions of the geometry.');\n    }\n  }\n  if (!bvh._indirectBuffer) {\n    ensureIndex(geometry, options);\n  }\n  const BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n  const triangleBounds = computeTriangleBounds(geometry);\n  const geometryRanges = options.indirect ? getFullGeometryRange(geometry, options.range) : getRootIndexRanges(geometry, options.range);\n  bvh._roots = geometryRanges.map(range => {\n    const root = buildTree(bvh, triangleBounds, range.offset, range.count, options);\n    const nodeCount = countNodes(root);\n    const buffer = new BufferConstructor(BYTES_PER_NODE * nodeCount);\n    populateBuffer(0, root, buffer);\n    return buffer;\n  });\n}", "map": {"version": 3, "names": ["ensureIndex", "getFullGeometryRange", "getRootIndexRanges", "getTriCount", "hasGroupGaps", "getBounds", "computeTriangleBounds", "getOptimalSplit", "MeshBVHNode", "BYTES_PER_NODE", "partition", "partition_indirect", "countNodes", "populate<PERSON><PERSON>er", "generateIndirectBuffer", "geometry", "useSharedArrayBuffer", "triCount", "index", "count", "attributes", "position", "useUint32", "byteCount", "buffer", "SharedArrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Uint32Array", "Uint16Array", "i", "l", "length", "buildTree", "bvh", "triangleBounds", "offset", "options", "max<PERSON><PERSON><PERSON>", "verbose", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "strategy", "onProgress", "indirect", "_<PERSON><PERSON><PERSON>er", "indexArray", "array", "partionFunc", "totalTriangles", "cacheCentroidBoundingData", "Float32Array", "reachedMaxDepth", "root", "boundingData", "splitNode", "triggerProgress", "trianglesProcessed", "node", "centroidBoundingData", "depth", "console", "warn", "split", "axis", "splitOffset", "splitAxis", "left", "lstart", "lcount", "right", "rstart", "rcount", "buildPackedTree", "range", "BufferConstructor", "geometryRanges", "_roots", "map", "nodeCount"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/build/buildTree.js"], "sourcesContent": ["import { ensureIndex, getFullGeometryRange, getRootIndexRanges, getTriCount, hasGroupGaps, } from './geometryUtils.js';\nimport { getBounds, computeTriangleBounds } from './computeBoundsUtils.js';\nimport { getOptimalSplit } from './splitUtils.js';\nimport { MeshBVHNode } from '../MeshBVHNode.js';\nimport { BYTES_PER_NODE } from '../Constants.js';\n\nimport { partition } from './sortUtils.generated.js';\nimport { partition_indirect } from './sortUtils_indirect.generated.js';\nimport { countNodes, populateBuffer } from './buildUtils.js';\n\nexport function generateIndirectBuffer( geometry, useSharedArrayBuffer ) {\n\n\tconst triCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\tconst useUint32 = triCount > 2 ** 16;\n\tconst byteCount = useUint32 ? 4 : 2;\n\n\tconst buffer = useSharedArrayBuffer ? new SharedArrayBuffer( triCount * byteCount ) : new ArrayBuffer( triCount * byteCount );\n\tconst indirectBuffer = useUint32 ? new Uint32Array( buffer ) : new Uint16Array( buffer );\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tindirectBuffer[ i ] = i;\n\n\t}\n\n\treturn indirectBuffer;\n\n}\n\nexport function buildTree( bvh, triangleBounds, offset, count, options ) {\n\n\t// epxand variables\n\tconst {\n\t\tmaxDepth,\n\t\tverbose,\n\t\tmaxLeafTris,\n\t\tstrategy,\n\t\tonProgress,\n\t\tindirect,\n\t} = options;\n\tconst indirectBuffer = bvh._indirectBuffer;\n\tconst geometry = bvh.geometry;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tconst partionFunc = indirect ? partition_indirect : partition;\n\n\t// generate intermediate variables\n\tconst totalTriangles = getTriCount( geometry );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tlet reachedMaxDepth = false;\n\n\tconst root = new MeshBVHNode();\n\tgetBounds( triangleBounds, offset, count, root.boundingData, cacheCentroidBoundingData );\n\tsplitNode( root, offset, count, cacheCentroidBoundingData );\n\treturn root;\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geometry );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partionFunc( indirectBuffer, indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n}\n\nexport function buildPackedTree( bvh, options ) {\n\n\tconst geometry = bvh.geometry;\n\tif ( options.indirect ) {\n\n\t\tbvh._indirectBuffer = generateIndirectBuffer( geometry, options.useSharedArrayBuffer );\n\n\t\tif ( hasGroupGaps( geometry, options.range ) && ! options.verbose ) {\n\n\t\t\tconsole.warn(\n\t\t\t\t'MeshBVH: Provided geometry contains groups or a range that do not fully span the vertex contents while using the \"indirect\" option. ' +\n\t\t\t\t'BVH may incorrectly report intersections on unrendered portions of the geometry.'\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\tif ( ! bvh._indirectBuffer ) {\n\n\t\tensureIndex( geometry, options );\n\n\t}\n\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\n\tconst triangleBounds = computeTriangleBounds( geometry );\n\tconst geometryRanges = options.indirect ? getFullGeometryRange( geometry, options.range ) : getRootIndexRanges( geometry, options.range );\n\tbvh._roots = geometryRanges.map( range => {\n\n\t\tconst root = buildTree( bvh, triangleBounds, range.offset, range.count, options );\n\t\tconst nodeCount = countNodes( root );\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tpopulateBuffer( 0, root, buffer );\n\t\treturn buffer;\n\n\t} );\n\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,YAAY,QAAS,oBAAoB;AACtH,SAASC,SAAS,EAAEC,qBAAqB,QAAQ,yBAAyB;AAC1E,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,cAAc,QAAQ,iBAAiB;AAEhD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,UAAU,EAAEC,cAAc,QAAQ,iBAAiB;AAE5D,OAAO,SAASC,sBAAsBA,CAAEC,QAAQ,EAAEC,oBAAoB,EAAG;EAExE,MAAMC,QAAQ,GAAG,CAAEF,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAACC,KAAK,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACF,KAAK,IAAK,CAAC;EACnG,MAAMG,SAAS,GAAGL,QAAQ,GAAG,CAAC,IAAI,EAAE;EACpC,MAAMM,SAAS,GAAGD,SAAS,GAAG,CAAC,GAAG,CAAC;EAEnC,MAAME,MAAM,GAAGR,oBAAoB,GAAG,IAAIS,iBAAiB,CAAER,QAAQ,GAAGM,SAAU,CAAC,GAAG,IAAIG,WAAW,CAAET,QAAQ,GAAGM,SAAU,CAAC;EAC7H,MAAMI,cAAc,GAAGL,SAAS,GAAG,IAAIM,WAAW,CAAEJ,MAAO,CAAC,GAAG,IAAIK,WAAW,CAAEL,MAAO,CAAC;EACxF,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,cAAc,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;IAEzDH,cAAc,CAAEG,CAAC,CAAE,GAAGA,CAAC;EAExB;EAEA,OAAOH,cAAc;AAEtB;AAEA,OAAO,SAASM,SAASA,CAAEC,GAAG,EAAEC,cAAc,EAAEC,MAAM,EAAEjB,KAAK,EAAEkB,OAAO,EAAG;EAExE;EACA,MAAM;IACLC,QAAQ;IACRC,OAAO;IACPC,WAAW;IACXC,QAAQ;IACRC,UAAU;IACVC;EACD,CAAC,GAAGN,OAAO;EACX,MAAMV,cAAc,GAAGO,GAAG,CAACU,eAAe;EAC1C,MAAM7B,QAAQ,GAAGmB,GAAG,CAACnB,QAAQ;EAC7B,MAAM8B,UAAU,GAAG9B,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC4B,KAAK,GAAG,IAAI;EAC/D,MAAMC,WAAW,GAAGJ,QAAQ,GAAGhC,kBAAkB,GAAGD,SAAS;;EAE7D;EACA,MAAMsC,cAAc,GAAG7C,WAAW,CAAEY,QAAS,CAAC;EAC9C,MAAMkC,yBAAyB,GAAG,IAAIC,YAAY,CAAE,CAAE,CAAC;EACvD,IAAIC,eAAe,GAAG,KAAK;EAE3B,MAAMC,IAAI,GAAG,IAAI5C,WAAW,CAAC,CAAC;EAC9BH,SAAS,CAAE8B,cAAc,EAAEC,MAAM,EAAEjB,KAAK,EAAEiC,IAAI,CAACC,YAAY,EAAEJ,yBAA0B,CAAC;EACxFK,SAAS,CAAEF,IAAI,EAAEhB,MAAM,EAAEjB,KAAK,EAAE8B,yBAA0B,CAAC;EAC3D,OAAOG,IAAI;EAEX,SAASG,eAAeA,CAAEC,kBAAkB,EAAG;IAE9C,IAAKd,UAAU,EAAG;MAEjBA,UAAU,CAAEc,kBAAkB,GAAGR,cAAe,CAAC;IAElD;EAED;;EAEA;EACA;EACA,SAASM,SAASA,CAAEG,IAAI,EAAErB,MAAM,EAAEjB,KAAK,EAAEuC,oBAAoB,GAAG,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAG;IAEjF,IAAK,CAAER,eAAe,IAAIQ,KAAK,IAAIrB,QAAQ,EAAG;MAE7Ca,eAAe,GAAG,IAAI;MACtB,IAAKZ,OAAO,EAAG;QAEdqB,OAAO,CAACC,IAAI,CAAE,yBAA0BvB,QAAQ,6DAA+D,CAAC;QAChHsB,OAAO,CAACC,IAAI,CAAE9C,QAAS,CAAC;MAEzB;IAED;;IAEA;IACA,IAAKI,KAAK,IAAIqB,WAAW,IAAImB,KAAK,IAAIrB,QAAQ,EAAG;MAEhDiB,eAAe,CAAEnB,MAAM,GAAGjB,KAAM,CAAC;MACjCsC,IAAI,CAACrB,MAAM,GAAGA,MAAM;MACpBqB,IAAI,CAACtC,KAAK,GAAGA,KAAK;MAClB,OAAOsC,IAAI;IAEZ;;IAEA;IACA,MAAMK,KAAK,GAAGvD,eAAe,CAAEkD,IAAI,CAACJ,YAAY,EAAEK,oBAAoB,EAAEvB,cAAc,EAAEC,MAAM,EAAEjB,KAAK,EAAEsB,QAAS,CAAC;IACjH,IAAKqB,KAAK,CAACC,IAAI,KAAK,CAAE,CAAC,EAAG;MAEzBR,eAAe,CAAEnB,MAAM,GAAGjB,KAAM,CAAC;MACjCsC,IAAI,CAACrB,MAAM,GAAGA,MAAM;MACpBqB,IAAI,CAACtC,KAAK,GAAGA,KAAK;MAClB,OAAOsC,IAAI;IAEZ;IAEA,MAAMO,WAAW,GAAGjB,WAAW,CAAEpB,cAAc,EAAEkB,UAAU,EAAEV,cAAc,EAAEC,MAAM,EAAEjB,KAAK,EAAE2C,KAAM,CAAC;;IAEnG;IACA,IAAKE,WAAW,KAAK5B,MAAM,IAAI4B,WAAW,KAAK5B,MAAM,GAAGjB,KAAK,EAAG;MAE/DoC,eAAe,CAAEnB,MAAM,GAAGjB,KAAM,CAAC;MACjCsC,IAAI,CAACrB,MAAM,GAAGA,MAAM;MACpBqB,IAAI,CAACtC,KAAK,GAAGA,KAAK;IAEnB,CAAC,MAAM;MAENsC,IAAI,CAACQ,SAAS,GAAGH,KAAK,CAACC,IAAI;;MAE3B;MACA,MAAMG,IAAI,GAAG,IAAI1D,WAAW,CAAC,CAAC;MAC9B,MAAM2D,MAAM,GAAG/B,MAAM;MACrB,MAAMgC,MAAM,GAAGJ,WAAW,GAAG5B,MAAM;MACnCqB,IAAI,CAACS,IAAI,GAAGA,IAAI;MAEhB7D,SAAS,CAAE8B,cAAc,EAAEgC,MAAM,EAAEC,MAAM,EAAEF,IAAI,CAACb,YAAY,EAAEJ,yBAA0B,CAAC;MACzFK,SAAS,CAAEY,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEnB,yBAAyB,EAAEU,KAAK,GAAG,CAAE,CAAC;;MAEvE;MACA,MAAMU,KAAK,GAAG,IAAI7D,WAAW,CAAC,CAAC;MAC/B,MAAM8D,MAAM,GAAGN,WAAW;MAC1B,MAAMO,MAAM,GAAGpD,KAAK,GAAGiD,MAAM;MAC7BX,IAAI,CAACY,KAAK,GAAGA,KAAK;MAElBhE,SAAS,CAAE8B,cAAc,EAAEmC,MAAM,EAAEC,MAAM,EAAEF,KAAK,CAAChB,YAAY,EAAEJ,yBAA0B,CAAC;MAC1FK,SAAS,CAAEe,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEtB,yBAAyB,EAAEU,KAAK,GAAG,CAAE,CAAC;IAEzE;IAEA,OAAOF,IAAI;EAEZ;AAED;AAEA,OAAO,SAASe,eAAeA,CAAEtC,GAAG,EAAEG,OAAO,EAAG;EAE/C,MAAMtB,QAAQ,GAAGmB,GAAG,CAACnB,QAAQ;EAC7B,IAAKsB,OAAO,CAACM,QAAQ,EAAG;IAEvBT,GAAG,CAACU,eAAe,GAAG9B,sBAAsB,CAAEC,QAAQ,EAAEsB,OAAO,CAACrB,oBAAqB,CAAC;IAEtF,IAAKZ,YAAY,CAAEW,QAAQ,EAAEsB,OAAO,CAACoC,KAAM,CAAC,IAAI,CAAEpC,OAAO,CAACE,OAAO,EAAG;MAEnEqB,OAAO,CAACC,IAAI,CACX,sIAAsI,GACtI,kFACD,CAAC;IAEF;EAED;EAEA,IAAK,CAAE3B,GAAG,CAACU,eAAe,EAAG;IAE5B5C,WAAW,CAAEe,QAAQ,EAAEsB,OAAQ,CAAC;EAEjC;EAEA,MAAMqC,iBAAiB,GAAGrC,OAAO,CAACrB,oBAAoB,GAAGS,iBAAiB,GAAGC,WAAW;EAExF,MAAMS,cAAc,GAAG7B,qBAAqB,CAAES,QAAS,CAAC;EACxD,MAAM4D,cAAc,GAAGtC,OAAO,CAACM,QAAQ,GAAG1C,oBAAoB,CAAEc,QAAQ,EAAEsB,OAAO,CAACoC,KAAM,CAAC,GAAGvE,kBAAkB,CAAEa,QAAQ,EAAEsB,OAAO,CAACoC,KAAM,CAAC;EACzIvC,GAAG,CAAC0C,MAAM,GAAGD,cAAc,CAACE,GAAG,CAAEJ,KAAK,IAAI;IAEzC,MAAMrB,IAAI,GAAGnB,SAAS,CAAEC,GAAG,EAAEC,cAAc,EAAEsC,KAAK,CAACrC,MAAM,EAAEqC,KAAK,CAACtD,KAAK,EAAEkB,OAAQ,CAAC;IACjF,MAAMyC,SAAS,GAAGlE,UAAU,CAAEwC,IAAK,CAAC;IACpC,MAAM5B,MAAM,GAAG,IAAIkD,iBAAiB,CAAEjE,cAAc,GAAGqE,SAAU,CAAC;IAClEjE,cAAc,CAAE,CAAC,EAAEuC,IAAI,EAAE5B,MAAO,CAAC;IACjC,OAAOA,MAAM;EAEd,CAAE,CAAC;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}