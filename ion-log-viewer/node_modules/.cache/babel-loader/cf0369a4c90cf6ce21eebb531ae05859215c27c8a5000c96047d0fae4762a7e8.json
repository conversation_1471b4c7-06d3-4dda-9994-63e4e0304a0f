{"ast": null, "code": "import { Scene, Camera, Mesh, PlaneGeometry, ShaderMaterial, WebGLRenderTarget, RGBAFormat, DataTexture, FloatType, NoToneMapping, NearestFilter, ClampToEdgeWrapping } from \"three\";\nclass GPUComputationRenderer {\n  constructor(sizeX, sizeY, renderer) {\n    this.variables = [];\n    this.currentTextureIndex = 0;\n    let dataType = FloatType;\n    const scene = new Scene();\n    const camera = new Camera();\n    camera.position.z = 1;\n    const passThruUniforms = {\n      passThruTexture: {\n        value: null\n      }\n    };\n    const passThruShader = createShaderMaterial(getPassThroughFragmentShader(), passThruUniforms);\n    const mesh = new Mesh(new PlaneGeometry(2, 2), passThruShader);\n    scene.add(mesh);\n    this.setDataType = function (type) {\n      dataType = type;\n      return this;\n    };\n    this.addVariable = function (variableName, computeFragmentShader, initialValueTexture) {\n      const material = this.createShaderMaterial(computeFragmentShader);\n      const variable = {\n        name: variableName,\n        initialValueTexture,\n        material,\n        dependencies: null,\n        renderTargets: [],\n        wrapS: null,\n        wrapT: null,\n        minFilter: NearestFilter,\n        magFilter: NearestFilter\n      };\n      this.variables.push(variable);\n      return variable;\n    };\n    this.setVariableDependencies = function (variable, dependencies) {\n      variable.dependencies = dependencies;\n    };\n    this.init = function () {\n      if (renderer.capabilities.isWebGL2 === false && renderer.extensions.has(\"OES_texture_float\") === false) {\n        return \"No OES_texture_float support for float textures.\";\n      }\n      if (renderer.capabilities.maxVertexTextures === 0) {\n        return \"No support for vertex shader textures.\";\n      }\n      for (let i = 0; i < this.variables.length; i++) {\n        const variable = this.variables[i];\n        variable.renderTargets[0] = this.createRenderTarget(sizeX, sizeY, variable.wrapS, variable.wrapT, variable.minFilter, variable.magFilter);\n        variable.renderTargets[1] = this.createRenderTarget(sizeX, sizeY, variable.wrapS, variable.wrapT, variable.minFilter, variable.magFilter);\n        this.renderTexture(variable.initialValueTexture, variable.renderTargets[0]);\n        this.renderTexture(variable.initialValueTexture, variable.renderTargets[1]);\n        const material = variable.material;\n        const uniforms = material.uniforms;\n        if (variable.dependencies !== null) {\n          for (let d = 0; d < variable.dependencies.length; d++) {\n            const depVar = variable.dependencies[d];\n            if (depVar.name !== variable.name) {\n              let found = false;\n              for (let j = 0; j < this.variables.length; j++) {\n                if (depVar.name === this.variables[j].name) {\n                  found = true;\n                  break;\n                }\n              }\n              if (!found) {\n                return \"Variable dependency not found. Variable=\" + variable.name + \", dependency=\" + depVar.name;\n              }\n            }\n            uniforms[depVar.name] = {\n              value: null\n            };\n            material.fragmentShader = \"\\nuniform sampler2D \" + depVar.name + \";\\n\" + material.fragmentShader;\n          }\n        }\n      }\n      this.currentTextureIndex = 0;\n      return null;\n    };\n    this.compute = function () {\n      const currentTextureIndex = this.currentTextureIndex;\n      const nextTextureIndex = this.currentTextureIndex === 0 ? 1 : 0;\n      for (let i = 0, il = this.variables.length; i < il; i++) {\n        const variable = this.variables[i];\n        if (variable.dependencies !== null) {\n          const uniforms = variable.material.uniforms;\n          for (let d = 0, dl = variable.dependencies.length; d < dl; d++) {\n            const depVar = variable.dependencies[d];\n            uniforms[depVar.name].value = depVar.renderTargets[currentTextureIndex].texture;\n          }\n        }\n        this.doRenderTarget(variable.material, variable.renderTargets[nextTextureIndex]);\n      }\n      this.currentTextureIndex = nextTextureIndex;\n    };\n    this.getCurrentRenderTarget = function (variable) {\n      return variable.renderTargets[this.currentTextureIndex];\n    };\n    this.getAlternateRenderTarget = function (variable) {\n      return variable.renderTargets[this.currentTextureIndex === 0 ? 1 : 0];\n    };\n    this.dispose = function () {\n      mesh.geometry.dispose();\n      mesh.material.dispose();\n      const variables = this.variables;\n      for (let i = 0; i < variables.length; i++) {\n        const variable = variables[i];\n        if (variable.initialValueTexture) variable.initialValueTexture.dispose();\n        const renderTargets = variable.renderTargets;\n        for (let j = 0; j < renderTargets.length; j++) {\n          const renderTarget = renderTargets[j];\n          renderTarget.dispose();\n        }\n      }\n    };\n    function addResolutionDefine(materialShader) {\n      materialShader.defines.resolution = \"vec2( \" + sizeX.toFixed(1) + \", \" + sizeY.toFixed(1) + \" )\";\n    }\n    this.addResolutionDefine = addResolutionDefine;\n    function createShaderMaterial(computeFragmentShader, uniforms) {\n      uniforms = uniforms || {};\n      const material = new ShaderMaterial({\n        uniforms,\n        vertexShader: getPassThroughVertexShader(),\n        fragmentShader: computeFragmentShader\n      });\n      addResolutionDefine(material);\n      return material;\n    }\n    this.createShaderMaterial = createShaderMaterial;\n    this.createRenderTarget = function (sizeXTexture, sizeYTexture, wrapS, wrapT, minFilter, magFilter) {\n      sizeXTexture = sizeXTexture || sizeX;\n      sizeYTexture = sizeYTexture || sizeY;\n      wrapS = wrapS || ClampToEdgeWrapping;\n      wrapT = wrapT || ClampToEdgeWrapping;\n      minFilter = minFilter || NearestFilter;\n      magFilter = magFilter || NearestFilter;\n      const renderTarget = new WebGLRenderTarget(sizeXTexture, sizeYTexture, {\n        wrapS,\n        wrapT,\n        minFilter,\n        magFilter,\n        format: RGBAFormat,\n        type: dataType,\n        depthBuffer: false\n      });\n      return renderTarget;\n    };\n    this.createTexture = function () {\n      const data = new Float32Array(sizeX * sizeY * 4);\n      const texture = new DataTexture(data, sizeX, sizeY, RGBAFormat, FloatType);\n      texture.needsUpdate = true;\n      return texture;\n    };\n    this.renderTexture = function (input, output) {\n      passThruUniforms.passThruTexture.value = input;\n      this.doRenderTarget(passThruShader, output);\n      passThruUniforms.passThruTexture.value = null;\n    };\n    this.doRenderTarget = function (material, output) {\n      const currentRenderTarget = renderer.getRenderTarget();\n      const currentXrEnabled = renderer.xr.enabled;\n      const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;\n      const currentOutputColorSpace = renderer.outputColorSpace;\n      const currentToneMapping = renderer.toneMapping;\n      renderer.xr.enabled = false;\n      renderer.shadowMap.autoUpdate = false;\n      if (\"outputColorSpace\" in renderer) renderer.outputColorSpace = \"srgb-linear\";else renderer.encoding = 3e3;\n      renderer.toneMapping = NoToneMapping;\n      mesh.material = material;\n      renderer.setRenderTarget(output);\n      renderer.render(scene, camera);\n      mesh.material = passThruShader;\n      renderer.xr.enabled = currentXrEnabled;\n      renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;\n      renderer.outputColorSpace = currentOutputColorSpace;\n      renderer.toneMapping = currentToneMapping;\n      renderer.setRenderTarget(currentRenderTarget);\n    };\n    function getPassThroughVertexShader() {\n      return \"void main()\t{\\n\\n\tgl_Position = vec4( position, 1.0 );\\n\\n}\\n\";\n    }\n    function getPassThroughFragmentShader() {\n      return \"uniform sampler2D passThruTexture;\\n\\nvoid main() {\\n\\n\tvec2 uv = gl_FragCoord.xy / resolution.xy;\\n\\n\tgl_FragColor = texture2D( passThruTexture, uv );\\n\\n}\\n\";\n    }\n  }\n}\nexport { GPUComputationRenderer };", "map": {"version": 3, "names": ["GPUCom<PERSON><PERSON><PERSON><PERSON>", "constructor", "sizeX", "sizeY", "renderer", "variables", "currentTextureIndex", "dataType", "FloatType", "scene", "Scene", "camera", "Camera", "position", "z", "passThruUniforms", "passThruTexture", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createShaderMaterial", "getPassThroughFragmentShader", "mesh", "<PERSON><PERSON>", "PlaneGeometry", "add", "setDataType", "type", "addVariable", "variableName", "computeFragmentShader", "initialValueTexture", "material", "variable", "name", "dependencies", "renderTargets", "wrapS", "wrapT", "minFilter", "NearestFilter", "magFilter", "push", "setVariableDependencies", "init", "capabilities", "isWebGL2", "extensions", "has", "maxVertexTextures", "i", "length", "createRenderTarget", "renderTexture", "uniforms", "d", "depVar", "found", "j", "fragmentShader", "compute", "nextTextureIndex", "il", "dl", "texture", "do<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentRenderTarget", "getAlternateRenderTarget", "dispose", "geometry", "renderTarget", "addResolutionDefine", "material<PERSON><PERSON>er", "defines", "resolution", "toFixed", "ShaderMaterial", "vertexShader", "getPassThroughVertexShader", "sizeXTexture", "sizeYTexture", "ClampToEdgeWrapping", "WebGLRenderTarget", "format", "RGBAFormat", "depthBuffer", "createTexture", "data", "Float32Array", "DataTexture", "needsUpdate", "input", "output", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "currentOutputColorSpace", "outputColorSpace", "currentToneMapping", "toneMapping", "encoding", "NoToneMapping", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/misc/GPUComputationRenderer.js"], "sourcesContent": ["import {\n  Camera,\n  ClampToEdgeWrapping,\n  DataTexture,\n  FloatType,\n  Mesh,\n  NearestFilter,\n  NoToneMapping,\n  PlaneGeometry,\n  RGBAFormat,\n  Scene,\n  ShaderMaterial,\n  WebGLRenderTarget,\n} from 'three'\n\n/**\n * GPUComputationRenderer, based on Simu<PERSON><PERSON><PERSON><PERSON> by zz85\n *\n * The GPUComputationRenderer uses the concept of variables. These variables are RGBA float textures that hold 4 floats\n * for each compute element (texel)\n *\n * Each variable has a fragment shader that defines the computation made to obtain the variable in question.\n * You can use as many variables you need, and make dependencies so you can use textures of other variables in the shader\n * (the sampler uniforms are added automatically) Most of the variables will need themselves as dependency.\n *\n * The renderer has actually two render targets per variable, to make ping-pong. Textures from the current frame are used\n * as inputs to render the textures of the next frame.\n *\n * The render targets of the variables can be used as input textures for your visualization shaders.\n *\n * Variable names should be valid identifiers and should not collide with THREE GLSL used identifiers.\n * a common approach could be to use 'texture' prefixing the variable name; i.e texturePosition, textureVelocity...\n *\n * The size of the computation (sizeX * sizeY) is defined as 'resolution' automatically in the shader. For example:\n * #DEFINE resolution vec2( 1024.0, 1024.0 )\n *\n * -------------\n *\n * Basic use:\n *\n * // Initialization...\n *\n * // Create computation renderer\n * const gpuCompute = new GPUComputationRenderer( 1024, 1024, renderer );\n *\n * // Create initial state float textures\n * const pos0 = gpuCompute.createTexture();\n * const vel0 = gpuCompute.createTexture();\n * // and fill in here the texture data...\n *\n * // Add texture variables\n * const velVar = gpuCompute.addVariable( \"textureVelocity\", fragmentShaderVel, pos0 );\n * const posVar = gpuCompute.addVariable( \"texturePosition\", fragmentShaderPos, vel0 );\n *\n * // Add variable dependencies\n * gpuCompute.setVariableDependencies( velVar, [ velVar, posVar ] );\n * gpuCompute.setVariableDependencies( posVar, [ velVar, posVar ] );\n *\n * // Add custom uniforms\n * velVar.material.uniforms.time = { value: 0.0 };\n *\n * // Check for completeness\n * const error = gpuCompute.init();\n * if ( error !== null ) {\n *\t\tconsole.error( error );\n * }\n *\n *\n * // In each frame...\n *\n * // Compute!\n * gpuCompute.compute();\n *\n * // Update texture uniforms in your visualization materials with the gpu renderer output\n * myMaterial.uniforms.myTexture.value = gpuCompute.getCurrentRenderTarget( posVar ).texture;\n *\n * // Do your rendering\n * renderer.render( myScene, myCamera );\n *\n * -------------\n *\n * Also, you can use utility functions to create ShaderMaterial and perform computations (rendering between textures)\n * Note that the shaders can have multiple input textures.\n *\n * const myFilter1 = gpuCompute.createShaderMaterial( myFilterFragmentShader1, { theTexture: { value: null } } );\n * const myFilter2 = gpuCompute.createShaderMaterial( myFilterFragmentShader2, { theTexture: { value: null } } );\n *\n * const inputTexture = gpuCompute.createTexture();\n *\n * // Fill in here inputTexture...\n *\n * myFilter1.uniforms.theTexture.value = inputTexture;\n *\n * const myRenderTarget = gpuCompute.createRenderTarget();\n * myFilter2.uniforms.theTexture.value = myRenderTarget.texture;\n *\n * const outputRenderTarget = gpuCompute.createRenderTarget();\n *\n * // Now use the output texture where you want:\n * myMaterial.uniforms.map.value = outputRenderTarget.texture;\n *\n * // And compute each frame, before rendering to screen:\n * gpuCompute.doRenderTarget( myFilter1, myRenderTarget );\n * gpuCompute.doRenderTarget( myFilter2, outputRenderTarget );\n *\n *\n *\n * @param {int} sizeX Computation problem size is always 2d: sizeX * sizeY elements.\n * @param {int} sizeY Computation problem size is always 2d: sizeX * sizeY elements.\n * @param {WebGLRenderer} renderer The renderer\n */\n\nclass GPUComputationRenderer {\n  constructor(sizeX, sizeY, renderer) {\n    this.variables = []\n\n    this.currentTextureIndex = 0\n\n    let dataType = FloatType\n\n    const scene = new Scene()\n\n    const camera = new Camera()\n    camera.position.z = 1\n\n    const passThruUniforms = {\n      passThruTexture: { value: null },\n    }\n\n    const passThruShader = createShaderMaterial(getPassThroughFragmentShader(), passThruUniforms)\n\n    const mesh = new Mesh(new PlaneGeometry(2, 2), passThruShader)\n    scene.add(mesh)\n\n    this.setDataType = function (type) {\n      dataType = type\n      return this\n    }\n\n    this.addVariable = function (variableName, computeFragmentShader, initialValueTexture) {\n      const material = this.createShaderMaterial(computeFragmentShader)\n\n      const variable = {\n        name: variableName,\n        initialValueTexture: initialValueTexture,\n        material: material,\n        dependencies: null,\n        renderTargets: [],\n        wrapS: null,\n        wrapT: null,\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n      }\n\n      this.variables.push(variable)\n\n      return variable\n    }\n\n    this.setVariableDependencies = function (variable, dependencies) {\n      variable.dependencies = dependencies\n    }\n\n    this.init = function () {\n      if (renderer.capabilities.isWebGL2 === false && renderer.extensions.has('OES_texture_float') === false) {\n        return 'No OES_texture_float support for float textures.'\n      }\n\n      if (renderer.capabilities.maxVertexTextures === 0) {\n        return 'No support for vertex shader textures.'\n      }\n\n      for (let i = 0; i < this.variables.length; i++) {\n        const variable = this.variables[i]\n\n        // Creates rendertargets and initialize them with input texture\n        variable.renderTargets[0] = this.createRenderTarget(\n          sizeX,\n          sizeY,\n          variable.wrapS,\n          variable.wrapT,\n          variable.minFilter,\n          variable.magFilter,\n        )\n        variable.renderTargets[1] = this.createRenderTarget(\n          sizeX,\n          sizeY,\n          variable.wrapS,\n          variable.wrapT,\n          variable.minFilter,\n          variable.magFilter,\n        )\n        this.renderTexture(variable.initialValueTexture, variable.renderTargets[0])\n        this.renderTexture(variable.initialValueTexture, variable.renderTargets[1])\n\n        // Adds dependencies uniforms to the ShaderMaterial\n        const material = variable.material\n        const uniforms = material.uniforms\n\n        if (variable.dependencies !== null) {\n          for (let d = 0; d < variable.dependencies.length; d++) {\n            const depVar = variable.dependencies[d]\n\n            if (depVar.name !== variable.name) {\n              // Checks if variable exists\n              let found = false\n\n              for (let j = 0; j < this.variables.length; j++) {\n                if (depVar.name === this.variables[j].name) {\n                  found = true\n                  break\n                }\n              }\n\n              if (!found) {\n                return 'Variable dependency not found. Variable=' + variable.name + ', dependency=' + depVar.name\n              }\n            }\n\n            uniforms[depVar.name] = { value: null }\n\n            material.fragmentShader = '\\nuniform sampler2D ' + depVar.name + ';\\n' + material.fragmentShader\n          }\n        }\n      }\n\n      this.currentTextureIndex = 0\n\n      return null\n    }\n\n    this.compute = function () {\n      const currentTextureIndex = this.currentTextureIndex\n      const nextTextureIndex = this.currentTextureIndex === 0 ? 1 : 0\n\n      for (let i = 0, il = this.variables.length; i < il; i++) {\n        const variable = this.variables[i]\n\n        // Sets texture dependencies uniforms\n        if (variable.dependencies !== null) {\n          const uniforms = variable.material.uniforms\n\n          for (let d = 0, dl = variable.dependencies.length; d < dl; d++) {\n            const depVar = variable.dependencies[d]\n\n            uniforms[depVar.name].value = depVar.renderTargets[currentTextureIndex].texture\n          }\n        }\n\n        // Performs the computation for this variable\n        this.doRenderTarget(variable.material, variable.renderTargets[nextTextureIndex])\n      }\n\n      this.currentTextureIndex = nextTextureIndex\n    }\n\n    this.getCurrentRenderTarget = function (variable) {\n      return variable.renderTargets[this.currentTextureIndex]\n    }\n\n    this.getAlternateRenderTarget = function (variable) {\n      return variable.renderTargets[this.currentTextureIndex === 0 ? 1 : 0]\n    }\n\n    this.dispose = function () {\n      mesh.geometry.dispose()\n      mesh.material.dispose()\n\n      const variables = this.variables\n\n      for (let i = 0; i < variables.length; i++) {\n        const variable = variables[i]\n\n        if (variable.initialValueTexture) variable.initialValueTexture.dispose()\n\n        const renderTargets = variable.renderTargets\n\n        for (let j = 0; j < renderTargets.length; j++) {\n          const renderTarget = renderTargets[j]\n          renderTarget.dispose()\n        }\n      }\n    }\n\n    function addResolutionDefine(materialShader) {\n      materialShader.defines.resolution = 'vec2( ' + sizeX.toFixed(1) + ', ' + sizeY.toFixed(1) + ' )'\n    }\n\n    this.addResolutionDefine = addResolutionDefine\n\n    // The following functions can be used to compute things manually\n\n    function createShaderMaterial(computeFragmentShader, uniforms) {\n      uniforms = uniforms || {}\n\n      const material = new ShaderMaterial({\n        uniforms: uniforms,\n        vertexShader: getPassThroughVertexShader(),\n        fragmentShader: computeFragmentShader,\n      })\n\n      addResolutionDefine(material)\n\n      return material\n    }\n\n    this.createShaderMaterial = createShaderMaterial\n\n    this.createRenderTarget = function (sizeXTexture, sizeYTexture, wrapS, wrapT, minFilter, magFilter) {\n      sizeXTexture = sizeXTexture || sizeX\n      sizeYTexture = sizeYTexture || sizeY\n\n      wrapS = wrapS || ClampToEdgeWrapping\n      wrapT = wrapT || ClampToEdgeWrapping\n\n      minFilter = minFilter || NearestFilter\n      magFilter = magFilter || NearestFilter\n\n      const renderTarget = new WebGLRenderTarget(sizeXTexture, sizeYTexture, {\n        wrapS: wrapS,\n        wrapT: wrapT,\n        minFilter: minFilter,\n        magFilter: magFilter,\n        format: RGBAFormat,\n        type: dataType,\n        depthBuffer: false,\n      })\n\n      return renderTarget\n    }\n\n    this.createTexture = function () {\n      const data = new Float32Array(sizeX * sizeY * 4)\n      const texture = new DataTexture(data, sizeX, sizeY, RGBAFormat, FloatType)\n      texture.needsUpdate = true\n      return texture\n    }\n\n    this.renderTexture = function (input, output) {\n      // Takes a texture, and render out in rendertarget\n      // input = Texture\n      // output = RenderTarget\n\n      passThruUniforms.passThruTexture.value = input\n\n      this.doRenderTarget(passThruShader, output)\n\n      passThruUniforms.passThruTexture.value = null\n    }\n\n    this.doRenderTarget = function (material, output) {\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      const currentXrEnabled = renderer.xr.enabled\n      const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n      const currentOutputColorSpace = renderer.outputColorSpace\n      const currentToneMapping = renderer.toneMapping\n\n      renderer.xr.enabled = false // Avoid camera modification\n      renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n      if ('outputColorSpace' in renderer) renderer.outputColorSpace = 'srgb-linear'\n      else renderer.encoding = 3000 // LinearEncoding\n      renderer.toneMapping = NoToneMapping\n\n      mesh.material = material\n      renderer.setRenderTarget(output)\n      renderer.render(scene, camera)\n      mesh.material = passThruShader\n\n      renderer.xr.enabled = currentXrEnabled\n      renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n      renderer.outputColorSpace = currentOutputColorSpace\n      renderer.toneMapping = currentToneMapping\n\n      renderer.setRenderTarget(currentRenderTarget)\n    }\n\n    // Shaders\n\n    function getPassThroughVertexShader() {\n      return 'void main()\t{\\n' + '\\n' + '\tgl_Position = vec4( position, 1.0 );\\n' + '\\n' + '}\\n'\n    }\n\n    function getPassThroughFragmentShader() {\n      return (\n        'uniform sampler2D passThruTexture;\\n' +\n        '\\n' +\n        'void main() {\\n' +\n        '\\n' +\n        '\tvec2 uv = gl_FragCoord.xy / resolution.xy;\\n' +\n        '\\n' +\n        '\tgl_FragColor = texture2D( passThruTexture, uv );\\n' +\n        '\\n' +\n        '}\\n'\n      )\n    }\n  }\n}\n\nexport { GPUComputationRenderer }\n"], "mappings": ";AAgHA,MAAMA,sBAAA,CAAuB;EAC3BC,YAAYC,KAAA,EAAOC,KAAA,EAAOC,QAAA,EAAU;IAClC,KAAKC,SAAA,GAAY,EAAE;IAEnB,KAAKC,mBAAA,GAAsB;IAE3B,IAAIC,QAAA,GAAWC,SAAA;IAEf,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAO;IAEzB,MAAMC,MAAA,GAAS,IAAIC,MAAA,CAAQ;IAC3BD,MAAA,CAAOE,QAAA,CAASC,CAAA,GAAI;IAEpB,MAAMC,gBAAA,GAAmB;MACvBC,eAAA,EAAiB;QAAEC,KAAA,EAAO;MAAM;IACjC;IAED,MAAMC,cAAA,GAAiBC,oBAAA,CAAqBC,4BAAA,CAA4B,GAAIL,gBAAgB;IAE5F,MAAMM,IAAA,GAAO,IAAIC,IAAA,CAAK,IAAIC,aAAA,CAAc,GAAG,CAAC,GAAGL,cAAc;IAC7DT,KAAA,CAAMe,GAAA,CAAIH,IAAI;IAEd,KAAKI,WAAA,GAAc,UAAUC,IAAA,EAAM;MACjCnB,QAAA,GAAWmB,IAAA;MACX,OAAO;IACR;IAED,KAAKC,WAAA,GAAc,UAAUC,YAAA,EAAcC,qBAAA,EAAuBC,mBAAA,EAAqB;MACrF,MAAMC,QAAA,GAAW,KAAKZ,oBAAA,CAAqBU,qBAAqB;MAEhE,MAAMG,QAAA,GAAW;QACfC,IAAA,EAAML,YAAA;QACNE,mBAAA;QACAC,QAAA;QACAG,YAAA,EAAc;QACdC,aAAA,EAAe,EAAE;QACjBC,KAAA,EAAO;QACPC,KAAA,EAAO;QACPC,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD;MACZ;MAED,KAAKlC,SAAA,CAAUoC,IAAA,CAAKT,QAAQ;MAE5B,OAAOA,QAAA;IACR;IAED,KAAKU,uBAAA,GAA0B,UAAUV,QAAA,EAAUE,YAAA,EAAc;MAC/DF,QAAA,CAASE,YAAA,GAAeA,YAAA;IACzB;IAED,KAAKS,IAAA,GAAO,YAAY;MACtB,IAAIvC,QAAA,CAASwC,YAAA,CAAaC,QAAA,KAAa,SAASzC,QAAA,CAAS0C,UAAA,CAAWC,GAAA,CAAI,mBAAmB,MAAM,OAAO;QACtG,OAAO;MACR;MAED,IAAI3C,QAAA,CAASwC,YAAA,CAAaI,iBAAA,KAAsB,GAAG;QACjD,OAAO;MACR;MAED,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5C,SAAA,CAAU6C,MAAA,EAAQD,CAAA,IAAK;QAC9C,MAAMjB,QAAA,GAAW,KAAK3B,SAAA,CAAU4C,CAAC;QAGjCjB,QAAA,CAASG,aAAA,CAAc,CAAC,IAAI,KAAKgB,kBAAA,CAC/BjD,KAAA,EACAC,KAAA,EACA6B,QAAA,CAASI,KAAA,EACTJ,QAAA,CAASK,KAAA,EACTL,QAAA,CAASM,SAAA,EACTN,QAAA,CAASQ,SACV;QACDR,QAAA,CAASG,aAAA,CAAc,CAAC,IAAI,KAAKgB,kBAAA,CAC/BjD,KAAA,EACAC,KAAA,EACA6B,QAAA,CAASI,KAAA,EACTJ,QAAA,CAASK,KAAA,EACTL,QAAA,CAASM,SAAA,EACTN,QAAA,CAASQ,SACV;QACD,KAAKY,aAAA,CAAcpB,QAAA,CAASF,mBAAA,EAAqBE,QAAA,CAASG,aAAA,CAAc,CAAC,CAAC;QAC1E,KAAKiB,aAAA,CAAcpB,QAAA,CAASF,mBAAA,EAAqBE,QAAA,CAASG,aAAA,CAAc,CAAC,CAAC;QAG1E,MAAMJ,QAAA,GAAWC,QAAA,CAASD,QAAA;QAC1B,MAAMsB,QAAA,GAAWtB,QAAA,CAASsB,QAAA;QAE1B,IAAIrB,QAAA,CAASE,YAAA,KAAiB,MAAM;UAClC,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAItB,QAAA,CAASE,YAAA,CAAagB,MAAA,EAAQI,CAAA,IAAK;YACrD,MAAMC,MAAA,GAASvB,QAAA,CAASE,YAAA,CAAaoB,CAAC;YAEtC,IAAIC,MAAA,CAAOtB,IAAA,KAASD,QAAA,CAASC,IAAA,EAAM;cAEjC,IAAIuB,KAAA,GAAQ;cAEZ,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKpD,SAAA,CAAU6C,MAAA,EAAQO,CAAA,IAAK;gBAC9C,IAAIF,MAAA,CAAOtB,IAAA,KAAS,KAAK5B,SAAA,CAAUoD,CAAC,EAAExB,IAAA,EAAM;kBAC1CuB,KAAA,GAAQ;kBACR;gBACD;cACF;cAED,IAAI,CAACA,KAAA,EAAO;gBACV,OAAO,6CAA6CxB,QAAA,CAASC,IAAA,GAAO,kBAAkBsB,MAAA,CAAOtB,IAAA;cAC9F;YACF;YAEDoB,QAAA,CAASE,MAAA,CAAOtB,IAAI,IAAI;cAAEhB,KAAA,EAAO;YAAM;YAEvCc,QAAA,CAAS2B,cAAA,GAAiB,yBAAyBH,MAAA,CAAOtB,IAAA,GAAO,QAAQF,QAAA,CAAS2B,cAAA;UACnF;QACF;MACF;MAED,KAAKpD,mBAAA,GAAsB;MAE3B,OAAO;IACR;IAED,KAAKqD,OAAA,GAAU,YAAY;MACzB,MAAMrD,mBAAA,GAAsB,KAAKA,mBAAA;MACjC,MAAMsD,gBAAA,GAAmB,KAAKtD,mBAAA,KAAwB,IAAI,IAAI;MAE9D,SAAS2C,CAAA,GAAI,GAAGY,EAAA,GAAK,KAAKxD,SAAA,CAAU6C,MAAA,EAAQD,CAAA,GAAIY,EAAA,EAAIZ,CAAA,IAAK;QACvD,MAAMjB,QAAA,GAAW,KAAK3B,SAAA,CAAU4C,CAAC;QAGjC,IAAIjB,QAAA,CAASE,YAAA,KAAiB,MAAM;UAClC,MAAMmB,QAAA,GAAWrB,QAAA,CAASD,QAAA,CAASsB,QAAA;UAEnC,SAASC,CAAA,GAAI,GAAGQ,EAAA,GAAK9B,QAAA,CAASE,YAAA,CAAagB,MAAA,EAAQI,CAAA,GAAIQ,EAAA,EAAIR,CAAA,IAAK;YAC9D,MAAMC,MAAA,GAASvB,QAAA,CAASE,YAAA,CAAaoB,CAAC;YAEtCD,QAAA,CAASE,MAAA,CAAOtB,IAAI,EAAEhB,KAAA,GAAQsC,MAAA,CAAOpB,aAAA,CAAc7B,mBAAmB,EAAEyD,OAAA;UACzE;QACF;QAGD,KAAKC,cAAA,CAAehC,QAAA,CAASD,QAAA,EAAUC,QAAA,CAASG,aAAA,CAAcyB,gBAAgB,CAAC;MAChF;MAED,KAAKtD,mBAAA,GAAsBsD,gBAAA;IAC5B;IAED,KAAKK,sBAAA,GAAyB,UAAUjC,QAAA,EAAU;MAChD,OAAOA,QAAA,CAASG,aAAA,CAAc,KAAK7B,mBAAmB;IACvD;IAED,KAAK4D,wBAAA,GAA2B,UAAUlC,QAAA,EAAU;MAClD,OAAOA,QAAA,CAASG,aAAA,CAAc,KAAK7B,mBAAA,KAAwB,IAAI,IAAI,CAAC;IACrE;IAED,KAAK6D,OAAA,GAAU,YAAY;MACzB9C,IAAA,CAAK+C,QAAA,CAASD,OAAA,CAAS;MACvB9C,IAAA,CAAKU,QAAA,CAASoC,OAAA,CAAS;MAEvB,MAAM9D,SAAA,GAAY,KAAKA,SAAA;MAEvB,SAAS4C,CAAA,GAAI,GAAGA,CAAA,GAAI5C,SAAA,CAAU6C,MAAA,EAAQD,CAAA,IAAK;QACzC,MAAMjB,QAAA,GAAW3B,SAAA,CAAU4C,CAAC;QAE5B,IAAIjB,QAAA,CAASF,mBAAA,EAAqBE,QAAA,CAASF,mBAAA,CAAoBqC,OAAA,CAAS;QAExE,MAAMhC,aAAA,GAAgBH,QAAA,CAASG,aAAA;QAE/B,SAASsB,CAAA,GAAI,GAAGA,CAAA,GAAItB,aAAA,CAAce,MAAA,EAAQO,CAAA,IAAK;UAC7C,MAAMY,YAAA,GAAelC,aAAA,CAAcsB,CAAC;UACpCY,YAAA,CAAaF,OAAA,CAAS;QACvB;MACF;IACF;IAED,SAASG,oBAAoBC,cAAA,EAAgB;MAC3CA,cAAA,CAAeC,OAAA,CAAQC,UAAA,GAAa,WAAWvE,KAAA,CAAMwE,OAAA,CAAQ,CAAC,IAAI,OAAOvE,KAAA,CAAMuE,OAAA,CAAQ,CAAC,IAAI;IAC7F;IAED,KAAKJ,mBAAA,GAAsBA,mBAAA;IAI3B,SAASnD,qBAAqBU,qBAAA,EAAuBwB,QAAA,EAAU;MAC7DA,QAAA,GAAWA,QAAA,IAAY,CAAE;MAEzB,MAAMtB,QAAA,GAAW,IAAI4C,cAAA,CAAe;QAClCtB,QAAA;QACAuB,YAAA,EAAcC,0BAAA,CAA4B;QAC1CnB,cAAA,EAAgB7B;MACxB,CAAO;MAEDyC,mBAAA,CAAoBvC,QAAQ;MAE5B,OAAOA,QAAA;IACR;IAED,KAAKZ,oBAAA,GAAuBA,oBAAA;IAE5B,KAAKgC,kBAAA,GAAqB,UAAU2B,YAAA,EAAcC,YAAA,EAAc3C,KAAA,EAAOC,KAAA,EAAOC,SAAA,EAAWE,SAAA,EAAW;MAClGsC,YAAA,GAAeA,YAAA,IAAgB5E,KAAA;MAC/B6E,YAAA,GAAeA,YAAA,IAAgB5E,KAAA;MAE/BiC,KAAA,GAAQA,KAAA,IAAS4C,mBAAA;MACjB3C,KAAA,GAAQA,KAAA,IAAS2C,mBAAA;MAEjB1C,SAAA,GAAYA,SAAA,IAAaC,aAAA;MACzBC,SAAA,GAAYA,SAAA,IAAaD,aAAA;MAEzB,MAAM8B,YAAA,GAAe,IAAIY,iBAAA,CAAkBH,YAAA,EAAcC,YAAA,EAAc;QACrE3C,KAAA;QACAC,KAAA;QACAC,SAAA;QACAE,SAAA;QACA0C,MAAA,EAAQC,UAAA;QACRzD,IAAA,EAAMnB,QAAA;QACN6E,WAAA,EAAa;MACrB,CAAO;MAED,OAAOf,YAAA;IACR;IAED,KAAKgB,aAAA,GAAgB,YAAY;MAC/B,MAAMC,IAAA,GAAO,IAAIC,YAAA,CAAarF,KAAA,GAAQC,KAAA,GAAQ,CAAC;MAC/C,MAAM4D,OAAA,GAAU,IAAIyB,WAAA,CAAYF,IAAA,EAAMpF,KAAA,EAAOC,KAAA,EAAOgF,UAAA,EAAY3E,SAAS;MACzEuD,OAAA,CAAQ0B,WAAA,GAAc;MACtB,OAAO1B,OAAA;IACR;IAED,KAAKX,aAAA,GAAgB,UAAUsC,KAAA,EAAOC,MAAA,EAAQ;MAK5C5E,gBAAA,CAAiBC,eAAA,CAAgBC,KAAA,GAAQyE,KAAA;MAEzC,KAAK1B,cAAA,CAAe9C,cAAA,EAAgByE,MAAM;MAE1C5E,gBAAA,CAAiBC,eAAA,CAAgBC,KAAA,GAAQ;IAC1C;IAED,KAAK+C,cAAA,GAAiB,UAAUjC,QAAA,EAAU4D,MAAA,EAAQ;MAChD,MAAMC,mBAAA,GAAsBxF,QAAA,CAASyF,eAAA,CAAiB;MAEtD,MAAMC,gBAAA,GAAmB1F,QAAA,CAAS2F,EAAA,CAAGC,OAAA;MACrC,MAAMC,uBAAA,GAA0B7F,QAAA,CAAS8F,SAAA,CAAUC,UAAA;MACnD,MAAMC,uBAAA,GAA0BhG,QAAA,CAASiG,gBAAA;MACzC,MAAMC,kBAAA,GAAqBlG,QAAA,CAASmG,WAAA;MAEpCnG,QAAA,CAAS2F,EAAA,CAAGC,OAAA,GAAU;MACtB5F,QAAA,CAAS8F,SAAA,CAAUC,UAAA,GAAa;MAChC,IAAI,sBAAsB/F,QAAA,EAAUA,QAAA,CAASiG,gBAAA,GAAmB,mBAC3DjG,QAAA,CAASoG,QAAA,GAAW;MACzBpG,QAAA,CAASmG,WAAA,GAAcE,aAAA;MAEvBpF,IAAA,CAAKU,QAAA,GAAWA,QAAA;MAChB3B,QAAA,CAASsG,eAAA,CAAgBf,MAAM;MAC/BvF,QAAA,CAASuG,MAAA,CAAOlG,KAAA,EAAOE,MAAM;MAC7BU,IAAA,CAAKU,QAAA,GAAWb,cAAA;MAEhBd,QAAA,CAAS2F,EAAA,CAAGC,OAAA,GAAUF,gBAAA;MACtB1F,QAAA,CAAS8F,SAAA,CAAUC,UAAA,GAAaF,uBAAA;MAChC7F,QAAA,CAASiG,gBAAA,GAAmBD,uBAAA;MAC5BhG,QAAA,CAASmG,WAAA,GAAcD,kBAAA;MAEvBlG,QAAA,CAASsG,eAAA,CAAgBd,mBAAmB;IAC7C;IAID,SAASf,2BAAA,EAA6B;MACpC,OAAO;IACR;IAED,SAASzD,6BAAA,EAA+B;MACtC,OACE;IAUH;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}