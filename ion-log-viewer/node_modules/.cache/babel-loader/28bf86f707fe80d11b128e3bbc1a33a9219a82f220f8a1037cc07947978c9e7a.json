{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/ThreeDViewport.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useMemo, useState, useRef } from 'react';\nimport { Paper, Typography, Box, Chip, Alert, Switch, FormControlLabel } from '@mui/material';\nimport { Canvas } from '@react-three/fiber';\nimport { OrbitControls, Grid, Text } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { findMessageAtTime } from '../utils/ionParser';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Robot model component\nconst RobotModel = ({\n  position,\n  rotation,\n  botModel\n}) => {\n  _s();\n  const meshRef = useRef(null);\n\n  // For now, use a simple box as robot representation\n  // In a real implementation, you would parse the botModel data (likely STL/OBJ format)\n  return /*#__PURE__*/_jsxDEV(\"mesh\", {\n    ref: meshRef,\n    position: position,\n    rotation: rotation,\n    children: [/*#__PURE__*/_jsxDEV(\"boxGeometry\", {\n      args: [0.5, 0.3, 0.2]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meshStandardMaterial\", {\n      color: \"orange\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n\n// Trail component to show robot path\n_s(RobotModel, \"e4CAwo/q3Mh5N9nuIOzG1d1uu5c=\");\n_c = RobotModel;\nconst RobotTrail = ({\n  positions\n}) => {\n  if (positions.length < 2) return null;\n  const points = positions;\n  const geometry = new THREE.BufferGeometry().setFromPoints(points);\n  return /*#__PURE__*/_jsxDEV(\"line\", {\n    geometry: geometry,\n    children: /*#__PURE__*/_jsxDEV(\"lineBasicMaterial\", {\n      color: \"cyan\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n\n// Main 3D scene component\n_c2 = RobotTrail;\nconst Scene = ({\n  currentOdometry,\n  trailPositions,\n  showTrail,\n  botModel\n}) => {\n  _s2();\n  // Convert ROS coordinates (z-up) to Three.js coordinates (y-up)\n  const robotPosition = useMemo(() => {\n    if (!currentOdometry) return new THREE.Vector3(0, 0, 0);\n    return new THREE.Vector3(currentOdometry.position.x, currentOdometry.position.z,\n    // ROS z becomes Three.js y\n    -currentOdometry.position.y // ROS y becomes Three.js -z\n    );\n  }, [currentOdometry]);\n  const robotRotation = useMemo(() => {\n    if (!currentOdometry) return new THREE.Euler(0, 0, 0);\n\n    // Convert quaternion to Euler angles and adjust for coordinate system\n    const quaternion = new THREE.Quaternion(currentOdometry.orientation.x, currentOdometry.orientation.z, -currentOdometry.orientation.y, currentOdometry.orientation.w);\n    return new THREE.Euler().setFromQuaternion(quaternion);\n  }, [currentOdometry]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n      intensity: 0.6\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n      position: [10, 10, 5],\n      intensity: 0.8\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      args: [20, 20],\n      cellSize: 1,\n      cellThickness: 0.5,\n      cellColor: \"#6f6f6f\",\n      sectionSize: 5,\n      sectionThickness: 1,\n      sectionColor: \"#9d4b4b\",\n      fadeDistance: 30,\n      fadeStrength: 1,\n      followCamera: false,\n      infiniteGrid: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      position: [1, 0, 0],\n      fontSize: 0.2,\n      color: \"red\",\n      anchorX: \"center\",\n      anchorY: \"middle\",\n      children: \"X\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      position: [0, 1, 0],\n      fontSize: 0.2,\n      color: \"green\",\n      anchorX: \"center\",\n      anchorY: \"middle\",\n      children: \"Y\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      position: [0, 0, 1],\n      fontSize: 0.2,\n      color: \"blue\",\n      anchorX: \"center\",\n      anchorY: \"middle\",\n      children: \"Z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), currentOdometry && /*#__PURE__*/_jsxDEV(RobotModel, {\n      position: robotPosition,\n      rotation: robotRotation,\n      botModel: botModel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), showTrail && /*#__PURE__*/_jsxDEV(RobotTrail, {\n      positions: trailPositions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(OrbitControls, {\n      enablePan: true,\n      enableZoom: true,\n      enableRotate: true,\n      target: robotPosition\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s2(Scene, \"aPbKcO5DKMvlf6d9lhc7vab4AAE=\");\n_c3 = Scene;\nconst ThreeDViewport = ({\n  data,\n  currentTime\n}) => {\n  _s3();\n  const [showTrail, setShowTrail] = useState(true);\n\n  // Find the wheel odometry topic\n  const odomTopic = useMemo(() => {\n    return data.topics.find(topic => topic.name === '/tb_control/wheel_odom' || topic.name.toLowerCase().includes('odom'));\n  }, [data.topics]);\n\n  // Get current odometry data\n  const currentOdometry = useMemo(() => {\n    var _content$pose;\n    if (!odomTopic || odomTopic.messages.length === 0) {\n      return null;\n    }\n    const message = findMessageAtTime(odomTopic.messages, currentTime);\n    if (!message) return null;\n    const content = message.content;\n    if (!content || typeof content !== 'object') return null;\n\n    // Parse odometry message (typical ROS nav_msgs/Odometry structure)\n    const pose = ((_content$pose = content.pose) === null || _content$pose === void 0 ? void 0 : _content$pose.pose) || content.pose || {};\n    const position = pose.position || {\n      x: 0,\n      y: 0,\n      z: 0\n    };\n    const orientation = pose.orientation || {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1\n    };\n    return {\n      position,\n      orientation,\n      timestamp: message.timestamp\n    };\n  }, [odomTopic, currentTime]);\n\n  // Generate trail positions up to current time\n  const trailPositions = useMemo(() => {\n    if (!odomTopic) return [];\n    const positions = [];\n    for (const message of odomTopic.messages) {\n      var _content$pose2;\n      if (message.timestamp > currentTime) break;\n      const content = message.content;\n      if (!content || typeof content !== 'object') continue;\n      const pose = ((_content$pose2 = content.pose) === null || _content$pose2 === void 0 ? void 0 : _content$pose2.pose) || content.pose || {};\n      const position = pose.position || {\n        x: 0,\n        y: 0,\n        z: 0\n      };\n\n      // Convert ROS coordinates to Three.js coordinates\n      positions.push(new THREE.Vector3(position.x, position.z,\n      // ROS z becomes Three.js y\n      -position.y // ROS y becomes Three.js -z\n      ));\n    }\n    return positions;\n  }, [odomTopic, currentTime]);\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"h2\",\n        gutterBottom: true,\n        children: \"3D Viewport\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: \"Question 5 (Bonus)\",\n        color: \"secondary\",\n        size: \"small\",\n        sx: {\n          mb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        display: 'flex',\n        gap: 2,\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showTrail,\n          onChange: e => setShowTrail(e.target.checked)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this),\n        label: \"Show Trail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), currentOdometry && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `X: ${currentOdometry.position.x.toFixed(2)}`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Y: ${currentOdometry.position.y.toFixed(2)}`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Z: ${currentOdometry.position.z.toFixed(2)}`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), !odomTopic ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      children: \"No odometry topic (/tb_control/wheel_odom) found in the log data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this) : !currentOdometry ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"No odometry data available at current timestamp\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        border: 1,\n        borderColor: 'divider',\n        borderRadius: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Canvas, {\n        camera: {\n          position: [5, 5, 5],\n          fov: 60\n        },\n        style: {\n          width: '100%',\n          height: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Scene, {\n          currentOdometry: currentOdometry,\n          trailPositions: trailPositions,\n          showTrail: showTrail,\n          botModel: data.robotInfo.botModel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: odomTopic ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"Odometry messages: \", odomTopic.messages.length, \" | Trail points: \", trailPositions.length, \" | 3D Model: \", data.robotInfo.botModel ? 'Available' : 'Not available']\n        }, void 0, true) : 'No odometry data available'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s3(ThreeDViewport, \"YSkNn9X156QnvUheUqTuASbDvK8=\");\n_c4 = ThreeDViewport;\nexport default ThreeDViewport;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"RobotModel\");\n$RefreshReg$(_c2, \"RobotTrail\");\n$RefreshReg$(_c3, \"Scene\");\n$RefreshReg$(_c4, \"ThreeDViewport\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useRef", "Paper", "Typography", "Box", "Chip", "<PERSON><PERSON>", "Switch", "FormControlLabel", "<PERSON><PERSON>", "OrbitControls", "Grid", "Text", "THREE", "findMessageAtTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RobotModel", "position", "rotation", "botModel", "_s", "meshRef", "ref", "children", "args", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "_c", "RobotTrail", "positions", "length", "points", "geometry", "BufferGeometry", "setFromPoints", "_c2", "Scene", "currentOdometry", "trailPositions", "showTrail", "_s2", "robotPosition", "Vector3", "x", "z", "y", "robotRotation", "<PERSON>uler", "quaternion", "Quaternion", "orientation", "w", "setFromQuaternion", "intensity", "cellSize", "cellThickness", "cellColor", "sectionSize", "sectionThickness", "sectionColor", "fadeDistance", "fadeStrength", "followCamera", "infiniteGrid", "fontSize", "anchorX", "anchorY", "enablePan", "enableZoom", "enableRotate", "target", "_c3", "ThreeDViewport", "data", "currentTime", "_s3", "setShowTrail", "odomTopic", "topics", "find", "topic", "name", "toLowerCase", "includes", "_content$pose", "messages", "message", "content", "pose", "timestamp", "_content$pose2", "push", "sx", "p", "height", "display", "flexDirection", "mb", "variant", "component", "gutterBottom", "label", "size", "gap", "alignItems", "control", "checked", "onChange", "e", "toFixed", "severity", "flex", "border", "borderColor", "borderRadius", "camera", "fov", "style", "width", "robotInfo", "mt", "_c4", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/src/components/ThreeDViewport.tsx"], "sourcesContent": ["import React, { useMemo, useState, useRef } from 'react';\nimport {\n  Paper,\n  Typography,\n  Box,\n  Chip,\n  Alert,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport { Canvas } from '@react-three/fiber';\nimport { OrbitControls, Grid, Text } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { IonLogData, findMessageAtTime } from '../utils/ionParser';\n\ninterface ThreeDViewportProps {\n  data: IonLogData;\n  currentTime: number;\n}\n\ninterface OdometryData {\n  position: { x: number; y: number; z: number };\n  orientation: { x: number; y: number; z: number; w: number };\n  timestamp: number;\n}\n\n// Robot model component\nconst RobotModel: React.FC<{ position: THREE.Vector3; rotation: THREE.Euler; botModel?: Uint8Array }> = ({\n  position,\n  rotation,\n  botModel\n}) => {\n  const meshRef = useRef<THREE.Mesh>(null);\n\n  // For now, use a simple box as robot representation\n  // In a real implementation, you would parse the botModel data (likely STL/OBJ format)\n  return (\n    <mesh ref={meshRef} position={position} rotation={rotation}>\n      <boxGeometry args={[0.5, 0.3, 0.2]} />\n      <meshStandardMaterial color=\"orange\" />\n    </mesh>\n  );\n};\n\n// Trail component to show robot path\nconst RobotTrail: React.FC<{ positions: THREE.Vector3[] }> = ({ positions }) => {\n  if (positions.length < 2) return null;\n\n  const points = positions;\n  const geometry = new THREE.BufferGeometry().setFromPoints(points);\n\n  return (\n    <line geometry={geometry}>\n      <lineBasicMaterial color=\"cyan\" />\n    </line>\n  );\n};\n\n// Main 3D scene component\nconst Scene: React.FC<{\n  currentOdometry: OdometryData | null;\n  trailPositions: THREE.Vector3[];\n  showTrail: boolean;\n  botModel?: Uint8Array;\n}> = ({ currentOdometry, trailPositions, showTrail, botModel }) => {\n  // Convert ROS coordinates (z-up) to Three.js coordinates (y-up)\n  const robotPosition = useMemo(() => {\n    if (!currentOdometry) return new THREE.Vector3(0, 0, 0);\n    return new THREE.Vector3(\n      currentOdometry.position.x,\n      currentOdometry.position.z, // ROS z becomes Three.js y\n      -currentOdometry.position.y  // ROS y becomes Three.js -z\n    );\n  }, [currentOdometry]);\n\n  const robotRotation = useMemo(() => {\n    if (!currentOdometry) return new THREE.Euler(0, 0, 0);\n\n    // Convert quaternion to Euler angles and adjust for coordinate system\n    const quaternion = new THREE.Quaternion(\n      currentOdometry.orientation.x,\n      currentOdometry.orientation.z,\n      -currentOdometry.orientation.y,\n      currentOdometry.orientation.w\n    );\n\n    return new THREE.Euler().setFromQuaternion(quaternion);\n  }, [currentOdometry]);\n\n  return (\n    <>\n      {/* Lighting */}\n      <ambientLight intensity={0.6} />\n      <directionalLight position={[10, 10, 5]} intensity={0.8} />\n\n      {/* Ground grid */}\n      <Grid\n        args={[20, 20]}\n        cellSize={1}\n        cellThickness={0.5}\n        cellColor=\"#6f6f6f\"\n        sectionSize={5}\n        sectionThickness={1}\n        sectionColor=\"#9d4b4b\"\n        fadeDistance={30}\n        fadeStrength={1}\n        followCamera={false}\n        infiniteGrid={true}\n      />\n\n      {/* Coordinate system indicators */}\n      <Text\n        position={[1, 0, 0]}\n        fontSize={0.2}\n        color=\"red\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        X\n      </Text>\n      <Text\n        position={[0, 1, 0]}\n        fontSize={0.2}\n        color=\"green\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        Y\n      </Text>\n      <Text\n        position={[0, 0, 1]}\n        fontSize={0.2}\n        color=\"blue\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n      >\n        Z\n      </Text>\n\n      {/* Robot model */}\n      {currentOdometry && (\n        <RobotModel\n          position={robotPosition}\n          rotation={robotRotation}\n          botModel={botModel}\n        />\n      )}\n\n      {/* Robot trail */}\n      {showTrail && <RobotTrail positions={trailPositions} />}\n\n      {/* Camera controls */}\n      <OrbitControls\n        enablePan={true}\n        enableZoom={true}\n        enableRotate={true}\n        target={robotPosition}\n      />\n    </>\n  );\n};\n\nconst ThreeDViewport: React.FC<ThreeDViewportProps> = ({ data, currentTime }) => {\n  const [showTrail, setShowTrail] = useState<boolean>(true);\n\n  // Find the wheel odometry topic\n  const odomTopic = useMemo(() => {\n    return data.topics.find(topic =>\n      topic.name === '/tb_control/wheel_odom' ||\n      topic.name.toLowerCase().includes('odom')\n    );\n  }, [data.topics]);\n\n  // Get current odometry data\n  const currentOdometry = useMemo(() => {\n    if (!odomTopic || odomTopic.messages.length === 0) {\n      return null;\n    }\n\n    const message = findMessageAtTime(odomTopic.messages, currentTime);\n    if (!message) return null;\n\n    const content = message.content;\n    if (!content || typeof content !== 'object') return null;\n\n    // Parse odometry message (typical ROS nav_msgs/Odometry structure)\n    const pose = content.pose?.pose || content.pose || {};\n    const position = pose.position || { x: 0, y: 0, z: 0 };\n    const orientation = pose.orientation || { x: 0, y: 0, z: 0, w: 1 };\n\n    return {\n      position,\n      orientation,\n      timestamp: message.timestamp\n    };\n  }, [odomTopic, currentTime]);\n\n  // Generate trail positions up to current time\n  const trailPositions = useMemo(() => {\n    if (!odomTopic) return [];\n\n    const positions: THREE.Vector3[] = [];\n\n    for (const message of odomTopic.messages) {\n      if (message.timestamp > currentTime) break;\n\n      const content = message.content;\n      if (!content || typeof content !== 'object') continue;\n\n      const pose = content.pose?.pose || content.pose || {};\n      const position = pose.position || { x: 0, y: 0, z: 0 };\n\n      // Convert ROS coordinates to Three.js coordinates\n      positions.push(new THREE.Vector3(\n        position.x,\n        position.z,  // ROS z becomes Three.js y\n        -position.y  // ROS y becomes Three.js -z\n      ));\n    }\n\n    return positions;\n  }, [odomTopic, currentTime]);\n\n  return (\n    <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <Box sx={{ mb: 2 }}>\n        <Typography variant=\"h6\" component=\"h2\" gutterBottom>\n          3D Viewport\n        </Typography>\n        <Chip\n          label=\"Question 5 (Bonus)\"\n          color=\"secondary\"\n          size=\"small\"\n          sx={{ mb: 1 }}\n        />\n      </Box>\n\n      {/* Controls */}\n      <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showTrail}\n              onChange={(e) => setShowTrail(e.target.checked)}\n            />\n          }\n          label=\"Show Trail\"\n        />\n\n        {currentOdometry && (\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            <Chip\n              label={`X: ${currentOdometry.position.x.toFixed(2)}`}\n              size=\"small\"\n              variant=\"outlined\"\n            />\n            <Chip\n              label={`Y: ${currentOdometry.position.y.toFixed(2)}`}\n              size=\"small\"\n              variant=\"outlined\"\n            />\n            <Chip\n              label={`Z: ${currentOdometry.position.z.toFixed(2)}`}\n              size=\"small\"\n              variant=\"outlined\"\n            />\n          </Box>\n        )}\n      </Box>\n\n      {/* 3D Canvas */}\n      {!odomTopic ? (\n        <Alert severity=\"warning\">\n          No odometry topic (/tb_control/wheel_odom) found in the log data\n        </Alert>\n      ) : !currentOdometry ? (\n        <Alert severity=\"info\">\n          No odometry data available at current timestamp\n        </Alert>\n      ) : (\n        <Box sx={{ flex: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>\n          <Canvas\n            camera={{ position: [5, 5, 5], fov: 60 }}\n            style={{ width: '100%', height: '100%' }}\n          >\n            <Scene\n              currentOdometry={currentOdometry}\n              trailPositions={trailPositions}\n              showTrail={showTrail}\n              botModel={data.robotInfo.botModel}\n            />\n          </Canvas>\n        </Box>\n      )}\n\n      {/* Information */}\n      <Box sx={{ mt: 1 }}>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          {odomTopic ? (\n            <>\n              Odometry messages: {odomTopic.messages.length} |\n              Trail points: {trailPositions.length} |\n              3D Model: {data.robotInfo.botModel ? 'Available' : 'Not available'}\n            </>\n          ) : (\n            'No odometry data available'\n          )}\n        </Typography>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default ThreeDViewport;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxD,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,aAAa,EAAEC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAAqBC,iBAAiB,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAanE;AACA,MAAMC,UAA+F,GAAGA,CAAC;EACvGC,QAAQ;EACRC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGvB,MAAM,CAAa,IAAI,CAAC;;EAExC;EACA;EACA,oBACEe,OAAA;IAAMS,GAAG,EAAED,OAAQ;IAACJ,QAAQ,EAAEA,QAAS;IAACC,QAAQ,EAAEA,QAAS;IAAAK,QAAA,gBACzDV,OAAA;MAAaW,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtCf,OAAA;MAAsBgB,KAAK,EAAC;IAAQ;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnC,CAAC;AAEX,CAAC;;AAED;AAAAR,EAAA,CAjBMJ,UAA+F;AAAAc,EAAA,GAA/Fd,UAA+F;AAkBrG,MAAMe,UAAoD,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAC9E,IAAIA,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;EAErC,MAAMC,MAAM,GAAGF,SAAS;EACxB,MAAMG,QAAQ,GAAG,IAAIzB,KAAK,CAAC0B,cAAc,CAAC,CAAC,CAACC,aAAa,CAACH,MAAM,CAAC;EAEjE,oBACErB,OAAA;IAAMsB,QAAQ,EAAEA,QAAS;IAAAZ,QAAA,eACvBV,OAAA;MAAmBgB,KAAK,EAAC;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEX,CAAC;;AAED;AAAAU,GAAA,GAbMP,UAAoD;AAc1D,MAAMQ,KAKJ,GAAGA,CAAC;EAAEC,eAAe;EAAEC,cAAc;EAAEC,SAAS;EAAEvB;AAAS,CAAC,KAAK;EAAAwB,GAAA;EACjE;EACA,MAAMC,aAAa,GAAGhD,OAAO,CAAC,MAAM;IAClC,IAAI,CAAC4C,eAAe,EAAE,OAAO,IAAI9B,KAAK,CAACmC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvD,OAAO,IAAInC,KAAK,CAACmC,OAAO,CACtBL,eAAe,CAACvB,QAAQ,CAAC6B,CAAC,EAC1BN,eAAe,CAACvB,QAAQ,CAAC8B,CAAC;IAAE;IAC5B,CAACP,eAAe,CAACvB,QAAQ,CAAC+B,CAAC,CAAE;IAC/B,CAAC;EACH,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMS,aAAa,GAAGrD,OAAO,CAAC,MAAM;IAClC,IAAI,CAAC4C,eAAe,EAAE,OAAO,IAAI9B,KAAK,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAErD;IACA,MAAMC,UAAU,GAAG,IAAIzC,KAAK,CAAC0C,UAAU,CACrCZ,eAAe,CAACa,WAAW,CAACP,CAAC,EAC7BN,eAAe,CAACa,WAAW,CAACN,CAAC,EAC7B,CAACP,eAAe,CAACa,WAAW,CAACL,CAAC,EAC9BR,eAAe,CAACa,WAAW,CAACC,CAC9B,CAAC;IAED,OAAO,IAAI5C,KAAK,CAACwC,KAAK,CAAC,CAAC,CAACK,iBAAiB,CAACJ,UAAU,CAAC;EACxD,CAAC,EAAE,CAACX,eAAe,CAAC,CAAC;EAErB,oBACE3B,OAAA,CAAAE,SAAA;IAAAQ,QAAA,gBAEEV,OAAA;MAAc2C,SAAS,EAAE;IAAI;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChCf,OAAA;MAAkBI,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;MAACuC,SAAS,EAAE;IAAI;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG3Df,OAAA,CAACL,IAAI;MACHgB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MACfiC,QAAQ,EAAE,CAAE;MACZC,aAAa,EAAE,GAAI;MACnBC,SAAS,EAAC,SAAS;MACnBC,WAAW,EAAE,CAAE;MACfC,gBAAgB,EAAE,CAAE;MACpBC,YAAY,EAAC,SAAS;MACtBC,YAAY,EAAE,EAAG;MACjBC,YAAY,EAAE,CAAE;MAChBC,YAAY,EAAE,KAAM;MACpBC,YAAY,EAAE;IAAK;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFf,OAAA,CAACJ,IAAI;MACHQ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;MACpBkD,QAAQ,EAAE,GAAI;MACdtC,KAAK,EAAC,KAAK;MACXuC,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,QAAQ;MAAA9C,QAAA,EACjB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACPf,OAAA,CAACJ,IAAI;MACHQ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;MACpBkD,QAAQ,EAAE,GAAI;MACdtC,KAAK,EAAC,OAAO;MACbuC,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,QAAQ;MAAA9C,QAAA,EACjB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACPf,OAAA,CAACJ,IAAI;MACHQ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;MACpBkD,QAAQ,EAAE,GAAI;MACdtC,KAAK,EAAC,MAAM;MACZuC,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,QAAQ;MAAA9C,QAAA,EACjB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAGNY,eAAe,iBACd3B,OAAA,CAACG,UAAU;MACTC,QAAQ,EAAE2B,aAAc;MACxB1B,QAAQ,EAAE+B,aAAc;MACxB9B,QAAQ,EAAEA;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACF,EAGAc,SAAS,iBAAI7B,OAAA,CAACkB,UAAU;MAACC,SAAS,EAAES;IAAe;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvDf,OAAA,CAACN,aAAa;MACZ+D,SAAS,EAAE,IAAK;MAChBC,UAAU,EAAE,IAAK;MACjBC,YAAY,EAAE,IAAK;MACnBC,MAAM,EAAE7B;IAAc;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACe,GAAA,CArGIJ,KAKJ;AAAAmC,GAAA,GALInC,KAKJ;AAkGF,MAAMoC,cAA6C,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAC,GAAA;EAC/E,MAAM,CAACpC,SAAS,EAAEqC,YAAY,CAAC,GAAGlF,QAAQ,CAAU,IAAI,CAAC;;EAEzD;EACA,MAAMmF,SAAS,GAAGpF,OAAO,CAAC,MAAM;IAC9B,OAAOgF,IAAI,CAACK,MAAM,CAACC,IAAI,CAACC,KAAK,IAC3BA,KAAK,CAACC,IAAI,KAAK,wBAAwB,IACvCD,KAAK,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAC1C,CAAC;EACH,CAAC,EAAE,CAACV,IAAI,CAACK,MAAM,CAAC,CAAC;;EAEjB;EACA,MAAMzC,eAAe,GAAG5C,OAAO,CAAC,MAAM;IAAA,IAAA2F,aAAA;IACpC,IAAI,CAACP,SAAS,IAAIA,SAAS,CAACQ,QAAQ,CAACvD,MAAM,KAAK,CAAC,EAAE;MACjD,OAAO,IAAI;IACb;IAEA,MAAMwD,OAAO,GAAG9E,iBAAiB,CAACqE,SAAS,CAACQ,QAAQ,EAAEX,WAAW,CAAC;IAClE,IAAI,CAACY,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO;IAC/B,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAExD;IACA,MAAMC,IAAI,GAAG,EAAAJ,aAAA,GAAAG,OAAO,CAACC,IAAI,cAAAJ,aAAA,uBAAZA,aAAA,CAAcI,IAAI,KAAID,OAAO,CAACC,IAAI,IAAI,CAAC,CAAC;IACrD,MAAM1E,QAAQ,GAAG0E,IAAI,CAAC1E,QAAQ,IAAI;MAAE6B,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAED,CAAC,EAAE;IAAE,CAAC;IACtD,MAAMM,WAAW,GAAGsC,IAAI,CAACtC,WAAW,IAAI;MAAEP,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAED,CAAC,EAAE,CAAC;MAAEO,CAAC,EAAE;IAAE,CAAC;IAElE,OAAO;MACLrC,QAAQ;MACRoC,WAAW;MACXuC,SAAS,EAAEH,OAAO,CAACG;IACrB,CAAC;EACH,CAAC,EAAE,CAACZ,SAAS,EAAEH,WAAW,CAAC,CAAC;;EAE5B;EACA,MAAMpC,cAAc,GAAG7C,OAAO,CAAC,MAAM;IACnC,IAAI,CAACoF,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMhD,SAA0B,GAAG,EAAE;IAErC,KAAK,MAAMyD,OAAO,IAAIT,SAAS,CAACQ,QAAQ,EAAE;MAAA,IAAAK,cAAA;MACxC,IAAIJ,OAAO,CAACG,SAAS,GAAGf,WAAW,EAAE;MAErC,MAAMa,OAAO,GAAGD,OAAO,CAACC,OAAO;MAC/B,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAE7C,MAAMC,IAAI,GAAG,EAAAE,cAAA,GAAAH,OAAO,CAACC,IAAI,cAAAE,cAAA,uBAAZA,cAAA,CAAcF,IAAI,KAAID,OAAO,CAACC,IAAI,IAAI,CAAC,CAAC;MACrD,MAAM1E,QAAQ,GAAG0E,IAAI,CAAC1E,QAAQ,IAAI;QAAE6B,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAED,CAAC,EAAE;MAAE,CAAC;;MAEtD;MACAf,SAAS,CAAC8D,IAAI,CAAC,IAAIpF,KAAK,CAACmC,OAAO,CAC9B5B,QAAQ,CAAC6B,CAAC,EACV7B,QAAQ,CAAC8B,CAAC;MAAG;MACb,CAAC9B,QAAQ,CAAC+B,CAAC,CAAE;MACf,CAAC,CAAC;IACJ;IAEA,OAAOhB,SAAS;EAClB,CAAC,EAAE,CAACgD,SAAS,EAAEH,WAAW,CAAC,CAAC;EAE5B,oBACEhE,OAAA,CAACd,KAAK;IAACgG,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAA5E,QAAA,gBAC5EV,OAAA,CAACZ,GAAG;MAAC8F,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAA7E,QAAA,gBACjBV,OAAA,CAACb,UAAU;QAACqG,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAhF,QAAA,EAAC;MAErD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbf,OAAA,CAACX,IAAI;QACHsG,KAAK,EAAC,oBAAoB;QAC1B3E,KAAK,EAAC,WAAW;QACjB4E,IAAI,EAAC,OAAO;QACZV,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE;MAAE;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNf,OAAA,CAACZ,GAAG;MAAC8F,EAAE,EAAE;QAAEK,EAAE,EAAE,CAAC;QAAEF,OAAO,EAAE,MAAM;QAAEQ,GAAG,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAApF,QAAA,gBAChEV,OAAA,CAACR,gBAAgB;QACfuG,OAAO,eACL/F,OAAA,CAACT,MAAM;UACLyG,OAAO,EAAEnE,SAAU;UACnBoE,QAAQ,EAAGC,CAAC,IAAKhC,YAAY,CAACgC,CAAC,CAACtC,MAAM,CAACoC,OAAO;QAAE;UAAApF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CACF;QACD4E,KAAK,EAAC;MAAY;QAAA/E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EAEDY,eAAe,iBACd3B,OAAA,CAACZ,GAAG;QAAC8F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEQ,GAAG,EAAE;QAAE,CAAE;QAAAnF,QAAA,gBACnCV,OAAA,CAACX,IAAI;UACHsG,KAAK,EAAE,MAAMhE,eAAe,CAACvB,QAAQ,CAAC6B,CAAC,CAACkE,OAAO,CAAC,CAAC,CAAC,EAAG;UACrDP,IAAI,EAAC,OAAO;UACZJ,OAAO,EAAC;QAAU;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFf,OAAA,CAACX,IAAI;UACHsG,KAAK,EAAE,MAAMhE,eAAe,CAACvB,QAAQ,CAAC+B,CAAC,CAACgE,OAAO,CAAC,CAAC,CAAC,EAAG;UACrDP,IAAI,EAAC,OAAO;UACZJ,OAAO,EAAC;QAAU;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFf,OAAA,CAACX,IAAI;UACHsG,KAAK,EAAE,MAAMhE,eAAe,CAACvB,QAAQ,CAAC8B,CAAC,CAACiE,OAAO,CAAC,CAAC,CAAC,EAAG;UACrDP,IAAI,EAAC,OAAO;UACZJ,OAAO,EAAC;QAAU;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACoD,SAAS,gBACTnE,OAAA,CAACV,KAAK;MAAC8G,QAAQ,EAAC,SAAS;MAAA1F,QAAA,EAAC;IAE1B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,GACN,CAACY,eAAe,gBAClB3B,OAAA,CAACV,KAAK;MAAC8G,QAAQ,EAAC,MAAM;MAAA1F,QAAA,EAAC;IAEvB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAERf,OAAA,CAACZ,GAAG;MAAC8F,EAAE,EAAE;QAAEmB,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAA9F,QAAA,eACvEV,OAAA,CAACP,MAAM;QACLgH,MAAM,EAAE;UAAErG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAEsG,GAAG,EAAE;QAAG,CAAE;QACzCC,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAExB,MAAM,EAAE;QAAO,CAAE;QAAA1E,QAAA,eAEzCV,OAAA,CAAC0B,KAAK;UACJC,eAAe,EAAEA,eAAgB;UACjCC,cAAc,EAAEA,cAAe;UAC/BC,SAAS,EAAEA,SAAU;UACrBvB,QAAQ,EAAEyD,IAAI,CAAC8C,SAAS,CAACvG;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDf,OAAA,CAACZ,GAAG;MAAC8F,EAAE,EAAE;QAAE4B,EAAE,EAAE;MAAE,CAAE;MAAApG,QAAA,eACjBV,OAAA,CAACb,UAAU;QAACqG,OAAO,EAAC,SAAS;QAACxE,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EACjDyD,SAAS,gBACRnE,OAAA,CAAAE,SAAA;UAAAQ,QAAA,GAAE,qBACmB,EAACyD,SAAS,CAACQ,QAAQ,CAACvD,MAAM,EAAC,mBAChC,EAACQ,cAAc,CAACR,MAAM,EAAC,eAC3B,EAAC2C,IAAI,CAAC8C,SAAS,CAACvG,QAAQ,GAAG,WAAW,GAAG,eAAe;QAAA,eAClE,CAAC,GAEH;MACD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACkD,GAAA,CArJIH,cAA6C;AAAAiD,GAAA,GAA7CjD,cAA6C;AAuJnD,eAAeA,cAAc;AAAC,IAAA7C,EAAA,EAAAQ,GAAA,EAAAoC,GAAA,EAAAkD,GAAA;AAAAC,YAAA,CAAA/F,EAAA;AAAA+F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}