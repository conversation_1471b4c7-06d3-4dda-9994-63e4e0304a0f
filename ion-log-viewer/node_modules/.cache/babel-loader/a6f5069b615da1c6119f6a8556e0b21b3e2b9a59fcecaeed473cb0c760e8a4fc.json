{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { Sequence } from \"./Sequence\";\nexport class SExpression extends Sequence(IonTypes.SEXP) {\n  constructor(children, annotations = []) {\n    super(children, annotations);\n  }\n  toString() {\n    return \"(\" + this.join(\" \") + \")\";\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "Sequence", "SExpression", "SEXP", "constructor", "children", "annotations", "toString", "join"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/SExpression.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { Sequence } from \"./Sequence\";\nexport class SExpression extends Sequence(IonTypes.SEXP) {\n    constructor(children, annotations = []) {\n        super(children, annotations);\n    }\n    toString() {\n        return \"(\" + this.join(\" \") + \")\";\n    }\n}\n//# sourceMappingURL=SExpression.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,WAAW,SAASD,QAAQ,CAACD,QAAQ,CAACG,IAAI,CAAC,CAAC;EACrDC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,EAAE,EAAE;IACpC,KAAK,CAACD,QAAQ,EAAEC,WAAW,CAAC;EAChC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,GAAG,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}