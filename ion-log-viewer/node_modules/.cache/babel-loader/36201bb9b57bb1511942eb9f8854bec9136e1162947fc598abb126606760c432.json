{"ast": null, "code": "import * as ion from \"../Ion\";\nimport { Decimal, IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withClasses(Date, ion.Timestamp).build();\nexport class Timestamp extends Value(Date, IonTypes.TIMESTAMP, _fromJsConstructor) {\n  constructor(dateOrTimestamp, annotations = []) {\n    let date;\n    let timestamp;\n    if (dateOrTimestamp instanceof Date) {\n      date = dateOrTimestamp;\n      timestamp = Timestamp._timestampFromDate(date);\n    } else {\n      timestamp = dateOrTimestamp;\n      date = timestamp.getDate();\n    }\n    super(date);\n    this._date = date;\n    this._timestamp = timestamp;\n    this._setAnnotations(annotations);\n  }\n  static _timestampFromDate(date) {\n    const milliseconds = date.getUTCSeconds() * 1000 + date.getUTCMilliseconds();\n    const fractionalSeconds = new Decimal(milliseconds, -3);\n    return new ion.Timestamp(0, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), fractionalSeconds);\n  }\n  timestampValue() {\n    return this._timestamp;\n  }\n  dateValue() {\n    return this._date;\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeTimestamp(this.timestampValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Timestamp) {\n      isSupportedType = true;\n      valueToCompare = other.timestampValue();\n    } else if (!options.onlyCompareIon) {\n      if (other instanceof ion.Timestamp) {\n        isSupportedType = true;\n        valueToCompare = other;\n      } else if (other instanceof Date) {\n        if (this.dateValue().getTime() === other.getTime()) {\n          return true;\n        } else {\n          return false;\n        }\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    if (options.ignoreTimestampPrecision) {\n      return this.timestampValue().compareTo(valueToCompare) === 0;\n    }\n    return this.timestampValue().equals(valueToCompare);\n  }\n}", "map": {"version": 3, "names": ["ion", "Decimal", "IonTypes", "FromJsConstructorBuilder", "Value", "_fromJsConstructor", "withClasses", "Date", "Timestamp", "build", "TIMESTAMP", "constructor", "dateOrTimestamp", "annotations", "date", "timestamp", "_timestampFromDate", "getDate", "_date", "_timestamp", "_setAnnotations", "milliseconds", "getUTCSeconds", "getUTCMilliseconds", "fractionalSeconds", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "timestampValue", "dateValue", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeTimestamp", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "getTime", "compareTo", "equals"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Timestamp.js"], "sourcesContent": ["import * as ion from \"../Ion\";\nimport { Decimal, IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withClasses(Date, ion.Timestamp)\n    .build();\nexport class Timestamp extends Value(Date, IonTypes.TIMESTAMP, _fromJsConstructor) {\n    constructor(dateOrTimestamp, annotations = []) {\n        let date;\n        let timestamp;\n        if (dateOrTimestamp instanceof Date) {\n            date = dateOrTimestamp;\n            timestamp = Timestamp._timestampFromDate(date);\n        }\n        else {\n            timestamp = dateOrTimestamp;\n            date = timestamp.getDate();\n        }\n        super(date);\n        this._date = date;\n        this._timestamp = timestamp;\n        this._setAnnotations(annotations);\n    }\n    static _timestampFromDate(date) {\n        const milliseconds = date.getUTCSeconds() * 1000 + date.getUTCMilliseconds();\n        const fractionalSeconds = new Decimal(milliseconds, -3);\n        return new ion.Timestamp(0, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), fractionalSeconds);\n    }\n    timestampValue() {\n        return this._timestamp;\n    }\n    dateValue() {\n        return this._date;\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeTimestamp(this.timestampValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Timestamp) {\n            isSupportedType = true;\n            valueToCompare = other.timestampValue();\n        }\n        else if (!options.onlyCompareIon) {\n            if (other instanceof ion.Timestamp) {\n                isSupportedType = true;\n                valueToCompare = other;\n            }\n            else if (other instanceof Date) {\n                if (this.dateValue().getTime() === other.getTime()) {\n                    return true;\n                }\n                else {\n                    return false;\n                }\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        if (options.ignoreTimestampPrecision) {\n            return this.timestampValue().compareTo(valueToCompare) === 0;\n        }\n        return this.timestampValue().equals(valueToCompare);\n    }\n}\n//# sourceMappingURL=Timestamp.js.map"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,QAAQ;AAC7B,SAASC,OAAO,EAAEC,QAAQ,QAAQ,QAAQ;AAC1C,SAASC,wBAAwB,QAAS,qBAAqB;AAC/D,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIF,wBAAwB,CAAC,CAAC,CACpDG,WAAW,CAACC,IAAI,EAAEP,GAAG,CAACQ,SAAS,CAAC,CAChCC,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMD,SAAS,SAASJ,KAAK,CAACG,IAAI,EAAEL,QAAQ,CAACQ,SAAS,EAAEL,kBAAkB,CAAC,CAAC;EAC/EM,WAAWA,CAACC,eAAe,EAAEC,WAAW,GAAG,EAAE,EAAE;IAC3C,IAAIC,IAAI;IACR,IAAIC,SAAS;IACb,IAAIH,eAAe,YAAYL,IAAI,EAAE;MACjCO,IAAI,GAAGF,eAAe;MACtBG,SAAS,GAAGP,SAAS,CAACQ,kBAAkB,CAACF,IAAI,CAAC;IAClD,CAAC,MACI;MACDC,SAAS,GAAGH,eAAe;MAC3BE,IAAI,GAAGC,SAAS,CAACE,OAAO,CAAC,CAAC;IAC9B;IACA,KAAK,CAACH,IAAI,CAAC;IACX,IAAI,CAACI,KAAK,GAAGJ,IAAI;IACjB,IAAI,CAACK,UAAU,GAAGJ,SAAS;IAC3B,IAAI,CAACK,eAAe,CAACP,WAAW,CAAC;EACrC;EACA,OAAOG,kBAAkBA,CAACF,IAAI,EAAE;IAC5B,MAAMO,YAAY,GAAGP,IAAI,CAACQ,aAAa,CAAC,CAAC,GAAG,IAAI,GAAGR,IAAI,CAACS,kBAAkB,CAAC,CAAC;IAC5E,MAAMC,iBAAiB,GAAG,IAAIvB,OAAO,CAACoB,YAAY,EAAE,CAAC,CAAC,CAAC;IACvD,OAAO,IAAIrB,GAAG,CAACQ,SAAS,CAAC,CAAC,EAAEM,IAAI,CAACW,cAAc,CAAC,CAAC,EAAEX,IAAI,CAACY,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEZ,IAAI,CAACa,UAAU,CAAC,CAAC,EAAEb,IAAI,CAACc,WAAW,CAAC,CAAC,EAAEd,IAAI,CAACe,aAAa,CAAC,CAAC,EAAEL,iBAAiB,CAAC;EAC9J;EACAM,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACX,UAAU;EAC1B;EACAY,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACb,KAAK;EACrB;EACAc,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,cAAc,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAAC;EAChD;EACAO,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAY9B,SAAS,EAAE;MAC5BoC,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK,CAACR,cAAc,CAAC,CAAC;IAC3C,CAAC,MACI,IAAI,CAACS,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAIL,KAAK,YAAYtC,GAAG,CAACQ,SAAS,EAAE;QAChCoC,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGP,KAAK;MAC1B,CAAC,MACI,IAAIA,KAAK,YAAY/B,IAAI,EAAE;QAC5B,IAAI,IAAI,CAACwB,SAAS,CAAC,CAAC,CAACe,OAAO,CAAC,CAAC,KAAKR,KAAK,CAACQ,OAAO,CAAC,CAAC,EAAE;UAChD,OAAO,IAAI;QACf,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,IAAI,CAACF,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAIL,OAAO,CAACG,wBAAwB,EAAE;MAClC,OAAO,IAAI,CAACZ,cAAc,CAAC,CAAC,CAACiB,SAAS,CAACF,cAAc,CAAC,KAAK,CAAC;IAChE;IACA,OAAO,IAAI,CAACf,cAAc,CAAC,CAAC,CAACkB,MAAM,CAACH,cAAc,CAAC;EACvD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}