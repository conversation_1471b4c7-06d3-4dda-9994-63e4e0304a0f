{"ast": null, "code": "import { LineSegments, BufferGeometry, Float32BufferAttribute, LineBasicMaterial, Vector3, Matrix3 } from \"three\";\nconst _v1 = /* @__PURE__ */new Vector3();\nconst _v2 = /* @__PURE__ */new Vector3();\nconst _normalMatrix = /* @__PURE__ */new Matrix3();\nclass VertexNormalsHelper extends LineSegments {\n  constructor(object, size = 1, color = 16711680) {\n    const geometry = new BufferGeometry();\n    const nNormals = object.geometry.attributes.normal.count;\n    const positions = new Float32BufferAttribute(nNormals * 2 * 3, 3);\n    geometry.setAttribute(\"position\", positions);\n    super(geometry, new LineBasicMaterial({\n      color,\n      toneMapped: false\n    }));\n    this.object = object;\n    this.size = size;\n    this.type = \"VertexNormalsHelper\";\n    this.matrixAutoUpdate = false;\n    this.update();\n  }\n  update() {\n    this.object.updateMatrixWorld(true);\n    _normalMatrix.getNormalMatrix(this.object.matrixWorld);\n    const matrixWorld = this.object.matrixWorld;\n    const position = this.geometry.attributes.position;\n    const objGeometry = this.object.geometry;\n    if (objGeometry) {\n      const objPos = objGeometry.attributes.position;\n      const objNorm = objGeometry.attributes.normal;\n      let idx = 0;\n      for (let j = 0, jl = objPos.count; j < jl; j++) {\n        _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld);\n        _v2.fromBufferAttribute(objNorm, j);\n        _v2.applyMatrix3(_normalMatrix).normalize().multiplyScalar(this.size).add(_v1);\n        position.setXYZ(idx, _v1.x, _v1.y, _v1.z);\n        idx = idx + 1;\n        position.setXYZ(idx, _v2.x, _v2.y, _v2.z);\n        idx = idx + 1;\n      }\n    }\n    position.needsUpdate = true;\n  }\n  dispose() {\n    this.geometry.dispose();\n    this.material.dispose();\n  }\n}\nexport { VertexNormalsHelper };", "map": {"version": 3, "names": ["_v1", "Vector3", "_v2", "_normalMatrix", "Matrix3", "VertexNormalsHelper", "LineSegments", "constructor", "object", "size", "color", "geometry", "BufferGeometry", "nNormals", "attributes", "normal", "count", "positions", "Float32BufferAttribute", "setAttribute", "LineBasicMaterial", "toneMapped", "type", "matrixAutoUpdate", "update", "updateMatrixWorld", "getNormalMatrix", "matrixWorld", "position", "objGeometry", "objPos", "objNorm", "idx", "j", "jl", "fromBufferAttribute", "applyMatrix4", "applyMatrix3", "normalize", "multiplyScalar", "add", "setXYZ", "x", "y", "z", "needsUpdate", "dispose", "material"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/helpers/VertexNormalsHelper.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, LineSegments, LineBasicMaterial, Matrix3, Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _normalMatrix = /* @__PURE__ */ new Matrix3()\n\nclass VertexNormalsHelper extends LineSegments {\n  constructor(object, size = 1, color = 0xff0000) {\n    const geometry = new BufferGeometry()\n\n    const nNormals = object.geometry.attributes.normal.count\n    const positions = new Float32BufferAttribute(nNormals * 2 * 3, 3)\n\n    geometry.setAttribute('position', positions)\n\n    super(geometry, new LineBasicMaterial({ color, toneMapped: false }))\n\n    this.object = object\n    this.size = size\n    this.type = 'VertexNormalsHelper'\n\n    //\n\n    this.matrixAutoUpdate = false\n\n    this.update()\n  }\n\n  update() {\n    this.object.updateMatrixWorld(true)\n\n    _normalMatrix.getNormalMatrix(this.object.matrixWorld)\n\n    const matrixWorld = this.object.matrixWorld\n\n    const position = this.geometry.attributes.position\n\n    //\n\n    const objGeometry = this.object.geometry\n\n    if (objGeometry) {\n      const objPos = objGeometry.attributes.position\n\n      const objNorm = objGeometry.attributes.normal\n\n      let idx = 0\n\n      // for simplicity, ignore index and drawcalls, and render every normal\n\n      for (let j = 0, jl = objPos.count; j < jl; j++) {\n        _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld)\n\n        _v2.fromBufferAttribute(objNorm, j)\n\n        _v2.applyMatrix3(_normalMatrix).normalize().multiplyScalar(this.size).add(_v1)\n\n        position.setXYZ(idx, _v1.x, _v1.y, _v1.z)\n\n        idx = idx + 1\n\n        position.setXYZ(idx, _v2.x, _v2.y, _v2.z)\n\n        idx = idx + 1\n      }\n    }\n\n    position.needsUpdate = true\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { VertexNormalsHelper }\n"], "mappings": ";AAEA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,GAAA,GAAsB,mBAAID,OAAA,CAAS;AACzC,MAAME,aAAA,GAAgC,mBAAIC,OAAA,CAAS;AAEnD,MAAMC,mBAAA,SAA4BC,YAAA,CAAa;EAC7CC,YAAYC,MAAA,EAAQC,IAAA,GAAO,GAAGC,KAAA,GAAQ,UAAU;IAC9C,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAErC,MAAMC,QAAA,GAAWL,MAAA,CAAOG,QAAA,CAASG,UAAA,CAAWC,MAAA,CAAOC,KAAA;IACnD,MAAMC,SAAA,GAAY,IAAIC,sBAAA,CAAuBL,QAAA,GAAW,IAAI,GAAG,CAAC;IAEhEF,QAAA,CAASQ,YAAA,CAAa,YAAYF,SAAS;IAE3C,MAAMN,QAAA,EAAU,IAAIS,iBAAA,CAAkB;MAAEV,KAAA;MAAOW,UAAA,EAAY;IAAK,CAAE,CAAC;IAEnE,KAAKb,MAAA,GAASA,MAAA;IACd,KAAKC,IAAA,GAAOA,IAAA;IACZ,KAAKa,IAAA,GAAO;IAIZ,KAAKC,gBAAA,GAAmB;IAExB,KAAKC,MAAA,CAAQ;EACd;EAEDA,OAAA,EAAS;IACP,KAAKhB,MAAA,CAAOiB,iBAAA,CAAkB,IAAI;IAElCtB,aAAA,CAAcuB,eAAA,CAAgB,KAAKlB,MAAA,CAAOmB,WAAW;IAErD,MAAMA,WAAA,GAAc,KAAKnB,MAAA,CAAOmB,WAAA;IAEhC,MAAMC,QAAA,GAAW,KAAKjB,QAAA,CAASG,UAAA,CAAWc,QAAA;IAI1C,MAAMC,WAAA,GAAc,KAAKrB,MAAA,CAAOG,QAAA;IAEhC,IAAIkB,WAAA,EAAa;MACf,MAAMC,MAAA,GAASD,WAAA,CAAYf,UAAA,CAAWc,QAAA;MAEtC,MAAMG,OAAA,GAAUF,WAAA,CAAYf,UAAA,CAAWC,MAAA;MAEvC,IAAIiB,GAAA,GAAM;MAIV,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKJ,MAAA,CAAOd,KAAA,EAAOiB,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9CjC,GAAA,CAAImC,mBAAA,CAAoBL,MAAA,EAAQG,CAAC,EAAEG,YAAA,CAAaT,WAAW;QAE3DzB,GAAA,CAAIiC,mBAAA,CAAoBJ,OAAA,EAASE,CAAC;QAElC/B,GAAA,CAAImC,YAAA,CAAalC,aAAa,EAAEmC,SAAA,CAAW,EAACC,cAAA,CAAe,KAAK9B,IAAI,EAAE+B,GAAA,CAAIxC,GAAG;QAE7E4B,QAAA,CAASa,MAAA,CAAOT,GAAA,EAAKhC,GAAA,CAAI0C,CAAA,EAAG1C,GAAA,CAAI2C,CAAA,EAAG3C,GAAA,CAAI4C,CAAC;QAExCZ,GAAA,GAAMA,GAAA,GAAM;QAEZJ,QAAA,CAASa,MAAA,CAAOT,GAAA,EAAK9B,GAAA,CAAIwC,CAAA,EAAGxC,GAAA,CAAIyC,CAAA,EAAGzC,GAAA,CAAI0C,CAAC;QAExCZ,GAAA,GAAMA,GAAA,GAAM;MACb;IACF;IAEDJ,QAAA,CAASiB,WAAA,GAAc;EACxB;EAEDC,QAAA,EAAU;IACR,KAAKnC,QAAA,CAASmC,OAAA,CAAS;IACvB,KAAKC,QAAA,CAASD,OAAA,CAAS;EACxB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}