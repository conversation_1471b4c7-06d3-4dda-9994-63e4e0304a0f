{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { EOF } from \"./IonConstants\";\nconst SPAN_TYPE_STRING = 0;\nconst SPAN_TYPE_BINARY = 1;\nconst SPAN_TYPE_SUB_FLAG = 2;\nconst SPAN_TYPE_SUB_STRING = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_STRING;\nconst SPAN_TYPE_SUB_BINARY = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_BINARY;\nconst MAX_POS = 1024 * 1024 * 1024;\nconst LINE_FEED = 10;\nconst CARRAIGE_RETURN = 13;\nconst DEBUG_FLAG = true;\nexport class Span {\n  constructor(_type) {\n    this._type = _type;\n  }\n  static error() {\n    throw new Error(\"span error\");\n  }\n  write(b) {\n    throw new Error(\"not implemented\");\n  }\n}\nexport class StringSpan extends Span {\n  constructor(src) {\n    super(SPAN_TYPE_STRING);\n    this._line = 1;\n    this._src = src;\n    this._limit = src.length;\n    this._start = 0;\n    this._pos = 0;\n    this._line_start = 0;\n    this._old_line_start = 0;\n  }\n  viewSource() {\n    return this._src;\n  }\n  position() {\n    return this._pos - this._start;\n  }\n  getRemaining() {\n    return this._limit - this._pos;\n  }\n  setRemaining(r) {\n    this._limit = r + this._pos;\n  }\n  is_empty() {\n    return this._pos >= this._limit;\n  }\n  next() {\n    let ch;\n    if (this.is_empty()) {\n      if (this._pos > MAX_POS) {\n        throw new Error(\"span position is out of bounds\");\n      }\n      this._pos++;\n      return EOF;\n    }\n    ch = this._src.charCodeAt(this._pos);\n    if (ch === CARRAIGE_RETURN) {\n      if (this.peek() != LINE_FEED) {\n        this._inc_line();\n      }\n    } else if (ch == LINE_FEED) {\n      this._inc_line();\n    }\n    this._pos++;\n    return ch;\n  }\n  _inc_line() {\n    this._old_line_start = this._line_start;\n    this._line++;\n    this._line_start = this._pos;\n  }\n  unread(ch) {\n    if (this._pos <= this._start) {\n      Span.error();\n    }\n    this._pos--;\n    if (ch < 0) {\n      if (this.is_empty() != true) {\n        Span.error();\n      }\n      return;\n    }\n    if (this._pos == this._line_start) {\n      this._line_start = this._old_line_start;\n      this._line--;\n    }\n    if (ch != this.peek()) {\n      Span.error();\n    }\n  }\n  peek() {\n    return this.valueAt(this._pos);\n  }\n  skip(dist) {\n    this._pos += dist;\n    if (this._pos > this._limit) {\n      this._pos = this._limit;\n    }\n  }\n  valueAt(ii) {\n    if (ii < this._start || ii >= this._limit) {\n      return EOF;\n    }\n    return this._src.charCodeAt(ii);\n  }\n  chunk(length) {\n    const tempStr = this._src.substr(this._pos, length);\n    this._pos += length;\n    return tempStr;\n  }\n  getCodePoint(index) {\n    return this._src.codePointAt(index);\n  }\n  line_number() {\n    return this._line;\n  }\n  offset() {\n    return this._pos - this._line_start;\n  }\n  clone(start) {\n    return new StringSpan(this._src.substr(this._pos));\n  }\n}\nexport class BinarySpan extends Span {\n  constructor(src) {\n    super(SPAN_TYPE_BINARY);\n    this._src = src;\n    this._limit = src.length;\n    this._start = 0;\n    this._pos = 0;\n  }\n  position() {\n    return this._pos - this._start;\n  }\n  getRemaining() {\n    return this._limit - this._pos;\n  }\n  setRemaining(r) {\n    this._limit = r + this._pos;\n  }\n  is_empty() {\n    return this._pos >= this._limit;\n  }\n  next() {\n    if (this.is_empty()) {\n      return EOF;\n    }\n    return this._src[this._pos++];\n  }\n  view(length) {\n    if (this._pos + length > this._limit) {\n      throw new Error(\"Unable to read \" + length + \" bytes (position: \" + this.position() + \", limit: \" + this._limit + \")\");\n    }\n    return this._src.subarray(this._pos, this._pos += length);\n  }\n  chunk(length) {\n    return new Uint8Array(this.view(length));\n  }\n  unread(b) {\n    if (this._pos <= this._start) {\n      Span.error();\n    }\n    this._pos--;\n    if (b == EOF) {\n      if (this.is_empty() == false) {\n        Span.error();\n      }\n    }\n    if (b != this.peek()) {\n      Span.error();\n    }\n  }\n  peek() {\n    if (this.is_empty()) {\n      return EOF;\n    }\n    return this._src[this._pos];\n  }\n  skip(dist) {\n    this._pos += dist;\n    if (this._pos > this._limit) {\n      throw new Error(\"Skipped over end of source.\");\n    }\n  }\n  valueAt(ii) {\n    if (ii < this._start || ii >= this._limit) {\n      return EOF;\n    }\n    return this._src[ii];\n  }\n  clone(start, len) {\n    return new BinarySpan(this._src.subarray(this._pos));\n  }\n}", "map": {"version": 3, "names": ["EOF", "SPAN_TYPE_STRING", "SPAN_TYPE_BINARY", "SPAN_TYPE_SUB_FLAG", "SPAN_TYPE_SUB_STRING", "SPAN_TYPE_SUB_BINARY", "MAX_POS", "LINE_FEED", "CARRAIGE_RETURN", "DEBUG_FLAG", "Span", "constructor", "_type", "error", "Error", "write", "b", "StringSpan", "src", "_line", "_src", "_limit", "length", "_start", "_pos", "_line_start", "_old_line_start", "viewSource", "position", "getRemaining", "setRemaining", "r", "is_empty", "next", "ch", "charCodeAt", "peek", "_inc_line", "unread", "valueAt", "skip", "dist", "ii", "chunk", "tempStr", "substr", "getCodePoint", "index", "codePointAt", "line_number", "offset", "clone", "start", "BinarySpan", "view", "subarray", "Uint8Array", "len"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSpan.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { EOF } from \"./IonConstants\";\nconst SPAN_TYPE_STRING = 0;\nconst SPAN_TYPE_BINARY = 1;\nconst SPAN_TYPE_SUB_FLAG = 2;\nconst SPAN_TYPE_SUB_STRING = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_STRING;\nconst SPAN_TYPE_SUB_BINARY = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_BINARY;\nconst MAX_POS = 1024 * 1024 * 1024;\nconst LINE_FEED = 10;\nconst CARRAIGE_RETURN = 13;\nconst DEBUG_FLAG = true;\nexport class Span {\n    constructor(_type) {\n        this._type = _type;\n    }\n    static error() {\n        throw new Error(\"span error\");\n    }\n    write(b) {\n        throw new Error(\"not implemented\");\n    }\n}\nexport class StringSpan extends Span {\n    constructor(src) {\n        super(SPAN_TYPE_STRING);\n        this._line = 1;\n        this._src = src;\n        this._limit = src.length;\n        this._start = 0;\n        this._pos = 0;\n        this._line_start = 0;\n        this._old_line_start = 0;\n    }\n    viewSource() {\n        return this._src;\n    }\n    position() {\n        return this._pos - this._start;\n    }\n    getRemaining() {\n        return this._limit - this._pos;\n    }\n    setRemaining(r) {\n        this._limit = r + this._pos;\n    }\n    is_empty() {\n        return this._pos >= this._limit;\n    }\n    next() {\n        let ch;\n        if (this.is_empty()) {\n            if (this._pos > MAX_POS) {\n                throw new Error(\"span position is out of bounds\");\n            }\n            this._pos++;\n            return EOF;\n        }\n        ch = this._src.charCodeAt(this._pos);\n        if (ch === CARRAIGE_RETURN) {\n            if (this.peek() != LINE_FEED) {\n                this._inc_line();\n            }\n        }\n        else if (ch == LINE_FEED) {\n            this._inc_line();\n        }\n        this._pos++;\n        return ch;\n    }\n    _inc_line() {\n        this._old_line_start = this._line_start;\n        this._line++;\n        this._line_start = this._pos;\n    }\n    unread(ch) {\n        if (this._pos <= this._start) {\n            Span.error();\n        }\n        this._pos--;\n        if (ch < 0) {\n            if (this.is_empty() != true) {\n                Span.error();\n            }\n            return;\n        }\n        if (this._pos == this._line_start) {\n            this._line_start = this._old_line_start;\n            this._line--;\n        }\n        if (ch != this.peek()) {\n            Span.error();\n        }\n    }\n    peek() {\n        return this.valueAt(this._pos);\n    }\n    skip(dist) {\n        this._pos += dist;\n        if (this._pos > this._limit) {\n            this._pos = this._limit;\n        }\n    }\n    valueAt(ii) {\n        if (ii < this._start || ii >= this._limit) {\n            return EOF;\n        }\n        return this._src.charCodeAt(ii);\n    }\n    chunk(length) {\n        const tempStr = this._src.substr(this._pos, length);\n        this._pos += length;\n        return tempStr;\n    }\n    getCodePoint(index) {\n        return this._src.codePointAt(index);\n    }\n    line_number() {\n        return this._line;\n    }\n    offset() {\n        return this._pos - this._line_start;\n    }\n    clone(start) {\n        return new StringSpan(this._src.substr(this._pos));\n    }\n}\nexport class BinarySpan extends Span {\n    constructor(src) {\n        super(SPAN_TYPE_BINARY);\n        this._src = src;\n        this._limit = src.length;\n        this._start = 0;\n        this._pos = 0;\n    }\n    position() {\n        return this._pos - this._start;\n    }\n    getRemaining() {\n        return this._limit - this._pos;\n    }\n    setRemaining(r) {\n        this._limit = r + this._pos;\n    }\n    is_empty() {\n        return this._pos >= this._limit;\n    }\n    next() {\n        if (this.is_empty()) {\n            return EOF;\n        }\n        return this._src[this._pos++];\n    }\n    view(length) {\n        if (this._pos + length > this._limit) {\n            throw new Error(\"Unable to read \" +\n                length +\n                \" bytes (position: \" +\n                this.position() +\n                \", limit: \" +\n                this._limit +\n                \")\");\n        }\n        return this._src.subarray(this._pos, (this._pos += length));\n    }\n    chunk(length) {\n        return new Uint8Array(this.view(length));\n    }\n    unread(b) {\n        if (this._pos <= this._start) {\n            Span.error();\n        }\n        this._pos--;\n        if (b == EOF) {\n            if (this.is_empty() == false) {\n                Span.error();\n            }\n        }\n        if (b != this.peek()) {\n            Span.error();\n        }\n    }\n    peek() {\n        if (this.is_empty()) {\n            return EOF;\n        }\n        return this._src[this._pos];\n    }\n    skip(dist) {\n        this._pos += dist;\n        if (this._pos > this._limit) {\n            throw new Error(\"Skipped over end of source.\");\n        }\n    }\n    valueAt(ii) {\n        if (ii < this._start || ii >= this._limit) {\n            return EOF;\n        }\n        return this._src[ii];\n    }\n    clone(start, len) {\n        return new BinarySpan(this._src.subarray(this._pos));\n    }\n}\n//# sourceMappingURL=IonSpan.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,gBAAgB;AACpC,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,oBAAoB,GAAGD,kBAAkB,GAAGF,gBAAgB;AAClE,MAAMI,oBAAoB,GAAGF,kBAAkB,GAAGD,gBAAgB;AAClE,MAAMI,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;AAClC,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,UAAU,GAAG,IAAI;AACvB,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACA,OAAOC,KAAKA,CAAA,EAAG;IACX,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;EACjC;EACAC,KAAKA,CAACC,CAAC,EAAE;IACL,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACJ;AACA,OAAO,MAAMG,UAAU,SAASP,IAAI,CAAC;EACjCC,WAAWA,CAACO,GAAG,EAAE;IACb,KAAK,CAACjB,gBAAgB,CAAC;IACvB,IAAI,CAACkB,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAGF,GAAG;IACf,IAAI,CAACG,MAAM,GAAGH,GAAG,CAACI,MAAM;IACxB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC5B;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,IAAI;EACpB;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACD,MAAM;EAClC;EACAM,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,MAAM,GAAG,IAAI,CAACG,IAAI;EAClC;EACAM,YAAYA,CAACC,CAAC,EAAE;IACZ,IAAI,CAACV,MAAM,GAAGU,CAAC,GAAG,IAAI,CAACP,IAAI;EAC/B;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACR,IAAI,IAAI,IAAI,CAACH,MAAM;EACnC;EACAY,IAAIA,CAAA,EAAG;IACH,IAAIC,EAAE;IACN,IAAI,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAE;MACjB,IAAI,IAAI,CAACR,IAAI,GAAGlB,OAAO,EAAE;QACrB,MAAM,IAAIQ,KAAK,CAAC,gCAAgC,CAAC;MACrD;MACA,IAAI,CAACU,IAAI,EAAE;MACX,OAAOxB,GAAG;IACd;IACAkC,EAAE,GAAG,IAAI,CAACd,IAAI,CAACe,UAAU,CAAC,IAAI,CAACX,IAAI,CAAC;IACpC,IAAIU,EAAE,KAAK1B,eAAe,EAAE;MACxB,IAAI,IAAI,CAAC4B,IAAI,CAAC,CAAC,IAAI7B,SAAS,EAAE;QAC1B,IAAI,CAAC8B,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC,MACI,IAAIH,EAAE,IAAI3B,SAAS,EAAE;MACtB,IAAI,CAAC8B,SAAS,CAAC,CAAC;IACpB;IACA,IAAI,CAACb,IAAI,EAAE;IACX,OAAOU,EAAE;EACb;EACAG,SAASA,CAAA,EAAG;IACR,IAAI,CAACX,eAAe,GAAG,IAAI,CAACD,WAAW;IACvC,IAAI,CAACN,KAAK,EAAE;IACZ,IAAI,CAACM,WAAW,GAAG,IAAI,CAACD,IAAI;EAChC;EACAc,MAAMA,CAACJ,EAAE,EAAE;IACP,IAAI,IAAI,CAACV,IAAI,IAAI,IAAI,CAACD,MAAM,EAAE;MAC1Bb,IAAI,CAACG,KAAK,CAAC,CAAC;IAChB;IACA,IAAI,CAACW,IAAI,EAAE;IACX,IAAIU,EAAE,GAAG,CAAC,EAAE;MACR,IAAI,IAAI,CAACF,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;QACzBtB,IAAI,CAACG,KAAK,CAAC,CAAC;MAChB;MACA;IACJ;IACA,IAAI,IAAI,CAACW,IAAI,IAAI,IAAI,CAACC,WAAW,EAAE;MAC/B,IAAI,CAACA,WAAW,GAAG,IAAI,CAACC,eAAe;MACvC,IAAI,CAACP,KAAK,EAAE;IAChB;IACA,IAAIe,EAAE,IAAI,IAAI,CAACE,IAAI,CAAC,CAAC,EAAE;MACnB1B,IAAI,CAACG,KAAK,CAAC,CAAC;IAChB;EACJ;EACAuB,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACG,OAAO,CAAC,IAAI,CAACf,IAAI,CAAC;EAClC;EACAgB,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACjB,IAAI,IAAIiB,IAAI;IACjB,IAAI,IAAI,CAACjB,IAAI,GAAG,IAAI,CAACH,MAAM,EAAE;MACzB,IAAI,CAACG,IAAI,GAAG,IAAI,CAACH,MAAM;IAC3B;EACJ;EACAkB,OAAOA,CAACG,EAAE,EAAE;IACR,IAAIA,EAAE,GAAG,IAAI,CAACnB,MAAM,IAAImB,EAAE,IAAI,IAAI,CAACrB,MAAM,EAAE;MACvC,OAAOrB,GAAG;IACd;IACA,OAAO,IAAI,CAACoB,IAAI,CAACe,UAAU,CAACO,EAAE,CAAC;EACnC;EACAC,KAAKA,CAACrB,MAAM,EAAE;IACV,MAAMsB,OAAO,GAAG,IAAI,CAACxB,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAEF,MAAM,CAAC;IACnD,IAAI,CAACE,IAAI,IAAIF,MAAM;IACnB,OAAOsB,OAAO;EAClB;EACAE,YAAYA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI,CAAC3B,IAAI,CAAC4B,WAAW,CAACD,KAAK,CAAC;EACvC;EACAE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9B,KAAK;EACrB;EACA+B,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC1B,IAAI,GAAG,IAAI,CAACC,WAAW;EACvC;EACA0B,KAAKA,CAACC,KAAK,EAAE;IACT,OAAO,IAAInC,UAAU,CAAC,IAAI,CAACG,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACrB,IAAI,CAAC,CAAC;EACtD;AACJ;AACA,OAAO,MAAM6B,UAAU,SAAS3C,IAAI,CAAC;EACjCC,WAAWA,CAACO,GAAG,EAAE;IACb,KAAK,CAAChB,gBAAgB,CAAC;IACvB,IAAI,CAACkB,IAAI,GAAGF,GAAG;IACf,IAAI,CAACG,MAAM,GAAGH,GAAG,CAACI,MAAM;IACxB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACD,MAAM;EAClC;EACAM,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,MAAM,GAAG,IAAI,CAACG,IAAI;EAClC;EACAM,YAAYA,CAACC,CAAC,EAAE;IACZ,IAAI,CAACV,MAAM,GAAGU,CAAC,GAAG,IAAI,CAACP,IAAI;EAC/B;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACR,IAAI,IAAI,IAAI,CAACH,MAAM;EACnC;EACAY,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAOhC,GAAG;IACd;IACA,OAAO,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACI,IAAI,EAAE,CAAC;EACjC;EACA8B,IAAIA,CAAChC,MAAM,EAAE;IACT,IAAI,IAAI,CAACE,IAAI,GAAGF,MAAM,GAAG,IAAI,CAACD,MAAM,EAAE;MAClC,MAAM,IAAIP,KAAK,CAAC,iBAAiB,GAC7BQ,MAAM,GACN,oBAAoB,GACpB,IAAI,CAACM,QAAQ,CAAC,CAAC,GACf,WAAW,GACX,IAAI,CAACP,MAAM,GACX,GAAG,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAAC/B,IAAI,EAAG,IAAI,CAACA,IAAI,IAAIF,MAAO,CAAC;EAC/D;EACAqB,KAAKA,CAACrB,MAAM,EAAE;IACV,OAAO,IAAIkC,UAAU,CAAC,IAAI,CAACF,IAAI,CAAChC,MAAM,CAAC,CAAC;EAC5C;EACAgB,MAAMA,CAACtB,CAAC,EAAE;IACN,IAAI,IAAI,CAACQ,IAAI,IAAI,IAAI,CAACD,MAAM,EAAE;MAC1Bb,IAAI,CAACG,KAAK,CAAC,CAAC;IAChB;IACA,IAAI,CAACW,IAAI,EAAE;IACX,IAAIR,CAAC,IAAIhB,GAAG,EAAE;MACV,IAAI,IAAI,CAACgC,QAAQ,CAAC,CAAC,IAAI,KAAK,EAAE;QAC1BtB,IAAI,CAACG,KAAK,CAAC,CAAC;MAChB;IACJ;IACA,IAAIG,CAAC,IAAI,IAAI,CAACoB,IAAI,CAAC,CAAC,EAAE;MAClB1B,IAAI,CAACG,KAAK,CAAC,CAAC;IAChB;EACJ;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACJ,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAOhC,GAAG;IACd;IACA,OAAO,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACI,IAAI,CAAC;EAC/B;EACAgB,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACjB,IAAI,IAAIiB,IAAI;IACjB,IAAI,IAAI,CAACjB,IAAI,GAAG,IAAI,CAACH,MAAM,EAAE;MACzB,MAAM,IAAIP,KAAK,CAAC,6BAA6B,CAAC;IAClD;EACJ;EACAyB,OAAOA,CAACG,EAAE,EAAE;IACR,IAAIA,EAAE,GAAG,IAAI,CAACnB,MAAM,IAAImB,EAAE,IAAI,IAAI,CAACrB,MAAM,EAAE;MACvC,OAAOrB,GAAG;IACd;IACA,OAAO,IAAI,CAACoB,IAAI,CAACsB,EAAE,CAAC;EACxB;EACAS,KAAKA,CAACC,KAAK,EAAEK,GAAG,EAAE;IACd,OAAO,IAAIJ,UAAU,CAAC,IAAI,CAACjC,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAAC/B,IAAI,CAAC,CAAC;EACxD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}