{"ast": null, "code": "import { Matrix3 } from 'three';\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n  var a = terms[0],\n    b = terms[1],\n    c = terms[2],\n    d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n  var a = terms[0],\n    b = terms[1],\n    c = terms[2],\n    d = terms[3],\n    e = terms[4],\n    f = terms[5],\n    g = terms[6],\n    h = terms[7],\n    i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n  terms[0];\n  terms[1];\n  terms[2];\n  terms[3];\n  terms[4];\n  terms[5];\n  terms[6];\n  terms[7];\n  terms[8];\n  terms[9];\n  terms[10];\n  terms[11];\n  terms[12];\n  terms[13];\n  terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n  return new Matrix3().fromArray(sum);\n}\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\nexport { matrixSum3 as a, determinant2 as b, determinant4 as c, determinant3 as d, getMinor as g, matrix as m };", "map": {"version": 3, "names": ["Matrix3", "determinant2", "_len", "arguments", "length", "terms", "Array", "_key", "a", "b", "c", "d", "determinant3", "_len2", "_key2", "e", "f", "g", "h", "i", "determinant4", "_len3", "_key3", "getMinor", "matrix", "r", "_matrixTranspose", "clone", "transpose", "x", "l", "elements", "n", "Math", "sqrt", "element", "row", "floor", "col", "push", "apply", "matrixSum3", "m1", "m2", "sum", "m1Array", "toArray", "m2Array", "fromArray", "Object", "freeze", "__proto__", "m"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/maath/dist/matrix-baa530bf.esm.js"], "sourcesContent": ["import { Matrix3 } from 'three';\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3],\n      e = terms[4],\n      f = terms[5],\n      g = terms[6],\n      h = terms[7],\n      i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n\n  terms[0];\n      terms[1];\n      terms[2];\n      terms[3];\n      terms[4];\n      terms[5];\n      terms[6];\n      terms[7];\n      terms[8];\n      terms[9];\n      terms[10];\n      terms[11];\n      terms[12];\n      terms[13];\n      terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n\n  return new Matrix3().fromArray(sum);\n}\n\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\n\nexport { matrixSum3 as a, determinant2 as b, determinant4 as c, determinant3 as d, getMinor as g, matrix as m };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,YAAYA,CAAA,EAAG;EACtB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACxFF,KAAK,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC/B;EAEA,IAAIC,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC;IACZI,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC;IACZK,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;IACZM,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;EAChB,OAAOG,CAAC,GAAGG,CAAC,GAAGF,CAAC,GAAGC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,YAAYA,CAAA,EAAG;EACtB,KAAK,IAAIC,KAAK,GAAGV,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACO,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC9FT,KAAK,CAACS,KAAK,CAAC,GAAGX,SAAS,CAACW,KAAK,CAAC;EACjC;EAEA,IAAIN,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC;IACZI,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC;IACZK,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;IACZM,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IACZU,CAAC,GAAGV,KAAK,CAAC,CAAC,CAAC;IACZW,CAAC,GAAGX,KAAK,CAAC,CAAC,CAAC;IACZY,CAAC,GAAGZ,KAAK,CAAC,CAAC,CAAC;IACZa,CAAC,GAAGb,KAAK,CAAC,CAAC,CAAC;IACZc,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;EAChB,OAAOG,CAAC,GAAGO,CAAC,GAAGI,CAAC,GAAGV,CAAC,GAAGO,CAAC,GAAGC,CAAC,GAAGP,CAAC,GAAGC,CAAC,GAAGO,CAAC,GAAGR,CAAC,GAAGK,CAAC,GAAGE,CAAC,GAAGR,CAAC,GAAGE,CAAC,GAAGQ,CAAC,GAAGX,CAAC,GAAGQ,CAAC,GAAGE,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,YAAYA,CAAA,EAAG;EACtB,KAAK,IAAIC,KAAK,GAAGlB,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACe,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC9FjB,KAAK,CAACiB,KAAK,CAAC,GAAGnB,SAAS,CAACmB,KAAK,CAAC;EACjC;EAEAjB,KAAK,CAAC,CAAC,CAAC;EACJA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,CAAC,CAAC;EACRA,KAAK,CAAC,EAAE,CAAC;EACTA,KAAK,CAAC,EAAE,CAAC;EACTA,KAAK,CAAC,EAAE,CAAC;EACTA,KAAK,CAAC,EAAE,CAAC;EACTA,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASkB,QAAQA,CAACC,MAAM,EAAEC,CAAC,EAAEf,CAAC,EAAE;EAC9B,IAAIgB,gBAAgB,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;EAEjD,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,CAAC,GAAGJ,gBAAgB,CAACK,QAAQ,CAAC3B,MAAM;EACxC,IAAI4B,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACJ,CAAC,CAAC;EAEpB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,CAAC,EAAEX,CAAC,EAAE,EAAE;IAC1B,IAAIgB,OAAO,GAAGT,gBAAgB,CAACK,QAAQ,CAACZ,CAAC,CAAC;IAC1C,IAAIiB,GAAG,GAAGH,IAAI,CAACI,KAAK,CAAClB,CAAC,GAAGa,CAAC,CAAC;IAC3B,IAAIM,GAAG,GAAGnB,CAAC,GAAGa,CAAC;IAEf,IAAII,GAAG,KAAKX,CAAC,GAAG,CAAC,IAAIa,GAAG,KAAK5B,CAAC,GAAG,CAAC,EAAE;MAClCmB,CAAC,CAACU,IAAI,CAACJ,OAAO,CAAC;IACjB;EACF;EAEA,OAAOvB,YAAY,CAAC4B,KAAK,CAAC,KAAK,CAAC,EAAEX,CAAC,CAAC;AACtC;AACA;AACA;AACA;;AAEA,SAASY,UAAUA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC1B,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,OAAO,GAAGH,EAAE,CAACI,OAAO,CAAC,CAAC;EAC1B,IAAIC,OAAO,GAAGJ,EAAE,CAACG,OAAO,CAAC,CAAC;EAE1B,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,OAAO,CAACzC,MAAM,EAAEe,CAAC,EAAE,EAAE;IACvCyB,GAAG,CAACzB,CAAC,CAAC,GAAG0B,OAAO,CAAC1B,CAAC,CAAC,GAAG4B,OAAO,CAAC5B,CAAC,CAAC;EAClC;EAEA,OAAO,IAAInB,OAAO,CAAC,CAAC,CAACgD,SAAS,CAACJ,GAAG,CAAC;AACrC;AAEA,IAAIpB,MAAM,GAAG,aAAayB,MAAM,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE,IAAI;EACflD,YAAY,EAAEA,YAAY;EAC1BW,YAAY,EAAEA,YAAY;EAC1BQ,YAAY,EAAEA,YAAY;EAC1BG,QAAQ,EAAEA,QAAQ;EAClBkB,UAAU,EAAEA;AACd,CAAC,CAAC;AAEF,SAASA,UAAU,IAAIjC,CAAC,EAAEP,YAAY,IAAIQ,CAAC,EAAEW,YAAY,IAAIV,CAAC,EAAEE,YAAY,IAAID,CAAC,EAAEY,QAAQ,IAAIN,CAAC,EAAEO,MAAM,IAAI4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}