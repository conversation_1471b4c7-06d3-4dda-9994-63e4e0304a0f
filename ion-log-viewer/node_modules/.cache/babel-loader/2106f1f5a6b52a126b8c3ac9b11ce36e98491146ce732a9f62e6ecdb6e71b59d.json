{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Color, Vector2, Mesh, Line, Points, Matrix3, BufferAttribute } from \"three\";\nclass OBJExporter {\n  constructor() {\n    __publicField(this, \"output\");\n    __publicField(this, \"indexVertex\");\n    __publicField(this, \"indexVertexUvs\");\n    __publicField(this, \"indexNormals\");\n    __publicField(this, \"vertex\");\n    __publicField(this, \"color\");\n    __publicField(this, \"normal\");\n    __publicField(this, \"uv\");\n    __publicField(this, \"face\");\n    this.output = \"\";\n    this.indexVertex = 0;\n    this.indexVertexUvs = 0;\n    this.indexNormals = 0;\n    this.vertex = new Vector3();\n    this.color = new Color();\n    this.normal = new Vector3();\n    this.uv = new Vector2();\n    this.face = [];\n  }\n  parse(object) {\n    object.traverse(child => {\n      if (child instanceof Mesh && child.isMesh) {\n        this.parseMesh(child);\n      }\n      if (child instanceof Line && child.isLine) {\n        this.parseLine(child);\n      }\n      if (child instanceof Points && child.isPoints) {\n        this.parsePoints(child);\n      }\n    });\n    return this.output;\n  }\n  parseMesh(mesh) {\n    let nbVertex = 0;\n    let nbNormals = 0;\n    let nbVertexUvs = 0;\n    const geometry = mesh.geometry;\n    const normalMatrixWorld = new Matrix3();\n    if (!geometry.isBufferGeometry) {\n      throw new Error(\"THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.\");\n    }\n    const vertices = geometry.getAttribute(\"position\");\n    const normals = geometry.getAttribute(\"normal\");\n    const uvs = geometry.getAttribute(\"uv\");\n    const indices = geometry.getIndex();\n    this.output += `o ${mesh.name}\n`;\n    if (mesh.material && !Array.isArray(mesh.material) && mesh.material.name) {\n      this.output += `usemtl ${mesh.material.name}\n`;\n    }\n    if (vertices !== void 0) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i);\n        this.vertex.y = vertices.getY(i);\n        this.vertex.z = vertices.getZ(i);\n        this.vertex.applyMatrix4(mesh.matrixWorld);\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\n`;\n      }\n    }\n    if (uvs !== void 0) {\n      for (let i = 0, l = uvs.count; i < l; i++, nbVertexUvs++) {\n        this.uv.x = uvs.getX(i);\n        this.uv.y = uvs.getY(i);\n        this.output += `vt ${this.uv.x} ${this.uv.y}\n`;\n      }\n    }\n    if (normals !== void 0) {\n      normalMatrixWorld.getNormalMatrix(mesh.matrixWorld);\n      for (let i = 0, l = normals.count; i < l; i++, nbNormals++) {\n        this.normal.x = normals.getX(i);\n        this.normal.y = normals.getY(i);\n        this.normal.z = normals.getZ(i);\n        this.normal.applyMatrix3(normalMatrixWorld).normalize();\n        this.output += `vn ${this.normal.x} ${this.normal.y} ${this.normal.z}\n`;\n      }\n    }\n    if (indices !== null) {\n      for (let i = 0, l = indices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = indices.getX(i + m) + 1;\n          this.face[m] = this.indexVertex + j + (normals || uvs ? `/${uvs ? this.indexVertexUvs + j : \"\"}${normals ? `/${this.indexNormals + j}` : \"\"}` : \"\");\n        }\n        this.output += `f ${this.face.join(\" \")}\n`;\n      }\n    } else {\n      for (let i = 0, l = vertices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = i + m + 1;\n          this.face[m] = this.indexVertex + j + (normals || uvs ? `/${uvs ? this.indexVertexUvs + j : \"\"}${normals ? `/${this.indexNormals + j}` : \"\"}` : \"\");\n        }\n        this.output += `f ${this.face.join(\" \")}\n`;\n      }\n    }\n    this.indexVertex += nbVertex;\n    this.indexVertexUvs += nbVertexUvs;\n    this.indexNormals += nbNormals;\n  }\n  parseLine(line) {\n    let nbVertex = 0;\n    const geometry = line.geometry;\n    const type = line.type;\n    if (geometry.isBufferGeometry) {\n      throw new Error(\"THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.\");\n    }\n    const vertices = geometry.getAttribute(\"position\");\n    this.output += `o ${line.name}\n`;\n    if (vertices !== void 0) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i);\n        this.vertex.y = vertices.getY(i);\n        this.vertex.z = vertices.getZ(i);\n        this.vertex.applyMatrix4(line.matrixWorld);\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\n`;\n      }\n    }\n    if (type === \"Line\") {\n      this.output += \"l \";\n      for (let j = 1, l = vertices.count; j <= l; j++) {\n        this.output += `${this.indexVertex + j} `;\n      }\n      this.output += \"\\n\";\n    }\n    if (type === \"LineSegments\") {\n      for (let j = 1, k = j + 1, l = vertices.count; j < l; j += 2, k = j + 1) {\n        this.output += `l ${this.indexVertex + j} ${this.indexVertex + k}\n`;\n      }\n    }\n    this.indexVertex += nbVertex;\n  }\n  parsePoints(points) {\n    let nbVertex = 0;\n    const geometry = points.geometry;\n    if (!geometry.isBufferGeometry) {\n      throw new Error(\"THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.\");\n    }\n    const vertices = geometry.getAttribute(\"position\");\n    const colors = geometry.getAttribute(\"color\");\n    this.output += `o ${points.name}\n`;\n    if (vertices !== void 0) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.fromBufferAttribute(vertices, i);\n        this.vertex.applyMatrix4(points.matrixWorld);\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}`;\n        if (colors !== void 0 && colors instanceof BufferAttribute) {\n          this.color.fromBufferAttribute(colors, i);\n          this.output += ` ${this.color.r} ${this.color.g} ${this.color.b}`;\n        }\n        this.output += \"\\n\";\n      }\n    }\n    this.output += \"p \";\n    for (let j = 1, l = vertices.count; j <= l; j++) {\n      this.output += `${this.indexVertex + j} `;\n    }\n    this.output += \"\\n\";\n    this.indexVertex += nbVertex;\n  }\n}\nexport { OBJExporter };", "map": {"version": 3, "names": ["OBJExporter", "constructor", "__publicField", "output", "indexVertex", "indexVertexUvs", "indexNormals", "vertex", "Vector3", "color", "Color", "normal", "uv", "Vector2", "face", "parse", "object", "traverse", "child", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Line", "isLine", "parseLine", "Points", "isPoints", "parsePoints", "mesh", "nbVertex", "nbNormals", "nbVertexUvs", "geometry", "normalMatrixWorld", "Matrix3", "isBufferGeometry", "Error", "vertices", "getAttribute", "normals", "uvs", "indices", "getIndex", "name", "material", "Array", "isArray", "i", "l", "count", "x", "getX", "y", "getY", "z", "getZ", "applyMatrix4", "matrixWorld", "getNormalMatrix", "applyMatrix3", "normalize", "m", "j", "join", "line", "type", "k", "points", "colors", "fromBufferAttribute", "BufferAttribute", "r", "g", "b"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/exporters/OBJExporter.ts"], "sourcesContent": ["import { BufferAttribute, Color, Line, Matrix3, Mesh, Object3D, Points, Vector2, Vector3 } from 'three'\n\nclass OBJExporter {\n  private output\n\n  private indexVertex\n  private indexVertexUvs\n  private indexNormals\n\n  private vertex\n  private color\n  private normal\n  private uv\n\n  private face: string[]\n\n  constructor() {\n    this.output = ''\n\n    this.indexVertex = 0\n    this.indexVertexUvs = 0\n    this.indexNormals = 0\n\n    this.vertex = new Vector3()\n    this.color = new Color()\n    this.normal = new Vector3()\n    this.uv = new Vector2()\n\n    this.face = []\n  }\n\n  public parse(object: Object3D): string {\n    object.traverse((child) => {\n      if (child instanceof Mesh && child.isMesh) {\n        this.parseMesh(child)\n      }\n\n      if (child instanceof Line && child.isLine) {\n        this.parseLine(child)\n      }\n\n      if (child instanceof Points && child.isPoints) {\n        this.parsePoints(child)\n      }\n    })\n\n    return this.output\n  }\n\n  private parseMesh(mesh: Mesh): void {\n    let nbVertex = 0\n    let nbNormals = 0\n    let nbVertexUvs = 0\n\n    const geometry = mesh.geometry\n\n    const normalMatrixWorld = new Matrix3()\n\n    if (!geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    // shortcuts\n    const vertices = geometry.getAttribute('position')\n    const normals = geometry.getAttribute('normal')\n    const uvs = geometry.getAttribute('uv')\n    const indices = geometry.getIndex()\n\n    // name of the mesh object\n    this.output += `o ${mesh.name}\\n`\n\n    // name of the mesh material\n    if (mesh.material && !Array.isArray(mesh.material) && mesh.material.name) {\n      this.output += `usemtl ${mesh.material.name}\\n`\n    }\n\n    // vertices\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i)\n        this.vertex.y = vertices.getY(i)\n        this.vertex.z = vertices.getZ(i)\n\n        // transform the vertex to world space\n        this.vertex.applyMatrix4(mesh.matrixWorld)\n\n        // transform the vertex to export format\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\\n`\n      }\n    }\n\n    // uvs\n\n    if (uvs !== undefined) {\n      for (let i = 0, l = uvs.count; i < l; i++, nbVertexUvs++) {\n        this.uv.x = uvs.getX(i)\n        this.uv.y = uvs.getY(i)\n\n        // transform the uv to export format\n        this.output += `vt ${this.uv.x} ${this.uv.y}\\n`\n      }\n    }\n\n    // normals\n\n    if (normals !== undefined) {\n      normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n      for (let i = 0, l = normals.count; i < l; i++, nbNormals++) {\n        this.normal.x = normals.getX(i)\n        this.normal.y = normals.getY(i)\n        this.normal.z = normals.getZ(i)\n\n        // transform the normal to world space\n        this.normal.applyMatrix3(normalMatrixWorld).normalize()\n\n        // transform the normal to export format\n        this.output += `vn ${this.normal.x} ${this.normal.y} ${this.normal.z}\\n`\n      }\n    }\n\n    // faces\n\n    if (indices !== null) {\n      for (let i = 0, l = indices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = indices.getX(i + m) + 1\n\n          this.face[m] =\n            this.indexVertex +\n            j +\n            (normals || uvs\n              ? `/${uvs ? this.indexVertexUvs + j : ''}${normals ? `/${this.indexNormals + j}` : ''}`\n              : '')\n        }\n\n        // transform the face to export format\n        this.output += `f ${this.face.join(' ')}\\n`\n      }\n    } else {\n      for (let i = 0, l = vertices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = i + m + 1\n\n          this.face[m] =\n            this.indexVertex +\n            j +\n            (normals || uvs\n              ? `/${uvs ? this.indexVertexUvs + j : ''}${normals ? `/${this.indexNormals + j}` : ''}`\n              : '')\n        }\n\n        // transform the face to export format\n        this.output += `f ${this.face.join(' ')}\\n`\n      }\n    }\n\n    // update index\n    this.indexVertex += nbVertex\n    this.indexVertexUvs += nbVertexUvs\n    this.indexNormals += nbNormals\n  }\n\n  private parseLine(line: Line): void {\n    let nbVertex = 0\n\n    const geometry = line.geometry\n    const type = line.type\n\n    if (geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    // shortcuts\n    const vertices = geometry.getAttribute('position')\n\n    // name of the line object\n    this.output += `o ${line.name}\\n`\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i)\n        this.vertex.y = vertices.getY(i)\n        this.vertex.z = vertices.getZ(i)\n\n        // transform the vertex to world space\n        this.vertex.applyMatrix4(line.matrixWorld)\n\n        // transform the vertex to export format\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\\n`\n      }\n    }\n\n    if (type === 'Line') {\n      this.output += 'l '\n\n      for (let j = 1, l = vertices.count; j <= l; j++) {\n        this.output += `${this.indexVertex + j} `\n      }\n\n      this.output += '\\n'\n    }\n\n    if (type === 'LineSegments') {\n      for (let j = 1, k = j + 1, l = vertices.count; j < l; j += 2, k = j + 1) {\n        this.output += `l ${this.indexVertex + j} ${this.indexVertex + k}\\n`\n      }\n    }\n\n    // update index\n    this.indexVertex += nbVertex\n  }\n\n  private parsePoints(points: Points): void {\n    let nbVertex = 0\n\n    const geometry = points.geometry\n\n    if (!geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    const vertices = geometry.getAttribute('position')\n    const colors = geometry.getAttribute('color')\n\n    this.output += `o ${points.name}\\n`\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.fromBufferAttribute(vertices, i)\n        this.vertex.applyMatrix4(points.matrixWorld)\n\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}`\n\n        if (colors !== undefined && colors instanceof BufferAttribute) {\n          this.color.fromBufferAttribute(colors, i)\n\n          this.output += ` ${this.color.r} ${this.color.g} ${this.color.b}`\n        }\n\n        this.output += '\\n'\n      }\n    }\n\n    this.output += 'p '\n\n    for (let j = 1, l = vertices.count; j <= l; j++) {\n      this.output += `${this.indexVertex + j} `\n    }\n\n    this.output += '\\n'\n\n    // update index\n    this.indexVertex += nbVertex\n  }\n}\n\nexport { OBJExporter }\n"], "mappings": ";;;;;;;;;;;;AAEA,MAAMA,WAAA,CAAY;EAchBC,YAAA,EAAc;IAbNC,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAGN,KAAKC,MAAA,GAAS;IAEd,KAAKC,WAAA,GAAc;IACnB,KAAKC,cAAA,GAAiB;IACtB,KAAKC,YAAA,GAAe;IAEf,KAAAC,MAAA,GAAS,IAAIC,OAAA;IACb,KAAAC,KAAA,GAAQ,IAAIC,KAAA;IACZ,KAAAC,MAAA,GAAS,IAAIH,OAAA;IACb,KAAAI,EAAA,GAAK,IAAIC,OAAA;IAEd,KAAKC,IAAA,GAAO;EACd;EAEOC,MAAMC,MAAA,EAA0B;IAC9BA,MAAA,CAAAC,QAAA,CAAUC,KAAA,IAAU;MACrB,IAAAA,KAAA,YAAiBC,IAAA,IAAQD,KAAA,CAAME,MAAA,EAAQ;QACzC,KAAKC,SAAA,CAAUH,KAAK;MACtB;MAEI,IAAAA,KAAA,YAAiBI,IAAA,IAAQJ,KAAA,CAAMK,MAAA,EAAQ;QACzC,KAAKC,SAAA,CAAUN,KAAK;MACtB;MAEI,IAAAA,KAAA,YAAiBO,MAAA,IAAUP,KAAA,CAAMQ,QAAA,EAAU;QAC7C,KAAKC,WAAA,CAAYT,KAAK;MACxB;IAAA,CACD;IAED,OAAO,KAAKf,MAAA;EACd;EAEQkB,UAAUO,IAAA,EAAkB;IAClC,IAAIC,QAAA,GAAW;IACf,IAAIC,SAAA,GAAY;IAChB,IAAIC,WAAA,GAAc;IAElB,MAAMC,QAAA,GAAWJ,IAAA,CAAKI,QAAA;IAEhB,MAAAC,iBAAA,GAAoB,IAAIC,OAAA;IAE1B,KAACF,QAAA,CAASG,gBAAA,EAAkB;MACxB,UAAIC,KAAA,CAAM,kEAAkE;IACpF;IAGM,MAAAC,QAAA,GAAWL,QAAA,CAASM,YAAA,CAAa,UAAU;IAC3C,MAAAC,OAAA,GAAUP,QAAA,CAASM,YAAA,CAAa,QAAQ;IACxC,MAAAE,GAAA,GAAMR,QAAA,CAASM,YAAA,CAAa,IAAI;IAChC,MAAAG,OAAA,GAAUT,QAAA,CAASU,QAAA;IAGpB,KAAAvC,MAAA,IAAU,KAAKyB,IAAA,CAAKe,IAAA;AAAA;IAGrB,IAAAf,IAAA,CAAKgB,QAAA,IAAY,CAACC,KAAA,CAAMC,OAAA,CAAQlB,IAAA,CAAKgB,QAAQ,KAAKhB,IAAA,CAAKgB,QAAA,CAASD,IAAA,EAAM;MACnE,KAAAxC,MAAA,IAAU,UAAUyB,IAAA,CAAKgB,QAAA,CAASD,IAAA;AAAA;IACzC;IAIA,IAAIN,QAAA,KAAa,QAAW;MACjB,SAAAU,CAAA,GAAI,GAAGC,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKlB,QAAA,IAAY;QAC1D,KAAKtB,MAAA,CAAO2C,CAAA,GAAIb,QAAA,CAASc,IAAA,CAAKJ,CAAC;QAC/B,KAAKxC,MAAA,CAAO6C,CAAA,GAAIf,QAAA,CAASgB,IAAA,CAAKN,CAAC;QAC/B,KAAKxC,MAAA,CAAO+C,CAAA,GAAIjB,QAAA,CAASkB,IAAA,CAAKR,CAAC;QAG1B,KAAAxC,MAAA,CAAOiD,YAAA,CAAa5B,IAAA,CAAK6B,WAAW;QAGpC,KAAAtD,MAAA,IAAU,KAAK,KAAKI,MAAA,CAAO2C,CAAA,IAAK,KAAK3C,MAAA,CAAO6C,CAAA,IAAK,KAAK7C,MAAA,CAAO+C,CAAA;AAAA;MACpE;IACF;IAIA,IAAId,GAAA,KAAQ,QAAW;MACZ,SAAAO,CAAA,GAAI,GAAGC,CAAA,GAAIR,GAAA,CAAIS,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKhB,WAAA,IAAe;QACxD,KAAKnB,EAAA,CAAGsC,CAAA,GAAIV,GAAA,CAAIW,IAAA,CAAKJ,CAAC;QACtB,KAAKnC,EAAA,CAAGwC,CAAA,GAAIZ,GAAA,CAAIa,IAAA,CAAKN,CAAC;QAGtB,KAAK5C,MAAA,IAAU,MAAM,KAAKS,EAAA,CAAGsC,CAAA,IAAK,KAAKtC,EAAA,CAAGwC,CAAA;AAAA;MAC5C;IACF;IAIA,IAAIb,OAAA,KAAY,QAAW;MACPN,iBAAA,CAAAyB,eAAA,CAAgB9B,IAAA,CAAK6B,WAAW;MAEzC,SAAAV,CAAA,GAAI,GAAGC,CAAA,GAAIT,OAAA,CAAQU,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKjB,SAAA,IAAa;QAC1D,KAAKnB,MAAA,CAAOuC,CAAA,GAAIX,OAAA,CAAQY,IAAA,CAAKJ,CAAC;QAC9B,KAAKpC,MAAA,CAAOyC,CAAA,GAAIb,OAAA,CAAQc,IAAA,CAAKN,CAAC;QAC9B,KAAKpC,MAAA,CAAO2C,CAAA,GAAIf,OAAA,CAAQgB,IAAA,CAAKR,CAAC;QAG9B,KAAKpC,MAAA,CAAOgD,YAAA,CAAa1B,iBAAiB,EAAE2B,SAAA,CAAU;QAGjD,KAAAzD,MAAA,IAAU,MAAM,KAAKQ,MAAA,CAAOuC,CAAA,IAAK,KAAKvC,MAAA,CAAOyC,CAAA,IAAK,KAAKzC,MAAA,CAAO2C,CAAA;AAAA;MACrE;IACF;IAIA,IAAIb,OAAA,KAAY,MAAM;MACX,SAAAM,CAAA,GAAI,GAAGC,CAAA,GAAIP,OAAA,CAAQQ,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;QAChD,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,MAAMC,CAAA,GAAIrB,OAAA,CAAQU,IAAA,CAAKJ,CAAA,GAAIc,CAAC,IAAI;UAEhC,KAAK/C,IAAA,CAAK+C,CAAC,IACT,KAAKzD,WAAA,GACL0D,CAAA,IACCvB,OAAA,IAAWC,GAAA,GACR,IAAIA,GAAA,GAAM,KAAKnC,cAAA,GAAiByD,CAAA,GAAI,KAAKvB,OAAA,GAAU,IAAI,KAAKjC,YAAA,GAAewD,CAAA,KAAM,OACjF;QACR;QAGA,KAAK3D,MAAA,IAAU,KAAK,KAAKW,IAAA,CAAKiD,IAAA,CAAK,GAAG;AAAA;MACxC;IAAA,OACK;MACI,SAAAhB,CAAA,GAAI,GAAGC,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;QACjD,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UACpB,MAAAC,CAAA,GAAIf,CAAA,GAAIc,CAAA,GAAI;UAElB,KAAK/C,IAAA,CAAK+C,CAAC,IACT,KAAKzD,WAAA,GACL0D,CAAA,IACCvB,OAAA,IAAWC,GAAA,GACR,IAAIA,GAAA,GAAM,KAAKnC,cAAA,GAAiByD,CAAA,GAAI,KAAKvB,OAAA,GAAU,IAAI,KAAKjC,YAAA,GAAewD,CAAA,KAAM,OACjF;QACR;QAGA,KAAK3D,MAAA,IAAU,KAAK,KAAKW,IAAA,CAAKiD,IAAA,CAAK,GAAG;AAAA;MACxC;IACF;IAGA,KAAK3D,WAAA,IAAeyB,QAAA;IACpB,KAAKxB,cAAA,IAAkB0B,WAAA;IACvB,KAAKzB,YAAA,IAAgBwB,SAAA;EACvB;EAEQN,UAAUwC,IAAA,EAAkB;IAClC,IAAInC,QAAA,GAAW;IAEf,MAAMG,QAAA,GAAWgC,IAAA,CAAKhC,QAAA;IACtB,MAAMiC,IAAA,GAAOD,IAAA,CAAKC,IAAA;IAElB,IAAIjC,QAAA,CAASG,gBAAA,EAAkB;MACvB,UAAIC,KAAA,CAAM,kEAAkE;IACpF;IAGM,MAAAC,QAAA,GAAWL,QAAA,CAASM,YAAA,CAAa,UAAU;IAG5C,KAAAnC,MAAA,IAAU,KAAK6D,IAAA,CAAKrB,IAAA;AAAA;IAEzB,IAAIN,QAAA,KAAa,QAAW;MACjB,SAAAU,CAAA,GAAI,GAAGC,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKlB,QAAA,IAAY;QAC1D,KAAKtB,MAAA,CAAO2C,CAAA,GAAIb,QAAA,CAASc,IAAA,CAAKJ,CAAC;QAC/B,KAAKxC,MAAA,CAAO6C,CAAA,GAAIf,QAAA,CAASgB,IAAA,CAAKN,CAAC;QAC/B,KAAKxC,MAAA,CAAO+C,CAAA,GAAIjB,QAAA,CAASkB,IAAA,CAAKR,CAAC;QAG1B,KAAAxC,MAAA,CAAOiD,YAAA,CAAaQ,IAAA,CAAKP,WAAW;QAGpC,KAAAtD,MAAA,IAAU,KAAK,KAAKI,MAAA,CAAO2C,CAAA,IAAK,KAAK3C,MAAA,CAAO6C,CAAA,IAAK,KAAK7C,MAAA,CAAO+C,CAAA;AAAA;MACpE;IACF;IAEA,IAAIW,IAAA,KAAS,QAAQ;MACnB,KAAK9D,MAAA,IAAU;MAEf,SAAS2D,CAAA,GAAI,GAAGd,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOa,CAAA,IAAKd,CAAA,EAAGc,CAAA,IAAK;QAC1C,KAAA3D,MAAA,IAAU,GAAG,KAAKC,WAAA,GAAc0D,CAAA;MACvC;MAEA,KAAK3D,MAAA,IAAU;IACjB;IAEA,IAAI8D,IAAA,KAAS,gBAAgB;MAC3B,SAASH,CAAA,GAAI,GAAGI,CAAA,GAAIJ,CAAA,GAAI,GAAGd,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOa,CAAA,GAAId,CAAA,EAAGc,CAAA,IAAK,GAAGI,CAAA,GAAIJ,CAAA,GAAI,GAAG;QACvE,KAAK3D,MAAA,IAAU,KAAK,KAAKC,WAAA,GAAc0D,CAAA,IAAK,KAAK1D,WAAA,GAAc8D,CAAA;AAAA;MACjE;IACF;IAGA,KAAK9D,WAAA,IAAeyB,QAAA;EACtB;EAEQF,YAAYwC,MAAA,EAAsB;IACxC,IAAItC,QAAA,GAAW;IAEf,MAAMG,QAAA,GAAWmC,MAAA,CAAOnC,QAAA;IAEpB,KAACA,QAAA,CAASG,gBAAA,EAAkB;MACxB,UAAIC,KAAA,CAAM,kEAAkE;IACpF;IAEM,MAAAC,QAAA,GAAWL,QAAA,CAASM,YAAA,CAAa,UAAU;IAC3C,MAAA8B,MAAA,GAASpC,QAAA,CAASM,YAAA,CAAa,OAAO;IAEvC,KAAAnC,MAAA,IAAU,KAAKgE,MAAA,CAAOxB,IAAA;AAAA;IAE3B,IAAIN,QAAA,KAAa,QAAW;MACjB,SAAAU,CAAA,GAAI,GAAGC,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAKlB,QAAA,IAAY;QACrD,KAAAtB,MAAA,CAAO8D,mBAAA,CAAoBhC,QAAA,EAAUU,CAAC;QACtC,KAAAxC,MAAA,CAAOiD,YAAA,CAAaW,MAAA,CAAOV,WAAW;QAEtC,KAAAtD,MAAA,IAAU,KAAK,KAAKI,MAAA,CAAO2C,CAAA,IAAK,KAAK3C,MAAA,CAAO6C,CAAA,IAAK,KAAK7C,MAAA,CAAO+C,CAAA;QAE9D,IAAAc,MAAA,KAAW,UAAaA,MAAA,YAAkBE,eAAA,EAAiB;UACxD,KAAA7D,KAAA,CAAM4D,mBAAA,CAAoBD,MAAA,EAAQrB,CAAC;UAEnC,KAAA5C,MAAA,IAAU,IAAI,KAAKM,KAAA,CAAM8D,CAAA,IAAK,KAAK9D,KAAA,CAAM+D,CAAA,IAAK,KAAK/D,KAAA,CAAMgE,CAAA;QAChE;QAEA,KAAKtE,MAAA,IAAU;MACjB;IACF;IAEA,KAAKA,MAAA,IAAU;IAEf,SAAS2D,CAAA,GAAI,GAAGd,CAAA,GAAIX,QAAA,CAASY,KAAA,EAAOa,CAAA,IAAKd,CAAA,EAAGc,CAAA,IAAK;MAC1C,KAAA3D,MAAA,IAAU,GAAG,KAAKC,WAAA,GAAc0D,CAAA;IACvC;IAEA,KAAK3D,MAAA,IAAU;IAGf,KAAKC,WAAA,IAAeyB,QAAA;EACtB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}