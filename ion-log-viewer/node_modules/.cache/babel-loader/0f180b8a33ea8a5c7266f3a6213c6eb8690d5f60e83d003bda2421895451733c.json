{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { Lob } from \"./Lob\";\nexport class <PERSON>lob extends Lob(IonTypes.CLOB) {\n  constructor(bytes, annotations = []) {\n    super(bytes, annotations);\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeClob(this);\n  }\n  toJSON() {\n    let encodedText = \"\";\n    for (const byte of this) {\n      if (byte >= 32 && byte <= 126) {\n        encodedText += String.fromCharCode(byte);\n        continue;\n      }\n      const hex = byte.toString(16);\n      if (hex.length == 1) {\n        encodedText += \"\\\\u000\" + hex;\n      } else {\n        encodedText += \"\\\\u00\" + hex;\n      }\n    }\n    return encodedText;\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "<PERSON><PERSON>", "Clob", "CLOB", "constructor", "bytes", "annotations", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeClob", "toJSON", "encodedText", "byte", "String", "fromCharCode", "hex", "toString", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Clob.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { Lob } from \"./Lob\";\nexport class <PERSON>lob extends Lob(IonTypes.CLOB) {\n    constructor(bytes, annotations = []) {\n        super(bytes, annotations);\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeClob(this);\n    }\n    toJSON() {\n        let encodedText = \"\";\n        for (const byte of this) {\n            if (byte >= 32 && byte <= 126) {\n                encodedText += String.fromCharCode(byte);\n                continue;\n            }\n            const hex = byte.toString(16);\n            if (hex.length == 1) {\n                encodedText += \"\\\\u000\" + hex;\n            }\n            else {\n                encodedText += \"\\\\u00\" + hex;\n            }\n        }\n        return encodedText;\n    }\n}\n//# sourceMappingURL=Clob.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,GAAG,QAAQ,OAAO;AAC3B,OAAO,MAAMC,IAAI,SAASD,GAAG,CAACD,QAAQ,CAACG,IAAI,CAAC,CAAC;EACzCC,WAAWA,CAACC,KAAK,EAAEC,WAAW,GAAG,EAAE,EAAE;IACjC,KAAK,CAACD,KAAK,EAAEC,WAAW,CAAC;EAC7B;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,SAAS,CAAC,IAAI,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAIC,WAAW,GAAG,EAAE;IACpB,KAAK,MAAMC,IAAI,IAAI,IAAI,EAAE;MACrB,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,GAAG,EAAE;QAC3BD,WAAW,IAAIE,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC;QACxC;MACJ;MACA,MAAMG,GAAG,GAAGH,IAAI,CAACI,QAAQ,CAAC,EAAE,CAAC;MAC7B,IAAID,GAAG,CAACE,MAAM,IAAI,CAAC,EAAE;QACjBN,WAAW,IAAI,QAAQ,GAAGI,GAAG;MACjC,CAAC,MACI;QACDJ,WAAW,IAAI,OAAO,GAAGI,GAAG;MAChC;IACJ;IACA,OAAOJ,WAAW;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}