{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport const NIBBLE_MASK = 0xf;\nexport const BYTE_MASK = 0xff;\nexport const TYPE_SHIFT = 4;\nexport const BYTE_SHIFT = 8;\nexport const LEN_MASK = 0xf;\nexport const LEN_VAR = 14;\nexport const LEN_NULL = 15;\nexport const TB_NULL = 0;\nexport const TB_BOOL = 1;\nexport const TB_INT = 2;\nexport const TB_NEG_INT = 3;\nexport const TB_FLOAT = 4;\nexport const TB_DECIMAL = 5;\nexport const TB_TIMESTAMP = 6;\nexport const TB_SYMBOL = 7;\nexport const TB_STRING = 8;\nexport const TB_CLOB = 9;\nexport const TB_BLOB = 10;\nexport const TB_LIST = 11;\nexport const TB_SEXP = 12;\nexport const TB_STRUCT = 13;\nexport const TB_ANNOTATION = 14;", "map": {"version": 3, "names": ["NIBBLE_MASK", "BYTE_MASK", "TYPE_SHIFT", "BYTE_SHIFT", "LEN_MASK", "LEN_VAR", "LEN_NULL", "TB_NULL", "TB_BOOL", "TB_INT", "TB_NEG_INT", "TB_FLOAT", "TB_DECIMAL", "TB_TIMESTAMP", "TB_SYMBOL", "TB_STRING", "TB_CLOB", "TB_BLOB", "TB_LIST", "TB_SEXP", "TB_STRUCT", "TB_ANNOTATION"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonBinary.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport const NIBBLE_MASK = 0xf;\nexport const BYTE_MASK = 0xff;\nexport const TYPE_SHIFT = 4;\nexport const BYTE_SHIFT = 8;\nexport const LEN_MASK = 0xf;\nexport const LEN_VAR = 14;\nexport const LEN_NULL = 15;\nexport const TB_NULL = 0;\nexport const TB_BOOL = 1;\nexport const TB_INT = 2;\nexport const TB_NEG_INT = 3;\nexport const TB_FLOAT = 4;\nexport const TB_DECIMAL = 5;\nexport const TB_TIMESTAMP = 6;\nexport const TB_SYMBOL = 7;\nexport const TB_STRING = 8;\nexport const TB_CLOB = 9;\nexport const TB_BLOB = 10;\nexport const TB_LIST = 11;\nexport const TB_SEXP = 12;\nexport const TB_STRUCT = 13;\nexport const TB_ANNOTATION = 14;\n//# sourceMappingURL=IonBinary.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,GAAG,GAAG;AAC9B,OAAO,MAAMC,SAAS,GAAG,IAAI;AAC7B,OAAO,MAAMC,UAAU,GAAG,CAAC;AAC3B,OAAO,MAAMC,UAAU,GAAG,CAAC;AAC3B,OAAO,MAAMC,QAAQ,GAAG,GAAG;AAC3B,OAAO,MAAMC,OAAO,GAAG,EAAE;AACzB,OAAO,MAAMC,QAAQ,GAAG,EAAE;AAC1B,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB,OAAO,MAAMC,MAAM,GAAG,CAAC;AACvB,OAAO,MAAMC,UAAU,GAAG,CAAC;AAC3B,OAAO,MAAMC,QAAQ,GAAG,CAAC;AACzB,OAAO,MAAMC,UAAU,GAAG,CAAC;AAC3B,OAAO,MAAMC,YAAY,GAAG,CAAC;AAC7B,OAAO,MAAMC,SAAS,GAAG,CAAC;AAC1B,OAAO,MAAMC,SAAS,GAAG,CAAC;AAC1B,OAAO,MAAMC,OAAO,GAAG,CAAC;AACxB,OAAO,MAAMC,OAAO,GAAG,EAAE;AACzB,OAAO,MAAMC,OAAO,GAAG,EAAE;AACzB,OAAO,MAAMC,OAAO,GAAG,EAAE;AACzB,OAAO,MAAMC,SAAS,GAAG,EAAE;AAC3B,OAAO,MAAMC,aAAa,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}