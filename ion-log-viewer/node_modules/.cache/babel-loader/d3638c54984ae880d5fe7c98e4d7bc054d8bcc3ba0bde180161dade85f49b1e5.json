{"ast": null, "code": "import { DataTextureLoader, HalfFloatType, FloatType, DataUtils, LinearFilter } from \"three\";\nclass RGBELoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n    this.type = HalfFloatType;\n  }\n  // adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n  parse(buffer) {\n    const rgbe_read_error = 1,\n      rgbe_write_error = 2,\n      rgbe_format_error = 3,\n      rgbe_memory_error = 4,\n      rgbe_error = function (rgbe_error_code, msg) {\n        switch (rgbe_error_code) {\n          case rgbe_read_error:\n            throw new Error(\"THREE.RGBELoader: Read Error: \" + (msg || \"\"));\n          case rgbe_write_error:\n            throw new Error(\"THREE.RGBELoader: Write Error: \" + (msg || \"\"));\n          case rgbe_format_error:\n            throw new Error(\"THREE.RGBELoader: Bad File Format: \" + (msg || \"\"));\n          default:\n          case rgbe_memory_error:\n            throw new Error(\"THREE.RGBELoader: Memory Error: \" + (msg || \"\"));\n        }\n      },\n      RGBE_VALID_PROGRAMTYPE = 1,\n      RGBE_VALID_FORMAT = 2,\n      RGBE_VALID_DIMENSIONS = 4,\n      NEWLINE = \"\\n\",\n      fgets = function (buffer2, lineLimit, consume) {\n        const chunkSize = 128;\n        lineLimit = !lineLimit ? 1024 : lineLimit;\n        let p = buffer2.pos,\n          i = -1,\n          len = 0,\n          s = \"\",\n          chunk = String.fromCharCode.apply(null, new Uint16Array(buffer2.subarray(p, p + chunkSize)));\n        while (0 > (i = chunk.indexOf(NEWLINE)) && len < lineLimit && p < buffer2.byteLength) {\n          s += chunk;\n          len += chunk.length;\n          p += chunkSize;\n          chunk += String.fromCharCode.apply(null, new Uint16Array(buffer2.subarray(p, p + chunkSize)));\n        }\n        if (-1 < i) {\n          if (false !== consume) buffer2.pos += len + i + 1;\n          return s + chunk.slice(0, i);\n        }\n        return false;\n      },\n      RGBE_ReadHeader = function (buffer2) {\n        const magic_token_re = /^#\\?(\\S+)/,\n          gamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          exposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          format_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n          dimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n          header = {\n            valid: 0,\n            string: \"\",\n            comments: \"\",\n            programtype: \"RGBE\",\n            format: \"\",\n            gamma: 1,\n            exposure: 1,\n            width: 0,\n            height: 0\n          };\n        let line, match;\n        if (buffer2.pos >= buffer2.byteLength || !(line = fgets(buffer2))) {\n          rgbe_error(rgbe_read_error, \"no header found\");\n        }\n        if (!(match = line.match(magic_token_re))) {\n          rgbe_error(rgbe_format_error, \"bad initial token\");\n        }\n        header.valid |= RGBE_VALID_PROGRAMTYPE;\n        header.programtype = match[1];\n        header.string += line + \"\\n\";\n        while (true) {\n          line = fgets(buffer2);\n          if (false === line) break;\n          header.string += line + \"\\n\";\n          if (\"#\" === line.charAt(0)) {\n            header.comments += line + \"\\n\";\n            continue;\n          }\n          if (match = line.match(gamma_re)) {\n            header.gamma = parseFloat(match[1]);\n          }\n          if (match = line.match(exposure_re)) {\n            header.exposure = parseFloat(match[1]);\n          }\n          if (match = line.match(format_re)) {\n            header.valid |= RGBE_VALID_FORMAT;\n            header.format = match[1];\n          }\n          if (match = line.match(dimensions_re)) {\n            header.valid |= RGBE_VALID_DIMENSIONS;\n            header.height = parseInt(match[1], 10);\n            header.width = parseInt(match[2], 10);\n          }\n          if (header.valid & RGBE_VALID_FORMAT && header.valid & RGBE_VALID_DIMENSIONS) break;\n        }\n        if (!(header.valid & RGBE_VALID_FORMAT)) {\n          rgbe_error(rgbe_format_error, \"missing format specifier\");\n        }\n        if (!(header.valid & RGBE_VALID_DIMENSIONS)) {\n          rgbe_error(rgbe_format_error, \"missing image size specifier\");\n        }\n        return header;\n      },\n      RGBE_ReadPixels_RLE = function (buffer2, w2, h2) {\n        const scanline_width = w2;\n        if (\n        // run length encoding is not allowed so read flat\n        scanline_width < 8 || scanline_width > 32767 ||\n        // this file is not run length encoded\n        2 !== buffer2[0] || 2 !== buffer2[1] || buffer2[2] & 128) {\n          return new Uint8Array(buffer2);\n        }\n        if (scanline_width !== (buffer2[2] << 8 | buffer2[3])) {\n          rgbe_error(rgbe_format_error, \"wrong scanline width\");\n        }\n        const data_rgba = new Uint8Array(4 * w2 * h2);\n        if (!data_rgba.length) {\n          rgbe_error(rgbe_memory_error, \"unable to allocate buffer space\");\n        }\n        let offset = 0,\n          pos = 0;\n        const ptr_end = 4 * scanline_width;\n        const rgbeStart = new Uint8Array(4);\n        const scanline_buffer = new Uint8Array(ptr_end);\n        let num_scanlines = h2;\n        while (num_scanlines > 0 && pos < buffer2.byteLength) {\n          if (pos + 4 > buffer2.byteLength) {\n            rgbe_error(rgbe_read_error);\n          }\n          rgbeStart[0] = buffer2[pos++];\n          rgbeStart[1] = buffer2[pos++];\n          rgbeStart[2] = buffer2[pos++];\n          rgbeStart[3] = buffer2[pos++];\n          if (2 != rgbeStart[0] || 2 != rgbeStart[1] || (rgbeStart[2] << 8 | rgbeStart[3]) != scanline_width) {\n            rgbe_error(rgbe_format_error, \"bad rgbe scanline format\");\n          }\n          let ptr = 0,\n            count;\n          while (ptr < ptr_end && pos < buffer2.byteLength) {\n            count = buffer2[pos++];\n            const isEncodedRun = count > 128;\n            if (isEncodedRun) count -= 128;\n            if (0 === count || ptr + count > ptr_end) {\n              rgbe_error(rgbe_format_error, \"bad scanline data\");\n            }\n            if (isEncodedRun) {\n              const byteValue = buffer2[pos++];\n              for (let i = 0; i < count; i++) {\n                scanline_buffer[ptr++] = byteValue;\n              }\n            } else {\n              scanline_buffer.set(buffer2.subarray(pos, pos + count), ptr);\n              ptr += count;\n              pos += count;\n            }\n          }\n          const l = scanline_width;\n          for (let i = 0; i < l; i++) {\n            let off = 0;\n            data_rgba[offset] = scanline_buffer[i + off];\n            off += scanline_width;\n            data_rgba[offset + 1] = scanline_buffer[i + off];\n            off += scanline_width;\n            data_rgba[offset + 2] = scanline_buffer[i + off];\n            off += scanline_width;\n            data_rgba[offset + 3] = scanline_buffer[i + off];\n            offset += 4;\n          }\n          num_scanlines--;\n        }\n        return data_rgba;\n      };\n    const RGBEByteToRGBFloat = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3];\n      const scale = Math.pow(2, e - 128) / 255;\n      destArray[destOffset + 0] = sourceArray[sourceOffset + 0] * scale;\n      destArray[destOffset + 1] = sourceArray[sourceOffset + 1] * scale;\n      destArray[destOffset + 2] = sourceArray[sourceOffset + 2] * scale;\n      destArray[destOffset + 3] = 1;\n    };\n    const RGBEByteToRGBHalf = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3];\n      const scale = Math.pow(2, e - 128) / 255;\n      destArray[destOffset + 0] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 0] * scale, 65504));\n      destArray[destOffset + 1] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 1] * scale, 65504));\n      destArray[destOffset + 2] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 2] * scale, 65504));\n      destArray[destOffset + 3] = DataUtils.toHalfFloat(1);\n    };\n    const byteArray = new Uint8Array(buffer);\n    byteArray.pos = 0;\n    const rgbe_header_info = RGBE_ReadHeader(byteArray);\n    const w = rgbe_header_info.width,\n      h = rgbe_header_info.height,\n      image_rgba_data = RGBE_ReadPixels_RLE(byteArray.subarray(byteArray.pos), w, h);\n    let data, type;\n    let numElements;\n    switch (this.type) {\n      case FloatType:\n        numElements = image_rgba_data.length / 4;\n        const floatArray = new Float32Array(numElements * 4);\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBFloat(image_rgba_data, j * 4, floatArray, j * 4);\n        }\n        data = floatArray;\n        type = FloatType;\n        break;\n      case HalfFloatType:\n        numElements = image_rgba_data.length / 4;\n        const halfArray = new Uint16Array(numElements * 4);\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBHalf(image_rgba_data, j * 4, halfArray, j * 4);\n        }\n        data = halfArray;\n        type = HalfFloatType;\n        break;\n      default:\n        throw new Error(\"THREE.RGBELoader: Unsupported type: \" + this.type);\n    }\n    return {\n      width: w,\n      height: h,\n      data,\n      header: rgbe_header_info.string,\n      gamma: rgbe_header_info.gamma,\n      exposure: rgbe_header_info.exposure,\n      type\n    };\n  }\n  setDataType(value) {\n    this.type = value;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      switch (texture.type) {\n        case FloatType:\n        case HalfFloatType:\n          if (\"colorSpace\" in texture) texture.colorSpace = \"srgb-linear\";else texture.encoding = 3e3;\n          texture.minFilter = LinearFilter;\n          texture.magFilter = LinearFilter;\n          texture.generateMipmaps = false;\n          texture.flipY = true;\n          break;\n      }\n      if (onLoad) onLoad(texture, texData);\n    }\n    return super.load(url, onLoadCallback, onProgress, onError);\n  }\n}\nexport { RGBELoader };", "map": {"version": 3, "names": ["RGBELoader", "DataTextureLoader", "constructor", "manager", "type", "HalfFloatType", "parse", "buffer", "rgbe_read_error", "rgbe_write_error", "rgbe_format_error", "rgbe_memory_error", "rgbe_error", "rgbe_error_code", "msg", "Error", "RGBE_VALID_PROGRAMTYPE", "RGBE_VALID_FORMAT", "RGBE_VALID_DIMENSIONS", "NEWLINE", "fgets", "buffer2", "lineLimit", "consume", "chunkSize", "p", "pos", "i", "len", "s", "chunk", "String", "fromCharCode", "apply", "Uint16Array", "subarray", "indexOf", "byteLength", "length", "slice", "RGBE_ReadHeader", "magic_token_re", "gamma_re", "exposure_re", "format_re", "dimensions_re", "header", "valid", "string", "comments", "programtype", "format", "gamma", "exposure", "width", "height", "line", "match", "char<PERSON>t", "parseFloat", "parseInt", "RGBE_ReadPixels_RLE", "w2", "h2", "scanline_width", "Uint8Array", "data_rgba", "offset", "ptr_end", "rgbeStart", "scanline_buffer", "num_scanlines", "ptr", "count", "isEncodedRun", "byteValue", "set", "l", "off", "RGBEByteToRGBFloat", "sourceArray", "sourceOffset", "destArray", "destOffset", "e", "scale", "Math", "pow", "RGBEByteToRGBHalf", "DataUtils", "toHalfFloat", "min", "byteArray", "rgbe_header_info", "w", "h", "image_rgba_data", "data", "numElements", "FloatType", "floatArray", "Float32Array", "j", "halfArray", "setDataType", "value", "load", "url", "onLoad", "onProgress", "onError", "onLoadCallback", "texture", "texData", "colorSpace", "encoding", "minFilter", "LinearFilter", "magFilter", "generateMipmaps", "flipY"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/RGBELoader.js"], "sourcesContent": ["import { DataTextureLoader, DataUtils, FloatType, HalfFloatType, LinearFilter } from 'three'\n\n// https://github.com/mrdoob/three.js/issues/5552\n// http://en.wikipedia.org/wiki/RGBE_image_format\n\nclass RGBELoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n  }\n\n  // adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n\n  parse(buffer) {\n    const /* default error routine.  change this to change error handling */\n      rgbe_read_error = 1,\n      rgbe_write_error = 2,\n      rgbe_format_error = 3,\n      rgbe_memory_error = 4,\n      rgbe_error = function (rgbe_error_code, msg) {\n        switch (rgbe_error_code) {\n          case rgbe_read_error:\n            throw new Error('THREE.RGBELoader: Read Error: ' + (msg || ''))\n          case rgbe_write_error:\n            throw new Error('THREE.RGBELoader: Write Error: ' + (msg || ''))\n          case rgbe_format_error:\n            throw new Error('THREE.RGBELoader: Bad File Format: ' + (msg || ''))\n          default:\n          case rgbe_memory_error:\n            throw new Error('THREE.RGBELoader: Memory Error: ' + (msg || ''))\n        }\n      },\n      /* offsets to red, green, and blue components in a data (float) pixel */\n      //RGBE_DATA_RED = 0,\n      //RGBE_DATA_GREEN = 1,\n      //RGBE_DATA_BLUE = 2,\n\n      /* number of floats per pixel, use 4 since stored in rgba image format */\n      //RGBE_DATA_SIZE = 4,\n\n      /* flags indicating which fields in an rgbe_header_info are valid */\n      RGBE_VALID_PROGRAMTYPE = 1,\n      RGBE_VALID_FORMAT = 2,\n      RGBE_VALID_DIMENSIONS = 4,\n      NEWLINE = '\\n',\n      fgets = function (buffer, lineLimit, consume) {\n        const chunkSize = 128\n\n        lineLimit = !lineLimit ? 1024 : lineLimit\n        let p = buffer.pos,\n          i = -1,\n          len = 0,\n          s = '',\n          chunk = String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n\n        while (0 > (i = chunk.indexOf(NEWLINE)) && len < lineLimit && p < buffer.byteLength) {\n          s += chunk\n          len += chunk.length\n          p += chunkSize\n          chunk += String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n        }\n\n        if (-1 < i) {\n          /*for (i=l-1; i>=0; i--) {\n\t\t\t\t\t\tbyteCode = m.charCodeAt(i);\n\t\t\t\t\t\tif (byteCode > 0x7f && byteCode <= 0x7ff) byteLen++;\n\t\t\t\t\t\telse if (byteCode > 0x7ff && byteCode <= 0xffff) byteLen += 2;\n\t\t\t\t\t\tif (byteCode >= 0xDC00 && byteCode <= 0xDFFF) i--; //trail surrogate\n\t\t\t\t\t}*/\n          if (false !== consume) buffer.pos += len + i + 1\n          return s + chunk.slice(0, i)\n        }\n\n        return false\n      },\n      /* minimal header reading.  modify if you want to parse more information */\n      RGBE_ReadHeader = function (buffer) {\n        // regexes to parse header info fields\n        const magic_token_re = /^#\\?(\\S+)/,\n          gamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          exposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          format_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n          dimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n          // RGBE format header struct\n          header = {\n            valid: 0 /* indicate which fields are valid */,\n\n            string: '' /* the actual header string */,\n\n            comments: '' /* comments found in header */,\n\n            programtype: 'RGBE' /* listed at beginning of file to identify it after \"#?\". defaults to \"RGBE\" */,\n\n            format: '' /* RGBE format, default 32-bit_rle_rgbe */,\n\n            gamma: 1.0 /* image has already been gamma corrected with given gamma. defaults to 1.0 (no correction) */,\n\n            exposure: 1.0 /* a value of 1.0 in an image corresponds to <exposure> watts/steradian/m^2. defaults to 1.0 */,\n\n            width: 0,\n            height: 0 /* image dimensions, width/height */,\n          }\n\n        let line, match\n\n        if (buffer.pos >= buffer.byteLength || !(line = fgets(buffer))) {\n          rgbe_error(rgbe_read_error, 'no header found')\n        }\n\n        /* if you want to require the magic token then uncomment the next line */\n        if (!(match = line.match(magic_token_re))) {\n          rgbe_error(rgbe_format_error, 'bad initial token')\n        }\n\n        header.valid |= RGBE_VALID_PROGRAMTYPE\n        header.programtype = match[1]\n        header.string += line + '\\n'\n\n        while (true) {\n          line = fgets(buffer)\n          if (false === line) break\n          header.string += line + '\\n'\n\n          if ('#' === line.charAt(0)) {\n            header.comments += line + '\\n'\n            continue // comment line\n          }\n\n          if ((match = line.match(gamma_re))) {\n            header.gamma = parseFloat(match[1])\n          }\n\n          if ((match = line.match(exposure_re))) {\n            header.exposure = parseFloat(match[1])\n          }\n\n          if ((match = line.match(format_re))) {\n            header.valid |= RGBE_VALID_FORMAT\n            header.format = match[1] //'32-bit_rle_rgbe';\n          }\n\n          if ((match = line.match(dimensions_re))) {\n            header.valid |= RGBE_VALID_DIMENSIONS\n            header.height = parseInt(match[1], 10)\n            header.width = parseInt(match[2], 10)\n          }\n\n          if (header.valid & RGBE_VALID_FORMAT && header.valid & RGBE_VALID_DIMENSIONS) break\n        }\n\n        if (!(header.valid & RGBE_VALID_FORMAT)) {\n          rgbe_error(rgbe_format_error, 'missing format specifier')\n        }\n\n        if (!(header.valid & RGBE_VALID_DIMENSIONS)) {\n          rgbe_error(rgbe_format_error, 'missing image size specifier')\n        }\n\n        return header\n      },\n      RGBE_ReadPixels_RLE = function (buffer, w, h) {\n        const scanline_width = w\n\n        if (\n          // run length encoding is not allowed so read flat\n          scanline_width < 8 ||\n          scanline_width > 0x7fff ||\n          // this file is not run length encoded\n          2 !== buffer[0] ||\n          2 !== buffer[1] ||\n          buffer[2] & 0x80\n        ) {\n          // return the flat buffer\n          return new Uint8Array(buffer)\n        }\n\n        if (scanline_width !== ((buffer[2] << 8) | buffer[3])) {\n          rgbe_error(rgbe_format_error, 'wrong scanline width')\n        }\n\n        const data_rgba = new Uint8Array(4 * w * h)\n\n        if (!data_rgba.length) {\n          rgbe_error(rgbe_memory_error, 'unable to allocate buffer space')\n        }\n\n        let offset = 0,\n          pos = 0\n\n        const ptr_end = 4 * scanline_width\n        const rgbeStart = new Uint8Array(4)\n        const scanline_buffer = new Uint8Array(ptr_end)\n        let num_scanlines = h\n\n        // read in each successive scanline\n        while (num_scanlines > 0 && pos < buffer.byteLength) {\n          if (pos + 4 > buffer.byteLength) {\n            rgbe_error(rgbe_read_error)\n          }\n\n          rgbeStart[0] = buffer[pos++]\n          rgbeStart[1] = buffer[pos++]\n          rgbeStart[2] = buffer[pos++]\n          rgbeStart[3] = buffer[pos++]\n\n          if (2 != rgbeStart[0] || 2 != rgbeStart[1] || ((rgbeStart[2] << 8) | rgbeStart[3]) != scanline_width) {\n            rgbe_error(rgbe_format_error, 'bad rgbe scanline format')\n          }\n\n          // read each of the four channels for the scanline into the buffer\n          // first red, then green, then blue, then exponent\n          let ptr = 0,\n            count\n\n          while (ptr < ptr_end && pos < buffer.byteLength) {\n            count = buffer[pos++]\n            const isEncodedRun = count > 128\n            if (isEncodedRun) count -= 128\n\n            if (0 === count || ptr + count > ptr_end) {\n              rgbe_error(rgbe_format_error, 'bad scanline data')\n            }\n\n            if (isEncodedRun) {\n              // a (encoded) run of the same value\n              const byteValue = buffer[pos++]\n              for (let i = 0; i < count; i++) {\n                scanline_buffer[ptr++] = byteValue\n              }\n              //ptr += count;\n            } else {\n              // a literal-run\n              scanline_buffer.set(buffer.subarray(pos, pos + count), ptr)\n              ptr += count\n              pos += count\n            }\n          }\n\n          // now convert data from buffer into rgba\n          // first red, then green, then blue, then exponent (alpha)\n          const l = scanline_width //scanline_buffer.byteLength;\n          for (let i = 0; i < l; i++) {\n            let off = 0\n            data_rgba[offset] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 1] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 2] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 3] = scanline_buffer[i + off]\n            offset += 4\n          }\n\n          num_scanlines--\n        }\n\n        return data_rgba\n      }\n\n    const RGBEByteToRGBFloat = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      destArray[destOffset + 0] = sourceArray[sourceOffset + 0] * scale\n      destArray[destOffset + 1] = sourceArray[sourceOffset + 1] * scale\n      destArray[destOffset + 2] = sourceArray[sourceOffset + 2] * scale\n      destArray[destOffset + 3] = 1\n    }\n\n    const RGBEByteToRGBHalf = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      // clamping to 65504, the maximum representable value in float16\n      destArray[destOffset + 0] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 0] * scale, 65504))\n      destArray[destOffset + 1] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 1] * scale, 65504))\n      destArray[destOffset + 2] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 2] * scale, 65504))\n      destArray[destOffset + 3] = DataUtils.toHalfFloat(1)\n    }\n\n    const byteArray = new Uint8Array(buffer)\n    byteArray.pos = 0\n    const rgbe_header_info = RGBE_ReadHeader(byteArray)\n\n    const w = rgbe_header_info.width,\n      h = rgbe_header_info.height,\n      image_rgba_data = RGBE_ReadPixels_RLE(byteArray.subarray(byteArray.pos), w, h)\n\n    let data, type\n    let numElements\n\n    switch (this.type) {\n      case FloatType:\n        numElements = image_rgba_data.length / 4\n        const floatArray = new Float32Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBFloat(image_rgba_data, j * 4, floatArray, j * 4)\n        }\n\n        data = floatArray\n        type = FloatType\n        break\n\n      case HalfFloatType:\n        numElements = image_rgba_data.length / 4\n        const halfArray = new Uint16Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBHalf(image_rgba_data, j * 4, halfArray, j * 4)\n        }\n\n        data = halfArray\n        type = HalfFloatType\n        break\n\n      default:\n        throw new Error('THREE.RGBELoader: Unsupported type: ' + this.type)\n        break\n    }\n\n    return {\n      width: w,\n      height: h,\n      data: data,\n      header: rgbe_header_info.string,\n      gamma: rgbe_header_info.gamma,\n      exposure: rgbe_header_info.exposure,\n      type: type,\n    }\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      switch (texture.type) {\n        case FloatType:\n        case HalfFloatType:\n          if ('colorSpace' in texture) texture.colorSpace = 'srgb-linear'\n          else texture.encoding = 3000 // LinearEncoding\n          texture.minFilter = LinearFilter\n          texture.magFilter = LinearFilter\n          texture.generateMipmaps = false\n          texture.flipY = true\n\n          break\n      }\n\n      if (onLoad) onLoad(texture, texData)\n    }\n\n    return super.load(url, onLoadCallback, onProgress, onError)\n  }\n}\n\nexport { RGBELoader }\n"], "mappings": ";AAKA,MAAMA,UAAA,SAAmBC,iBAAA,CAAkB;EACzCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,IAAA,GAAOC,aAAA;EACb;EAAA;EAIDC,MAAMC,MAAA,EAAQ;IACZ,MACEC,eAAA,GAAkB;MAClBC,gBAAA,GAAmB;MACnBC,iBAAA,GAAoB;MACpBC,iBAAA,GAAoB;MACpBC,UAAA,GAAa,SAAAA,CAAUC,eAAA,EAAiBC,GAAA,EAAK;QAC3C,QAAQD,eAAA;UACN,KAAKL,eAAA;YACH,MAAM,IAAIO,KAAA,CAAM,oCAAoCD,GAAA,IAAO,GAAG;UAChE,KAAKL,gBAAA;YACH,MAAM,IAAIM,KAAA,CAAM,qCAAqCD,GAAA,IAAO,GAAG;UACjE,KAAKJ,iBAAA;YACH,MAAM,IAAIK,KAAA,CAAM,yCAAyCD,GAAA,IAAO,GAAG;UACrE;UACA,KAAKH,iBAAA;YACH,MAAM,IAAII,KAAA,CAAM,sCAAsCD,GAAA,IAAO,GAAG;QACnE;MACF;MAUDE,sBAAA,GAAyB;MACzBC,iBAAA,GAAoB;MACpBC,qBAAA,GAAwB;MACxBC,OAAA,GAAU;MACVC,KAAA,GAAQ,SAAAA,CAAUC,OAAA,EAAQC,SAAA,EAAWC,OAAA,EAAS;QAC5C,MAAMC,SAAA,GAAY;QAElBF,SAAA,GAAY,CAACA,SAAA,GAAY,OAAOA,SAAA;QAChC,IAAIG,CAAA,GAAIJ,OAAA,CAAOK,GAAA;UACbC,CAAA,GAAI;UACJC,GAAA,GAAM;UACNC,CAAA,GAAI;UACJC,KAAA,GAAQC,MAAA,CAAOC,YAAA,CAAaC,KAAA,CAAM,MAAM,IAAIC,WAAA,CAAYb,OAAA,CAAOc,QAAA,CAASV,CAAA,EAAGA,CAAA,GAAID,SAAS,CAAC,CAAC;QAE5F,OAAO,KAAKG,CAAA,GAAIG,KAAA,CAAMM,OAAA,CAAQjB,OAAO,MAAMS,GAAA,GAAMN,SAAA,IAAaG,CAAA,GAAIJ,OAAA,CAAOgB,UAAA,EAAY;UACnFR,CAAA,IAAKC,KAAA;UACLF,GAAA,IAAOE,KAAA,CAAMQ,MAAA;UACbb,CAAA,IAAKD,SAAA;UACLM,KAAA,IAASC,MAAA,CAAOC,YAAA,CAAaC,KAAA,CAAM,MAAM,IAAIC,WAAA,CAAYb,OAAA,CAAOc,QAAA,CAASV,CAAA,EAAGA,CAAA,GAAID,SAAS,CAAC,CAAC;QAC5F;QAED,IAAI,KAAKG,CAAA,EAAG;UAOV,IAAI,UAAUJ,OAAA,EAASF,OAAA,CAAOK,GAAA,IAAOE,GAAA,GAAMD,CAAA,GAAI;UAC/C,OAAOE,CAAA,GAAIC,KAAA,CAAMS,KAAA,CAAM,GAAGZ,CAAC;QAC5B;QAED,OAAO;MACR;MAEDa,eAAA,GAAkB,SAAAA,CAAUnB,OAAA,EAAQ;QAElC,MAAMoB,cAAA,GAAiB;UACrBC,QAAA,GAAW;UACXC,WAAA,GAAc;UACdC,SAAA,GAAY;UACZC,aAAA,GAAgB;UAEhBC,MAAA,GAAS;YACPC,KAAA,EAAO;YAEPC,MAAA,EAAQ;YAERC,QAAA,EAAU;YAEVC,WAAA,EAAa;YAEbC,MAAA,EAAQ;YAERC,KAAA,EAAO;YAEPC,QAAA,EAAU;YAEVC,KAAA,EAAO;YACPC,MAAA,EAAQ;UACT;QAEH,IAAIC,IAAA,EAAMC,KAAA;QAEV,IAAIpC,OAAA,CAAOK,GAAA,IAAOL,OAAA,CAAOgB,UAAA,IAAc,EAAEmB,IAAA,GAAOpC,KAAA,CAAMC,OAAM,IAAI;UAC9DT,UAAA,CAAWJ,eAAA,EAAiB,iBAAiB;QAC9C;QAGD,IAAI,EAAEiD,KAAA,GAAQD,IAAA,CAAKC,KAAA,CAAMhB,cAAc,IAAI;UACzC7B,UAAA,CAAWF,iBAAA,EAAmB,mBAAmB;QAClD;QAEDoC,MAAA,CAAOC,KAAA,IAAS/B,sBAAA;QAChB8B,MAAA,CAAOI,WAAA,GAAcO,KAAA,CAAM,CAAC;QAC5BX,MAAA,CAAOE,MAAA,IAAUQ,IAAA,GAAO;QAExB,OAAO,MAAM;UACXA,IAAA,GAAOpC,KAAA,CAAMC,OAAM;UACnB,IAAI,UAAUmC,IAAA,EAAM;UACpBV,MAAA,CAAOE,MAAA,IAAUQ,IAAA,GAAO;UAExB,IAAI,QAAQA,IAAA,CAAKE,MAAA,CAAO,CAAC,GAAG;YAC1BZ,MAAA,CAAOG,QAAA,IAAYO,IAAA,GAAO;YAC1B;UACD;UAED,IAAKC,KAAA,GAAQD,IAAA,CAAKC,KAAA,CAAMf,QAAQ,GAAI;YAClCI,MAAA,CAAOM,KAAA,GAAQO,UAAA,CAAWF,KAAA,CAAM,CAAC,CAAC;UACnC;UAED,IAAKA,KAAA,GAAQD,IAAA,CAAKC,KAAA,CAAMd,WAAW,GAAI;YACrCG,MAAA,CAAOO,QAAA,GAAWM,UAAA,CAAWF,KAAA,CAAM,CAAC,CAAC;UACtC;UAED,IAAKA,KAAA,GAAQD,IAAA,CAAKC,KAAA,CAAMb,SAAS,GAAI;YACnCE,MAAA,CAAOC,KAAA,IAAS9B,iBAAA;YAChB6B,MAAA,CAAOK,MAAA,GAASM,KAAA,CAAM,CAAC;UACxB;UAED,IAAKA,KAAA,GAAQD,IAAA,CAAKC,KAAA,CAAMZ,aAAa,GAAI;YACvCC,MAAA,CAAOC,KAAA,IAAS7B,qBAAA;YAChB4B,MAAA,CAAOS,MAAA,GAASK,QAAA,CAASH,KAAA,CAAM,CAAC,GAAG,EAAE;YACrCX,MAAA,CAAOQ,KAAA,GAAQM,QAAA,CAASH,KAAA,CAAM,CAAC,GAAG,EAAE;UACrC;UAED,IAAIX,MAAA,CAAOC,KAAA,GAAQ9B,iBAAA,IAAqB6B,MAAA,CAAOC,KAAA,GAAQ7B,qBAAA,EAAuB;QAC/E;QAED,IAAI,EAAE4B,MAAA,CAAOC,KAAA,GAAQ9B,iBAAA,GAAoB;UACvCL,UAAA,CAAWF,iBAAA,EAAmB,0BAA0B;QACzD;QAED,IAAI,EAAEoC,MAAA,CAAOC,KAAA,GAAQ7B,qBAAA,GAAwB;UAC3CN,UAAA,CAAWF,iBAAA,EAAmB,8BAA8B;QAC7D;QAED,OAAOoC,MAAA;MACR;MACDe,mBAAA,GAAsB,SAAAA,CAAUxC,OAAA,EAAQyC,EAAA,EAAGC,EAAA,EAAG;QAC5C,MAAMC,cAAA,GAAiBF,EAAA;QAEvB;QAAA;QAEEE,cAAA,GAAiB,KACjBA,cAAA,GAAiB;QAAA;QAEjB,MAAM3C,OAAA,CAAO,CAAC,KACd,MAAMA,OAAA,CAAO,CAAC,KACdA,OAAA,CAAO,CAAC,IAAI,KACZ;UAEA,OAAO,IAAI4C,UAAA,CAAW5C,OAAM;QAC7B;QAED,IAAI2C,cAAA,MAAqB3C,OAAA,CAAO,CAAC,KAAK,IAAKA,OAAA,CAAO,CAAC,IAAI;UACrDT,UAAA,CAAWF,iBAAA,EAAmB,sBAAsB;QACrD;QAED,MAAMwD,SAAA,GAAY,IAAID,UAAA,CAAW,IAAIH,EAAA,GAAIC,EAAC;QAE1C,IAAI,CAACG,SAAA,CAAU5B,MAAA,EAAQ;UACrB1B,UAAA,CAAWD,iBAAA,EAAmB,iCAAiC;QAChE;QAED,IAAIwD,MAAA,GAAS;UACXzC,GAAA,GAAM;QAER,MAAM0C,OAAA,GAAU,IAAIJ,cAAA;QACpB,MAAMK,SAAA,GAAY,IAAIJ,UAAA,CAAW,CAAC;QAClC,MAAMK,eAAA,GAAkB,IAAIL,UAAA,CAAWG,OAAO;QAC9C,IAAIG,aAAA,GAAgBR,EAAA;QAGpB,OAAOQ,aAAA,GAAgB,KAAK7C,GAAA,GAAML,OAAA,CAAOgB,UAAA,EAAY;UACnD,IAAIX,GAAA,GAAM,IAAIL,OAAA,CAAOgB,UAAA,EAAY;YAC/BzB,UAAA,CAAWJ,eAAe;UAC3B;UAED6D,SAAA,CAAU,CAAC,IAAIhD,OAAA,CAAOK,GAAA,EAAK;UAC3B2C,SAAA,CAAU,CAAC,IAAIhD,OAAA,CAAOK,GAAA,EAAK;UAC3B2C,SAAA,CAAU,CAAC,IAAIhD,OAAA,CAAOK,GAAA,EAAK;UAC3B2C,SAAA,CAAU,CAAC,IAAIhD,OAAA,CAAOK,GAAA,EAAK;UAE3B,IAAI,KAAK2C,SAAA,CAAU,CAAC,KAAK,KAAKA,SAAA,CAAU,CAAC,MAAOA,SAAA,CAAU,CAAC,KAAK,IAAKA,SAAA,CAAU,CAAC,MAAML,cAAA,EAAgB;YACpGpD,UAAA,CAAWF,iBAAA,EAAmB,0BAA0B;UACzD;UAID,IAAI8D,GAAA,GAAM;YACRC,KAAA;UAEF,OAAOD,GAAA,GAAMJ,OAAA,IAAW1C,GAAA,GAAML,OAAA,CAAOgB,UAAA,EAAY;YAC/CoC,KAAA,GAAQpD,OAAA,CAAOK,GAAA,EAAK;YACpB,MAAMgD,YAAA,GAAeD,KAAA,GAAQ;YAC7B,IAAIC,YAAA,EAAcD,KAAA,IAAS;YAE3B,IAAI,MAAMA,KAAA,IAASD,GAAA,GAAMC,KAAA,GAAQL,OAAA,EAAS;cACxCxD,UAAA,CAAWF,iBAAA,EAAmB,mBAAmB;YAClD;YAED,IAAIgE,YAAA,EAAc;cAEhB,MAAMC,SAAA,GAAYtD,OAAA,CAAOK,GAAA,EAAK;cAC9B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI8C,KAAA,EAAO9C,CAAA,IAAK;gBAC9B2C,eAAA,CAAgBE,GAAA,EAAK,IAAIG,SAAA;cAC1B;YAEf,OAAmB;cAELL,eAAA,CAAgBM,GAAA,CAAIvD,OAAA,CAAOc,QAAA,CAAST,GAAA,EAAKA,GAAA,GAAM+C,KAAK,GAAGD,GAAG;cAC1DA,GAAA,IAAOC,KAAA;cACP/C,GAAA,IAAO+C,KAAA;YACR;UACF;UAID,MAAMI,CAAA,GAAIb,cAAA;UACV,SAASrC,CAAA,GAAI,GAAGA,CAAA,GAAIkD,CAAA,EAAGlD,CAAA,IAAK;YAC1B,IAAImD,GAAA,GAAM;YACVZ,SAAA,CAAUC,MAAM,IAAIG,eAAA,CAAgB3C,CAAA,GAAImD,GAAG;YAC3CA,GAAA,IAAOd,cAAA;YACPE,SAAA,CAAUC,MAAA,GAAS,CAAC,IAAIG,eAAA,CAAgB3C,CAAA,GAAImD,GAAG;YAC/CA,GAAA,IAAOd,cAAA;YACPE,SAAA,CAAUC,MAAA,GAAS,CAAC,IAAIG,eAAA,CAAgB3C,CAAA,GAAImD,GAAG;YAC/CA,GAAA,IAAOd,cAAA;YACPE,SAAA,CAAUC,MAAA,GAAS,CAAC,IAAIG,eAAA,CAAgB3C,CAAA,GAAImD,GAAG;YAC/CX,MAAA,IAAU;UACX;UAEDI,aAAA;QACD;QAED,OAAOL,SAAA;MACR;IAEH,MAAMa,kBAAA,GAAqB,SAAAA,CAAUC,WAAA,EAAaC,YAAA,EAAcC,SAAA,EAAWC,UAAA,EAAY;MACrF,MAAMC,CAAA,GAAIJ,WAAA,CAAYC,YAAA,GAAe,CAAC;MACtC,MAAMI,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAI,GAAKH,CAAA,GAAI,GAAK,IAAI;MAEzCF,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIH,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA;MAC5DH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIH,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA;MAC5DH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIH,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA;MAC5DH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAI;IAC7B;IAED,MAAMK,iBAAA,GAAoB,SAAAA,CAAUR,WAAA,EAAaC,YAAA,EAAcC,SAAA,EAAWC,UAAA,EAAY;MACpF,MAAMC,CAAA,GAAIJ,WAAA,CAAYC,YAAA,GAAe,CAAC;MACtC,MAAMI,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAI,GAAKH,CAAA,GAAI,GAAK,IAAI;MAGzCF,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIM,SAAA,CAAUC,WAAA,CAAYJ,IAAA,CAAKK,GAAA,CAAIX,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA,EAAO,KAAK,CAAC;MACxGH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIM,SAAA,CAAUC,WAAA,CAAYJ,IAAA,CAAKK,GAAA,CAAIX,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA,EAAO,KAAK,CAAC;MACxGH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIM,SAAA,CAAUC,WAAA,CAAYJ,IAAA,CAAKK,GAAA,CAAIX,WAAA,CAAYC,YAAA,GAAe,CAAC,IAAII,KAAA,EAAO,KAAK,CAAC;MACxGH,SAAA,CAAUC,UAAA,GAAa,CAAC,IAAIM,SAAA,CAAUC,WAAA,CAAY,CAAC;IACpD;IAED,MAAME,SAAA,GAAY,IAAI3B,UAAA,CAAW1D,MAAM;IACvCqF,SAAA,CAAUlE,GAAA,GAAM;IAChB,MAAMmE,gBAAA,GAAmBrD,eAAA,CAAgBoD,SAAS;IAElD,MAAME,CAAA,GAAID,gBAAA,CAAiBvC,KAAA;MACzByC,CAAA,GAAIF,gBAAA,CAAiBtC,MAAA;MACrByC,eAAA,GAAkBnC,mBAAA,CAAoB+B,SAAA,CAAUzD,QAAA,CAASyD,SAAA,CAAUlE,GAAG,GAAGoE,CAAA,EAAGC,CAAC;IAE/E,IAAIE,IAAA,EAAM7F,IAAA;IACV,IAAI8F,WAAA;IAEJ,QAAQ,KAAK9F,IAAA;MACX,KAAK+F,SAAA;QACHD,WAAA,GAAcF,eAAA,CAAgB1D,MAAA,GAAS;QACvC,MAAM8D,UAAA,GAAa,IAAIC,YAAA,CAAaH,WAAA,GAAc,CAAC;QAEnD,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIJ,WAAA,EAAaI,CAAA,IAAK;UACpCvB,kBAAA,CAAmBiB,eAAA,EAAiBM,CAAA,GAAI,GAAGF,UAAA,EAAYE,CAAA,GAAI,CAAC;QAC7D;QAEDL,IAAA,GAAOG,UAAA;QACPhG,IAAA,GAAO+F,SAAA;QACP;MAEF,KAAK9F,aAAA;QACH6F,WAAA,GAAcF,eAAA,CAAgB1D,MAAA,GAAS;QACvC,MAAMiE,SAAA,GAAY,IAAIrE,WAAA,CAAYgE,WAAA,GAAc,CAAC;QAEjD,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIJ,WAAA,EAAaI,CAAA,IAAK;UACpCd,iBAAA,CAAkBQ,eAAA,EAAiBM,CAAA,GAAI,GAAGC,SAAA,EAAWD,CAAA,GAAI,CAAC;QAC3D;QAEDL,IAAA,GAAOM,SAAA;QACPnG,IAAA,GAAOC,aAAA;QACP;MAEF;QACE,MAAM,IAAIU,KAAA,CAAM,yCAAyC,KAAKX,IAAI;IAErE;IAED,OAAO;MACLkD,KAAA,EAAOwC,CAAA;MACPvC,MAAA,EAAQwC,CAAA;MACRE,IAAA;MACAnD,MAAA,EAAQ+C,gBAAA,CAAiB7C,MAAA;MACzBI,KAAA,EAAOyC,gBAAA,CAAiBzC,KAAA;MACxBC,QAAA,EAAUwC,gBAAA,CAAiBxC,QAAA;MAC3BjD;IACD;EACF;EAEDoG,YAAYC,KAAA,EAAO;IACjB,KAAKrG,IAAA,GAAOqG,KAAA;IACZ,OAAO;EACR;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,SAASC,eAAeC,OAAA,EAASC,OAAA,EAAS;MACxC,QAAQD,OAAA,CAAQ5G,IAAA;QACd,KAAK+F,SAAA;QACL,KAAK9F,aAAA;UACH,IAAI,gBAAgB2G,OAAA,EAASA,OAAA,CAAQE,UAAA,GAAa,mBAC7CF,OAAA,CAAQG,QAAA,GAAW;UACxBH,OAAA,CAAQI,SAAA,GAAYC,YAAA;UACpBL,OAAA,CAAQM,SAAA,GAAYD,YAAA;UACpBL,OAAA,CAAQO,eAAA,GAAkB;UAC1BP,OAAA,CAAQQ,KAAA,GAAQ;UAEhB;MACH;MAED,IAAIZ,MAAA,EAAQA,MAAA,CAAOI,OAAA,EAASC,OAAO;IACpC;IAED,OAAO,MAAMP,IAAA,CAAKC,GAAA,EAAKI,cAAA,EAAgBF,UAAA,EAAYC,OAAO;EAC3D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}