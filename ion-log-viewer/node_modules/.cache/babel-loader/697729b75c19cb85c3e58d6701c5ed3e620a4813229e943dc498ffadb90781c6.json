{"ast": null, "code": "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\nconst identity = arg => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(api.subscribe, () => selector(api.getState()), () => selector(api.getInitialState()));\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = createState => {\n  const api = createStore(createState);\n  const useBoundStore = selector => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = createState => createState ? createImpl(createState) : createImpl;\nexport { create, useStore };", "map": {"version": 3, "names": ["React", "createStore", "identity", "arg", "useStore", "api", "selector", "slice", "useSyncExternalStore", "subscribe", "getState", "getInitialState", "useDebugValue", "createImpl", "createState", "useBoundStore", "Object", "assign", "create"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/zustand/esm/react.mjs"], "sourcesContent": ["import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,iBAAiB;AAE7C,MAAMC,QAAQ,GAAIC,GAAG,IAAKA,GAAG;AAC7B,SAASC,QAAQA,CAACC,GAAG,EAAEC,QAAQ,GAAGJ,QAAQ,EAAE;EAC1C,MAAMK,KAAK,GAAGP,KAAK,CAACQ,oBAAoB,CACtCH,GAAG,CAACI,SAAS,EACb,MAAMH,QAAQ,CAACD,GAAG,CAACK,QAAQ,CAAC,CAAC,CAAC,EAC9B,MAAMJ,QAAQ,CAACD,GAAG,CAACM,eAAe,CAAC,CAAC,CACtC,CAAC;EACDX,KAAK,CAACY,aAAa,CAACL,KAAK,CAAC;EAC1B,OAAOA,KAAK;AACd;AACA,MAAMM,UAAU,GAAIC,WAAW,IAAK;EAClC,MAAMT,GAAG,GAAGJ,WAAW,CAACa,WAAW,CAAC;EACpC,MAAMC,aAAa,GAAIT,QAAQ,IAAKF,QAAQ,CAACC,GAAG,EAAEC,QAAQ,CAAC;EAC3DU,MAAM,CAACC,MAAM,CAACF,aAAa,EAAEV,GAAG,CAAC;EACjC,OAAOU,aAAa;AACtB,CAAC;AACD,MAAMG,MAAM,GAAIJ,WAAW,IAAKA,WAAW,GAAGD,UAAU,CAACC,WAAW,CAAC,GAAGD,UAAU;AAElF,SAASK,MAAM,EAAEd,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}