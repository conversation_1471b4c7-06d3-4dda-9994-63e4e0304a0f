{"ast": null, "code": "import { Object3D, Vector3, <PERSON>uatern<PERSON>, Matrix4 } from \"three\";\nconst _position = /* @__PURE__ */new Vector3();\nconst _quaternion = /* @__PURE__ */new Quaternion();\nconst _scale = /* @__PURE__ */new Vector3();\nclass CSS3DObject extends Object3D {\n  constructor(element = document.createElement(\"div\")) {\n    super();\n    this.isCSS3DObject = true;\n    this.element = element;\n    this.element.style.position = \"absolute\";\n    this.element.style.pointerEvents = \"auto\";\n    this.element.style.userSelect = \"none\";\n    this.element.setAttribute(\"draggable\", false);\n    this.addEventListener(\"removed\", function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element);\n        }\n      });\n    });\n  }\n  copy(source, recursive) {\n    super.copy(source, recursive);\n    this.element = source.element.cloneNode(true);\n    return this;\n  }\n}\nclass CSS3DSprite extends CSS3DObject {\n  constructor(element) {\n    super(element);\n    this.isCSS3DSprite = true;\n    this.rotation2D = 0;\n  }\n  copy(source, recursive) {\n    super.copy(source, recursive);\n    this.rotation2D = source.rotation2D;\n    return this;\n  }\n}\nconst _matrix = /* @__PURE__ */new Matrix4();\nconst _matrix2 = /* @__PURE__ */new Matrix4();\nclass CSS3DRenderer {\n  constructor(parameters = {}) {\n    const _this = this;\n    let _width, _height;\n    let _widthHalf, _heightHalf;\n    const cache = {\n      camera: {\n        style: \"\"\n      },\n      objects: /* @__PURE__ */new WeakMap()\n    };\n    const domElement = parameters.element !== void 0 ? parameters.element : document.createElement(\"div\");\n    domElement.style.overflow = \"hidden\";\n    this.domElement = domElement;\n    const viewElement = document.createElement(\"div\");\n    viewElement.style.transformOrigin = \"0 0\";\n    viewElement.style.pointerEvents = \"none\";\n    domElement.appendChild(viewElement);\n    const cameraElement = document.createElement(\"div\");\n    cameraElement.style.transformStyle = \"preserve-3d\";\n    viewElement.appendChild(cameraElement);\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height\n      };\n    };\n    this.render = function (scene, camera) {\n      const fov = camera.projectionMatrix.elements[5] * _heightHalf;\n      if (camera.view && camera.view.enabled) {\n        viewElement.style.transform = `translate( ${-camera.view.offsetX * (_width / camera.view.width)}px, ${-camera.view.offsetY * (_height / camera.view.height)}px )`;\n        viewElement.style.transform += `scale( ${camera.view.fullWidth / camera.view.width}, ${camera.view.fullHeight / camera.view.height} )`;\n      } else {\n        viewElement.style.transform = \"\";\n      }\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      let tx, ty;\n      if (camera.isOrthographicCamera) {\n        tx = -(camera.right + camera.left) / 2;\n        ty = (camera.top + camera.bottom) / 2;\n      }\n      const scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1;\n      const cameraCSSMatrix = camera.isOrthographicCamera ? `scale( ${scaleByViewOffset} )scale(` + fov + \")translate(\" + epsilon(tx) + \"px,\" + epsilon(ty) + \"px)\" + getCameraCSSMatrix(camera.matrixWorldInverse) : `scale( ${scaleByViewOffset} )translateZ(` + fov + \"px)\" + getCameraCSSMatrix(camera.matrixWorldInverse);\n      const perspective = camera.isPerspectiveCamera ? \"perspective(\" + fov + \"px) \" : \"\";\n      const style = perspective + cameraCSSMatrix + \"translate(\" + _widthHalf + \"px,\" + _heightHalf + \"px)\";\n      if (cache.camera.style !== style) {\n        cameraElement.style.transform = style;\n        cache.camera.style = style;\n      }\n      renderObject(scene, scene, camera);\n    };\n    this.setSize = function (width, height) {\n      _width = width;\n      _height = height;\n      _widthHalf = _width / 2;\n      _heightHalf = _height / 2;\n      domElement.style.width = width + \"px\";\n      domElement.style.height = height + \"px\";\n      viewElement.style.width = width + \"px\";\n      viewElement.style.height = height + \"px\";\n      cameraElement.style.width = width + \"px\";\n      cameraElement.style.height = height + \"px\";\n    };\n    function epsilon(value) {\n      return Math.abs(value) < 1e-10 ? 0 : value;\n    }\n    function getCameraCSSMatrix(matrix) {\n      const elements = matrix.elements;\n      return \"matrix3d(\" + epsilon(elements[0]) + \",\" + epsilon(-elements[1]) + \",\" + epsilon(elements[2]) + \",\" + epsilon(elements[3]) + \",\" + epsilon(elements[4]) + \",\" + epsilon(-elements[5]) + \",\" + epsilon(elements[6]) + \",\" + epsilon(elements[7]) + \",\" + epsilon(elements[8]) + \",\" + epsilon(-elements[9]) + \",\" + epsilon(elements[10]) + \",\" + epsilon(elements[11]) + \",\" + epsilon(elements[12]) + \",\" + epsilon(-elements[13]) + \",\" + epsilon(elements[14]) + \",\" + epsilon(elements[15]) + \")\";\n    }\n    function getObjectCSSMatrix(matrix) {\n      const elements = matrix.elements;\n      const matrix3d = \"matrix3d(\" + epsilon(elements[0]) + \",\" + epsilon(elements[1]) + \",\" + epsilon(elements[2]) + \",\" + epsilon(elements[3]) + \",\" + epsilon(-elements[4]) + \",\" + epsilon(-elements[5]) + \",\" + epsilon(-elements[6]) + \",\" + epsilon(-elements[7]) + \",\" + epsilon(elements[8]) + \",\" + epsilon(elements[9]) + \",\" + epsilon(elements[10]) + \",\" + epsilon(elements[11]) + \",\" + epsilon(elements[12]) + \",\" + epsilon(elements[13]) + \",\" + epsilon(elements[14]) + \",\" + epsilon(elements[15]) + \")\";\n      return \"translate(-50%,-50%)\" + matrix3d;\n    }\n    function renderObject(object, scene, camera, cameraCSSMatrix) {\n      if (object.isCSS3DObject) {\n        const visible = object.visible === true && object.layers.test(camera.layers) === true;\n        object.element.style.display = visible === true ? \"\" : \"none\";\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera);\n          let style;\n          if (object.isCSS3DSprite) {\n            _matrix.copy(camera.matrixWorldInverse);\n            _matrix.transpose();\n            if (object.rotation2D !== 0) _matrix.multiply(_matrix2.makeRotationZ(object.rotation2D));\n            object.matrixWorld.decompose(_position, _quaternion, _scale);\n            _matrix.setPosition(_position);\n            _matrix.scale(_scale);\n            _matrix.elements[3] = 0;\n            _matrix.elements[7] = 0;\n            _matrix.elements[11] = 0;\n            _matrix.elements[15] = 1;\n            style = getObjectCSSMatrix(_matrix);\n          } else {\n            style = getObjectCSSMatrix(object.matrixWorld);\n          }\n          const element = object.element;\n          const cachedObject = cache.objects.get(object);\n          if (cachedObject === void 0 || cachedObject.style !== style) {\n            element.style.transform = style;\n            const objectData = {\n              style\n            };\n            cache.objects.set(object, objectData);\n          }\n          if (element.parentNode !== cameraElement) {\n            cameraElement.appendChild(element);\n          }\n          object.onAfterRender(_this, scene, camera);\n        }\n      }\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera);\n      }\n    }\n  }\n}\nexport { CSS3DObject, CSS3DRenderer, CSS3DSprite };", "map": {"version": 3, "names": ["_position", "Vector3", "_quaternion", "Quaternion", "_scale", "CSS3DObject", "Object3D", "constructor", "element", "document", "createElement", "isCSS3DObject", "style", "position", "pointerEvents", "userSelect", "setAttribute", "addEventListener", "traverse", "object", "Element", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "copy", "source", "recursive", "cloneNode", "CSS3DSprite", "isCSS3DSprite", "rotation2D", "_matrix", "Matrix4", "_matrix2", "CSS3<PERSON><PERSON><PERSON>", "parameters", "_this", "_width", "_height", "_widthHalf", "_heightHalf", "cache", "camera", "objects", "WeakMap", "dom<PERSON>lement", "overflow", "viewElement", "transform<PERSON><PERSON>in", "append<PERSON><PERSON><PERSON>", "cameraElement", "transformStyle", "getSize", "width", "height", "render", "scene", "fov", "projectionMatrix", "elements", "view", "enabled", "transform", "offsetX", "offsetY", "fullWidth", "fullHeight", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "tx", "ty", "isOrthographicCamera", "right", "left", "top", "bottom", "scaleByViewOffset", "cameraCSSMatrix", "epsilon", "getCameraCSSMatrix", "matrixWorldInverse", "perspective", "isPerspectiveCamera", "renderObject", "setSize", "value", "Math", "abs", "matrix", "getObjectCSSMatrix", "matrix3d", "visible", "layers", "test", "display", "onBeforeRender", "transpose", "multiply", "makeRotationZ", "matrixWorld", "decompose", "setPosition", "scale", "cachedObject", "get", "objectData", "set", "onAfterRender", "i", "l", "children", "length"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/renderers/CSS3DRenderer.js"], "sourcesContent": ["import { Matrix4, Object3D, Quaternion, Vector3 } from 'three'\n\n/**\n * Based on http://www.emagix.net/academic/mscs-project/item/camera-sync-with-css3-and-webgl-threejs\n */\n\nconst _position = /* @__PURE__ */ new Vector3()\nconst _quaternion = /* @__PURE__ */ new Quaternion()\nconst _scale = /* @__PURE__ */ new Vector3()\n\nclass CSS3DObject extends Object3D {\n  constructor(element = document.createElement('div')) {\n    super()\n\n    this.isCSS3DObject = true\n\n    this.element = element\n    this.element.style.position = 'absolute'\n    this.element.style.pointerEvents = 'auto'\n    this.element.style.userSelect = 'none'\n\n    this.element.setAttribute('draggable', false)\n\n    this.addEventListener('removed', function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element)\n        }\n      })\n    })\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.element = source.element.cloneNode(true)\n\n    return this\n  }\n}\n\nclass CSS3DSprite extends CSS3DObject {\n  constructor(element) {\n    super(element)\n\n    this.isCSS3DSprite = true\n\n    this.rotation2D = 0\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.rotation2D = source.rotation2D\n\n    return this\n  }\n}\n\n//\n\nconst _matrix = /* @__PURE__ */ new Matrix4()\nconst _matrix2 = /* @__PURE__ */ new Matrix4()\n\nclass CSS3DRenderer {\n  constructor(parameters = {}) {\n    const _this = this\n\n    let _width, _height\n    let _widthHalf, _heightHalf\n\n    const cache = {\n      camera: { style: '' },\n      objects: new WeakMap(),\n    }\n\n    const domElement = parameters.element !== undefined ? parameters.element : document.createElement('div')\n\n    domElement.style.overflow = 'hidden'\n\n    this.domElement = domElement\n\n    const viewElement = document.createElement('div')\n    viewElement.style.transformOrigin = '0 0'\n    viewElement.style.pointerEvents = 'none'\n    domElement.appendChild(viewElement)\n\n    const cameraElement = document.createElement('div')\n\n    cameraElement.style.transformStyle = 'preserve-3d'\n\n    viewElement.appendChild(cameraElement)\n\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height,\n      }\n    }\n\n    this.render = function (scene, camera) {\n      const fov = camera.projectionMatrix.elements[5] * _heightHalf\n\n      if (camera.view && camera.view.enabled) {\n        // view offset\n        viewElement.style.transform = `translate( ${-camera.view.offsetX * (_width / camera.view.width)}px, ${\n          -camera.view.offsetY * (_height / camera.view.height)\n        }px )`\n\n        // view fullWidth and fullHeight, view width and height\n        viewElement.style.transform += `scale( ${camera.view.fullWidth / camera.view.width}, ${\n          camera.view.fullHeight / camera.view.height\n        } )`\n      } else {\n        viewElement.style.transform = ''\n      }\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      let tx, ty\n\n      if (camera.isOrthographicCamera) {\n        tx = -(camera.right + camera.left) / 2\n        ty = (camera.top + camera.bottom) / 2\n      }\n\n      const scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1\n      const cameraCSSMatrix = camera.isOrthographicCamera\n        ? `scale( ${scaleByViewOffset} )` +\n          'scale(' +\n          fov +\n          ')' +\n          'translate(' +\n          epsilon(tx) +\n          'px,' +\n          epsilon(ty) +\n          'px)' +\n          getCameraCSSMatrix(camera.matrixWorldInverse)\n        : `scale( ${scaleByViewOffset} )` + 'translateZ(' + fov + 'px)' + getCameraCSSMatrix(camera.matrixWorldInverse)\n      const perspective = camera.isPerspectiveCamera ? 'perspective(' + fov + 'px) ' : ''\n\n      const style = perspective + cameraCSSMatrix + 'translate(' + _widthHalf + 'px,' + _heightHalf + 'px)'\n\n      if (cache.camera.style !== style) {\n        cameraElement.style.transform = style\n\n        cache.camera.style = style\n      }\n\n      renderObject(scene, scene, camera, cameraCSSMatrix)\n    }\n\n    this.setSize = function (width, height) {\n      _width = width\n      _height = height\n      _widthHalf = _width / 2\n      _heightHalf = _height / 2\n\n      domElement.style.width = width + 'px'\n      domElement.style.height = height + 'px'\n\n      viewElement.style.width = width + 'px'\n      viewElement.style.height = height + 'px'\n\n      cameraElement.style.width = width + 'px'\n      cameraElement.style.height = height + 'px'\n    }\n\n    function epsilon(value) {\n      return Math.abs(value) < 1e-10 ? 0 : value\n    }\n\n    function getCameraCSSMatrix(matrix) {\n      const elements = matrix.elements\n\n      return (\n        'matrix3d(' +\n        epsilon(elements[0]) +\n        ',' +\n        epsilon(-elements[1]) +\n        ',' +\n        epsilon(elements[2]) +\n        ',' +\n        epsilon(elements[3]) +\n        ',' +\n        epsilon(elements[4]) +\n        ',' +\n        epsilon(-elements[5]) +\n        ',' +\n        epsilon(elements[6]) +\n        ',' +\n        epsilon(elements[7]) +\n        ',' +\n        epsilon(elements[8]) +\n        ',' +\n        epsilon(-elements[9]) +\n        ',' +\n        epsilon(elements[10]) +\n        ',' +\n        epsilon(elements[11]) +\n        ',' +\n        epsilon(elements[12]) +\n        ',' +\n        epsilon(-elements[13]) +\n        ',' +\n        epsilon(elements[14]) +\n        ',' +\n        epsilon(elements[15]) +\n        ')'\n      )\n    }\n\n    function getObjectCSSMatrix(matrix) {\n      const elements = matrix.elements\n      const matrix3d =\n        'matrix3d(' +\n        epsilon(elements[0]) +\n        ',' +\n        epsilon(elements[1]) +\n        ',' +\n        epsilon(elements[2]) +\n        ',' +\n        epsilon(elements[3]) +\n        ',' +\n        epsilon(-elements[4]) +\n        ',' +\n        epsilon(-elements[5]) +\n        ',' +\n        epsilon(-elements[6]) +\n        ',' +\n        epsilon(-elements[7]) +\n        ',' +\n        epsilon(elements[8]) +\n        ',' +\n        epsilon(elements[9]) +\n        ',' +\n        epsilon(elements[10]) +\n        ',' +\n        epsilon(elements[11]) +\n        ',' +\n        epsilon(elements[12]) +\n        ',' +\n        epsilon(elements[13]) +\n        ',' +\n        epsilon(elements[14]) +\n        ',' +\n        epsilon(elements[15]) +\n        ')'\n\n      return 'translate(-50%,-50%)' + matrix3d\n    }\n\n    function renderObject(object, scene, camera, cameraCSSMatrix) {\n      if (object.isCSS3DObject) {\n        const visible = object.visible === true && object.layers.test(camera.layers) === true\n        object.element.style.display = visible === true ? '' : 'none'\n\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera)\n\n          let style\n\n          if (object.isCSS3DSprite) {\n            // http://swiftcoder.wordpress.com/2008/11/25/constructing-a-billboard-matrix/\n\n            _matrix.copy(camera.matrixWorldInverse)\n            _matrix.transpose()\n\n            if (object.rotation2D !== 0) _matrix.multiply(_matrix2.makeRotationZ(object.rotation2D))\n\n            object.matrixWorld.decompose(_position, _quaternion, _scale)\n            _matrix.setPosition(_position)\n            _matrix.scale(_scale)\n\n            _matrix.elements[3] = 0\n            _matrix.elements[7] = 0\n            _matrix.elements[11] = 0\n            _matrix.elements[15] = 1\n\n            style = getObjectCSSMatrix(_matrix)\n          } else {\n            style = getObjectCSSMatrix(object.matrixWorld)\n          }\n\n          const element = object.element\n          const cachedObject = cache.objects.get(object)\n\n          if (cachedObject === undefined || cachedObject.style !== style) {\n            element.style.transform = style\n\n            const objectData = { style: style }\n            cache.objects.set(object, objectData)\n          }\n\n          if (element.parentNode !== cameraElement) {\n            cameraElement.appendChild(element)\n          }\n\n          object.onAfterRender(_this, scene, camera)\n        }\n      }\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera, cameraCSSMatrix)\n      }\n    }\n  }\n}\n\nexport { CSS3DObject, CSS3DSprite, CSS3DRenderer }\n"], "mappings": ";AAMA,MAAMA,SAAA,GAA4B,mBAAIC,OAAA,CAAS;AAC/C,MAAMC,WAAA,GAA8B,mBAAIC,UAAA,CAAY;AACpD,MAAMC,MAAA,GAAyB,mBAAIH,OAAA,CAAS;AAE5C,MAAMI,WAAA,SAAoBC,QAAA,CAAS;EACjCC,YAAYC,OAAA,GAAUC,QAAA,CAASC,aAAA,CAAc,KAAK,GAAG;IACnD,MAAO;IAEP,KAAKC,aAAA,GAAgB;IAErB,KAAKH,OAAA,GAAUA,OAAA;IACf,KAAKA,OAAA,CAAQI,KAAA,CAAMC,QAAA,GAAW;IAC9B,KAAKL,OAAA,CAAQI,KAAA,CAAME,aAAA,GAAgB;IACnC,KAAKN,OAAA,CAAQI,KAAA,CAAMG,UAAA,GAAa;IAEhC,KAAKP,OAAA,CAAQQ,YAAA,CAAa,aAAa,KAAK;IAE5C,KAAKC,gBAAA,CAAiB,WAAW,YAAY;MAC3C,KAAKC,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,IAAIA,MAAA,CAAOX,OAAA,YAAmBY,OAAA,IAAWD,MAAA,CAAOX,OAAA,CAAQa,UAAA,KAAe,MAAM;UAC3EF,MAAA,CAAOX,OAAA,CAAQa,UAAA,CAAWC,WAAA,CAAYH,MAAA,CAAOX,OAAO;QACrD;MACT,CAAO;IACP,CAAK;EACF;EAEDe,KAAKC,MAAA,EAAQC,SAAA,EAAW;IACtB,MAAMF,IAAA,CAAKC,MAAA,EAAQC,SAAS;IAE5B,KAAKjB,OAAA,GAAUgB,MAAA,CAAOhB,OAAA,CAAQkB,SAAA,CAAU,IAAI;IAE5C,OAAO;EACR;AACH;AAEA,MAAMC,WAAA,SAAoBtB,WAAA,CAAY;EACpCE,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKoB,aAAA,GAAgB;IAErB,KAAKC,UAAA,GAAa;EACnB;EAEDN,KAAKC,MAAA,EAAQC,SAAA,EAAW;IACtB,MAAMF,IAAA,CAAKC,MAAA,EAAQC,SAAS;IAE5B,KAAKI,UAAA,GAAaL,MAAA,CAAOK,UAAA;IAEzB,OAAO;EACR;AACH;AAIA,MAAMC,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAC7C,MAAMC,QAAA,GAA2B,mBAAID,OAAA,CAAS;AAE9C,MAAME,aAAA,CAAc;EAClB1B,YAAY2B,UAAA,GAAa,IAAI;IAC3B,MAAMC,KAAA,GAAQ;IAEd,IAAIC,MAAA,EAAQC,OAAA;IACZ,IAAIC,UAAA,EAAYC,WAAA;IAEhB,MAAMC,KAAA,GAAQ;MACZC,MAAA,EAAQ;QAAE7B,KAAA,EAAO;MAAI;MACrB8B,OAAA,EAAS,mBAAIC,OAAA,CAAS;IACvB;IAED,MAAMC,UAAA,GAAaV,UAAA,CAAW1B,OAAA,KAAY,SAAY0B,UAAA,CAAW1B,OAAA,GAAUC,QAAA,CAASC,aAAA,CAAc,KAAK;IAEvGkC,UAAA,CAAWhC,KAAA,CAAMiC,QAAA,GAAW;IAE5B,KAAKD,UAAA,GAAaA,UAAA;IAElB,MAAME,WAAA,GAAcrC,QAAA,CAASC,aAAA,CAAc,KAAK;IAChDoC,WAAA,CAAYlC,KAAA,CAAMmC,eAAA,GAAkB;IACpCD,WAAA,CAAYlC,KAAA,CAAME,aAAA,GAAgB;IAClC8B,UAAA,CAAWI,WAAA,CAAYF,WAAW;IAElC,MAAMG,aAAA,GAAgBxC,QAAA,CAASC,aAAA,CAAc,KAAK;IAElDuC,aAAA,CAAcrC,KAAA,CAAMsC,cAAA,GAAiB;IAErCJ,WAAA,CAAYE,WAAA,CAAYC,aAAa;IAErC,KAAKE,OAAA,GAAU,YAAY;MACzB,OAAO;QACLC,KAAA,EAAOhB,MAAA;QACPiB,MAAA,EAAQhB;MACT;IACF;IAED,KAAKiB,MAAA,GAAS,UAAUC,KAAA,EAAOd,MAAA,EAAQ;MACrC,MAAMe,GAAA,GAAMf,MAAA,CAAOgB,gBAAA,CAAiBC,QAAA,CAAS,CAAC,IAAInB,WAAA;MAElD,IAAIE,MAAA,CAAOkB,IAAA,IAAQlB,MAAA,CAAOkB,IAAA,CAAKC,OAAA,EAAS;QAEtCd,WAAA,CAAYlC,KAAA,CAAMiD,SAAA,GAAY,cAAc,CAACpB,MAAA,CAAOkB,IAAA,CAAKG,OAAA,IAAW1B,MAAA,GAASK,MAAA,CAAOkB,IAAA,CAAKP,KAAA,QACvF,CAACX,MAAA,CAAOkB,IAAA,CAAKI,OAAA,IAAW1B,OAAA,GAAUI,MAAA,CAAOkB,IAAA,CAAKN,MAAA;QAIhDP,WAAA,CAAYlC,KAAA,CAAMiD,SAAA,IAAa,UAAUpB,MAAA,CAAOkB,IAAA,CAAKK,SAAA,GAAYvB,MAAA,CAAOkB,IAAA,CAAKP,KAAA,KAC3EX,MAAA,CAAOkB,IAAA,CAAKM,UAAA,GAAaxB,MAAA,CAAOkB,IAAA,CAAKN,MAAA;MAE/C,OAAa;QACLP,WAAA,CAAYlC,KAAA,CAAMiD,SAAA,GAAY;MAC/B;MAED,IAAIN,KAAA,CAAMW,qBAAA,KAA0B,MAAMX,KAAA,CAAMY,iBAAA,CAAmB;MACnE,IAAI1B,MAAA,CAAO2B,MAAA,KAAW,QAAQ3B,MAAA,CAAOyB,qBAAA,KAA0B,MAAMzB,MAAA,CAAO0B,iBAAA,CAAmB;MAE/F,IAAIE,EAAA,EAAIC,EAAA;MAER,IAAI7B,MAAA,CAAO8B,oBAAA,EAAsB;QAC/BF,EAAA,GAAK,EAAE5B,MAAA,CAAO+B,KAAA,GAAQ/B,MAAA,CAAOgC,IAAA,IAAQ;QACrCH,EAAA,IAAM7B,MAAA,CAAOiC,GAAA,GAAMjC,MAAA,CAAOkC,MAAA,IAAU;MACrC;MAED,MAAMC,iBAAA,GAAoBnC,MAAA,CAAOkB,IAAA,IAAQlB,MAAA,CAAOkB,IAAA,CAAKC,OAAA,GAAUnB,MAAA,CAAOkB,IAAA,CAAKN,MAAA,GAASZ,MAAA,CAAOkB,IAAA,CAAKM,UAAA,GAAa;MAC7G,MAAMY,eAAA,GAAkBpC,MAAA,CAAO8B,oBAAA,GAC3B,UAAUK,iBAAA,aAEVpB,GAAA,GACA,gBAEAsB,OAAA,CAAQT,EAAE,IACV,QACAS,OAAA,CAAQR,EAAE,IACV,QACAS,kBAAA,CAAmBtC,MAAA,CAAOuC,kBAAkB,IAC5C,UAAUJ,iBAAA,kBAAwCpB,GAAA,GAAM,QAAQuB,kBAAA,CAAmBtC,MAAA,CAAOuC,kBAAkB;MAChH,MAAMC,WAAA,GAAcxC,MAAA,CAAOyC,mBAAA,GAAsB,iBAAiB1B,GAAA,GAAM,SAAS;MAEjF,MAAM5C,KAAA,GAAQqE,WAAA,GAAcJ,eAAA,GAAkB,eAAevC,UAAA,GAAa,QAAQC,WAAA,GAAc;MAEhG,IAAIC,KAAA,CAAMC,MAAA,CAAO7B,KAAA,KAAUA,KAAA,EAAO;QAChCqC,aAAA,CAAcrC,KAAA,CAAMiD,SAAA,GAAYjD,KAAA;QAEhC4B,KAAA,CAAMC,MAAA,CAAO7B,KAAA,GAAQA,KAAA;MACtB;MAEDuE,YAAA,CAAa5B,KAAA,EAAOA,KAAA,EAAOd,MAAuB;IACnD;IAED,KAAK2C,OAAA,GAAU,UAAUhC,KAAA,EAAOC,MAAA,EAAQ;MACtCjB,MAAA,GAASgB,KAAA;MACTf,OAAA,GAAUgB,MAAA;MACVf,UAAA,GAAaF,MAAA,GAAS;MACtBG,WAAA,GAAcF,OAAA,GAAU;MAExBO,UAAA,CAAWhC,KAAA,CAAMwC,KAAA,GAAQA,KAAA,GAAQ;MACjCR,UAAA,CAAWhC,KAAA,CAAMyC,MAAA,GAASA,MAAA,GAAS;MAEnCP,WAAA,CAAYlC,KAAA,CAAMwC,KAAA,GAAQA,KAAA,GAAQ;MAClCN,WAAA,CAAYlC,KAAA,CAAMyC,MAAA,GAASA,MAAA,GAAS;MAEpCJ,aAAA,CAAcrC,KAAA,CAAMwC,KAAA,GAAQA,KAAA,GAAQ;MACpCH,aAAA,CAAcrC,KAAA,CAAMyC,MAAA,GAASA,MAAA,GAAS;IACvC;IAED,SAASyB,QAAQO,KAAA,EAAO;MACtB,OAAOC,IAAA,CAAKC,GAAA,CAAIF,KAAK,IAAI,QAAQ,IAAIA,KAAA;IACtC;IAED,SAASN,mBAAmBS,MAAA,EAAQ;MAClC,MAAM9B,QAAA,GAAW8B,MAAA,CAAO9B,QAAA;MAExB,OACE,cACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,EAAE,CAAC,IACrB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB;IAEH;IAED,SAAS+B,mBAAmBD,MAAA,EAAQ;MAClC,MAAM9B,QAAA,GAAW8B,MAAA,CAAO9B,QAAA;MACxB,MAAMgC,QAAA,GACJ,cACAZ,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQ,CAACpB,QAAA,CAAS,CAAC,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,CAAC,CAAC,IACnB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB,MACAoB,OAAA,CAAQpB,QAAA,CAAS,EAAE,CAAC,IACpB;MAEF,OAAO,yBAAyBgC,QAAA;IACjC;IAED,SAASP,aAAahE,MAAA,EAAQoC,KAAA,EAAOd,MAAA,EAAQoC,eAAA,EAAiB;MAC5D,IAAI1D,MAAA,CAAOR,aAAA,EAAe;QACxB,MAAMgF,OAAA,GAAUxE,MAAA,CAAOwE,OAAA,KAAY,QAAQxE,MAAA,CAAOyE,MAAA,CAAOC,IAAA,CAAKpD,MAAA,CAAOmD,MAAM,MAAM;QACjFzE,MAAA,CAAOX,OAAA,CAAQI,KAAA,CAAMkF,OAAA,GAAUH,OAAA,KAAY,OAAO,KAAK;QAEvD,IAAIA,OAAA,KAAY,MAAM;UACpBxE,MAAA,CAAO4E,cAAA,CAAe5D,KAAA,EAAOoB,KAAA,EAAOd,MAAM;UAE1C,IAAI7B,KAAA;UAEJ,IAAIO,MAAA,CAAOS,aAAA,EAAe;YAGxBE,OAAA,CAAQP,IAAA,CAAKkB,MAAA,CAAOuC,kBAAkB;YACtClD,OAAA,CAAQkE,SAAA,CAAW;YAEnB,IAAI7E,MAAA,CAAOU,UAAA,KAAe,GAAGC,OAAA,CAAQmE,QAAA,CAASjE,QAAA,CAASkE,aAAA,CAAc/E,MAAA,CAAOU,UAAU,CAAC;YAEvFV,MAAA,CAAOgF,WAAA,CAAYC,SAAA,CAAUpG,SAAA,EAAWE,WAAA,EAAaE,MAAM;YAC3D0B,OAAA,CAAQuE,WAAA,CAAYrG,SAAS;YAC7B8B,OAAA,CAAQwE,KAAA,CAAMlG,MAAM;YAEpB0B,OAAA,CAAQ4B,QAAA,CAAS,CAAC,IAAI;YACtB5B,OAAA,CAAQ4B,QAAA,CAAS,CAAC,IAAI;YACtB5B,OAAA,CAAQ4B,QAAA,CAAS,EAAE,IAAI;YACvB5B,OAAA,CAAQ4B,QAAA,CAAS,EAAE,IAAI;YAEvB9C,KAAA,GAAQ6E,kBAAA,CAAmB3D,OAAO;UAC9C,OAAiB;YACLlB,KAAA,GAAQ6E,kBAAA,CAAmBtE,MAAA,CAAOgF,WAAW;UAC9C;UAED,MAAM3F,OAAA,GAAUW,MAAA,CAAOX,OAAA;UACvB,MAAM+F,YAAA,GAAe/D,KAAA,CAAME,OAAA,CAAQ8D,GAAA,CAAIrF,MAAM;UAE7C,IAAIoF,YAAA,KAAiB,UAAaA,YAAA,CAAa3F,KAAA,KAAUA,KAAA,EAAO;YAC9DJ,OAAA,CAAQI,KAAA,CAAMiD,SAAA,GAAYjD,KAAA;YAE1B,MAAM6F,UAAA,GAAa;cAAE7F;YAAc;YACnC4B,KAAA,CAAME,OAAA,CAAQgE,GAAA,CAAIvF,MAAA,EAAQsF,UAAU;UACrC;UAED,IAAIjG,OAAA,CAAQa,UAAA,KAAe4B,aAAA,EAAe;YACxCA,aAAA,CAAcD,WAAA,CAAYxC,OAAO;UAClC;UAEDW,MAAA,CAAOwF,aAAA,CAAcxE,KAAA,EAAOoB,KAAA,EAAOd,MAAM;QAC1C;MACF;MAED,SAASmE,CAAA,GAAI,GAAGC,CAAA,GAAI1F,MAAA,CAAO2F,QAAA,CAASC,MAAA,EAAQH,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACtDzB,YAAA,CAAahE,MAAA,CAAO2F,QAAA,CAASF,CAAC,GAAGrD,KAAA,EAAOd,MAAuB;MAChE;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}