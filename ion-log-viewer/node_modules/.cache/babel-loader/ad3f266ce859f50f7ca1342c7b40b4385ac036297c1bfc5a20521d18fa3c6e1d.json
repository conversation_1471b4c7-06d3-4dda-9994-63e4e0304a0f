{"ast": null, "code": "import { CompressedTextureLoader } from \"three\";\nclass KT<PERSON><PERSON>oader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager);\n  }\n  parse(buffer, loadMipmaps) {\n    const ktx = new KhronosTextureContainer(buffer, 1);\n    return {\n      mipmaps: ktx.mipmaps(loadMipmaps),\n      width: ktx.pixelWidth,\n      height: ktx.pixelHeight,\n      format: ktx.glInternalFormat,\n      isCubemap: ktx.numberOfFaces === 6,\n      mipmapCount: ktx.numberOfMipmapLevels\n    };\n  }\n}\nconst HEADER_LEN = 12 + 13 * 4;\nconst COMPRESSED_2D = 0;\nclass KhronosTextureContainer {\n  /**\n   * @param {ArrayBuffer} arrayBuffer- contents of the KTX container file\n   * @param {number} facesExpected- should be either 1 or 6, based whether a cube texture or or\n   * @param {boolean} threeDExpected- provision for indicating that data should be a 3D texture, not implemented\n   * @param {boolean} textureArrayExpected- provision for indicating that data should be a texture array, not implemented\n   */\n  constructor(arrayBuffer, facesExpected) {\n    this.arrayBuffer = arrayBuffer;\n    const identifier = new Uint8Array(this.arrayBuffer, 0, 12);\n    if (identifier[0] !== 171 || identifier[1] !== 75 || identifier[2] !== 84 || identifier[3] !== 88 || identifier[4] !== 32 || identifier[5] !== 49 || identifier[6] !== 49 || identifier[7] !== 187 || identifier[8] !== 13 || identifier[9] !== 10 || identifier[10] !== 26 || identifier[11] !== 10) {\n      console.error(\"texture missing KTX identifier\");\n      return;\n    }\n    const dataSize = Uint32Array.BYTES_PER_ELEMENT;\n    const headerDataView = new DataView(this.arrayBuffer, 12, 13 * dataSize);\n    const endianness = headerDataView.getUint32(0, true);\n    const littleEndian = endianness === 67305985;\n    this.glType = headerDataView.getUint32(1 * dataSize, littleEndian);\n    this.glTypeSize = headerDataView.getUint32(2 * dataSize, littleEndian);\n    this.glFormat = headerDataView.getUint32(3 * dataSize, littleEndian);\n    this.glInternalFormat = headerDataView.getUint32(4 * dataSize, littleEndian);\n    this.glBaseInternalFormat = headerDataView.getUint32(5 * dataSize, littleEndian);\n    this.pixelWidth = headerDataView.getUint32(6 * dataSize, littleEndian);\n    this.pixelHeight = headerDataView.getUint32(7 * dataSize, littleEndian);\n    this.pixelDepth = headerDataView.getUint32(8 * dataSize, littleEndian);\n    this.numberOfArrayElements = headerDataView.getUint32(9 * dataSize, littleEndian);\n    this.numberOfFaces = headerDataView.getUint32(10 * dataSize, littleEndian);\n    this.numberOfMipmapLevels = headerDataView.getUint32(11 * dataSize, littleEndian);\n    this.bytesOfKeyValueData = headerDataView.getUint32(12 * dataSize, littleEndian);\n    if (this.glType !== 0) {\n      console.warn(\"only compressed formats currently supported\");\n      return;\n    } else {\n      this.numberOfMipmapLevels = Math.max(1, this.numberOfMipmapLevels);\n    }\n    if (this.pixelHeight === 0 || this.pixelDepth !== 0) {\n      console.warn(\"only 2D textures currently supported\");\n      return;\n    }\n    if (this.numberOfArrayElements !== 0) {\n      console.warn(\"texture arrays not currently supported\");\n      return;\n    }\n    if (this.numberOfFaces !== facesExpected) {\n      console.warn(\"number of faces expected\" + facesExpected + \", but found \" + this.numberOfFaces);\n      return;\n    }\n    this.loadType = COMPRESSED_2D;\n  }\n  mipmaps(loadMipmaps) {\n    const mipmaps = [];\n    let dataOffset = HEADER_LEN + this.bytesOfKeyValueData;\n    let width = this.pixelWidth;\n    let height = this.pixelHeight;\n    const mipmapCount = loadMipmaps ? this.numberOfMipmapLevels : 1;\n    for (let level = 0; level < mipmapCount; level++) {\n      const imageSize = new Int32Array(this.arrayBuffer, dataOffset, 1)[0];\n      dataOffset += 4;\n      for (let face = 0; face < this.numberOfFaces; face++) {\n        const byteArray = new Uint8Array(this.arrayBuffer, dataOffset, imageSize);\n        mipmaps.push({\n          data: byteArray,\n          width,\n          height\n        });\n        dataOffset += imageSize;\n        dataOffset += 3 - (imageSize + 3) % 4;\n      }\n      width = Math.max(1, width * 0.5);\n      height = Math.max(1, height * 0.5);\n    }\n    return mipmaps;\n  }\n}\nexport { KTXLoader };", "map": {"version": 3, "names": ["KTXLoader", "CompressedTextureLoader", "constructor", "manager", "parse", "buffer", "loadMipmaps", "ktx", "KhronosTextureContainer", "mipmaps", "width", "pixelWidth", "height", "pixelHeight", "format", "glInternalFormat", "isCubemap", "numberOfFaces", "mipmapCount", "numberOfMipmapLevels", "HEADER_LEN", "COMPRESSED_2D", "arrayBuffer", "facesExpected", "identifier", "Uint8Array", "console", "error", "dataSize", "Uint32Array", "BYTES_PER_ELEMENT", "headerDataView", "DataView", "endianness", "getUint32", "littleEndian", "glType", "glTypeSize", "glFormat", "glBaseInternalFormat", "pixelDepth", "numberOfArrayElements", "bytesOfKeyValueData", "warn", "Math", "max", "loadType", "dataOffset", "level", "imageSize", "Int32Array", "face", "byteArray", "push", "data"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/KTXLoader.js"], "sourcesContent": ["import { CompressedTextureLoader } from 'three'\n\n/**\n * for description see https://www.khronos.org/opengles/sdk/tools/KTX/\n * for file layout see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/\n *\n * ported from https://github.com/BabylonJS/Babylon.js/blob/master/src/Tools/babylon.khronosTextureContainer.ts\n */\n\nclass KTXLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer, loadMipmaps) {\n    const ktx = new KhronosTextureContainer(buffer, 1)\n\n    return {\n      mipmaps: ktx.mipmaps(loadMipmaps),\n      width: ktx.pixelWidth,\n      height: ktx.pixelHeight,\n      format: ktx.glInternalFormat,\n      isCubemap: ktx.numberOfFaces === 6,\n      mipmapCount: ktx.numberOfMipmapLevels,\n    }\n  }\n}\n\nconst HEADER_LEN = 12 + 13 * 4 // identifier + header elements (not including key value meta-data pairs)\n// load types\nconst COMPRESSED_2D = 0 // uses a gl.compressedTexImage2D()\n//const COMPRESSED_3D = 1; // uses a gl.compressedTexImage3D()\n//const TEX_2D = 2; // uses a gl.texImage2D()\n//const TEX_3D = 3; // uses a gl.texImage3D()\n\nclass KhronosTextureContainer {\n  /**\n   * @param {ArrayBuffer} arrayBuffer- contents of the KTX container file\n   * @param {number} facesExpected- should be either 1 or 6, based whether a cube texture or or\n   * @param {boolean} threeDExpected- provision for indicating that data should be a 3D texture, not implemented\n   * @param {boolean} textureArrayExpected- provision for indicating that data should be a texture array, not implemented\n   */\n  constructor(arrayBuffer, facesExpected /*, threeDExpected, textureArrayExpected */) {\n    this.arrayBuffer = arrayBuffer\n\n    // Test that it is a ktx formatted file, based on the first 12 bytes, character representation is:\n    // '´', 'K', 'T', 'X', ' ', '1', '1', 'ª', '\\r', '\\n', '\\x1A', '\\n'\n    // 0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A\n    const identifier = new Uint8Array(this.arrayBuffer, 0, 12)\n    if (\n      identifier[0] !== 0xab ||\n      identifier[1] !== 0x4b ||\n      identifier[2] !== 0x54 ||\n      identifier[3] !== 0x58 ||\n      identifier[4] !== 0x20 ||\n      identifier[5] !== 0x31 ||\n      identifier[6] !== 0x31 ||\n      identifier[7] !== 0xbb ||\n      identifier[8] !== 0x0d ||\n      identifier[9] !== 0x0a ||\n      identifier[10] !== 0x1a ||\n      identifier[11] !== 0x0a\n    ) {\n      console.error('texture missing KTX identifier')\n      return\n    }\n\n    // load the reset of the header in native 32 bit uint\n    const dataSize = Uint32Array.BYTES_PER_ELEMENT\n    const headerDataView = new DataView(this.arrayBuffer, 12, 13 * dataSize)\n    const endianness = headerDataView.getUint32(0, true)\n    const littleEndian = endianness === 0x04030201\n\n    this.glType = headerDataView.getUint32(1 * dataSize, littleEndian) // must be 0 for compressed textures\n    this.glTypeSize = headerDataView.getUint32(2 * dataSize, littleEndian) // must be 1 for compressed textures\n    this.glFormat = headerDataView.getUint32(3 * dataSize, littleEndian) // must be 0 for compressed textures\n    this.glInternalFormat = headerDataView.getUint32(4 * dataSize, littleEndian) // the value of arg passed to gl.compressedTexImage2D(,,x,,,,)\n    this.glBaseInternalFormat = headerDataView.getUint32(5 * dataSize, littleEndian) // specify GL_RGB, GL_RGBA, GL_ALPHA, etc (un-compressed only)\n    this.pixelWidth = headerDataView.getUint32(6 * dataSize, littleEndian) // level 0 value of arg passed to gl.compressedTexImage2D(,,,x,,,)\n    this.pixelHeight = headerDataView.getUint32(7 * dataSize, littleEndian) // level 0 value of arg passed to gl.compressedTexImage2D(,,,,x,,)\n    this.pixelDepth = headerDataView.getUint32(8 * dataSize, littleEndian) // level 0 value of arg passed to gl.compressedTexImage3D(,,,,,x,,)\n    this.numberOfArrayElements = headerDataView.getUint32(9 * dataSize, littleEndian) // used for texture arrays\n    this.numberOfFaces = headerDataView.getUint32(10 * dataSize, littleEndian) // used for cubemap textures, should either be 1 or 6\n    this.numberOfMipmapLevels = headerDataView.getUint32(11 * dataSize, littleEndian) // number of levels; disregard possibility of 0 for compressed textures\n    this.bytesOfKeyValueData = headerDataView.getUint32(12 * dataSize, littleEndian) // the amount of space after the header for meta-data\n\n    // Make sure we have a compressed type.  Not only reduces work, but probably better to let dev know they are not compressing.\n    if (this.glType !== 0) {\n      console.warn('only compressed formats currently supported')\n      return\n    } else {\n      // value of zero is an indication to generate mipmaps @ runtime.  Not usually allowed for compressed, so disregard.\n      this.numberOfMipmapLevels = Math.max(1, this.numberOfMipmapLevels)\n    }\n\n    if (this.pixelHeight === 0 || this.pixelDepth !== 0) {\n      console.warn('only 2D textures currently supported')\n      return\n    }\n\n    if (this.numberOfArrayElements !== 0) {\n      console.warn('texture arrays not currently supported')\n      return\n    }\n\n    if (this.numberOfFaces !== facesExpected) {\n      console.warn('number of faces expected' + facesExpected + ', but found ' + this.numberOfFaces)\n      return\n    }\n\n    // we now have a completely validated file, so could use existence of loadType as success\n    // would need to make this more elaborate & adjust checks above to support more than one load type\n    this.loadType = COMPRESSED_2D\n  }\n\n  mipmaps(loadMipmaps) {\n    const mipmaps = []\n\n    // initialize width & height for level 1\n    let dataOffset = HEADER_LEN + this.bytesOfKeyValueData\n    let width = this.pixelWidth\n    let height = this.pixelHeight\n    const mipmapCount = loadMipmaps ? this.numberOfMipmapLevels : 1\n\n    for (let level = 0; level < mipmapCount; level++) {\n      const imageSize = new Int32Array(this.arrayBuffer, dataOffset, 1)[0] // size per face, since not supporting array cubemaps\n      dataOffset += 4 // size of the image + 4 for the imageSize field\n\n      for (let face = 0; face < this.numberOfFaces; face++) {\n        const byteArray = new Uint8Array(this.arrayBuffer, dataOffset, imageSize)\n\n        mipmaps.push({ data: byteArray, width: width, height: height })\n\n        dataOffset += imageSize\n        dataOffset += 3 - ((imageSize + 3) % 4) // add padding for odd sized image\n      }\n\n      width = Math.max(1.0, width * 0.5)\n      height = Math.max(1.0, height * 0.5)\n    }\n\n    return mipmaps\n  }\n}\n\nexport { KTXLoader }\n"], "mappings": ";AASA,MAAMA,SAAA,SAAkBC,uBAAA,CAAwB;EAC9CC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,MAAMC,MAAA,EAAQC,WAAA,EAAa;IACzB,MAAMC,GAAA,GAAM,IAAIC,uBAAA,CAAwBH,MAAA,EAAQ,CAAC;IAEjD,OAAO;MACLI,OAAA,EAASF,GAAA,CAAIE,OAAA,CAAQH,WAAW;MAChCI,KAAA,EAAOH,GAAA,CAAII,UAAA;MACXC,MAAA,EAAQL,GAAA,CAAIM,WAAA;MACZC,MAAA,EAAQP,GAAA,CAAIQ,gBAAA;MACZC,SAAA,EAAWT,GAAA,CAAIU,aAAA,KAAkB;MACjCC,WAAA,EAAaX,GAAA,CAAIY;IAClB;EACF;AACH;AAEA,MAAMC,UAAA,GAAa,KAAK,KAAK;AAE7B,MAAMC,aAAA,GAAgB;AAKtB,MAAMb,uBAAA,CAAwB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAO5BN,YAAYoB,WAAA,EAAaC,aAAA,EAA2D;IAClF,KAAKD,WAAA,GAAcA,WAAA;IAKnB,MAAME,UAAA,GAAa,IAAIC,UAAA,CAAW,KAAKH,WAAA,EAAa,GAAG,EAAE;IACzD,IACEE,UAAA,CAAW,CAAC,MAAM,OAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,OAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,CAAC,MAAM,MAClBA,UAAA,CAAW,EAAE,MAAM,MACnBA,UAAA,CAAW,EAAE,MAAM,IACnB;MACAE,OAAA,CAAQC,KAAA,CAAM,gCAAgC;MAC9C;IACD;IAGD,MAAMC,QAAA,GAAWC,WAAA,CAAYC,iBAAA;IAC7B,MAAMC,cAAA,GAAiB,IAAIC,QAAA,CAAS,KAAKV,WAAA,EAAa,IAAI,KAAKM,QAAQ;IACvE,MAAMK,UAAA,GAAaF,cAAA,CAAeG,SAAA,CAAU,GAAG,IAAI;IACnD,MAAMC,YAAA,GAAeF,UAAA,KAAe;IAEpC,KAAKG,MAAA,GAASL,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACjE,KAAKE,UAAA,GAAaN,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACrE,KAAKG,QAAA,GAAWP,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACnE,KAAKpB,gBAAA,GAAmBgB,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IAC3E,KAAKI,oBAAA,GAAuBR,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IAC/E,KAAKxB,UAAA,GAAaoB,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACrE,KAAKtB,WAAA,GAAckB,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACtE,KAAKK,UAAA,GAAaT,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IACrE,KAAKM,qBAAA,GAAwBV,cAAA,CAAeG,SAAA,CAAU,IAAIN,QAAA,EAAUO,YAAY;IAChF,KAAKlB,aAAA,GAAgBc,cAAA,CAAeG,SAAA,CAAU,KAAKN,QAAA,EAAUO,YAAY;IACzE,KAAKhB,oBAAA,GAAuBY,cAAA,CAAeG,SAAA,CAAU,KAAKN,QAAA,EAAUO,YAAY;IAChF,KAAKO,mBAAA,GAAsBX,cAAA,CAAeG,SAAA,CAAU,KAAKN,QAAA,EAAUO,YAAY;IAG/E,IAAI,KAAKC,MAAA,KAAW,GAAG;MACrBV,OAAA,CAAQiB,IAAA,CAAK,6CAA6C;MAC1D;IACN,OAAW;MAEL,KAAKxB,oBAAA,GAAuByB,IAAA,CAAKC,GAAA,CAAI,GAAG,KAAK1B,oBAAoB;IAClE;IAED,IAAI,KAAKN,WAAA,KAAgB,KAAK,KAAK2B,UAAA,KAAe,GAAG;MACnDd,OAAA,CAAQiB,IAAA,CAAK,sCAAsC;MACnD;IACD;IAED,IAAI,KAAKF,qBAAA,KAA0B,GAAG;MACpCf,OAAA,CAAQiB,IAAA,CAAK,wCAAwC;MACrD;IACD;IAED,IAAI,KAAK1B,aAAA,KAAkBM,aAAA,EAAe;MACxCG,OAAA,CAAQiB,IAAA,CAAK,6BAA6BpB,aAAA,GAAgB,iBAAiB,KAAKN,aAAa;MAC7F;IACD;IAID,KAAK6B,QAAA,GAAWzB,aAAA;EACjB;EAEDZ,QAAQH,WAAA,EAAa;IACnB,MAAMG,OAAA,GAAU,EAAE;IAGlB,IAAIsC,UAAA,GAAa3B,UAAA,GAAa,KAAKsB,mBAAA;IACnC,IAAIhC,KAAA,GAAQ,KAAKC,UAAA;IACjB,IAAIC,MAAA,GAAS,KAAKC,WAAA;IAClB,MAAMK,WAAA,GAAcZ,WAAA,GAAc,KAAKa,oBAAA,GAAuB;IAE9D,SAAS6B,KAAA,GAAQ,GAAGA,KAAA,GAAQ9B,WAAA,EAAa8B,KAAA,IAAS;MAChD,MAAMC,SAAA,GAAY,IAAIC,UAAA,CAAW,KAAK5B,WAAA,EAAayB,UAAA,EAAY,CAAC,EAAE,CAAC;MACnEA,UAAA,IAAc;MAEd,SAASI,IAAA,GAAO,GAAGA,IAAA,GAAO,KAAKlC,aAAA,EAAekC,IAAA,IAAQ;QACpD,MAAMC,SAAA,GAAY,IAAI3B,UAAA,CAAW,KAAKH,WAAA,EAAayB,UAAA,EAAYE,SAAS;QAExExC,OAAA,CAAQ4C,IAAA,CAAK;UAAEC,IAAA,EAAMF,SAAA;UAAW1C,KAAA;UAAcE;QAAA,CAAgB;QAE9DmC,UAAA,IAAcE,SAAA;QACdF,UAAA,IAAc,KAAME,SAAA,GAAY,KAAK;MACtC;MAEDvC,KAAA,GAAQkC,IAAA,CAAKC,GAAA,CAAI,GAAKnC,KAAA,GAAQ,GAAG;MACjCE,MAAA,GAASgC,IAAA,CAAKC,GAAA,CAAI,GAAKjC,MAAA,GAAS,GAAG;IACpC;IAED,OAAOH,OAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}