{"ast": null, "code": "import * as THREE from \"three\";\nfunction RoomEnvironment() {\n  const scene = new THREE.Scene();\n  const geometry = new THREE.BoxGeometry();\n  geometry.deleteAttribute(\"uv\");\n  const roomMaterial = new THREE.MeshStandardMaterial({\n    side: THREE.BackSide\n  });\n  const boxMaterial = new THREE.MeshStandardMaterial();\n  const mainLight = new THREE.PointLight(16777215, 5, 28, 2);\n  mainLight.position.set(0.418, 16.199, 0.3);\n  scene.add(mainLight);\n  const room = new THREE.Mesh(geometry, roomMaterial);\n  room.position.set(-0.757, 13.219, 0.717);\n  room.scale.set(31.713, 28.305, 28.591);\n  scene.add(room);\n  const box1 = new THREE.Mesh(geometry, boxMaterial);\n  box1.position.set(-10.906, 2.009, 1.846);\n  box1.rotation.set(0, -0.195, 0);\n  box1.scale.set(2.328, 7.905, 4.651);\n  scene.add(box1);\n  const box2 = new THREE.Mesh(geometry, boxMaterial);\n  box2.position.set(-5.607, -0.754, -0.758);\n  box2.rotation.set(0, 0.994, 0);\n  box2.scale.set(1.97, 1.534, 3.955);\n  scene.add(box2);\n  const box3 = new THREE.Mesh(geometry, boxMaterial);\n  box3.position.set(6.167, 0.857, 7.803);\n  box3.rotation.set(0, 0.561, 0);\n  box3.scale.set(3.927, 6.285, 3.687);\n  scene.add(box3);\n  const box4 = new THREE.Mesh(geometry, boxMaterial);\n  box4.position.set(-2.017, 0.018, 6.124);\n  box4.rotation.set(0, 0.333, 0);\n  box4.scale.set(2.002, 4.566, 2.064);\n  scene.add(box4);\n  const box5 = new THREE.Mesh(geometry, boxMaterial);\n  box5.position.set(2.291, -0.756, -2.621);\n  box5.rotation.set(0, -0.286, 0);\n  box5.scale.set(1.546, 1.552, 1.496);\n  scene.add(box5);\n  const box6 = new THREE.Mesh(geometry, boxMaterial);\n  box6.position.set(-2.193, -0.369, -5.547);\n  box6.rotation.set(0, 0.516, 0);\n  box6.scale.set(3.875, 3.487, 2.986);\n  scene.add(box6);\n  const light1 = new THREE.Mesh(geometry, createAreaLightMaterial(50));\n  light1.position.set(-16.116, 14.37, 8.208);\n  light1.scale.set(0.1, 2.428, 2.739);\n  scene.add(light1);\n  const light2 = new THREE.Mesh(geometry, createAreaLightMaterial(50));\n  light2.position.set(-16.109, 18.021, -8.207);\n  light2.scale.set(0.1, 2.425, 2.751);\n  scene.add(light2);\n  const light3 = new THREE.Mesh(geometry, createAreaLightMaterial(17));\n  light3.position.set(14.904, 12.198, -1.832);\n  light3.scale.set(0.15, 4.265, 6.331);\n  scene.add(light3);\n  const light4 = new THREE.Mesh(geometry, createAreaLightMaterial(43));\n  light4.position.set(-0.462, 8.89, 14.52);\n  light4.scale.set(4.38, 5.441, 0.088);\n  scene.add(light4);\n  const light5 = new THREE.Mesh(geometry, createAreaLightMaterial(20));\n  light5.position.set(3.235, 11.486, -12.541);\n  light5.scale.set(2.5, 2, 0.1);\n  scene.add(light5);\n  const light6 = new THREE.Mesh(geometry, createAreaLightMaterial(100));\n  light6.position.set(0, 20, 0);\n  light6.scale.set(1, 0.1, 1);\n  scene.add(light6);\n  function createAreaLightMaterial(intensity) {\n    const material = new THREE.MeshBasicMaterial();\n    material.color.setScalar(intensity);\n    return material;\n  }\n  return scene;\n}\nexport { RoomEnvironment };", "map": {"version": 3, "names": ["RoomEnvironment", "scene", "THREE", "Scene", "geometry", "BoxGeometry", "deleteAttribute", "roomMaterial", "MeshStandardMaterial", "side", "BackSide", "boxMaterial", "mainLight", "PointLight", "position", "set", "add", "room", "<PERSON><PERSON>", "scale", "box1", "rotation", "box2", "box3", "box4", "box5", "box6", "light1", "createAreaLightMaterial", "light2", "light3", "light4", "light5", "light6", "intensity", "material", "MeshBasicMaterial", "color", "setScalar"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/environments/RoomEnvironment.ts"], "sourcesContent": ["/**\n * https://github.com/google/model-viewer/blob/master/packages/model-viewer/src/three-components/EnvironmentScene.ts\n */\n\nimport * as THREE from 'three'\n\nfunction RoomEnvironment() {\n  const scene = new THREE.Scene()\n\n  const geometry = new THREE.BoxGeometry()\n  geometry.deleteAttribute('uv')\n\n  const roomMaterial = new THREE.MeshStandardMaterial({ side: THREE.BackSide })\n  const boxMaterial = new THREE.MeshStandardMaterial()\n\n  const mainLight = new THREE.PointLight(0xffffff, 5.0, 28, 2)\n  mainLight.position.set(0.418, 16.199, 0.3)\n  scene.add(mainLight)\n\n  const room = new THREE.Mesh(geometry, roomMaterial)\n  room.position.set(-0.757, 13.219, 0.717)\n  room.scale.set(31.713, 28.305, 28.591)\n  scene.add(room)\n\n  const box1 = new THREE.Mesh(geometry, boxMaterial)\n  box1.position.set(-10.906, 2.009, 1.846)\n  box1.rotation.set(0, -0.195, 0)\n  box1.scale.set(2.328, 7.905, 4.651)\n  scene.add(box1)\n\n  const box2 = new THREE.Mesh(geometry, boxMaterial)\n  box2.position.set(-5.607, -0.754, -0.758)\n  box2.rotation.set(0, 0.994, 0)\n  box2.scale.set(1.97, 1.534, 3.955)\n  scene.add(box2)\n\n  const box3 = new THREE.Mesh(geometry, boxMaterial)\n  box3.position.set(6.167, 0.857, 7.803)\n  box3.rotation.set(0, 0.561, 0)\n  box3.scale.set(3.927, 6.285, 3.687)\n  scene.add(box3)\n\n  const box4 = new THREE.Mesh(geometry, boxMaterial)\n  box4.position.set(-2.017, 0.018, 6.124)\n  box4.rotation.set(0, 0.333, 0)\n  box4.scale.set(2.002, 4.566, 2.064)\n  scene.add(box4)\n\n  const box5 = new THREE.Mesh(geometry, boxMaterial)\n  box5.position.set(2.291, -0.756, -2.621)\n  box5.rotation.set(0, -0.286, 0)\n  box5.scale.set(1.546, 1.552, 1.496)\n  scene.add(box5)\n\n  const box6 = new THREE.Mesh(geometry, boxMaterial)\n  box6.position.set(-2.193, -0.369, -5.547)\n  box6.rotation.set(0, 0.516, 0)\n  box6.scale.set(3.875, 3.487, 2.986)\n  scene.add(box6)\n\n  // -x right\n  const light1 = new THREE.Mesh(geometry, createAreaLightMaterial(50))\n  light1.position.set(-16.116, 14.37, 8.208)\n  light1.scale.set(0.1, 2.428, 2.739)\n  scene.add(light1)\n\n  // -x left\n  const light2 = new THREE.Mesh(geometry, createAreaLightMaterial(50))\n  light2.position.set(-16.109, 18.021, -8.207)\n  light2.scale.set(0.1, 2.425, 2.751)\n  scene.add(light2)\n\n  // +x\n  const light3 = new THREE.Mesh(geometry, createAreaLightMaterial(17))\n  light3.position.set(14.904, 12.198, -1.832)\n  light3.scale.set(0.15, 4.265, 6.331)\n  scene.add(light3)\n\n  // +z\n  const light4 = new THREE.Mesh(geometry, createAreaLightMaterial(43))\n  light4.position.set(-0.462, 8.89, 14.52)\n  light4.scale.set(4.38, 5.441, 0.088)\n  scene.add(light4)\n\n  // -z\n  const light5 = new THREE.Mesh(geometry, createAreaLightMaterial(20))\n  light5.position.set(3.235, 11.486, -12.541)\n  light5.scale.set(2.5, 2.0, 0.1)\n  scene.add(light5)\n\n  // +y\n  const light6 = new THREE.Mesh(geometry, createAreaLightMaterial(100))\n  light6.position.set(0.0, 20.0, 0.0)\n  light6.scale.set(1.0, 0.1, 1.0)\n  scene.add(light6)\n\n  function createAreaLightMaterial(intensity: number) {\n    const material = new THREE.MeshBasicMaterial()\n    material.color.setScalar(intensity)\n    return material\n  }\n\n  return scene\n}\n\nexport { RoomEnvironment }\n"], "mappings": ";AAMA,SAASA,gBAAA,EAAkB;EACnB,MAAAC,KAAA,GAAQ,IAAIC,KAAA,CAAMC,KAAA;EAElB,MAAAC,QAAA,GAAW,IAAIF,KAAA,CAAMG,WAAA;EAC3BD,QAAA,CAASE,eAAA,CAAgB,IAAI;EAEvB,MAAAC,YAAA,GAAe,IAAIL,KAAA,CAAMM,oBAAA,CAAqB;IAAEC,IAAA,EAAMP,KAAA,CAAMQ;EAAA,CAAU;EACtE,MAAAC,WAAA,GAAc,IAAIT,KAAA,CAAMM,oBAAA;EAE9B,MAAMI,SAAA,GAAY,IAAIV,KAAA,CAAMW,UAAA,CAAW,UAAU,GAAK,IAAI,CAAC;EAC3DD,SAAA,CAAUE,QAAA,CAASC,GAAA,CAAI,OAAO,QAAQ,GAAG;EACzCd,KAAA,CAAMe,GAAA,CAAIJ,SAAS;EAEnB,MAAMK,IAAA,GAAO,IAAIf,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUG,YAAY;EAClDU,IAAA,CAAKH,QAAA,CAASC,GAAA,CAAI,QAAQ,QAAQ,KAAK;EACvCE,IAAA,CAAKE,KAAA,CAAMJ,GAAA,CAAI,QAAQ,QAAQ,MAAM;EACrCd,KAAA,CAAMe,GAAA,CAAIC,IAAI;EAEd,MAAMG,IAAA,GAAO,IAAIlB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDS,IAAA,CAAKN,QAAA,CAASC,GAAA,CAAI,SAAS,OAAO,KAAK;EACvCK,IAAA,CAAKC,QAAA,CAASN,GAAA,CAAI,GAAG,QAAQ,CAAC;EAC9BK,IAAA,CAAKD,KAAA,CAAMJ,GAAA,CAAI,OAAO,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAII,IAAI;EAEd,MAAME,IAAA,GAAO,IAAIpB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDW,IAAA,CAAKR,QAAA,CAASC,GAAA,CAAI,QAAQ,QAAQ,MAAM;EACxCO,IAAA,CAAKD,QAAA,CAASN,GAAA,CAAI,GAAG,OAAO,CAAC;EAC7BO,IAAA,CAAKH,KAAA,CAAMJ,GAAA,CAAI,MAAM,OAAO,KAAK;EACjCd,KAAA,CAAMe,GAAA,CAAIM,IAAI;EAEd,MAAMC,IAAA,GAAO,IAAIrB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDY,IAAA,CAAKT,QAAA,CAASC,GAAA,CAAI,OAAO,OAAO,KAAK;EACrCQ,IAAA,CAAKF,QAAA,CAASN,GAAA,CAAI,GAAG,OAAO,CAAC;EAC7BQ,IAAA,CAAKJ,KAAA,CAAMJ,GAAA,CAAI,OAAO,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIO,IAAI;EAEd,MAAMC,IAAA,GAAO,IAAItB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDa,IAAA,CAAKV,QAAA,CAASC,GAAA,CAAI,QAAQ,OAAO,KAAK;EACtCS,IAAA,CAAKH,QAAA,CAASN,GAAA,CAAI,GAAG,OAAO,CAAC;EAC7BS,IAAA,CAAKL,KAAA,CAAMJ,GAAA,CAAI,OAAO,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIQ,IAAI;EAEd,MAAMC,IAAA,GAAO,IAAIvB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDc,IAAA,CAAKX,QAAA,CAASC,GAAA,CAAI,OAAO,QAAQ,MAAM;EACvCU,IAAA,CAAKJ,QAAA,CAASN,GAAA,CAAI,GAAG,QAAQ,CAAC;EAC9BU,IAAA,CAAKN,KAAA,CAAMJ,GAAA,CAAI,OAAO,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIS,IAAI;EAEd,MAAMC,IAAA,GAAO,IAAIxB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUO,WAAW;EACjDe,IAAA,CAAKZ,QAAA,CAASC,GAAA,CAAI,QAAQ,QAAQ,MAAM;EACxCW,IAAA,CAAKL,QAAA,CAASN,GAAA,CAAI,GAAG,OAAO,CAAC;EAC7BW,IAAA,CAAKP,KAAA,CAAMJ,GAAA,CAAI,OAAO,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIU,IAAI;EAGd,MAAMC,MAAA,GAAS,IAAIzB,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,EAAE,CAAC;EACnED,MAAA,CAAOb,QAAA,CAASC,GAAA,CAAI,SAAS,OAAO,KAAK;EACzCY,MAAA,CAAOR,KAAA,CAAMJ,GAAA,CAAI,KAAK,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIW,MAAM;EAGhB,MAAME,MAAA,GAAS,IAAI3B,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,EAAE,CAAC;EACnEC,MAAA,CAAOf,QAAA,CAASC,GAAA,CAAI,SAAS,QAAQ,MAAM;EAC3Cc,MAAA,CAAOV,KAAA,CAAMJ,GAAA,CAAI,KAAK,OAAO,KAAK;EAClCd,KAAA,CAAMe,GAAA,CAAIa,MAAM;EAGhB,MAAMC,MAAA,GAAS,IAAI5B,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,EAAE,CAAC;EACnEE,MAAA,CAAOhB,QAAA,CAASC,GAAA,CAAI,QAAQ,QAAQ,MAAM;EAC1Ce,MAAA,CAAOX,KAAA,CAAMJ,GAAA,CAAI,MAAM,OAAO,KAAK;EACnCd,KAAA,CAAMe,GAAA,CAAIc,MAAM;EAGhB,MAAMC,MAAA,GAAS,IAAI7B,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,EAAE,CAAC;EACnEG,MAAA,CAAOjB,QAAA,CAASC,GAAA,CAAI,QAAQ,MAAM,KAAK;EACvCgB,MAAA,CAAOZ,KAAA,CAAMJ,GAAA,CAAI,MAAM,OAAO,KAAK;EACnCd,KAAA,CAAMe,GAAA,CAAIe,MAAM;EAGhB,MAAMC,MAAA,GAAS,IAAI9B,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,EAAE,CAAC;EACnEI,MAAA,CAAOlB,QAAA,CAASC,GAAA,CAAI,OAAO,QAAQ,OAAO;EAC1CiB,MAAA,CAAOb,KAAA,CAAMJ,GAAA,CAAI,KAAK,GAAK,GAAG;EAC9Bd,KAAA,CAAMe,GAAA,CAAIgB,MAAM;EAGhB,MAAMC,MAAA,GAAS,IAAI/B,KAAA,CAAMgB,IAAA,CAAKd,QAAA,EAAUwB,uBAAA,CAAwB,GAAG,CAAC;EACpEK,MAAA,CAAOnB,QAAA,CAASC,GAAA,CAAI,GAAK,IAAM,CAAG;EAClCkB,MAAA,CAAOd,KAAA,CAAMJ,GAAA,CAAI,GAAK,KAAK,CAAG;EAC9Bd,KAAA,CAAMe,GAAA,CAAIiB,MAAM;EAEhB,SAASL,wBAAwBM,SAAA,EAAmB;IAC5C,MAAAC,QAAA,GAAW,IAAIjC,KAAA,CAAMkC,iBAAA;IAClBD,QAAA,CAAAE,KAAA,CAAMC,SAAA,CAAUJ,SAAS;IAC3B,OAAAC,QAAA;EACT;EAEO,OAAAlC,KAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}