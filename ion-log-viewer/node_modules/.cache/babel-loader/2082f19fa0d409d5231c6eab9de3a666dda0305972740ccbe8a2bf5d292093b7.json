{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, <PERSON>ufferGeometry, Float32BufferAttribute } from \"three\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass PLYLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.propertyNameMapping = {};\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  setPropertyNameMapping(mapping) {\n    this.propertyNameMapping = mapping;\n  }\n  parse(data) {\n    function parseHeader(data2) {\n      const patternHeader = /ply([\\s\\S]*)end_header\\r?\\n/;\n      let headerText = \"\";\n      let headerLength = 0;\n      const result = patternHeader.exec(data2);\n      if (result !== null) {\n        headerText = result[1];\n        headerLength = new Blob([result[0]]).size;\n      }\n      const header = {\n        comments: [],\n        elements: [],\n        headerLength,\n        objInfo: \"\"\n      };\n      const lines = headerText.split(\"\\n\");\n      let currentElement;\n      function make_ply_element_property(propertValues, propertyNameMapping) {\n        const property = {\n          type: propertValues[0]\n        };\n        if (property.type === \"list\") {\n          property.name = propertValues[3];\n          property.countType = propertValues[1];\n          property.itemType = propertValues[2];\n        } else {\n          property.name = propertValues[1];\n        }\n        if (property.name in propertyNameMapping) {\n          property.name = propertyNameMapping[property.name];\n        }\n        return property;\n      }\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i];\n        line = line.trim();\n        if (line === \"\") continue;\n        const lineValues = line.split(/\\s+/);\n        const lineType = lineValues.shift();\n        line = lineValues.join(\" \");\n        switch (lineType) {\n          case \"format\":\n            header.format = lineValues[0];\n            header.version = lineValues[1];\n            break;\n          case \"comment\":\n            header.comments.push(line);\n            break;\n          case \"element\":\n            if (currentElement !== void 0) {\n              header.elements.push(currentElement);\n            }\n            currentElement = {};\n            currentElement.name = lineValues[0];\n            currentElement.count = parseInt(lineValues[1]);\n            currentElement.properties = [];\n            break;\n          case \"property\":\n            currentElement.properties.push(make_ply_element_property(lineValues, scope.propertyNameMapping));\n            break;\n          case \"obj_info\":\n            header.objInfo = line;\n            break;\n          default:\n            console.log(\"unhandled\", lineType, lineValues);\n        }\n      }\n      if (currentElement !== void 0) {\n        header.elements.push(currentElement);\n      }\n      return header;\n    }\n    function parseASCIINumber(n, type) {\n      switch (type) {\n        case \"char\":\n        case \"uchar\":\n        case \"short\":\n        case \"ushort\":\n        case \"int\":\n        case \"uint\":\n        case \"int8\":\n        case \"uint8\":\n        case \"int16\":\n        case \"uint16\":\n        case \"int32\":\n        case \"uint32\":\n          return parseInt(n);\n        case \"float\":\n        case \"double\":\n        case \"float32\":\n        case \"float64\":\n          return parseFloat(n);\n      }\n    }\n    function parseASCIIElement(properties, line) {\n      const values = line.split(/\\s+/);\n      const element = {};\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === \"list\") {\n          const list = [];\n          const n = parseASCIINumber(values.shift(), properties[i].countType);\n          for (let j = 0; j < n; j++) {\n            list.push(parseASCIINumber(values.shift(), properties[i].itemType));\n          }\n          element[properties[i].name] = list;\n        } else {\n          element[properties[i].name] = parseASCIINumber(values.shift(), properties[i].type);\n        }\n      }\n      return element;\n    }\n    function parseASCII(data2, header) {\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: []\n      };\n      let result;\n      const patternBody = /end_header\\s([\\s\\S]*)$/;\n      let body = \"\";\n      if ((result = patternBody.exec(data2)) !== null) {\n        body = result[1];\n      }\n      const lines = body.split(\"\\n\");\n      let currentElement = 0;\n      let currentElementCount = 0;\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i];\n        line = line.trim();\n        if (line === \"\") {\n          continue;\n        }\n        if (currentElementCount >= header.elements[currentElement].count) {\n          currentElement++;\n          currentElementCount = 0;\n        }\n        const element = parseASCIIElement(header.elements[currentElement].properties, line);\n        handleElement(buffer, header.elements[currentElement].name, element);\n        currentElementCount++;\n      }\n      return postProcess(buffer);\n    }\n    function postProcess(buffer) {\n      let geometry2 = new BufferGeometry();\n      if (buffer.indices.length > 0) {\n        geometry2.setIndex(buffer.indices);\n      }\n      geometry2.setAttribute(\"position\", new Float32BufferAttribute(buffer.vertices, 3));\n      if (buffer.normals.length > 0) {\n        geometry2.setAttribute(\"normal\", new Float32BufferAttribute(buffer.normals, 3));\n      }\n      if (buffer.uvs.length > 0) {\n        geometry2.setAttribute(\"uv\", new Float32BufferAttribute(buffer.uvs, 2));\n      }\n      if (buffer.colors.length > 0) {\n        geometry2.setAttribute(\"color\", new Float32BufferAttribute(buffer.colors, 3));\n      }\n      if (buffer.faceVertexUvs.length > 0) {\n        geometry2 = geometry2.toNonIndexed();\n        geometry2.setAttribute(\"uv\", new Float32BufferAttribute(buffer.faceVertexUvs, 2));\n      }\n      geometry2.computeBoundingSphere();\n      return geometry2;\n    }\n    function handleElement(buffer, elementName, element) {\n      if (elementName === \"vertex\") {\n        buffer.vertices.push(element.x, element.y, element.z);\n        if (\"nx\" in element && \"ny\" in element && \"nz\" in element) {\n          buffer.normals.push(element.nx, element.ny, element.nz);\n        }\n        if (\"s\" in element && \"t\" in element) {\n          buffer.uvs.push(element.s, element.t);\n        }\n        if (\"red\" in element && \"green\" in element && \"blue\" in element) {\n          buffer.colors.push(element.red / 255, element.green / 255, element.blue / 255);\n        }\n      } else if (elementName === \"face\") {\n        const vertex_indices = element.vertex_indices || element.vertex_index;\n        const texcoord = element.texcoord;\n        if (vertex_indices.length === 3) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[2]);\n          if (texcoord && texcoord.length === 6) {\n            buffer.faceVertexUvs.push(texcoord[0], texcoord[1]);\n            buffer.faceVertexUvs.push(texcoord[2], texcoord[3]);\n            buffer.faceVertexUvs.push(texcoord[4], texcoord[5]);\n          }\n        } else if (vertex_indices.length === 4) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[3]);\n          buffer.indices.push(vertex_indices[1], vertex_indices[2], vertex_indices[3]);\n        }\n      }\n    }\n    function binaryRead(dataview, at, type, little_endian) {\n      switch (type) {\n        case \"int8\":\n        case \"char\":\n          return [dataview.getInt8(at), 1];\n        case \"uint8\":\n        case \"uchar\":\n          return [dataview.getUint8(at), 1];\n        case \"int16\":\n        case \"short\":\n          return [dataview.getInt16(at, little_endian), 2];\n        case \"uint16\":\n        case \"ushort\":\n          return [dataview.getUint16(at, little_endian), 2];\n        case \"int32\":\n        case \"int\":\n          return [dataview.getInt32(at, little_endian), 4];\n        case \"uint32\":\n        case \"uint\":\n          return [dataview.getUint32(at, little_endian), 4];\n        case \"float32\":\n        case \"float\":\n          return [dataview.getFloat32(at, little_endian), 4];\n        case \"float64\":\n        case \"double\":\n          return [dataview.getFloat64(at, little_endian), 8];\n      }\n    }\n    function binaryReadElement(dataview, at, properties, little_endian) {\n      const element = {};\n      let result,\n        read = 0;\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === \"list\") {\n          const list = [];\n          result = binaryRead(dataview, at + read, properties[i].countType, little_endian);\n          const n = result[0];\n          read += result[1];\n          for (let j = 0; j < n; j++) {\n            result = binaryRead(dataview, at + read, properties[i].itemType, little_endian);\n            list.push(result[0]);\n            read += result[1];\n          }\n          element[properties[i].name] = list;\n        } else {\n          result = binaryRead(dataview, at + read, properties[i].type, little_endian);\n          element[properties[i].name] = result[0];\n          read += result[1];\n        }\n      }\n      return [element, read];\n    }\n    function parseBinary(data2, header) {\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: []\n      };\n      const little_endian = header.format === \"binary_little_endian\";\n      const body = new DataView(data2, header.headerLength);\n      let result,\n        loc = 0;\n      for (let currentElement = 0; currentElement < header.elements.length; currentElement++) {\n        for (let currentElementCount = 0; currentElementCount < header.elements[currentElement].count; currentElementCount++) {\n          result = binaryReadElement(body, loc, header.elements[currentElement].properties, little_endian);\n          loc += result[1];\n          const element = result[0];\n          handleElement(buffer, header.elements[currentElement].name, element);\n        }\n      }\n      return postProcess(buffer);\n    }\n    let geometry;\n    const scope = this;\n    if (data instanceof ArrayBuffer) {\n      const text = decodeText(new Uint8Array(data));\n      const header = parseHeader(text);\n      geometry = header.format === \"ascii\" ? parseASCII(text, header) : parseBinary(data, header);\n    } else {\n      geometry = parseASCII(data, parseHeader(data));\n    }\n    return geometry;\n  }\n}\nexport { PLYLoader };", "map": {"version": 3, "names": ["PLYLoader", "Loader", "constructor", "manager", "propertyNameMapping", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "setPropertyNameMapping", "mapping", "data", "parse<PERSON><PERSON><PERSON>", "data2", "pattern<PERSON>eader", "headerText", "<PERSON><PERSON><PERSON><PERSON>", "result", "exec", "Blob", "size", "header", "comments", "elements", "objInfo", "lines", "split", "currentElement", "make_ply_element_property", "propertValues", "property", "type", "name", "countType", "itemType", "i", "length", "line", "trim", "lineValues", "lineType", "shift", "join", "format", "version", "push", "count", "parseInt", "properties", "log", "parseASCIINumber", "n", "parseFloat", "parseASCIIElement", "values", "element", "list", "j", "parseASCII", "buffer", "indices", "vertices", "normals", "uvs", "faceVertexUvs", "colors", "patternBody", "body", "currentElementCount", "handleElement", "postProcess", "geometry2", "BufferGeometry", "setIndex", "setAttribute", "Float32BufferAttribute", "toNonIndexed", "computeBoundingSphere", "elementName", "x", "y", "z", "nx", "ny", "nz", "s", "t", "red", "green", "blue", "vertex_indices", "vertex_index", "texcoord", "binaryRead", "dataview", "at", "little_endian", "getInt8", "getUint8", "getInt16", "getUint16", "getInt32", "getUint32", "getFloat32", "getFloat64", "binaryReadElement", "read", "parseBinary", "DataView", "loc", "geometry", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decodeText", "Uint8Array"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/PLYLoader.js"], "sourcesContent": ["import { BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils } from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n * Description: A THREE loader for PLY ASCII files (known as the Polygon\n * File Format or the Stanford Triangle Format).\n *\n * Limitations: ASCII decoding assumes file is UTF-8.\n *\n * Usage:\n *\tconst loader = new PLYLoader();\n *\tloader.load('./models/ply/ascii/dolphins.ply', function (geometry) {\n *\n *\t\tscene.add( new THREE.Mesh( geometry ) );\n *\n *\t} );\n *\n * If the PLY file uses non standard property names, they can be mapped while\n * loading. For example, the following maps the properties\n * “diffuse_(red|green|blue)” in the file to standard color names.\n *\n * loader.setPropertyNameMapping( {\n *\tdiffuse_red: 'red',\n *\tdiffuse_green: 'green',\n *\tdiffuse_blue: 'blue'\n * } );\n *\n */\n\nclass PLYLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.propertyNameMapping = {}\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setPropertyNameMapping(mapping) {\n    this.propertyNameMapping = mapping\n  }\n\n  parse(data) {\n    function parseHeader(data) {\n      const patternHeader = /ply([\\s\\S]*)end_header\\r?\\n/\n      let headerText = ''\n      let headerLength = 0\n      const result = patternHeader.exec(data)\n\n      if (result !== null) {\n        headerText = result[1]\n        headerLength = new Blob([result[0]]).size\n      }\n\n      const header = {\n        comments: [],\n        elements: [],\n        headerLength: headerLength,\n        objInfo: '',\n      }\n\n      const lines = headerText.split('\\n')\n      let currentElement\n\n      function make_ply_element_property(propertValues, propertyNameMapping) {\n        const property = { type: propertValues[0] }\n\n        if (property.type === 'list') {\n          property.name = propertValues[3]\n          property.countType = propertValues[1]\n          property.itemType = propertValues[2]\n        } else {\n          property.name = propertValues[1]\n        }\n\n        if (property.name in propertyNameMapping) {\n          property.name = propertyNameMapping[property.name]\n        }\n\n        return property\n      }\n\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i]\n        line = line.trim()\n\n        if (line === '') continue\n\n        const lineValues = line.split(/\\s+/)\n        const lineType = lineValues.shift()\n        line = lineValues.join(' ')\n\n        switch (lineType) {\n          case 'format':\n            header.format = lineValues[0]\n            header.version = lineValues[1]\n\n            break\n\n          case 'comment':\n            header.comments.push(line)\n\n            break\n\n          case 'element':\n            if (currentElement !== undefined) {\n              header.elements.push(currentElement)\n            }\n\n            currentElement = {}\n            currentElement.name = lineValues[0]\n            currentElement.count = parseInt(lineValues[1])\n            currentElement.properties = []\n\n            break\n\n          case 'property':\n            currentElement.properties.push(make_ply_element_property(lineValues, scope.propertyNameMapping))\n\n            break\n\n          case 'obj_info':\n            header.objInfo = line\n\n            break\n\n          default:\n            console.log('unhandled', lineType, lineValues)\n        }\n      }\n\n      if (currentElement !== undefined) {\n        header.elements.push(currentElement)\n      }\n\n      return header\n    }\n\n    function parseASCIINumber(n, type) {\n      switch (type) {\n        case 'char':\n        case 'uchar':\n        case 'short':\n        case 'ushort':\n        case 'int':\n        case 'uint':\n        case 'int8':\n        case 'uint8':\n        case 'int16':\n        case 'uint16':\n        case 'int32':\n        case 'uint32':\n          return parseInt(n)\n\n        case 'float':\n        case 'double':\n        case 'float32':\n        case 'float64':\n          return parseFloat(n)\n      }\n    }\n\n    function parseASCIIElement(properties, line) {\n      const values = line.split(/\\s+/)\n\n      const element = {}\n\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === 'list') {\n          const list = []\n          const n = parseASCIINumber(values.shift(), properties[i].countType)\n\n          for (let j = 0; j < n; j++) {\n            list.push(parseASCIINumber(values.shift(), properties[i].itemType))\n          }\n\n          element[properties[i].name] = list\n        } else {\n          element[properties[i].name] = parseASCIINumber(values.shift(), properties[i].type)\n        }\n      }\n\n      return element\n    }\n\n    function parseASCII(data, header) {\n      // PLY ascii format specification, as per http://en.wikipedia.org/wiki/PLY_(file_format)\n\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: [],\n      }\n\n      let result\n\n      const patternBody = /end_header\\s([\\s\\S]*)$/\n      let body = ''\n      if ((result = patternBody.exec(data)) !== null) {\n        body = result[1]\n      }\n\n      const lines = body.split('\\n')\n      let currentElement = 0\n      let currentElementCount = 0\n\n      for (let i = 0; i < lines.length; i++) {\n        let line = lines[i]\n        line = line.trim()\n        if (line === '') {\n          continue\n        }\n\n        if (currentElementCount >= header.elements[currentElement].count) {\n          currentElement++\n          currentElementCount = 0\n        }\n\n        const element = parseASCIIElement(header.elements[currentElement].properties, line)\n\n        handleElement(buffer, header.elements[currentElement].name, element)\n\n        currentElementCount++\n      }\n\n      return postProcess(buffer)\n    }\n\n    function postProcess(buffer) {\n      let geometry = new BufferGeometry()\n\n      // mandatory buffer data\n\n      if (buffer.indices.length > 0) {\n        geometry.setIndex(buffer.indices)\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(buffer.vertices, 3))\n\n      // optional buffer data\n\n      if (buffer.normals.length > 0) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(buffer.normals, 3))\n      }\n\n      if (buffer.uvs.length > 0) {\n        geometry.setAttribute('uv', new Float32BufferAttribute(buffer.uvs, 2))\n      }\n\n      if (buffer.colors.length > 0) {\n        geometry.setAttribute('color', new Float32BufferAttribute(buffer.colors, 3))\n      }\n\n      if (buffer.faceVertexUvs.length > 0) {\n        geometry = geometry.toNonIndexed()\n        geometry.setAttribute('uv', new Float32BufferAttribute(buffer.faceVertexUvs, 2))\n      }\n\n      geometry.computeBoundingSphere()\n\n      return geometry\n    }\n\n    function handleElement(buffer, elementName, element) {\n      if (elementName === 'vertex') {\n        buffer.vertices.push(element.x, element.y, element.z)\n\n        if ('nx' in element && 'ny' in element && 'nz' in element) {\n          buffer.normals.push(element.nx, element.ny, element.nz)\n        }\n\n        if ('s' in element && 't' in element) {\n          buffer.uvs.push(element.s, element.t)\n        }\n\n        if ('red' in element && 'green' in element && 'blue' in element) {\n          buffer.colors.push(element.red / 255.0, element.green / 255.0, element.blue / 255.0)\n        }\n      } else if (elementName === 'face') {\n        const vertex_indices = element.vertex_indices || element.vertex_index // issue #9338\n        const texcoord = element.texcoord\n\n        if (vertex_indices.length === 3) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[2])\n\n          if (texcoord && texcoord.length === 6) {\n            buffer.faceVertexUvs.push(texcoord[0], texcoord[1])\n            buffer.faceVertexUvs.push(texcoord[2], texcoord[3])\n            buffer.faceVertexUvs.push(texcoord[4], texcoord[5])\n          }\n        } else if (vertex_indices.length === 4) {\n          buffer.indices.push(vertex_indices[0], vertex_indices[1], vertex_indices[3])\n          buffer.indices.push(vertex_indices[1], vertex_indices[2], vertex_indices[3])\n        }\n      }\n    }\n\n    function binaryRead(dataview, at, type, little_endian) {\n      switch (type) {\n        // corespondences for non-specific length types here match rply:\n        case 'int8':\n        case 'char':\n          return [dataview.getInt8(at), 1]\n        case 'uint8':\n        case 'uchar':\n          return [dataview.getUint8(at), 1]\n        case 'int16':\n        case 'short':\n          return [dataview.getInt16(at, little_endian), 2]\n        case 'uint16':\n        case 'ushort':\n          return [dataview.getUint16(at, little_endian), 2]\n        case 'int32':\n        case 'int':\n          return [dataview.getInt32(at, little_endian), 4]\n        case 'uint32':\n        case 'uint':\n          return [dataview.getUint32(at, little_endian), 4]\n        case 'float32':\n        case 'float':\n          return [dataview.getFloat32(at, little_endian), 4]\n        case 'float64':\n        case 'double':\n          return [dataview.getFloat64(at, little_endian), 8]\n      }\n    }\n\n    function binaryReadElement(dataview, at, properties, little_endian) {\n      const element = {}\n      let result,\n        read = 0\n\n      for (let i = 0; i < properties.length; i++) {\n        if (properties[i].type === 'list') {\n          const list = []\n\n          result = binaryRead(dataview, at + read, properties[i].countType, little_endian)\n          const n = result[0]\n          read += result[1]\n\n          for (let j = 0; j < n; j++) {\n            result = binaryRead(dataview, at + read, properties[i].itemType, little_endian)\n            list.push(result[0])\n            read += result[1]\n          }\n\n          element[properties[i].name] = list\n        } else {\n          result = binaryRead(dataview, at + read, properties[i].type, little_endian)\n          element[properties[i].name] = result[0]\n          read += result[1]\n        }\n      }\n\n      return [element, read]\n    }\n\n    function parseBinary(data, header) {\n      const buffer = {\n        indices: [],\n        vertices: [],\n        normals: [],\n        uvs: [],\n        faceVertexUvs: [],\n        colors: [],\n      }\n\n      const little_endian = header.format === 'binary_little_endian'\n      const body = new DataView(data, header.headerLength)\n      let result,\n        loc = 0\n\n      for (let currentElement = 0; currentElement < header.elements.length; currentElement++) {\n        for (\n          let currentElementCount = 0;\n          currentElementCount < header.elements[currentElement].count;\n          currentElementCount++\n        ) {\n          result = binaryReadElement(body, loc, header.elements[currentElement].properties, little_endian)\n          loc += result[1]\n          const element = result[0]\n\n          handleElement(buffer, header.elements[currentElement].name, element)\n        }\n      }\n\n      return postProcess(buffer)\n    }\n\n    //\n\n    let geometry\n    const scope = this\n\n    if (data instanceof ArrayBuffer) {\n      const text = decodeText(new Uint8Array(data))\n      const header = parseHeader(text)\n\n      geometry = header.format === 'ascii' ? parseASCII(text, header) : parseBinary(data, header)\n    } else {\n      geometry = parseASCII(data, parseHeader(data))\n    }\n\n    return geometry\n  }\n}\n\nexport { PLYLoader }\n"], "mappings": ";;AA6BA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,mBAAA,GAAsB,CAAE;EAC9B;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKT,OAAO;IAC1CQ,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;IACxBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiB,KAAKC,aAAa;IAC1CN,MAAA,CAAOO,kBAAA,CAAmB,KAAKC,eAAe;IAC9CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMP,OAAA,CAAQsB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDiB,uBAAuBC,OAAA,EAAS;IAC9B,KAAKvB,mBAAA,GAAsBuB,OAAA;EAC5B;EAEDN,MAAMO,IAAA,EAAM;IACV,SAASC,YAAYC,KAAA,EAAM;MACzB,MAAMC,aAAA,GAAgB;MACtB,IAAIC,UAAA,GAAa;MACjB,IAAIC,YAAA,GAAe;MACnB,MAAMC,MAAA,GAASH,aAAA,CAAcI,IAAA,CAAKL,KAAI;MAEtC,IAAII,MAAA,KAAW,MAAM;QACnBF,UAAA,GAAaE,MAAA,CAAO,CAAC;QACrBD,YAAA,GAAe,IAAIG,IAAA,CAAK,CAACF,MAAA,CAAO,CAAC,CAAC,CAAC,EAAEG,IAAA;MACtC;MAED,MAAMC,MAAA,GAAS;QACbC,QAAA,EAAU,EAAE;QACZC,QAAA,EAAU,EAAE;QACZP,YAAA;QACAQ,OAAA,EAAS;MACV;MAED,MAAMC,KAAA,GAAQV,UAAA,CAAWW,KAAA,CAAM,IAAI;MACnC,IAAIC,cAAA;MAEJ,SAASC,0BAA0BC,aAAA,EAAe1C,mBAAA,EAAqB;QACrE,MAAM2C,QAAA,GAAW;UAAEC,IAAA,EAAMF,aAAA,CAAc,CAAC;QAAG;QAE3C,IAAIC,QAAA,CAASC,IAAA,KAAS,QAAQ;UAC5BD,QAAA,CAASE,IAAA,GAAOH,aAAA,CAAc,CAAC;UAC/BC,QAAA,CAASG,SAAA,GAAYJ,aAAA,CAAc,CAAC;UACpCC,QAAA,CAASI,QAAA,GAAWL,aAAA,CAAc,CAAC;QAC7C,OAAe;UACLC,QAAA,CAASE,IAAA,GAAOH,aAAA,CAAc,CAAC;QAChC;QAED,IAAIC,QAAA,CAASE,IAAA,IAAQ7C,mBAAA,EAAqB;UACxC2C,QAAA,CAASE,IAAA,GAAO7C,mBAAA,CAAoB2C,QAAA,CAASE,IAAI;QAClD;QAED,OAAOF,QAAA;MACR;MAED,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIV,KAAA,CAAMW,MAAA,EAAQD,CAAA,IAAK;QACrC,IAAIE,IAAA,GAAOZ,KAAA,CAAMU,CAAC;QAClBE,IAAA,GAAOA,IAAA,CAAKC,IAAA,CAAM;QAElB,IAAID,IAAA,KAAS,IAAI;QAEjB,MAAME,UAAA,GAAaF,IAAA,CAAKX,KAAA,CAAM,KAAK;QACnC,MAAMc,QAAA,GAAWD,UAAA,CAAWE,KAAA,CAAO;QACnCJ,IAAA,GAAOE,UAAA,CAAWG,IAAA,CAAK,GAAG;QAE1B,QAAQF,QAAA;UACN,KAAK;YACHnB,MAAA,CAAOsB,MAAA,GAASJ,UAAA,CAAW,CAAC;YAC5BlB,MAAA,CAAOuB,OAAA,GAAUL,UAAA,CAAW,CAAC;YAE7B;UAEF,KAAK;YACHlB,MAAA,CAAOC,QAAA,CAASuB,IAAA,CAAKR,IAAI;YAEzB;UAEF,KAAK;YACH,IAAIV,cAAA,KAAmB,QAAW;cAChCN,MAAA,CAAOE,QAAA,CAASsB,IAAA,CAAKlB,cAAc;YACpC;YAEDA,cAAA,GAAiB,CAAE;YACnBA,cAAA,CAAeK,IAAA,GAAOO,UAAA,CAAW,CAAC;YAClCZ,cAAA,CAAemB,KAAA,GAAQC,QAAA,CAASR,UAAA,CAAW,CAAC,CAAC;YAC7CZ,cAAA,CAAeqB,UAAA,GAAa,EAAE;YAE9B;UAEF,KAAK;YACHrB,cAAA,CAAeqB,UAAA,CAAWH,IAAA,CAAKjB,yBAAA,CAA0BW,UAAA,EAAY9C,KAAA,CAAMN,mBAAmB,CAAC;YAE/F;UAEF,KAAK;YACHkC,MAAA,CAAOG,OAAA,GAAUa,IAAA;YAEjB;UAEF;YACE/B,OAAA,CAAQ2C,GAAA,CAAI,aAAaT,QAAA,EAAUD,UAAU;QAChD;MACF;MAED,IAAIZ,cAAA,KAAmB,QAAW;QAChCN,MAAA,CAAOE,QAAA,CAASsB,IAAA,CAAKlB,cAAc;MACpC;MAED,OAAON,MAAA;IACR;IAED,SAAS6B,iBAAiBC,CAAA,EAAGpB,IAAA,EAAM;MACjC,QAAQA,IAAA;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,OAAOgB,QAAA,CAASI,CAAC;QAEnB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,OAAOC,UAAA,CAAWD,CAAC;MACtB;IACF;IAED,SAASE,kBAAkBL,UAAA,EAAYX,IAAA,EAAM;MAC3C,MAAMiB,MAAA,GAASjB,IAAA,CAAKX,KAAA,CAAM,KAAK;MAE/B,MAAM6B,OAAA,GAAU,CAAE;MAElB,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIa,UAAA,CAAWZ,MAAA,EAAQD,CAAA,IAAK;QAC1C,IAAIa,UAAA,CAAWb,CAAC,EAAEJ,IAAA,KAAS,QAAQ;UACjC,MAAMyB,IAAA,GAAO,EAAE;UACf,MAAML,CAAA,GAAID,gBAAA,CAAiBI,MAAA,CAAOb,KAAA,CAAO,GAAEO,UAAA,CAAWb,CAAC,EAAEF,SAAS;UAElE,SAASwB,CAAA,GAAI,GAAGA,CAAA,GAAIN,CAAA,EAAGM,CAAA,IAAK;YAC1BD,IAAA,CAAKX,IAAA,CAAKK,gBAAA,CAAiBI,MAAA,CAAOb,KAAA,CAAO,GAAEO,UAAA,CAAWb,CAAC,EAAED,QAAQ,CAAC;UACnE;UAEDqB,OAAA,CAAQP,UAAA,CAAWb,CAAC,EAAEH,IAAI,IAAIwB,IAAA;QACxC,OAAe;UACLD,OAAA,CAAQP,UAAA,CAAWb,CAAC,EAAEH,IAAI,IAAIkB,gBAAA,CAAiBI,MAAA,CAAOb,KAAA,CAAK,GAAIO,UAAA,CAAWb,CAAC,EAAEJ,IAAI;QAClF;MACF;MAED,OAAOwB,OAAA;IACR;IAED,SAASG,WAAW7C,KAAA,EAAMQ,MAAA,EAAQ;MAGhC,MAAMsC,MAAA,GAAS;QACbC,OAAA,EAAS,EAAE;QACXC,QAAA,EAAU,EAAE;QACZC,OAAA,EAAS,EAAE;QACXC,GAAA,EAAK,EAAE;QACPC,aAAA,EAAe,EAAE;QACjBC,MAAA,EAAQ;MACT;MAED,IAAIhD,MAAA;MAEJ,MAAMiD,WAAA,GAAc;MACpB,IAAIC,IAAA,GAAO;MACX,KAAKlD,MAAA,GAASiD,WAAA,CAAYhD,IAAA,CAAKL,KAAI,OAAO,MAAM;QAC9CsD,IAAA,GAAOlD,MAAA,CAAO,CAAC;MAChB;MAED,MAAMQ,KAAA,GAAQ0C,IAAA,CAAKzC,KAAA,CAAM,IAAI;MAC7B,IAAIC,cAAA,GAAiB;MACrB,IAAIyC,mBAAA,GAAsB;MAE1B,SAASjC,CAAA,GAAI,GAAGA,CAAA,GAAIV,KAAA,CAAMW,MAAA,EAAQD,CAAA,IAAK;QACrC,IAAIE,IAAA,GAAOZ,KAAA,CAAMU,CAAC;QAClBE,IAAA,GAAOA,IAAA,CAAKC,IAAA,CAAM;QAClB,IAAID,IAAA,KAAS,IAAI;UACf;QACD;QAED,IAAI+B,mBAAA,IAAuB/C,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEmB,KAAA,EAAO;UAChEnB,cAAA;UACAyC,mBAAA,GAAsB;QACvB;QAED,MAAMb,OAAA,GAAUF,iBAAA,CAAkBhC,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEqB,UAAA,EAAYX,IAAI;QAElFgC,aAAA,CAAcV,MAAA,EAAQtC,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEK,IAAA,EAAMuB,OAAO;QAEnEa,mBAAA;MACD;MAED,OAAOE,WAAA,CAAYX,MAAM;IAC1B;IAED,SAASW,YAAYX,MAAA,EAAQ;MAC3B,IAAIY,SAAA,GAAW,IAAIC,cAAA,CAAgB;MAInC,IAAIb,MAAA,CAAOC,OAAA,CAAQxB,MAAA,GAAS,GAAG;QAC7BmC,SAAA,CAASE,QAAA,CAASd,MAAA,CAAOC,OAAO;MACjC;MAEDW,SAAA,CAASG,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBhB,MAAA,CAAOE,QAAA,EAAU,CAAC,CAAC;MAIhF,IAAIF,MAAA,CAAOG,OAAA,CAAQ1B,MAAA,GAAS,GAAG;QAC7BmC,SAAA,CAASG,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBhB,MAAA,CAAOG,OAAA,EAAS,CAAC,CAAC;MAC9E;MAED,IAAIH,MAAA,CAAOI,GAAA,CAAI3B,MAAA,GAAS,GAAG;QACzBmC,SAAA,CAASG,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBhB,MAAA,CAAOI,GAAA,EAAK,CAAC,CAAC;MACtE;MAED,IAAIJ,MAAA,CAAOM,MAAA,CAAO7B,MAAA,GAAS,GAAG;QAC5BmC,SAAA,CAASG,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBhB,MAAA,CAAOM,MAAA,EAAQ,CAAC,CAAC;MAC5E;MAED,IAAIN,MAAA,CAAOK,aAAA,CAAc5B,MAAA,GAAS,GAAG;QACnCmC,SAAA,GAAWA,SAAA,CAASK,YAAA,CAAc;QAClCL,SAAA,CAASG,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBhB,MAAA,CAAOK,aAAA,EAAe,CAAC,CAAC;MAChF;MAEDO,SAAA,CAASM,qBAAA,CAAuB;MAEhC,OAAON,SAAA;IACR;IAED,SAASF,cAAcV,MAAA,EAAQmB,WAAA,EAAavB,OAAA,EAAS;MACnD,IAAIuB,WAAA,KAAgB,UAAU;QAC5BnB,MAAA,CAAOE,QAAA,CAAShB,IAAA,CAAKU,OAAA,CAAQwB,CAAA,EAAGxB,OAAA,CAAQyB,CAAA,EAAGzB,OAAA,CAAQ0B,CAAC;QAEpD,IAAI,QAAQ1B,OAAA,IAAW,QAAQA,OAAA,IAAW,QAAQA,OAAA,EAAS;UACzDI,MAAA,CAAOG,OAAA,CAAQjB,IAAA,CAAKU,OAAA,CAAQ2B,EAAA,EAAI3B,OAAA,CAAQ4B,EAAA,EAAI5B,OAAA,CAAQ6B,EAAE;QACvD;QAED,IAAI,OAAO7B,OAAA,IAAW,OAAOA,OAAA,EAAS;UACpCI,MAAA,CAAOI,GAAA,CAAIlB,IAAA,CAAKU,OAAA,CAAQ8B,CAAA,EAAG9B,OAAA,CAAQ+B,CAAC;QACrC;QAED,IAAI,SAAS/B,OAAA,IAAW,WAAWA,OAAA,IAAW,UAAUA,OAAA,EAAS;UAC/DI,MAAA,CAAOM,MAAA,CAAOpB,IAAA,CAAKU,OAAA,CAAQgC,GAAA,GAAM,KAAOhC,OAAA,CAAQiC,KAAA,GAAQ,KAAOjC,OAAA,CAAQkC,IAAA,GAAO,GAAK;QACpF;MACT,WAAiBX,WAAA,KAAgB,QAAQ;QACjC,MAAMY,cAAA,GAAiBnC,OAAA,CAAQmC,cAAA,IAAkBnC,OAAA,CAAQoC,YAAA;QACzD,MAAMC,QAAA,GAAWrC,OAAA,CAAQqC,QAAA;QAEzB,IAAIF,cAAA,CAAetD,MAAA,KAAW,GAAG;UAC/BuB,MAAA,CAAOC,OAAA,CAAQf,IAAA,CAAK6C,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,CAAC;UAE3E,IAAIE,QAAA,IAAYA,QAAA,CAASxD,MAAA,KAAW,GAAG;YACrCuB,MAAA,CAAOK,aAAA,CAAcnB,IAAA,CAAK+C,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;YAClDjC,MAAA,CAAOK,aAAA,CAAcnB,IAAA,CAAK+C,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;YAClDjC,MAAA,CAAOK,aAAA,CAAcnB,IAAA,CAAK+C,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;UACnD;QACX,WAAmBF,cAAA,CAAetD,MAAA,KAAW,GAAG;UACtCuB,MAAA,CAAOC,OAAA,CAAQf,IAAA,CAAK6C,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,CAAC;UAC3E/B,MAAA,CAAOC,OAAA,CAAQf,IAAA,CAAK6C,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,GAAGA,cAAA,CAAe,CAAC,CAAC;QAC5E;MACF;IACF;IAED,SAASG,WAAWC,QAAA,EAAUC,EAAA,EAAIhE,IAAA,EAAMiE,aAAA,EAAe;MACrD,QAAQjE,IAAA;QAEN,KAAK;QACL,KAAK;UACH,OAAO,CAAC+D,QAAA,CAASG,OAAA,CAAQF,EAAE,GAAG,CAAC;QACjC,KAAK;QACL,KAAK;UACH,OAAO,CAACD,QAAA,CAASI,QAAA,CAASH,EAAE,GAAG,CAAC;QAClC,KAAK;QACL,KAAK;UACH,OAAO,CAACD,QAAA,CAASK,QAAA,CAASJ,EAAA,EAAIC,aAAa,GAAG,CAAC;QACjD,KAAK;QACL,KAAK;UACH,OAAO,CAACF,QAAA,CAASM,SAAA,CAAUL,EAAA,EAAIC,aAAa,GAAG,CAAC;QAClD,KAAK;QACL,KAAK;UACH,OAAO,CAACF,QAAA,CAASO,QAAA,CAASN,EAAA,EAAIC,aAAa,GAAG,CAAC;QACjD,KAAK;QACL,KAAK;UACH,OAAO,CAACF,QAAA,CAASQ,SAAA,CAAUP,EAAA,EAAIC,aAAa,GAAG,CAAC;QAClD,KAAK;QACL,KAAK;UACH,OAAO,CAACF,QAAA,CAASS,UAAA,CAAWR,EAAA,EAAIC,aAAa,GAAG,CAAC;QACnD,KAAK;QACL,KAAK;UACH,OAAO,CAACF,QAAA,CAASU,UAAA,CAAWT,EAAA,EAAIC,aAAa,GAAG,CAAC;MACpD;IACF;IAED,SAASS,kBAAkBX,QAAA,EAAUC,EAAA,EAAI/C,UAAA,EAAYgD,aAAA,EAAe;MAClE,MAAMzC,OAAA,GAAU,CAAE;MAClB,IAAItC,MAAA;QACFyF,IAAA,GAAO;MAET,SAASvE,CAAA,GAAI,GAAGA,CAAA,GAAIa,UAAA,CAAWZ,MAAA,EAAQD,CAAA,IAAK;QAC1C,IAAIa,UAAA,CAAWb,CAAC,EAAEJ,IAAA,KAAS,QAAQ;UACjC,MAAMyB,IAAA,GAAO,EAAE;UAEfvC,MAAA,GAAS4E,UAAA,CAAWC,QAAA,EAAUC,EAAA,GAAKW,IAAA,EAAM1D,UAAA,CAAWb,CAAC,EAAEF,SAAA,EAAW+D,aAAa;UAC/E,MAAM7C,CAAA,GAAIlC,MAAA,CAAO,CAAC;UAClByF,IAAA,IAAQzF,MAAA,CAAO,CAAC;UAEhB,SAASwC,CAAA,GAAI,GAAGA,CAAA,GAAIN,CAAA,EAAGM,CAAA,IAAK;YAC1BxC,MAAA,GAAS4E,UAAA,CAAWC,QAAA,EAAUC,EAAA,GAAKW,IAAA,EAAM1D,UAAA,CAAWb,CAAC,EAAED,QAAA,EAAU8D,aAAa;YAC9ExC,IAAA,CAAKX,IAAA,CAAK5B,MAAA,CAAO,CAAC,CAAC;YACnByF,IAAA,IAAQzF,MAAA,CAAO,CAAC;UACjB;UAEDsC,OAAA,CAAQP,UAAA,CAAWb,CAAC,EAAEH,IAAI,IAAIwB,IAAA;QACxC,OAAe;UACLvC,MAAA,GAAS4E,UAAA,CAAWC,QAAA,EAAUC,EAAA,GAAKW,IAAA,EAAM1D,UAAA,CAAWb,CAAC,EAAEJ,IAAA,EAAMiE,aAAa;UAC1EzC,OAAA,CAAQP,UAAA,CAAWb,CAAC,EAAEH,IAAI,IAAIf,MAAA,CAAO,CAAC;UACtCyF,IAAA,IAAQzF,MAAA,CAAO,CAAC;QACjB;MACF;MAED,OAAO,CAACsC,OAAA,EAASmD,IAAI;IACtB;IAED,SAASC,YAAY9F,KAAA,EAAMQ,MAAA,EAAQ;MACjC,MAAMsC,MAAA,GAAS;QACbC,OAAA,EAAS,EAAE;QACXC,QAAA,EAAU,EAAE;QACZC,OAAA,EAAS,EAAE;QACXC,GAAA,EAAK,EAAE;QACPC,aAAA,EAAe,EAAE;QACjBC,MAAA,EAAQ;MACT;MAED,MAAM+B,aAAA,GAAgB3E,MAAA,CAAOsB,MAAA,KAAW;MACxC,MAAMwB,IAAA,GAAO,IAAIyC,QAAA,CAAS/F,KAAA,EAAMQ,MAAA,CAAOL,YAAY;MACnD,IAAIC,MAAA;QACF4F,GAAA,GAAM;MAER,SAASlF,cAAA,GAAiB,GAAGA,cAAA,GAAiBN,MAAA,CAAOE,QAAA,CAASa,MAAA,EAAQT,cAAA,IAAkB;QACtF,SACMyC,mBAAA,GAAsB,GAC1BA,mBAAA,GAAsB/C,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEmB,KAAA,EACtDsB,mBAAA,IACA;UACAnD,MAAA,GAASwF,iBAAA,CAAkBtC,IAAA,EAAM0C,GAAA,EAAKxF,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEqB,UAAA,EAAYgD,aAAa;UAC/Fa,GAAA,IAAO5F,MAAA,CAAO,CAAC;UACf,MAAMsC,OAAA,GAAUtC,MAAA,CAAO,CAAC;UAExBoD,aAAA,CAAcV,MAAA,EAAQtC,MAAA,CAAOE,QAAA,CAASI,cAAc,EAAEK,IAAA,EAAMuB,OAAO;QACpE;MACF;MAED,OAAOe,WAAA,CAAYX,MAAM;IAC1B;IAID,IAAImD,QAAA;IACJ,MAAMrH,KAAA,GAAQ;IAEd,IAAIkB,IAAA,YAAgBoG,WAAA,EAAa;MAC/B,MAAM5G,IAAA,GAAO6G,UAAA,CAAW,IAAIC,UAAA,CAAWtG,IAAI,CAAC;MAC5C,MAAMU,MAAA,GAAST,WAAA,CAAYT,IAAI;MAE/B2G,QAAA,GAAWzF,MAAA,CAAOsB,MAAA,KAAW,UAAUe,UAAA,CAAWvD,IAAA,EAAMkB,MAAM,IAAIsF,WAAA,CAAYhG,IAAA,EAAMU,MAAM;IAChG,OAAW;MACLyF,QAAA,GAAWpD,UAAA,CAAW/C,IAAA,EAAMC,WAAA,CAAYD,IAAI,CAAC;IAC9C;IAED,OAAOmG,QAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}