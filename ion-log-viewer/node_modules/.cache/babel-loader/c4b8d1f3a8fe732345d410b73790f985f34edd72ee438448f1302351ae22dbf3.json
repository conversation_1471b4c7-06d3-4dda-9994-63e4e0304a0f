{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Mesh, PerspectiveCamera, Color, Plane, Vector3, Matrix4, Vector4, WebGLRenderTarget, HalfFloatType, ShaderMaterial, UniformsUtils, NoToneMapping } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nconst Reflector = /* @__PURE__ */(() => {\n  const _Reflector = class extends Mesh {\n    constructor(geometry, options = {}) {\n      super(geometry);\n      this.isReflector = true;\n      this.type = \"Reflector\";\n      this.camera = new PerspectiveCamera();\n      const scope = this;\n      const color = options.color !== void 0 ? new Color(options.color) : new Color(8355711);\n      const textureWidth = options.textureWidth || 512;\n      const textureHeight = options.textureHeight || 512;\n      const clipBias = options.clipBias || 0;\n      const shader = options.shader || _Reflector.ReflectorShader;\n      const multisample = options.multisample !== void 0 ? options.multisample : 4;\n      const reflectorPlane = new Plane();\n      const normal = new Vector3();\n      const reflectorWorldPosition = new Vector3();\n      const cameraWorldPosition = new Vector3();\n      const rotationMatrix = new Matrix4();\n      const lookAtPosition = new Vector3(0, 0, -1);\n      const clipPlane = new Vector4();\n      const view = new Vector3();\n      const target = new Vector3();\n      const q = new Vector4();\n      const textureMatrix = new Matrix4();\n      const virtualCamera = this.camera;\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, {\n        samples: multisample,\n        type: HalfFloatType\n      });\n      const material = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        fragmentShader: shader.fragmentShader,\n        vertexShader: shader.vertexShader\n      });\n      material.uniforms[\"tDiffuse\"].value = renderTarget.texture;\n      material.uniforms[\"color\"].value = color;\n      material.uniforms[\"textureMatrix\"].value = textureMatrix;\n      this.material = material;\n      this.onBeforeRender = function (renderer, scene, camera) {\n        reflectorWorldPosition.setFromMatrixPosition(scope.matrixWorld);\n        cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n        rotationMatrix.extractRotation(scope.matrixWorld);\n        normal.set(0, 0, 1);\n        normal.applyMatrix4(rotationMatrix);\n        view.subVectors(reflectorWorldPosition, cameraWorldPosition);\n        if (view.dot(normal) > 0) return;\n        view.reflect(normal).negate();\n        view.add(reflectorWorldPosition);\n        rotationMatrix.extractRotation(camera.matrixWorld);\n        lookAtPosition.set(0, 0, -1);\n        lookAtPosition.applyMatrix4(rotationMatrix);\n        lookAtPosition.add(cameraWorldPosition);\n        target.subVectors(reflectorWorldPosition, lookAtPosition);\n        target.reflect(normal).negate();\n        target.add(reflectorWorldPosition);\n        virtualCamera.position.copy(view);\n        virtualCamera.up.set(0, 1, 0);\n        virtualCamera.up.applyMatrix4(rotationMatrix);\n        virtualCamera.up.reflect(normal);\n        virtualCamera.lookAt(target);\n        virtualCamera.far = camera.far;\n        virtualCamera.updateMatrixWorld();\n        virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n        textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n        textureMatrix.multiply(virtualCamera.projectionMatrix);\n        textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n        textureMatrix.multiply(scope.matrixWorld);\n        reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n        reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n        clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n        const projectionMatrix = virtualCamera.projectionMatrix;\n        q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n        q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n        q.z = -1;\n        q.w = (1 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n        clipPlane.multiplyScalar(2 / clipPlane.dot(q));\n        projectionMatrix.elements[2] = clipPlane.x;\n        projectionMatrix.elements[6] = clipPlane.y;\n        projectionMatrix.elements[10] = clipPlane.z + 1 - clipBias;\n        projectionMatrix.elements[14] = clipPlane.w;\n        scope.visible = false;\n        const currentRenderTarget = renderer.getRenderTarget();\n        const currentXrEnabled = renderer.xr.enabled;\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;\n        const currentToneMapping = renderer.toneMapping;\n        let isSRGB = false;\n        if (\"outputColorSpace\" in renderer) isSRGB = renderer.outputColorSpace === \"srgb\";else isSRGB = renderer.outputEncoding === 3001;\n        renderer.xr.enabled = false;\n        renderer.shadowMap.autoUpdate = false;\n        if (\"outputColorSpace\" in renderer) renderer.outputColorSpace = \"srgb-linear\";else renderer.outputEncoding = 3e3;\n        renderer.toneMapping = NoToneMapping;\n        renderer.setRenderTarget(renderTarget);\n        renderer.state.buffers.depth.setMask(true);\n        if (renderer.autoClear === false) renderer.clear();\n        renderer.render(scene, virtualCamera);\n        renderer.xr.enabled = currentXrEnabled;\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;\n        renderer.toneMapping = currentToneMapping;\n        if (\"outputColorSpace\" in renderer) renderer.outputColorSpace = isSRGB ? \"srgb\" : \"srgb-linear\";else renderer.outputEncoding = isSRGB ? 3001 : 3e3;\n        renderer.setRenderTarget(currentRenderTarget);\n        const viewport = camera.viewport;\n        if (viewport !== void 0) {\n          renderer.state.viewport(viewport);\n        }\n        scope.visible = true;\n      };\n      this.getRenderTarget = function () {\n        return renderTarget;\n      };\n      this.dispose = function () {\n        renderTarget.dispose();\n        scope.material.dispose();\n      };\n    }\n  };\n  let Reflector2 = _Reflector;\n  __publicField(Reflector2, \"ReflectorShader\", {\n    uniforms: {\n      color: {\n        value: null\n      },\n      tDiffuse: {\n        value: null\n      },\n      textureMatrix: {\n        value: null\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\t\tuniform mat4 textureMatrix;\n\t\tvarying vec4 vUv;\n\n\t\t#include <common>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t#include <logdepthbuf_vertex>\n\n\t\t}`),\n    fragmentShader: (/* glsl */\n    `\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\t\tvarying vec4 vUv;\n\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tfloat blendOverlay( float base, float blend ) {\n\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n\t\t}\n\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n\n\t\t}`)\n  });\n  return Reflector2;\n})();\nexport { Reflector };", "map": {"version": 3, "names": ["Reflector", "_Reflector", "<PERSON><PERSON>", "constructor", "geometry", "options", "isReflector", "type", "camera", "PerspectiveCamera", "scope", "color", "Color", "textureWidth", "textureHeight", "clipBias", "shader", "ReflectorShader", "multisample", "reflectorPlane", "Plane", "normal", "Vector3", "reflectorWorldPosition", "cameraWorldPosition", "rotationMatrix", "Matrix4", "lookAtPosition", "clipPlane", "Vector4", "view", "target", "q", "textureMatrix", "virtualCamera", "renderTarget", "WebGLRenderTarget", "samples", "HalfFloatType", "material", "ShaderMaterial", "uniforms", "UniformsUtils", "clone", "fragmentShader", "vertexShader", "value", "texture", "onBeforeRender", "renderer", "scene", "setFromMatrixPosition", "matrixWorld", "extractRotation", "set", "applyMatrix4", "subVectors", "dot", "reflect", "negate", "add", "position", "copy", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "multiply", "matrixWorldInverse", "setFromNormalAndCoplanarPoint", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "currentToneMapping", "toneMapping", "isSRGB", "outputColorSpace", "outputEncoding", "NoToneMapping", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "viewport", "dispose", "Reflector2", "__publicField", "tDiffuse", "version"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/objects/Reflector.js"], "sourcesContent": ["import {\n  Color,\n  Matrix4,\n  Mesh,\n  PerspectiveCamera,\n  Plane,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector3,\n  Vector4,\n  WebGLRenderTarget,\n  HalfFloatType,\n  NoToneMapping,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nconst Reflector = /* @__PURE__ */ (() => {\n  class Reflector extends Mesh {\n    static ReflectorShader = {\n      uniforms: {\n        color: {\n          value: null,\n        },\n\n        tDiffuse: {\n          value: null,\n        },\n\n        textureMatrix: {\n          value: null,\n        },\n      },\n\n      vertexShader: /* glsl */ `\n\t\tuniform mat4 textureMatrix;\n\t\tvarying vec4 vUv;\n\n\t\t#include <common>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t#include <logdepthbuf_vertex>\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\t\tvarying vec4 vUv;\n\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tfloat blendOverlay( float base, float blend ) {\n\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n\t\t}\n\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\n\t\t}`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isReflector = true\n\n      this.type = 'Reflector'\n      this.camera = new PerspectiveCamera()\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0x7f7f7f)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const shader = options.shader || Reflector.ReflectorShader\n      const multisample = options.multisample !== undefined ? options.multisample : 4\n\n      //\n\n      const reflectorPlane = new Plane()\n      const normal = new Vector3()\n      const reflectorWorldPosition = new Vector3()\n      const cameraWorldPosition = new Vector3()\n      const rotationMatrix = new Matrix4()\n      const lookAtPosition = new Vector3(0, 0, -1)\n      const clipPlane = new Vector4()\n\n      const view = new Vector3()\n      const target = new Vector3()\n      const q = new Vector4()\n\n      const textureMatrix = new Matrix4()\n      const virtualCamera = this.camera\n\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, {\n        samples: multisample,\n        type: HalfFloatType,\n      })\n\n      const material = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        fragmentShader: shader.fragmentShader,\n        vertexShader: shader.vertexShader,\n      })\n\n      material.uniforms['tDiffuse'].value = renderTarget.texture\n      material.uniforms['color'].value = color\n      material.uniforms['textureMatrix'].value = textureMatrix\n\n      this.material = material\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        reflectorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n        cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n        rotationMatrix.extractRotation(scope.matrixWorld)\n\n        normal.set(0, 0, 1)\n        normal.applyMatrix4(rotationMatrix)\n\n        view.subVectors(reflectorWorldPosition, cameraWorldPosition)\n\n        // Avoid rendering when reflector is facing away\n\n        if (view.dot(normal) > 0) return\n\n        view.reflect(normal).negate()\n        view.add(reflectorWorldPosition)\n\n        rotationMatrix.extractRotation(camera.matrixWorld)\n\n        lookAtPosition.set(0, 0, -1)\n        lookAtPosition.applyMatrix4(rotationMatrix)\n        lookAtPosition.add(cameraWorldPosition)\n\n        target.subVectors(reflectorWorldPosition, lookAtPosition)\n        target.reflect(normal).negate()\n        target.add(reflectorWorldPosition)\n\n        virtualCamera.position.copy(view)\n        virtualCamera.up.set(0, 1, 0)\n        virtualCamera.up.applyMatrix4(rotationMatrix)\n        virtualCamera.up.reflect(normal)\n        virtualCamera.lookAt(target)\n\n        virtualCamera.far = camera.far // Used in WebGLBackground\n\n        virtualCamera.updateMatrixWorld()\n        virtualCamera.projectionMatrix.copy(camera.projectionMatrix)\n\n        // Update the texture matrix\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n        textureMatrix.multiply(virtualCamera.projectionMatrix)\n        textureMatrix.multiply(virtualCamera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n\n        // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n        // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n        reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition)\n        reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse)\n\n        clipPlane.set(\n          reflectorPlane.normal.x,\n          reflectorPlane.normal.y,\n          reflectorPlane.normal.z,\n          reflectorPlane.constant,\n        )\n\n        const projectionMatrix = virtualCamera.projectionMatrix\n\n        q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0]\n        q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5]\n        q.z = -1.0\n        q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]\n\n        // Calculate the scaled plane vector\n        clipPlane.multiplyScalar(2.0 / clipPlane.dot(q))\n\n        // Replacing the third row of the projection matrix\n        projectionMatrix.elements[2] = clipPlane.x\n        projectionMatrix.elements[6] = clipPlane.y\n        projectionMatrix.elements[10] = clipPlane.z + 1.0 - clipBias\n        projectionMatrix.elements[14] = clipPlane.w\n\n        // Render\n        scope.visible = false\n\n        const currentRenderTarget = renderer.getRenderTarget()\n\n        const currentXrEnabled = renderer.xr.enabled\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n        const currentToneMapping = renderer.toneMapping\n\n        let isSRGB = false\n        if ('outputColorSpace' in renderer) isSRGB = renderer.outputColorSpace === 'srgb'\n        else isSRGB = renderer.outputEncoding === 3001 // sRGBEncoding\n\n        renderer.xr.enabled = false // Avoid camera modification\n        renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = 'srgb-linear'\n        else renderer.outputEncoding = 3000 // LinearEncoding\n        renderer.toneMapping = NoToneMapping\n\n        renderer.setRenderTarget(renderTarget)\n\n        renderer.state.buffers.depth.setMask(true) // make sure the depth buffer is writable so it can be properly cleared, see #18897\n\n        if (renderer.autoClear === false) renderer.clear()\n        renderer.render(scene, virtualCamera)\n\n        renderer.xr.enabled = currentXrEnabled\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n        renderer.toneMapping = currentToneMapping\n\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = isSRGB ? 'srgb' : 'srgb-linear'\n        else renderer.outputEncoding = isSRGB ? 3001 : 3000\n\n        renderer.setRenderTarget(currentRenderTarget)\n\n        // Restore viewport\n\n        const viewport = camera.viewport\n\n        if (viewport !== undefined) {\n          renderer.state.viewport(viewport)\n        }\n\n        scope.visible = true\n      }\n\n      this.getRenderTarget = function () {\n        return renderTarget\n      }\n\n      this.dispose = function () {\n        renderTarget.dispose()\n        scope.material.dispose()\n      }\n    }\n  }\n\n  return Reflector\n})()\n\nexport { Reflector }\n"], "mappings": ";;;;;;;;;;;;;AAgBK,MAACA,SAAA,GAA6B,sBAAM;EACvC,MAAMC,UAAA,GAAN,cAAwBC,IAAA,CAAK;IAiE3BC,YAAYC,QAAA,EAAUC,OAAA,GAAU,IAAI;MAClC,MAAMD,QAAQ;MAEd,KAAKE,WAAA,GAAc;MAEnB,KAAKC,IAAA,GAAO;MACZ,KAAKC,MAAA,GAAS,IAAIC,iBAAA,CAAmB;MAErC,MAAMC,KAAA,GAAQ;MAEd,MAAMC,KAAA,GAAQN,OAAA,CAAQM,KAAA,KAAU,SAAY,IAAIC,KAAA,CAAMP,OAAA,CAAQM,KAAK,IAAI,IAAIC,KAAA,CAAM,OAAQ;MACzF,MAAMC,YAAA,GAAeR,OAAA,CAAQQ,YAAA,IAAgB;MAC7C,MAAMC,aAAA,GAAgBT,OAAA,CAAQS,aAAA,IAAiB;MAC/C,MAAMC,QAAA,GAAWV,OAAA,CAAQU,QAAA,IAAY;MACrC,MAAMC,MAAA,GAASX,OAAA,CAAQW,MAAA,IAAUf,UAAA,CAAUgB,eAAA;MAC3C,MAAMC,WAAA,GAAcb,OAAA,CAAQa,WAAA,KAAgB,SAAYb,OAAA,CAAQa,WAAA,GAAc;MAI9E,MAAMC,cAAA,GAAiB,IAAIC,KAAA,CAAO;MAClC,MAAMC,MAAA,GAAS,IAAIC,OAAA,CAAS;MAC5B,MAAMC,sBAAA,GAAyB,IAAID,OAAA,CAAS;MAC5C,MAAME,mBAAA,GAAsB,IAAIF,OAAA,CAAS;MACzC,MAAMG,cAAA,GAAiB,IAAIC,OAAA,CAAS;MACpC,MAAMC,cAAA,GAAiB,IAAIL,OAAA,CAAQ,GAAG,GAAG,EAAE;MAC3C,MAAMM,SAAA,GAAY,IAAIC,OAAA,CAAS;MAE/B,MAAMC,IAAA,GAAO,IAAIR,OAAA,CAAS;MAC1B,MAAMS,MAAA,GAAS,IAAIT,OAAA,CAAS;MAC5B,MAAMU,CAAA,GAAI,IAAIH,OAAA,CAAS;MAEvB,MAAMI,aAAA,GAAgB,IAAIP,OAAA,CAAS;MACnC,MAAMQ,aAAA,GAAgB,KAAK1B,MAAA;MAE3B,MAAM2B,YAAA,GAAe,IAAIC,iBAAA,CAAkBvB,YAAA,EAAcC,aAAA,EAAe;QACtEuB,OAAA,EAASnB,WAAA;QACTX,IAAA,EAAM+B;MACd,CAAO;MAED,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAe;QAClCC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM3B,MAAA,CAAOyB,QAAQ;QAC7CG,cAAA,EAAgB5B,MAAA,CAAO4B,cAAA;QACvBC,YAAA,EAAc7B,MAAA,CAAO6B;MAC7B,CAAO;MAEDN,QAAA,CAASE,QAAA,CAAS,UAAU,EAAEK,KAAA,GAAQX,YAAA,CAAaY,OAAA;MACnDR,QAAA,CAASE,QAAA,CAAS,OAAO,EAAEK,KAAA,GAAQnC,KAAA;MACnC4B,QAAA,CAASE,QAAA,CAAS,eAAe,EAAEK,KAAA,GAAQb,aAAA;MAE3C,KAAKM,QAAA,GAAWA,QAAA;MAEhB,KAAKS,cAAA,GAAiB,UAAUC,QAAA,EAAUC,KAAA,EAAO1C,MAAA,EAAQ;QACvDe,sBAAA,CAAuB4B,qBAAA,CAAsBzC,KAAA,CAAM0C,WAAW;QAC9D5B,mBAAA,CAAoB2B,qBAAA,CAAsB3C,MAAA,CAAO4C,WAAW;QAE5D3B,cAAA,CAAe4B,eAAA,CAAgB3C,KAAA,CAAM0C,WAAW;QAEhD/B,MAAA,CAAOiC,GAAA,CAAI,GAAG,GAAG,CAAC;QAClBjC,MAAA,CAAOkC,YAAA,CAAa9B,cAAc;QAElCK,IAAA,CAAK0B,UAAA,CAAWjC,sBAAA,EAAwBC,mBAAmB;QAI3D,IAAIM,IAAA,CAAK2B,GAAA,CAAIpC,MAAM,IAAI,GAAG;QAE1BS,IAAA,CAAK4B,OAAA,CAAQrC,MAAM,EAAEsC,MAAA,CAAQ;QAC7B7B,IAAA,CAAK8B,GAAA,CAAIrC,sBAAsB;QAE/BE,cAAA,CAAe4B,eAAA,CAAgB7C,MAAA,CAAO4C,WAAW;QAEjDzB,cAAA,CAAe2B,GAAA,CAAI,GAAG,GAAG,EAAE;QAC3B3B,cAAA,CAAe4B,YAAA,CAAa9B,cAAc;QAC1CE,cAAA,CAAeiC,GAAA,CAAIpC,mBAAmB;QAEtCO,MAAA,CAAOyB,UAAA,CAAWjC,sBAAA,EAAwBI,cAAc;QACxDI,MAAA,CAAO2B,OAAA,CAAQrC,MAAM,EAAEsC,MAAA,CAAQ;QAC/B5B,MAAA,CAAO6B,GAAA,CAAIrC,sBAAsB;QAEjCW,aAAA,CAAc2B,QAAA,CAASC,IAAA,CAAKhC,IAAI;QAChCI,aAAA,CAAc6B,EAAA,CAAGT,GAAA,CAAI,GAAG,GAAG,CAAC;QAC5BpB,aAAA,CAAc6B,EAAA,CAAGR,YAAA,CAAa9B,cAAc;QAC5CS,aAAA,CAAc6B,EAAA,CAAGL,OAAA,CAAQrC,MAAM;QAC/Ba,aAAA,CAAc8B,MAAA,CAAOjC,MAAM;QAE3BG,aAAA,CAAc+B,GAAA,GAAMzD,MAAA,CAAOyD,GAAA;QAE3B/B,aAAA,CAAcgC,iBAAA,CAAmB;QACjChC,aAAA,CAAciC,gBAAA,CAAiBL,IAAA,CAAKtD,MAAA,CAAO2D,gBAAgB;QAG3DlC,aAAA,CAAcqB,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;QAChGrB,aAAA,CAAcmC,QAAA,CAASlC,aAAA,CAAciC,gBAAgB;QACrDlC,aAAA,CAAcmC,QAAA,CAASlC,aAAA,CAAcmC,kBAAkB;QACvDpC,aAAA,CAAcmC,QAAA,CAAS1D,KAAA,CAAM0C,WAAW;QAIxCjC,cAAA,CAAemD,6BAAA,CAA8BjD,MAAA,EAAQE,sBAAsB;QAC3EJ,cAAA,CAAeoC,YAAA,CAAarB,aAAA,CAAcmC,kBAAkB;QAE5DzC,SAAA,CAAU0B,GAAA,CACRnC,cAAA,CAAeE,MAAA,CAAOkD,CAAA,EACtBpD,cAAA,CAAeE,MAAA,CAAOmD,CAAA,EACtBrD,cAAA,CAAeE,MAAA,CAAOoD,CAAA,EACtBtD,cAAA,CAAeuD,QAChB;QAED,MAAMP,gBAAA,GAAmBjC,aAAA,CAAciC,gBAAA;QAEvCnC,CAAA,CAAEuC,CAAA,IAAKI,IAAA,CAAKC,IAAA,CAAKhD,SAAA,CAAU2C,CAAC,IAAIJ,gBAAA,CAAiBU,QAAA,CAAS,CAAC,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,CAAC;QAC3F7C,CAAA,CAAEwC,CAAA,IAAKG,IAAA,CAAKC,IAAA,CAAKhD,SAAA,CAAU4C,CAAC,IAAIL,gBAAA,CAAiBU,QAAA,CAAS,CAAC,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,CAAC;QAC3F7C,CAAA,CAAEyC,CAAA,GAAI;QACNzC,CAAA,CAAE8C,CAAA,IAAK,IAAMX,gBAAA,CAAiBU,QAAA,CAAS,EAAE,KAAKV,gBAAA,CAAiBU,QAAA,CAAS,EAAE;QAG1EjD,SAAA,CAAUmD,cAAA,CAAe,IAAMnD,SAAA,CAAU6B,GAAA,CAAIzB,CAAC,CAAC;QAG/CmC,gBAAA,CAAiBU,QAAA,CAAS,CAAC,IAAIjD,SAAA,CAAU2C,CAAA;QACzCJ,gBAAA,CAAiBU,QAAA,CAAS,CAAC,IAAIjD,SAAA,CAAU4C,CAAA;QACzCL,gBAAA,CAAiBU,QAAA,CAAS,EAAE,IAAIjD,SAAA,CAAU6C,CAAA,GAAI,IAAM1D,QAAA;QACpDoD,gBAAA,CAAiBU,QAAA,CAAS,EAAE,IAAIjD,SAAA,CAAUkD,CAAA;QAG1CpE,KAAA,CAAMsE,OAAA,GAAU;QAEhB,MAAMC,mBAAA,GAAsBhC,QAAA,CAASiC,eAAA,CAAiB;QAEtD,MAAMC,gBAAA,GAAmBlC,QAAA,CAASmC,EAAA,CAAGC,OAAA;QACrC,MAAMC,uBAAA,GAA0BrC,QAAA,CAASsC,SAAA,CAAUC,UAAA;QACnD,MAAMC,kBAAA,GAAqBxC,QAAA,CAASyC,WAAA;QAEpC,IAAIC,MAAA,GAAS;QACb,IAAI,sBAAsB1C,QAAA,EAAU0C,MAAA,GAAS1C,QAAA,CAAS2C,gBAAA,KAAqB,YACtED,MAAA,GAAS1C,QAAA,CAAS4C,cAAA,KAAmB;QAE1C5C,QAAA,CAASmC,EAAA,CAAGC,OAAA,GAAU;QACtBpC,QAAA,CAASsC,SAAA,CAAUC,UAAA,GAAa;QAChC,IAAI,sBAAsBvC,QAAA,EAAUA,QAAA,CAAS2C,gBAAA,GAAmB,mBAC3D3C,QAAA,CAAS4C,cAAA,GAAiB;QAC/B5C,QAAA,CAASyC,WAAA,GAAcI,aAAA;QAEvB7C,QAAA,CAAS8C,eAAA,CAAgB5D,YAAY;QAErCc,QAAA,CAAS+C,KAAA,CAAMC,OAAA,CAAQC,KAAA,CAAMC,OAAA,CAAQ,IAAI;QAEzC,IAAIlD,QAAA,CAASmD,SAAA,KAAc,OAAOnD,QAAA,CAASoD,KAAA,CAAO;QAClDpD,QAAA,CAASqD,MAAA,CAAOpD,KAAA,EAAOhB,aAAa;QAEpCe,QAAA,CAASmC,EAAA,CAAGC,OAAA,GAAUF,gBAAA;QACtBlC,QAAA,CAASsC,SAAA,CAAUC,UAAA,GAAaF,uBAAA;QAChCrC,QAAA,CAASyC,WAAA,GAAcD,kBAAA;QAEvB,IAAI,sBAAsBxC,QAAA,EAAUA,QAAA,CAAS2C,gBAAA,GAAmBD,MAAA,GAAS,SAAS,mBAC7E1C,QAAA,CAAS4C,cAAA,GAAiBF,MAAA,GAAS,OAAO;QAE/C1C,QAAA,CAAS8C,eAAA,CAAgBd,mBAAmB;QAI5C,MAAMsB,QAAA,GAAW/F,MAAA,CAAO+F,QAAA;QAExB,IAAIA,QAAA,KAAa,QAAW;UAC1BtD,QAAA,CAAS+C,KAAA,CAAMO,QAAA,CAASA,QAAQ;QACjC;QAED7F,KAAA,CAAMsE,OAAA,GAAU;MACjB;MAED,KAAKE,eAAA,GAAkB,YAAY;QACjC,OAAO/C,YAAA;MACR;MAED,KAAKqE,OAAA,GAAU,YAAY;QACzBrE,YAAA,CAAaqE,OAAA,CAAS;QACtB9F,KAAA,CAAM6B,QAAA,CAASiE,OAAA,CAAS;MACzB;IACF;EACF;EApPD,IAAMC,UAAA,GAANxG,UAAA;EACEyG,aAAA,CADID,UAAA,EACG,mBAAkB;IACvBhE,QAAA,EAAU;MACR9B,KAAA,EAAO;QACLmC,KAAA,EAAO;MACR;MAED6D,QAAA,EAAU;QACR7D,KAAA,EAAO;MACR;MAEDb,aAAA,EAAe;QACba,KAAA,EAAO;MACR;IACF;IAEDD,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAiBzBD,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eA2BlBgE,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;EAGnD;EAuLH,OAAOH,UAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}