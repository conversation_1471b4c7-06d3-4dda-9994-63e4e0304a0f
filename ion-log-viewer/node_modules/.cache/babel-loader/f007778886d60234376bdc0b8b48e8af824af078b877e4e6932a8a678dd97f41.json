{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withClasses(Uint8Array).build();\nexport function Lob(ionType) {\n  return class extends Value(Uint8Array, ionType, _fromJsConstructor) {\n    constructor(data, annotations = []) {\n      super(data);\n      this._setAnnotations(annotations);\n    }\n    uInt8ArrayValue() {\n      return this;\n    }\n    _valueEquals(other, options = {\n      epsilon: null,\n      ignoreAnnotations: false,\n      ignoreTimestampPrecision: false,\n      onlyCompareIon: true\n    }) {\n      let isSupportedType = false;\n      let valueToCompare = null;\n      if (options.onlyCompareIon) {\n        if (other.getType() === IonTypes.CLOB || other.getType() === IonTypes.BLOB) {\n          isSupportedType = true;\n          valueToCompare = other.uInt8ArrayValue();\n        }\n      } else {\n        if (other instanceof Uint8Array) {\n          isSupportedType = true;\n          valueToCompare = other.valueOf();\n        }\n      }\n      if (!isSupportedType) {\n        return false;\n      }\n      let current = this.uInt8ArrayValue();\n      let expected = valueToCompare;\n      if (current.length !== expected.length) {\n        return false;\n      }\n      for (let i = 0; i < current.length; i++) {\n        if (current[i] !== expected[i]) {\n          return false;\n        }\n      }\n      return true;\n    }\n  };\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Value", "_fromJsConstructor", "withClasses", "Uint8Array", "build", "<PERSON><PERSON>", "ionType", "constructor", "data", "annotations", "_setAnnotations", "uInt8ArrayValue", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "getType", "CLOB", "BLOB", "valueOf", "current", "expected", "length", "i"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Lob.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withClasses(Uint8Array)\n    .build();\nexport function Lob(ionType) {\n    return class extends Value(Uint8Array, ionType, _fromJsConstructor) {\n        constructor(data, annotations = []) {\n            super(data);\n            this._setAnnotations(annotations);\n        }\n        uInt8ArrayValue() {\n            return this;\n        }\n        _valueEquals(other, options = {\n            epsilon: null,\n            ignoreAnnotations: false,\n            ignoreTimestampPrecision: false,\n            onlyCompareIon: true,\n        }) {\n            let isSupportedType = false;\n            let valueToCompare = null;\n            if (options.onlyCompareIon) {\n                if (other.getType() === IonTypes.CLOB ||\n                    other.getType() === IonTypes.BLOB) {\n                    isSupportedType = true;\n                    valueToCompare = other.uInt8ArrayValue();\n                }\n            }\n            else {\n                if (other instanceof Uint8Array) {\n                    isSupportedType = true;\n                    valueToCompare = other.valueOf();\n                }\n            }\n            if (!isSupportedType) {\n                return false;\n            }\n            let current = this.uInt8ArrayValue();\n            let expected = valueToCompare;\n            if (current.length !== expected.length) {\n                return false;\n            }\n            for (let i = 0; i < current.length; i++) {\n                if (current[i] !== expected[i]) {\n                    return false;\n                }\n            }\n            return true;\n        }\n    };\n}\n//# sourceMappingURL=Lob.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,QAAS,qBAAqB;AAC/D,SAASC,KAAK,QAAQ,SAAS;AAC/B,MAAMC,kBAAkB,GAAG,IAAIF,wBAAwB,CAAC,CAAC,CACpDG,WAAW,CAACC,UAAU,CAAC,CACvBC,KAAK,CAAC,CAAC;AACZ,OAAO,SAASC,GAAGA,CAACC,OAAO,EAAE;EACzB,OAAO,cAAcN,KAAK,CAACG,UAAU,EAAEG,OAAO,EAAEL,kBAAkB,CAAC,CAAC;IAChEM,WAAWA,CAACC,IAAI,EAAEC,WAAW,GAAG,EAAE,EAAE;MAChC,KAAK,CAACD,IAAI,CAAC;MACX,IAAI,CAACE,eAAe,CAACD,WAAW,CAAC;IACrC;IACAE,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI;IACf;IACAC,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;MAC1BC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,KAAK;MACxBC,wBAAwB,EAAE,KAAK;MAC/BC,cAAc,EAAE;IACpB,CAAC,EAAE;MACC,IAAIC,eAAe,GAAG,KAAK;MAC3B,IAAIC,cAAc,GAAG,IAAI;MACzB,IAAIN,OAAO,CAACI,cAAc,EAAE;QACxB,IAAIL,KAAK,CAACQ,OAAO,CAAC,CAAC,KAAKvB,QAAQ,CAACwB,IAAI,IACjCT,KAAK,CAACQ,OAAO,CAAC,CAAC,KAAKvB,QAAQ,CAACyB,IAAI,EAAE;UACnCJ,eAAe,GAAG,IAAI;UACtBC,cAAc,GAAGP,KAAK,CAACF,eAAe,CAAC,CAAC;QAC5C;MACJ,CAAC,MACI;QACD,IAAIE,KAAK,YAAYV,UAAU,EAAE;UAC7BgB,eAAe,GAAG,IAAI;UACtBC,cAAc,GAAGP,KAAK,CAACW,OAAO,CAAC,CAAC;QACpC;MACJ;MACA,IAAI,CAACL,eAAe,EAAE;QAClB,OAAO,KAAK;MAChB;MACA,IAAIM,OAAO,GAAG,IAAI,CAACd,eAAe,CAAC,CAAC;MACpC,IAAIe,QAAQ,GAAGN,cAAc;MAC7B,IAAIK,OAAO,CAACE,MAAM,KAAKD,QAAQ,CAACC,MAAM,EAAE;QACpC,OAAO,KAAK;MAChB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,IAAIH,OAAO,CAACG,CAAC,CAAC,KAAKF,QAAQ,CAACE,CAAC,CAAC,EAAE;UAC5B,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}