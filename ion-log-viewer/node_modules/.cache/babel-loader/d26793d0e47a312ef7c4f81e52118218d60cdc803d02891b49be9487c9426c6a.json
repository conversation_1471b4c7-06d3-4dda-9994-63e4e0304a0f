{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport class Struct extends Value(Object, IonTypes.STRUCT, FromJsConstructor.NONE) {\n  constructor(fields, annotations = []) {\n    super();\n    this._fields = Object.create(null);\n    for (const [fieldName, fieldValue] of fields) {\n      this._fields[fieldName] = fieldValue instanceof Value ? [fieldValue] : fieldValue;\n    }\n    this._setAnnotations(annotations);\n    return new Proxy(this, {\n      set: function (target, name, value) {\n        if (!(value instanceof Value)) {\n          value = Value.from(value);\n        }\n        target._fields[name] = [value];\n        return true;\n      },\n      get: function (target, name) {\n        if (name in target) {\n          return target[name];\n        }\n        let length = target._fields[name] !== undefined ? target._fields[name].length : -1;\n        if (length === -1) {\n          return target._fields[name];\n        }\n        return target._fields[name][length - 1];\n      },\n      deleteProperty: function (target, name) {\n        if (name in target._fields) {\n          delete target._fields[name];\n        }\n        return true;\n      }\n    });\n  }\n  get(...pathElements) {\n    if (pathElements.length === 0) {\n      throw new Error(\"Value#get requires at least one parameter.\");\n    }\n    const [pathHead, ...pathTail] = pathElements;\n    if (typeof pathHead !== \"string\") {\n      throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);\n    }\n    const child = this._fields[pathHead];\n    if (child === undefined) {\n      return null;\n    }\n    if (pathTail.length === 0) {\n      return child[child.length - 1];\n    }\n    return child[child.length - 1].get(...pathTail);\n  }\n  getAll(...pathElements) {\n    if (pathElements.length === 0) {\n      throw new Error(\"Value#get requires at least one parameter.\");\n    }\n    const [pathHead, ...pathTail] = pathElements;\n    if (typeof pathHead !== \"string\") {\n      throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);\n    }\n    const child = this._fields[pathHead];\n    if (child === undefined) {\n      return null;\n    }\n    if (pathTail.length === 0) {\n      return child;\n    }\n    let values = [];\n    child.forEach(value => values.push(...value.getAll(...pathTail)));\n    return values;\n  }\n  fieldNames() {\n    return Object.keys(this._fields);\n  }\n  allFields() {\n    return Object.entries(this._fields);\n  }\n  fields() {\n    let singleValueFields = Object.create(null);\n    for (const [fieldName, values] of this.allFields()) {\n      singleValueFields[fieldName] = values[values.length - 1];\n    }\n    return Object.entries(singleValueFields);\n  }\n  elements() {\n    return Object.values(this._fields).flat();\n  }\n  [Symbol.iterator]() {\n    return this.fields()[Symbol.iterator]();\n  }\n  toString() {\n    return \"{\" + [...this.allFields()].map(([name, value]) => name + \": \" + value).join(\", \") + \"}\";\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.stepIn(IonTypes.STRUCT);\n    for (const [fieldName, values] of this.allFields()) {\n      for (let value of values) {\n        writer.writeFieldName(fieldName);\n        value.writeTo(writer);\n      }\n    }\n    writer.stepOut();\n  }\n  deleteField(name) {\n    if (name in this._fields) {\n      delete this._fields[name];\n      return true;\n    }\n    return false;\n  }\n  toJSON() {\n    let normalizedFields = Object.create(Struct.prototype);\n    for (const [key, value] of this.fields()) {\n      normalizedFields[key] = value;\n    }\n    return normalizedFields;\n  }\n  static _fromJsValue(jsValue, annotations) {\n    if (!(jsValue instanceof Object)) {\n      throw new Error(`Cannot create a dom.Struct from: ${jsValue.toString()}`);\n    }\n    const fields = Object.entries(jsValue).map(([key, value]) => [key, [Value.from(value)]]);\n    return new this(fields, annotations);\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Struct) {\n      isSupportedType = true;\n      valueToCompare = other.allFields();\n    } else if (!options.onlyCompareIon) {\n      if (typeof other === \"object\" || other instanceof Object) {\n        isSupportedType = true;\n        valueToCompare = Value.from(other).allFields();\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    if (this.allFields().length !== valueToCompare.length) {\n      return false;\n    }\n    let matchFound = true;\n    const paired = new Array(valueToCompare.length);\n    for (let i = 0; matchFound && i < this.allFields().length; i++) {\n      matchFound = false;\n      for (let j = 0; !matchFound && j < valueToCompare.length; j++) {\n        if (!paired[j]) {\n          const child = this.allFields()[i];\n          const expectedChild = valueToCompare[j];\n          matchFound = child[0] === expectedChild[0] && this._ionValueEquals(child[1].sort(), expectedChild[1].sort(), options);\n          if (matchFound) {\n            paired[j] = true;\n          }\n        }\n      }\n    }\n    for (let i = 0; i < paired.length; i++) {\n      if (!paired[i]) {\n        matchFound = false;\n        break;\n      }\n    }\n    return matchFound;\n  }\n  _ionValueEquals(child, expectedChild, options) {\n    if (child.length !== expectedChild.length) {\n      return false;\n    }\n    for (let i = 0; i < child.length; i++) {\n      if (options.onlyCompareIon) {\n        if (!child[i].ionEquals(expectedChild[i], options)) {\n          return false;\n        }\n      } else {\n        if (!child[i].equals(expectedChild[i])) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructor", "Value", "Struct", "Object", "STRUCT", "NONE", "constructor", "fields", "annotations", "_fields", "create", "fieldName", "fieldValue", "_setAnnotations", "Proxy", "set", "target", "name", "value", "from", "get", "length", "undefined", "deleteProperty", "pathElements", "Error", "pathHead", "pathTail", "child", "getAll", "values", "for<PERSON>ach", "push", "fieldNames", "keys", "allFields", "entries", "singleV<PERSON><PERSON>Fields", "elements", "flat", "Symbol", "iterator", "toString", "map", "join", "writeTo", "writer", "setAnnotations", "getAnnotations", "stepIn", "writeFieldName", "stepOut", "deleteField", "toJSON", "normalizedFields", "prototype", "key", "_fromJsValue", "jsValue", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "isSupportedType", "valueToCompare", "matchFound", "paired", "Array", "i", "j", "<PERSON><PERSON><PERSON><PERSON>", "_ionValueEquals", "sort", "ionEquals", "equals"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Struct.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructor } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nexport class Struct extends Value(Object, IonTypes.STRUCT, FromJsConstructor.NONE) {\n    constructor(fields, annotations = []) {\n        super();\n        this._fields = Object.create(null);\n        for (const [fieldName, fieldValue] of fields) {\n            this._fields[fieldName] =\n                fieldValue instanceof Value ? [fieldValue] : fieldValue;\n        }\n        this._setAnnotations(annotations);\n        return new Proxy(this, {\n            set: function (target, name, value) {\n                if (!(value instanceof Value)) {\n                    value = Value.from(value);\n                }\n                target._fields[name] = [value];\n                return true;\n            },\n            get: function (target, name) {\n                if (name in target) {\n                    return target[name];\n                }\n                let length = target._fields[name] !== undefined ? target._fields[name].length : -1;\n                if (length === -1) {\n                    return target._fields[name];\n                }\n                return target._fields[name][length - 1];\n            },\n            deleteProperty: function (target, name) {\n                if (name in target._fields) {\n                    delete target._fields[name];\n                }\n                return true;\n            },\n        });\n    }\n    get(...pathElements) {\n        if (pathElements.length === 0) {\n            throw new Error(\"Value#get requires at least one parameter.\");\n        }\n        const [pathHead, ...pathTail] = pathElements;\n        if (typeof pathHead !== \"string\") {\n            throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);\n        }\n        const child = this._fields[pathHead];\n        if (child === undefined) {\n            return null;\n        }\n        if (pathTail.length === 0) {\n            return child[child.length - 1];\n        }\n        return child[child.length - 1].get(...pathTail);\n    }\n    getAll(...pathElements) {\n        if (pathElements.length === 0) {\n            throw new Error(\"Value#get requires at least one parameter.\");\n        }\n        const [pathHead, ...pathTail] = pathElements;\n        if (typeof pathHead !== \"string\") {\n            throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);\n        }\n        const child = this._fields[pathHead];\n        if (child === undefined) {\n            return null;\n        }\n        if (pathTail.length === 0) {\n            return child;\n        }\n        let values = [];\n        child.forEach((value) => values.push(...value.getAll(...pathTail)));\n        return values;\n    }\n    fieldNames() {\n        return Object.keys(this._fields);\n    }\n    allFields() {\n        return Object.entries(this._fields);\n    }\n    fields() {\n        let singleValueFields = Object.create(null);\n        for (const [fieldName, values] of this.allFields()) {\n            singleValueFields[fieldName] = values[values.length - 1];\n        }\n        return Object.entries(singleValueFields);\n    }\n    elements() {\n        return Object.values(this._fields).flat();\n    }\n    [Symbol.iterator]() {\n        return this.fields()[Symbol.iterator]();\n    }\n    toString() {\n        return (\"{\" +\n            [...this.allFields()]\n                .map(([name, value]) => name + \": \" + value)\n                .join(\", \") +\n            \"}\");\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.stepIn(IonTypes.STRUCT);\n        for (const [fieldName, values] of this.allFields()) {\n            for (let value of values) {\n                writer.writeFieldName(fieldName);\n                value.writeTo(writer);\n            }\n        }\n        writer.stepOut();\n    }\n    deleteField(name) {\n        if (name in this._fields) {\n            delete this._fields[name];\n            return true;\n        }\n        return false;\n    }\n    toJSON() {\n        let normalizedFields = Object.create(Struct.prototype);\n        for (const [key, value] of this.fields()) {\n            normalizedFields[key] = value;\n        }\n        return normalizedFields;\n    }\n    static _fromJsValue(jsValue, annotations) {\n        if (!(jsValue instanceof Object)) {\n            throw new Error(`Cannot create a dom.Struct from: ${jsValue.toString()}`);\n        }\n        const fields = Object.entries(jsValue).map(([key, value]) => [key, [Value.from(value)]]);\n        return new this(fields, annotations);\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Struct) {\n            isSupportedType = true;\n            valueToCompare = other.allFields();\n        }\n        else if (!options.onlyCompareIon) {\n            if (typeof other === \"object\" || other instanceof Object) {\n                isSupportedType = true;\n                valueToCompare = Value.from(other).allFields();\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        if (this.allFields().length !== valueToCompare.length) {\n            return false;\n        }\n        let matchFound = true;\n        const paired = new Array(valueToCompare.length);\n        for (let i = 0; matchFound && i < this.allFields().length; i++) {\n            matchFound = false;\n            for (let j = 0; !matchFound && j < valueToCompare.length; j++) {\n                if (!paired[j]) {\n                    const child = this.allFields()[i];\n                    const expectedChild = valueToCompare[j];\n                    matchFound =\n                        child[0] === expectedChild[0] &&\n                            this._ionValueEquals(child[1].sort(), expectedChild[1].sort(), options);\n                    if (matchFound) {\n                        paired[j] = true;\n                    }\n                }\n            }\n        }\n        for (let i = 0; i < paired.length; i++) {\n            if (!paired[i]) {\n                matchFound = false;\n                break;\n            }\n        }\n        return matchFound;\n    }\n    _ionValueEquals(child, expectedChild, options) {\n        if (child.length !== expectedChild.length) {\n            return false;\n        }\n        for (let i = 0; i < child.length; i++) {\n            if (options.onlyCompareIon) {\n                if (!child[i].ionEquals(expectedChild[i], options)) {\n                    return false;\n                }\n            }\n            else {\n                if (!child[i].equals(expectedChild[i])) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n}\n//# sourceMappingURL=Struct.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,MAAMC,MAAM,SAASD,KAAK,CAACE,MAAM,EAAEJ,QAAQ,CAACK,MAAM,EAAEJ,iBAAiB,CAACK,IAAI,CAAC,CAAC;EAC/EC,WAAWA,CAACC,MAAM,EAAEC,WAAW,GAAG,EAAE,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAGN,MAAM,CAACO,MAAM,CAAC,IAAI,CAAC;IAClC,KAAK,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,IAAIL,MAAM,EAAE;MAC1C,IAAI,CAACE,OAAO,CAACE,SAAS,CAAC,GACnBC,UAAU,YAAYX,KAAK,GAAG,CAACW,UAAU,CAAC,GAAGA,UAAU;IAC/D;IACA,IAAI,CAACC,eAAe,CAACL,WAAW,CAAC;IACjC,OAAO,IAAIM,KAAK,CAAC,IAAI,EAAE;MACnBC,GAAG,EAAE,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;QAChC,IAAI,EAAEA,KAAK,YAAYjB,KAAK,CAAC,EAAE;UAC3BiB,KAAK,GAAGjB,KAAK,CAACkB,IAAI,CAACD,KAAK,CAAC;QAC7B;QACAF,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC,GAAG,CAACC,KAAK,CAAC;QAC9B,OAAO,IAAI;MACf,CAAC;MACDE,GAAG,EAAE,SAAAA,CAAUJ,MAAM,EAAEC,IAAI,EAAE;QACzB,IAAIA,IAAI,IAAID,MAAM,EAAE;UAChB,OAAOA,MAAM,CAACC,IAAI,CAAC;QACvB;QACA,IAAII,MAAM,GAAGL,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC,KAAKK,SAAS,GAAGN,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC,CAACI,MAAM,GAAG,CAAC,CAAC;QAClF,IAAIA,MAAM,KAAK,CAAC,CAAC,EAAE;UACf,OAAOL,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC;QAC/B;QACA,OAAOD,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC,CAACI,MAAM,GAAG,CAAC,CAAC;MAC3C,CAAC;MACDE,cAAc,EAAE,SAAAA,CAAUP,MAAM,EAAEC,IAAI,EAAE;QACpC,IAAIA,IAAI,IAAID,MAAM,CAACP,OAAO,EAAE;UACxB,OAAOO,MAAM,CAACP,OAAO,CAACQ,IAAI,CAAC;QAC/B;QACA,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;EACN;EACAG,GAAGA,CAAC,GAAGI,YAAY,EAAE;IACjB,IAAIA,YAAY,CAACH,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAII,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,MAAM,CAACC,QAAQ,EAAE,GAAGC,QAAQ,CAAC,GAAGH,YAAY;IAC5C,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAID,KAAK,CAAC,qCAAqC,OAAOC,QAAQ,GAAG,CAAC;IAC5E;IACA,MAAME,KAAK,GAAG,IAAI,CAACnB,OAAO,CAACiB,QAAQ,CAAC;IACpC,IAAIE,KAAK,KAAKN,SAAS,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAIK,QAAQ,CAACN,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOO,KAAK,CAACA,KAAK,CAACP,MAAM,GAAG,CAAC,CAAC;IAClC;IACA,OAAOO,KAAK,CAACA,KAAK,CAACP,MAAM,GAAG,CAAC,CAAC,CAACD,GAAG,CAAC,GAAGO,QAAQ,CAAC;EACnD;EACAE,MAAMA,CAAC,GAAGL,YAAY,EAAE;IACpB,IAAIA,YAAY,CAACH,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAII,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,MAAM,CAACC,QAAQ,EAAE,GAAGC,QAAQ,CAAC,GAAGH,YAAY;IAC5C,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAID,KAAK,CAAC,qCAAqC,OAAOC,QAAQ,GAAG,CAAC;IAC5E;IACA,MAAME,KAAK,GAAG,IAAI,CAACnB,OAAO,CAACiB,QAAQ,CAAC;IACpC,IAAIE,KAAK,KAAKN,SAAS,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAIK,QAAQ,CAACN,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOO,KAAK;IAChB;IACA,IAAIE,MAAM,GAAG,EAAE;IACfF,KAAK,CAACG,OAAO,CAAEb,KAAK,IAAKY,MAAM,CAACE,IAAI,CAAC,GAAGd,KAAK,CAACW,MAAM,CAAC,GAAGF,QAAQ,CAAC,CAAC,CAAC;IACnE,OAAOG,MAAM;EACjB;EACAG,UAAUA,CAAA,EAAG;IACT,OAAO9B,MAAM,CAAC+B,IAAI,CAAC,IAAI,CAACzB,OAAO,CAAC;EACpC;EACA0B,SAASA,CAAA,EAAG;IACR,OAAOhC,MAAM,CAACiC,OAAO,CAAC,IAAI,CAAC3B,OAAO,CAAC;EACvC;EACAF,MAAMA,CAAA,EAAG;IACL,IAAI8B,iBAAiB,GAAGlC,MAAM,CAACO,MAAM,CAAC,IAAI,CAAC;IAC3C,KAAK,MAAM,CAACC,SAAS,EAAEmB,MAAM,CAAC,IAAI,IAAI,CAACK,SAAS,CAAC,CAAC,EAAE;MAChDE,iBAAiB,CAAC1B,SAAS,CAAC,GAAGmB,MAAM,CAACA,MAAM,CAACT,MAAM,GAAG,CAAC,CAAC;IAC5D;IACA,OAAOlB,MAAM,CAACiC,OAAO,CAACC,iBAAiB,CAAC;EAC5C;EACAC,QAAQA,CAAA,EAAG;IACP,OAAOnC,MAAM,CAAC2B,MAAM,CAAC,IAAI,CAACrB,OAAO,CAAC,CAAC8B,IAAI,CAAC,CAAC;EAC7C;EACA,CAACC,MAAM,CAACC,QAAQ,IAAI;IAChB,OAAO,IAAI,CAAClC,MAAM,CAAC,CAAC,CAACiC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC3C;EACAC,QAAQA,CAAA,EAAG;IACP,OAAQ,GAAG,GACP,CAAC,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAChBQ,GAAG,CAAC,CAAC,CAAC1B,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,GAAG,IAAI,GAAGC,KAAK,CAAC,CAC3C0B,IAAI,CAAC,IAAI,CAAC,GACf,GAAG;EACX;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,MAAM,CAAClD,QAAQ,CAACK,MAAM,CAAC;IAC9B,KAAK,MAAM,CAACO,SAAS,EAAEmB,MAAM,CAAC,IAAI,IAAI,CAACK,SAAS,CAAC,CAAC,EAAE;MAChD,KAAK,IAAIjB,KAAK,IAAIY,MAAM,EAAE;QACtBgB,MAAM,CAACI,cAAc,CAACvC,SAAS,CAAC;QAChCO,KAAK,CAAC2B,OAAO,CAACC,MAAM,CAAC;MACzB;IACJ;IACAA,MAAM,CAACK,OAAO,CAAC,CAAC;EACpB;EACAC,WAAWA,CAACnC,IAAI,EAAE;IACd,IAAIA,IAAI,IAAI,IAAI,CAACR,OAAO,EAAE;MACtB,OAAO,IAAI,CAACA,OAAO,CAACQ,IAAI,CAAC;MACzB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAoC,MAAMA,CAAA,EAAG;IACL,IAAIC,gBAAgB,GAAGnD,MAAM,CAACO,MAAM,CAACR,MAAM,CAACqD,SAAS,CAAC;IACtD,KAAK,MAAM,CAACC,GAAG,EAAEtC,KAAK,CAAC,IAAI,IAAI,CAACX,MAAM,CAAC,CAAC,EAAE;MACtC+C,gBAAgB,CAACE,GAAG,CAAC,GAAGtC,KAAK;IACjC;IACA,OAAOoC,gBAAgB;EAC3B;EACA,OAAOG,YAAYA,CAACC,OAAO,EAAElD,WAAW,EAAE;IACtC,IAAI,EAAEkD,OAAO,YAAYvD,MAAM,CAAC,EAAE;MAC9B,MAAM,IAAIsB,KAAK,CAAC,oCAAoCiC,OAAO,CAAChB,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC7E;IACA,MAAMnC,MAAM,GAAGJ,MAAM,CAACiC,OAAO,CAACsB,OAAO,CAAC,CAACf,GAAG,CAAC,CAAC,CAACa,GAAG,EAAEtC,KAAK,CAAC,KAAK,CAACsC,GAAG,EAAE,CAACvD,KAAK,CAACkB,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;IACxF,OAAO,IAAI,IAAI,CAACX,MAAM,EAAEC,WAAW,CAAC;EACxC;EACAmD,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE;EACpB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIP,KAAK,YAAY1D,MAAM,EAAE;MACzBgE,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGP,KAAK,CAACzB,SAAS,CAAC,CAAC;IACtC,CAAC,MACI,IAAI,CAAC0B,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYzD,MAAM,EAAE;QACtD+D,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGlE,KAAK,CAACkB,IAAI,CAACyC,KAAK,CAAC,CAACzB,SAAS,CAAC,CAAC;MAClD;IACJ;IACA,IAAI,CAAC+B,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAAC/B,SAAS,CAAC,CAAC,CAACd,MAAM,KAAK8C,cAAc,CAAC9C,MAAM,EAAE;MACnD,OAAO,KAAK;IAChB;IACA,IAAI+C,UAAU,GAAG,IAAI;IACrB,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACH,cAAc,CAAC9C,MAAM,CAAC;IAC/C,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEH,UAAU,IAAIG,CAAC,GAAG,IAAI,CAACpC,SAAS,CAAC,CAAC,CAACd,MAAM,EAAEkD,CAAC,EAAE,EAAE;MAC5DH,UAAU,GAAG,KAAK;MAClB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAE,CAACJ,UAAU,IAAII,CAAC,GAAGL,cAAc,CAAC9C,MAAM,EAAEmD,CAAC,EAAE,EAAE;QAC3D,IAAI,CAACH,MAAM,CAACG,CAAC,CAAC,EAAE;UACZ,MAAM5C,KAAK,GAAG,IAAI,CAACO,SAAS,CAAC,CAAC,CAACoC,CAAC,CAAC;UACjC,MAAME,aAAa,GAAGN,cAAc,CAACK,CAAC,CAAC;UACvCJ,UAAU,GACNxC,KAAK,CAAC,CAAC,CAAC,KAAK6C,aAAa,CAAC,CAAC,CAAC,IACzB,IAAI,CAACC,eAAe,CAAC9C,KAAK,CAAC,CAAC,CAAC,CAAC+C,IAAI,CAAC,CAAC,EAAEF,aAAa,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,EAAEd,OAAO,CAAC;UAC/E,IAAIO,UAAU,EAAE;YACZC,MAAM,CAACG,CAAC,CAAC,GAAG,IAAI;UACpB;QACJ;MACJ;IACJ;IACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAChD,MAAM,EAAEkD,CAAC,EAAE,EAAE;MACpC,IAAI,CAACF,MAAM,CAACE,CAAC,CAAC,EAAE;QACZH,UAAU,GAAG,KAAK;QAClB;MACJ;IACJ;IACA,OAAOA,UAAU;EACrB;EACAM,eAAeA,CAAC9C,KAAK,EAAE6C,aAAa,EAAEZ,OAAO,EAAE;IAC3C,IAAIjC,KAAK,CAACP,MAAM,KAAKoD,aAAa,CAACpD,MAAM,EAAE;MACvC,OAAO,KAAK;IAChB;IACA,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,CAACP,MAAM,EAAEkD,CAAC,EAAE,EAAE;MACnC,IAAIV,OAAO,CAACI,cAAc,EAAE;QACxB,IAAI,CAACrC,KAAK,CAAC2C,CAAC,CAAC,CAACK,SAAS,CAACH,aAAa,CAACF,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;UAChD,OAAO,KAAK;QAChB;MACJ,CAAC,MACI;QACD,IAAI,CAACjC,KAAK,CAAC2C,CAAC,CAAC,CAACM,MAAM,CAACJ,aAAa,CAACF,CAAC,CAAC,CAAC,EAAE;UACpC,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}