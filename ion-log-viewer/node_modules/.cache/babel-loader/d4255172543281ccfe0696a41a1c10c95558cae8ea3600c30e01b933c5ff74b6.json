{"ast": null, "code": "import { REVISION } from 'three';\nconst getVersion = () => parseInt(REVISION.replace(/\\D+/g, ''));\nconst version = /* @__PURE__ */getVersion();\nexport { version };", "map": {"version": 3, "names": ["REVISION", "getVersion", "parseInt", "replace", "version"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@react-three/drei/helpers/constants.js"], "sourcesContent": ["import { REVISION } from 'three';\n\nconst getVersion = () => parseInt(REVISION.replace(/\\D+/g, ''));\nconst version = /* @__PURE__ */getVersion();\n\nexport { version };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,MAAMC,UAAU,GAAGA,CAAA,KAAMC,QAAQ,CAACF,QAAQ,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC/D,MAAMC,OAAO,GAAG,eAAeH,UAAU,CAAC,CAAC;AAE3C,SAASG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}