{"ast": null, "code": "import { DataTextureLoader, LinearMipmapLinearFilter } from \"three\";\nclass TGALoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n  }\n  parse(buffer) {\n    function tgaCheckHeader(header2) {\n      switch (header2.image_type) {\n        case TGA_TYPE_INDEXED:\n        case TGA_TYPE_RLE_INDEXED:\n          if (header2.colormap_length > 256 || header2.colormap_size !== 24 || header2.colormap_type !== 1) {\n            console.error(\"THREE.TGALoader: Invalid type colormap data for indexed type.\");\n          }\n          break;\n        case TGA_TYPE_RGB:\n        case TGA_TYPE_GREY:\n        case TGA_TYPE_RLE_RGB:\n        case TGA_TYPE_RLE_GREY:\n          if (header2.colormap_type) {\n            console.error(\"THREE.TGALoader: Invalid type colormap data for colormap type.\");\n          }\n          break;\n        case TGA_TYPE_NO_DATA:\n          console.error(\"THREE.TGALoader: No data.\");\n        default:\n          console.error('THREE.TGALoader: Invalid type \"%s\".', header2.image_type);\n      }\n      if (header2.width <= 0 || header2.height <= 0) {\n        console.error(\"THREE.TGALoader: Invalid image size.\");\n      }\n      if (header2.pixel_size !== 8 && header2.pixel_size !== 16 && header2.pixel_size !== 24 && header2.pixel_size !== 32) {\n        console.error('THREE.TGALoader: Invalid pixel size \"%s\".', header2.pixel_size);\n      }\n    }\n    function tgaParse(use_rle2, use_pal2, header2, offset2, data) {\n      let pixel_data, palettes;\n      const pixel_size = header2.pixel_size >> 3;\n      const pixel_total = header2.width * header2.height * pixel_size;\n      if (use_pal2) {\n        palettes = data.subarray(offset2, offset2 += header2.colormap_length * (header2.colormap_size >> 3));\n      }\n      if (use_rle2) {\n        pixel_data = new Uint8Array(pixel_total);\n        let c, count, i;\n        let shift = 0;\n        const pixels = new Uint8Array(pixel_size);\n        while (shift < pixel_total) {\n          c = data[offset2++];\n          count = (c & 127) + 1;\n          if (c & 128) {\n            for (i = 0; i < pixel_size; ++i) {\n              pixels[i] = data[offset2++];\n            }\n            for (i = 0; i < count; ++i) {\n              pixel_data.set(pixels, shift + i * pixel_size);\n            }\n            shift += pixel_size * count;\n          } else {\n            count *= pixel_size;\n            for (i = 0; i < count; ++i) {\n              pixel_data[shift + i] = data[offset2++];\n            }\n            shift += count;\n          }\n        }\n      } else {\n        pixel_data = data.subarray(offset2, offset2 += use_pal2 ? header2.width * header2.height : pixel_total);\n      }\n      return {\n        pixel_data,\n        palettes\n      };\n    }\n    function tgaGetImageData8bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image, palettes) {\n      const colormap = palettes;\n      let color,\n        i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i];\n          imageData2[(x + width * y) * 4 + 3] = 255;\n          imageData2[(x + width * y) * 4 + 2] = colormap[color * 3 + 0];\n          imageData2[(x + width * y) * 4 + 1] = colormap[color * 3 + 1];\n          imageData2[(x + width * y) * 4 + 0] = colormap[color * 3 + 2];\n        }\n      }\n      return imageData2;\n    }\n    function tgaGetImageData16bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          color = image[i + 0] + (image[i + 1] << 8);\n          imageData2[(x + width * y) * 4 + 0] = (color & 31744) >> 7;\n          imageData2[(x + width * y) * 4 + 1] = (color & 992) >> 2;\n          imageData2[(x + width * y) * 4 + 2] = (color & 31) >> 3;\n          imageData2[(x + width * y) * 4 + 3] = color & 32768 ? 0 : 255;\n        }\n      }\n      return imageData2;\n    }\n    function tgaGetImageData24bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 3) {\n          imageData2[(x + width * y) * 4 + 3] = 255;\n          imageData2[(x + width * y) * 4 + 2] = image[i + 0];\n          imageData2[(x + width * y) * 4 + 1] = image[i + 1];\n          imageData2[(x + width * y) * 4 + 0] = image[i + 2];\n        }\n      }\n      return imageData2;\n    }\n    function tgaGetImageData32bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 4) {\n          imageData2[(x + width * y) * 4 + 2] = image[i + 0];\n          imageData2[(x + width * y) * 4 + 1] = image[i + 1];\n          imageData2[(x + width * y) * 4 + 0] = image[i + 2];\n          imageData2[(x + width * y) * 4 + 3] = image[i + 3];\n        }\n      }\n      return imageData2;\n    }\n    function tgaGetImageDataGrey8bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i];\n          imageData2[(x + width * y) * 4 + 0] = color;\n          imageData2[(x + width * y) * 4 + 1] = color;\n          imageData2[(x + width * y) * 4 + 2] = color;\n          imageData2[(x + width * y) * 4 + 3] = 255;\n        }\n      }\n      return imageData2;\n    }\n    function tgaGetImageDataGrey16bits(imageData2, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y;\n      const width = header.width;\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          imageData2[(x + width * y) * 4 + 0] = image[i + 0];\n          imageData2[(x + width * y) * 4 + 1] = image[i + 0];\n          imageData2[(x + width * y) * 4 + 2] = image[i + 0];\n          imageData2[(x + width * y) * 4 + 3] = image[i + 1];\n        }\n      }\n      return imageData2;\n    }\n    function getTgaRGBA(data, width, height, image, palette) {\n      let x_start, y_start, x_step, y_step, x_end, y_end;\n      switch ((header.flags & TGA_ORIGIN_MASK) >> TGA_ORIGIN_SHIFT) {\n        default:\n        case TGA_ORIGIN_UL:\n          x_start = 0;\n          x_step = 1;\n          x_end = width;\n          y_start = 0;\n          y_step = 1;\n          y_end = height;\n          break;\n        case TGA_ORIGIN_BL:\n          x_start = 0;\n          x_step = 1;\n          x_end = width;\n          y_start = height - 1;\n          y_step = -1;\n          y_end = -1;\n          break;\n        case TGA_ORIGIN_UR:\n          x_start = width - 1;\n          x_step = -1;\n          x_end = -1;\n          y_start = 0;\n          y_step = 1;\n          y_end = height;\n          break;\n        case TGA_ORIGIN_BR:\n          x_start = width - 1;\n          x_step = -1;\n          x_end = -1;\n          y_start = height - 1;\n          y_step = -1;\n          y_end = -1;\n          break;\n      }\n      if (use_grey) {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageDataGrey8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image);\n            break;\n          case 16:\n            tgaGetImageDataGrey16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image);\n            break;\n          default:\n            console.error(\"THREE.TGALoader: Format not supported.\");\n            break;\n        }\n      } else {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageData8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image, palette);\n            break;\n          case 16:\n            tgaGetImageData16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image);\n            break;\n          case 24:\n            tgaGetImageData24bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image);\n            break;\n          case 32:\n            tgaGetImageData32bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image);\n            break;\n          default:\n            console.error(\"THREE.TGALoader: Format not supported.\");\n            break;\n        }\n      }\n      return data;\n    }\n    const TGA_TYPE_NO_DATA = 0,\n      TGA_TYPE_INDEXED = 1,\n      TGA_TYPE_RGB = 2,\n      TGA_TYPE_GREY = 3,\n      TGA_TYPE_RLE_INDEXED = 9,\n      TGA_TYPE_RLE_RGB = 10,\n      TGA_TYPE_RLE_GREY = 11,\n      TGA_ORIGIN_MASK = 48,\n      TGA_ORIGIN_SHIFT = 4,\n      TGA_ORIGIN_BL = 0,\n      TGA_ORIGIN_BR = 1,\n      TGA_ORIGIN_UL = 2,\n      TGA_ORIGIN_UR = 3;\n    if (buffer.length < 19) console.error(\"THREE.TGALoader: Not enough data to contain header.\");\n    let offset = 0;\n    const content = new Uint8Array(buffer),\n      header = {\n        id_length: content[offset++],\n        colormap_type: content[offset++],\n        image_type: content[offset++],\n        colormap_index: content[offset++] | content[offset++] << 8,\n        colormap_length: content[offset++] | content[offset++] << 8,\n        colormap_size: content[offset++],\n        origin: [content[offset++] | content[offset++] << 8, content[offset++] | content[offset++] << 8],\n        width: content[offset++] | content[offset++] << 8,\n        height: content[offset++] | content[offset++] << 8,\n        pixel_size: content[offset++],\n        flags: content[offset++]\n      };\n    tgaCheckHeader(header);\n    if (header.id_length + offset > buffer.length) {\n      console.error(\"THREE.TGALoader: No data.\");\n    }\n    offset += header.id_length;\n    let use_rle = false,\n      use_pal = false,\n      use_grey = false;\n    switch (header.image_type) {\n      case TGA_TYPE_RLE_INDEXED:\n        use_rle = true;\n        use_pal = true;\n        break;\n      case TGA_TYPE_INDEXED:\n        use_pal = true;\n        break;\n      case TGA_TYPE_RLE_RGB:\n        use_rle = true;\n        break;\n      case TGA_TYPE_RGB:\n        break;\n      case TGA_TYPE_RLE_GREY:\n        use_rle = true;\n        use_grey = true;\n        break;\n      case TGA_TYPE_GREY:\n        use_grey = true;\n        break;\n    }\n    const imageData = new Uint8Array(header.width * header.height * 4);\n    const result = tgaParse(use_rle, use_pal, header, offset, content);\n    getTgaRGBA(imageData, header.width, header.height, result.pixel_data, result.palettes);\n    return {\n      data: imageData,\n      width: header.width,\n      height: header.height,\n      flipY: true,\n      generateMipmaps: true,\n      minFilter: LinearMipmapLinearFilter\n    };\n  }\n}\nexport { TGALoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataTextureLoader", "constructor", "manager", "parse", "buffer", "tgaCheckHeader", "header2", "image_type", "TGA_TYPE_INDEXED", "TGA_TYPE_RLE_INDEXED", "colormap_length", "colormap_size", "colormap_type", "console", "error", "TGA_TYPE_RGB", "TGA_TYPE_GREY", "TGA_TYPE_RLE_RGB", "TGA_TYPE_RLE_GREY", "TGA_TYPE_NO_DATA", "width", "height", "pixel_size", "tgaParse", "use_rle2", "use_pal2", "offset2", "data", "pixel_data", "palettes", "pixel_total", "subarray", "Uint8Array", "c", "count", "i", "shift", "pixels", "set", "tgaGetImageData8bits", "imageData2", "y_start", "y_step", "y_end", "x_start", "x_step", "x_end", "image", "colormap", "color", "x", "y", "header", "tgaGetImageData16bits", "tgaGetImageData24bits", "tgaGetImageData32bits", "tgaGetImageDataGrey8bits", "tgaGetImageDataGrey16bits", "getTgaRGBA", "palette", "flags", "TGA_ORIGIN_MASK", "TGA_ORIGIN_SHIFT", "TGA_ORIGIN_UL", "TGA_ORIGIN_BL", "TGA_ORIGIN_UR", "TGA_ORIGIN_BR", "use_grey", "length", "offset", "content", "id_length", "colormap_index", "origin", "use_rle", "use_pal", "imageData", "result", "flipY", "generateMipmaps", "minFilter", "LinearMipmapLinearFilter"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/TGALoader.js"], "sourcesContent": ["import { DataTextureLoader, LinearMipmapLinearFilter } from 'three'\n\nclass TGALoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer) {\n    // reference from vthibault, https://github.com/vthibault/roBrowser/blob/master/src/Loaders/Targa.js\n\n    function tgaCheckHeader(header) {\n      switch (header.image_type) {\n        // check indexed type\n\n        case TGA_TYPE_INDEXED:\n        case TGA_TYPE_RLE_INDEXED:\n          if (header.colormap_length > 256 || header.colormap_size !== 24 || header.colormap_type !== 1) {\n            console.error('THREE.TGALoader: Invalid type colormap data for indexed type.')\n          }\n\n          break\n\n        // check colormap type\n\n        case TGA_TYPE_RGB:\n        case TGA_TYPE_GREY:\n        case TGA_TYPE_RLE_RGB:\n        case TGA_TYPE_RLE_GREY:\n          if (header.colormap_type) {\n            console.error('THREE.TGALoader: Invalid type colormap data for colormap type.')\n          }\n\n          break\n\n        // What the need of a file without data ?\n\n        case TGA_TYPE_NO_DATA:\n          console.error('THREE.TGALoader: No data.')\n\n        // Invalid type ?\n\n        default:\n          console.error('THREE.TGALoader: Invalid type \"%s\".', header.image_type)\n      }\n\n      // check image width and height\n\n      if (header.width <= 0 || header.height <= 0) {\n        console.error('THREE.TGALoader: Invalid image size.')\n      }\n\n      // check image pixel size\n\n      if (header.pixel_size !== 8 && header.pixel_size !== 16 && header.pixel_size !== 24 && header.pixel_size !== 32) {\n        console.error('THREE.TGALoader: Invalid pixel size \"%s\".', header.pixel_size)\n      }\n    }\n\n    // parse tga image buffer\n\n    function tgaParse(use_rle, use_pal, header, offset, data) {\n      let pixel_data, palettes\n\n      const pixel_size = header.pixel_size >> 3\n      const pixel_total = header.width * header.height * pixel_size\n\n      // read palettes\n\n      if (use_pal) {\n        palettes = data.subarray(offset, (offset += header.colormap_length * (header.colormap_size >> 3)))\n      }\n\n      // read RLE\n\n      if (use_rle) {\n        pixel_data = new Uint8Array(pixel_total)\n\n        let c, count, i\n        let shift = 0\n        const pixels = new Uint8Array(pixel_size)\n\n        while (shift < pixel_total) {\n          c = data[offset++]\n          count = (c & 0x7f) + 1\n\n          // RLE pixels\n\n          if (c & 0x80) {\n            // bind pixel tmp array\n\n            for (i = 0; i < pixel_size; ++i) {\n              pixels[i] = data[offset++]\n            }\n\n            // copy pixel array\n\n            for (i = 0; i < count; ++i) {\n              pixel_data.set(pixels, shift + i * pixel_size)\n            }\n\n            shift += pixel_size * count\n          } else {\n            // raw pixels\n\n            count *= pixel_size\n\n            for (i = 0; i < count; ++i) {\n              pixel_data[shift + i] = data[offset++]\n            }\n\n            shift += count\n          }\n        }\n      } else {\n        // raw pixels\n\n        pixel_data = data.subarray(offset, (offset += use_pal ? header.width * header.height : pixel_total))\n      }\n\n      return {\n        pixel_data: pixel_data,\n        palettes: palettes,\n      }\n    }\n\n    function tgaGetImageData8bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image, palettes) {\n      const colormap = palettes\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i]\n          imageData[(x + width * y) * 4 + 3] = 255\n          imageData[(x + width * y) * 4 + 2] = colormap[color * 3 + 0]\n          imageData[(x + width * y) * 4 + 1] = colormap[color * 3 + 1]\n          imageData[(x + width * y) * 4 + 0] = colormap[color * 3 + 2]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData16bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          color = image[i + 0] + (image[i + 1] << 8) // Inversed ?\n          imageData[(x + width * y) * 4 + 0] = (color & 0x7c00) >> 7\n          imageData[(x + width * y) * 4 + 1] = (color & 0x03e0) >> 2\n          imageData[(x + width * y) * 4 + 2] = (color & 0x001f) >> 3\n          imageData[(x + width * y) * 4 + 3] = color & 0x8000 ? 0 : 255\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData24bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 3) {\n          imageData[(x + width * y) * 4 + 3] = 255\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 1]\n          imageData[(x + width * y) * 4 + 0] = image[i + 2]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData32bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 4) {\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 1]\n          imageData[(x + width * y) * 4 + 0] = image[i + 2]\n          imageData[(x + width * y) * 4 + 3] = image[i + 3]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageDataGrey8bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i]\n          imageData[(x + width * y) * 4 + 0] = color\n          imageData[(x + width * y) * 4 + 1] = color\n          imageData[(x + width * y) * 4 + 2] = color\n          imageData[(x + width * y) * 4 + 3] = 255\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageDataGrey16bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          imageData[(x + width * y) * 4 + 0] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 0]\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 3] = image[i + 1]\n        }\n      }\n\n      return imageData\n    }\n\n    function getTgaRGBA(data, width, height, image, palette) {\n      let x_start, y_start, x_step, y_step, x_end, y_end\n\n      switch ((header.flags & TGA_ORIGIN_MASK) >> TGA_ORIGIN_SHIFT) {\n        default:\n        case TGA_ORIGIN_UL:\n          x_start = 0\n          x_step = 1\n          x_end = width\n          y_start = 0\n          y_step = 1\n          y_end = height\n          break\n\n        case TGA_ORIGIN_BL:\n          x_start = 0\n          x_step = 1\n          x_end = width\n          y_start = height - 1\n          y_step = -1\n          y_end = -1\n          break\n\n        case TGA_ORIGIN_UR:\n          x_start = width - 1\n          x_step = -1\n          x_end = -1\n          y_start = 0\n          y_step = 1\n          y_end = height\n          break\n\n        case TGA_ORIGIN_BR:\n          x_start = width - 1\n          x_step = -1\n          x_end = -1\n          y_start = height - 1\n          y_step = -1\n          y_end = -1\n          break\n      }\n\n      if (use_grey) {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageDataGrey8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 16:\n            tgaGetImageDataGrey16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          default:\n            console.error('THREE.TGALoader: Format not supported.')\n            break\n        }\n      } else {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageData8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image, palette)\n            break\n\n          case 16:\n            tgaGetImageData16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 24:\n            tgaGetImageData24bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 32:\n            tgaGetImageData32bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          default:\n            console.error('THREE.TGALoader: Format not supported.')\n            break\n        }\n      }\n\n      // Load image data according to specific method\n      // let func = 'tgaGetImageData' + (use_grey ? 'Grey' : '') + (header.pixel_size) + 'bits';\n      // func(data, y_start, y_step, y_end, x_start, x_step, x_end, width, image, palette );\n      return data\n    }\n\n    // TGA constants\n\n    const TGA_TYPE_NO_DATA = 0,\n      TGA_TYPE_INDEXED = 1,\n      TGA_TYPE_RGB = 2,\n      TGA_TYPE_GREY = 3,\n      TGA_TYPE_RLE_INDEXED = 9,\n      TGA_TYPE_RLE_RGB = 10,\n      TGA_TYPE_RLE_GREY = 11,\n      TGA_ORIGIN_MASK = 0x30,\n      TGA_ORIGIN_SHIFT = 0x04,\n      TGA_ORIGIN_BL = 0x00,\n      TGA_ORIGIN_BR = 0x01,\n      TGA_ORIGIN_UL = 0x02,\n      TGA_ORIGIN_UR = 0x03\n\n    if (buffer.length < 19) console.error('THREE.TGALoader: Not enough data to contain header.')\n\n    let offset = 0\n\n    const content = new Uint8Array(buffer),\n      header = {\n        id_length: content[offset++],\n        colormap_type: content[offset++],\n        image_type: content[offset++],\n        colormap_index: content[offset++] | (content[offset++] << 8),\n        colormap_length: content[offset++] | (content[offset++] << 8),\n        colormap_size: content[offset++],\n        origin: [content[offset++] | (content[offset++] << 8), content[offset++] | (content[offset++] << 8)],\n        width: content[offset++] | (content[offset++] << 8),\n        height: content[offset++] | (content[offset++] << 8),\n        pixel_size: content[offset++],\n        flags: content[offset++],\n      }\n\n    // check tga if it is valid format\n\n    tgaCheckHeader(header)\n\n    if (header.id_length + offset > buffer.length) {\n      console.error('THREE.TGALoader: No data.')\n    }\n\n    // skip the needn't data\n\n    offset += header.id_length\n\n    // get targa information about RLE compression and palette\n\n    let use_rle = false,\n      use_pal = false,\n      use_grey = false\n\n    switch (header.image_type) {\n      case TGA_TYPE_RLE_INDEXED:\n        use_rle = true\n        use_pal = true\n        break\n\n      case TGA_TYPE_INDEXED:\n        use_pal = true\n        break\n\n      case TGA_TYPE_RLE_RGB:\n        use_rle = true\n        break\n\n      case TGA_TYPE_RGB:\n        break\n\n      case TGA_TYPE_RLE_GREY:\n        use_rle = true\n        use_grey = true\n        break\n\n      case TGA_TYPE_GREY:\n        use_grey = true\n        break\n    }\n\n    //\n\n    const imageData = new Uint8Array(header.width * header.height * 4)\n    const result = tgaParse(use_rle, use_pal, header, offset, content)\n    getTgaRGBA(imageData, header.width, header.height, result.pixel_data, result.palettes)\n\n    return {\n      data: imageData,\n      width: header.width,\n      height: header.height,\n      flipY: true,\n      generateMipmaps: true,\n      minFilter: LinearMipmapLinearFilter,\n    }\n  }\n}\n\nexport { TGALoader }\n"], "mappings": ";AAEA,MAAMA,SAAA,SAAkBC,iBAAA,CAAkB;EACxCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,MAAMC,MAAA,EAAQ;IAGZ,SAASC,eAAeC,OAAA,EAAQ;MAC9B,QAAQA,OAAA,CAAOC,UAAA;QAGb,KAAKC,gBAAA;QACL,KAAKC,oBAAA;UACH,IAAIH,OAAA,CAAOI,eAAA,GAAkB,OAAOJ,OAAA,CAAOK,aAAA,KAAkB,MAAML,OAAA,CAAOM,aAAA,KAAkB,GAAG;YAC7FC,OAAA,CAAQC,KAAA,CAAM,+DAA+D;UAC9E;UAED;QAIF,KAAKC,YAAA;QACL,KAAKC,aAAA;QACL,KAAKC,gBAAA;QACL,KAAKC,iBAAA;UACH,IAAIZ,OAAA,CAAOM,aAAA,EAAe;YACxBC,OAAA,CAAQC,KAAA,CAAM,gEAAgE;UAC/E;UAED;QAIF,KAAKK,gBAAA;UACHN,OAAA,CAAQC,KAAA,CAAM,2BAA2B;QAI3C;UACED,OAAA,CAAQC,KAAA,CAAM,uCAAuCR,OAAA,CAAOC,UAAU;MACzE;MAID,IAAID,OAAA,CAAOc,KAAA,IAAS,KAAKd,OAAA,CAAOe,MAAA,IAAU,GAAG;QAC3CR,OAAA,CAAQC,KAAA,CAAM,sCAAsC;MACrD;MAID,IAAIR,OAAA,CAAOgB,UAAA,KAAe,KAAKhB,OAAA,CAAOgB,UAAA,KAAe,MAAMhB,OAAA,CAAOgB,UAAA,KAAe,MAAMhB,OAAA,CAAOgB,UAAA,KAAe,IAAI;QAC/GT,OAAA,CAAQC,KAAA,CAAM,6CAA6CR,OAAA,CAAOgB,UAAU;MAC7E;IACF;IAID,SAASC,SAASC,QAAA,EAASC,QAAA,EAASnB,OAAA,EAAQoB,OAAA,EAAQC,IAAA,EAAM;MACxD,IAAIC,UAAA,EAAYC,QAAA;MAEhB,MAAMP,UAAA,GAAahB,OAAA,CAAOgB,UAAA,IAAc;MACxC,MAAMQ,WAAA,GAAcxB,OAAA,CAAOc,KAAA,GAAQd,OAAA,CAAOe,MAAA,GAASC,UAAA;MAInD,IAAIG,QAAA,EAAS;QACXI,QAAA,GAAWF,IAAA,CAAKI,QAAA,CAASL,OAAA,EAASA,OAAA,IAAUpB,OAAA,CAAOI,eAAA,IAAmBJ,OAAA,CAAOK,aAAA,IAAiB,EAAI;MACnG;MAID,IAAIa,QAAA,EAAS;QACXI,UAAA,GAAa,IAAII,UAAA,CAAWF,WAAW;QAEvC,IAAIG,CAAA,EAAGC,KAAA,EAAOC,CAAA;QACd,IAAIC,KAAA,GAAQ;QACZ,MAAMC,MAAA,GAAS,IAAIL,UAAA,CAAWV,UAAU;QAExC,OAAOc,KAAA,GAAQN,WAAA,EAAa;UAC1BG,CAAA,GAAIN,IAAA,CAAKD,OAAA,EAAQ;UACjBQ,KAAA,IAASD,CAAA,GAAI,OAAQ;UAIrB,IAAIA,CAAA,GAAI,KAAM;YAGZ,KAAKE,CAAA,GAAI,GAAGA,CAAA,GAAIb,UAAA,EAAY,EAAEa,CAAA,EAAG;cAC/BE,MAAA,CAAOF,CAAC,IAAIR,IAAA,CAAKD,OAAA,EAAQ;YAC1B;YAID,KAAKS,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,EAAO,EAAEC,CAAA,EAAG;cAC1BP,UAAA,CAAWU,GAAA,CAAID,MAAA,EAAQD,KAAA,GAAQD,CAAA,GAAIb,UAAU;YAC9C;YAEDc,KAAA,IAASd,UAAA,GAAaY,KAAA;UAClC,OAAiB;YAGLA,KAAA,IAASZ,UAAA;YAET,KAAKa,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,EAAO,EAAEC,CAAA,EAAG;cAC1BP,UAAA,CAAWQ,KAAA,GAAQD,CAAC,IAAIR,IAAA,CAAKD,OAAA,EAAQ;YACtC;YAEDU,KAAA,IAASF,KAAA;UACV;QACF;MACT,OAAa;QAGLN,UAAA,GAAaD,IAAA,CAAKI,QAAA,CAASL,OAAA,EAASA,OAAA,IAAUD,QAAA,GAAUnB,OAAA,CAAOc,KAAA,GAAQd,OAAA,CAAOe,MAAA,GAASS,WAAa;MACrG;MAED,OAAO;QACLF,UAAA;QACAC;MACD;IACF;IAED,SAASU,qBAAqBC,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAOlB,QAAA,EAAU;MACxG,MAAMmB,QAAA,GAAWnB,QAAA;MACjB,IAAIoB,KAAA;QACFd,CAAA,GAAI;QACJe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK;UAC/Cc,KAAA,GAAQF,KAAA,CAAMZ,CAAC;UACfK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAI;UACrCX,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIH,QAAA,CAASC,KAAA,GAAQ,IAAI,CAAC;UAC3DT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIH,QAAA,CAASC,KAAA,GAAQ,IAAI,CAAC;UAC3DT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIH,QAAA,CAASC,KAAA,GAAQ,IAAI,CAAC;QAC5D;MACF;MAED,OAAOT,UAAA;IACR;IAED,SAASa,sBAAsBb,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAO;MAC/F,IAAIE,KAAA;QACFd,CAAA,GAAI;QACJe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK,GAAG;UAClDc,KAAA,GAAQF,KAAA,CAAMZ,CAAA,GAAI,CAAC,KAAKY,KAAA,CAAMZ,CAAA,GAAI,CAAC,KAAK;UACxCK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,KAAKF,KAAA,GAAQ,UAAW;UACzDT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,KAAKF,KAAA,GAAQ,QAAW;UACzDT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,KAAKF,KAAA,GAAQ,OAAW;UACzDT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIF,KAAA,GAAQ,QAAS,IAAI;QAC3D;MACF;MAED,OAAOT,UAAA;IACR;IAED,SAASc,sBAAsBd,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAO;MAC/F,IAAIZ,CAAA,GAAI;QACNe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK,GAAG;UAClDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAI;UACrCX,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;QACjD;MACF;MAED,OAAOK,UAAA;IACR;IAED,SAASe,sBAAsBf,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAO;MAC/F,IAAIZ,CAAA,GAAI;QACNe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK,GAAG;UAClDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;QACjD;MACF;MAED,OAAOK,UAAA;IACR;IAED,SAASgB,yBAAyBhB,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAO;MAClG,IAAIE,KAAA;QACFd,CAAA,GAAI;QACJe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK;UAC/Cc,KAAA,GAAQF,KAAA,CAAMZ,CAAC;UACfK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIF,KAAA;UACrCT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIF,KAAA;UACrCT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIF,KAAA;UACrCT,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAI;QACtC;MACF;MAED,OAAOX,UAAA;IACR;IAED,SAASiB,0BAA0BjB,UAAA,EAAWC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAO;MACnG,IAAIZ,CAAA,GAAI;QACNe,CAAA;QACAC,CAAA;MACF,MAAM/B,KAAA,GAAQgC,MAAA,CAAOhC,KAAA;MAErB,KAAK+B,CAAA,GAAIV,OAAA,EAASU,CAAA,KAAMR,KAAA,EAAOQ,CAAA,IAAKT,MAAA,EAAQ;QAC1C,KAAKQ,CAAA,GAAIN,OAAA,EAASM,CAAA,KAAMJ,KAAA,EAAOI,CAAA,IAAKL,MAAA,EAAQV,CAAA,IAAK,GAAG;UAClDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;UAChDK,UAAA,EAAWU,CAAA,GAAI9B,KAAA,GAAQ+B,CAAA,IAAK,IAAI,CAAC,IAAIJ,KAAA,CAAMZ,CAAA,GAAI,CAAC;QACjD;MACF;MAED,OAAOK,UAAA;IACR;IAED,SAASkB,WAAW/B,IAAA,EAAMP,KAAA,EAAOC,MAAA,EAAQ0B,KAAA,EAAOY,OAAA,EAAS;MACvD,IAAIf,OAAA,EAASH,OAAA,EAASI,MAAA,EAAQH,MAAA,EAAQI,KAAA,EAAOH,KAAA;MAE7C,SAASS,MAAA,CAAOQ,KAAA,GAAQC,eAAA,KAAoBC,gBAAA;QAC1C;QACA,KAAKC,aAAA;UACHnB,OAAA,GAAU;UACVC,MAAA,GAAS;UACTC,KAAA,GAAQ1B,KAAA;UACRqB,OAAA,GAAU;UACVC,MAAA,GAAS;UACTC,KAAA,GAAQtB,MAAA;UACR;QAEF,KAAK2C,aAAA;UACHpB,OAAA,GAAU;UACVC,MAAA,GAAS;UACTC,KAAA,GAAQ1B,KAAA;UACRqB,OAAA,GAAUpB,MAAA,GAAS;UACnBqB,MAAA,GAAS;UACTC,KAAA,GAAQ;UACR;QAEF,KAAKsB,aAAA;UACHrB,OAAA,GAAUxB,KAAA,GAAQ;UAClByB,MAAA,GAAS;UACTC,KAAA,GAAQ;UACRL,OAAA,GAAU;UACVC,MAAA,GAAS;UACTC,KAAA,GAAQtB,MAAA;UACR;QAEF,KAAK6C,aAAA;UACHtB,OAAA,GAAUxB,KAAA,GAAQ;UAClByB,MAAA,GAAS;UACTC,KAAA,GAAQ;UACRL,OAAA,GAAUpB,MAAA,GAAS;UACnBqB,MAAA,GAAS;UACTC,KAAA,GAAQ;UACR;MACH;MAED,IAAIwB,QAAA,EAAU;QACZ,QAAQf,MAAA,CAAO9B,UAAA;UACb,KAAK;YACHkC,wBAAA,CAAyB7B,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAK;YACpF;UAEF,KAAK;YACHU,yBAAA,CAA0B9B,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAK;YACrF;UAEF;YACElC,OAAA,CAAQC,KAAA,CAAM,wCAAwC;YACtD;QACH;MACT,OAAa;QACL,QAAQsC,MAAA,CAAO9B,UAAA;UACb,KAAK;YACHiB,oBAAA,CAAqBZ,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAA,EAAOY,OAAO;YACzF;UAEF,KAAK;YACHN,qBAAA,CAAsB1B,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAK;YACjF;UAEF,KAAK;YACHO,qBAAA,CAAsB3B,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAK;YACjF;UAEF,KAAK;YACHQ,qBAAA,CAAsB5B,IAAA,EAAMc,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,OAAA,EAASC,MAAA,EAAQC,KAAA,EAAOC,KAAK;YACjF;UAEF;YACElC,OAAA,CAAQC,KAAA,CAAM,wCAAwC;YACtD;QACH;MACF;MAKD,OAAOa,IAAA;IACR;IAID,MAAMR,gBAAA,GAAmB;MACvBX,gBAAA,GAAmB;MACnBO,YAAA,GAAe;MACfC,aAAA,GAAgB;MAChBP,oBAAA,GAAuB;MACvBQ,gBAAA,GAAmB;MACnBC,iBAAA,GAAoB;MACpB2C,eAAA,GAAkB;MAClBC,gBAAA,GAAmB;MACnBE,aAAA,GAAgB;MAChBE,aAAA,GAAgB;MAChBH,aAAA,GAAgB;MAChBE,aAAA,GAAgB;IAElB,IAAI7D,MAAA,CAAOgE,MAAA,GAAS,IAAIvD,OAAA,CAAQC,KAAA,CAAM,qDAAqD;IAE3F,IAAIuD,MAAA,GAAS;IAEb,MAAMC,OAAA,GAAU,IAAItC,UAAA,CAAW5B,MAAM;MACnCgD,MAAA,GAAS;QACPmB,SAAA,EAAWD,OAAA,CAAQD,MAAA,EAAQ;QAC3BzD,aAAA,EAAe0D,OAAA,CAAQD,MAAA,EAAQ;QAC/B9D,UAAA,EAAY+D,OAAA,CAAQD,MAAA,EAAQ;QAC5BG,cAAA,EAAgBF,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK;QAC1D3D,eAAA,EAAiB4D,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK;QAC3D1D,aAAA,EAAe2D,OAAA,CAAQD,MAAA,EAAQ;QAC/BI,MAAA,EAAQ,CAACH,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK,GAAIC,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK,CAAE;QACnGjD,KAAA,EAAOkD,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK;QACjDhD,MAAA,EAAQiD,OAAA,CAAQD,MAAA,EAAQ,IAAKC,OAAA,CAAQD,MAAA,EAAQ,KAAK;QAClD/C,UAAA,EAAYgD,OAAA,CAAQD,MAAA,EAAQ;QAC5BT,KAAA,EAAOU,OAAA,CAAQD,MAAA,EAAQ;MACxB;IAIHhE,cAAA,CAAe+C,MAAM;IAErB,IAAIA,MAAA,CAAOmB,SAAA,GAAYF,MAAA,GAASjE,MAAA,CAAOgE,MAAA,EAAQ;MAC7CvD,OAAA,CAAQC,KAAA,CAAM,2BAA2B;IAC1C;IAIDuD,MAAA,IAAUjB,MAAA,CAAOmB,SAAA;IAIjB,IAAIG,OAAA,GAAU;MACZC,OAAA,GAAU;MACVR,QAAA,GAAW;IAEb,QAAQf,MAAA,CAAO7C,UAAA;MACb,KAAKE,oBAAA;QACHiE,OAAA,GAAU;QACVC,OAAA,GAAU;QACV;MAEF,KAAKnE,gBAAA;QACHmE,OAAA,GAAU;QACV;MAEF,KAAK1D,gBAAA;QACHyD,OAAA,GAAU;QACV;MAEF,KAAK3D,YAAA;QACH;MAEF,KAAKG,iBAAA;QACHwD,OAAA,GAAU;QACVP,QAAA,GAAW;QACX;MAEF,KAAKnD,aAAA;QACHmD,QAAA,GAAW;QACX;IACH;IAID,MAAMS,SAAA,GAAY,IAAI5C,UAAA,CAAWoB,MAAA,CAAOhC,KAAA,GAAQgC,MAAA,CAAO/B,MAAA,GAAS,CAAC;IACjE,MAAMwD,MAAA,GAAStD,QAAA,CAASmD,OAAA,EAASC,OAAA,EAASvB,MAAA,EAAQiB,MAAA,EAAQC,OAAO;IACjEZ,UAAA,CAAWkB,SAAA,EAAWxB,MAAA,CAAOhC,KAAA,EAAOgC,MAAA,CAAO/B,MAAA,EAAQwD,MAAA,CAAOjD,UAAA,EAAYiD,MAAA,CAAOhD,QAAQ;IAErF,OAAO;MACLF,IAAA,EAAMiD,SAAA;MACNxD,KAAA,EAAOgC,MAAA,CAAOhC,KAAA;MACdC,MAAA,EAAQ+B,MAAA,CAAO/B,MAAA;MACfyD,KAAA,EAAO;MACPC,eAAA,EAAiB;MACjBC,SAAA,EAAWC;IACZ;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}