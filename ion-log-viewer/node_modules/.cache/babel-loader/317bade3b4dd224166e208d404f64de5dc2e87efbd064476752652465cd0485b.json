{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst offsetMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst AxisArrow = ({\n  direction,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offset0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    clickInfo.current = {\n      clickPoint,\n      dir\n    };\n    offset0.current = translation.current[axis];\n    onDragStart({\n      component: 'Arrow',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, direction, camControls, onDragStart, translation, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir\n      } = clickInfo.current;\n      const [min, max] = (translationLimits == null ? void 0 : translationLimits[axis]) || [undefined, undefined];\n      let offset = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      if (min !== undefined) {\n        offset = Math.max(offset, min - offset0.current);\n      }\n      if (max !== undefined) {\n        offset = Math.min(offset, max - offset0.current);\n      }\n      translation.current[axis] = offset0.current + offset;\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      }\n      offsetMatrix.makeTranslation(dir.x * offset, dir.y * offset, dir.z * offset);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    cylinderLength,\n    coneWidth,\n    coneLength,\n    matrixL\n  } = React.useMemo(() => {\n    const coneWidth = fixed ? lineWidth / scale * 1.6 : scale / 20;\n    const coneLength = fixed ? 0.2 : scale / 5;\n    const cylinderLength = fixed ? 1 - coneLength : scale - coneLength;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      cylinderLength,\n      coneWidth,\n      coneLength,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color_ = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, -coneLength, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: false,\n    position: [0, (cylinderLength + coneLength) / 2.0, 0],\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"cylinderGeometry\", {\n    args: [coneWidth * 1.4, coneWidth * 1.4, cylinderLength + coneLength, 8, 1]\n  })), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: [0, 0, 0, 0, cylinderLength, 0],\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    renderOrder: 1,\n    polygonOffsetFactor: -10,\n    fog: false\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    raycast: () => null,\n    position: [0, cylinderLength + coneLength / 2.0, 0],\n    renderOrder: 500\n  }, /*#__PURE__*/React.createElement(\"coneGeometry\", {\n    args: [coneWidth, coneLength, 24, 1]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }))));\n};\nexport { AxisArrow, calculateOffset };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "Line", "Html", "context", "vec1", "Vector3", "vec2", "calculateOffset", "clickPoint", "normal", "rayStart", "rayDir", "e1", "dot", "e2", "e3", "copy", "multiplyScalar", "sub", "add", "offset", "upV", "offsetMatrix", "Matrix4", "AxisArrow", "direction", "axis", "translation", "translationLimits", "annotations", "annotationsClass", "depthTest", "scale", "lineWidth", "fixed", "axisColors", "hoveredColor", "opacity", "onDragStart", "onDrag", "onDragEnd", "userData", "useContext", "camControls", "state", "controls", "divRef", "useRef", "objRef", "clickInfo", "offset0", "isHovered", "setIsHovered", "useState", "onPointerDown", "useCallback", "e", "current", "innerText", "toFixed", "style", "display", "stopPropagation", "rotation", "extractRotation", "matrixWorld", "point", "clone", "origin", "setFromMatrixPosition", "dir", "applyMatrix4", "normalize", "component", "directions", "enabled", "target", "setPointerCapture", "pointerId", "onPointerMove", "min", "max", "undefined", "ray", "Math", "makeTranslation", "x", "y", "z", "onPointerUp", "releasePointerCapture", "onPointerOut", "cylinderLength", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "matrixL", "useMemo", "quaternion", "Quaternion", "setFromUnitVectors", "makeRotationFromQuaternion", "color_", "createElement", "ref", "matrix", "matrixAutoUpdate", "position", "background", "color", "padding", "borderRadius", "whiteSpace", "className", "visible", "args", "transparent", "raycast", "points", "side", "DoubleSide", "polygonOffset", "renderOrder", "polygonOffsetFactor", "fog"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@react-three/drei/web/pivotControls/AxisArrow.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst offsetMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst AxisArrow = ({\n  direction,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offset0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    clickInfo.current = {\n      clickPoint,\n      dir\n    };\n    offset0.current = translation.current[axis];\n    onDragStart({\n      component: 'Arrow',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, direction, camControls, onDragStart, translation, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir\n      } = clickInfo.current;\n      const [min, max] = (translationLimits == null ? void 0 : translationLimits[axis]) || [undefined, undefined];\n      let offset = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      if (min !== undefined) {\n        offset = Math.max(offset, min - offset0.current);\n      }\n      if (max !== undefined) {\n        offset = Math.min(offset, max - offset0.current);\n      }\n      translation.current[axis] = offset0.current + offset;\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      }\n      offsetMatrix.makeTranslation(dir.x * offset, dir.y * offset, dir.z * offset);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    cylinderLength,\n    coneWidth,\n    coneLength,\n    matrixL\n  } = React.useMemo(() => {\n    const coneWidth = fixed ? lineWidth / scale * 1.6 : scale / 20;\n    const coneLength = fixed ? 0.2 : scale / 5;\n    const cylinderLength = fixed ? 1 - coneLength : scale - coneLength;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      cylinderLength,\n      coneWidth,\n      coneLength,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color_ = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, -coneLength, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: false,\n    position: [0, (cylinderLength + coneLength) / 2.0, 0],\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"cylinderGeometry\", {\n    args: [coneWidth * 1.4, coneWidth * 1.4, cylinderLength + coneLength, 8, 1]\n  })), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: [0, 0, 0, 0, cylinderLength, 0],\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    renderOrder: 1,\n    polygonOffsetFactor: -10,\n    fog: false\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    raycast: () => null,\n    position: [0, cylinderLength + coneLength / 2.0, 0],\n    renderOrder: 500\n  }, /*#__PURE__*/React.createElement(\"coneGeometry\", {\n    args: [coneWidth, coneLength, 24, 1]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }))));\n};\n\nexport { AxisArrow, calculateOffset };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,cAAc;AAEtC,MAAMC,IAAI,GAAG,eAAe,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC;AAC/C,MAAMC,IAAI,GAAG,eAAe,IAAIP,KAAK,CAACM,OAAO,CAAC,CAAC;AAC/C,MAAME,eAAe,GAAGA,CAACC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAChE,MAAMC,EAAE,GAAGH,MAAM,CAACI,GAAG,CAACJ,MAAM,CAAC;EAC7B,MAAMK,EAAE,GAAGL,MAAM,CAACI,GAAG,CAACL,UAAU,CAAC,GAAGC,MAAM,CAACI,GAAG,CAACH,QAAQ,CAAC;EACxD,MAAMK,EAAE,GAAGN,MAAM,CAACI,GAAG,CAACF,MAAM,CAAC;EAC7B,IAAII,EAAE,KAAK,CAAC,EAAE;IACZ,OAAO,CAACD,EAAE,GAAGF,EAAE;EACjB;EACAR,IAAI,CAACY,IAAI,CAACL,MAAM,CAAC,CAACM,cAAc,CAACL,EAAE,GAAGG,EAAE,CAAC,CAACG,GAAG,CAACT,MAAM,CAAC;EACrDH,IAAI,CAACU,IAAI,CAACL,MAAM,CAAC,CAACM,cAAc,CAACH,EAAE,GAAGC,EAAE,CAAC,CAACI,GAAG,CAACT,QAAQ,CAAC,CAACQ,GAAG,CAACV,UAAU,CAAC;EACvE,MAAMY,MAAM,GAAG,CAAChB,IAAI,CAACS,GAAG,CAACP,IAAI,CAAC,GAAGF,IAAI,CAACS,GAAG,CAACT,IAAI,CAAC;EAC/C,OAAOgB,MAAM;AACf,CAAC;AACD,MAAMC,GAAG,GAAG,eAAe,IAAItB,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrD,MAAMiB,YAAY,GAAG,eAAe,IAAIvB,KAAK,CAACwB,OAAO,CAAC,CAAC;AACvD,MAAMC,SAAS,GAAGA,CAAC;EACjBC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,WAAW;IACXC,iBAAiB;IACjBC,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY;IACZC,OAAO;IACPC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAG3C,KAAK,CAAC4C,UAAU,CAACvC,OAAO,CAAC;EAC7B,MAAMwC,WAAW,GAAG3C,QAAQ,CAAC4C,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACrD,MAAMC,MAAM,GAAGhD,KAAK,CAACiD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,MAAM,GAAGlD,KAAK,CAACiD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,SAAS,GAAGnD,KAAK,CAACiD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMG,OAAO,GAAGpD,KAAK,CAACiD,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGtD,KAAK,CAACuD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMC,aAAa,GAAGxD,KAAK,CAACyD,WAAW,CAACC,CAAC,IAAI;IAC3C,IAAI3B,WAAW,EAAE;MACfiB,MAAM,CAACW,OAAO,CAACC,SAAS,GAAG,GAAG/B,WAAW,CAAC8B,OAAO,CAAC/B,IAAI,CAAC,CAACiC,OAAO,CAAC,CAAC,CAAC,EAAE;MACpEb,MAAM,CAACW,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IACxC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,MAAMC,QAAQ,GAAG,IAAIhE,KAAK,CAACwB,OAAO,CAAC,CAAC,CAACyC,eAAe,CAAChB,MAAM,CAACS,OAAO,CAACQ,WAAW,CAAC;IAChF,MAAMzD,UAAU,GAAGgD,CAAC,CAACU,KAAK,CAACC,KAAK,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG,IAAIrE,KAAK,CAACM,OAAO,CAAC,CAAC,CAACgE,qBAAqB,CAACrB,MAAM,CAACS,OAAO,CAACQ,WAAW,CAAC;IACpF,MAAMK,GAAG,GAAG7C,SAAS,CAAC0C,KAAK,CAAC,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC,CAACS,SAAS,CAAC,CAAC;IAChEvB,SAAS,CAACQ,OAAO,GAAG;MAClBjD,UAAU;MACV8D;IACF,CAAC;IACDpB,OAAO,CAACO,OAAO,GAAG9B,WAAW,CAAC8B,OAAO,CAAC/B,IAAI,CAAC;IAC3CY,WAAW,CAAC;MACVmC,SAAS,EAAE,OAAO;MAClB/C,IAAI;MACJ0C,MAAM;MACNM,UAAU,EAAE,CAACJ,GAAG;IAClB,CAAC,CAAC;IACF3B,WAAW,KAAKA,WAAW,CAACgC,OAAO,GAAG,KAAK,CAAC;IAC5C;IACAnB,CAAC,CAACoB,MAAM,CAACC,iBAAiB,CAACrB,CAAC,CAACsB,SAAS,CAAC;EACzC,CAAC,EAAE,CAACjD,WAAW,EAAEJ,SAAS,EAAEkB,WAAW,EAAEL,WAAW,EAAEX,WAAW,EAAED,IAAI,CAAC,CAAC;EACzE,MAAMqD,aAAa,GAAGjF,KAAK,CAACyD,WAAW,CAACC,CAAC,IAAI;IAC3CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,IAAI,CAACX,SAAS,EAAEC,YAAY,CAAC,IAAI,CAAC;IAClC,IAAIH,SAAS,CAACQ,OAAO,EAAE;MACrB,MAAM;QACJjD,UAAU;QACV8D;MACF,CAAC,GAAGrB,SAAS,CAACQ,OAAO;MACrB,MAAM,CAACuB,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACrD,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACF,IAAI,CAAC,KAAK,CAACwD,SAAS,EAAEA,SAAS,CAAC;MAC3G,IAAI9D,MAAM,GAAGb,eAAe,CAACC,UAAU,EAAE8D,GAAG,EAAEd,CAAC,CAAC2B,GAAG,CAACf,MAAM,EAAEZ,CAAC,CAAC2B,GAAG,CAAC1D,SAAS,CAAC;MAC5E,IAAIuD,GAAG,KAAKE,SAAS,EAAE;QACrB9D,MAAM,GAAGgE,IAAI,CAACH,GAAG,CAAC7D,MAAM,EAAE4D,GAAG,GAAG9B,OAAO,CAACO,OAAO,CAAC;MAClD;MACA,IAAIwB,GAAG,KAAKC,SAAS,EAAE;QACrB9D,MAAM,GAAGgE,IAAI,CAACJ,GAAG,CAAC5D,MAAM,EAAE6D,GAAG,GAAG/B,OAAO,CAACO,OAAO,CAAC;MAClD;MACA9B,WAAW,CAAC8B,OAAO,CAAC/B,IAAI,CAAC,GAAGwB,OAAO,CAACO,OAAO,GAAGrC,MAAM;MACpD,IAAIS,WAAW,EAAE;QACfiB,MAAM,CAACW,OAAO,CAACC,SAAS,GAAG,GAAG/B,WAAW,CAAC8B,OAAO,CAAC/B,IAAI,CAAC,CAACiC,OAAO,CAAC,CAAC,CAAC,EAAE;MACtE;MACArC,YAAY,CAAC+D,eAAe,CAACf,GAAG,CAACgB,CAAC,GAAGlE,MAAM,EAAEkD,GAAG,CAACiB,CAAC,GAAGnE,MAAM,EAAEkD,GAAG,CAACkB,CAAC,GAAGpE,MAAM,CAAC;MAC5EmB,MAAM,CAACjB,YAAY,CAAC;IACtB;EACF,CAAC,EAAE,CAACO,WAAW,EAAEU,MAAM,EAAEY,SAAS,EAAExB,WAAW,EAAEC,iBAAiB,EAAEF,IAAI,CAAC,CAAC;EAC1E,MAAM+D,WAAW,GAAG3F,KAAK,CAACyD,WAAW,CAACC,CAAC,IAAI;IACzC,IAAI3B,WAAW,EAAE;MACfiB,MAAM,CAACW,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IACvC;IACAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBb,SAAS,CAACQ,OAAO,GAAG,IAAI;IACxBjB,SAAS,CAAC,CAAC;IACXG,WAAW,KAAKA,WAAW,CAACgC,OAAO,GAAG,IAAI,CAAC;IAC3C;IACAnB,CAAC,CAACoB,MAAM,CAACc,qBAAqB,CAAClC,CAAC,CAACsB,SAAS,CAAC;EAC7C,CAAC,EAAE,CAACjD,WAAW,EAAEc,WAAW,EAAEH,SAAS,CAAC,CAAC;EACzC,MAAMmD,YAAY,GAAG7F,KAAK,CAACyD,WAAW,CAACC,CAAC,IAAI;IAC1CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBV,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,MAAM;IACJwC,cAAc;IACdC,SAAS;IACTC,UAAU;IACVC;EACF,CAAC,GAAGjG,KAAK,CAACkG,OAAO,CAAC,MAAM;IACtB,MAAMH,SAAS,GAAG3D,KAAK,GAAGD,SAAS,GAAGD,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,EAAE;IAC9D,MAAM8D,UAAU,GAAG5D,KAAK,GAAG,GAAG,GAAGF,KAAK,GAAG,CAAC;IAC1C,MAAM4D,cAAc,GAAG1D,KAAK,GAAG,CAAC,GAAG4D,UAAU,GAAG9D,KAAK,GAAG8D,UAAU;IAClE,MAAMG,UAAU,GAAG,IAAIlG,KAAK,CAACmG,UAAU,CAAC,CAAC,CAACC,kBAAkB,CAAC9E,GAAG,EAAEI,SAAS,CAAC0C,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC,CAAC;IAChG,MAAMuB,OAAO,GAAG,IAAIhG,KAAK,CAACwB,OAAO,CAAC,CAAC,CAAC6E,0BAA0B,CAACH,UAAU,CAAC;IAC1E,OAAO;MACLL,cAAc;MACdC,SAAS;MACTC,UAAU;MACVC;IACF,CAAC;EACH,CAAC,EAAE,CAACtE,SAAS,EAAEO,KAAK,EAAEC,SAAS,EAAEC,KAAK,CAAC,CAAC;EACxC,MAAMmE,MAAM,GAAGlD,SAAS,GAAGf,YAAY,GAAGD,UAAU,CAACT,IAAI,CAAC;EAC1D,OAAO,aAAa5B,KAAK,CAACwG,aAAa,CAAC,OAAO,EAAE;IAC/CC,GAAG,EAAEvD;EACP,CAAC,EAAE,aAAalD,KAAK,CAACwG,aAAa,CAAC,OAAO,EAAE;IAC3CE,MAAM,EAAET,OAAO;IACfU,gBAAgB,EAAE,KAAK;IACvBnD,aAAa,EAAEA,aAAa;IAC5ByB,aAAa,EAAEA,aAAa;IAC5BU,WAAW,EAAEA,WAAW;IACxBE,YAAY,EAAEA;EAChB,CAAC,EAAE9D,WAAW,IAAI,aAAa/B,KAAK,CAACwG,aAAa,CAACpG,IAAI,EAAE;IACvDwG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACZ,UAAU,EAAE,CAAC;EAC9B,CAAC,EAAE,aAAahG,KAAK,CAACwG,aAAa,CAAC,KAAK,EAAE;IACzC1C,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACf8C,UAAU,EAAE,SAAS;MACrBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAElF,gBAAgB;IAC3ByE,GAAG,EAAEzD;EACP,CAAC,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACwG,aAAa,CAAC,MAAM,EAAE;IAC5CW,OAAO,EAAE,KAAK;IACdP,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACd,cAAc,GAAGE,UAAU,IAAI,GAAG,EAAE,CAAC,CAAC;IACrDrD,QAAQ,EAAEA;EACZ,CAAC,EAAE,aAAa3C,KAAK,CAACwG,aAAa,CAAC,kBAAkB,EAAE;IACtDY,IAAI,EAAE,CAACrB,SAAS,GAAG,GAAG,EAAEA,SAAS,GAAG,GAAG,EAAED,cAAc,GAAGE,UAAU,EAAE,CAAC,EAAE,CAAC;EAC5E,CAAC,CAAC,CAAC,EAAE,aAAahG,KAAK,CAACwG,aAAa,CAACrG,IAAI,EAAE;IAC1CkH,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAEA,CAAA,KAAM,IAAI;IACnBrF,SAAS,EAAEA,SAAS;IACpBsF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEzB,cAAc,EAAE,CAAC,CAAC;IACvC3D,SAAS,EAAEA,SAAS;IACpBqF,IAAI,EAAEvH,KAAK,CAACwH,UAAU;IACtBX,KAAK,EAAEP,MAAM;IACbhE,OAAO,EAAEA,OAAO;IAChBmF,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,CAAC;IACdC,mBAAmB,EAAE,CAAC,EAAE;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC,EAAE,aAAa7H,KAAK,CAACwG,aAAa,CAAC,MAAM,EAAE;IAC3Cc,OAAO,EAAEA,CAAA,KAAM,IAAI;IACnBV,QAAQ,EAAE,CAAC,CAAC,EAAEd,cAAc,GAAGE,UAAU,GAAG,GAAG,EAAE,CAAC,CAAC;IACnD2B,WAAW,EAAE;EACf,CAAC,EAAE,aAAa3H,KAAK,CAACwG,aAAa,CAAC,cAAc,EAAE;IAClDY,IAAI,EAAE,CAACrB,SAAS,EAAEC,UAAU,EAAE,EAAE,EAAE,CAAC;EACrC,CAAC,CAAC,EAAE,aAAahG,KAAK,CAACwG,aAAa,CAAC,mBAAmB,EAAE;IACxDa,WAAW,EAAE,IAAI;IACjBpF,SAAS,EAAEA,SAAS;IACpB6E,KAAK,EAAEP,MAAM;IACbhE,OAAO,EAAEA,OAAO;IAChBmF,aAAa,EAAE,IAAI;IACnBE,mBAAmB,EAAE,CAAC,EAAE;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAASnG,SAAS,EAAEjB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}