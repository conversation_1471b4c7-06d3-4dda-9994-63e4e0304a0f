{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>erGeometry, Float32BufferAttribute, MeshStandardMaterial, RedFormat, NearestFilter, LinearFilter } from \"three\";\nimport { Data3DTexture } from \"../_polyfill/Data3DTexture.js\";\nclass VOXLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(buffer) {\n    const data = new DataView(buffer);\n    const id = data.getUint32(0, true);\n    const version = data.getUint32(4, true);\n    if (id !== 542658390 || version !== 150) {\n      console.error(\"Not a valid VOX file\");\n      return;\n    }\n    const DEFAULT_PALETTE = [0, 4294967295, 4291624959, 4288282623, 4284940287, 4281597951, 4278255615, 4294954239, 4291611903, 4288269567, 4284927231, 4281584895, 4278242559, 4294941183, 4291598847, 4288256511, 4284914175, 4281571839, 4278229503, 4294928127, 4291585791, 4288243455, 4284901119, 4281558783, 4278216447, 4294915071, 4291572735, 4288230399, 4284888063, 4281545727, 4278203391, 4294902015, 4291559679, 4288217343, 4284875007, 4281532671, 4278190335, 4294967244, 4291624908, 4288282572, 4284940236, 4281597900, 4278255564, 4294954188, 4291611852, 4288269516, 4284927180, 4281584844, 4278242508, 4294941132, 4291598796, 4288256460, 4284914124, 4281571788, 4278229452, 4294928076, 4291585740, 4288243404, 4284901068, 4281558732, 4278216396, 4294915020, 4291572684, 4288230348, 4284888012, 4281545676, 4278203340, 4294901964, 4291559628, 4288217292, 4284874956, 4281532620, 4278190284, 4294967193, 4291624857, 4288282521, 4284940185, 4281597849, 4278255513, 4294954137, 4291611801, 4288269465, 4284927129, 4281584793, 4278242457, 4294941081, 4291598745, 4288256409, 4284914073, 4281571737, 4278229401, 4294928025, 4291585689, 4288243353, 4284901017, 4281558681, 4278216345, 4294914969, 4291572633, 4288230297, 4284887961, 4281545625, 4278203289, 4294901913, 4291559577, 4288217241, 4284874905, 4281532569, 4278190233, 4294967142, 4291624806, 4288282470, 4284940134, 4281597798, 4278255462, 4294954086, 4291611750, 4288269414, 4284927078, 4281584742, 4278242406, 4294941030, 4291598694, 4288256358, 4284914022, 4281571686, 4278229350, 4294927974, 4291585638, 4288243302, 4284900966, 4281558630, 4278216294, 4294914918, 4291572582, 4288230246, 4284887910, 4281545574, 4278203238, 4294901862, 4291559526, 4288217190, 4284874854, 4281532518, 4278190182, 4294967091, 4291624755, 4288282419, 4284940083, 4281597747, 4278255411, 4294954035, 4291611699, 4288269363, 4284927027, 4281584691, 4278242355, 4294940979, 4291598643, 4288256307, 4284913971, 4281571635, 4278229299, 4294927923, 4291585587, 4288243251, 4284900915, 4281558579, 4278216243, 4294914867, 4291572531, 4288230195, 4284887859, 4281545523, 4278203187, 4294901811, 4291559475, 4288217139, 4284874803, 4281532467, 4278190131, 4294967040, 4291624704, 4288282368, 4284940032, 4281597696, 4278255360, 4294953984, 4291611648, 4288269312, 4284926976, 4281584640, 4278242304, 4294940928, 4291598592, 4288256256, 4284913920, 4281571584, 4278229248, 4294927872, 4291585536, 4288243200, 4284900864, 4281558528, 4278216192, 4294914816, 4291572480, 4288230144, 4284887808, 4281545472, 4278203136, 4294901760, 4291559424, 4288217088, 4284874752, 4281532416, 4278190318, 4278190301, 4278190267, 4278190250, 4278190216, 4278190199, 4278190165, 4278190148, 4278190114, 4278190097, 4278251008, 4278246656, 4278237952, 4278233600, 4278224896, 4278220544, 4278211840, 4278207488, 4278198784, 4278194432, 4293787648, 4292673536, 4290445312, 4289331200, 4287102976, 4285988864, 4283760640, 4282646528, 4280418304, 4279304192, 4293848814, 4292730333, 4290493371, 4289374890, 4287137928, 4286019447, 4283782485, 4282664004, 4280427042, 4279308561];\n    let i = 8;\n    let chunk;\n    const chunks = [];\n    while (i < data.byteLength) {\n      let id2 = \"\";\n      for (let j = 0; j < 4; j++) {\n        id2 += String.fromCharCode(data.getUint8(i++));\n      }\n      const chunkSize = data.getUint32(i, true);\n      i += 4;\n      i += 4;\n      if (id2 === \"SIZE\") {\n        const x = data.getUint32(i, true);\n        i += 4;\n        const y = data.getUint32(i, true);\n        i += 4;\n        const z = data.getUint32(i, true);\n        i += 4;\n        chunk = {\n          palette: DEFAULT_PALETTE,\n          size: {\n            x,\n            y,\n            z\n          }\n        };\n        chunks.push(chunk);\n        i += chunkSize - 3 * 4;\n      } else if (id2 === \"XYZI\") {\n        const numVoxels = data.getUint32(i, true);\n        i += 4;\n        chunk.data = new Uint8Array(buffer, i, numVoxels * 4);\n        i += numVoxels * 4;\n      } else if (id2 === \"RGBA\") {\n        const palette = [0];\n        for (let j = 0; j < 256; j++) {\n          palette[j + 1] = data.getUint32(i, true);\n          i += 4;\n        }\n        chunk.palette = palette;\n      } else {\n        i += chunkSize;\n      }\n    }\n    return chunks;\n  }\n}\nclass VOXMesh extends Mesh {\n  constructor(chunk) {\n    const data = chunk.data;\n    const size = chunk.size;\n    const palette = chunk.palette;\n    const vertices = [];\n    const colors = [];\n    const nx = [0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1];\n    const px = [1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0];\n    const py = [0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1];\n    const ny = [0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1, 0];\n    const nz = [0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0];\n    const pz = [0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1];\n    function add(tile, x, y, z, r, g, b) {\n      x -= size.x / 2;\n      y -= size.z / 2;\n      z += size.y / 2;\n      for (let i = 0; i < 18; i += 3) {\n        vertices.push(tile[i + 0] + x, tile[i + 1] + y, tile[i + 2] + z);\n        colors.push(r, g, b);\n      }\n    }\n    const offsety = size.x;\n    const offsetz = size.x * size.y;\n    const array = new Uint8Array(size.x * size.y * size.z);\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0];\n      const y = data[j + 1];\n      const z = data[j + 2];\n      const index = x + y * offsety + z * offsetz;\n      array[index] = 255;\n    }\n    let hasColors = false;\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0];\n      const y = data[j + 1];\n      const z = data[j + 2];\n      const c = data[j + 3];\n      const hex = palette[c];\n      const r = (hex >> 0 & 255) / 255;\n      const g = (hex >> 8 & 255) / 255;\n      const b = (hex >> 16 & 255) / 255;\n      if (r > 0 || g > 0 || b > 0) hasColors = true;\n      const index = x + y * offsety + z * offsetz;\n      if (array[index + 1] === 0 || x === size.x - 1) add(px, x, z, -y, r, g, b);\n      if (array[index - 1] === 0 || x === 0) add(nx, x, z, -y, r, g, b);\n      if (array[index + offsety] === 0 || y === size.y - 1) add(ny, x, z, -y, r, g, b);\n      if (array[index - offsety] === 0 || y === 0) add(py, x, z, -y, r, g, b);\n      if (array[index + offsetz] === 0 || z === size.z - 1) add(pz, x, z, -y, r, g, b);\n      if (array[index - offsetz] === 0 || z === 0) add(nz, x, z, -y, r, g, b);\n    }\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n    geometry.computeVertexNormals();\n    const material = new MeshStandardMaterial();\n    if (hasColors) {\n      geometry.setAttribute(\"color\", new Float32BufferAttribute(colors, 3));\n      material.vertexColors = true;\n    }\n    super(geometry, material);\n  }\n}\nclass VOXData3DTexture extends Data3DTexture {\n  constructor(chunk) {\n    const data = chunk.data;\n    const size = chunk.size;\n    const offsety = size.x;\n    const offsetz = size.x * size.y;\n    const array = new Uint8Array(size.x * size.y * size.z);\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0];\n      const y = data[j + 1];\n      const z = data[j + 2];\n      const index = x + y * offsety + z * offsetz;\n      array[index] = 255;\n    }\n    super(array, size.x, size.y, size.z);\n    this.format = RedFormat;\n    this.minFilter = NearestFilter;\n    this.magFilter = LinearFilter;\n    this.unpackAlignment = 1;\n    this.needsUpdate = true;\n  }\n}\nexport { VOXData3DTexture, VOXLoader, VOXMesh };", "map": {"version": 3, "names": ["VOXLoader", "Loader", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "buffer", "parse", "e", "console", "error", "itemError", "data", "DataView", "id", "getUint32", "version", "DEFAULT_PALETTE", "i", "chunk", "chunks", "byteLength", "id2", "j", "String", "fromCharCode", "getUint8", "chunkSize", "x", "y", "z", "palette", "size", "push", "numVoxels", "Uint8Array", "VOXMesh", "<PERSON><PERSON>", "constructor", "vertices", "colors", "nx", "px", "py", "ny", "nz", "pz", "add", "tile", "r", "g", "b", "<PERSON>y", "offsetz", "array", "length", "index", "hasColors", "c", "hex", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "computeVertexNormals", "material", "MeshStandardMaterial", "vertexColors", "VOXData3DTexture", "Data3DTexture", "format", "RedFormat", "minFilter", "NearestFilter", "magFilter", "LinearFilter", "unpackAlignment", "needsUpdate"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/VOXLoader.js"], "sourcesContent": ["import {\n  <PERSON>ufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  Loader,\n  LinearFilter,\n  Mesh,\n  MeshStandardMaterial,\n  NearestFilter,\n  RedFormat,\n} from 'three'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nclass VOXLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer) {\n    const data = new DataView(buffer)\n\n    const id = data.getUint32(0, true)\n    const version = data.getUint32(4, true)\n\n    if (id !== 542658390 || version !== 150) {\n      console.error('Not a valid VOX file')\n      return\n    }\n\n    const DEFAULT_PALETTE = [\n      0x00000000,\n      0xffffffff,\n      0xffccffff,\n      0xff99ffff,\n      0xff66ffff,\n      0xff33ffff,\n      0xff00ffff,\n      0xffffccff,\n      0xffccccff,\n      0xff99ccff,\n      0xff66ccff,\n      0xff33ccff,\n      0xff00ccff,\n      0xffff99ff,\n      0xffcc99ff,\n      0xff9999ff,\n      0xff6699ff,\n      0xff3399ff,\n      0xff0099ff,\n      0xffff66ff,\n      0xffcc66ff,\n      0xff9966ff,\n      0xff6666ff,\n      0xff3366ff,\n      0xff0066ff,\n      0xffff33ff,\n      0xffcc33ff,\n      0xff9933ff,\n      0xff6633ff,\n      0xff3333ff,\n      0xff0033ff,\n      0xffff00ff,\n      0xffcc00ff,\n      0xff9900ff,\n      0xff6600ff,\n      0xff3300ff,\n      0xff0000ff,\n      0xffffffcc,\n      0xffccffcc,\n      0xff99ffcc,\n      0xff66ffcc,\n      0xff33ffcc,\n      0xff00ffcc,\n      0xffffcccc,\n      0xffcccccc,\n      0xff99cccc,\n      0xff66cccc,\n      0xff33cccc,\n      0xff00cccc,\n      0xffff99cc,\n      0xffcc99cc,\n      0xff9999cc,\n      0xff6699cc,\n      0xff3399cc,\n      0xff0099cc,\n      0xffff66cc,\n      0xffcc66cc,\n      0xff9966cc,\n      0xff6666cc,\n      0xff3366cc,\n      0xff0066cc,\n      0xffff33cc,\n      0xffcc33cc,\n      0xff9933cc,\n      0xff6633cc,\n      0xff3333cc,\n      0xff0033cc,\n      0xffff00cc,\n      0xffcc00cc,\n      0xff9900cc,\n      0xff6600cc,\n      0xff3300cc,\n      0xff0000cc,\n      0xffffff99,\n      0xffccff99,\n      0xff99ff99,\n      0xff66ff99,\n      0xff33ff99,\n      0xff00ff99,\n      0xffffcc99,\n      0xffcccc99,\n      0xff99cc99,\n      0xff66cc99,\n      0xff33cc99,\n      0xff00cc99,\n      0xffff9999,\n      0xffcc9999,\n      0xff999999,\n      0xff669999,\n      0xff339999,\n      0xff009999,\n      0xffff6699,\n      0xffcc6699,\n      0xff996699,\n      0xff666699,\n      0xff336699,\n      0xff006699,\n      0xffff3399,\n      0xffcc3399,\n      0xff993399,\n      0xff663399,\n      0xff333399,\n      0xff003399,\n      0xffff0099,\n      0xffcc0099,\n      0xff990099,\n      0xff660099,\n      0xff330099,\n      0xff000099,\n      0xffffff66,\n      0xffccff66,\n      0xff99ff66,\n      0xff66ff66,\n      0xff33ff66,\n      0xff00ff66,\n      0xffffcc66,\n      0xffcccc66,\n      0xff99cc66,\n      0xff66cc66,\n      0xff33cc66,\n      0xff00cc66,\n      0xffff9966,\n      0xffcc9966,\n      0xff999966,\n      0xff669966,\n      0xff339966,\n      0xff009966,\n      0xffff6666,\n      0xffcc6666,\n      0xff996666,\n      0xff666666,\n      0xff336666,\n      0xff006666,\n      0xffff3366,\n      0xffcc3366,\n      0xff993366,\n      0xff663366,\n      0xff333366,\n      0xff003366,\n      0xffff0066,\n      0xffcc0066,\n      0xff990066,\n      0xff660066,\n      0xff330066,\n      0xff000066,\n      0xffffff33,\n      0xffccff33,\n      0xff99ff33,\n      0xff66ff33,\n      0xff33ff33,\n      0xff00ff33,\n      0xffffcc33,\n      0xffcccc33,\n      0xff99cc33,\n      0xff66cc33,\n      0xff33cc33,\n      0xff00cc33,\n      0xffff9933,\n      0xffcc9933,\n      0xff999933,\n      0xff669933,\n      0xff339933,\n      0xff009933,\n      0xffff6633,\n      0xffcc6633,\n      0xff996633,\n      0xff666633,\n      0xff336633,\n      0xff006633,\n      0xffff3333,\n      0xffcc3333,\n      0xff993333,\n      0xff663333,\n      0xff333333,\n      0xff003333,\n      0xffff0033,\n      0xffcc0033,\n      0xff990033,\n      0xff660033,\n      0xff330033,\n      0xff000033,\n      0xffffff00,\n      0xffccff00,\n      0xff99ff00,\n      0xff66ff00,\n      0xff33ff00,\n      0xff00ff00,\n      0xffffcc00,\n      0xffcccc00,\n      0xff99cc00,\n      0xff66cc00,\n      0xff33cc00,\n      0xff00cc00,\n      0xffff9900,\n      0xffcc9900,\n      0xff999900,\n      0xff669900,\n      0xff339900,\n      0xff009900,\n      0xffff6600,\n      0xffcc6600,\n      0xff996600,\n      0xff666600,\n      0xff336600,\n      0xff006600,\n      0xffff3300,\n      0xffcc3300,\n      0xff993300,\n      0xff663300,\n      0xff333300,\n      0xff003300,\n      0xffff0000,\n      0xffcc0000,\n      0xff990000,\n      0xff660000,\n      0xff330000,\n      0xff0000ee,\n      0xff0000dd,\n      0xff0000bb,\n      0xff0000aa,\n      0xff000088,\n      0xff000077,\n      0xff000055,\n      0xff000044,\n      0xff000022,\n      0xff000011,\n      0xff00ee00,\n      0xff00dd00,\n      0xff00bb00,\n      0xff00aa00,\n      0xff008800,\n      0xff007700,\n      0xff005500,\n      0xff004400,\n      0xff002200,\n      0xff001100,\n      0xffee0000,\n      0xffdd0000,\n      0xffbb0000,\n      0xffaa0000,\n      0xff880000,\n      0xff770000,\n      0xff550000,\n      0xff440000,\n      0xff220000,\n      0xff110000,\n      0xffeeeeee,\n      0xffdddddd,\n      0xffbbbbbb,\n      0xffaaaaaa,\n      0xff888888,\n      0xff777777,\n      0xff555555,\n      0xff444444,\n      0xff222222,\n      0xff111111,\n    ]\n\n    let i = 8\n\n    let chunk\n    const chunks = []\n\n    while (i < data.byteLength) {\n      let id = ''\n\n      for (let j = 0; j < 4; j++) {\n        id += String.fromCharCode(data.getUint8(i++))\n      }\n\n      const chunkSize = data.getUint32(i, true)\n      i += 4\n      i += 4 // childChunks\n\n      if (id === 'SIZE') {\n        const x = data.getUint32(i, true)\n        i += 4\n        const y = data.getUint32(i, true)\n        i += 4\n        const z = data.getUint32(i, true)\n        i += 4\n\n        chunk = {\n          palette: DEFAULT_PALETTE,\n          size: { x: x, y: y, z: z },\n        }\n\n        chunks.push(chunk)\n\n        i += chunkSize - 3 * 4\n      } else if (id === 'XYZI') {\n        const numVoxels = data.getUint32(i, true)\n        i += 4\n        chunk.data = new Uint8Array(buffer, i, numVoxels * 4)\n\n        i += numVoxels * 4\n      } else if (id === 'RGBA') {\n        const palette = [0]\n\n        for (let j = 0; j < 256; j++) {\n          palette[j + 1] = data.getUint32(i, true)\n          i += 4\n        }\n\n        chunk.palette = palette\n      } else {\n        // console.log( id, chunkSize, childChunks );\n\n        i += chunkSize\n      }\n    }\n\n    return chunks\n  }\n}\n\nclass VOXMesh extends Mesh {\n  constructor(chunk) {\n    const data = chunk.data\n    const size = chunk.size\n    const palette = chunk.palette\n\n    //\n\n    const vertices = []\n    const colors = []\n\n    const nx = [0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1]\n    const px = [1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0]\n    const py = [0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1]\n    const ny = [0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1, 0]\n    const nz = [0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0]\n    const pz = [0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1]\n\n    function add(tile, x, y, z, r, g, b) {\n      x -= size.x / 2\n      y -= size.z / 2\n      z += size.y / 2\n\n      for (let i = 0; i < 18; i += 3) {\n        vertices.push(tile[i + 0] + x, tile[i + 1] + y, tile[i + 2] + z)\n        colors.push(r, g, b)\n      }\n    }\n\n    // Store data in a volume for sampling\n\n    const offsety = size.x\n    const offsetz = size.x * size.y\n\n    const array = new Uint8Array(size.x * size.y * size.z)\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n\n      const index = x + y * offsety + z * offsetz\n\n      array[index] = 255\n    }\n\n    // Construct geometry\n\n    let hasColors = false\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n      const c = data[j + 3]\n\n      const hex = palette[c]\n      const r = ((hex >> 0) & 0xff) / 0xff\n      const g = ((hex >> 8) & 0xff) / 0xff\n      const b = ((hex >> 16) & 0xff) / 0xff\n\n      if (r > 0 || g > 0 || b > 0) hasColors = true\n\n      const index = x + y * offsety + z * offsetz\n\n      if (array[index + 1] === 0 || x === size.x - 1) add(px, x, z, -y, r, g, b)\n      if (array[index - 1] === 0 || x === 0) add(nx, x, z, -y, r, g, b)\n      if (array[index + offsety] === 0 || y === size.y - 1) add(ny, x, z, -y, r, g, b)\n      if (array[index - offsety] === 0 || y === 0) add(py, x, z, -y, r, g, b)\n      if (array[index + offsetz] === 0 || z === size.z - 1) add(pz, x, z, -y, r, g, b)\n      if (array[index - offsetz] === 0 || z === 0) add(nz, x, z, -y, r, g, b)\n    }\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    geometry.computeVertexNormals()\n\n    const material = new MeshStandardMaterial()\n\n    if (hasColors) {\n      geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n      material.vertexColors = true\n    }\n\n    super(geometry, material)\n  }\n}\n\nclass VOXData3DTexture extends Data3DTexture {\n  constructor(chunk) {\n    const data = chunk.data\n    const size = chunk.size\n\n    const offsety = size.x\n    const offsetz = size.x * size.y\n\n    const array = new Uint8Array(size.x * size.y * size.z)\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n\n      const index = x + y * offsety + z * offsetz\n\n      array[index] = 255\n    }\n\n    super(array, size.x, size.y, size.z)\n\n    this.format = RedFormat\n    this.minFilter = NearestFilter\n    this.magFilter = LinearFilter\n    this.unpackAlignment = 1\n    this.needsUpdate = true\n  }\n}\n\nexport { VOXLoader, VOXMesh, VOXData3DTexture }\n"], "mappings": ";;AAaA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMG,OAAO;IAC3CF,MAAA,CAAOG,OAAA,CAAQJ,KAAA,CAAMK,IAAI;IACzBJ,MAAA,CAAOK,eAAA,CAAgB,aAAa;IACpCL,MAAA,CAAOM,gBAAA,CAAiBP,KAAA,CAAMQ,aAAa;IAC3CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,MAAA,EAAQ;MAChB,IAAI;QACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,MAAM,CAAC;MAC3B,SAAQE,CAAA,EAAP;QACA,IAAIZ,OAAA,EAAS;UACXA,OAAA,CAAQY,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDX,KAAA,CAAMG,OAAA,CAAQW,SAAA,CAAUlB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDW,MAAMD,MAAA,EAAQ;IACZ,MAAMM,IAAA,GAAO,IAAIC,QAAA,CAASP,MAAM;IAEhC,MAAMQ,EAAA,GAAKF,IAAA,CAAKG,SAAA,CAAU,GAAG,IAAI;IACjC,MAAMC,OAAA,GAAUJ,IAAA,CAAKG,SAAA,CAAU,GAAG,IAAI;IAEtC,IAAID,EAAA,KAAO,aAAaE,OAAA,KAAY,KAAK;MACvCP,OAAA,CAAQC,KAAA,CAAM,sBAAsB;MACpC;IACD;IAED,MAAMO,eAAA,GAAkB,CACtB,GACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,WACD;IAED,IAAIC,CAAA,GAAI;IAER,IAAIC,KAAA;IACJ,MAAMC,MAAA,GAAS,EAAE;IAEjB,OAAOF,CAAA,GAAIN,IAAA,CAAKS,UAAA,EAAY;MAC1B,IAAIC,GAAA,GAAK;MAET,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BD,GAAA,IAAME,MAAA,CAAOC,YAAA,CAAab,IAAA,CAAKc,QAAA,CAASR,CAAA,EAAG,CAAC;MAC7C;MAED,MAAMS,SAAA,GAAYf,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;MACxCA,CAAA,IAAK;MACLA,CAAA,IAAK;MAEL,IAAII,GAAA,KAAO,QAAQ;QACjB,MAAMM,CAAA,GAAIhB,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;QAChCA,CAAA,IAAK;QACL,MAAMW,CAAA,GAAIjB,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;QAChCA,CAAA,IAAK;QACL,MAAMY,CAAA,GAAIlB,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;QAChCA,CAAA,IAAK;QAELC,KAAA,GAAQ;UACNY,OAAA,EAASd,eAAA;UACTe,IAAA,EAAM;YAAEJ,CAAA;YAAMC,CAAA;YAAMC;UAAM;QAC3B;QAEDV,MAAA,CAAOa,IAAA,CAAKd,KAAK;QAEjBD,CAAA,IAAKS,SAAA,GAAY,IAAI;MAC7B,WAAiBL,GAAA,KAAO,QAAQ;QACxB,MAAMY,SAAA,GAAYtB,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;QACxCA,CAAA,IAAK;QACLC,KAAA,CAAMP,IAAA,GAAO,IAAIuB,UAAA,CAAW7B,MAAA,EAAQY,CAAA,EAAGgB,SAAA,GAAY,CAAC;QAEpDhB,CAAA,IAAKgB,SAAA,GAAY;MACzB,WAAiBZ,GAAA,KAAO,QAAQ;QACxB,MAAMS,OAAA,GAAU,CAAC,CAAC;QAElB,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKA,CAAA,IAAK;UAC5BQ,OAAA,CAAQR,CAAA,GAAI,CAAC,IAAIX,IAAA,CAAKG,SAAA,CAAUG,CAAA,EAAG,IAAI;UACvCA,CAAA,IAAK;QACN;QAEDC,KAAA,CAAMY,OAAA,GAAUA,OAAA;MACxB,OAAa;QAGLb,CAAA,IAAKS,SAAA;MACN;IACF;IAED,OAAOP,MAAA;EACR;AACH;AAEA,MAAMgB,OAAA,SAAgBC,IAAA,CAAK;EACzBC,YAAYnB,KAAA,EAAO;IACjB,MAAMP,IAAA,GAAOO,KAAA,CAAMP,IAAA;IACnB,MAAMoB,IAAA,GAAOb,KAAA,CAAMa,IAAA;IACnB,MAAMD,OAAA,GAAUZ,KAAA,CAAMY,OAAA;IAItB,MAAMQ,QAAA,GAAW,EAAE;IACnB,MAAMC,MAAA,GAAS,EAAE;IAEjB,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAChE,MAAMC,EAAA,GAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAEhE,SAASC,IAAIC,IAAA,EAAMpB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGmB,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACnCvB,CAAA,IAAKI,IAAA,CAAKJ,CAAA,GAAI;MACdC,CAAA,IAAKG,IAAA,CAAKF,CAAA,GAAI;MACdA,CAAA,IAAKE,IAAA,CAAKH,CAAA,GAAI;MAEd,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK,GAAG;QAC9BqB,QAAA,CAASN,IAAA,CAAKe,IAAA,CAAK9B,CAAA,GAAI,CAAC,IAAIU,CAAA,EAAGoB,IAAA,CAAK9B,CAAA,GAAI,CAAC,IAAIW,CAAA,EAAGmB,IAAA,CAAK9B,CAAA,GAAI,CAAC,IAAIY,CAAC;QAC/DU,MAAA,CAAOP,IAAA,CAAKgB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACpB;IACF;IAID,MAAMC,OAAA,GAAUpB,IAAA,CAAKJ,CAAA;IACrB,MAAMyB,OAAA,GAAUrB,IAAA,CAAKJ,CAAA,GAAII,IAAA,CAAKH,CAAA;IAE9B,MAAMyB,KAAA,GAAQ,IAAInB,UAAA,CAAWH,IAAA,CAAKJ,CAAA,GAAII,IAAA,CAAKH,CAAA,GAAIG,IAAA,CAAKF,CAAC;IAErD,SAASP,CAAA,GAAI,GAAGA,CAAA,GAAIX,IAAA,CAAK2C,MAAA,EAAQhC,CAAA,IAAK,GAAG;MACvC,MAAMK,CAAA,GAAIhB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMM,CAAA,GAAIjB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMO,CAAA,GAAIlB,IAAA,CAAKW,CAAA,GAAI,CAAC;MAEpB,MAAMiC,KAAA,GAAQ5B,CAAA,GAAIC,CAAA,GAAIuB,OAAA,GAAUtB,CAAA,GAAIuB,OAAA;MAEpCC,KAAA,CAAME,KAAK,IAAI;IAChB;IAID,IAAIC,SAAA,GAAY;IAEhB,SAASlC,CAAA,GAAI,GAAGA,CAAA,GAAIX,IAAA,CAAK2C,MAAA,EAAQhC,CAAA,IAAK,GAAG;MACvC,MAAMK,CAAA,GAAIhB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMM,CAAA,GAAIjB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMO,CAAA,GAAIlB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMmC,CAAA,GAAI9C,IAAA,CAAKW,CAAA,GAAI,CAAC;MAEpB,MAAMoC,GAAA,GAAM5B,OAAA,CAAQ2B,CAAC;MACrB,MAAMT,CAAA,IAAMU,GAAA,IAAO,IAAK,OAAQ;MAChC,MAAMT,CAAA,IAAMS,GAAA,IAAO,IAAK,OAAQ;MAChC,MAAMR,CAAA,IAAMQ,GAAA,IAAO,KAAM,OAAQ;MAEjC,IAAIV,CAAA,GAAI,KAAKC,CAAA,GAAI,KAAKC,CAAA,GAAI,GAAGM,SAAA,GAAY;MAEzC,MAAMD,KAAA,GAAQ5B,CAAA,GAAIC,CAAA,GAAIuB,OAAA,GAAUtB,CAAA,GAAIuB,OAAA;MAEpC,IAAIC,KAAA,CAAME,KAAA,GAAQ,CAAC,MAAM,KAAK5B,CAAA,KAAMI,IAAA,CAAKJ,CAAA,GAAI,GAAGmB,GAAA,CAAIL,EAAA,EAAId,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACzE,IAAIG,KAAA,CAAME,KAAA,GAAQ,CAAC,MAAM,KAAK5B,CAAA,KAAM,GAAGmB,GAAA,CAAIN,EAAA,EAAIb,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAChE,IAAIG,KAAA,CAAME,KAAA,GAAQJ,OAAO,MAAM,KAAKvB,CAAA,KAAMG,IAAA,CAAKH,CAAA,GAAI,GAAGkB,GAAA,CAAIH,EAAA,EAAIhB,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAC/E,IAAIG,KAAA,CAAME,KAAA,GAAQJ,OAAO,MAAM,KAAKvB,CAAA,KAAM,GAAGkB,GAAA,CAAIJ,EAAA,EAAIf,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACtE,IAAIG,KAAA,CAAME,KAAA,GAAQH,OAAO,MAAM,KAAKvB,CAAA,KAAME,IAAA,CAAKF,CAAA,GAAI,GAAGiB,GAAA,CAAID,EAAA,EAAIlB,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAC/E,IAAIG,KAAA,CAAME,KAAA,GAAQH,OAAO,MAAM,KAAKvB,CAAA,KAAM,GAAGiB,GAAA,CAAIF,EAAA,EAAIjB,CAAA,EAAGE,CAAA,EAAG,CAACD,CAAA,EAAGoB,CAAA,EAAGC,CAAA,EAAGC,CAAC;IACvE;IAED,MAAMS,QAAA,GAAW,IAAIC,cAAA,CAAgB;IACrCD,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBxB,QAAA,EAAU,CAAC,CAAC;IACzEqB,QAAA,CAASI,oBAAA,CAAsB;IAE/B,MAAMC,QAAA,GAAW,IAAIC,oBAAA,CAAsB;IAE3C,IAAIT,SAAA,EAAW;MACbG,QAAA,CAASE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBvB,MAAA,EAAQ,CAAC,CAAC;MACpEyB,QAAA,CAASE,YAAA,GAAe;IACzB;IAED,MAAMP,QAAA,EAAUK,QAAQ;EACzB;AACH;AAEA,MAAMG,gBAAA,SAAyBC,aAAA,CAAc;EAC3C/B,YAAYnB,KAAA,EAAO;IACjB,MAAMP,IAAA,GAAOO,KAAA,CAAMP,IAAA;IACnB,MAAMoB,IAAA,GAAOb,KAAA,CAAMa,IAAA;IAEnB,MAAMoB,OAAA,GAAUpB,IAAA,CAAKJ,CAAA;IACrB,MAAMyB,OAAA,GAAUrB,IAAA,CAAKJ,CAAA,GAAII,IAAA,CAAKH,CAAA;IAE9B,MAAMyB,KAAA,GAAQ,IAAInB,UAAA,CAAWH,IAAA,CAAKJ,CAAA,GAAII,IAAA,CAAKH,CAAA,GAAIG,IAAA,CAAKF,CAAC;IAErD,SAASP,CAAA,GAAI,GAAGA,CAAA,GAAIX,IAAA,CAAK2C,MAAA,EAAQhC,CAAA,IAAK,GAAG;MACvC,MAAMK,CAAA,GAAIhB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMM,CAAA,GAAIjB,IAAA,CAAKW,CAAA,GAAI,CAAC;MACpB,MAAMO,CAAA,GAAIlB,IAAA,CAAKW,CAAA,GAAI,CAAC;MAEpB,MAAMiC,KAAA,GAAQ5B,CAAA,GAAIC,CAAA,GAAIuB,OAAA,GAAUtB,CAAA,GAAIuB,OAAA;MAEpCC,KAAA,CAAME,KAAK,IAAI;IAChB;IAED,MAAMF,KAAA,EAAOtB,IAAA,CAAKJ,CAAA,EAAGI,IAAA,CAAKH,CAAA,EAAGG,IAAA,CAAKF,CAAC;IAEnC,KAAKwC,MAAA,GAASC,SAAA;IACd,KAAKC,SAAA,GAAYC,aAAA;IACjB,KAAKC,SAAA,GAAYC,YAAA;IACjB,KAAKC,eAAA,GAAkB;IACvB,KAAKC,WAAA,GAAc;EACpB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}