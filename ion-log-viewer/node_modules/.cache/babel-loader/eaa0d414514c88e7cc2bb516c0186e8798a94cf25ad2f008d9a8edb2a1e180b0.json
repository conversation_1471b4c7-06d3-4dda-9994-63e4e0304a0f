{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class IonType {\n  constructor(binaryTypeId, name, isScalar, isLob, isNumeric, isContainer) {\n    this.binaryTypeId = binaryTypeId;\n    this.name = name;\n    this.isScalar = isScalar;\n    this.isLob = isLob;\n    this.isNumeric = isNumeric;\n    this.isContainer = isContainer;\n  }\n}", "map": {"version": 3, "names": ["IonType", "constructor", "binaryTypeId", "name", "isScalar", "is<PERSON>ob", "isNumeric", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonType.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class IonType {\n    constructor(binaryTypeId, name, isScalar, isLob, isNumeric, isContainer) {\n        this.binaryTypeId = binaryTypeId;\n        this.name = name;\n        this.isScalar = isScalar;\n        this.isLob = isLob;\n        this.isNumeric = isNumeric;\n        this.isContainer = isContainer;\n    }\n}\n//# sourceMappingURL=IonType.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,OAAO,CAAC;EACjBC,WAAWA,CAACC,YAAY,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAE;IACrE,IAAI,CAACL,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}