{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { ComparisonResult } from \"../ComparisonResult\";\nimport { ComparisonResultType } from \"../ComparisonResult\";\nimport { defaultLocalSymbolTable, makeReader } from \"../Ion\";\nimport { BinaryReader } from \"../IonBinaryReader\";\nimport { BinarySpan } from \"../IonSpan\";\nimport { IonTypes } from \"../IonTypes\";\nimport { EventStreamError } from \"./EventStreamError\";\nimport { IonEventFactory, IonEventType } from \"./IonEvent\";\nconst READ = \"READ\";\nconst WRITE = \"WRITE\";\nexport class IonEventStream {\n  constructor(reader) {\n    this.events = [];\n    this.reader = reader;\n    this.eventFactory = new IonEventFactory();\n    this.isEventStream = false;\n    this.generateStream();\n  }\n  writeEventStream(writer) {\n    writer.writeSymbol(\"$ion_event_stream\");\n    for (let i = 0; i < this.events.length; i++) {\n      this.events[i].write(writer);\n    }\n  }\n  writeIon(writer) {\n    try {\n      let tempEvent;\n      let isEmbedded = false;\n      for (let indice = 0; indice < this.events.length; indice++) {\n        tempEvent = this.events[indice];\n        if (tempEvent.fieldName !== null) {\n          writer.writeFieldName(tempEvent.fieldName);\n        }\n        if ((tempEvent.ionType == IonTypes.SEXP || tempEvent.ionType == IonTypes.LIST) && this.isEmbedded(tempEvent)) {\n          isEmbedded = true;\n        }\n        writer.setAnnotations(tempEvent.annotations);\n        switch (tempEvent.eventType) {\n          case IonEventType.SCALAR:\n            if (tempEvent.ionValue == null) {\n              writer.writeNull(tempEvent.ionType);\n              return;\n            }\n            if (isEmbedded) {\n              writer.writeString(tempEvent.ionValue.toString());\n              break;\n            }\n            switch (tempEvent.ionType) {\n              case IonTypes.BOOL:\n                writer.writeBoolean(tempEvent.ionValue);\n                break;\n              case IonTypes.STRING:\n                writer.writeString(tempEvent.ionValue);\n                break;\n              case IonTypes.SYMBOL:\n                writer.writeSymbol(tempEvent.ionValue);\n                break;\n              case IonTypes.INT:\n                writer.writeInt(tempEvent.ionValue);\n                break;\n              case IonTypes.DECIMAL:\n                writer.writeDecimal(tempEvent.ionValue);\n                break;\n              case IonTypes.FLOAT:\n                writer.writeFloat64(tempEvent.ionValue);\n                break;\n              case IonTypes.NULL:\n                writer.writeNull(tempEvent.ionType);\n                break;\n              case IonTypes.TIMESTAMP:\n                writer.writeTimestamp(tempEvent.ionValue);\n                break;\n              case IonTypes.CLOB:\n                writer.writeClob(tempEvent.ionValue);\n                break;\n              case IonTypes.BLOB:\n                writer.writeBlob(tempEvent.ionValue);\n                break;\n              default:\n                throw new Error(\"unexpected type: \" + tempEvent.ionType.name);\n            }\n            break;\n          case IonEventType.CONTAINER_START:\n            writer.stepIn(tempEvent.ionType);\n            break;\n          case IonEventType.CONTAINER_END:\n            if (isEmbedded) {\n              isEmbedded = false;\n            }\n            writer.stepOut();\n            break;\n          case IonEventType.STREAM_END:\n            break;\n          case IonEventType.SYMBOL_TABLE:\n            throw new Error(\"Symboltables unsupported.\");\n          default:\n            throw new Error(\"Unexpected event type: \" + tempEvent.eventType);\n        }\n      }\n      writer.close();\n    } catch (error) {\n      throw new EventStreamError(WRITE, error.message, this.events.length, this.events);\n    }\n  }\n  getEvents() {\n    return this.events;\n  }\n  equals(expected) {\n    return this.compare(expected).result == ComparisonResultType.EQUAL;\n  }\n  compare(expected) {\n    let actualIndex = 0;\n    let expectedIndex = 0;\n    if (this.events.length != expected.events.length) {\n      return new ComparisonResult(ComparisonResultType.NOT_EQUAL, \"The event streams have different lengths\");\n    }\n    while (actualIndex < this.events.length && expectedIndex < expected.events.length) {\n      const actualEvent = this.events[actualIndex];\n      const expectedEvent = expected.events[expectedIndex];\n      if (actualEvent.eventType === IonEventType.SYMBOL_TABLE) {\n        actualIndex++;\n      }\n      if (expectedEvent.eventType === IonEventType.SYMBOL_TABLE) {\n        expectedIndex++;\n      }\n      if (actualEvent.eventType === IonEventType.SYMBOL_TABLE || expectedEvent.eventType === IonEventType.SYMBOL_TABLE) {\n        continue;\n      }\n      switch (actualEvent.eventType) {\n        case IonEventType.SCALAR:\n          {\n            const eventResult = actualEvent.compare(expectedEvent);\n            if (eventResult.result == ComparisonResultType.NOT_EQUAL) {\n              eventResult.actualIndex = actualIndex;\n              eventResult.expectedIndex = expectedIndex;\n              return eventResult;\n            }\n            break;\n          }\n        case IonEventType.CONTAINER_START:\n          {\n            const eventResult = actualEvent.compare(expectedEvent);\n            if (eventResult.result == ComparisonResultType.NOT_EQUAL) {\n              actualIndex += eventResult.actualIndex;\n              expectedIndex += eventResult.expectedIndex;\n              eventResult.actualIndex = actualIndex;\n              eventResult.expectedIndex = expectedIndex;\n              return eventResult;\n            } else {\n              if (actualEvent.ionValue !== null && expectedEvent.ionValue !== null) {\n                actualIndex = actualIndex + actualEvent.ionValue.length;\n                expectedIndex = expectedIndex + expectedEvent.ionValue.length;\n              }\n            }\n            break;\n          }\n        case IonEventType.CONTAINER_END:\n        case IonEventType.STREAM_END:\n          {\n            break;\n          }\n        default:\n          {\n            throw new Error(\"Unexpected event type: \" + actualEvent.eventType);\n          }\n      }\n      actualIndex++;\n      expectedIndex++;\n    }\n    return new ComparisonResult(ComparisonResultType.EQUAL);\n  }\n  isEmbedded(event) {\n    if (event.annotations[0] === \"embedded_documents\") {\n      return true;\n    }\n    return false;\n  }\n  generateStream() {\n    try {\n      let tid = this.reader.next();\n      if (tid === IonTypes.SYMBOL && this.reader.stringValue() === \"$ion_event_stream\") {\n        this.marshalStream();\n        this.isEventStream = true;\n        return;\n      }\n      const currentContainer = [];\n      const currentContainerIndex = [];\n      while (true) {\n        if (this.reader.isNull()) {\n          this.events.push(this.eventFactory.makeEvent(IonEventType.SCALAR, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), true, this.reader.value()));\n        } else {\n          switch (tid) {\n            case IonTypes.LIST:\n            case IonTypes.SEXP:\n            case IonTypes.STRUCT:\n              {\n                const containerEvent = this.eventFactory.makeEvent(IonEventType.CONTAINER_START, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), false, null);\n                this.events.push(containerEvent);\n                currentContainer.push(containerEvent);\n                currentContainerIndex.push(this.events.length);\n                this.reader.stepIn();\n                break;\n              }\n            case null:\n              {\n                if (this.reader.depth() === 0) {\n                  this.events.push(this.eventFactory.makeEvent(IonEventType.STREAM_END, IonTypes.NULL, null, this.reader.depth(), [], false, undefined));\n                  return;\n                } else {\n                  this.reader.stepOut();\n                  this.endContainer(currentContainer.pop(), currentContainerIndex.pop());\n                }\n                break;\n              }\n            default:\n              {\n                this.events.push(this.eventFactory.makeEvent(IonEventType.SCALAR, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), false, this.reader.value()));\n                break;\n              }\n          }\n        }\n        tid = this.reader.next();\n      }\n    } catch (error) {\n      throw new EventStreamError(READ, error.message, this.events.length, this.events);\n    }\n  }\n  endContainer(thisContainer, thisContainerIndex) {\n    this.events.push(this.eventFactory.makeEvent(IonEventType.CONTAINER_END, thisContainer.ionType, null, thisContainer.depth, [], false, null));\n    thisContainer.ionValue = this.events.slice(thisContainerIndex, this.events.length);\n  }\n  marshalStream() {\n    this.events = [];\n    const currentContainer = [];\n    const currentContainerIndex = [];\n    for (let tid = this.reader.next(); tid === IonTypes.STRUCT; tid = this.reader.next()) {\n      this.reader.stepIn();\n      const tempEvent = this.marshalEvent();\n      if (tempEvent.eventType === IonEventType.CONTAINER_START) {\n        currentContainer.push(tempEvent);\n        this.events.push(tempEvent);\n        currentContainerIndex.push(this.events.length);\n      } else if (tempEvent.eventType === IonEventType.CONTAINER_END) {\n        this.endContainer(currentContainer.pop(), currentContainerIndex.pop());\n      } else if (tempEvent.eventType === IonEventType.SCALAR || tempEvent.eventType === IonEventType.STREAM_END) {\n        this.events.push(tempEvent);\n      } else {\n        throw new Error(\"Unexpected eventType: \" + tempEvent.eventType);\n      }\n      this.reader.stepOut();\n    }\n  }\n  marshalEvent() {\n    const currentEvent = {};\n    for (let tid; tid = this.reader.next();) {\n      const fieldName = this.reader.fieldName();\n      if (fieldName && currentEvent[fieldName] !== undefined) {\n        throw new Error(\"Repeated event field: \" + fieldName);\n      }\n      switch (fieldName) {\n        case \"event_type\":\n          {\n            currentEvent[fieldName] = this.reader.stringValue();\n            break;\n          }\n        case \"ion_type\":\n          {\n            currentEvent[fieldName] = this.parseIonType();\n            break;\n          }\n        case \"field_name\":\n          {\n            currentEvent[fieldName] = this.resolveFieldNameFromSerializedSymbolToken();\n            break;\n          }\n        case \"annotations\":\n          {\n            currentEvent[fieldName] = this.parseAnnotations();\n            break;\n          }\n        case \"value_text\":\n          {\n            let tempString = this.reader.stringValue();\n            if (tempString.substr(0, 5) === \"$ion_\") {\n              tempString = \"$ion_user_value::\" + tempString;\n            }\n            const tempReader = makeReader(tempString);\n            tempReader.next();\n            const tempValue = tempReader.value();\n            currentEvent[\"isNull\"] = tempReader.isNull();\n            currentEvent[fieldName] = tempValue;\n            break;\n          }\n        case \"value_binary\":\n          {\n            currentEvent[fieldName] = this.parseBinaryValue();\n            break;\n          }\n        case \"imports\":\n          {\n            currentEvent[fieldName] = this.parseImports();\n            break;\n          }\n        case \"depth\":\n          {\n            currentEvent[fieldName] = this.reader.numberValue();\n            break;\n          }\n        default:\n          throw new Error(\"Unexpected event field: \" + fieldName);\n      }\n    }\n    let eventType;\n    switch (currentEvent[\"event_type\"]) {\n      case \"CONTAINER_START\":\n        eventType = IonEventType.CONTAINER_START;\n        break;\n      case \"STREAM_END\":\n        eventType = IonEventType.STREAM_END;\n        break;\n      case \"CONTAINER_END\":\n        eventType = IonEventType.CONTAINER_END;\n        break;\n      case \"SCALAR\":\n        eventType = IonEventType.SCALAR;\n        break;\n      case \"SYMBOL_TABLE\":\n        throw new Error(\"Symbol tables unsupported\");\n    }\n    const fieldname = currentEvent[\"field_name\"] !== undefined ? currentEvent[\"field_name\"] : null;\n    if (!currentEvent[\"annotations\"]) {\n      currentEvent[\"annotations\"] = [];\n    }\n    const textEvent = this.eventFactory.makeEvent(eventType, currentEvent[\"ion_type\"], fieldname, currentEvent[\"depth\"], currentEvent[\"annotations\"], currentEvent[\"isNull\"], currentEvent[\"value_text\"]);\n    if (eventType === IonEventType.SCALAR) {\n      const binaryEvent = this.eventFactory.makeEvent(eventType, currentEvent[\"ion_type\"], fieldname, currentEvent[\"depth\"], currentEvent[\"annotations\"], currentEvent[\"isNull\"], currentEvent[\"value_binary\"]);\n      if (!textEvent.equals(binaryEvent)) {\n        throw new Error(`Text event ${currentEvent[\"value_text\"]} does not equal binary event ${currentEvent[\"value_binary\"]}`);\n      }\n    }\n    return textEvent;\n  }\n  parseIonType() {\n    const input = this.reader.stringValue().toLowerCase();\n    switch (input) {\n      case \"null\":\n        {\n          return IonTypes.NULL;\n        }\n      case \"bool\":\n        {\n          return IonTypes.BOOL;\n        }\n      case \"int\":\n        {\n          return IonTypes.INT;\n        }\n      case \"float\":\n        {\n          return IonTypes.FLOAT;\n        }\n      case \"decimal\":\n        {\n          return IonTypes.DECIMAL;\n        }\n      case \"timestamp\":\n        {\n          return IonTypes.TIMESTAMP;\n        }\n      case \"symbol\":\n        {\n          return IonTypes.SYMBOL;\n        }\n      case \"string\":\n        {\n          return IonTypes.STRING;\n        }\n      case \"clob\":\n        {\n          return IonTypes.CLOB;\n        }\n      case \"blob\":\n        {\n          return IonTypes.BLOB;\n        }\n      case \"list\":\n        {\n          return IonTypes.LIST;\n        }\n      case \"sexp\":\n        {\n          return IonTypes.SEXP;\n        }\n      case \"struct\":\n        {\n          return IonTypes.STRUCT;\n        }\n      default:\n        {\n          throw new Error(\"i: \" + input);\n        }\n    }\n  }\n  parseAnnotations() {\n    const annotations = [];\n    if (this.reader.isNull()) {\n      return annotations;\n    } else {\n      this.reader.stepIn();\n      for (let tid; tid = this.reader.next();) {\n        if (tid == IonTypes.STRUCT) {\n          this.reader.stepIn();\n          const type = this.reader.next();\n          if (this.reader.fieldName() == \"text\" && type == IonTypes.STRING) {\n            const text = this.reader.stringValue();\n            if (text !== null) {\n              annotations.push(text);\n            }\n          } else if (this.reader.fieldName() == \"importLocation\" && type == IonTypes.INT) {\n            const symtab = defaultLocalSymbolTable();\n            const symbol = symtab.getSymbolText(this.reader.numberValue());\n            if (symbol === undefined || symbol === null) {\n              throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n            }\n            annotations.push(symbol);\n          }\n          this.reader.stepOut();\n        }\n      }\n      this.reader.stepOut();\n      return annotations;\n    }\n  }\n  parseBinaryValue() {\n    if (this.reader.isNull()) {\n      return null;\n    }\n    const numBuffer = [];\n    this.reader.stepIn();\n    let tid = this.reader.next();\n    while (tid) {\n      numBuffer.push(this.reader.numberValue());\n      tid = this.reader.next();\n    }\n    this.reader.stepOut();\n    const bufArray = new Uint8Array(numBuffer);\n    const tempReader = new BinaryReader(new BinarySpan(bufArray));\n    tempReader.next();\n    return tempReader.value();\n  }\n  parseImports() {\n    return this.reader.value();\n  }\n  resolveFieldNameFromSerializedSymbolToken() {\n    if (this.reader.isNull()) {\n      return null;\n    }\n    this.reader.stepIn();\n    const type = this.reader.next();\n    if (this.reader.fieldName() == \"text\" && type == IonTypes.STRING) {\n      const text = this.reader.stringValue();\n      if (text !== null) {\n        this.reader.stepOut();\n        return text;\n      }\n    } else if (this.reader.fieldName() == \"importLocation\" && type == IonTypes.INT) {\n      const symtab = defaultLocalSymbolTable();\n      const symbol = symtab.getSymbolText(this.reader.numberValue());\n      if (symbol === undefined || symbol === null) {\n        throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n      }\n      this.reader.stepOut();\n      return symbol;\n    }\n    return null;\n  }\n}", "map": {"version": 3, "names": ["ComparisonResult", "ComparisonResultType", "defaultLocalSymbolTable", "makeReader", "BinaryReader", "BinarySpan", "IonTypes", "EventStreamError", "IonEventFactory", "IonEventType", "READ", "WRITE", "IonEventStream", "constructor", "reader", "events", "eventFactory", "isEventStream", "generateStream", "writeEventStream", "writer", "writeSymbol", "i", "length", "write", "writeIon", "tempEvent", "isEmbedded", "indice", "fieldName", "writeFieldName", "ionType", "SEXP", "LIST", "setAnnotations", "annotations", "eventType", "SCALAR", "ionValue", "writeNull", "writeString", "toString", "BOOL", "writeBoolean", "STRING", "SYMBOL", "INT", "writeInt", "DECIMAL", "writeDecimal", "FLOAT", "writeFloat64", "NULL", "TIMESTAMP", "writeTimestamp", "CLOB", "writeClob", "BLOB", "writeBlob", "Error", "name", "CONTAINER_START", "stepIn", "CONTAINER_END", "stepOut", "STREAM_END", "SYMBOL_TABLE", "close", "error", "message", "getEvents", "equals", "expected", "compare", "result", "EQUAL", "actualIndex", "expectedIndex", "NOT_EQUAL", "actualEvent", "expectedEvent", "eventResult", "event", "tid", "next", "stringValue", "marshal<PERSON><PERSON>am", "currentC<PERSON><PERSON>", "currentContainerIndex", "isNull", "push", "makeEvent", "depth", "value", "STRUCT", "containerEvent", "undefined", "endContainer", "pop", "thisContainer", "thisContainerIndex", "slice", "marshalEvent", "currentEvent", "parseIonType", "resolveFieldNameFromSerializedSymbolToken", "parseAnnotations", "tempString", "substr", "temp<PERSON><PERSON><PERSON>", "tempValue", "parseBinaryValue", "parseImports", "numberValue", "fieldname", "textEvent", "binaryEvent", "input", "toLowerCase", "type", "text", "symtab", "symbol", "getSymbolText", "numB<PERSON><PERSON>", "bufArray", "Uint8Array"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/events/IonEventStream.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { ComparisonResult } from \"../ComparisonResult\";\nimport { ComparisonResultType } from \"../ComparisonResult\";\nimport { defaultLocalSymbolTable, makeReader } from \"../Ion\";\nimport { BinaryReader } from \"../IonBinaryReader\";\nimport { BinarySpan } from \"../IonSpan\";\nimport { IonTypes } from \"../IonTypes\";\nimport { EventStreamError } from \"./EventStreamError\";\nimport { IonEventFactory, IonEventType } from \"./IonEvent\";\nconst READ = \"READ\";\nconst WRITE = \"WRITE\";\nexport class IonEventStream {\n    constructor(reader) {\n        this.events = [];\n        this.reader = reader;\n        this.eventFactory = new IonEventFactory();\n        this.isEventStream = false;\n        this.generateStream();\n    }\n    writeEventStream(writer) {\n        writer.writeSymbol(\"$ion_event_stream\");\n        for (let i = 0; i < this.events.length; i++) {\n            this.events[i].write(writer);\n        }\n    }\n    writeIon(writer) {\n        try {\n            let tempEvent;\n            let isEmbedded = false;\n            for (let indice = 0; indice < this.events.length; indice++) {\n                tempEvent = this.events[indice];\n                if (tempEvent.fieldName !== null) {\n                    writer.writeFieldName(tempEvent.fieldName);\n                }\n                if ((tempEvent.ionType == IonTypes.SEXP ||\n                    tempEvent.ionType == IonTypes.LIST) &&\n                    this.isEmbedded(tempEvent)) {\n                    isEmbedded = true;\n                }\n                writer.setAnnotations(tempEvent.annotations);\n                switch (tempEvent.eventType) {\n                    case IonEventType.SCALAR:\n                        if (tempEvent.ionValue == null) {\n                            writer.writeNull(tempEvent.ionType);\n                            return;\n                        }\n                        if (isEmbedded) {\n                            writer.writeString(tempEvent.ionValue.toString());\n                            break;\n                        }\n                        switch (tempEvent.ionType) {\n                            case IonTypes.BOOL:\n                                writer.writeBoolean(tempEvent.ionValue);\n                                break;\n                            case IonTypes.STRING:\n                                writer.writeString(tempEvent.ionValue);\n                                break;\n                            case IonTypes.SYMBOL:\n                                writer.writeSymbol(tempEvent.ionValue);\n                                break;\n                            case IonTypes.INT:\n                                writer.writeInt(tempEvent.ionValue);\n                                break;\n                            case IonTypes.DECIMAL:\n                                writer.writeDecimal(tempEvent.ionValue);\n                                break;\n                            case IonTypes.FLOAT:\n                                writer.writeFloat64(tempEvent.ionValue);\n                                break;\n                            case IonTypes.NULL:\n                                writer.writeNull(tempEvent.ionType);\n                                break;\n                            case IonTypes.TIMESTAMP:\n                                writer.writeTimestamp(tempEvent.ionValue);\n                                break;\n                            case IonTypes.CLOB:\n                                writer.writeClob(tempEvent.ionValue);\n                                break;\n                            case IonTypes.BLOB:\n                                writer.writeBlob(tempEvent.ionValue);\n                                break;\n                            default:\n                                throw new Error(\"unexpected type: \" + tempEvent.ionType.name);\n                        }\n                        break;\n                    case IonEventType.CONTAINER_START:\n                        writer.stepIn(tempEvent.ionType);\n                        break;\n                    case IonEventType.CONTAINER_END:\n                        if (isEmbedded) {\n                            isEmbedded = false;\n                        }\n                        writer.stepOut();\n                        break;\n                    case IonEventType.STREAM_END:\n                        break;\n                    case IonEventType.SYMBOL_TABLE:\n                        throw new Error(\"Symboltables unsupported.\");\n                    default:\n                        throw new Error(\"Unexpected event type: \" + tempEvent.eventType);\n                }\n            }\n            writer.close();\n        }\n        catch (error) {\n            throw new EventStreamError(WRITE, error.message, this.events.length, this.events);\n        }\n    }\n    getEvents() {\n        return this.events;\n    }\n    equals(expected) {\n        return this.compare(expected).result == ComparisonResultType.EQUAL;\n    }\n    compare(expected) {\n        let actualIndex = 0;\n        let expectedIndex = 0;\n        if (this.events.length != expected.events.length) {\n            return new ComparisonResult(ComparisonResultType.NOT_EQUAL, \"The event streams have different lengths\");\n        }\n        while (actualIndex < this.events.length &&\n            expectedIndex < expected.events.length) {\n            const actualEvent = this.events[actualIndex];\n            const expectedEvent = expected.events[expectedIndex];\n            if (actualEvent.eventType === IonEventType.SYMBOL_TABLE) {\n                actualIndex++;\n            }\n            if (expectedEvent.eventType === IonEventType.SYMBOL_TABLE) {\n                expectedIndex++;\n            }\n            if (actualEvent.eventType === IonEventType.SYMBOL_TABLE ||\n                expectedEvent.eventType === IonEventType.SYMBOL_TABLE) {\n                continue;\n            }\n            switch (actualEvent.eventType) {\n                case IonEventType.SCALAR: {\n                    const eventResult = actualEvent.compare(expectedEvent);\n                    if (eventResult.result == ComparisonResultType.NOT_EQUAL) {\n                        eventResult.actualIndex = actualIndex;\n                        eventResult.expectedIndex = expectedIndex;\n                        return eventResult;\n                    }\n                    break;\n                }\n                case IonEventType.CONTAINER_START: {\n                    const eventResult = actualEvent.compare(expectedEvent);\n                    if (eventResult.result == ComparisonResultType.NOT_EQUAL) {\n                        actualIndex += eventResult.actualIndex;\n                        expectedIndex += eventResult.expectedIndex;\n                        eventResult.actualIndex = actualIndex;\n                        eventResult.expectedIndex = expectedIndex;\n                        return eventResult;\n                    }\n                    else {\n                        if (actualEvent.ionValue !== null &&\n                            expectedEvent.ionValue !== null) {\n                            actualIndex = actualIndex + actualEvent.ionValue.length;\n                            expectedIndex = expectedIndex + expectedEvent.ionValue.length;\n                        }\n                    }\n                    break;\n                }\n                case IonEventType.CONTAINER_END:\n                case IonEventType.STREAM_END: {\n                    break;\n                }\n                default: {\n                    throw new Error(\"Unexpected event type: \" + actualEvent.eventType);\n                }\n            }\n            actualIndex++;\n            expectedIndex++;\n        }\n        return new ComparisonResult(ComparisonResultType.EQUAL);\n    }\n    isEmbedded(event) {\n        if (event.annotations[0] === \"embedded_documents\") {\n            return true;\n        }\n        return false;\n    }\n    generateStream() {\n        try {\n            let tid = this.reader.next();\n            if (tid === IonTypes.SYMBOL &&\n                this.reader.stringValue() === \"$ion_event_stream\") {\n                this.marshalStream();\n                this.isEventStream = true;\n                return;\n            }\n            const currentContainer = [];\n            const currentContainerIndex = [];\n            while (true) {\n                if (this.reader.isNull()) {\n                    this.events.push(this.eventFactory.makeEvent(IonEventType.SCALAR, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), true, this.reader.value()));\n                }\n                else {\n                    switch (tid) {\n                        case IonTypes.LIST:\n                        case IonTypes.SEXP:\n                        case IonTypes.STRUCT: {\n                            const containerEvent = this.eventFactory.makeEvent(IonEventType.CONTAINER_START, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), false, null);\n                            this.events.push(containerEvent);\n                            currentContainer.push(containerEvent);\n                            currentContainerIndex.push(this.events.length);\n                            this.reader.stepIn();\n                            break;\n                        }\n                        case null: {\n                            if (this.reader.depth() === 0) {\n                                this.events.push(this.eventFactory.makeEvent(IonEventType.STREAM_END, IonTypes.NULL, null, this.reader.depth(), [], false, undefined));\n                                return;\n                            }\n                            else {\n                                this.reader.stepOut();\n                                this.endContainer(currentContainer.pop(), currentContainerIndex.pop());\n                            }\n                            break;\n                        }\n                        default: {\n                            this.events.push(this.eventFactory.makeEvent(IonEventType.SCALAR, tid, this.reader.fieldName(), this.reader.depth(), this.reader.annotations(), false, this.reader.value()));\n                            break;\n                        }\n                    }\n                }\n                tid = this.reader.next();\n            }\n        }\n        catch (error) {\n            throw new EventStreamError(READ, error.message, this.events.length, this.events);\n        }\n    }\n    endContainer(thisContainer, thisContainerIndex) {\n        this.events.push(this.eventFactory.makeEvent(IonEventType.CONTAINER_END, thisContainer.ionType, null, thisContainer.depth, [], false, null));\n        thisContainer.ionValue = this.events.slice(thisContainerIndex, this.events.length);\n    }\n    marshalStream() {\n        this.events = [];\n        const currentContainer = [];\n        const currentContainerIndex = [];\n        for (let tid = this.reader.next(); tid === IonTypes.STRUCT; tid = this.reader.next()) {\n            this.reader.stepIn();\n            const tempEvent = this.marshalEvent();\n            if (tempEvent.eventType === IonEventType.CONTAINER_START) {\n                currentContainer.push(tempEvent);\n                this.events.push(tempEvent);\n                currentContainerIndex.push(this.events.length);\n            }\n            else if (tempEvent.eventType === IonEventType.CONTAINER_END) {\n                this.endContainer(currentContainer.pop(), currentContainerIndex.pop());\n            }\n            else if (tempEvent.eventType === IonEventType.SCALAR ||\n                tempEvent.eventType === IonEventType.STREAM_END) {\n                this.events.push(tempEvent);\n            }\n            else {\n                throw new Error(\"Unexpected eventType: \" + tempEvent.eventType);\n            }\n            this.reader.stepOut();\n        }\n    }\n    marshalEvent() {\n        const currentEvent = {};\n        for (let tid; (tid = this.reader.next());) {\n            const fieldName = this.reader.fieldName();\n            if (fieldName && currentEvent[fieldName] !== undefined) {\n                throw new Error(\"Repeated event field: \" + fieldName);\n            }\n            switch (fieldName) {\n                case \"event_type\": {\n                    currentEvent[fieldName] = this.reader.stringValue();\n                    break;\n                }\n                case \"ion_type\": {\n                    currentEvent[fieldName] = this.parseIonType();\n                    break;\n                }\n                case \"field_name\": {\n                    currentEvent[fieldName] = this.resolveFieldNameFromSerializedSymbolToken();\n                    break;\n                }\n                case \"annotations\": {\n                    currentEvent[fieldName] = this.parseAnnotations();\n                    break;\n                }\n                case \"value_text\": {\n                    let tempString = this.reader.stringValue();\n                    if (tempString.substr(0, 5) === \"$ion_\") {\n                        tempString = \"$ion_user_value::\" + tempString;\n                    }\n                    const tempReader = makeReader(tempString);\n                    tempReader.next();\n                    const tempValue = tempReader.value();\n                    currentEvent[\"isNull\"] = tempReader.isNull();\n                    currentEvent[fieldName] = tempValue;\n                    break;\n                }\n                case \"value_binary\": {\n                    currentEvent[fieldName] = this.parseBinaryValue();\n                    break;\n                }\n                case \"imports\": {\n                    currentEvent[fieldName] = this.parseImports();\n                    break;\n                }\n                case \"depth\": {\n                    currentEvent[fieldName] = this.reader.numberValue();\n                    break;\n                }\n                default:\n                    throw new Error(\"Unexpected event field: \" + fieldName);\n            }\n        }\n        let eventType;\n        switch (currentEvent[\"event_type\"]) {\n            case \"CONTAINER_START\":\n                eventType = IonEventType.CONTAINER_START;\n                break;\n            case \"STREAM_END\":\n                eventType = IonEventType.STREAM_END;\n                break;\n            case \"CONTAINER_END\":\n                eventType = IonEventType.CONTAINER_END;\n                break;\n            case \"SCALAR\":\n                eventType = IonEventType.SCALAR;\n                break;\n            case \"SYMBOL_TABLE\":\n                throw new Error(\"Symbol tables unsupported\");\n        }\n        const fieldname = currentEvent[\"field_name\"] !== undefined\n            ? currentEvent[\"field_name\"]\n            : null;\n        if (!currentEvent[\"annotations\"]) {\n            currentEvent[\"annotations\"] = [];\n        }\n        const textEvent = this.eventFactory.makeEvent(eventType, currentEvent[\"ion_type\"], fieldname, currentEvent[\"depth\"], currentEvent[\"annotations\"], currentEvent[\"isNull\"], currentEvent[\"value_text\"]);\n        if (eventType === IonEventType.SCALAR) {\n            const binaryEvent = this.eventFactory.makeEvent(eventType, currentEvent[\"ion_type\"], fieldname, currentEvent[\"depth\"], currentEvent[\"annotations\"], currentEvent[\"isNull\"], currentEvent[\"value_binary\"]);\n            if (!textEvent.equals(binaryEvent)) {\n                throw new Error(`Text event ${currentEvent[\"value_text\"]} does not equal binary event ${currentEvent[\"value_binary\"]}`);\n            }\n        }\n        return textEvent;\n    }\n    parseIonType() {\n        const input = this.reader.stringValue().toLowerCase();\n        switch (input) {\n            case \"null\": {\n                return IonTypes.NULL;\n            }\n            case \"bool\": {\n                return IonTypes.BOOL;\n            }\n            case \"int\": {\n                return IonTypes.INT;\n            }\n            case \"float\": {\n                return IonTypes.FLOAT;\n            }\n            case \"decimal\": {\n                return IonTypes.DECIMAL;\n            }\n            case \"timestamp\": {\n                return IonTypes.TIMESTAMP;\n            }\n            case \"symbol\": {\n                return IonTypes.SYMBOL;\n            }\n            case \"string\": {\n                return IonTypes.STRING;\n            }\n            case \"clob\": {\n                return IonTypes.CLOB;\n            }\n            case \"blob\": {\n                return IonTypes.BLOB;\n            }\n            case \"list\": {\n                return IonTypes.LIST;\n            }\n            case \"sexp\": {\n                return IonTypes.SEXP;\n            }\n            case \"struct\": {\n                return IonTypes.STRUCT;\n            }\n            default: {\n                throw new Error(\"i: \" + input);\n            }\n        }\n    }\n    parseAnnotations() {\n        const annotations = [];\n        if (this.reader.isNull()) {\n            return annotations;\n        }\n        else {\n            this.reader.stepIn();\n            for (let tid; (tid = this.reader.next());) {\n                if (tid == IonTypes.STRUCT) {\n                    this.reader.stepIn();\n                    const type = this.reader.next();\n                    if (this.reader.fieldName() == \"text\" && type == IonTypes.STRING) {\n                        const text = this.reader.stringValue();\n                        if (text !== null) {\n                            annotations.push(text);\n                        }\n                    }\n                    else if (this.reader.fieldName() == \"importLocation\" &&\n                        type == IonTypes.INT) {\n                        const symtab = defaultLocalSymbolTable();\n                        const symbol = symtab.getSymbolText(this.reader.numberValue());\n                        if (symbol === undefined || symbol === null) {\n                            throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n                        }\n                        annotations.push(symbol);\n                    }\n                    this.reader.stepOut();\n                }\n            }\n            this.reader.stepOut();\n            return annotations;\n        }\n    }\n    parseBinaryValue() {\n        if (this.reader.isNull()) {\n            return null;\n        }\n        const numBuffer = [];\n        this.reader.stepIn();\n        let tid = this.reader.next();\n        while (tid) {\n            numBuffer.push(this.reader.numberValue());\n            tid = this.reader.next();\n        }\n        this.reader.stepOut();\n        const bufArray = new Uint8Array(numBuffer);\n        const tempReader = new BinaryReader(new BinarySpan(bufArray));\n        tempReader.next();\n        return tempReader.value();\n    }\n    parseImports() {\n        return this.reader.value();\n    }\n    resolveFieldNameFromSerializedSymbolToken() {\n        if (this.reader.isNull()) {\n            return null;\n        }\n        this.reader.stepIn();\n        const type = this.reader.next();\n        if (this.reader.fieldName() == \"text\" && type == IonTypes.STRING) {\n            const text = this.reader.stringValue();\n            if (text !== null) {\n                this.reader.stepOut();\n                return text;\n            }\n        }\n        else if (this.reader.fieldName() == \"importLocation\" &&\n            type == IonTypes.INT) {\n            const symtab = defaultLocalSymbolTable();\n            const symbol = symtab.getSymbolText(this.reader.numberValue());\n            if (symbol === undefined || symbol === null) {\n                throw new Error(\"Unresolvable symbol ID, symboltokens unsupported.\");\n            }\n            this.reader.stepOut();\n            return symbol;\n        }\n        return null;\n    }\n}\n//# sourceMappingURL=IonEventStream.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,uBAAuB,EAAEC,UAAU,QAAQ,QAAQ;AAC5D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,EAAEC,YAAY,QAAQ,YAAY;AAC1D,MAAMC,IAAI,GAAG,MAAM;AACnB,MAAMC,KAAK,GAAG,OAAO;AACrB,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,YAAY,GAAG,IAAIR,eAAe,CAAC,CAAC;IACzC,IAAI,CAACS,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,gBAAgBA,CAACC,MAAM,EAAE;IACrBA,MAAM,CAACC,WAAW,CAAC,mBAAmB,CAAC;IACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,MAAM,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAI,CAACP,MAAM,CAACO,CAAC,CAAC,CAACE,KAAK,CAACJ,MAAM,CAAC;IAChC;EACJ;EACAK,QAAQA,CAACL,MAAM,EAAE;IACb,IAAI;MACA,IAAIM,SAAS;MACb,IAAIC,UAAU,GAAG,KAAK;MACtB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,IAAI,CAACb,MAAM,CAACQ,MAAM,EAAEK,MAAM,EAAE,EAAE;QACxDF,SAAS,GAAG,IAAI,CAACX,MAAM,CAACa,MAAM,CAAC;QAC/B,IAAIF,SAAS,CAACG,SAAS,KAAK,IAAI,EAAE;UAC9BT,MAAM,CAACU,cAAc,CAACJ,SAAS,CAACG,SAAS,CAAC;QAC9C;QACA,IAAI,CAACH,SAAS,CAACK,OAAO,IAAIzB,QAAQ,CAAC0B,IAAI,IACnCN,SAAS,CAACK,OAAO,IAAIzB,QAAQ,CAAC2B,IAAI,KAClC,IAAI,CAACN,UAAU,CAACD,SAAS,CAAC,EAAE;UAC5BC,UAAU,GAAG,IAAI;QACrB;QACAP,MAAM,CAACc,cAAc,CAACR,SAAS,CAACS,WAAW,CAAC;QAC5C,QAAQT,SAAS,CAACU,SAAS;UACvB,KAAK3B,YAAY,CAAC4B,MAAM;YACpB,IAAIX,SAAS,CAACY,QAAQ,IAAI,IAAI,EAAE;cAC5BlB,MAAM,CAACmB,SAAS,CAACb,SAAS,CAACK,OAAO,CAAC;cACnC;YACJ;YACA,IAAIJ,UAAU,EAAE;cACZP,MAAM,CAACoB,WAAW,CAACd,SAAS,CAACY,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC;cACjD;YACJ;YACA,QAAQf,SAAS,CAACK,OAAO;cACrB,KAAKzB,QAAQ,CAACoC,IAAI;gBACdtB,MAAM,CAACuB,YAAY,CAACjB,SAAS,CAACY,QAAQ,CAAC;gBACvC;cACJ,KAAKhC,QAAQ,CAACsC,MAAM;gBAChBxB,MAAM,CAACoB,WAAW,CAACd,SAAS,CAACY,QAAQ,CAAC;gBACtC;cACJ,KAAKhC,QAAQ,CAACuC,MAAM;gBAChBzB,MAAM,CAACC,WAAW,CAACK,SAAS,CAACY,QAAQ,CAAC;gBACtC;cACJ,KAAKhC,QAAQ,CAACwC,GAAG;gBACb1B,MAAM,CAAC2B,QAAQ,CAACrB,SAAS,CAACY,QAAQ,CAAC;gBACnC;cACJ,KAAKhC,QAAQ,CAAC0C,OAAO;gBACjB5B,MAAM,CAAC6B,YAAY,CAACvB,SAAS,CAACY,QAAQ,CAAC;gBACvC;cACJ,KAAKhC,QAAQ,CAAC4C,KAAK;gBACf9B,MAAM,CAAC+B,YAAY,CAACzB,SAAS,CAACY,QAAQ,CAAC;gBACvC;cACJ,KAAKhC,QAAQ,CAAC8C,IAAI;gBACdhC,MAAM,CAACmB,SAAS,CAACb,SAAS,CAACK,OAAO,CAAC;gBACnC;cACJ,KAAKzB,QAAQ,CAAC+C,SAAS;gBACnBjC,MAAM,CAACkC,cAAc,CAAC5B,SAAS,CAACY,QAAQ,CAAC;gBACzC;cACJ,KAAKhC,QAAQ,CAACiD,IAAI;gBACdnC,MAAM,CAACoC,SAAS,CAAC9B,SAAS,CAACY,QAAQ,CAAC;gBACpC;cACJ,KAAKhC,QAAQ,CAACmD,IAAI;gBACdrC,MAAM,CAACsC,SAAS,CAAChC,SAAS,CAACY,QAAQ,CAAC;gBACpC;cACJ;gBACI,MAAM,IAAIqB,KAAK,CAAC,mBAAmB,GAAGjC,SAAS,CAACK,OAAO,CAAC6B,IAAI,CAAC;YACrE;YACA;UACJ,KAAKnD,YAAY,CAACoD,eAAe;YAC7BzC,MAAM,CAAC0C,MAAM,CAACpC,SAAS,CAACK,OAAO,CAAC;YAChC;UACJ,KAAKtB,YAAY,CAACsD,aAAa;YAC3B,IAAIpC,UAAU,EAAE;cACZA,UAAU,GAAG,KAAK;YACtB;YACAP,MAAM,CAAC4C,OAAO,CAAC,CAAC;YAChB;UACJ,KAAKvD,YAAY,CAACwD,UAAU;YACxB;UACJ,KAAKxD,YAAY,CAACyD,YAAY;YAC1B,MAAM,IAAIP,KAAK,CAAC,2BAA2B,CAAC;UAChD;YACI,MAAM,IAAIA,KAAK,CAAC,yBAAyB,GAAGjC,SAAS,CAACU,SAAS,CAAC;QACxE;MACJ;MACAhB,MAAM,CAAC+C,KAAK,CAAC,CAAC;IAClB,CAAC,CACD,OAAOC,KAAK,EAAE;MACV,MAAM,IAAI7D,gBAAgB,CAACI,KAAK,EAAEyD,KAAK,CAACC,OAAO,EAAE,IAAI,CAACtD,MAAM,CAACQ,MAAM,EAAE,IAAI,CAACR,MAAM,CAAC;IACrF;EACJ;EACAuD,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACvD,MAAM;EACtB;EACAwD,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI,CAACC,OAAO,CAACD,QAAQ,CAAC,CAACE,MAAM,IAAIzE,oBAAoB,CAAC0E,KAAK;EACtE;EACAF,OAAOA,CAACD,QAAQ,EAAE;IACd,IAAII,WAAW,GAAG,CAAC;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAI,IAAI,CAAC9D,MAAM,CAACQ,MAAM,IAAIiD,QAAQ,CAACzD,MAAM,CAACQ,MAAM,EAAE;MAC9C,OAAO,IAAIvB,gBAAgB,CAACC,oBAAoB,CAAC6E,SAAS,EAAE,0CAA0C,CAAC;IAC3G;IACA,OAAOF,WAAW,GAAG,IAAI,CAAC7D,MAAM,CAACQ,MAAM,IACnCsD,aAAa,GAAGL,QAAQ,CAACzD,MAAM,CAACQ,MAAM,EAAE;MACxC,MAAMwD,WAAW,GAAG,IAAI,CAAChE,MAAM,CAAC6D,WAAW,CAAC;MAC5C,MAAMI,aAAa,GAAGR,QAAQ,CAACzD,MAAM,CAAC8D,aAAa,CAAC;MACpD,IAAIE,WAAW,CAAC3C,SAAS,KAAK3B,YAAY,CAACyD,YAAY,EAAE;QACrDU,WAAW,EAAE;MACjB;MACA,IAAII,aAAa,CAAC5C,SAAS,KAAK3B,YAAY,CAACyD,YAAY,EAAE;QACvDW,aAAa,EAAE;MACnB;MACA,IAAIE,WAAW,CAAC3C,SAAS,KAAK3B,YAAY,CAACyD,YAAY,IACnDc,aAAa,CAAC5C,SAAS,KAAK3B,YAAY,CAACyD,YAAY,EAAE;QACvD;MACJ;MACA,QAAQa,WAAW,CAAC3C,SAAS;QACzB,KAAK3B,YAAY,CAAC4B,MAAM;UAAE;YACtB,MAAM4C,WAAW,GAAGF,WAAW,CAACN,OAAO,CAACO,aAAa,CAAC;YACtD,IAAIC,WAAW,CAACP,MAAM,IAAIzE,oBAAoB,CAAC6E,SAAS,EAAE;cACtDG,WAAW,CAACL,WAAW,GAAGA,WAAW;cACrCK,WAAW,CAACJ,aAAa,GAAGA,aAAa;cACzC,OAAOI,WAAW;YACtB;YACA;UACJ;QACA,KAAKxE,YAAY,CAACoD,eAAe;UAAE;YAC/B,MAAMoB,WAAW,GAAGF,WAAW,CAACN,OAAO,CAACO,aAAa,CAAC;YACtD,IAAIC,WAAW,CAACP,MAAM,IAAIzE,oBAAoB,CAAC6E,SAAS,EAAE;cACtDF,WAAW,IAAIK,WAAW,CAACL,WAAW;cACtCC,aAAa,IAAII,WAAW,CAACJ,aAAa;cAC1CI,WAAW,CAACL,WAAW,GAAGA,WAAW;cACrCK,WAAW,CAACJ,aAAa,GAAGA,aAAa;cACzC,OAAOI,WAAW;YACtB,CAAC,MACI;cACD,IAAIF,WAAW,CAACzC,QAAQ,KAAK,IAAI,IAC7B0C,aAAa,CAAC1C,QAAQ,KAAK,IAAI,EAAE;gBACjCsC,WAAW,GAAGA,WAAW,GAAGG,WAAW,CAACzC,QAAQ,CAACf,MAAM;gBACvDsD,aAAa,GAAGA,aAAa,GAAGG,aAAa,CAAC1C,QAAQ,CAACf,MAAM;cACjE;YACJ;YACA;UACJ;QACA,KAAKd,YAAY,CAACsD,aAAa;QAC/B,KAAKtD,YAAY,CAACwD,UAAU;UAAE;YAC1B;UACJ;QACA;UAAS;YACL,MAAM,IAAIN,KAAK,CAAC,yBAAyB,GAAGoB,WAAW,CAAC3C,SAAS,CAAC;UACtE;MACJ;MACAwC,WAAW,EAAE;MACbC,aAAa,EAAE;IACnB;IACA,OAAO,IAAI7E,gBAAgB,CAACC,oBAAoB,CAAC0E,KAAK,CAAC;EAC3D;EACAhD,UAAUA,CAACuD,KAAK,EAAE;IACd,IAAIA,KAAK,CAAC/C,WAAW,CAAC,CAAC,CAAC,KAAK,oBAAoB,EAAE;MAC/C,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAjB,cAAcA,CAAA,EAAG;IACb,IAAI;MACA,IAAIiE,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC;MAC5B,IAAID,GAAG,KAAK7E,QAAQ,CAACuC,MAAM,IACvB,IAAI,CAAC/B,MAAM,CAACuE,WAAW,CAAC,CAAC,KAAK,mBAAmB,EAAE;QACnD,IAAI,CAACC,aAAa,CAAC,CAAC;QACpB,IAAI,CAACrE,aAAa,GAAG,IAAI;QACzB;MACJ;MACA,MAAMsE,gBAAgB,GAAG,EAAE;MAC3B,MAAMC,qBAAqB,GAAG,EAAE;MAChC,OAAO,IAAI,EAAE;QACT,IAAI,IAAI,CAAC1E,MAAM,CAAC2E,MAAM,CAAC,CAAC,EAAE;UACtB,IAAI,CAAC1E,MAAM,CAAC2E,IAAI,CAAC,IAAI,CAAC1E,YAAY,CAAC2E,SAAS,CAAClF,YAAY,CAAC4B,MAAM,EAAE8C,GAAG,EAAE,IAAI,CAACrE,MAAM,CAACe,SAAS,CAAC,CAAC,EAAE,IAAI,CAACf,MAAM,CAAC8E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC9E,MAAM,CAACqB,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAACrB,MAAM,CAAC+E,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/K,CAAC,MACI;UACD,QAAQV,GAAG;YACP,KAAK7E,QAAQ,CAAC2B,IAAI;YAClB,KAAK3B,QAAQ,CAAC0B,IAAI;YAClB,KAAK1B,QAAQ,CAACwF,MAAM;cAAE;gBAClB,MAAMC,cAAc,GAAG,IAAI,CAAC/E,YAAY,CAAC2E,SAAS,CAAClF,YAAY,CAACoD,eAAe,EAAEsB,GAAG,EAAE,IAAI,CAACrE,MAAM,CAACe,SAAS,CAAC,CAAC,EAAE,IAAI,CAACf,MAAM,CAAC8E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC9E,MAAM,CAACqB,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;gBAC3K,IAAI,CAACpB,MAAM,CAAC2E,IAAI,CAACK,cAAc,CAAC;gBAChCR,gBAAgB,CAACG,IAAI,CAACK,cAAc,CAAC;gBACrCP,qBAAqB,CAACE,IAAI,CAAC,IAAI,CAAC3E,MAAM,CAACQ,MAAM,CAAC;gBAC9C,IAAI,CAACT,MAAM,CAACgD,MAAM,CAAC,CAAC;gBACpB;cACJ;YACA,KAAK,IAAI;cAAE;gBACP,IAAI,IAAI,CAAChD,MAAM,CAAC8E,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;kBAC3B,IAAI,CAAC7E,MAAM,CAAC2E,IAAI,CAAC,IAAI,CAAC1E,YAAY,CAAC2E,SAAS,CAAClF,YAAY,CAACwD,UAAU,EAAE3D,QAAQ,CAAC8C,IAAI,EAAE,IAAI,EAAE,IAAI,CAACtC,MAAM,CAAC8E,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAEI,SAAS,CAAC,CAAC;kBACtI;gBACJ,CAAC,MACI;kBACD,IAAI,CAAClF,MAAM,CAACkD,OAAO,CAAC,CAAC;kBACrB,IAAI,CAACiC,YAAY,CAACV,gBAAgB,CAACW,GAAG,CAAC,CAAC,EAAEV,qBAAqB,CAACU,GAAG,CAAC,CAAC,CAAC;gBAC1E;gBACA;cACJ;YACA;cAAS;gBACL,IAAI,CAACnF,MAAM,CAAC2E,IAAI,CAAC,IAAI,CAAC1E,YAAY,CAAC2E,SAAS,CAAClF,YAAY,CAAC4B,MAAM,EAAE8C,GAAG,EAAE,IAAI,CAACrE,MAAM,CAACe,SAAS,CAAC,CAAC,EAAE,IAAI,CAACf,MAAM,CAAC8E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC9E,MAAM,CAACqB,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAACrB,MAAM,CAAC+E,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5K;cACJ;UACJ;QACJ;QACAV,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC;MAC5B;IACJ,CAAC,CACD,OAAOhB,KAAK,EAAE;MACV,MAAM,IAAI7D,gBAAgB,CAACG,IAAI,EAAE0D,KAAK,CAACC,OAAO,EAAE,IAAI,CAACtD,MAAM,CAACQ,MAAM,EAAE,IAAI,CAACR,MAAM,CAAC;IACpF;EACJ;EACAkF,YAAYA,CAACE,aAAa,EAAEC,kBAAkB,EAAE;IAC5C,IAAI,CAACrF,MAAM,CAAC2E,IAAI,CAAC,IAAI,CAAC1E,YAAY,CAAC2E,SAAS,CAAClF,YAAY,CAACsD,aAAa,EAAEoC,aAAa,CAACpE,OAAO,EAAE,IAAI,EAAEoE,aAAa,CAACP,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5IO,aAAa,CAAC7D,QAAQ,GAAG,IAAI,CAACvB,MAAM,CAACsF,KAAK,CAACD,kBAAkB,EAAE,IAAI,CAACrF,MAAM,CAACQ,MAAM,CAAC;EACtF;EACA+D,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACvE,MAAM,GAAG,EAAE;IAChB,MAAMwE,gBAAgB,GAAG,EAAE;IAC3B,MAAMC,qBAAqB,GAAG,EAAE;IAChC,KAAK,IAAIL,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC,EAAED,GAAG,KAAK7E,QAAQ,CAACwF,MAAM,EAAEX,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC,EAAE;MAClF,IAAI,CAACtE,MAAM,CAACgD,MAAM,CAAC,CAAC;MACpB,MAAMpC,SAAS,GAAG,IAAI,CAAC4E,YAAY,CAAC,CAAC;MACrC,IAAI5E,SAAS,CAACU,SAAS,KAAK3B,YAAY,CAACoD,eAAe,EAAE;QACtD0B,gBAAgB,CAACG,IAAI,CAAChE,SAAS,CAAC;QAChC,IAAI,CAACX,MAAM,CAAC2E,IAAI,CAAChE,SAAS,CAAC;QAC3B8D,qBAAqB,CAACE,IAAI,CAAC,IAAI,CAAC3E,MAAM,CAACQ,MAAM,CAAC;MAClD,CAAC,MACI,IAAIG,SAAS,CAACU,SAAS,KAAK3B,YAAY,CAACsD,aAAa,EAAE;QACzD,IAAI,CAACkC,YAAY,CAACV,gBAAgB,CAACW,GAAG,CAAC,CAAC,EAAEV,qBAAqB,CAACU,GAAG,CAAC,CAAC,CAAC;MAC1E,CAAC,MACI,IAAIxE,SAAS,CAACU,SAAS,KAAK3B,YAAY,CAAC4B,MAAM,IAChDX,SAAS,CAACU,SAAS,KAAK3B,YAAY,CAACwD,UAAU,EAAE;QACjD,IAAI,CAAClD,MAAM,CAAC2E,IAAI,CAAChE,SAAS,CAAC;MAC/B,CAAC,MACI;QACD,MAAM,IAAIiC,KAAK,CAAC,wBAAwB,GAAGjC,SAAS,CAACU,SAAS,CAAC;MACnE;MACA,IAAI,CAACtB,MAAM,CAACkD,OAAO,CAAC,CAAC;IACzB;EACJ;EACAsC,YAAYA,CAAA,EAAG;IACX,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvB,KAAK,IAAIpB,GAAG,EAAGA,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC,GAAI;MACvC,MAAMvD,SAAS,GAAG,IAAI,CAACf,MAAM,CAACe,SAAS,CAAC,CAAC;MACzC,IAAIA,SAAS,IAAI0E,YAAY,CAAC1E,SAAS,CAAC,KAAKmE,SAAS,EAAE;QACpD,MAAM,IAAIrC,KAAK,CAAC,wBAAwB,GAAG9B,SAAS,CAAC;MACzD;MACA,QAAQA,SAAS;QACb,KAAK,YAAY;UAAE;YACf0E,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAACf,MAAM,CAACuE,WAAW,CAAC,CAAC;YACnD;UACJ;QACA,KAAK,UAAU;UAAE;YACbkB,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAAC2E,YAAY,CAAC,CAAC;YAC7C;UACJ;QACA,KAAK,YAAY;UAAE;YACfD,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAAC4E,yCAAyC,CAAC,CAAC;YAC1E;UACJ;QACA,KAAK,aAAa;UAAE;YAChBF,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAAC6E,gBAAgB,CAAC,CAAC;YACjD;UACJ;QACA,KAAK,YAAY;UAAE;YACf,IAAIC,UAAU,GAAG,IAAI,CAAC7F,MAAM,CAACuE,WAAW,CAAC,CAAC;YAC1C,IAAIsB,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;cACrCD,UAAU,GAAG,mBAAmB,GAAGA,UAAU;YACjD;YACA,MAAME,UAAU,GAAG1G,UAAU,CAACwG,UAAU,CAAC;YACzCE,UAAU,CAACzB,IAAI,CAAC,CAAC;YACjB,MAAM0B,SAAS,GAAGD,UAAU,CAAChB,KAAK,CAAC,CAAC;YACpCU,YAAY,CAAC,QAAQ,CAAC,GAAGM,UAAU,CAACpB,MAAM,CAAC,CAAC;YAC5Cc,YAAY,CAAC1E,SAAS,CAAC,GAAGiF,SAAS;YACnC;UACJ;QACA,KAAK,cAAc;UAAE;YACjBP,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAACkF,gBAAgB,CAAC,CAAC;YACjD;UACJ;QACA,KAAK,SAAS;UAAE;YACZR,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAACmF,YAAY,CAAC,CAAC;YAC7C;UACJ;QACA,KAAK,OAAO;UAAE;YACVT,YAAY,CAAC1E,SAAS,CAAC,GAAG,IAAI,CAACf,MAAM,CAACmG,WAAW,CAAC,CAAC;YACnD;UACJ;QACA;UACI,MAAM,IAAItD,KAAK,CAAC,0BAA0B,GAAG9B,SAAS,CAAC;MAC/D;IACJ;IACA,IAAIO,SAAS;IACb,QAAQmE,YAAY,CAAC,YAAY,CAAC;MAC9B,KAAK,iBAAiB;QAClBnE,SAAS,GAAG3B,YAAY,CAACoD,eAAe;QACxC;MACJ,KAAK,YAAY;QACbzB,SAAS,GAAG3B,YAAY,CAACwD,UAAU;QACnC;MACJ,KAAK,eAAe;QAChB7B,SAAS,GAAG3B,YAAY,CAACsD,aAAa;QACtC;MACJ,KAAK,QAAQ;QACT3B,SAAS,GAAG3B,YAAY,CAAC4B,MAAM;QAC/B;MACJ,KAAK,cAAc;QACf,MAAM,IAAIsB,KAAK,CAAC,2BAA2B,CAAC;IACpD;IACA,MAAMuD,SAAS,GAAGX,YAAY,CAAC,YAAY,CAAC,KAAKP,SAAS,GACpDO,YAAY,CAAC,YAAY,CAAC,GAC1B,IAAI;IACV,IAAI,CAACA,YAAY,CAAC,aAAa,CAAC,EAAE;MAC9BA,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE;IACpC;IACA,MAAMY,SAAS,GAAG,IAAI,CAACnG,YAAY,CAAC2E,SAAS,CAACvD,SAAS,EAAEmE,YAAY,CAAC,UAAU,CAAC,EAAEW,SAAS,EAAEX,YAAY,CAAC,OAAO,CAAC,EAAEA,YAAY,CAAC,aAAa,CAAC,EAAEA,YAAY,CAAC,QAAQ,CAAC,EAAEA,YAAY,CAAC,YAAY,CAAC,CAAC;IACrM,IAAInE,SAAS,KAAK3B,YAAY,CAAC4B,MAAM,EAAE;MACnC,MAAM+E,WAAW,GAAG,IAAI,CAACpG,YAAY,CAAC2E,SAAS,CAACvD,SAAS,EAAEmE,YAAY,CAAC,UAAU,CAAC,EAAEW,SAAS,EAAEX,YAAY,CAAC,OAAO,CAAC,EAAEA,YAAY,CAAC,aAAa,CAAC,EAAEA,YAAY,CAAC,QAAQ,CAAC,EAAEA,YAAY,CAAC,cAAc,CAAC,CAAC;MACzM,IAAI,CAACY,SAAS,CAAC5C,MAAM,CAAC6C,WAAW,CAAC,EAAE;QAChC,MAAM,IAAIzD,KAAK,CAAC,cAAc4C,YAAY,CAAC,YAAY,CAAC,gCAAgCA,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;MAC3H;IACJ;IACA,OAAOY,SAAS;EACpB;EACAX,YAAYA,CAAA,EAAG;IACX,MAAMa,KAAK,GAAG,IAAI,CAACvG,MAAM,CAACuE,WAAW,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC;IACrD,QAAQD,KAAK;MACT,KAAK,MAAM;QAAE;UACT,OAAO/G,QAAQ,CAAC8C,IAAI;QACxB;MACA,KAAK,MAAM;QAAE;UACT,OAAO9C,QAAQ,CAACoC,IAAI;QACxB;MACA,KAAK,KAAK;QAAE;UACR,OAAOpC,QAAQ,CAACwC,GAAG;QACvB;MACA,KAAK,OAAO;QAAE;UACV,OAAOxC,QAAQ,CAAC4C,KAAK;QACzB;MACA,KAAK,SAAS;QAAE;UACZ,OAAO5C,QAAQ,CAAC0C,OAAO;QAC3B;MACA,KAAK,WAAW;QAAE;UACd,OAAO1C,QAAQ,CAAC+C,SAAS;QAC7B;MACA,KAAK,QAAQ;QAAE;UACX,OAAO/C,QAAQ,CAACuC,MAAM;QAC1B;MACA,KAAK,QAAQ;QAAE;UACX,OAAOvC,QAAQ,CAACsC,MAAM;QAC1B;MACA,KAAK,MAAM;QAAE;UACT,OAAOtC,QAAQ,CAACiD,IAAI;QACxB;MACA,KAAK,MAAM;QAAE;UACT,OAAOjD,QAAQ,CAACmD,IAAI;QACxB;MACA,KAAK,MAAM;QAAE;UACT,OAAOnD,QAAQ,CAAC2B,IAAI;QACxB;MACA,KAAK,MAAM;QAAE;UACT,OAAO3B,QAAQ,CAAC0B,IAAI;QACxB;MACA,KAAK,QAAQ;QAAE;UACX,OAAO1B,QAAQ,CAACwF,MAAM;QAC1B;MACA;QAAS;UACL,MAAM,IAAInC,KAAK,CAAC,KAAK,GAAG0D,KAAK,CAAC;QAClC;IACJ;EACJ;EACAX,gBAAgBA,CAAA,EAAG;IACf,MAAMvE,WAAW,GAAG,EAAE;IACtB,IAAI,IAAI,CAACrB,MAAM,CAAC2E,MAAM,CAAC,CAAC,EAAE;MACtB,OAAOtD,WAAW;IACtB,CAAC,MACI;MACD,IAAI,CAACrB,MAAM,CAACgD,MAAM,CAAC,CAAC;MACpB,KAAK,IAAIqB,GAAG,EAAGA,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC,GAAI;QACvC,IAAID,GAAG,IAAI7E,QAAQ,CAACwF,MAAM,EAAE;UACxB,IAAI,CAAChF,MAAM,CAACgD,MAAM,CAAC,CAAC;UACpB,MAAMyD,IAAI,GAAG,IAAI,CAACzG,MAAM,CAACsE,IAAI,CAAC,CAAC;UAC/B,IAAI,IAAI,CAACtE,MAAM,CAACe,SAAS,CAAC,CAAC,IAAI,MAAM,IAAI0F,IAAI,IAAIjH,QAAQ,CAACsC,MAAM,EAAE;YAC9D,MAAM4E,IAAI,GAAG,IAAI,CAAC1G,MAAM,CAACuE,WAAW,CAAC,CAAC;YACtC,IAAImC,IAAI,KAAK,IAAI,EAAE;cACfrF,WAAW,CAACuD,IAAI,CAAC8B,IAAI,CAAC;YAC1B;UACJ,CAAC,MACI,IAAI,IAAI,CAAC1G,MAAM,CAACe,SAAS,CAAC,CAAC,IAAI,gBAAgB,IAChD0F,IAAI,IAAIjH,QAAQ,CAACwC,GAAG,EAAE;YACtB,MAAM2E,MAAM,GAAGvH,uBAAuB,CAAC,CAAC;YACxC,MAAMwH,MAAM,GAAGD,MAAM,CAACE,aAAa,CAAC,IAAI,CAAC7G,MAAM,CAACmG,WAAW,CAAC,CAAC,CAAC;YAC9D,IAAIS,MAAM,KAAK1B,SAAS,IAAI0B,MAAM,KAAK,IAAI,EAAE;cACzC,MAAM,IAAI/D,KAAK,CAAC,mDAAmD,CAAC;YACxE;YACAxB,WAAW,CAACuD,IAAI,CAACgC,MAAM,CAAC;UAC5B;UACA,IAAI,CAAC5G,MAAM,CAACkD,OAAO,CAAC,CAAC;QACzB;MACJ;MACA,IAAI,CAAClD,MAAM,CAACkD,OAAO,CAAC,CAAC;MACrB,OAAO7B,WAAW;IACtB;EACJ;EACA4E,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACjG,MAAM,CAAC2E,MAAM,CAAC,CAAC,EAAE;MACtB,OAAO,IAAI;IACf;IACA,MAAMmC,SAAS,GAAG,EAAE;IACpB,IAAI,CAAC9G,MAAM,CAACgD,MAAM,CAAC,CAAC;IACpB,IAAIqB,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC;IAC5B,OAAOD,GAAG,EAAE;MACRyC,SAAS,CAAClC,IAAI,CAAC,IAAI,CAAC5E,MAAM,CAACmG,WAAW,CAAC,CAAC,CAAC;MACzC9B,GAAG,GAAG,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAACtE,MAAM,CAACkD,OAAO,CAAC,CAAC;IACrB,MAAM6D,QAAQ,GAAG,IAAIC,UAAU,CAACF,SAAS,CAAC;IAC1C,MAAMf,UAAU,GAAG,IAAIzG,YAAY,CAAC,IAAIC,UAAU,CAACwH,QAAQ,CAAC,CAAC;IAC7DhB,UAAU,CAACzB,IAAI,CAAC,CAAC;IACjB,OAAOyB,UAAU,CAAChB,KAAK,CAAC,CAAC;EAC7B;EACAmB,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClG,MAAM,CAAC+E,KAAK,CAAC,CAAC;EAC9B;EACAY,yCAAyCA,CAAA,EAAG;IACxC,IAAI,IAAI,CAAC3F,MAAM,CAAC2E,MAAM,CAAC,CAAC,EAAE;MACtB,OAAO,IAAI;IACf;IACA,IAAI,CAAC3E,MAAM,CAACgD,MAAM,CAAC,CAAC;IACpB,MAAMyD,IAAI,GAAG,IAAI,CAACzG,MAAM,CAACsE,IAAI,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACtE,MAAM,CAACe,SAAS,CAAC,CAAC,IAAI,MAAM,IAAI0F,IAAI,IAAIjH,QAAQ,CAACsC,MAAM,EAAE;MAC9D,MAAM4E,IAAI,GAAG,IAAI,CAAC1G,MAAM,CAACuE,WAAW,CAAC,CAAC;MACtC,IAAImC,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,CAAC1G,MAAM,CAACkD,OAAO,CAAC,CAAC;QACrB,OAAOwD,IAAI;MACf;IACJ,CAAC,MACI,IAAI,IAAI,CAAC1G,MAAM,CAACe,SAAS,CAAC,CAAC,IAAI,gBAAgB,IAChD0F,IAAI,IAAIjH,QAAQ,CAACwC,GAAG,EAAE;MACtB,MAAM2E,MAAM,GAAGvH,uBAAuB,CAAC,CAAC;MACxC,MAAMwH,MAAM,GAAGD,MAAM,CAACE,aAAa,CAAC,IAAI,CAAC7G,MAAM,CAACmG,WAAW,CAAC,CAAC,CAAC;MAC9D,IAAIS,MAAM,KAAK1B,SAAS,IAAI0B,MAAM,KAAK,IAAI,EAAE;QACzC,MAAM,IAAI/D,KAAK,CAAC,mDAAmD,CAAC;MACxE;MACA,IAAI,CAAC7C,MAAM,CAACkD,OAAO,CAAC,CAAC;MACrB,OAAO0D,MAAM;IACjB;IACA,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}