{"ast": null, "code": "import { Box3 } from 'three';\nimport { CONTAINED } from '../Constants.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nlet _box1, _box2;\nconst boxStack = [];\nconst boxPool = /* @__PURE__ */new PrimitivePool(() => new Box3());\nexport function shapecast(bvh, root, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset) {\n  // setup\n  _box1 = boxPool.getPrimitive();\n  _box2 = boxPool.getPrimitive();\n  boxStack.push(_box1, _box2);\n  BufferStack.setBuffer(bvh._roots[root]);\n  const result = shapecastTraverse(0, bvh.geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset);\n\n  // cleanup\n  BufferStack.clearBuffer();\n  boxPool.releasePrimitive(_box1);\n  boxPool.releasePrimitive(_box2);\n  boxStack.pop();\n  boxStack.pop();\n  const length = boxStack.length;\n  if (length > 0) {\n    _box2 = boxStack[length - 1];\n    _box1 = boxStack[length - 2];\n  }\n  return result;\n}\nfunction shapecastTraverse(nodeIndex32, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc = null, nodeIndexByteOffset = 0,\n// offset for unique node identifier\ndepth = 0) {\n  const {\n    float32Array,\n    uint16Array,\n    uint32Array\n  } = BufferStack;\n  let nodeIndex16 = nodeIndex32 * 2;\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n    arrayToBox(BOUNDING_DATA_INDEX(nodeIndex32), float32Array, _box1);\n    return intersectsRangeFunc(offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1);\n  } else {\n    const left = LEFT_NODE(nodeIndex32);\n    const right = RIGHT_NODE(nodeIndex32, uint32Array);\n    let c1 = left;\n    let c2 = right;\n    let score1, score2;\n    let box1, box2;\n    if (nodeScoreFunc) {\n      box1 = _box1;\n      box2 = _box2;\n\n      // bounding data is not offset\n      arrayToBox(BOUNDING_DATA_INDEX(c1), float32Array, box1);\n      arrayToBox(BOUNDING_DATA_INDEX(c2), float32Array, box2);\n      score1 = nodeScoreFunc(box1);\n      score2 = nodeScoreFunc(box2);\n      if (score2 < score1) {\n        c1 = right;\n        c2 = left;\n        const temp = score1;\n        score1 = score2;\n        score2 = temp;\n        box1 = box2;\n        // box2 is always set before use below\n      }\n    }\n\n    // Check box 1 intersection\n    if (!box1) {\n      box1 = _box1;\n      arrayToBox(BOUNDING_DATA_INDEX(c1), float32Array, box1);\n    }\n    const isC1Leaf = IS_LEAF(c1 * 2, uint16Array);\n    const c1Intersection = intersectsBoundsFunc(box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1);\n    let c1StopTraversal;\n    if (c1Intersection === CONTAINED) {\n      const offset = getLeftOffset(c1);\n      const end = getRightEndOffset(c1);\n      const count = end - offset;\n      c1StopTraversal = intersectsRangeFunc(offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1);\n    } else {\n      c1StopTraversal = c1Intersection && shapecastTraverse(c1, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc, nodeIndexByteOffset, depth + 1);\n    }\n    if (c1StopTraversal) return true;\n\n    // Check box 2 intersection\n    // cached box2 will have been overwritten by previous traversal\n    box2 = _box2;\n    arrayToBox(BOUNDING_DATA_INDEX(c2), float32Array, box2);\n    const isC2Leaf = IS_LEAF(c2 * 2, uint16Array);\n    const c2Intersection = intersectsBoundsFunc(box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2);\n    let c2StopTraversal;\n    if (c2Intersection === CONTAINED) {\n      const offset = getLeftOffset(c2);\n      const end = getRightEndOffset(c2);\n      const count = end - offset;\n      c2StopTraversal = intersectsRangeFunc(offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2);\n    } else {\n      c2StopTraversal = c2Intersection && shapecastTraverse(c2, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc, nodeIndexByteOffset, depth + 1);\n    }\n    if (c2StopTraversal) return true;\n    return false;\n\n    // Define these inside the function so it has access to the local variables needed\n    // when converting to the buffer equivalents\n    function getLeftOffset(nodeIndex32) {\n      const {\n        uint16Array,\n        uint32Array\n      } = BufferStack;\n      let nodeIndex16 = nodeIndex32 * 2;\n\n      // traverse until we find a leaf\n      while (!IS_LEAF(nodeIndex16, uint16Array)) {\n        nodeIndex32 = LEFT_NODE(nodeIndex32);\n        nodeIndex16 = nodeIndex32 * 2;\n      }\n      return OFFSET(nodeIndex32, uint32Array);\n    }\n    function getRightEndOffset(nodeIndex32) {\n      const {\n        uint16Array,\n        uint32Array\n      } = BufferStack;\n      let nodeIndex16 = nodeIndex32 * 2;\n\n      // traverse until we find a leaf\n      while (!IS_LEAF(nodeIndex16, uint16Array)) {\n        // adjust offset to point to the right node\n        nodeIndex32 = RIGHT_NODE(nodeIndex32, uint32Array);\n        nodeIndex16 = nodeIndex32 * 2;\n      }\n\n      // return the end offset of the triangle range\n      return OFFSET(nodeIndex32, uint32Array) + COUNT(nodeIndex16, uint16Array);\n    }\n  }\n}", "map": {"version": 3, "names": ["Box3", "CONTAINED", "arrayToBox", "PrimitivePool", "COUNT", "OFFSET", "LEFT_NODE", "RIGHT_NODE", "IS_LEAF", "BOUNDING_DATA_INDEX", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>", "_box1", "_box2", "boxStack", "boxPool", "shapecast", "bvh", "root", "intersectsBounds", "intersectsRange", "boundsTraverseOrder", "byteOffset", "getPrimitive", "push", "<PERSON><PERSON><PERSON><PERSON>", "_roots", "result", "shapecastTraverse", "geometry", "<PERSON><PERSON><PERSON><PERSON>", "releasePrimitive", "pop", "length", "nodeIndex32", "intersectsBoundsFunc", "intersectsRangeFunc", "nodeScoreFunc", "nodeIndexByteOffset", "depth", "float32Array", "uint16Array", "uint32Array", "nodeIndex16", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "left", "right", "c1", "c2", "score1", "score2", "box1", "box2", "temp", "isC1Leaf", "c1Intersection", "c1StopTraversal", "getLeftOffset", "end", "getRightEndOffset", "isC2Leaf", "c2Intersection", "c2StopTraversal"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/core/cast/shapecast.js"], "sourcesContent": ["import { Box3 } from 'three';\nimport { CONTAINED } from '../Constants.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\nlet _box1, _box2;\nconst boxStack = [];\nconst boxPool = /* @__PURE__ */ new PrimitivePool( () => new Box3() );\n\nexport function shapecast( bvh, root, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset ) {\n\n\t// setup\n\t_box1 = boxPool.getPrimitive();\n\t_box2 = boxPool.getPrimitive();\n\tboxStack.push( _box1, _box2 );\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\n\tconst result = shapecastTraverse( 0, bvh.geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t// cleanup\n\tBufferStack.clearBuffer();\n\tboxPool.releasePrimitive( _box1 );\n\tboxPool.releasePrimitive( _box2 );\n\tboxStack.pop();\n\tboxStack.pop();\n\n\tconst length = boxStack.length;\n\tif ( length > 0 ) {\n\n\t\t_box2 = boxStack[ length - 1 ];\n\t\t_box1 = boxStack[ length - 2 ];\n\n\t}\n\n\treturn result;\n\n}\n\nfunction shapecastTraverse(\n\tnodeIndex32,\n\tgeometry,\n\tintersectsBoundsFunc,\n\tintersectsRangeFunc,\n\tnodeScoreFunc = null,\n\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\tdepth = 0\n) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t} else {\n\n\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tlet c1 = left;\n\t\tlet c2 = right;\n\n\t\tlet score1, score2;\n\t\tlet box1, box2;\n\t\tif ( nodeScoreFunc ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tbox2 = _box2;\n\n\t\t\t// bounding data is not offset\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\tc1 = right;\n\t\t\t\tc2 = left;\n\n\t\t\t\tconst temp = score1;\n\t\t\t\tscore1 = score2;\n\t\t\t\tscore2 = temp;\n\n\t\t\t\tbox1 = box2;\n\t\t\t\t// box2 is always set before use below\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Check box 1 intersection\n\t\tif ( ! box1 ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t}\n\n\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\tlet c1StopTraversal;\n\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t} else {\n\n\t\t\tc1StopTraversal =\n\t\t\t\tc1Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc1,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c1StopTraversal ) return true;\n\n\t\t// Check box 2 intersection\n\t\t// cached box2 will have been overwritten by previous traversal\n\t\tbox2 = _box2;\n\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\tlet c2StopTraversal;\n\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t} else {\n\n\t\t\tc2StopTraversal =\n\t\t\t\tc2Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc2,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c2StopTraversal ) return true;\n\n\t\treturn false;\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAO;AAC5B,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,mBAAmB,QAAQ,6BAA6B;AAChH,SAASC,WAAW,QAAQ,yBAAyB;AAErD,IAAIC,KAAK,EAAEC,KAAK;AAChB,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,OAAO,GAAG,eAAgB,IAAIX,aAAa,CAAE,MAAM,IAAIH,IAAI,CAAC,CAAE,CAAC;AAErE,OAAO,SAASe,SAASA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,UAAU,EAAG;EAE1G;EACAV,KAAK,GAAGG,OAAO,CAACQ,YAAY,CAAC,CAAC;EAC9BV,KAAK,GAAGE,OAAO,CAACQ,YAAY,CAAC,CAAC;EAC9BT,QAAQ,CAACU,IAAI,CAAEZ,KAAK,EAAEC,KAAM,CAAC;EAC7BF,WAAW,CAACc,SAAS,CAAER,GAAG,CAACS,MAAM,CAAER,IAAI,CAAG,CAAC;EAE3C,MAAMS,MAAM,GAAGC,iBAAiB,CAAE,CAAC,EAAEX,GAAG,CAACY,QAAQ,EAAEV,gBAAgB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,UAAW,CAAC;;EAEvH;EACAX,WAAW,CAACmB,WAAW,CAAC,CAAC;EACzBf,OAAO,CAACgB,gBAAgB,CAAEnB,KAAM,CAAC;EACjCG,OAAO,CAACgB,gBAAgB,CAAElB,KAAM,CAAC;EACjCC,QAAQ,CAACkB,GAAG,CAAC,CAAC;EACdlB,QAAQ,CAACkB,GAAG,CAAC,CAAC;EAEd,MAAMC,MAAM,GAAGnB,QAAQ,CAACmB,MAAM;EAC9B,IAAKA,MAAM,GAAG,CAAC,EAAG;IAEjBpB,KAAK,GAAGC,QAAQ,CAAEmB,MAAM,GAAG,CAAC,CAAE;IAC9BrB,KAAK,GAAGE,QAAQ,CAAEmB,MAAM,GAAG,CAAC,CAAE;EAE/B;EAEA,OAAON,MAAM;AAEd;AAEA,SAASC,iBAAiBA,CACzBM,WAAW,EACXL,QAAQ,EACRM,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,GAAG,IAAI,EACpBC,mBAAmB,GAAG,CAAC;AAAE;AACzBC,KAAK,GAAG,CAAC,EACR;EAED,MAAM;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAG/B,WAAW;EAC9D,IAAIgC,WAAW,GAAGT,WAAW,GAAG,CAAC;EAEjC,MAAMU,MAAM,GAAGnC,OAAO,CAAEkC,WAAW,EAAEF,WAAY,CAAC;EAClD,IAAKG,MAAM,EAAG;IAEb,MAAMC,MAAM,GAAGvC,MAAM,CAAE4B,WAAW,EAAEQ,WAAY,CAAC;IACjD,MAAMI,KAAK,GAAGzC,KAAK,CAAEsC,WAAW,EAAEF,WAAY,CAAC;IAC/CtC,UAAU,CAAEO,mBAAmB,CAAEwB,WAAY,CAAC,EAAEM,YAAY,EAAE5B,KAAM,CAAC;IACrE,OAAOwB,mBAAmB,CAAES,MAAM,EAAEC,KAAK,EAAE,KAAK,EAAEP,KAAK,EAAED,mBAAmB,GAAGJ,WAAW,EAAEtB,KAAM,CAAC;EAEpG,CAAC,MAAM;IAEN,MAAMmC,IAAI,GAAGxC,SAAS,CAAE2B,WAAY,CAAC;IACrC,MAAMc,KAAK,GAAGxC,UAAU,CAAE0B,WAAW,EAAEQ,WAAY,CAAC;IACpD,IAAIO,EAAE,GAAGF,IAAI;IACb,IAAIG,EAAE,GAAGF,KAAK;IAEd,IAAIG,MAAM,EAAEC,MAAM;IAClB,IAAIC,IAAI,EAAEC,IAAI;IACd,IAAKjB,aAAa,EAAG;MAEpBgB,IAAI,GAAGzC,KAAK;MACZ0C,IAAI,GAAGzC,KAAK;;MAEZ;MACAV,UAAU,CAAEO,mBAAmB,CAAEuC,EAAG,CAAC,EAAET,YAAY,EAAEa,IAAK,CAAC;MAC3DlD,UAAU,CAAEO,mBAAmB,CAAEwC,EAAG,CAAC,EAAEV,YAAY,EAAEc,IAAK,CAAC;MAE3DH,MAAM,GAAGd,aAAa,CAAEgB,IAAK,CAAC;MAC9BD,MAAM,GAAGf,aAAa,CAAEiB,IAAK,CAAC;MAE9B,IAAKF,MAAM,GAAGD,MAAM,EAAG;QAEtBF,EAAE,GAAGD,KAAK;QACVE,EAAE,GAAGH,IAAI;QAET,MAAMQ,IAAI,GAAGJ,MAAM;QACnBA,MAAM,GAAGC,MAAM;QACfA,MAAM,GAAGG,IAAI;QAEbF,IAAI,GAAGC,IAAI;QACX;MAED;IAED;;IAEA;IACA,IAAK,CAAED,IAAI,EAAG;MAEbA,IAAI,GAAGzC,KAAK;MACZT,UAAU,CAAEO,mBAAmB,CAAEuC,EAAG,CAAC,EAAET,YAAY,EAAEa,IAAK,CAAC;IAE5D;IAEA,MAAMG,QAAQ,GAAG/C,OAAO,CAAEwC,EAAE,GAAG,CAAC,EAAER,WAAY,CAAC;IAC/C,MAAMgB,cAAc,GAAGtB,oBAAoB,CAAEkB,IAAI,EAAEG,QAAQ,EAAEL,MAAM,EAAEZ,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGW,EAAG,CAAC;IAE1G,IAAIS,eAAe;IACnB,IAAKD,cAAc,KAAKvD,SAAS,EAAG;MAEnC,MAAM2C,MAAM,GAAGc,aAAa,CAAEV,EAAG,CAAC;MAClC,MAAMW,GAAG,GAAGC,iBAAiB,CAAEZ,EAAG,CAAC;MACnC,MAAMH,KAAK,GAAGc,GAAG,GAAGf,MAAM;MAE1Ba,eAAe,GAAGtB,mBAAmB,CAAES,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEP,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGW,EAAE,EAAEI,IAAK,CAAC;IAExG,CAAC,MAAM;MAENK,eAAe,GACdD,cAAc,IACd7B,iBAAiB,CAChBqB,EAAE,EACFpB,QAAQ,EACRM,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,mBAAmB,EACnBC,KAAK,GAAG,CACT,CAAC;IAEH;IAEA,IAAKmB,eAAe,EAAG,OAAO,IAAI;;IAElC;IACA;IACAJ,IAAI,GAAGzC,KAAK;IACZV,UAAU,CAAEO,mBAAmB,CAAEwC,EAAG,CAAC,EAAEV,YAAY,EAAEc,IAAK,CAAC;IAE3D,MAAMQ,QAAQ,GAAGrD,OAAO,CAAEyC,EAAE,GAAG,CAAC,EAAET,WAAY,CAAC;IAC/C,MAAMsB,cAAc,GAAG5B,oBAAoB,CAAEmB,IAAI,EAAEQ,QAAQ,EAAEV,MAAM,EAAEb,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGY,EAAG,CAAC;IAE1G,IAAIc,eAAe;IACnB,IAAKD,cAAc,KAAK7D,SAAS,EAAG;MAEnC,MAAM2C,MAAM,GAAGc,aAAa,CAAET,EAAG,CAAC;MAClC,MAAMU,GAAG,GAAGC,iBAAiB,CAAEX,EAAG,CAAC;MACnC,MAAMJ,KAAK,GAAGc,GAAG,GAAGf,MAAM;MAE1BmB,eAAe,GAAG5B,mBAAmB,CAAES,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEP,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGY,EAAE,EAAEI,IAAK,CAAC;IAExG,CAAC,MAAM;MAENU,eAAe,GACdD,cAAc,IACdnC,iBAAiB,CAChBsB,EAAE,EACFrB,QAAQ,EACRM,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,mBAAmB,EACnBC,KAAK,GAAG,CACT,CAAC;IAEH;IAEA,IAAKyB,eAAe,EAAG,OAAO,IAAI;IAElC,OAAO,KAAK;;IAEZ;IACA;IACA,SAASL,aAAaA,CAAEzB,WAAW,EAAG;MAErC,MAAM;QAAEO,WAAW;QAAEC;MAAY,CAAC,GAAG/B,WAAW;MAChD,IAAIgC,WAAW,GAAGT,WAAW,GAAG,CAAC;;MAEjC;MACA,OAAQ,CAAEzB,OAAO,CAAEkC,WAAW,EAAEF,WAAY,CAAC,EAAG;QAE/CP,WAAW,GAAG3B,SAAS,CAAE2B,WAAY,CAAC;QACtCS,WAAW,GAAGT,WAAW,GAAG,CAAC;MAE9B;MAEA,OAAO5B,MAAM,CAAE4B,WAAW,EAAEQ,WAAY,CAAC;IAE1C;IAEA,SAASmB,iBAAiBA,CAAE3B,WAAW,EAAG;MAEzC,MAAM;QAAEO,WAAW;QAAEC;MAAY,CAAC,GAAG/B,WAAW;MAChD,IAAIgC,WAAW,GAAGT,WAAW,GAAG,CAAC;;MAEjC;MACA,OAAQ,CAAEzB,OAAO,CAAEkC,WAAW,EAAEF,WAAY,CAAC,EAAG;QAE/C;QACAP,WAAW,GAAG1B,UAAU,CAAE0B,WAAW,EAAEQ,WAAY,CAAC;QACpDC,WAAW,GAAGT,WAAW,GAAG,CAAC;MAE9B;;MAEA;MACA,OAAO5B,MAAM,CAAE4B,WAAW,EAAEQ,WAAY,CAAC,GAAGrC,KAAK,CAAEsC,WAAW,EAAEF,WAAY,CAAC;IAE9E;EAED;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}