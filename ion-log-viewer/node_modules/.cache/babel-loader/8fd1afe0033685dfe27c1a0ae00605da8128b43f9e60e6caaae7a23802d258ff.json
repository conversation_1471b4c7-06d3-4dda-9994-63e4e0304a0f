{"ast": null, "code": "import { Decimal, dom, Timestamp } from \"../Ion\";\nimport { IonTypes } from \"../IonTypes\";\nimport { _hasValue } from \"../util\";\nexport const _NativeJsBoolean = Boolean;\nexport const _NativeJsString = String;\nlet _domTypesByIonType = null;\nfunction _getDomTypesByIonTypeMap() {\n  if (_domTypesByIonType === null) {\n    _domTypesByIonType = new Map([[IonTypes.NULL, dom.Null], [IonTypes.BOOL, dom.Boolean], [IonTypes.INT, dom.Integer], [IonTypes.FLOAT, dom.Float], [IonTypes.DECIMAL, dom.Decimal], [IonTypes.TIMESTAMP, dom.Timestamp], [IonTypes.STRING, dom.String], [IonTypes.BLOB, dom.Blob], [IonTypes.LIST, dom.List], [IonTypes.STRUCT, dom.Struct]]);\n  }\n  return _domTypesByIonType;\n}\nexport function _domConstructorFor(ionType) {\n  const domConstructor = _getDomTypesByIonTypeMap().get(ionType);\n  if (!_hasValue(domConstructor)) {\n    throw new Error(`No dom type constructor was found for Ion type ${ionType.name}`);\n  }\n  return domConstructor;\n}\nfunction _inferType(value) {\n  if (value === undefined) {\n    throw new Error(\"Cannot create an Ion value from `undefined`.\");\n  }\n  if (value === null) {\n    return IonTypes.NULL;\n  }\n  const valueType = typeof value;\n  switch (valueType) {\n    case \"string\":\n      return IonTypes.STRING;\n    case \"number\":\n      return Number.isInteger(value) ? IonTypes.INT : IonTypes.FLOAT;\n    case \"boolean\":\n      return IonTypes.BOOL;\n    case \"object\":\n      break;\n    case \"bigint\":\n      return IonTypes.INT;\n    default:\n      throw new Error(`Value.from() does not support the JS primitive type ${valueType}.`);\n  }\n  if (value instanceof BigInt) {\n    return IonTypes.INT;\n  }\n  if (value instanceof Number) {\n    return Number.isInteger(value.valueOf()) ? IonTypes.INT : IonTypes.FLOAT;\n  }\n  if (value instanceof Boolean) {\n    return IonTypes.BOOL;\n  }\n  if (value instanceof String) {\n    return IonTypes.STRING;\n  }\n  if (value instanceof Decimal) {\n    return IonTypes.DECIMAL;\n  }\n  if (value instanceof Date || value instanceof Timestamp) {\n    return IonTypes.TIMESTAMP;\n  }\n  if (value instanceof Uint8Array) {\n    return IonTypes.BLOB;\n  }\n  if (value instanceof Array) {\n    return IonTypes.LIST;\n  }\n  return IonTypes.STRUCT;\n}\nexport function _ionValueFromJsValue(value, annotations = []) {\n  const ionType = _inferType(value);\n  const ionTypeConstructor = _domConstructorFor(ionType);\n  return ionTypeConstructor._fromJsValue(value, annotations);\n}", "map": {"version": 3, "names": ["Decimal", "dom", "Timestamp", "IonTypes", "_hasValue", "_NativeJsBoolean", "Boolean", "_NativeJsString", "String", "_domTypesByIonType", "_getDomTypesByIonTypeMap", "Map", "NULL", "<PERSON><PERSON>", "BOOL", "INT", "Integer", "FLOAT", "Float", "DECIMAL", "TIMESTAMP", "STRING", "BLOB", "Blob", "LIST", "List", "STRUCT", "Struct", "_domConstructorFor", "ionType", "domConstructor", "get", "Error", "name", "_inferType", "value", "undefined", "valueType", "Number", "isInteger", "BigInt", "valueOf", "Date", "Uint8Array", "Array", "_ionValueFromJsValue", "annotations", "ionTypeConstructor", "_fromJsValue"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/JsValueConversion.js"], "sourcesContent": ["import { Decimal, dom, Timestamp } from \"../Ion\";\nimport { IonTypes } from \"../IonTypes\";\nimport { _hasValue } from \"../util\";\nexport const _NativeJsBoolean = Boolean;\nexport const _NativeJsString = String;\nlet _domTypesByIonType = null;\nfunction _getDomTypesByIonTypeMap() {\n    if (_domTypesByIonType === null) {\n        _domTypesByIonType = new Map([\n            [IonTypes.NULL, dom.Null],\n            [IonTypes.BOOL, dom.Boolean],\n            [IonTypes.INT, dom.Integer],\n            [IonTypes.FLOAT, dom.Float],\n            [IonTypes.DECIMAL, dom.Decimal],\n            [IonTypes.TIMESTAMP, dom.Timestamp],\n            [IonTypes.STRING, dom.String],\n            [IonTypes.BLOB, dom.Blob],\n            [IonTypes.LIST, dom.List],\n            [IonTypes.STRUCT, dom.Struct],\n        ]);\n    }\n    return _domTypesByIonType;\n}\nexport function _domConstructorFor(ionType) {\n    const domConstructor = _getDomTypesByIonTypeMap().get(ionType);\n    if (!_hasValue(domConstructor)) {\n        throw new Error(`No dom type constructor was found for Ion type ${ionType.name}`);\n    }\n    return domConstructor;\n}\nfunction _inferType(value) {\n    if (value === undefined) {\n        throw new Error(\"Cannot create an Ion value from `undefined`.\");\n    }\n    if (value === null) {\n        return IonTypes.NULL;\n    }\n    const valueType = typeof value;\n    switch (valueType) {\n        case \"string\":\n            return IonTypes.STRING;\n        case \"number\":\n            return Number.isInteger(value) ? IonTypes.INT : IonTypes.FLOAT;\n        case \"boolean\":\n            return IonTypes.BOOL;\n        case \"object\":\n            break;\n        case \"bigint\":\n            return IonTypes.INT;\n        default:\n            throw new Error(`Value.from() does not support the JS primitive type ${valueType}.`);\n    }\n    if (value instanceof BigInt) {\n        return IonTypes.INT;\n    }\n    if (value instanceof Number) {\n        return Number.isInteger(value.valueOf()) ? IonTypes.INT : IonTypes.FLOAT;\n    }\n    if (value instanceof Boolean) {\n        return IonTypes.BOOL;\n    }\n    if (value instanceof String) {\n        return IonTypes.STRING;\n    }\n    if (value instanceof Decimal) {\n        return IonTypes.DECIMAL;\n    }\n    if (value instanceof Date || value instanceof Timestamp) {\n        return IonTypes.TIMESTAMP;\n    }\n    if (value instanceof Uint8Array) {\n        return IonTypes.BLOB;\n    }\n    if (value instanceof Array) {\n        return IonTypes.LIST;\n    }\n    return IonTypes.STRUCT;\n}\nexport function _ionValueFromJsValue(value, annotations = []) {\n    const ionType = _inferType(value);\n    const ionTypeConstructor = _domConstructorFor(ionType);\n    return ionTypeConstructor._fromJsValue(value, annotations);\n}\n//# sourceMappingURL=JsValueConversion.js.map"], "mappings": "AAAA,SAASA,OAAO,EAAEC,GAAG,EAAEC,SAAS,QAAQ,QAAQ;AAChD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAO,MAAMC,gBAAgB,GAAGC,OAAO;AACvC,OAAO,MAAMC,eAAe,GAAGC,MAAM;AACrC,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,SAASC,wBAAwBA,CAAA,EAAG;EAChC,IAAID,kBAAkB,KAAK,IAAI,EAAE;IAC7BA,kBAAkB,GAAG,IAAIE,GAAG,CAAC,CACzB,CAACR,QAAQ,CAACS,IAAI,EAAEX,GAAG,CAACY,IAAI,CAAC,EACzB,CAACV,QAAQ,CAACW,IAAI,EAAEb,GAAG,CAACK,OAAO,CAAC,EAC5B,CAACH,QAAQ,CAACY,GAAG,EAAEd,GAAG,CAACe,OAAO,CAAC,EAC3B,CAACb,QAAQ,CAACc,KAAK,EAAEhB,GAAG,CAACiB,KAAK,CAAC,EAC3B,CAACf,QAAQ,CAACgB,OAAO,EAAElB,GAAG,CAACD,OAAO,CAAC,EAC/B,CAACG,QAAQ,CAACiB,SAAS,EAAEnB,GAAG,CAACC,SAAS,CAAC,EACnC,CAACC,QAAQ,CAACkB,MAAM,EAAEpB,GAAG,CAACO,MAAM,CAAC,EAC7B,CAACL,QAAQ,CAACmB,IAAI,EAAErB,GAAG,CAACsB,IAAI,CAAC,EACzB,CAACpB,QAAQ,CAACqB,IAAI,EAAEvB,GAAG,CAACwB,IAAI,CAAC,EACzB,CAACtB,QAAQ,CAACuB,MAAM,EAAEzB,GAAG,CAAC0B,MAAM,CAAC,CAChC,CAAC;EACN;EACA,OAAOlB,kBAAkB;AAC7B;AACA,OAAO,SAASmB,kBAAkBA,CAACC,OAAO,EAAE;EACxC,MAAMC,cAAc,GAAGpB,wBAAwB,CAAC,CAAC,CAACqB,GAAG,CAACF,OAAO,CAAC;EAC9D,IAAI,CAACzB,SAAS,CAAC0B,cAAc,CAAC,EAAE;IAC5B,MAAM,IAAIE,KAAK,CAAC,kDAAkDH,OAAO,CAACI,IAAI,EAAE,CAAC;EACrF;EACA,OAAOH,cAAc;AACzB;AACA,SAASI,UAAUA,CAACC,KAAK,EAAE;EACvB,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACrB,MAAM,IAAIJ,KAAK,CAAC,8CAA8C,CAAC;EACnE;EACA,IAAIG,KAAK,KAAK,IAAI,EAAE;IAChB,OAAOhC,QAAQ,CAACS,IAAI;EACxB;EACA,MAAMyB,SAAS,GAAG,OAAOF,KAAK;EAC9B,QAAQE,SAAS;IACb,KAAK,QAAQ;MACT,OAAOlC,QAAQ,CAACkB,MAAM;IAC1B,KAAK,QAAQ;MACT,OAAOiB,MAAM,CAACC,SAAS,CAACJ,KAAK,CAAC,GAAGhC,QAAQ,CAACY,GAAG,GAAGZ,QAAQ,CAACc,KAAK;IAClE,KAAK,SAAS;MACV,OAAOd,QAAQ,CAACW,IAAI;IACxB,KAAK,QAAQ;MACT;IACJ,KAAK,QAAQ;MACT,OAAOX,QAAQ,CAACY,GAAG;IACvB;MACI,MAAM,IAAIiB,KAAK,CAAC,uDAAuDK,SAAS,GAAG,CAAC;EAC5F;EACA,IAAIF,KAAK,YAAYK,MAAM,EAAE;IACzB,OAAOrC,QAAQ,CAACY,GAAG;EACvB;EACA,IAAIoB,KAAK,YAAYG,MAAM,EAAE;IACzB,OAAOA,MAAM,CAACC,SAAS,CAACJ,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAGtC,QAAQ,CAACY,GAAG,GAAGZ,QAAQ,CAACc,KAAK;EAC5E;EACA,IAAIkB,KAAK,YAAY7B,OAAO,EAAE;IAC1B,OAAOH,QAAQ,CAACW,IAAI;EACxB;EACA,IAAIqB,KAAK,YAAY3B,MAAM,EAAE;IACzB,OAAOL,QAAQ,CAACkB,MAAM;EAC1B;EACA,IAAIc,KAAK,YAAYnC,OAAO,EAAE;IAC1B,OAAOG,QAAQ,CAACgB,OAAO;EAC3B;EACA,IAAIgB,KAAK,YAAYO,IAAI,IAAIP,KAAK,YAAYjC,SAAS,EAAE;IACrD,OAAOC,QAAQ,CAACiB,SAAS;EAC7B;EACA,IAAIe,KAAK,YAAYQ,UAAU,EAAE;IAC7B,OAAOxC,QAAQ,CAACmB,IAAI;EACxB;EACA,IAAIa,KAAK,YAAYS,KAAK,EAAE;IACxB,OAAOzC,QAAQ,CAACqB,IAAI;EACxB;EACA,OAAOrB,QAAQ,CAACuB,MAAM;AAC1B;AACA,OAAO,SAASmB,oBAAoBA,CAACV,KAAK,EAAEW,WAAW,GAAG,EAAE,EAAE;EAC1D,MAAMjB,OAAO,GAAGK,UAAU,CAACC,KAAK,CAAC;EACjC,MAAMY,kBAAkB,GAAGnB,kBAAkB,CAACC,OAAO,CAAC;EACtD,OAAOkB,kBAAkB,CAACC,YAAY,CAACb,KAAK,EAAEW,WAAW,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}