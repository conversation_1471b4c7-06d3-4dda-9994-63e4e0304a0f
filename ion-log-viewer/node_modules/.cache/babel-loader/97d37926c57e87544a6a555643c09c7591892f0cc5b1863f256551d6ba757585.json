{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Mesh, PerspectiveCamera, Color, Plane, Matrix4, WebGLRenderTarget, HalfFloatType, ShaderMaterial, UniformsUtils, Vector3, Quaternion, Vector4, NoToneMapping } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nconst Refractor = /* @__PURE__ */(() => {\n  const _Refractor = class extends Mesh {\n    constructor(geometry, options = {}) {\n      super(geometry);\n      this.isRefractor = true;\n      this.type = \"Refractor\";\n      this.camera = new PerspectiveCamera();\n      const scope = this;\n      const color = options.color !== void 0 ? new Color(options.color) : new Color(8355711);\n      const textureWidth = options.textureWidth || 512;\n      const textureHeight = options.textureHeight || 512;\n      const clipBias = options.clipBias || 0;\n      const shader = options.shader || _Refractor.RefractorShader;\n      const multisample = options.multisample !== void 0 ? options.multisample : 4;\n      const virtualCamera = this.camera;\n      virtualCamera.matrixAutoUpdate = false;\n      virtualCamera.userData.refractor = true;\n      const refractorPlane = new Plane();\n      const textureMatrix = new Matrix4();\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, {\n        samples: multisample,\n        type: HalfFloatType\n      });\n      this.material = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        transparent: true\n        // ensures, refractors are drawn from farthest to closest\n      });\n      this.material.uniforms[\"color\"].value = color;\n      this.material.uniforms[\"tDiffuse\"].value = renderTarget.texture;\n      this.material.uniforms[\"textureMatrix\"].value = textureMatrix;\n      const visible = function () {\n        const refractorWorldPosition = new Vector3();\n        const cameraWorldPosition = new Vector3();\n        const rotationMatrix = new Matrix4();\n        const view = new Vector3();\n        const normal = new Vector3();\n        return function visible2(camera) {\n          refractorWorldPosition.setFromMatrixPosition(scope.matrixWorld);\n          cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n          view.subVectors(refractorWorldPosition, cameraWorldPosition);\n          rotationMatrix.extractRotation(scope.matrixWorld);\n          normal.set(0, 0, 1);\n          normal.applyMatrix4(rotationMatrix);\n          return view.dot(normal) < 0;\n        };\n      }();\n      const updateRefractorPlane = function () {\n        const normal = new Vector3();\n        const position = new Vector3();\n        const quaternion = new Quaternion();\n        const scale = new Vector3();\n        return function updateRefractorPlane2() {\n          scope.matrixWorld.decompose(position, quaternion, scale);\n          normal.set(0, 0, 1).applyQuaternion(quaternion).normalize();\n          normal.negate();\n          refractorPlane.setFromNormalAndCoplanarPoint(normal, position);\n        };\n      }();\n      const updateVirtualCamera = function () {\n        const clipPlane = new Plane();\n        const clipVector = new Vector4();\n        const q = new Vector4();\n        return function updateVirtualCamera2(camera) {\n          virtualCamera.matrixWorld.copy(camera.matrixWorld);\n          virtualCamera.matrixWorldInverse.copy(virtualCamera.matrixWorld).invert();\n          virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n          virtualCamera.far = camera.far;\n          clipPlane.copy(refractorPlane);\n          clipPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n          clipVector.set(clipPlane.normal.x, clipPlane.normal.y, clipPlane.normal.z, clipPlane.constant);\n          const projectionMatrix = virtualCamera.projectionMatrix;\n          q.x = (Math.sign(clipVector.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n          q.y = (Math.sign(clipVector.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n          q.z = -1;\n          q.w = (1 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n          clipVector.multiplyScalar(2 / clipVector.dot(q));\n          projectionMatrix.elements[2] = clipVector.x;\n          projectionMatrix.elements[6] = clipVector.y;\n          projectionMatrix.elements[10] = clipVector.z + 1 - clipBias;\n          projectionMatrix.elements[14] = clipVector.w;\n        };\n      }();\n      function updateTextureMatrix(camera) {\n        textureMatrix.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1);\n        textureMatrix.multiply(camera.projectionMatrix);\n        textureMatrix.multiply(camera.matrixWorldInverse);\n        textureMatrix.multiply(scope.matrixWorld);\n      }\n      function render(renderer, scene, camera) {\n        scope.visible = false;\n        const currentRenderTarget = renderer.getRenderTarget();\n        const currentXrEnabled = renderer.xr.enabled;\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate;\n        const currentToneMapping = renderer.toneMapping;\n        let isSRGB = false;\n        if (\"outputColorSpace\" in renderer) isSRGB = renderer.outputColorSpace === \"srgb\";else isSRGB = renderer.outputEncoding === 3001;\n        renderer.xr.enabled = false;\n        renderer.shadowMap.autoUpdate = false;\n        if (\"outputColorSpace\" in renderer) renderer.outputColorSpace = \"srgb-linear\";else renderer.outputEncoding = 3e3;\n        renderer.toneMapping = NoToneMapping;\n        renderer.setRenderTarget(renderTarget);\n        if (renderer.autoClear === false) renderer.clear();\n        renderer.render(scene, virtualCamera);\n        renderer.xr.enabled = currentXrEnabled;\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate;\n        renderer.toneMapping = currentToneMapping;\n        renderer.setRenderTarget(currentRenderTarget);\n        if (\"outputColorSpace\" in renderer) renderer.outputColorSpace = isSRGB ? \"srgb\" : \"srgb-linear\";else renderer.outputEncoding = isSRGB ? 3001 : 3e3;\n        const viewport = camera.viewport;\n        if (viewport !== void 0) {\n          renderer.state.viewport(viewport);\n        }\n        scope.visible = true;\n      }\n      this.onBeforeRender = function (renderer, scene, camera) {\n        if (camera.userData.refractor === true) return;\n        if (!visible(camera) === true) return;\n        updateRefractorPlane();\n        updateTextureMatrix(camera);\n        updateVirtualCamera(camera);\n        render(renderer, scene, camera);\n      };\n      this.getRenderTarget = function () {\n        return renderTarget;\n      };\n      this.dispose = function () {\n        renderTarget.dispose();\n        scope.material.dispose();\n      };\n    }\n  };\n  let Refractor2 = _Refractor;\n  __publicField(Refractor2, \"RefractorShader\", {\n    uniforms: {\n      color: {\n        value: null\n      },\n      tDiffuse: {\n        value: null\n      },\n      textureMatrix: {\n        value: null\n      }\n    },\n    vertexShader: (/* glsl */\n    `\n\n\t\tuniform mat4 textureMatrix;\n\n\t\tvarying vec4 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`),\n    fragmentShader: (/* glsl */\n    `\n\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\n\t\tvarying vec4 vUv;\n\n\t\tfloat blendOverlay( float base, float blend ) {\n\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n\t\t}\n\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n\n\t\t}`)\n  });\n  return Refractor2;\n})();\nexport { Refractor };", "map": {"version": 3, "names": ["Refractor", "_Refractor", "<PERSON><PERSON>", "constructor", "geometry", "options", "isRefractor", "type", "camera", "PerspectiveCamera", "scope", "color", "Color", "textureWidth", "textureHeight", "clipBias", "shader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multisample", "virtualCamera", "matrixAutoUpdate", "userData", "refractor", "refractorPlane", "Plane", "textureMatrix", "Matrix4", "renderTarget", "WebGLRenderTarget", "samples", "HalfFloatType", "material", "ShaderMaterial", "uniforms", "UniformsUtils", "clone", "vertexShader", "fragmentShader", "transparent", "value", "texture", "visible", "refractorWorldPosition", "Vector3", "cameraWorldPosition", "rotationMatrix", "view", "normal", "visible2", "setFromMatrixPosition", "matrixWorld", "subVectors", "extractRotation", "set", "applyMatrix4", "dot", "updateRefractorPlane", "position", "quaternion", "Quaternion", "scale", "updateRefractorPlane2", "decompose", "applyQuaternion", "normalize", "negate", "setFromNormalAndCoplanarPoint", "updateVirtualCamera", "clipPlane", "clipVector", "Vector4", "q", "updateVirtualCamera2", "copy", "matrixWorldInverse", "invert", "projectionMatrix", "far", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "updateTextureMatrix", "multiply", "render", "renderer", "scene", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "currentToneMapping", "toneMapping", "isSRGB", "outputColorSpace", "outputEncoding", "NoToneMapping", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autoClear", "clear", "viewport", "state", "onBeforeRender", "dispose", "Refractor2", "__publicField", "tDiffuse", "version"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/objects/Refractor.js"], "sourcesContent": ["import {\n  Color,\n  Matrix4,\n  Mesh,\n  PerspectiveCamera,\n  Plane,\n  Quaternion,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector3,\n  Vector4,\n  WebGLRenderTarget,\n  NoToneMapping,\n  HalfFloatType,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nconst Refractor = /* @__PURE__ */ (() => {\n  class Refractor extends Mesh {\n    static RefractorShader = {\n      uniforms: {\n        color: {\n          value: null,\n        },\n\n        tDiffuse: {\n          value: null,\n        },\n\n        textureMatrix: {\n          value: null,\n        },\n      },\n\n      vertexShader: /* glsl */ `\n\n\t\tuniform mat4 textureMatrix;\n\n\t\tvarying vec4 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\n\t\tvarying vec4 vUv;\n\n\t\tfloat blendOverlay( float base, float blend ) {\n\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n\t\t}\n\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\n\t\t}`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isRefractor = true\n\n      this.type = 'Refractor'\n      this.camera = new PerspectiveCamera()\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0x7f7f7f)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const shader = options.shader || Refractor.RefractorShader\n      const multisample = options.multisample !== undefined ? options.multisample : 4\n\n      //\n\n      const virtualCamera = this.camera\n      virtualCamera.matrixAutoUpdate = false\n      virtualCamera.userData.refractor = true\n\n      //\n\n      const refractorPlane = new Plane()\n      const textureMatrix = new Matrix4()\n\n      // render target\n\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, {\n        samples: multisample,\n        type: HalfFloatType,\n      })\n\n      // material\n\n      this.material = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        transparent: true, // ensures, refractors are drawn from farthest to closest\n      })\n\n      this.material.uniforms['color'].value = color\n      this.material.uniforms['tDiffuse'].value = renderTarget.texture\n      this.material.uniforms['textureMatrix'].value = textureMatrix\n\n      // functions\n\n      const visible = (function () {\n        const refractorWorldPosition = new Vector3()\n        const cameraWorldPosition = new Vector3()\n        const rotationMatrix = new Matrix4()\n\n        const view = new Vector3()\n        const normal = new Vector3()\n\n        return function visible(camera) {\n          refractorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n          cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n          view.subVectors(refractorWorldPosition, cameraWorldPosition)\n\n          rotationMatrix.extractRotation(scope.matrixWorld)\n\n          normal.set(0, 0, 1)\n          normal.applyMatrix4(rotationMatrix)\n\n          return view.dot(normal) < 0\n        }\n      })()\n\n      const updateRefractorPlane = (function () {\n        const normal = new Vector3()\n        const position = new Vector3()\n        const quaternion = new Quaternion()\n        const scale = new Vector3()\n\n        return function updateRefractorPlane() {\n          scope.matrixWorld.decompose(position, quaternion, scale)\n          normal.set(0, 0, 1).applyQuaternion(quaternion).normalize()\n\n          // flip the normal because we want to cull everything above the plane\n\n          normal.negate()\n\n          refractorPlane.setFromNormalAndCoplanarPoint(normal, position)\n        }\n      })()\n\n      const updateVirtualCamera = (function () {\n        const clipPlane = new Plane()\n        const clipVector = new Vector4()\n        const q = new Vector4()\n\n        return function updateVirtualCamera(camera) {\n          virtualCamera.matrixWorld.copy(camera.matrixWorld)\n          virtualCamera.matrixWorldInverse.copy(virtualCamera.matrixWorld).invert()\n          virtualCamera.projectionMatrix.copy(camera.projectionMatrix)\n          virtualCamera.far = camera.far // used in WebGLBackground\n\n          // The following code creates an oblique view frustum for clipping.\n          // see: Lengyel, Eric. “Oblique View Frustum Depth Projection and Clipping”.\n          // Journal of Game Development, Vol. 1, No. 2 (2005), Charles River Media, pp. 5–16\n\n          clipPlane.copy(refractorPlane)\n          clipPlane.applyMatrix4(virtualCamera.matrixWorldInverse)\n\n          clipVector.set(clipPlane.normal.x, clipPlane.normal.y, clipPlane.normal.z, clipPlane.constant)\n\n          // calculate the clip-space corner point opposite the clipping plane and\n          // transform it into camera space by multiplying it by the inverse of the projection matrix\n\n          const projectionMatrix = virtualCamera.projectionMatrix\n\n          q.x = (Math.sign(clipVector.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0]\n          q.y = (Math.sign(clipVector.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5]\n          q.z = -1.0\n          q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]\n\n          // calculate the scaled plane vector\n\n          clipVector.multiplyScalar(2.0 / clipVector.dot(q))\n\n          // replacing the third row of the projection matrix\n\n          projectionMatrix.elements[2] = clipVector.x\n          projectionMatrix.elements[6] = clipVector.y\n          projectionMatrix.elements[10] = clipVector.z + 1.0 - clipBias\n          projectionMatrix.elements[14] = clipVector.w\n        }\n      })()\n\n      // This will update the texture matrix that is used for projective texture mapping in the shader.\n      // see: http://developer.download.nvidia.com/assets/gamedev/docs/projective_texture_mapping.pdf\n\n      function updateTextureMatrix(camera) {\n        // this matrix does range mapping to [ 0, 1 ]\n\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n\n        // we use \"Object Linear Texgen\", so we need to multiply the texture matrix T\n        // (matrix above) with the projection and view matrix of the virtual camera\n        // and the model matrix of the refractor\n\n        textureMatrix.multiply(camera.projectionMatrix)\n        textureMatrix.multiply(camera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n      }\n\n      //\n\n      function render(renderer, scene, camera) {\n        scope.visible = false\n\n        const currentRenderTarget = renderer.getRenderTarget()\n        const currentXrEnabled = renderer.xr.enabled\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n        const currentToneMapping = renderer.toneMapping\n\n        let isSRGB = false\n        if ('outputColorSpace' in renderer) isSRGB = renderer.outputColorSpace === 'srgb'\n        else isSRGB = renderer.outputEncoding === 3001 // sRGBEncoding\n\n        renderer.xr.enabled = false // avoid camera modification\n        renderer.shadowMap.autoUpdate = false // avoid re-computing shadows\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = 'srgb-linear'\n        else renderer.outputEncoding = 3000 // LinearEncoding\n        renderer.toneMapping = NoToneMapping\n\n        renderer.setRenderTarget(renderTarget)\n        if (renderer.autoClear === false) renderer.clear()\n        renderer.render(scene, virtualCamera)\n\n        renderer.xr.enabled = currentXrEnabled\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n        renderer.toneMapping = currentToneMapping\n        renderer.setRenderTarget(currentRenderTarget)\n\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = isSRGB ? 'srgb' : 'srgb-linear'\n        else renderer.outputEncoding = isSRGB ? 3001 : 3000\n\n        // restore viewport\n\n        const viewport = camera.viewport\n\n        if (viewport !== undefined) {\n          renderer.state.viewport(viewport)\n        }\n\n        scope.visible = true\n      }\n\n      //\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        // ensure refractors are rendered only once per frame\n\n        if (camera.userData.refractor === true) return\n\n        // avoid rendering when the refractor is viewed from behind\n\n        if (!visible(camera) === true) return\n\n        // update\n\n        updateRefractorPlane()\n\n        updateTextureMatrix(camera)\n\n        updateVirtualCamera(camera)\n\n        render(renderer, scene, camera)\n      }\n\n      this.getRenderTarget = function () {\n        return renderTarget\n      }\n\n      this.dispose = function () {\n        renderTarget.dispose()\n        scope.material.dispose()\n      }\n    }\n  }\n\n  return Refractor\n})()\n\nexport { Refractor }\n"], "mappings": ";;;;;;;;;;;;;AAiBK,MAACA,SAAA,GAA6B,sBAAM;EACvC,MAAMC,UAAA,GAAN,cAAwBC,IAAA,CAAK;IA2D3BC,YAAYC,QAAA,EAAUC,OAAA,GAAU,IAAI;MAClC,MAAMD,QAAQ;MAEd,KAAKE,WAAA,GAAc;MAEnB,KAAKC,IAAA,GAAO;MACZ,KAAKC,MAAA,GAAS,IAAIC,iBAAA,CAAmB;MAErC,MAAMC,KAAA,GAAQ;MAEd,MAAMC,KAAA,GAAQN,OAAA,CAAQM,KAAA,KAAU,SAAY,IAAIC,KAAA,CAAMP,OAAA,CAAQM,KAAK,IAAI,IAAIC,KAAA,CAAM,OAAQ;MACzF,MAAMC,YAAA,GAAeR,OAAA,CAAQQ,YAAA,IAAgB;MAC7C,MAAMC,aAAA,GAAgBT,OAAA,CAAQS,aAAA,IAAiB;MAC/C,MAAMC,QAAA,GAAWV,OAAA,CAAQU,QAAA,IAAY;MACrC,MAAMC,MAAA,GAASX,OAAA,CAAQW,MAAA,IAAUf,UAAA,CAAUgB,eAAA;MAC3C,MAAMC,WAAA,GAAcb,OAAA,CAAQa,WAAA,KAAgB,SAAYb,OAAA,CAAQa,WAAA,GAAc;MAI9E,MAAMC,aAAA,GAAgB,KAAKX,MAAA;MAC3BW,aAAA,CAAcC,gBAAA,GAAmB;MACjCD,aAAA,CAAcE,QAAA,CAASC,SAAA,GAAY;MAInC,MAAMC,cAAA,GAAiB,IAAIC,KAAA,CAAO;MAClC,MAAMC,aAAA,GAAgB,IAAIC,OAAA,CAAS;MAInC,MAAMC,YAAA,GAAe,IAAIC,iBAAA,CAAkBf,YAAA,EAAcC,aAAA,EAAe;QACtEe,OAAA,EAASX,WAAA;QACTX,IAAA,EAAMuB;MACd,CAAO;MAID,KAAKC,QAAA,GAAW,IAAIC,cAAA,CAAe;QACjCC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAMnB,MAAA,CAAOiB,QAAQ;QAC7CG,YAAA,EAAcpB,MAAA,CAAOoB,YAAA;QACrBC,cAAA,EAAgBrB,MAAA,CAAOqB,cAAA;QACvBC,WAAA,EAAa;QAAA;MACrB,CAAO;MAED,KAAKP,QAAA,CAASE,QAAA,CAAS,OAAO,EAAEM,KAAA,GAAQ5B,KAAA;MACxC,KAAKoB,QAAA,CAASE,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQZ,YAAA,CAAaa,OAAA;MACxD,KAAKT,QAAA,CAASE,QAAA,CAAS,eAAe,EAAEM,KAAA,GAAQd,aAAA;MAIhD,MAAMgB,OAAA,GAAW,YAAY;QAC3B,MAAMC,sBAAA,GAAyB,IAAIC,OAAA,CAAS;QAC5C,MAAMC,mBAAA,GAAsB,IAAID,OAAA,CAAS;QACzC,MAAME,cAAA,GAAiB,IAAInB,OAAA,CAAS;QAEpC,MAAMoB,IAAA,GAAO,IAAIH,OAAA,CAAS;QAC1B,MAAMI,MAAA,GAAS,IAAIJ,OAAA,CAAS;QAE5B,OAAO,SAASK,SAAQxC,MAAA,EAAQ;UAC9BkC,sBAAA,CAAuBO,qBAAA,CAAsBvC,KAAA,CAAMwC,WAAW;UAC9DN,mBAAA,CAAoBK,qBAAA,CAAsBzC,MAAA,CAAO0C,WAAW;UAE5DJ,IAAA,CAAKK,UAAA,CAAWT,sBAAA,EAAwBE,mBAAmB;UAE3DC,cAAA,CAAeO,eAAA,CAAgB1C,KAAA,CAAMwC,WAAW;UAEhDH,MAAA,CAAOM,GAAA,CAAI,GAAG,GAAG,CAAC;UAClBN,MAAA,CAAOO,YAAA,CAAaT,cAAc;UAElC,OAAOC,IAAA,CAAKS,GAAA,CAAIR,MAAM,IAAI;QAC3B;MACT,EAAU;MAEJ,MAAMS,oBAAA,GAAwB,YAAY;QACxC,MAAMT,MAAA,GAAS,IAAIJ,OAAA,CAAS;QAC5B,MAAMc,QAAA,GAAW,IAAId,OAAA,CAAS;QAC9B,MAAMe,UAAA,GAAa,IAAIC,UAAA,CAAY;QACnC,MAAMC,KAAA,GAAQ,IAAIjB,OAAA,CAAS;QAE3B,OAAO,SAASkB,sBAAA,EAAuB;UACrCnD,KAAA,CAAMwC,WAAA,CAAYY,SAAA,CAAUL,QAAA,EAAUC,UAAA,EAAYE,KAAK;UACvDb,MAAA,CAAOM,GAAA,CAAI,GAAG,GAAG,CAAC,EAAEU,eAAA,CAAgBL,UAAU,EAAEM,SAAA,CAAW;UAI3DjB,MAAA,CAAOkB,MAAA,CAAQ;UAEf1C,cAAA,CAAe2C,6BAAA,CAA8BnB,MAAA,EAAQU,QAAQ;QAC9D;MACT,EAAU;MAEJ,MAAMU,mBAAA,GAAuB,YAAY;QACvC,MAAMC,SAAA,GAAY,IAAI5C,KAAA,CAAO;QAC7B,MAAM6C,UAAA,GAAa,IAAIC,OAAA,CAAS;QAChC,MAAMC,CAAA,GAAI,IAAID,OAAA,CAAS;QAEvB,OAAO,SAASE,qBAAoBhE,MAAA,EAAQ;UAC1CW,aAAA,CAAc+B,WAAA,CAAYuB,IAAA,CAAKjE,MAAA,CAAO0C,WAAW;UACjD/B,aAAA,CAAcuD,kBAAA,CAAmBD,IAAA,CAAKtD,aAAA,CAAc+B,WAAW,EAAEyB,MAAA,CAAQ;UACzExD,aAAA,CAAcyD,gBAAA,CAAiBH,IAAA,CAAKjE,MAAA,CAAOoE,gBAAgB;UAC3DzD,aAAA,CAAc0D,GAAA,GAAMrE,MAAA,CAAOqE,GAAA;UAM3BT,SAAA,CAAUK,IAAA,CAAKlD,cAAc;UAC7B6C,SAAA,CAAUd,YAAA,CAAanC,aAAA,CAAcuD,kBAAkB;UAEvDL,UAAA,CAAWhB,GAAA,CAAIe,SAAA,CAAUrB,MAAA,CAAO+B,CAAA,EAAGV,SAAA,CAAUrB,MAAA,CAAOgC,CAAA,EAAGX,SAAA,CAAUrB,MAAA,CAAOiC,CAAA,EAAGZ,SAAA,CAAUa,QAAQ;UAK7F,MAAML,gBAAA,GAAmBzD,aAAA,CAAcyD,gBAAA;UAEvCL,CAAA,CAAEO,CAAA,IAAKI,IAAA,CAAKC,IAAA,CAAKd,UAAA,CAAWS,CAAC,IAAIF,gBAAA,CAAiBQ,QAAA,CAAS,CAAC,KAAKR,gBAAA,CAAiBQ,QAAA,CAAS,CAAC;UAC5Fb,CAAA,CAAEQ,CAAA,IAAKG,IAAA,CAAKC,IAAA,CAAKd,UAAA,CAAWU,CAAC,IAAIH,gBAAA,CAAiBQ,QAAA,CAAS,CAAC,KAAKR,gBAAA,CAAiBQ,QAAA,CAAS,CAAC;UAC5Fb,CAAA,CAAES,CAAA,GAAI;UACNT,CAAA,CAAEc,CAAA,IAAK,IAAMT,gBAAA,CAAiBQ,QAAA,CAAS,EAAE,KAAKR,gBAAA,CAAiBQ,QAAA,CAAS,EAAE;UAI1Ef,UAAA,CAAWiB,cAAA,CAAe,IAAMjB,UAAA,CAAWd,GAAA,CAAIgB,CAAC,CAAC;UAIjDK,gBAAA,CAAiBQ,QAAA,CAAS,CAAC,IAAIf,UAAA,CAAWS,CAAA;UAC1CF,gBAAA,CAAiBQ,QAAA,CAAS,CAAC,IAAIf,UAAA,CAAWU,CAAA;UAC1CH,gBAAA,CAAiBQ,QAAA,CAAS,EAAE,IAAIf,UAAA,CAAWW,CAAA,GAAI,IAAMjE,QAAA;UACrD6D,gBAAA,CAAiBQ,QAAA,CAAS,EAAE,IAAIf,UAAA,CAAWgB,CAAA;QAC5C;MACT,EAAU;MAKJ,SAASE,oBAAoB/E,MAAA,EAAQ;QAGnCiB,aAAA,CAAc4B,GAAA,CAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;QAMhG5B,aAAA,CAAc+D,QAAA,CAAShF,MAAA,CAAOoE,gBAAgB;QAC9CnD,aAAA,CAAc+D,QAAA,CAAShF,MAAA,CAAOkE,kBAAkB;QAChDjD,aAAA,CAAc+D,QAAA,CAAS9E,KAAA,CAAMwC,WAAW;MACzC;MAID,SAASuC,OAAOC,QAAA,EAAUC,KAAA,EAAOnF,MAAA,EAAQ;QACvCE,KAAA,CAAM+B,OAAA,GAAU;QAEhB,MAAMmD,mBAAA,GAAsBF,QAAA,CAASG,eAAA,CAAiB;QACtD,MAAMC,gBAAA,GAAmBJ,QAAA,CAASK,EAAA,CAAGC,OAAA;QACrC,MAAMC,uBAAA,GAA0BP,QAAA,CAASQ,SAAA,CAAUC,UAAA;QACnD,MAAMC,kBAAA,GAAqBV,QAAA,CAASW,WAAA;QAEpC,IAAIC,MAAA,GAAS;QACb,IAAI,sBAAsBZ,QAAA,EAAUY,MAAA,GAASZ,QAAA,CAASa,gBAAA,KAAqB,YACtED,MAAA,GAASZ,QAAA,CAASc,cAAA,KAAmB;QAE1Cd,QAAA,CAASK,EAAA,CAAGC,OAAA,GAAU;QACtBN,QAAA,CAASQ,SAAA,CAAUC,UAAA,GAAa;QAChC,IAAI,sBAAsBT,QAAA,EAAUA,QAAA,CAASa,gBAAA,GAAmB,mBAC3Db,QAAA,CAASc,cAAA,GAAiB;QAC/Bd,QAAA,CAASW,WAAA,GAAcI,aAAA;QAEvBf,QAAA,CAASgB,eAAA,CAAgB/E,YAAY;QACrC,IAAI+D,QAAA,CAASiB,SAAA,KAAc,OAAOjB,QAAA,CAASkB,KAAA,CAAO;QAClDlB,QAAA,CAASD,MAAA,CAAOE,KAAA,EAAOxE,aAAa;QAEpCuE,QAAA,CAASK,EAAA,CAAGC,OAAA,GAAUF,gBAAA;QACtBJ,QAAA,CAASQ,SAAA,CAAUC,UAAA,GAAaF,uBAAA;QAChCP,QAAA,CAASW,WAAA,GAAcD,kBAAA;QACvBV,QAAA,CAASgB,eAAA,CAAgBd,mBAAmB;QAE5C,IAAI,sBAAsBF,QAAA,EAAUA,QAAA,CAASa,gBAAA,GAAmBD,MAAA,GAAS,SAAS,mBAC7EZ,QAAA,CAASc,cAAA,GAAiBF,MAAA,GAAS,OAAO;QAI/C,MAAMO,QAAA,GAAWrG,MAAA,CAAOqG,QAAA;QAExB,IAAIA,QAAA,KAAa,QAAW;UAC1BnB,QAAA,CAASoB,KAAA,CAAMD,QAAA,CAASA,QAAQ;QACjC;QAEDnG,KAAA,CAAM+B,OAAA,GAAU;MACjB;MAID,KAAKsE,cAAA,GAAiB,UAAUrB,QAAA,EAAUC,KAAA,EAAOnF,MAAA,EAAQ;QAGvD,IAAIA,MAAA,CAAOa,QAAA,CAASC,SAAA,KAAc,MAAM;QAIxC,IAAI,CAACmB,OAAA,CAAQjC,MAAM,MAAM,MAAM;QAI/BgD,oBAAA,CAAsB;QAEtB+B,mBAAA,CAAoB/E,MAAM;QAE1B2D,mBAAA,CAAoB3D,MAAM;QAE1BiF,MAAA,CAAOC,QAAA,EAAUC,KAAA,EAAOnF,MAAM;MAC/B;MAED,KAAKqF,eAAA,GAAkB,YAAY;QACjC,OAAOlE,YAAA;MACR;MAED,KAAKqF,OAAA,GAAU,YAAY;QACzBrF,YAAA,CAAaqF,OAAA,CAAS;QACtBtG,KAAA,CAAMqB,QAAA,CAASiF,OAAA,CAAS;MACzB;IACF;EACF;EA5RD,IAAMC,UAAA,GAANhH,UAAA;EACEiH,aAAA,CADID,UAAA,EACG,mBAAkB;IACvBhF,QAAA,EAAU;MACRtB,KAAA,EAAO;QACL4B,KAAA,EAAO;MACR;MAED4E,QAAA,EAAU;QACR5E,KAAA,EAAO;MACR;MAEDd,aAAA,EAAe;QACbc,KAAA,EAAO;MACR;IACF;IAEDH,YAAA;IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAazBC,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAyBlB+E,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;EAGnD;EAqOH,OAAOH,UAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}