{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport var ComparisonResultType;\n(function (ComparisonResultType) {\n  ComparisonResultType[\"EQUAL\"] = \"EQUAL\";\n  ComparisonResultType[\"NOT_EQUAL\"] = \"NOT_EQUAL\";\n  ComparisonResultType[\"ERROR\"] = \"ERROR\";\n})(ComparisonResultType || (ComparisonResultType = {}));\nexport class ComparisonResult {\n  constructor(result = ComparisonResultType.EQUAL, message = \"\", actualIndex = 0, expectedIndex = 0) {\n    this.result = result;\n    this.message = message;\n    this.actualIndex = actualIndex;\n    this.expectedIndex = expectedIndex;\n  }\n}", "map": {"version": 3, "names": ["ComparisonResultType", "ComparisonResult", "constructor", "result", "EQUAL", "message", "actualIndex", "expectedIndex"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/ComparisonResult.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport var ComparisonResultType;\n(function (ComparisonResultType) {\n    ComparisonResultType[\"EQUAL\"] = \"EQUAL\";\n    ComparisonResultType[\"NOT_EQUAL\"] = \"NOT_EQUAL\";\n    ComparisonResultType[\"ERROR\"] = \"ERROR\";\n})(ComparisonResultType || (ComparisonResultType = {}));\nexport class ComparisonResult {\n    constructor(result = ComparisonResultType.EQUAL, message = \"\", actualIndex = 0, expectedIndex = 0) {\n        this.result = result;\n        this.message = message;\n        this.actualIndex = actualIndex;\n        this.expectedIndex = expectedIndex;\n    }\n}\n//# sourceMappingURL=ComparisonResult.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO;EACvCA,oBAAoB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC/CA,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO;AAC3C,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,MAAM,GAAGH,oBAAoB,CAACI,KAAK,EAAEC,OAAO,GAAG,EAAE,EAAEC,WAAW,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,EAAE;IAC/F,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}