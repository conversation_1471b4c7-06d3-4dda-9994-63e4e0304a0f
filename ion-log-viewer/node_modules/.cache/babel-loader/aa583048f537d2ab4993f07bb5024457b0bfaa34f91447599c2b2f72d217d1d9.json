{"ast": null, "code": "import { Object3D, Box2, Color, Vector3, Matrix3, Matrix4, Camera } from \"three\";\nimport { Projector, RenderableSprite, RenderableLine, RenderableFace } from \"./Projector.js\";\nclass SVGObject extends Object3D {\n  constructor(node) {\n    super();\n    this.isSVGObject = true;\n    this.node = node;\n  }\n}\nclass SVGRenderer {\n  constructor() {\n    let _renderData,\n      _elements,\n      _lights,\n      _svgWidth,\n      _svgHeight,\n      _svgWidthHalf,\n      _svgHeightHalf,\n      _v1,\n      _v2,\n      _v3,\n      _svgNode,\n      _pathCount = 0,\n      _precision = null,\n      _quality = 1,\n      _currentPath,\n      _currentStyle;\n    const _this = this,\n      _clipBox = new Box2(),\n      _elemBox = new Box2(),\n      _color = new Color(),\n      _diffuseColor = new Color(),\n      _ambientLight = new Color(),\n      _directionalLights = new Color(),\n      _pointLights = new Color(),\n      _clearColor = new Color(),\n      _vector3 = new Vector3(),\n      _centroid = new Vector3(),\n      _normal = new Vector3(),\n      _normalViewMatrix = new Matrix3(),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _svgPathPool = [],\n      _projector = new Projector(),\n      _svg = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n    this.domElement = _svg;\n    this.autoClear = true;\n    this.sortObjects = true;\n    this.sortElements = true;\n    this.overdraw = 0.5;\n    this.info = {\n      render: {\n        vertices: 0,\n        faces: 0\n      }\n    };\n    this.setQuality = function (quality) {\n      switch (quality) {\n        case \"high\":\n          _quality = 1;\n          break;\n        case \"low\":\n          _quality = 0;\n          break;\n      }\n    };\n    this.setClearColor = function (color) {\n      _clearColor.set(color);\n    };\n    this.setPixelRatio = function () {};\n    this.setSize = function (width, height) {\n      _svgWidth = width;\n      _svgHeight = height;\n      _svgWidthHalf = _svgWidth / 2;\n      _svgHeightHalf = _svgHeight / 2;\n      _svg.setAttribute(\"viewBox\", -_svgWidthHalf + \" \" + -_svgHeightHalf + \" \" + _svgWidth + \" \" + _svgHeight);\n      _svg.setAttribute(\"width\", _svgWidth);\n      _svg.setAttribute(\"height\", _svgHeight);\n      _clipBox.min.set(-_svgWidthHalf, -_svgHeightHalf);\n      _clipBox.max.set(_svgWidthHalf, _svgHeightHalf);\n    };\n    this.getSize = function () {\n      return {\n        width: _svgWidth,\n        height: _svgHeight\n      };\n    };\n    this.setPrecision = function (precision) {\n      _precision = precision;\n    };\n    function removeChildNodes() {\n      _pathCount = 0;\n      while (_svg.childNodes.length > 0) {\n        _svg.removeChild(_svg.childNodes[0]);\n      }\n    }\n    function convert(c) {\n      return _precision !== null ? c.toFixed(_precision) : c;\n    }\n    this.clear = function () {\n      removeChildNodes();\n      _svg.style.backgroundColor = _clearColor.getStyle();\n    };\n    this.render = function (scene, camera) {\n      if (camera instanceof Camera === false) {\n        console.error(\"THREE.SVGRenderer.render: camera is not an instance of Camera.\");\n        return;\n      }\n      const background = scene.background;\n      if (background && background.isColor) {\n        removeChildNodes();\n        _svg.style.backgroundColor = background.getStyle();\n      } else if (this.autoClear === true) {\n        this.clear();\n      }\n      _this.info.render.vertices = 0;\n      _this.info.render.faces = 0;\n      _viewMatrix.copy(camera.matrixWorldInverse);\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix);\n      _renderData = _projector.projectScene(scene, camera, this.sortObjects, this.sortElements);\n      _elements = _renderData.elements;\n      _lights = _renderData.lights;\n      _normalViewMatrix.getNormalMatrix(camera.matrixWorldInverse);\n      calculateLights(_lights);\n      _currentPath = \"\";\n      _currentStyle = \"\";\n      for (let e = 0, el = _elements.length; e < el; e++) {\n        const element = _elements[e];\n        const material = element.material;\n        if (material === void 0 || material.opacity === 0) continue;\n        _elemBox.makeEmpty();\n        if (element instanceof RenderableSprite) {\n          _v1 = element;\n          _v1.x *= _svgWidthHalf;\n          _v1.y *= -_svgHeightHalf;\n          renderSprite(_v1, element, material);\n        } else if (element instanceof RenderableLine) {\n          _v1 = element.v1;\n          _v2 = element.v2;\n          _v1.positionScreen.x *= _svgWidthHalf;\n          _v1.positionScreen.y *= -_svgHeightHalf;\n          _v2.positionScreen.x *= _svgWidthHalf;\n          _v2.positionScreen.y *= -_svgHeightHalf;\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen]);\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderLine(_v1, _v2, material);\n          }\n        } else if (element instanceof RenderableFace) {\n          _v1 = element.v1;\n          _v2 = element.v2;\n          _v3 = element.v3;\n          if (_v1.positionScreen.z < -1 || _v1.positionScreen.z > 1) continue;\n          if (_v2.positionScreen.z < -1 || _v2.positionScreen.z > 1) continue;\n          if (_v3.positionScreen.z < -1 || _v3.positionScreen.z > 1) continue;\n          _v1.positionScreen.x *= _svgWidthHalf;\n          _v1.positionScreen.y *= -_svgHeightHalf;\n          _v2.positionScreen.x *= _svgWidthHalf;\n          _v2.positionScreen.y *= -_svgHeightHalf;\n          _v3.positionScreen.x *= _svgWidthHalf;\n          _v3.positionScreen.y *= -_svgHeightHalf;\n          if (this.overdraw > 0) {\n            expand(_v1.positionScreen, _v2.positionScreen, this.overdraw);\n            expand(_v2.positionScreen, _v3.positionScreen, this.overdraw);\n            expand(_v3.positionScreen, _v1.positionScreen, this.overdraw);\n          }\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen, _v3.positionScreen]);\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderFace3(_v1, _v2, _v3, element, material);\n          }\n        }\n      }\n      flushPath();\n      scene.traverseVisible(function (object) {\n        if (object.isSVGObject) {\n          _vector3.setFromMatrixPosition(object.matrixWorld);\n          _vector3.applyMatrix4(_viewProjectionMatrix);\n          if (_vector3.z < -1 || _vector3.z > 1) return;\n          const x = _vector3.x * _svgWidthHalf;\n          const y = -_vector3.y * _svgHeightHalf;\n          const node = object.node;\n          node.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n          _svg.appendChild(node);\n        }\n      });\n    };\n    function calculateLights(lights) {\n      _ambientLight.setRGB(0, 0, 0);\n      _directionalLights.setRGB(0, 0, 0);\n      _pointLights.setRGB(0, 0, 0);\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l];\n        const lightColor = light.color;\n        if (light.isAmbientLight) {\n          _ambientLight.r += lightColor.r;\n          _ambientLight.g += lightColor.g;\n          _ambientLight.b += lightColor.b;\n        } else if (light.isDirectionalLight) {\n          _directionalLights.r += lightColor.r;\n          _directionalLights.g += lightColor.g;\n          _directionalLights.b += lightColor.b;\n        } else if (light.isPointLight) {\n          _pointLights.r += lightColor.r;\n          _pointLights.g += lightColor.g;\n          _pointLights.b += lightColor.b;\n        }\n      }\n    }\n    function calculateLight(lights, position, normal, color) {\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l];\n        const lightColor = light.color;\n        if (light.isDirectionalLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld).normalize();\n          let amount = normal.dot(lightPosition);\n          if (amount <= 0) continue;\n          amount *= light.intensity;\n          color.r += lightColor.r * amount;\n          color.g += lightColor.g * amount;\n          color.b += lightColor.b * amount;\n        } else if (light.isPointLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld);\n          let amount = normal.dot(_vector3.subVectors(lightPosition, position).normalize());\n          if (amount <= 0) continue;\n          amount *= light.distance == 0 ? 1 : 1 - Math.min(position.distanceTo(lightPosition) / light.distance, 1);\n          if (amount == 0) continue;\n          amount *= light.intensity;\n          color.r += lightColor.r * amount;\n          color.g += lightColor.g * amount;\n          color.b += lightColor.b * amount;\n        }\n      }\n    }\n    function renderSprite(v1, element, material) {\n      let scaleX = element.scale.x * _svgWidthHalf;\n      let scaleY = element.scale.y * _svgHeightHalf;\n      if (material.isPointsMaterial) {\n        scaleX *= material.size;\n        scaleY *= material.size;\n      }\n      const path = \"M\" + convert(v1.x - scaleX * 0.5) + \",\" + convert(v1.y - scaleY * 0.5) + \"h\" + convert(scaleX) + \"v\" + convert(scaleY) + \"h\" + convert(-scaleX) + \"z\";\n      let style = \"\";\n      if (material.isSpriteMaterial || material.isPointsMaterial) {\n        style = \"fill:\" + material.color.getStyle() + \";fill-opacity:\" + material.opacity;\n      }\n      addPath(style, path);\n    }\n    function renderLine(v1, v2, material) {\n      const path = \"M\" + convert(v1.positionScreen.x) + \",\" + convert(v1.positionScreen.y) + \"L\" + convert(v2.positionScreen.x) + \",\" + convert(v2.positionScreen.y);\n      if (material.isLineBasicMaterial) {\n        let style = \"fill:none;stroke:\" + material.color.getStyle() + \";stroke-opacity:\" + material.opacity + \";stroke-width:\" + material.linewidth + \";stroke-linecap:\" + material.linecap;\n        if (material.isLineDashedMaterial) {\n          style = style + \";stroke-dasharray:\" + material.dashSize + \",\" + material.gapSize;\n        }\n        addPath(style, path);\n      }\n    }\n    function renderFace3(v1, v2, v3, element, material) {\n      _this.info.render.vertices += 3;\n      _this.info.render.faces++;\n      const path = \"M\" + convert(v1.positionScreen.x) + \",\" + convert(v1.positionScreen.y) + \"L\" + convert(v2.positionScreen.x) + \",\" + convert(v2.positionScreen.y) + \"L\" + convert(v3.positionScreen.x) + \",\" + convert(v3.positionScreen.y) + \"z\";\n      let style = \"\";\n      if (material.isMeshBasicMaterial) {\n        _color.copy(material.color);\n        if (material.vertexColors) {\n          _color.multiply(element.color);\n        }\n      } else if (material.isMeshLambertMaterial || material.isMeshPhongMaterial || material.isMeshStandardMaterial) {\n        _diffuseColor.copy(material.color);\n        if (material.vertexColors) {\n          _diffuseColor.multiply(element.color);\n        }\n        _color.copy(_ambientLight);\n        _centroid.copy(v1.positionWorld).add(v2.positionWorld).add(v3.positionWorld).divideScalar(3);\n        calculateLight(_lights, _centroid, element.normalModel, _color);\n        _color.multiply(_diffuseColor).add(material.emissive);\n      } else if (material.isMeshNormalMaterial) {\n        _normal.copy(element.normalModel).applyMatrix3(_normalViewMatrix).normalize();\n        _color.setRGB(_normal.x, _normal.y, _normal.z).multiplyScalar(0.5).addScalar(0.5);\n      }\n      if (material.wireframe) {\n        style = \"fill:none;stroke:\" + _color.getStyle() + \";stroke-opacity:\" + material.opacity + \";stroke-width:\" + material.wireframeLinewidth + \";stroke-linecap:\" + material.wireframeLinecap + \";stroke-linejoin:\" + material.wireframeLinejoin;\n      } else {\n        style = \"fill:\" + _color.getStyle() + \";fill-opacity:\" + material.opacity;\n      }\n      addPath(style, path);\n    }\n    function expand(v1, v2, pixels) {\n      let x = v2.x - v1.x,\n        y = v2.y - v1.y;\n      const det = x * x + y * y;\n      if (det === 0) return;\n      const idet = pixels / Math.sqrt(det);\n      x *= idet;\n      y *= idet;\n      v2.x += x;\n      v2.y += y;\n      v1.x -= x;\n      v1.y -= y;\n    }\n    function addPath(style, path) {\n      if (_currentStyle === style) {\n        _currentPath += path;\n      } else {\n        flushPath();\n        _currentStyle = style;\n        _currentPath = path;\n      }\n    }\n    function flushPath() {\n      if (_currentPath) {\n        _svgNode = getPathNode(_pathCount++);\n        _svgNode.setAttribute(\"d\", _currentPath);\n        _svgNode.setAttribute(\"style\", _currentStyle);\n        _svg.appendChild(_svgNode);\n      }\n      _currentPath = \"\";\n      _currentStyle = \"\";\n    }\n    function getPathNode(id) {\n      if (_svgPathPool[id] == null) {\n        _svgPathPool[id] = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        if (_quality == 0) {\n          _svgPathPool[id].setAttribute(\"shape-rendering\", \"crispEdges\");\n        }\n        return _svgPathPool[id];\n      }\n      return _svgPathPool[id];\n    }\n  }\n}\nexport { SVGObject, SVGRenderer };", "map": {"version": 3, "names": ["SVGObject", "Object3D", "constructor", "node", "isSVGObject", "<PERSON><PERSON><PERSON><PERSON>", "_renderData", "_elements", "_lights", "_svgWidth", "_svgHeight", "_svgWidthHalf", "_svgHeightHalf", "_v1", "_v2", "_v3", "_svgNode", "_pathCount", "_precision", "_quality", "_currentPath", "_currentStyle", "_this", "_clipBox", "Box2", "_elemBox", "_color", "Color", "_diffuseColor", "_ambientLight", "_directionalLights", "_pointLights", "_clearColor", "_vector3", "Vector3", "_centroid", "_normal", "_normalViewMatrix", "Matrix3", "_viewMatrix", "Matrix4", "_viewProjectionMatrix", "_svgPathPool", "_projector", "Projector", "_svg", "document", "createElementNS", "dom<PERSON>lement", "autoClear", "sortObjects", "sortElements", "overdraw", "info", "render", "vertices", "faces", "setQuality", "quality", "setClearColor", "color", "set", "setPixelRatio", "setSize", "width", "height", "setAttribute", "min", "max", "getSize", "setPrecision", "precision", "removeChildNodes", "childNodes", "length", "<PERSON><PERSON><PERSON><PERSON>", "convert", "c", "toFixed", "clear", "style", "backgroundColor", "getStyle", "scene", "camera", "Camera", "console", "error", "background", "isColor", "copy", "matrixWorldInverse", "multiplyMatrices", "projectionMatrix", "projectScene", "elements", "lights", "getNormalMatrix", "calculateLights", "e", "el", "element", "material", "opacity", "makeEmpty", "RenderableSprite", "x", "y", "renderSprite", "RenderableLine", "v1", "v2", "positionScreen", "setFromPoints", "intersectsBox", "renderLine", "RenderableFace", "v3", "z", "expand", "renderFace3", "flush<PERSON><PERSON>", "traverseVisible", "object", "setFromMatrixPosition", "matrixWorld", "applyMatrix4", "append<PERSON><PERSON><PERSON>", "setRGB", "l", "ll", "light", "lightColor", "isAmbientLight", "r", "g", "b", "isDirectionalLight", "isPointLight", "calculateLight", "position", "normal", "lightPosition", "normalize", "amount", "dot", "intensity", "subVectors", "distance", "Math", "distanceTo", "scaleX", "scale", "scaleY", "isPointsMaterial", "size", "path", "isSpriteMaterial", "addPath", "isLineBasicMaterial", "linewidth", "linecap", "isLineDashedMaterial", "dashSize", "gapSize", "isMeshBasicMaterial", "vertexColors", "multiply", "isMeshLambertMaterial", "isMeshPhongMaterial", "isMeshStandardMaterial", "positionWorld", "add", "divideScalar", "normalModel", "emissive", "isMeshNormalMaterial", "applyMatrix3", "multiplyScalar", "addScalar", "wireframe", "wireframeLinewidth", "wireframeLinecap", "wireframeLinejoin", "pixels", "det", "idet", "sqrt", "getPathNode", "id"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/renderers/SVGRenderer.js"], "sourcesContent": ["import { Box2, <PERSON>, Color, Matrix3, Matrix4, Object3D, Vector3 } from 'three'\nimport { Projector, RenderableFace, RenderableLine, RenderableSprite } from '../renderers/Projector'\n\nclass SVGObject extends Object3D {\n  constructor(node) {\n    super()\n\n    this.isSVGObject = true\n\n    this.node = node\n  }\n}\n\nclass SVGRenderer {\n  constructor() {\n    let _renderData,\n      _elements,\n      _lights,\n      _svgWidth,\n      _svgHeight,\n      _svgWidthHalf,\n      _svgHeightHalf,\n      _v1,\n      _v2,\n      _v3,\n      _svgNode,\n      _pathCount = 0,\n      _precision = null,\n      _quality = 1,\n      _currentPath,\n      _currentStyle\n\n    const _this = this,\n      _clipBox = new Box2(),\n      _elemBox = new Box2(),\n      _color = new Color(),\n      _diffuseColor = new Color(),\n      _ambientLight = new Color(),\n      _directionalLights = new Color(),\n      _pointLights = new Color(),\n      _clearColor = new Color(),\n      _vector3 = new Vector3(), // Needed for PointLight\n      _centroid = new Vector3(),\n      _normal = new Vector3(),\n      _normalViewMatrix = new Matrix3(),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _svgPathPool = [],\n      _projector = new Projector(),\n      _svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n\n    this.domElement = _svg\n\n    this.autoClear = true\n    this.sortObjects = true\n    this.sortElements = true\n\n    this.overdraw = 0.5\n\n    this.info = {\n      render: {\n        vertices: 0,\n        faces: 0,\n      },\n    }\n\n    this.setQuality = function (quality) {\n      switch (quality) {\n        case 'high':\n          _quality = 1\n          break\n        case 'low':\n          _quality = 0\n          break\n      }\n    }\n\n    this.setClearColor = function (color) {\n      _clearColor.set(color)\n    }\n\n    this.setPixelRatio = function () {}\n\n    this.setSize = function (width, height) {\n      _svgWidth = width\n      _svgHeight = height\n      _svgWidthHalf = _svgWidth / 2\n      _svgHeightHalf = _svgHeight / 2\n\n      _svg.setAttribute('viewBox', -_svgWidthHalf + ' ' + -_svgHeightHalf + ' ' + _svgWidth + ' ' + _svgHeight)\n      _svg.setAttribute('width', _svgWidth)\n      _svg.setAttribute('height', _svgHeight)\n\n      _clipBox.min.set(-_svgWidthHalf, -_svgHeightHalf)\n      _clipBox.max.set(_svgWidthHalf, _svgHeightHalf)\n    }\n\n    this.getSize = function () {\n      return {\n        width: _svgWidth,\n        height: _svgHeight,\n      }\n    }\n\n    this.setPrecision = function (precision) {\n      _precision = precision\n    }\n\n    function removeChildNodes() {\n      _pathCount = 0\n\n      while (_svg.childNodes.length > 0) {\n        _svg.removeChild(_svg.childNodes[0])\n      }\n    }\n\n    function convert(c) {\n      return _precision !== null ? c.toFixed(_precision) : c\n    }\n\n    this.clear = function () {\n      removeChildNodes()\n      _svg.style.backgroundColor = _clearColor.getStyle()\n    }\n\n    this.render = function (scene, camera) {\n      if (camera instanceof Camera === false) {\n        console.error('THREE.SVGRenderer.render: camera is not an instance of Camera.')\n        return\n      }\n\n      const background = scene.background\n\n      if (background && background.isColor) {\n        removeChildNodes()\n        _svg.style.backgroundColor = background.getStyle()\n      } else if (this.autoClear === true) {\n        this.clear()\n      }\n\n      _this.info.render.vertices = 0\n      _this.info.render.faces = 0\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      _renderData = _projector.projectScene(scene, camera, this.sortObjects, this.sortElements)\n      _elements = _renderData.elements\n      _lights = _renderData.lights\n\n      _normalViewMatrix.getNormalMatrix(camera.matrixWorldInverse)\n\n      calculateLights(_lights)\n\n      // reset accumulated path\n\n      _currentPath = ''\n      _currentStyle = ''\n\n      for (let e = 0, el = _elements.length; e < el; e++) {\n        const element = _elements[e]\n        const material = element.material\n\n        if (material === undefined || material.opacity === 0) continue\n\n        _elemBox.makeEmpty()\n\n        if (element instanceof RenderableSprite) {\n          _v1 = element\n          _v1.x *= _svgWidthHalf\n          _v1.y *= -_svgHeightHalf\n\n          renderSprite(_v1, element, material)\n        } else if (element instanceof RenderableLine) {\n          _v1 = element.v1\n          _v2 = element.v2\n\n          _v1.positionScreen.x *= _svgWidthHalf\n          _v1.positionScreen.y *= -_svgHeightHalf\n          _v2.positionScreen.x *= _svgWidthHalf\n          _v2.positionScreen.y *= -_svgHeightHalf\n\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen])\n\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderLine(_v1, _v2, material)\n          }\n        } else if (element instanceof RenderableFace) {\n          _v1 = element.v1\n          _v2 = element.v2\n          _v3 = element.v3\n\n          if (_v1.positionScreen.z < -1 || _v1.positionScreen.z > 1) continue\n          if (_v2.positionScreen.z < -1 || _v2.positionScreen.z > 1) continue\n          if (_v3.positionScreen.z < -1 || _v3.positionScreen.z > 1) continue\n\n          _v1.positionScreen.x *= _svgWidthHalf\n          _v1.positionScreen.y *= -_svgHeightHalf\n          _v2.positionScreen.x *= _svgWidthHalf\n          _v2.positionScreen.y *= -_svgHeightHalf\n          _v3.positionScreen.x *= _svgWidthHalf\n          _v3.positionScreen.y *= -_svgHeightHalf\n\n          if (this.overdraw > 0) {\n            expand(_v1.positionScreen, _v2.positionScreen, this.overdraw)\n            expand(_v2.positionScreen, _v3.positionScreen, this.overdraw)\n            expand(_v3.positionScreen, _v1.positionScreen, this.overdraw)\n          }\n\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen, _v3.positionScreen])\n\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderFace3(_v1, _v2, _v3, element, material)\n          }\n        }\n      }\n\n      flushPath() // just to flush last svg:path\n\n      scene.traverseVisible(function (object) {\n        if (object.isSVGObject) {\n          _vector3.setFromMatrixPosition(object.matrixWorld)\n          _vector3.applyMatrix4(_viewProjectionMatrix)\n\n          if (_vector3.z < -1 || _vector3.z > 1) return\n\n          const x = _vector3.x * _svgWidthHalf\n          const y = -_vector3.y * _svgHeightHalf\n\n          const node = object.node\n          node.setAttribute('transform', 'translate(' + x + ',' + y + ')')\n\n          _svg.appendChild(node)\n        }\n      })\n    }\n\n    function calculateLights(lights) {\n      _ambientLight.setRGB(0, 0, 0)\n      _directionalLights.setRGB(0, 0, 0)\n      _pointLights.setRGB(0, 0, 0)\n\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l]\n        const lightColor = light.color\n\n        if (light.isAmbientLight) {\n          _ambientLight.r += lightColor.r\n          _ambientLight.g += lightColor.g\n          _ambientLight.b += lightColor.b\n        } else if (light.isDirectionalLight) {\n          _directionalLights.r += lightColor.r\n          _directionalLights.g += lightColor.g\n          _directionalLights.b += lightColor.b\n        } else if (light.isPointLight) {\n          _pointLights.r += lightColor.r\n          _pointLights.g += lightColor.g\n          _pointLights.b += lightColor.b\n        }\n      }\n    }\n\n    function calculateLight(lights, position, normal, color) {\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l]\n        const lightColor = light.color\n\n        if (light.isDirectionalLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld).normalize()\n\n          let amount = normal.dot(lightPosition)\n\n          if (amount <= 0) continue\n\n          amount *= light.intensity\n\n          color.r += lightColor.r * amount\n          color.g += lightColor.g * amount\n          color.b += lightColor.b * amount\n        } else if (light.isPointLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld)\n\n          let amount = normal.dot(_vector3.subVectors(lightPosition, position).normalize())\n\n          if (amount <= 0) continue\n\n          amount *= light.distance == 0 ? 1 : 1 - Math.min(position.distanceTo(lightPosition) / light.distance, 1)\n\n          if (amount == 0) continue\n\n          amount *= light.intensity\n\n          color.r += lightColor.r * amount\n          color.g += lightColor.g * amount\n          color.b += lightColor.b * amount\n        }\n      }\n    }\n\n    function renderSprite(v1, element, material) {\n      let scaleX = element.scale.x * _svgWidthHalf\n      let scaleY = element.scale.y * _svgHeightHalf\n\n      if (material.isPointsMaterial) {\n        scaleX *= material.size\n        scaleY *= material.size\n      }\n\n      const path =\n        'M' +\n        convert(v1.x - scaleX * 0.5) +\n        ',' +\n        convert(v1.y - scaleY * 0.5) +\n        'h' +\n        convert(scaleX) +\n        'v' +\n        convert(scaleY) +\n        'h' +\n        convert(-scaleX) +\n        'z'\n      let style = ''\n\n      if (material.isSpriteMaterial || material.isPointsMaterial) {\n        style = 'fill:' + material.color.getStyle() + ';fill-opacity:' + material.opacity\n      }\n\n      addPath(style, path)\n    }\n\n    function renderLine(v1, v2, material) {\n      const path =\n        'M' +\n        convert(v1.positionScreen.x) +\n        ',' +\n        convert(v1.positionScreen.y) +\n        'L' +\n        convert(v2.positionScreen.x) +\n        ',' +\n        convert(v2.positionScreen.y)\n\n      if (material.isLineBasicMaterial) {\n        let style =\n          'fill:none;stroke:' +\n          material.color.getStyle() +\n          ';stroke-opacity:' +\n          material.opacity +\n          ';stroke-width:' +\n          material.linewidth +\n          ';stroke-linecap:' +\n          material.linecap\n\n        if (material.isLineDashedMaterial) {\n          style = style + ';stroke-dasharray:' + material.dashSize + ',' + material.gapSize\n        }\n\n        addPath(style, path)\n      }\n    }\n\n    function renderFace3(v1, v2, v3, element, material) {\n      _this.info.render.vertices += 3\n      _this.info.render.faces++\n\n      const path =\n        'M' +\n        convert(v1.positionScreen.x) +\n        ',' +\n        convert(v1.positionScreen.y) +\n        'L' +\n        convert(v2.positionScreen.x) +\n        ',' +\n        convert(v2.positionScreen.y) +\n        'L' +\n        convert(v3.positionScreen.x) +\n        ',' +\n        convert(v3.positionScreen.y) +\n        'z'\n      let style = ''\n\n      if (material.isMeshBasicMaterial) {\n        _color.copy(material.color)\n\n        if (material.vertexColors) {\n          _color.multiply(element.color)\n        }\n      } else if (material.isMeshLambertMaterial || material.isMeshPhongMaterial || material.isMeshStandardMaterial) {\n        _diffuseColor.copy(material.color)\n\n        if (material.vertexColors) {\n          _diffuseColor.multiply(element.color)\n        }\n\n        _color.copy(_ambientLight)\n\n        _centroid.copy(v1.positionWorld).add(v2.positionWorld).add(v3.positionWorld).divideScalar(3)\n\n        calculateLight(_lights, _centroid, element.normalModel, _color)\n\n        _color.multiply(_diffuseColor).add(material.emissive)\n      } else if (material.isMeshNormalMaterial) {\n        _normal.copy(element.normalModel).applyMatrix3(_normalViewMatrix).normalize()\n\n        _color.setRGB(_normal.x, _normal.y, _normal.z).multiplyScalar(0.5).addScalar(0.5)\n      }\n\n      if (material.wireframe) {\n        style =\n          'fill:none;stroke:' +\n          _color.getStyle() +\n          ';stroke-opacity:' +\n          material.opacity +\n          ';stroke-width:' +\n          material.wireframeLinewidth +\n          ';stroke-linecap:' +\n          material.wireframeLinecap +\n          ';stroke-linejoin:' +\n          material.wireframeLinejoin\n      } else {\n        style = 'fill:' + _color.getStyle() + ';fill-opacity:' + material.opacity\n      }\n\n      addPath(style, path)\n    }\n\n    // Hide anti-alias gaps\n\n    function expand(v1, v2, pixels) {\n      let x = v2.x - v1.x,\n        y = v2.y - v1.y\n      const det = x * x + y * y\n\n      if (det === 0) return\n\n      const idet = pixels / Math.sqrt(det)\n\n      x *= idet\n      y *= idet\n\n      v2.x += x\n      v2.y += y\n      v1.x -= x\n      v1.y -= y\n    }\n\n    function addPath(style, path) {\n      if (_currentStyle === style) {\n        _currentPath += path\n      } else {\n        flushPath()\n\n        _currentStyle = style\n        _currentPath = path\n      }\n    }\n\n    function flushPath() {\n      if (_currentPath) {\n        _svgNode = getPathNode(_pathCount++)\n        _svgNode.setAttribute('d', _currentPath)\n        _svgNode.setAttribute('style', _currentStyle)\n        _svg.appendChild(_svgNode)\n      }\n\n      _currentPath = ''\n      _currentStyle = ''\n    }\n\n    function getPathNode(id) {\n      if (_svgPathPool[id] == null) {\n        _svgPathPool[id] = document.createElementNS('http://www.w3.org/2000/svg', 'path')\n\n        if (_quality == 0) {\n          _svgPathPool[id].setAttribute('shape-rendering', 'crispEdges') //optimizeSpeed\n        }\n\n        return _svgPathPool[id]\n      }\n\n      return _svgPathPool[id]\n    }\n  }\n}\n\nexport { SVGObject, SVGRenderer }\n"], "mappings": ";;AAGA,MAAMA,SAAA,SAAkBC,QAAA,CAAS;EAC/BC,YAAYC,IAAA,EAAM;IAChB,MAAO;IAEP,KAAKC,WAAA,GAAc;IAEnB,KAAKD,IAAA,GAAOA,IAAA;EACb;AACH;AAEA,MAAME,WAAA,CAAY;EAChBH,YAAA,EAAc;IACZ,IAAII,WAAA;MACFC,SAAA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACAC,cAAA;MACAC,GAAA;MACAC,GAAA;MACAC,GAAA;MACAC,QAAA;MACAC,UAAA,GAAa;MACbC,UAAA,GAAa;MACbC,QAAA,GAAW;MACXC,YAAA;MACAC,aAAA;IAEF,MAAMC,KAAA,GAAQ;MACZC,QAAA,GAAW,IAAIC,IAAA,CAAM;MACrBC,QAAA,GAAW,IAAID,IAAA,CAAM;MACrBE,MAAA,GAAS,IAAIC,KAAA,CAAO;MACpBC,aAAA,GAAgB,IAAID,KAAA,CAAO;MAC3BE,aAAA,GAAgB,IAAIF,KAAA,CAAO;MAC3BG,kBAAA,GAAqB,IAAIH,KAAA,CAAO;MAChCI,YAAA,GAAe,IAAIJ,KAAA,CAAO;MAC1BK,WAAA,GAAc,IAAIL,KAAA,CAAO;MACzBM,QAAA,GAAW,IAAIC,OAAA,CAAS;MACxBC,SAAA,GAAY,IAAID,OAAA,CAAS;MACzBE,OAAA,GAAU,IAAIF,OAAA,CAAS;MACvBG,iBAAA,GAAoB,IAAIC,OAAA,CAAS;MACjCC,WAAA,GAAc,IAAIC,OAAA,CAAS;MAC3BC,qBAAA,GAAwB,IAAID,OAAA,CAAS;MACrCE,YAAA,GAAe,EAAE;MACjBC,UAAA,GAAa,IAAIC,SAAA,CAAW;MAC5BC,IAAA,GAAOC,QAAA,CAASC,eAAA,CAAgB,8BAA8B,KAAK;IAErE,KAAKC,UAAA,GAAaH,IAAA;IAElB,KAAKI,SAAA,GAAY;IACjB,KAAKC,WAAA,GAAc;IACnB,KAAKC,YAAA,GAAe;IAEpB,KAAKC,QAAA,GAAW;IAEhB,KAAKC,IAAA,GAAO;MACVC,MAAA,EAAQ;QACNC,QAAA,EAAU;QACVC,KAAA,EAAO;MACR;IACF;IAED,KAAKC,UAAA,GAAa,UAAUC,OAAA,EAAS;MACnC,QAAQA,OAAA;QACN,KAAK;UACHvC,QAAA,GAAW;UACX;QACF,KAAK;UACHA,QAAA,GAAW;UACX;MACH;IACF;IAED,KAAKwC,aAAA,GAAgB,UAAUC,KAAA,EAAO;MACpC5B,WAAA,CAAY6B,GAAA,CAAID,KAAK;IACtB;IAED,KAAKE,aAAA,GAAgB,YAAY,CAAE;IAEnC,KAAKC,OAAA,GAAU,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACtCxD,SAAA,GAAYuD,KAAA;MACZtD,UAAA,GAAauD,MAAA;MACbtD,aAAA,GAAgBF,SAAA,GAAY;MAC5BG,cAAA,GAAiBF,UAAA,GAAa;MAE9BmC,IAAA,CAAKqB,YAAA,CAAa,WAAW,CAACvD,aAAA,GAAgB,MAAM,CAACC,cAAA,GAAiB,MAAMH,SAAA,GAAY,MAAMC,UAAU;MACxGmC,IAAA,CAAKqB,YAAA,CAAa,SAASzD,SAAS;MACpCoC,IAAA,CAAKqB,YAAA,CAAa,UAAUxD,UAAU;MAEtCa,QAAA,CAAS4C,GAAA,CAAIN,GAAA,CAAI,CAAClD,aAAA,EAAe,CAACC,cAAc;MAChDW,QAAA,CAAS6C,GAAA,CAAIP,GAAA,CAAIlD,aAAA,EAAeC,cAAc;IAC/C;IAED,KAAKyD,OAAA,GAAU,YAAY;MACzB,OAAO;QACLL,KAAA,EAAOvD,SAAA;QACPwD,MAAA,EAAQvD;MACT;IACF;IAED,KAAK4D,YAAA,GAAe,UAAUC,SAAA,EAAW;MACvCrD,UAAA,GAAaqD,SAAA;IACd;IAED,SAASC,iBAAA,EAAmB;MAC1BvD,UAAA,GAAa;MAEb,OAAO4B,IAAA,CAAK4B,UAAA,CAAWC,MAAA,GAAS,GAAG;QACjC7B,IAAA,CAAK8B,WAAA,CAAY9B,IAAA,CAAK4B,UAAA,CAAW,CAAC,CAAC;MACpC;IACF;IAED,SAASG,QAAQC,CAAA,EAAG;MAClB,OAAO3D,UAAA,KAAe,OAAO2D,CAAA,CAAEC,OAAA,CAAQ5D,UAAU,IAAI2D,CAAA;IACtD;IAED,KAAKE,KAAA,GAAQ,YAAY;MACvBP,gBAAA,CAAkB;MAClB3B,IAAA,CAAKmC,KAAA,CAAMC,eAAA,GAAkBjD,WAAA,CAAYkD,QAAA,CAAU;IACpD;IAED,KAAK5B,MAAA,GAAS,UAAU6B,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAIA,MAAA,YAAkBC,MAAA,KAAW,OAAO;QACtCC,OAAA,CAAQC,KAAA,CAAM,gEAAgE;QAC9E;MACD;MAED,MAAMC,UAAA,GAAaL,KAAA,CAAMK,UAAA;MAEzB,IAAIA,UAAA,IAAcA,UAAA,CAAWC,OAAA,EAAS;QACpCjB,gBAAA,CAAkB;QAClB3B,IAAA,CAAKmC,KAAA,CAAMC,eAAA,GAAkBO,UAAA,CAAWN,QAAA,CAAU;MAC1D,WAAiB,KAAKjC,SAAA,KAAc,MAAM;QAClC,KAAK8B,KAAA,CAAO;MACb;MAEDzD,KAAA,CAAM+B,IAAA,CAAKC,MAAA,CAAOC,QAAA,GAAW;MAC7BjC,KAAA,CAAM+B,IAAA,CAAKC,MAAA,CAAOE,KAAA,GAAQ;MAE1BjB,WAAA,CAAYmD,IAAA,CAAKN,MAAA,CAAOO,kBAAkB;MAC1ClD,qBAAA,CAAsBmD,gBAAA,CAAiBR,MAAA,CAAOS,gBAAA,EAAkBtD,WAAW;MAE3EjC,WAAA,GAAcqC,UAAA,CAAWmD,YAAA,CAAaX,KAAA,EAAOC,MAAA,EAAQ,KAAKlC,WAAA,EAAa,KAAKC,YAAY;MACxF5C,SAAA,GAAYD,WAAA,CAAYyF,QAAA;MACxBvF,OAAA,GAAUF,WAAA,CAAY0F,MAAA;MAEtB3D,iBAAA,CAAkB4D,eAAA,CAAgBb,MAAA,CAAOO,kBAAkB;MAE3DO,eAAA,CAAgB1F,OAAO;MAIvBY,YAAA,GAAe;MACfC,aAAA,GAAgB;MAEhB,SAAS8E,CAAA,GAAI,GAAGC,EAAA,GAAK7F,SAAA,CAAUmE,MAAA,EAAQyB,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAClD,MAAME,OAAA,GAAU9F,SAAA,CAAU4F,CAAC;QAC3B,MAAMG,QAAA,GAAWD,OAAA,CAAQC,QAAA;QAEzB,IAAIA,QAAA,KAAa,UAAaA,QAAA,CAASC,OAAA,KAAY,GAAG;QAEtD9E,QAAA,CAAS+E,SAAA,CAAW;QAEpB,IAAIH,OAAA,YAAmBI,gBAAA,EAAkB;UACvC5F,GAAA,GAAMwF,OAAA;UACNxF,GAAA,CAAI6F,CAAA,IAAK/F,aAAA;UACTE,GAAA,CAAI8F,CAAA,IAAK,CAAC/F,cAAA;UAEVgG,YAAA,CAAa/F,GAAA,EAAKwF,OAAA,EAASC,QAAQ;QAC7C,WAAmBD,OAAA,YAAmBQ,cAAA,EAAgB;UAC5ChG,GAAA,GAAMwF,OAAA,CAAQS,EAAA;UACdhG,GAAA,GAAMuF,OAAA,CAAQU,EAAA;UAEdlG,GAAA,CAAImG,cAAA,CAAeN,CAAA,IAAK/F,aAAA;UACxBE,GAAA,CAAImG,cAAA,CAAeL,CAAA,IAAK,CAAC/F,cAAA;UACzBE,GAAA,CAAIkG,cAAA,CAAeN,CAAA,IAAK/F,aAAA;UACxBG,GAAA,CAAIkG,cAAA,CAAeL,CAAA,IAAK,CAAC/F,cAAA;UAEzBa,QAAA,CAASwF,aAAA,CAAc,CAACpG,GAAA,CAAImG,cAAA,EAAgBlG,GAAA,CAAIkG,cAAc,CAAC;UAE/D,IAAIzF,QAAA,CAAS2F,aAAA,CAAczF,QAAQ,MAAM,MAAM;YAC7C0F,UAAA,CAAWtG,GAAA,EAAKC,GAAA,EAAKwF,QAAQ;UAC9B;QACX,WAAmBD,OAAA,YAAmBe,cAAA,EAAgB;UAC5CvG,GAAA,GAAMwF,OAAA,CAAQS,EAAA;UACdhG,GAAA,GAAMuF,OAAA,CAAQU,EAAA;UACdhG,GAAA,GAAMsF,OAAA,CAAQgB,EAAA;UAEd,IAAIxG,GAAA,CAAImG,cAAA,CAAeM,CAAA,GAAI,MAAMzG,GAAA,CAAImG,cAAA,CAAeM,CAAA,GAAI,GAAG;UAC3D,IAAIxG,GAAA,CAAIkG,cAAA,CAAeM,CAAA,GAAI,MAAMxG,GAAA,CAAIkG,cAAA,CAAeM,CAAA,GAAI,GAAG;UAC3D,IAAIvG,GAAA,CAAIiG,cAAA,CAAeM,CAAA,GAAI,MAAMvG,GAAA,CAAIiG,cAAA,CAAeM,CAAA,GAAI,GAAG;UAE3DzG,GAAA,CAAImG,cAAA,CAAeN,CAAA,IAAK/F,aAAA;UACxBE,GAAA,CAAImG,cAAA,CAAeL,CAAA,IAAK,CAAC/F,cAAA;UACzBE,GAAA,CAAIkG,cAAA,CAAeN,CAAA,IAAK/F,aAAA;UACxBG,GAAA,CAAIkG,cAAA,CAAeL,CAAA,IAAK,CAAC/F,cAAA;UACzBG,GAAA,CAAIiG,cAAA,CAAeN,CAAA,IAAK/F,aAAA;UACxBI,GAAA,CAAIiG,cAAA,CAAeL,CAAA,IAAK,CAAC/F,cAAA;UAEzB,IAAI,KAAKwC,QAAA,GAAW,GAAG;YACrBmE,MAAA,CAAO1G,GAAA,CAAImG,cAAA,EAAgBlG,GAAA,CAAIkG,cAAA,EAAgB,KAAK5D,QAAQ;YAC5DmE,MAAA,CAAOzG,GAAA,CAAIkG,cAAA,EAAgBjG,GAAA,CAAIiG,cAAA,EAAgB,KAAK5D,QAAQ;YAC5DmE,MAAA,CAAOxG,GAAA,CAAIiG,cAAA,EAAgBnG,GAAA,CAAImG,cAAA,EAAgB,KAAK5D,QAAQ;UAC7D;UAED3B,QAAA,CAASwF,aAAA,CAAc,CAACpG,GAAA,CAAImG,cAAA,EAAgBlG,GAAA,CAAIkG,cAAA,EAAgBjG,GAAA,CAAIiG,cAAc,CAAC;UAEnF,IAAIzF,QAAA,CAAS2F,aAAA,CAAczF,QAAQ,MAAM,MAAM;YAC7C+F,WAAA,CAAY3G,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKsF,OAAA,EAASC,QAAQ;UAC7C;QACF;MACF;MAEDmB,SAAA,CAAW;MAEXtC,KAAA,CAAMuC,eAAA,CAAgB,UAAUC,MAAA,EAAQ;QACtC,IAAIA,MAAA,CAAOvH,WAAA,EAAa;UACtB6B,QAAA,CAAS2F,qBAAA,CAAsBD,MAAA,CAAOE,WAAW;UACjD5F,QAAA,CAAS6F,YAAA,CAAarF,qBAAqB;UAE3C,IAAIR,QAAA,CAASqF,CAAA,GAAI,MAAMrF,QAAA,CAASqF,CAAA,GAAI,GAAG;UAEvC,MAAMZ,CAAA,GAAIzE,QAAA,CAASyE,CAAA,GAAI/F,aAAA;UACvB,MAAMgG,CAAA,GAAI,CAAC1E,QAAA,CAAS0E,CAAA,GAAI/F,cAAA;UAExB,MAAMT,IAAA,GAAOwH,MAAA,CAAOxH,IAAA;UACpBA,IAAA,CAAK+D,YAAA,CAAa,aAAa,eAAewC,CAAA,GAAI,MAAMC,CAAA,GAAI,GAAG;UAE/D9D,IAAA,CAAKkF,WAAA,CAAY5H,IAAI;QACtB;MACT,CAAO;IACF;IAED,SAAS+F,gBAAgBF,MAAA,EAAQ;MAC/BnE,aAAA,CAAcmG,MAAA,CAAO,GAAG,GAAG,CAAC;MAC5BlG,kBAAA,CAAmBkG,MAAA,CAAO,GAAG,GAAG,CAAC;MACjCjG,YAAA,CAAaiG,MAAA,CAAO,GAAG,GAAG,CAAC;MAE3B,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKlC,MAAA,CAAOtB,MAAA,EAAQuD,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/C,MAAME,KAAA,GAAQnC,MAAA,CAAOiC,CAAC;QACtB,MAAMG,UAAA,GAAaD,KAAA,CAAMvE,KAAA;QAEzB,IAAIuE,KAAA,CAAME,cAAA,EAAgB;UACxBxG,aAAA,CAAcyG,CAAA,IAAKF,UAAA,CAAWE,CAAA;UAC9BzG,aAAA,CAAc0G,CAAA,IAAKH,UAAA,CAAWG,CAAA;UAC9B1G,aAAA,CAAc2G,CAAA,IAAKJ,UAAA,CAAWI,CAAA;QACxC,WAAmBL,KAAA,CAAMM,kBAAA,EAAoB;UACnC3G,kBAAA,CAAmBwG,CAAA,IAAKF,UAAA,CAAWE,CAAA;UACnCxG,kBAAA,CAAmByG,CAAA,IAAKH,UAAA,CAAWG,CAAA;UACnCzG,kBAAA,CAAmB0G,CAAA,IAAKJ,UAAA,CAAWI,CAAA;QAC7C,WAAmBL,KAAA,CAAMO,YAAA,EAAc;UAC7B3G,YAAA,CAAauG,CAAA,IAAKF,UAAA,CAAWE,CAAA;UAC7BvG,YAAA,CAAawG,CAAA,IAAKH,UAAA,CAAWG,CAAA;UAC7BxG,YAAA,CAAayG,CAAA,IAAKJ,UAAA,CAAWI,CAAA;QAC9B;MACF;IACF;IAED,SAASG,eAAe3C,MAAA,EAAQ4C,QAAA,EAAUC,MAAA,EAAQjF,KAAA,EAAO;MACvD,SAASqE,CAAA,GAAI,GAAGC,EAAA,GAAKlC,MAAA,CAAOtB,MAAA,EAAQuD,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/C,MAAME,KAAA,GAAQnC,MAAA,CAAOiC,CAAC;QACtB,MAAMG,UAAA,GAAaD,KAAA,CAAMvE,KAAA;QAEzB,IAAIuE,KAAA,CAAMM,kBAAA,EAAoB;UAC5B,MAAMK,aAAA,GAAgB7G,QAAA,CAAS2F,qBAAA,CAAsBO,KAAA,CAAMN,WAAW,EAAEkB,SAAA,CAAW;UAEnF,IAAIC,MAAA,GAASH,MAAA,CAAOI,GAAA,CAAIH,aAAa;UAErC,IAAIE,MAAA,IAAU,GAAG;UAEjBA,MAAA,IAAUb,KAAA,CAAMe,SAAA;UAEhBtF,KAAA,CAAM0E,CAAA,IAAKF,UAAA,CAAWE,CAAA,GAAIU,MAAA;UAC1BpF,KAAA,CAAM2E,CAAA,IAAKH,UAAA,CAAWG,CAAA,GAAIS,MAAA;UAC1BpF,KAAA,CAAM4E,CAAA,IAAKJ,UAAA,CAAWI,CAAA,GAAIQ,MAAA;QACpC,WAAmBb,KAAA,CAAMO,YAAA,EAAc;UAC7B,MAAMI,aAAA,GAAgB7G,QAAA,CAAS2F,qBAAA,CAAsBO,KAAA,CAAMN,WAAW;UAEtE,IAAImB,MAAA,GAASH,MAAA,CAAOI,GAAA,CAAIhH,QAAA,CAASkH,UAAA,CAAWL,aAAA,EAAeF,QAAQ,EAAEG,SAAA,EAAW;UAEhF,IAAIC,MAAA,IAAU,GAAG;UAEjBA,MAAA,IAAUb,KAAA,CAAMiB,QAAA,IAAY,IAAI,IAAI,IAAIC,IAAA,CAAKlF,GAAA,CAAIyE,QAAA,CAASU,UAAA,CAAWR,aAAa,IAAIX,KAAA,CAAMiB,QAAA,EAAU,CAAC;UAEvG,IAAIJ,MAAA,IAAU,GAAG;UAEjBA,MAAA,IAAUb,KAAA,CAAMe,SAAA;UAEhBtF,KAAA,CAAM0E,CAAA,IAAKF,UAAA,CAAWE,CAAA,GAAIU,MAAA;UAC1BpF,KAAA,CAAM2E,CAAA,IAAKH,UAAA,CAAWG,CAAA,GAAIS,MAAA;UAC1BpF,KAAA,CAAM4E,CAAA,IAAKJ,UAAA,CAAWI,CAAA,GAAIQ,MAAA;QAC3B;MACF;IACF;IAED,SAASpC,aAAaE,EAAA,EAAIT,OAAA,EAASC,QAAA,EAAU;MAC3C,IAAIiD,MAAA,GAASlD,OAAA,CAAQmD,KAAA,CAAM9C,CAAA,GAAI/F,aAAA;MAC/B,IAAI8I,MAAA,GAASpD,OAAA,CAAQmD,KAAA,CAAM7C,CAAA,GAAI/F,cAAA;MAE/B,IAAI0F,QAAA,CAASoD,gBAAA,EAAkB;QAC7BH,MAAA,IAAUjD,QAAA,CAASqD,IAAA;QACnBF,MAAA,IAAUnD,QAAA,CAASqD,IAAA;MACpB;MAED,MAAMC,IAAA,GACJ,MACAhF,OAAA,CAAQkC,EAAA,CAAGJ,CAAA,GAAI6C,MAAA,GAAS,GAAG,IAC3B,MACA3E,OAAA,CAAQkC,EAAA,CAAGH,CAAA,GAAI8C,MAAA,GAAS,GAAG,IAC3B,MACA7E,OAAA,CAAQ2E,MAAM,IACd,MACA3E,OAAA,CAAQ6E,MAAM,IACd,MACA7E,OAAA,CAAQ,CAAC2E,MAAM,IACf;MACF,IAAIvE,KAAA,GAAQ;MAEZ,IAAIsB,QAAA,CAASuD,gBAAA,IAAoBvD,QAAA,CAASoD,gBAAA,EAAkB;QAC1D1E,KAAA,GAAQ,UAAUsB,QAAA,CAAS1C,KAAA,CAAMsB,QAAA,KAAa,mBAAmBoB,QAAA,CAASC,OAAA;MAC3E;MAEDuD,OAAA,CAAQ9E,KAAA,EAAO4E,IAAI;IACpB;IAED,SAASzC,WAAWL,EAAA,EAAIC,EAAA,EAAIT,QAAA,EAAU;MACpC,MAAMsD,IAAA,GACJ,MACAhF,OAAA,CAAQkC,EAAA,CAAGE,cAAA,CAAeN,CAAC,IAC3B,MACA9B,OAAA,CAAQkC,EAAA,CAAGE,cAAA,CAAeL,CAAC,IAC3B,MACA/B,OAAA,CAAQmC,EAAA,CAAGC,cAAA,CAAeN,CAAC,IAC3B,MACA9B,OAAA,CAAQmC,EAAA,CAAGC,cAAA,CAAeL,CAAC;MAE7B,IAAIL,QAAA,CAASyD,mBAAA,EAAqB;QAChC,IAAI/E,KAAA,GACF,sBACAsB,QAAA,CAAS1C,KAAA,CAAMsB,QAAA,CAAU,IACzB,qBACAoB,QAAA,CAASC,OAAA,GACT,mBACAD,QAAA,CAAS0D,SAAA,GACT,qBACA1D,QAAA,CAAS2D,OAAA;QAEX,IAAI3D,QAAA,CAAS4D,oBAAA,EAAsB;UACjClF,KAAA,GAAQA,KAAA,GAAQ,uBAAuBsB,QAAA,CAAS6D,QAAA,GAAW,MAAM7D,QAAA,CAAS8D,OAAA;QAC3E;QAEDN,OAAA,CAAQ9E,KAAA,EAAO4E,IAAI;MACpB;IACF;IAED,SAASpC,YAAYV,EAAA,EAAIC,EAAA,EAAIM,EAAA,EAAIhB,OAAA,EAASC,QAAA,EAAU;MAClDhF,KAAA,CAAM+B,IAAA,CAAKC,MAAA,CAAOC,QAAA,IAAY;MAC9BjC,KAAA,CAAM+B,IAAA,CAAKC,MAAA,CAAOE,KAAA;MAElB,MAAMoG,IAAA,GACJ,MACAhF,OAAA,CAAQkC,EAAA,CAAGE,cAAA,CAAeN,CAAC,IAC3B,MACA9B,OAAA,CAAQkC,EAAA,CAAGE,cAAA,CAAeL,CAAC,IAC3B,MACA/B,OAAA,CAAQmC,EAAA,CAAGC,cAAA,CAAeN,CAAC,IAC3B,MACA9B,OAAA,CAAQmC,EAAA,CAAGC,cAAA,CAAeL,CAAC,IAC3B,MACA/B,OAAA,CAAQyC,EAAA,CAAGL,cAAA,CAAeN,CAAC,IAC3B,MACA9B,OAAA,CAAQyC,EAAA,CAAGL,cAAA,CAAeL,CAAC,IAC3B;MACF,IAAI3B,KAAA,GAAQ;MAEZ,IAAIsB,QAAA,CAAS+D,mBAAA,EAAqB;QAChC3I,MAAA,CAAOgE,IAAA,CAAKY,QAAA,CAAS1C,KAAK;QAE1B,IAAI0C,QAAA,CAASgE,YAAA,EAAc;UACzB5I,MAAA,CAAO6I,QAAA,CAASlE,OAAA,CAAQzC,KAAK;QAC9B;MACT,WAAiB0C,QAAA,CAASkE,qBAAA,IAAyBlE,QAAA,CAASmE,mBAAA,IAAuBnE,QAAA,CAASoE,sBAAA,EAAwB;QAC5G9I,aAAA,CAAc8D,IAAA,CAAKY,QAAA,CAAS1C,KAAK;QAEjC,IAAI0C,QAAA,CAASgE,YAAA,EAAc;UACzB1I,aAAA,CAAc2I,QAAA,CAASlE,OAAA,CAAQzC,KAAK;QACrC;QAEDlC,MAAA,CAAOgE,IAAA,CAAK7D,aAAa;QAEzBM,SAAA,CAAUuD,IAAA,CAAKoB,EAAA,CAAG6D,aAAa,EAAEC,GAAA,CAAI7D,EAAA,CAAG4D,aAAa,EAAEC,GAAA,CAAIvD,EAAA,CAAGsD,aAAa,EAAEE,YAAA,CAAa,CAAC;QAE3FlC,cAAA,CAAenI,OAAA,EAAS2B,SAAA,EAAWkE,OAAA,CAAQyE,WAAA,EAAapJ,MAAM;QAE9DA,MAAA,CAAO6I,QAAA,CAAS3I,aAAa,EAAEgJ,GAAA,CAAItE,QAAA,CAASyE,QAAQ;MAC5D,WAAiBzE,QAAA,CAAS0E,oBAAA,EAAsB;QACxC5I,OAAA,CAAQsD,IAAA,CAAKW,OAAA,CAAQyE,WAAW,EAAEG,YAAA,CAAa5I,iBAAiB,EAAE0G,SAAA,CAAW;QAE7ErH,MAAA,CAAOsG,MAAA,CAAO5F,OAAA,CAAQsE,CAAA,EAAGtE,OAAA,CAAQuE,CAAA,EAAGvE,OAAA,CAAQkF,CAAC,EAAE4D,cAAA,CAAe,GAAG,EAAEC,SAAA,CAAU,GAAG;MACjF;MAED,IAAI7E,QAAA,CAAS8E,SAAA,EAAW;QACtBpG,KAAA,GACE,sBACAtD,MAAA,CAAOwD,QAAA,CAAU,IACjB,qBACAoB,QAAA,CAASC,OAAA,GACT,mBACAD,QAAA,CAAS+E,kBAAA,GACT,qBACA/E,QAAA,CAASgF,gBAAA,GACT,sBACAhF,QAAA,CAASiF,iBAAA;MACnB,OAAa;QACLvG,KAAA,GAAQ,UAAUtD,MAAA,CAAOwD,QAAA,CAAQ,IAAK,mBAAmBoB,QAAA,CAASC,OAAA;MACnE;MAEDuD,OAAA,CAAQ9E,KAAA,EAAO4E,IAAI;IACpB;IAID,SAASrC,OAAOT,EAAA,EAAIC,EAAA,EAAIyE,MAAA,EAAQ;MAC9B,IAAI9E,CAAA,GAAIK,EAAA,CAAGL,CAAA,GAAII,EAAA,CAAGJ,CAAA;QAChBC,CAAA,GAAII,EAAA,CAAGJ,CAAA,GAAIG,EAAA,CAAGH,CAAA;MAChB,MAAM8E,GAAA,GAAM/E,CAAA,GAAIA,CAAA,GAAIC,CAAA,GAAIA,CAAA;MAExB,IAAI8E,GAAA,KAAQ,GAAG;MAEf,MAAMC,IAAA,GAAOF,MAAA,GAASnC,IAAA,CAAKsC,IAAA,CAAKF,GAAG;MAEnC/E,CAAA,IAAKgF,IAAA;MACL/E,CAAA,IAAK+E,IAAA;MAEL3E,EAAA,CAAGL,CAAA,IAAKA,CAAA;MACRK,EAAA,CAAGJ,CAAA,IAAKA,CAAA;MACRG,EAAA,CAAGJ,CAAA,IAAKA,CAAA;MACRI,EAAA,CAAGH,CAAA,IAAKA,CAAA;IACT;IAED,SAASmD,QAAQ9E,KAAA,EAAO4E,IAAA,EAAM;MAC5B,IAAIvI,aAAA,KAAkB2D,KAAA,EAAO;QAC3B5D,YAAA,IAAgBwI,IAAA;MACxB,OAAa;QACLnC,SAAA,CAAW;QAEXpG,aAAA,GAAgB2D,KAAA;QAChB5D,YAAA,GAAewI,IAAA;MAChB;IACF;IAED,SAASnC,UAAA,EAAY;MACnB,IAAIrG,YAAA,EAAc;QAChBJ,QAAA,GAAW4K,WAAA,CAAY3K,UAAA,EAAY;QACnCD,QAAA,CAASkD,YAAA,CAAa,KAAK9C,YAAY;QACvCJ,QAAA,CAASkD,YAAA,CAAa,SAAS7C,aAAa;QAC5CwB,IAAA,CAAKkF,WAAA,CAAY/G,QAAQ;MAC1B;MAEDI,YAAA,GAAe;MACfC,aAAA,GAAgB;IACjB;IAED,SAASuK,YAAYC,EAAA,EAAI;MACvB,IAAInJ,YAAA,CAAamJ,EAAE,KAAK,MAAM;QAC5BnJ,YAAA,CAAamJ,EAAE,IAAI/I,QAAA,CAASC,eAAA,CAAgB,8BAA8B,MAAM;QAEhF,IAAI5B,QAAA,IAAY,GAAG;UACjBuB,YAAA,CAAamJ,EAAE,EAAE3H,YAAA,CAAa,mBAAmB,YAAY;QAC9D;QAED,OAAOxB,YAAA,CAAamJ,EAAE;MACvB;MAED,OAAOnJ,YAAA,CAAamJ,EAAE;IACvB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}