{"ast": null, "code": "/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { RGBAFormat, LinearFilter, ClampToEdgeWrapping, Scene, OrthographicCamera, HalfFloatType, FloatType, Mesh, PlaneGeometry, WebGLRenderTarget, UVMapping, WebGLRenderer, DataTexture, LinearSRGBColorSpace, ShaderMaterial, Texture, IntType, ShortType, ByteType, UnsignedIntType, UnsignedByteType, MeshBasicMaterial } from 'three';\nconst getBufferForType = (type, width, height) => {\n  let out;\n  switch (type) {\n    case UnsignedByteType:\n      out = new Uint8ClampedArray(width * height * 4);\n      break;\n    case HalfFloatType:\n      out = new Uint16Array(width * height * 4);\n      break;\n    case UnsignedIntType:\n      out = new Uint32Array(width * height * 4);\n      break;\n    case ByteType:\n      out = new Int8Array(width * height * 4);\n      break;\n    case ShortType:\n      out = new Int16Array(width * height * 4);\n      break;\n    case IntType:\n      out = new Int32Array(width * height * 4);\n      break;\n    case FloatType:\n      out = new Float32Array(width * height * 4);\n      break;\n    default:\n      throw new Error('Unsupported data type');\n  }\n  return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n  if (_canReadPixelsResult !== undefined) return _canReadPixelsResult;\n  const testRT = new WebGLRenderTarget(1, 1, renderTargetOptions);\n  renderer.setRenderTarget(testRT);\n  const mesh = new Mesh(new PlaneGeometry(), new MeshBasicMaterial({\n    color: 0xffffff\n  }));\n  renderer.render(mesh, camera);\n  renderer.setRenderTarget(null);\n  const out = getBufferForType(type, testRT.width, testRT.height);\n  renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n  testRT.dispose();\n  mesh.geometry.dispose();\n  mesh.material.dispose();\n  _canReadPixelsResult = out[0] !== 0;\n  return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n  /**\n   * Constructs a new QuadRenderer\n   *\n   * @param options Parameters for this QuadRenderer\n   */\n  constructor(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n    this._rendererIsDisposable = false;\n    this._supportsReadPixels = true;\n    /**\n     * Renders the input texture using the specified material\n     */\n    this.render = () => {\n      this._renderer.setRenderTarget(this._renderTarget);\n      try {\n        this._renderer.render(this._scene, this._camera);\n      } catch (e) {\n        this._renderer.setRenderTarget(null);\n        throw e;\n      }\n      this._renderer.setRenderTarget(null);\n    };\n    this._width = options.width;\n    this._height = options.height;\n    this._type = options.type;\n    this._colorSpace = options.colorSpace;\n    const rtOptions = {\n      // fixed options\n      format: RGBAFormat,\n      depthBuffer: false,\n      stencilBuffer: false,\n      // user options\n      type: this._type,\n      // set in class property\n      colorSpace: this._colorSpace,\n      // set in class property\n      anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n      generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n      magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : LinearFilter,\n      minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : LinearFilter,\n      samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n      wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : ClampToEdgeWrapping,\n      wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : ClampToEdgeWrapping\n    };\n    this._material = options.material;\n    if (options.renderer) {\n      this._renderer = options.renderer;\n    } else {\n      this._renderer = QuadRenderer.instantiateRenderer();\n      this._rendererIsDisposable = true;\n    }\n    this._scene = new Scene();\n    this._camera = new OrthographicCamera();\n    this._camera.position.set(0, 0, 10);\n    this._camera.left = -0.5;\n    this._camera.right = 0.5;\n    this._camera.top = 0.5;\n    this._camera.bottom = -0.5;\n    this._camera.updateProjectionMatrix();\n    if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n      let alternativeType;\n      switch (this._type) {\n        case HalfFloatType:\n          alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? FloatType : undefined;\n          break;\n      }\n      if (alternativeType !== undefined) {\n        console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${FloatType}`);\n        this._type = alternativeType;\n      } else {\n        this._supportsReadPixels = false;\n        console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n      }\n    }\n    this._quad = new Mesh(new PlaneGeometry(), this._material);\n    this._quad.geometry.computeBoundingBox();\n    this._scene.add(this._quad);\n    this._renderTarget = new WebGLRenderTarget(this.width, this.height, rtOptions);\n    this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : UVMapping;\n  }\n  /**\n   * Instantiates a temporary renderer\n   *\n   * @returns\n   */\n  static instantiateRenderer() {\n    const renderer = new WebGLRenderer();\n    renderer.setSize(128, 128);\n    // renderer.outputColorSpace = SRGBColorSpace\n    // renderer.toneMapping = LinearToneMapping\n    // renderer.debug.checkShaderErrors = false\n    // this._rendererIsDisposable = true\n    return renderer;\n  }\n  /**\n   * Obtains a Buffer containing the rendered texture.\n   *\n   * @throws Error if the browser cannot read pixels from this RenderTarget type.\n   * @returns a TypedArray containing RGBA values from this renderer\n   */\n  toArray() {\n    if (!this._supportsReadPixels) throw new Error('Can\\'t read pixels in this browser');\n    const out = getBufferForType(this._type, this._width, this._height);\n    this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n    return out;\n  }\n  /**\n   * Performs a readPixel operation in the renderTarget\n   * and returns a DataTexture containing the read data\n   *\n   * @param options options\n   * @returns\n   */\n  toDataTexture(options) {\n    const returnValue = new DataTexture(\n    // fixed values\n    this.toArray(), this.width, this.height, RGBAFormat, this._type,\n    // user values\n    (options === null || options === void 0 ? void 0 : options.mapping) || UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1,\n    // fixed value\n    LinearSRGBColorSpace);\n    // set this afterwards, we can't set it in constructor\n    returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n    return returnValue;\n  }\n  /**\n   * If using a disposable renderer, it will dispose it.\n   */\n  disposeOnDemandRenderer() {\n    this._renderer.setRenderTarget(null);\n    if (this._rendererIsDisposable) {\n      this._renderer.dispose();\n      this._renderer.forceContextLoss();\n    }\n  }\n  /**\n   * Will dispose of **all** assets used by this renderer.\n   *\n   *\n   * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n   * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n   * or are otherwise done with it.\n   *\n   * @example\n   * ```js\n   * const loader = new HDRJPGLoader(renderer)\n   * const result = await loader.loadAsync('gainmap.jpeg')\n   * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n   * // DO NOT dispose the renderTarget here,\n   * // it is used directly in the material\n   * result.dispose()\n   * ```\n   *\n   * @example\n   * ```js\n   * const loader = new HDRJPGLoader(renderer)\n   * const pmremGenerator = new PMREMGenerator( renderer );\n   * const result = await loader.loadAsync('gainmap.jpeg')\n   * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n   * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n   * // renderTarget can be disposed here\n   * // because it was used to generate a PMREM texture\n   * result.dispose(true)\n   * ```\n   */\n  dispose(disposeRenderTarget) {\n    this.disposeOnDemandRenderer();\n    if (disposeRenderTarget) {\n      this.renderTarget.dispose();\n    }\n    // dispose shader material texture uniforms\n    if (this.material instanceof ShaderMaterial) {\n      Object.values(this.material.uniforms).forEach(v => {\n        if (v.value instanceof Texture) v.value.dispose();\n      });\n    }\n    // dispose other material properties\n    Object.values(this.material).forEach(value => {\n      if (value instanceof Texture) value.dispose();\n    });\n    this.material.dispose();\n    this._quad.geometry.dispose();\n  }\n  /**\n   * Width of the texture\n   */\n  get width() {\n    return this._width;\n  }\n  set width(value) {\n    this._width = value;\n    this._renderTarget.setSize(this._width, this._height);\n  }\n  /**\n   * Height of the texture\n   */\n  get height() {\n    return this._height;\n  }\n  set height(value) {\n    this._height = value;\n    this._renderTarget.setSize(this._width, this._height);\n  }\n  /**\n   * The renderer used\n   */\n  get renderer() {\n    return this._renderer;\n  }\n  /**\n   * The `WebGLRenderTarget` used.\n   */\n  get renderTarget() {\n    return this._renderTarget;\n  }\n  set renderTarget(value) {\n    this._renderTarget = value;\n    this._width = value.width;\n    this._height = value.height;\n    // this._type = value.texture.type\n  }\n  /**\n   * The `Material` used.\n   */\n  get material() {\n    return this._material;\n  }\n  /**\n   *\n   */\n  get type() {\n    return this._type;\n  }\n  get colorSpace() {\n    return this._colorSpace;\n  }\n}\nexport { QuadRenderer as Q };", "map": {"version": 3, "names": ["RGBAFormat", "LinearFilter", "ClampToEdgeWrapping", "Scene", "OrthographicCamera", "HalfFloatType", "FloatType", "<PERSON><PERSON>", "PlaneGeometry", "WebGLRenderTarget", "UVMapping", "WebGLRenderer", "DataTexture", "LinearSRGBColorSpace", "ShaderMaterial", "Texture", "IntType", "ShortType", "ByteType", "UnsignedIntType", "UnsignedByteType", "MeshBasicMaterial", "getBufferForType", "type", "width", "height", "out", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "Int8Array", "Int16Array", "Int32Array", "Float32Array", "Error", "_canReadPixelsResult", "canReadPixels", "renderer", "camera", "renderTargetOptions", "undefined", "testRT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh", "color", "render", "readRenderTargetPixels", "dispose", "geometry", "material", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "_o", "_p", "_q", "_r", "_rendererIsDisposable", "_supportsReadPixels", "_renderer", "_renderTarget", "_scene", "_camera", "e", "_width", "_height", "_type", "_colorSpace", "colorSpace", "rtOptions", "format", "depthBuffer", "stencil<PERSON>uffer", "anisotropy", "generateMipmaps", "magFilter", "minFilter", "samples", "wrapS", "wrapT", "_material", "instantiate<PERSON><PERSON><PERSON>", "position", "set", "left", "right", "top", "bottom", "updateProjectionMatrix", "alternativeType", "extensions", "has", "console", "warn", "_quad", "computeBoundingBox", "add", "texture", "mapping", "setSize", "toArray", "toDataTexture", "returnValue", "dispose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "dispose<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderTarget", "Object", "values", "uniforms", "for<PERSON>ach", "v", "value", "Q"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { RGBAFormat, LinearFilter, ClampToEdgeWrapping, Scene, OrthographicCamera, HalfFloatType, FloatType, Mesh, PlaneGeometry, WebGLRenderTarget, UVMapping, WebGLRenderer, DataTexture, LinearSRGBColorSpace, ShaderMaterial, Texture, IntType, ShortType, ByteType, UnsignedIntType, UnsignedByteType, MeshBasicMaterial } from 'three';\n\nconst getBufferForType = (type, width, height) => {\n    let out;\n    switch (type) {\n        case UnsignedByteType:\n            out = new Uint8ClampedArray(width * height * 4);\n            break;\n        case HalfFloatType:\n            out = new Uint16Array(width * height * 4);\n            break;\n        case UnsignedIntType:\n            out = new Uint32Array(width * height * 4);\n            break;\n        case ByteType:\n            out = new Int8Array(width * height * 4);\n            break;\n        case ShortType:\n            out = new Int16Array(width * height * 4);\n            break;\n        case IntType:\n            out = new Int32Array(width * height * 4);\n            break;\n        case FloatType:\n            out = new Float32Array(width * height * 4);\n            break;\n        default:\n            throw new Error('Unsupported data type');\n    }\n    return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n    if (_canReadPixelsResult !== undefined)\n        return _canReadPixelsResult;\n    const testRT = new WebGLRenderTarget(1, 1, renderTargetOptions);\n    renderer.setRenderTarget(testRT);\n    const mesh = new Mesh(new PlaneGeometry(), new MeshBasicMaterial({ color: 0xffffff }));\n    renderer.render(mesh, camera);\n    renderer.setRenderTarget(null);\n    const out = getBufferForType(type, testRT.width, testRT.height);\n    renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n    testRT.dispose();\n    mesh.geometry.dispose();\n    mesh.material.dispose();\n    _canReadPixelsResult = out[0] !== 0;\n    return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n    /**\n     * Constructs a new QuadRenderer\n     *\n     * @param options Parameters for this QuadRenderer\n     */\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n        this._rendererIsDisposable = false;\n        this._supportsReadPixels = true;\n        /**\n         * Renders the input texture using the specified material\n         */\n        this.render = () => {\n            this._renderer.setRenderTarget(this._renderTarget);\n            try {\n                this._renderer.render(this._scene, this._camera);\n            }\n            catch (e) {\n                this._renderer.setRenderTarget(null);\n                throw e;\n            }\n            this._renderer.setRenderTarget(null);\n        };\n        this._width = options.width;\n        this._height = options.height;\n        this._type = options.type;\n        this._colorSpace = options.colorSpace;\n        const rtOptions = {\n            // fixed options\n            format: RGBAFormat,\n            depthBuffer: false,\n            stencilBuffer: false,\n            // user options\n            type: this._type, // set in class property\n            colorSpace: this._colorSpace, // set in class property\n            anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n            generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n            magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : LinearFilter,\n            minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : LinearFilter,\n            samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n            wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : ClampToEdgeWrapping,\n            wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : ClampToEdgeWrapping\n        };\n        this._material = options.material;\n        if (options.renderer) {\n            this._renderer = options.renderer;\n        }\n        else {\n            this._renderer = QuadRenderer.instantiateRenderer();\n            this._rendererIsDisposable = true;\n        }\n        this._scene = new Scene();\n        this._camera = new OrthographicCamera();\n        this._camera.position.set(0, 0, 10);\n        this._camera.left = -0.5;\n        this._camera.right = 0.5;\n        this._camera.top = 0.5;\n        this._camera.bottom = -0.5;\n        this._camera.updateProjectionMatrix();\n        if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n            let alternativeType;\n            switch (this._type) {\n                case HalfFloatType:\n                    alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? FloatType : undefined;\n                    break;\n            }\n            if (alternativeType !== undefined) {\n                console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${FloatType}`);\n                this._type = alternativeType;\n            }\n            else {\n                this._supportsReadPixels = false;\n                console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n            }\n        }\n        this._quad = new Mesh(new PlaneGeometry(), this._material);\n        this._quad.geometry.computeBoundingBox();\n        this._scene.add(this._quad);\n        this._renderTarget = new WebGLRenderTarget(this.width, this.height, rtOptions);\n        this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : UVMapping;\n    }\n    /**\n     * Instantiates a temporary renderer\n     *\n     * @returns\n     */\n    static instantiateRenderer() {\n        const renderer = new WebGLRenderer();\n        renderer.setSize(128, 128);\n        // renderer.outputColorSpace = SRGBColorSpace\n        // renderer.toneMapping = LinearToneMapping\n        // renderer.debug.checkShaderErrors = false\n        // this._rendererIsDisposable = true\n        return renderer;\n    }\n    /**\n     * Obtains a Buffer containing the rendered texture.\n     *\n     * @throws Error if the browser cannot read pixels from this RenderTarget type.\n     * @returns a TypedArray containing RGBA values from this renderer\n     */\n    toArray() {\n        if (!this._supportsReadPixels)\n            throw new Error('Can\\'t read pixels in this browser');\n        const out = getBufferForType(this._type, this._width, this._height);\n        this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n        return out;\n    }\n    /**\n     * Performs a readPixel operation in the renderTarget\n     * and returns a DataTexture containing the read data\n     *\n     * @param options options\n     * @returns\n     */\n    toDataTexture(options) {\n        const returnValue = new DataTexture(\n        // fixed values\n        this.toArray(), this.width, this.height, RGBAFormat, this._type, \n        // user values\n        (options === null || options === void 0 ? void 0 : options.mapping) || UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1, \n        // fixed value\n        LinearSRGBColorSpace);\n        // set this afterwards, we can't set it in constructor\n        returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n        return returnValue;\n    }\n    /**\n     * If using a disposable renderer, it will dispose it.\n     */\n    disposeOnDemandRenderer() {\n        this._renderer.setRenderTarget(null);\n        if (this._rendererIsDisposable) {\n            this._renderer.dispose();\n            this._renderer.forceContextLoss();\n        }\n    }\n    /**\n     * Will dispose of **all** assets used by this renderer.\n     *\n     *\n     * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n     * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n     * or are otherwise done with it.\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n     * // DO NOT dispose the renderTarget here,\n     * // it is used directly in the material\n     * result.dispose()\n     * ```\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const pmremGenerator = new PMREMGenerator( renderer );\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n     * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n     * // renderTarget can be disposed here\n     * // because it was used to generate a PMREM texture\n     * result.dispose(true)\n     * ```\n     */\n    dispose(disposeRenderTarget) {\n        this.disposeOnDemandRenderer();\n        if (disposeRenderTarget) {\n            this.renderTarget.dispose();\n        }\n        // dispose shader material texture uniforms\n        if (this.material instanceof ShaderMaterial) {\n            Object.values(this.material.uniforms).forEach(v => {\n                if (v.value instanceof Texture)\n                    v.value.dispose();\n            });\n        }\n        // dispose other material properties\n        Object.values(this.material).forEach(value => {\n            if (value instanceof Texture)\n                value.dispose();\n        });\n        this.material.dispose();\n        this._quad.geometry.dispose();\n    }\n    /**\n     * Width of the texture\n     */\n    get width() { return this._width; }\n    set width(value) {\n        this._width = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * Height of the texture\n     */\n    get height() { return this._height; }\n    set height(value) {\n        this._height = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * The renderer used\n     */\n    get renderer() { return this._renderer; }\n    /**\n     * The `WebGLRenderTarget` used.\n     */\n    get renderTarget() { return this._renderTarget; }\n    set renderTarget(value) {\n        this._renderTarget = value;\n        this._width = value.width;\n        this._height = value.height;\n        // this._type = value.texture.type\n    }\n    /**\n     * The `Material` used.\n     */\n    get material() { return this._material; }\n    /**\n     *\n     */\n    get type() { return this._type; }\n    get colorSpace() { return this._colorSpace; }\n}\n\nexport { QuadRenderer as Q };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,IAAI,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,OAAO;AAE5U,MAAMC,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC9C,IAAIC,GAAG;EACP,QAAQH,IAAI;IACR,KAAKH,gBAAgB;MACjBM,GAAG,GAAG,IAAIC,iBAAiB,CAACH,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MAC/C;IACJ,KAAKpB,aAAa;MACdqB,GAAG,GAAG,IAAIE,WAAW,CAACJ,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MACzC;IACJ,KAAKN,eAAe;MAChBO,GAAG,GAAG,IAAIG,WAAW,CAACL,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MACzC;IACJ,KAAKP,QAAQ;MACTQ,GAAG,GAAG,IAAII,SAAS,CAACN,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MACvC;IACJ,KAAKR,SAAS;MACVS,GAAG,GAAG,IAAIK,UAAU,CAACP,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MACxC;IACJ,KAAKT,OAAO;MACRU,GAAG,GAAG,IAAIM,UAAU,CAACR,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MACxC;IACJ,KAAKnB,SAAS;MACVoB,GAAG,GAAG,IAAIO,YAAY,CAACT,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC;MAC1C;IACJ;MACI,MAAM,IAAIS,KAAK,CAAC,uBAAuB,CAAC;EAChD;EACA,OAAOR,GAAG;AACd,CAAC;AACD,IAAIS,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACb,IAAI,EAAEc,QAAQ,EAAEC,MAAM,EAAEC,mBAAmB,KAAK;EACnE,IAAIJ,oBAAoB,KAAKK,SAAS,EAClC,OAAOL,oBAAoB;EAC/B,MAAMM,MAAM,GAAG,IAAIhC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE8B,mBAAmB,CAAC;EAC/DF,QAAQ,CAACK,eAAe,CAACD,MAAM,CAAC;EAChC,MAAME,IAAI,GAAG,IAAIpC,IAAI,CAAC,IAAIC,aAAa,CAAC,CAAC,EAAE,IAAIa,iBAAiB,CAAC;IAAEuB,KAAK,EAAE;EAAS,CAAC,CAAC,CAAC;EACtFP,QAAQ,CAACQ,MAAM,CAACF,IAAI,EAAEL,MAAM,CAAC;EAC7BD,QAAQ,CAACK,eAAe,CAAC,IAAI,CAAC;EAC9B,MAAMhB,GAAG,GAAGJ,gBAAgB,CAACC,IAAI,EAAEkB,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;EAC/DY,QAAQ,CAACS,sBAAsB,CAACL,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEA,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,EAAEC,GAAG,CAAC;EAC/Ee,MAAM,CAACM,OAAO,CAAC,CAAC;EAChBJ,IAAI,CAACK,QAAQ,CAACD,OAAO,CAAC,CAAC;EACvBJ,IAAI,CAACM,QAAQ,CAACF,OAAO,CAAC,CAAC;EACvBZ,oBAAoB,GAAGT,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;EACnC,OAAOS,oBAAoB;AAC/B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClE,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;AACR;AACA;IACQ,IAAI,CAACzB,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC0B,SAAS,CAAC7B,eAAe,CAAC,IAAI,CAAC8B,aAAa,CAAC;MAClD,IAAI;QACA,IAAI,CAACD,SAAS,CAAC1B,MAAM,CAAC,IAAI,CAAC4B,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;MACpD,CAAC,CACD,OAAOC,CAAC,EAAE;QACN,IAAI,CAACJ,SAAS,CAAC7B,eAAe,CAAC,IAAI,CAAC;QACpC,MAAMiC,CAAC;MACX;MACA,IAAI,CAACJ,SAAS,CAAC7B,eAAe,CAAC,IAAI,CAAC;IACxC,CAAC;IACD,IAAI,CAACkC,MAAM,GAAGxB,OAAO,CAAC5B,KAAK;IAC3B,IAAI,CAACqD,OAAO,GAAGzB,OAAO,CAAC3B,MAAM;IAC7B,IAAI,CAACqD,KAAK,GAAG1B,OAAO,CAAC7B,IAAI;IACzB,IAAI,CAACwD,WAAW,GAAG3B,OAAO,CAAC4B,UAAU;IACrC,MAAMC,SAAS,GAAG;MACd;MACAC,MAAM,EAAElF,UAAU;MAClBmF,WAAW,EAAE,KAAK;MAClBC,aAAa,EAAE,KAAK;MACpB;MACA7D,IAAI,EAAE,IAAI,CAACuD,KAAK;MAAE;MAClBE,UAAU,EAAE,IAAI,CAACD,WAAW;MAAE;MAC9BM,UAAU,EAAE,CAAC,CAAChC,EAAE,GAAGD,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgC,UAAU,MAAM7C,SAAS,GAAG,CAACc,EAAE,GAAGF,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,UAAU,GAAG,CAAC;MAC7MC,eAAe,EAAE,CAAC,CAAC/B,EAAE,GAAGH,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,eAAe,MAAM9C,SAAS,GAAG,CAACgB,EAAE,GAAGJ,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,eAAe,GAAG,KAAK;MAChOC,SAAS,EAAE,CAAC,CAAC9B,EAAE,GAAGL,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,SAAS,MAAM/C,SAAS,GAAG,CAACkB,EAAE,GAAGN,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAImB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,SAAS,GAAGtF,YAAY;MACrNuF,SAAS,EAAE,CAAC,CAAC7B,EAAE,GAAGP,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,SAAS,MAAMhD,SAAS,GAAG,CAACoB,EAAE,GAAGR,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,SAAS,GAAGvF,YAAY;MACrNwF,OAAO,EAAE,CAAC,CAAC5B,EAAE,GAAGT,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIsB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,OAAO,MAAMjD,SAAS,GAAG,CAACsB,EAAE,GAAGV,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,OAAO,GAAGjD,SAAS;MAC5MkD,KAAK,EAAE,CAAC,CAAC3B,EAAE,GAAGX,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,KAAK,MAAMlD,SAAS,GAAG,CAACwB,EAAE,GAAGZ,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,KAAK,GAAGxF,mBAAmB;MAChNyF,KAAK,EAAE,CAAC,CAAC1B,EAAE,GAAGb,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,KAAK,MAAMnD,SAAS,GAAG,CAAC0B,EAAE,GAAGd,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAI2B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,KAAK,GAAGzF;IACjM,CAAC;IACD,IAAI,CAAC0F,SAAS,GAAGxC,OAAO,CAACH,QAAQ;IACjC,IAAIG,OAAO,CAACf,QAAQ,EAAE;MAClB,IAAI,CAACkC,SAAS,GAAGnB,OAAO,CAACf,QAAQ;IACrC,CAAC,MACI;MACD,IAAI,CAACkC,SAAS,GAAGrB,YAAY,CAAC2C,mBAAmB,CAAC,CAAC;MACnD,IAAI,CAACxB,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAI,CAACI,MAAM,GAAG,IAAItE,KAAK,CAAC,CAAC;IACzB,IAAI,CAACuE,OAAO,GAAG,IAAItE,kBAAkB,CAAC,CAAC;IACvC,IAAI,CAACsE,OAAO,CAACoB,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnC,IAAI,CAACrB,OAAO,CAACsB,IAAI,GAAG,CAAC,GAAG;IACxB,IAAI,CAACtB,OAAO,CAACuB,KAAK,GAAG,GAAG;IACxB,IAAI,CAACvB,OAAO,CAACwB,GAAG,GAAG,GAAG;IACtB,IAAI,CAACxB,OAAO,CAACyB,MAAM,GAAG,CAAC,GAAG;IAC1B,IAAI,CAACzB,OAAO,CAAC0B,sBAAsB,CAAC,CAAC;IACrC,IAAI,CAAChE,aAAa,CAAC,IAAI,CAAC0C,KAAK,EAAE,IAAI,CAACP,SAAS,EAAE,IAAI,CAACG,OAAO,EAAEO,SAAS,CAAC,EAAE;MACrE,IAAIoB,eAAe;MACnB,QAAQ,IAAI,CAACvB,KAAK;QACd,KAAKzE,aAAa;UACdgG,eAAe,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,UAAU,CAACC,GAAG,CAAC,wBAAwB,CAAC,GAAGjG,SAAS,GAAGkC,SAAS;UACjG;MACR;MACA,IAAI6D,eAAe,KAAK7D,SAAS,EAAE;QAC/BgE,OAAO,CAACC,IAAI,CAAC,qDAAqD,IAAI,CAAC3B,KAAK,gCAAgCxE,SAAS,EAAE,CAAC;QACxH,IAAI,CAACwE,KAAK,GAAGuB,eAAe;MAChC,CAAC,MACI;QACD,IAAI,CAAC/B,mBAAmB,GAAG,KAAK;QAChCkC,OAAO,CAACC,IAAI,CAAC,8GAA8G,CAAC;MAChI;IACJ;IACA,IAAI,CAACC,KAAK,GAAG,IAAInG,IAAI,CAAC,IAAIC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACoF,SAAS,CAAC;IAC1D,IAAI,CAACc,KAAK,CAAC1D,QAAQ,CAAC2D,kBAAkB,CAAC,CAAC;IACxC,IAAI,CAAClC,MAAM,CAACmC,GAAG,CAAC,IAAI,CAACF,KAAK,CAAC;IAC3B,IAAI,CAAClC,aAAa,GAAG,IAAI/D,iBAAiB,CAAC,IAAI,CAACe,KAAK,EAAE,IAAI,CAACC,MAAM,EAAEwD,SAAS,CAAC;IAC9E,IAAI,CAACT,aAAa,CAACqC,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC3C,EAAE,GAAGf,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,OAAO,MAAMtE,SAAS,GAAG,CAAC4B,EAAE,GAAGhB,OAAO,CAACb,mBAAmB,MAAM,IAAI,IAAI6B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0C,OAAO,GAAGpG,SAAS;EAC5O;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOmF,mBAAmBA,CAAA,EAAG;IACzB,MAAMxD,QAAQ,GAAG,IAAI1B,aAAa,CAAC,CAAC;IACpC0B,QAAQ,CAAC0E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1B;IACA;IACA;IACA;IACA,OAAO1E,QAAQ;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2E,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC1C,mBAAmB,EACzB,MAAM,IAAIpC,KAAK,CAAC,oCAAoC,CAAC;IACzD,MAAMR,GAAG,GAAGJ,gBAAgB,CAAC,IAAI,CAACwD,KAAK,EAAE,IAAI,CAACF,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;IACnE,IAAI,CAACN,SAAS,CAACzB,sBAAsB,CAAC,IAAI,CAAC0B,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACI,MAAM,EAAE,IAAI,CAACC,OAAO,EAAEnD,GAAG,CAAC;IAC/F,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuF,aAAaA,CAAC7D,OAAO,EAAE;IACnB,MAAM8D,WAAW,GAAG,IAAItG,WAAW;IACnC;IACA,IAAI,CAACoG,OAAO,CAAC,CAAC,EAAE,IAAI,CAACxF,KAAK,EAAE,IAAI,CAACC,MAAM,EAAEzB,UAAU,EAAE,IAAI,CAAC8E,KAAK;IAC/D;IACA,CAAC1B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0D,OAAO,KAAKpG,SAAS,EAAE,CAAC0C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsC,KAAK,KAAKxF,mBAAmB,EAAE,CAACkD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuC,KAAK,KAAKzF,mBAAmB,EAAE,CAACkD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmC,SAAS,KAAKtF,YAAY,EAAE,CAACmD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoC,SAAS,KAAKvF,YAAY,EAAE,CAACmD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiC,UAAU,KAAK,CAAC;IAC/f;IACAxE,oBAAoB,CAAC;IACrB;IACAqG,WAAW,CAAC5B,eAAe,GAAG,CAAClC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkC,eAAe,MAAM9C,SAAS,GAAGY,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkC,eAAe,GAAG,KAAK;IAC3M,OAAO4B,WAAW;EACtB;EACA;AACJ;AACA;EACIC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC5C,SAAS,CAAC7B,eAAe,CAAC,IAAI,CAAC;IACpC,IAAI,IAAI,CAAC2B,qBAAqB,EAAE;MAC5B,IAAI,CAACE,SAAS,CAACxB,OAAO,CAAC,CAAC;MACxB,IAAI,CAACwB,SAAS,CAAC6C,gBAAgB,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrE,OAAOA,CAACsE,mBAAmB,EAAE;IACzB,IAAI,CAACF,uBAAuB,CAAC,CAAC;IAC9B,IAAIE,mBAAmB,EAAE;MACrB,IAAI,CAACC,YAAY,CAACvE,OAAO,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,IAAI,CAACE,QAAQ,YAAYnC,cAAc,EAAE;MACzCyG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACvE,QAAQ,CAACwE,QAAQ,CAAC,CAACC,OAAO,CAACC,CAAC,IAAI;QAC/C,IAAIA,CAAC,CAACC,KAAK,YAAY7G,OAAO,EAC1B4G,CAAC,CAACC,KAAK,CAAC7E,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;IACA;IACAwE,MAAM,CAACC,MAAM,CAAC,IAAI,CAACvE,QAAQ,CAAC,CAACyE,OAAO,CAACE,KAAK,IAAI;MAC1C,IAAIA,KAAK,YAAY7G,OAAO,EACxB6G,KAAK,CAAC7E,OAAO,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACE,QAAQ,CAACF,OAAO,CAAC,CAAC;IACvB,IAAI,CAAC2D,KAAK,CAAC1D,QAAQ,CAACD,OAAO,CAAC,CAAC;EACjC;EACA;AACJ;AACA;EACI,IAAIvB,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACoD,MAAM;EAAE;EAClC,IAAIpD,KAAKA,CAACoG,KAAK,EAAE;IACb,IAAI,CAAChD,MAAM,GAAGgD,KAAK;IACnB,IAAI,CAACpD,aAAa,CAACuC,OAAO,CAAC,IAAI,CAACnC,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;EACzD;EACA;AACJ;AACA;EACI,IAAIpD,MAAMA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACoD,OAAO;EAAE;EACpC,IAAIpD,MAAMA,CAACmG,KAAK,EAAE;IACd,IAAI,CAAC/C,OAAO,GAAG+C,KAAK;IACpB,IAAI,CAACpD,aAAa,CAACuC,OAAO,CAAC,IAAI,CAACnC,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;EACzD;EACA;AACJ;AACA;EACI,IAAIxC,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACkC,SAAS;EAAE;EACxC;AACJ;AACA;EACI,IAAI+C,YAAYA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC9C,aAAa;EAAE;EAChD,IAAI8C,YAAYA,CAACM,KAAK,EAAE;IACpB,IAAI,CAACpD,aAAa,GAAGoD,KAAK;IAC1B,IAAI,CAAChD,MAAM,GAAGgD,KAAK,CAACpG,KAAK;IACzB,IAAI,CAACqD,OAAO,GAAG+C,KAAK,CAACnG,MAAM;IAC3B;EACJ;EACA;AACJ;AACA;EACI,IAAIwB,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC2C,SAAS;EAAE;EACxC;AACJ;AACA;EACI,IAAIrE,IAAIA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACuD,KAAK;EAAE;EAChC,IAAIE,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACD,WAAW;EAAE;AAChD;AAEA,SAAS7B,YAAY,IAAI2E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}