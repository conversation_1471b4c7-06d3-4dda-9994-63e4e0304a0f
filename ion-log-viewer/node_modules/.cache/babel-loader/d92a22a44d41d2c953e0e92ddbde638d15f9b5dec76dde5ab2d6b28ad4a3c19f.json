{"ast": null, "code": "import { d as doThreePointsMakeARight, a as _toConsumableArray, _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Vector3, Matrix3 } from 'three';\nimport { a as matrixSum3 } from './matrix-baa530bf.esm.js';\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n    radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n    return a.y - b.y;\n  }\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && doThreePointsMakeARight(_toConsumableArray(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && doThreePointsMakeARight(_toConsumableArray(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = _slicedToArray(_ref2, 2),\n    low1 = _ref4[0],\n    high1 = _ref4[1];\n  var _ref5 = _slicedToArray(_ref3, 2),\n    low2 = _ref5[0],\n    high2 = _ref5[1];\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n  var _final = matrixSum3(matrixSum3(i, vx), vxsquared);\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = _slicedToArray(segment, 2),\n    a = _segment[0],\n    b = _segment[1];\n  var matrix = rotateVectorOnVector(plane.normal, new Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = _slicedToArray(coords, 3),\n    ix = _coords[0],\n    iy = _coords[1],\n    iz = _coords[2];\n  var _sides = _slicedToArray(sides, 2),\n    rx = _sides[0],\n    ry = _sides[1];\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = _slicedToArray(size, 2),\n    rx = _size[0],\n    ry = _size[1];\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  repeat: repeat,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\nexport { degToRad as a, radToDeg as b, clamp as c, deltaAngle as d, fibonacciOnSphere as e, fade as f, lexicographic as g, convexHull as h, remap as i, inverseLerp as j, rotateVectorOnVector as k, lerp as l, misc as m, normalize as n, pointToCoordinate as o, pointOnCubeToPointOnSphere as p, coordinateToPoint as q, repeat as r, planeSegmentIntersection as s, pointToPlaneDistance as t, getIndexFrom3D as u, vectorEquals as v, get3DFromIndex as w, getIndexFrom2D as x, get2DFromIndex as y };", "map": {"version": 3, "names": ["d", "doThreePointsMakeARight", "a", "_toConsumableArray", "_", "_slicedToArray", "Vector3", "Matrix3", "matrixSum3", "clamp", "value", "min", "max", "Math", "repeat", "t", "length", "floor", "deltaAngle", "current", "target", "delta", "PI", "degToRad", "degrees", "radToDeg", "radians", "fibonacciOnSphere", "buffer", "_ref", "_ref$radius", "radius", "samples", "offset", "increment", "i", "y", "distance", "sqrt", "pow", "phi", "x", "cos", "z", "sin", "vectorEquals", "b", "eps", "arguments", "undefined", "Number", "EPSILON", "abs", "lexicographic", "convexHull", "_points", "points", "sort", "lUpper", "push", "slice", "splice", "l<PERSON><PERSON><PERSON>", "_i", "c", "concat", "remap", "_ref2", "_ref3", "_ref4", "low1", "high1", "_ref5", "low2", "high2", "fade", "lerp", "v0", "v1", "inverseLerp", "normalize", "m", "pointOnCubeToPointOnSphere", "x2", "y2", "z2", "nx", "ny", "nz", "rotateVectorOnVector", "v", "crossVectors", "dot", "identity", "vx", "set", "vxsquared", "multiplyMatrices", "multiplyScalar", "_final", "pointToCoordinate", "lat", "asin", "lon", "atan2", "coordinateToPoint", "r", "planeSegmentIntersection", "plane", "segment", "_segment", "matrix", "normal", "clone", "applyMatrix3", "lerpVectors", "pointToPlaneDistance", "p", "getIndexFrom3D", "coords", "sides", "_coords", "ix", "iy", "iz", "_sides", "rx", "ry", "get3DFromIndex", "index", "size", "_size", "getIndexFrom2D", "get2DFromIndex", "columns", "misc", "Object", "freeze", "__proto__", "e", "f", "g", "h", "j", "k", "l", "n", "o", "q", "s", "u", "w"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/maath/dist/misc-19a3ec46.esm.js"], "sourcesContent": ["import { d as doThreePointsMakeARight, a as _toConsumableArray, _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Vector3, Matrix3 } from 'three';\nimport { a as matrixSum3 } from './matrix-baa530bf.esm.js';\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n      radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n\n    return a.y - b.y;\n  }\n\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && doThreePointsMakeARight(_toConsumableArray(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && doThreePointsMakeARight(_toConsumableArray(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = _slicedToArray(_ref2, 2),\n      low1 = _ref4[0],\n      high1 = _ref4[1];\n\n  var _ref5 = _slicedToArray(_ref3, 2),\n      low2 = _ref5[0],\n      high2 = _ref5[1];\n\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n\n  var _final = matrixSum3(matrixSum3(i, vx), vxsquared);\n\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = _slicedToArray(segment, 2),\n      a = _segment[0],\n      b = _segment[1];\n\n  var matrix = rotateVectorOnVector(plane.normal, new Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = _slicedToArray(coords, 3),\n      ix = _coords[0],\n      iy = _coords[1],\n      iz = _coords[2];\n\n  var _sides = _slicedToArray(sides, 2),\n      rx = _sides[0],\n      ry = _sides[1];\n\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = _slicedToArray(size, 2),\n      rx = _size[0],\n      ry = _size[1];\n\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\n\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  repeat: repeat,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\n\nexport { degToRad as a, radToDeg as b, clamp as c, deltaAngle as d, fibonacciOnSphere as e, fade as f, lexicographic as g, convexHull as h, remap as i, inverseLerp as j, rotateVectorOnVector as k, lerp as l, misc as m, normalize as n, pointToCoordinate as o, pointOnCubeToPointOnSphere as p, coordinateToPoint as q, repeat as r, planeSegmentIntersection as s, pointToPlaneDistance as t, getIndexFrom3D as u, vectorEquals as v, get3DFromIndex as w, getIndexFrom2D as x, get2DFromIndex as y };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,4BAA4B;AACvH,SAASC,OAAO,EAAEC,OAAO,QAAQ,OAAO;AACxC,SAASL,CAAC,IAAIM,UAAU,QAAQ,0BAA0B;;AAE1D;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAOC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEF,KAAK,CAAC,CAAC;AAC5C,CAAC,CAAC;;AAEF,SAASI,MAAMA,CAACC,CAAC,EAAEC,MAAM,EAAE;EACzB,OAAOP,KAAK,CAACM,CAAC,GAAGF,IAAI,CAACI,KAAK,CAACF,CAAC,GAAGC,MAAM,CAAC,GAAGA,MAAM,EAAE,CAAC,EAAEA,MAAM,CAAC;AAC9D,CAAC,CAAC;;AAEF,SAASE,UAAUA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnC,IAAIC,KAAK,GAAGP,MAAM,CAACM,MAAM,GAAGD,OAAO,EAAEN,IAAI,CAACS,EAAE,GAAG,CAAC,CAAC;EACjD,IAAID,KAAK,GAAGR,IAAI,CAACS,EAAE,EAAED,KAAK,IAAIR,IAAI,CAACS,EAAE,GAAG,CAAC;EACzC,OAAOD,KAAK;AACd;AACA;AACA;AACA;;AAEA,SAASE,QAAQA,CAACC,OAAO,EAAE;EACzB,OAAOA,OAAO,GAAG,GAAG,GAAGX,IAAI,CAACS,EAAE;AAChC;AACA;AACA;AACA;;AAEA,SAASG,QAAQA,CAACC,OAAO,EAAE;EACzB,OAAOA,OAAO,GAAG,GAAG,GAAGb,IAAI,CAACS,EAAE;AAChC,CAAC,CAAC;;AAEF,SAASK,iBAAiBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACvC,IAAIC,WAAW,GAAGD,IAAI,CAACE,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;EACrD,IAAIE,OAAO,GAAGJ,MAAM,CAACZ,MAAM,GAAG,CAAC;EAC/B,IAAIiB,MAAM,GAAG,CAAC,GAAGD,OAAO;EACxB,IAAIE,SAAS,GAAGrB,IAAI,CAACS,EAAE,IAAI,CAAC,GAAG,YAAY,CAAC;EAE5C,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACZ,MAAM,EAAEmB,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIC,CAAC,GAAGD,CAAC,GAAGF,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;IACnC,IAAII,QAAQ,GAAGxB,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,IAAII,GAAG,GAAGL,CAAC,GAAGH,OAAO,GAAGE,SAAS;IACjC,IAAIO,CAAC,GAAG5B,IAAI,CAAC6B,GAAG,CAACF,GAAG,CAAC,GAAGH,QAAQ;IAChC,IAAIM,CAAC,GAAG9B,IAAI,CAAC+B,GAAG,CAACJ,GAAG,CAAC,GAAGH,QAAQ;IAChCT,MAAM,CAACO,CAAC,CAAC,GAAGM,CAAC,GAAGV,MAAM;IACtBH,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,GAAGL,MAAM;IAC1BH,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGQ,CAAC,GAAGZ,MAAM;EAC5B;AACF,CAAC,CAAC;;AAEF,SAASc,YAAYA,CAAC3C,CAAC,EAAE4C,CAAC,EAAE;EAC1B,IAAIC,GAAG,GAAGC,SAAS,CAAChC,MAAM,GAAG,CAAC,IAAIgC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACC,OAAO;EAC5F,OAAOtC,IAAI,CAACuC,GAAG,CAAClD,CAAC,CAACuC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC,GAAGM,GAAG,IAAIlC,IAAI,CAACuC,GAAG,CAAClD,CAAC,CAACkC,CAAC,GAAGU,CAAC,CAACV,CAAC,CAAC,GAAGW,GAAG,IAAIlC,IAAI,CAACuC,GAAG,CAAClD,CAAC,CAACyC,CAAC,GAAGG,CAAC,CAACH,CAAC,CAAC,GAAGI,GAAG;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASM,aAAaA,CAACnD,CAAC,EAAE4C,CAAC,EAAE;EAC3B,IAAI5C,CAAC,CAACuC,CAAC,KAAKK,CAAC,CAACL,CAAC,EAAE;IACf;IACA;IACA,IAAI,OAAOvC,CAAC,CAACyC,CAAC,KAAK,WAAW,EAAE;MAC9B,IAAIzC,CAAC,CAACkC,CAAC,KAAKU,CAAC,CAACV,CAAC,EAAE;QACf,OAAOlC,CAAC,CAACyC,CAAC,GAAGG,CAAC,CAACH,CAAC;MAClB;IACF;IAEA,OAAOzC,CAAC,CAACkC,CAAC,GAAGU,CAAC,CAACV,CAAC;EAClB;EAEA,OAAOlC,CAAC,CAACuC,CAAC,GAAGK,CAAC,CAACL,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASa,UAAUA,CAACC,OAAO,EAAE;EAC3B,IAAIC,MAAM,GAAGD,OAAO,CAACE,IAAI,CAACJ,aAAa,CAAC,CAAC,CAAC;;EAG1C,IAAIK,MAAM,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErC,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,MAAM,CAACxC,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACtCuB,MAAM,CAACC,IAAI,CAACH,MAAM,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExB,OAAOuB,MAAM,CAAC1C,MAAM,GAAG,CAAC,IAAIf,uBAAuB,CAACE,kBAAkB,CAACuD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzF;MACAF,MAAM,CAACG,MAAM,CAACH,MAAM,CAAC1C,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;;EAGF,IAAI8C,MAAM,GAAG,CAACN,MAAM,CAACA,MAAM,CAACxC,MAAM,GAAG,CAAC,CAAC,EAAEwC,MAAM,CAACA,MAAM,CAACxC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErE,KAAK,IAAI+C,EAAE,GAAGP,MAAM,CAACxC,MAAM,GAAG,CAAC,EAAE+C,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;IAC9C;IACAD,MAAM,CAACH,IAAI,CAACH,MAAM,CAACO,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEzB,OAAOD,MAAM,CAAC9C,MAAM,GAAG,CAAC,IAAIf,uBAAuB,CAACE,kBAAkB,CAAC2D,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzF;MACAE,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC9C,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;;EAGF8C,MAAM,CAACD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACnBC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC9C,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAErC,IAAIgD,CAAC,GAAG,EAAE,CAACC,MAAM,CAACP,MAAM,EAAEI,MAAM,CAAC;EACjC,OAAOE,CAAC;AACV;AACA,SAASE,KAAKA,CAACzB,CAAC,EAAE0B,KAAK,EAAEC,KAAK,EAAE;EAC9B,IAAIC,KAAK,GAAGhE,cAAc,CAAC8D,KAAK,EAAE,CAAC,CAAC;IAChCG,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;IACfE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC;EAEpB,IAAIG,KAAK,GAAGnE,cAAc,CAAC+D,KAAK,EAAE,CAAC,CAAC;IAChCK,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;IACfE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC;EAEpB,OAAOC,IAAI,GAAG,CAAChC,CAAC,GAAG6B,IAAI,KAAKI,KAAK,GAAGD,IAAI,CAAC,IAAIF,KAAK,GAAGD,IAAI,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,IAAIA,CAAC5D,CAAC,EAAE;EACf,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC,IAAIA,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6D,IAAIA,CAACC,EAAE,EAAEC,EAAE,EAAE/D,CAAC,EAAE;EACvB,OAAO8D,EAAE,IAAI,CAAC,GAAG9D,CAAC,CAAC,GAAG+D,EAAE,GAAG/D,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASgE,WAAWA,CAACF,EAAE,EAAEC,EAAE,EAAE/D,CAAC,EAAE;EAC9B,OAAO,CAACA,CAAC,GAAG8D,EAAE,KAAKC,EAAE,GAAGD,EAAE,CAAC;AAC7B;AACA;AACA;AACA;;AAEA,SAASG,SAASA,CAACvC,CAAC,EAAEL,CAAC,EAAEO,CAAC,EAAE;EAC1B,IAAIsC,CAAC,GAAGpE,IAAI,CAACyB,IAAI,CAACG,CAAC,GAAGA,CAAC,GAAGL,CAAC,GAAGA,CAAC,GAAGO,CAAC,GAAGA,CAAC,CAAC;EACxC,OAAO,CAACF,CAAC,GAAGwC,CAAC,EAAE7C,CAAC,GAAG6C,CAAC,EAAEtC,CAAC,GAAGsC,CAAC,CAAC;AAC9B;AACA;AACA;AACA;;AAEA,SAASC,0BAA0BA,CAACzC,CAAC,EAAEL,CAAC,EAAEO,CAAC,EAAE;EAC3C,IAAIwC,EAAE,GAAG1C,CAAC,GAAGA,CAAC;EACd,IAAI2C,EAAE,GAAGhD,CAAC,GAAGA,CAAC;EACd,IAAIiD,EAAE,GAAG1C,CAAC,GAAGA,CAAC;EACd,IAAI2C,EAAE,GAAG7C,CAAC,GAAG5B,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAAC8C,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC;EACvD,IAAIE,EAAE,GAAGnD,CAAC,GAAGvB,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAAC+C,EAAE,GAAGF,EAAE,IAAI,CAAC,GAAGE,EAAE,GAAGF,EAAE,GAAG,CAAC,CAAC;EACvD,IAAIK,EAAE,GAAG7C,CAAC,GAAG9B,IAAI,CAACyB,IAAI,CAAC,CAAC,GAAG,CAAC6C,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC;EACvD,OAAO,CAACE,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AACrB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;;AAEA,SAASC,oBAAoBA,CAACvF,CAAC,EAAE4C,CAAC,EAAE;EAClC,IAAI4C,CAAC,GAAG,IAAIpF,OAAO,CAAC,CAAC,CAACqF,YAAY,CAACzF,CAAC,EAAE4C,CAAC,CAAC;EACxC,IAAIkB,CAAC,GAAG9D,CAAC,CAAC0F,GAAG,CAAC9C,CAAC,CAAC;EAChB,IAAIX,CAAC,GAAG,IAAI5B,OAAO,CAAC,CAAC,CAACsF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEA,IAAIC,EAAE,GAAG,IAAIvF,OAAO,CAAC,CAAC,CAACwF,GAAG,CAAC,CAAC,EAAE,CAACL,CAAC,CAAC/C,CAAC,EAAE+C,CAAC,CAACtD,CAAC,EAAEsD,CAAC,CAAC/C,CAAC,EAAE,CAAC,EAAE,CAAC+C,CAAC,CAACjD,CAAC,EAAE,CAACiD,CAAC,CAACtD,CAAC,EAAEsD,CAAC,CAACjD,CAAC,EAAE,CAAC,CAAC;EACpE,IAAIuD,SAAS,GAAG,IAAIzF,OAAO,CAAC,CAAC,CAAC0F,gBAAgB,CAACH,EAAE,EAAEA,EAAE,CAAC,CAACI,cAAc,CAAC,CAAC,IAAI,CAAC,GAAGlC,CAAC,CAAC,CAAC;EAElF,IAAImC,MAAM,GAAG3F,UAAU,CAACA,UAAU,CAAC2B,CAAC,EAAE2D,EAAE,CAAC,EAAEE,SAAS,CAAC;EAErD,OAAOG,MAAM;AACf,CAAC,CAAC;;AAEF,SAASC,iBAAiBA,CAAC3D,CAAC,EAAEL,CAAC,EAAEO,CAAC,EAAE;EAClC,IAAI0D,GAAG,GAAGxF,IAAI,CAACyF,IAAI,CAAClE,CAAC,CAAC;EACtB,IAAImE,GAAG,GAAG1F,IAAI,CAAC2F,KAAK,CAAC/D,CAAC,EAAE,CAACE,CAAC,CAAC;EAC3B,OAAO,CAAC0D,GAAG,EAAEE,GAAG,CAAC;AACnB,CAAC,CAAC;;AAEF,SAASE,iBAAiBA,CAACJ,GAAG,EAAEE,GAAG,EAAE;EACnC,IAAInE,CAAC,GAAGvB,IAAI,CAAC+B,GAAG,CAACyD,GAAG,CAAC;EACrB,IAAIK,CAAC,GAAG7F,IAAI,CAAC6B,GAAG,CAAC2D,GAAG,CAAC;EACrB,IAAI5D,CAAC,GAAG5B,IAAI,CAAC+B,GAAG,CAAC2D,GAAG,CAAC,GAAGG,CAAC;EACzB,IAAI/D,CAAC,GAAG,CAAC9B,IAAI,CAAC6B,GAAG,CAAC6D,GAAG,CAAC,GAAGG,CAAC;EAC1B,OAAO,CAACjE,CAAC,EAAEL,CAAC,EAAEO,CAAC,CAAC;AAClB;AACA;AACA;AACA;;AAEA,SAASgE,wBAAwBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAChD,IAAIC,QAAQ,GAAGzG,cAAc,CAACwG,OAAO,EAAE,CAAC,CAAC;IACrC3G,CAAC,GAAG4G,QAAQ,CAAC,CAAC,CAAC;IACfhE,CAAC,GAAGgE,QAAQ,CAAC,CAAC,CAAC;EAEnB,IAAIC,MAAM,GAAGtB,oBAAoB,CAACmB,KAAK,CAACI,MAAM,EAAE,IAAI1G,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACrE,IAAIS,CAAC,GAAGgE,WAAW,CAAC7E,CAAC,CAAC+G,KAAK,CAAC,CAAC,CAACC,YAAY,CAACH,MAAM,CAAC,CAAC3E,CAAC,EAAEU,CAAC,CAACmE,KAAK,CAAC,CAAC,CAACC,YAAY,CAACH,MAAM,CAAC,CAAC3E,CAAC,EAAE,CAAC,CAAC;EAC1F,OAAO,IAAI9B,OAAO,CAAC,CAAC,CAAC6G,WAAW,CAACjH,CAAC,EAAE4C,CAAC,EAAE/B,CAAC,CAAC;AAC3C;AACA;AACA;AACA;;AAEA,SAASqG,oBAAoBA,CAACC,CAAC,EAAET,KAAK,EAAE;EACtC,IAAI5G,CAAC,GAAG4G,KAAK,CAACI,MAAM,CAACpB,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC;;EAE7B,OAAOrH,CAAC;AACV;AACA,SAASsH,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAIC,OAAO,GAAGpH,cAAc,CAACkH,MAAM,EAAE,CAAC,CAAC;IACnCG,EAAE,GAAGD,OAAO,CAAC,CAAC,CAAC;IACfE,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC;IACfG,EAAE,GAAGH,OAAO,CAAC,CAAC,CAAC;EAEnB,IAAII,MAAM,GAAGxH,cAAc,CAACmH,KAAK,EAAE,CAAC,CAAC;IACjCM,EAAE,GAAGD,MAAM,CAAC,CAAC,CAAC;IACdE,EAAE,GAAGF,MAAM,CAAC,CAAC,CAAC;EAElB,OAAOD,EAAE,GAAGE,EAAE,GAAGC,EAAE,GAAGJ,EAAE,GAAGG,EAAE,GAAGJ,EAAE;AACpC;AACA,SAASM,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACnC,IAAIC,KAAK,GAAG9H,cAAc,CAAC6H,IAAI,EAAE,CAAC,CAAC;IAC/BJ,EAAE,GAAGK,KAAK,CAAC,CAAC,CAAC;IACbJ,EAAE,GAAGI,KAAK,CAAC,CAAC,CAAC;EAEjB,IAAIjI,CAAC,GAAG4H,EAAE,GAAGC,EAAE;EACf,IAAIpF,CAAC,GAAGsF,KAAK,GAAG/H,CAAC;EACjB,IAAI4C,CAAC,GAAGmF,KAAK,GAAG/H,CAAC,GAAGyC,CAAC;EACrB,IAAIP,CAAC,GAAGU,CAAC,GAAGgF,EAAE;EACd,IAAIrF,CAAC,GAAGK,CAAC,GAAGgF,EAAE;EACd,OAAO,CAACrF,CAAC,EAAEL,CAAC,EAAEO,CAAC,CAAC;AAClB;AACA,SAASyF,cAAcA,CAACb,MAAM,EAAEW,IAAI,EAAE;EACpC,OAAOX,MAAM,CAAC,CAAC,CAAC,GAAGW,IAAI,CAAC,CAAC,CAAC,GAAGX,MAAM,CAAC,CAAC,CAAC;AACxC;AACA,SAASc,cAAcA,CAACJ,KAAK,EAAEK,OAAO,EAAE;EACtC,IAAI7F,CAAC,GAAGwF,KAAK,GAAGK,OAAO;EACvB,IAAIlG,CAAC,GAAGvB,IAAI,CAACI,KAAK,CAACgH,KAAK,GAAGK,OAAO,CAAC;EACnC,OAAO,CAAC7F,CAAC,EAAEL,CAAC,CAAC;AACf;AAEA,IAAImG,IAAI,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EACpCC,SAAS,EAAE,IAAI;EACfjI,KAAK,EAAEA,KAAK;EACZK,MAAM,EAAEA,MAAM;EACdI,UAAU,EAAEA,UAAU;EACtBK,QAAQ,EAAEA,QAAQ;EAClBE,QAAQ,EAAEA,QAAQ;EAClBE,iBAAiB,EAAEA,iBAAiB;EACpCkB,YAAY,EAAEA,YAAY;EAC1BQ,aAAa,EAAEA,aAAa;EAC5BC,UAAU,EAAEA,UAAU;EACtBY,KAAK,EAAEA,KAAK;EACZS,IAAI,EAAEA,IAAI;EACVC,IAAI,EAAEA,IAAI;EACVG,WAAW,EAAEA,WAAW;EACxBC,SAAS,EAAEA,SAAS;EACpBE,0BAA0B,EAAEA,0BAA0B;EACtDO,oBAAoB,EAAEA,oBAAoB;EAC1CW,iBAAiB,EAAEA,iBAAiB;EACpCK,iBAAiB,EAAEA,iBAAiB;EACpCE,wBAAwB,EAAEA,wBAAwB;EAClDS,oBAAoB,EAAEA,oBAAoB;EAC1CE,cAAc,EAAEA,cAAc;EAC9BU,cAAc,EAAEA,cAAc;EAC9BI,cAAc,EAAEA,cAAc;EAC9BC,cAAc,EAAEA;AAClB,CAAC,CAAC;AAEF,SAAS9G,QAAQ,IAAIrB,CAAC,EAAEuB,QAAQ,IAAIqB,CAAC,EAAErC,KAAK,IAAIuD,CAAC,EAAE9C,UAAU,IAAIlB,CAAC,EAAE2B,iBAAiB,IAAIgH,CAAC,EAAEhE,IAAI,IAAIiE,CAAC,EAAEvF,aAAa,IAAIwF,CAAC,EAAEvF,UAAU,IAAIwF,CAAC,EAAE5E,KAAK,IAAI/B,CAAC,EAAE4C,WAAW,IAAIgE,CAAC,EAAEtD,oBAAoB,IAAIuD,CAAC,EAAEpE,IAAI,IAAIqE,CAAC,EAAEV,IAAI,IAAItD,CAAC,EAAED,SAAS,IAAIkE,CAAC,EAAE9C,iBAAiB,IAAI+C,CAAC,EAAEjE,0BAA0B,IAAImC,CAAC,EAAEZ,iBAAiB,IAAI2C,CAAC,EAAEtI,MAAM,IAAI4F,CAAC,EAAEC,wBAAwB,IAAI0C,CAAC,EAAEjC,oBAAoB,IAAIrG,CAAC,EAAEuG,cAAc,IAAIgC,CAAC,EAAEzG,YAAY,IAAI6C,CAAC,EAAEsC,cAAc,IAAIuB,CAAC,EAAEnB,cAAc,IAAI3F,CAAC,EAAE4F,cAAc,IAAIjG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}