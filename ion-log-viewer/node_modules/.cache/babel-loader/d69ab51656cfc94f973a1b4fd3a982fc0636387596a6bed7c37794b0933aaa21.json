{"ast": null, "code": "import { ExtrudeGeometry } from \"three\";\nclass TextGeometry extends ExtrudeGeometry {\n  constructor(text, parameters = {}) {\n    const {\n      bevelEnabled = false,\n      bevelSize = 8,\n      bevelThickness = 10,\n      font,\n      height = 50,\n      size = 100,\n      lineHeight = 1,\n      letterSpacing = 0,\n      ...rest\n    } = parameters;\n    if (font === void 0) {\n      super();\n    } else {\n      const shapes = font.generateShapes(text, size, {\n        lineHeight,\n        letterSpacing\n      });\n      super(shapes, {\n        ...rest,\n        bevelEnabled,\n        bevelSize,\n        bevelThickness,\n        depth: height\n      });\n    }\n    this.type = \"TextGeometry\";\n  }\n}\nexport { TextGeometry as TextBufferGeometry, TextGeometry };", "map": {"version": 3, "names": ["TextGeometry", "ExtrudeGeometry", "constructor", "text", "parameters", "bevelEnabled", "bevelSize", "bevelThickness", "font", "height", "size", "lineHeight", "letterSpacing", "rest", "shapes", "generateShapes", "depth", "type"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/geometries/TextGeometry.ts"], "sourcesContent": ["import { ExtrudeGeometry } from 'three'\n\nimport type { Font } from '../loaders/FontLoader'\n\nexport type TextGeometryParameters = {\n  bevelEnabled?: boolean\n  bevelOffset?: number\n  bevelSize?: number\n  bevelThickness?: number\n  curveSegments?: number\n  font: Font\n  height?: number\n  size?: number\n  lineHeight?: number\n  letterSpacing?: number\n}\n\nexport class TextGeometry extends ExtrudeGeometry {\n  constructor(text: string, parameters: TextGeometryParameters = {} as TextGeometryParameters) {\n    const {\n      bevelEnabled = false,\n      bevelSize = 8,\n      bevelThickness = 10,\n      font,\n      height = 50,\n      size = 100,\n      lineHeight = 1,\n      letterSpacing = 0,\n      ...rest\n    } = parameters\n\n    if (font === undefined) {\n      // @ts-ignore\n      super() // generate default extrude geometry\n    } else {\n      const shapes = font.generateShapes(text, size, { lineHeight, letterSpacing })\n      super(shapes, { ...rest, bevelEnabled, bevelSize, bevelThickness, depth: height })\n    }\n    // @ts-ignore\n    this.type = 'TextGeometry'\n  }\n}\n\nexport { TextGeometry as TextBufferGeometry }\n"], "mappings": ";AAiBO,MAAMA,YAAA,SAAqBC,eAAA,CAAgB;EAChDC,YAAYC,IAAA,EAAcC,UAAA,GAAqC,IAA8B;IACrF;MACJC,YAAA,GAAe;MACfC,SAAA,GAAY;MACZC,cAAA,GAAiB;MACjBC,IAAA;MACAC,MAAA,GAAS;MACTC,IAAA,GAAO;MACPC,UAAA,GAAa;MACbC,aAAA,GAAgB;MAChB,GAAGC;IACD,IAAAT,UAAA;IAEJ,IAAII,IAAA,KAAS,QAAW;MAEhB;IAAA,OACD;MACC,MAAAM,MAAA,GAASN,IAAA,CAAKO,cAAA,CAAeZ,IAAA,EAAMO,IAAA,EAAM;QAAEC,UAAA;QAAYC;MAAA,CAAe;MACtE,MAAAE,MAAA,EAAQ;QAAE,GAAGD,IAAA;QAAMR,YAAA;QAAcC,SAAA;QAAWC,cAAA;QAAgBS,KAAA,EAAOP;MAAA,CAAQ;IACnF;IAEA,KAAKQ,IAAA,GAAO;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}