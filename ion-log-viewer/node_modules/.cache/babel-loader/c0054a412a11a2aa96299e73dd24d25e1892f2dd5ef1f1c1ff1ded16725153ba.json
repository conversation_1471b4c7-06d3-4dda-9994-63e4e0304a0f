{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Color, DepthTexture, UnsignedShortType, NearestFilter, WebGLRenderTarget, HalfFloatType, ShaderMaterial, UniformsUtils, NoBlending, MeshNormalMaterial, MeshBasicMaterial, SrcAlphaFactor, OneMinusSrcAlphaFactor, AddEquation, NormalBlending } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { SSRShader, SSRBlurShader, SSRDepthShader } from \"../shaders/SSRShader.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nconst SSRPass = /* @__PURE__ */(() => {\n  const _SSRPass = class extends Pass {\n    constructor({\n      renderer,\n      scene,\n      camera,\n      width,\n      height,\n      selects,\n      bouncing = false,\n      groundReflector\n    }) {\n      super();\n      this.width = width !== void 0 ? width : 512;\n      this.height = height !== void 0 ? height : 512;\n      this.clear = true;\n      this.renderer = renderer;\n      this.scene = scene;\n      this.camera = camera;\n      this.groundReflector = groundReflector;\n      this.opacity = SSRShader.uniforms.opacity.value;\n      this.output = 0;\n      this.maxDistance = SSRShader.uniforms.maxDistance.value;\n      this.thickness = SSRShader.uniforms.thickness.value;\n      this.tempColor = new Color();\n      this._selects = selects;\n      this.selective = Array.isArray(this._selects);\n      Object.defineProperty(this, \"selects\", {\n        get() {\n          return this._selects;\n        },\n        set(val) {\n          if (this._selects === val) return;\n          this._selects = val;\n          if (Array.isArray(val)) {\n            this.selective = true;\n            this.ssrMaterial.defines.SELECTIVE = true;\n            this.ssrMaterial.needsUpdate = true;\n          } else {\n            this.selective = false;\n            this.ssrMaterial.defines.SELECTIVE = false;\n            this.ssrMaterial.needsUpdate = true;\n          }\n        }\n      });\n      this._bouncing = bouncing;\n      Object.defineProperty(this, \"bouncing\", {\n        get() {\n          return this._bouncing;\n        },\n        set(val) {\n          if (this._bouncing === val) return;\n          this._bouncing = val;\n          if (val) {\n            this.ssrMaterial.uniforms[\"tDiffuse\"].value = this.prevRenderTarget.texture;\n          } else {\n            this.ssrMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n          }\n        }\n      });\n      this.blur = true;\n      this._distanceAttenuation = SSRShader.defines.DISTANCE_ATTENUATION;\n      Object.defineProperty(this, \"distanceAttenuation\", {\n        get() {\n          return this._distanceAttenuation;\n        },\n        set(val) {\n          if (this._distanceAttenuation === val) return;\n          this._distanceAttenuation = val;\n          this.ssrMaterial.defines.DISTANCE_ATTENUATION = val;\n          this.ssrMaterial.needsUpdate = true;\n        }\n      });\n      this._fresnel = SSRShader.defines.FRESNEL;\n      Object.defineProperty(this, \"fresnel\", {\n        get() {\n          return this._fresnel;\n        },\n        set(val) {\n          if (this._fresnel === val) return;\n          this._fresnel = val;\n          this.ssrMaterial.defines.FRESNEL = val;\n          this.ssrMaterial.needsUpdate = true;\n        }\n      });\n      this._infiniteThick = SSRShader.defines.INFINITE_THICK;\n      Object.defineProperty(this, \"infiniteThick\", {\n        get() {\n          return this._infiniteThick;\n        },\n        set(val) {\n          if (this._infiniteThick === val) return;\n          this._infiniteThick = val;\n          this.ssrMaterial.defines.INFINITE_THICK = val;\n          this.ssrMaterial.needsUpdate = true;\n        }\n      });\n      const depthTexture = new DepthTexture();\n      depthTexture.type = UnsignedShortType;\n      depthTexture.minFilter = NearestFilter;\n      depthTexture.magFilter = NearestFilter;\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n        depthTexture,\n        depthBuffer: true\n      });\n      this.prevRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter\n      });\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType\n      });\n      this.metalnessRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType\n      });\n      this.ssrRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter\n      });\n      this.blurRenderTarget = this.ssrRenderTarget.clone();\n      this.blurRenderTarget2 = this.ssrRenderTarget.clone();\n      this.ssrMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRShader.defines, {\n          MAX_STEP: Math.sqrt(this.width * this.width + this.height * this.height)\n        }),\n        uniforms: UniformsUtils.clone(SSRShader.uniforms),\n        vertexShader: SSRShader.vertexShader,\n        fragmentShader: SSRShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.ssrMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n      this.ssrMaterial.uniforms[\"tNormal\"].value = this.normalRenderTarget.texture;\n      this.ssrMaterial.defines.SELECTIVE = this.selective;\n      this.ssrMaterial.needsUpdate = true;\n      this.ssrMaterial.uniforms[\"tMetalness\"].value = this.metalnessRenderTarget.texture;\n      this.ssrMaterial.uniforms[\"tDepth\"].value = this.beautyRenderTarget.depthTexture;\n      this.ssrMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.ssrMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.ssrMaterial.uniforms[\"thickness\"].value = this.thickness;\n      this.ssrMaterial.uniforms[\"resolution\"].value.set(this.width, this.height);\n      this.ssrMaterial.uniforms[\"cameraProjectionMatrix\"].value.copy(this.camera.projectionMatrix);\n      this.ssrMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.normalMaterial = new MeshNormalMaterial();\n      this.normalMaterial.blending = NoBlending;\n      this.metalnessOnMaterial = new MeshBasicMaterial({\n        color: \"white\"\n      });\n      this.metalnessOffMaterial = new MeshBasicMaterial({\n        color: \"black\"\n      });\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader\n      });\n      this.blurMaterial.uniforms[\"tDiffuse\"].value = this.ssrRenderTarget.texture;\n      this.blurMaterial.uniforms[\"resolution\"].value.set(this.width, this.height);\n      this.blurMaterial2 = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader\n      });\n      this.blurMaterial2.uniforms[\"tDiffuse\"].value = this.blurRenderTarget.texture;\n      this.blurMaterial2.uniforms[\"resolution\"].value.set(this.width, this.height);\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRDepthShader.defines),\n        uniforms: UniformsUtils.clone(SSRDepthShader.uniforms),\n        vertexShader: SSRDepthShader.vertexShader,\n        fragmentShader: SSRDepthShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.depthRenderMaterial.uniforms[\"tDepth\"].value = this.beautyRenderTarget.depthTexture;\n      this.depthRenderMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.depthRenderMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: SrcAlphaFactor,\n        blendDst: OneMinusSrcAlphaFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: SrcAlphaFactor,\n        blendDstAlpha: OneMinusSrcAlphaFactor,\n        blendEquationAlpha: AddEquation\n        // premultipliedAlpha:true,\n      });\n      this.fsQuad = new FullScreenQuad(null);\n      this.originalClearColor = new Color();\n    }\n    dispose() {\n      this.beautyRenderTarget.dispose();\n      this.prevRenderTarget.dispose();\n      this.normalRenderTarget.dispose();\n      this.metalnessRenderTarget.dispose();\n      this.ssrRenderTarget.dispose();\n      this.blurRenderTarget.dispose();\n      this.blurRenderTarget2.dispose();\n      this.normalMaterial.dispose();\n      this.metalnessOnMaterial.dispose();\n      this.metalnessOffMaterial.dispose();\n      this.blurMaterial.dispose();\n      this.blurMaterial2.dispose();\n      this.copyMaterial.dispose();\n      this.depthRenderMaterial.dispose();\n      this.fsQuad.dispose();\n    }\n    render(renderer, writeBuffer) {\n      renderer.setRenderTarget(this.beautyRenderTarget);\n      renderer.clear();\n      if (this.groundReflector) {\n        this.groundReflector.visible = false;\n        this.groundReflector.doRender(this.renderer, this.scene, this.camera);\n        this.groundReflector.visible = true;\n      }\n      renderer.render(this.scene, this.camera);\n      if (this.groundReflector) this.groundReflector.visible = false;\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0, 0);\n      if (this.selective) {\n        this.renderMetalness(renderer, this.metalnessOnMaterial, this.metalnessRenderTarget, 0, 0);\n      }\n      this.ssrMaterial.uniforms[\"opacity\"].value = this.opacity;\n      this.ssrMaterial.uniforms[\"maxDistance\"].value = this.maxDistance;\n      this.ssrMaterial.uniforms[\"thickness\"].value = this.thickness;\n      this.renderPass(renderer, this.ssrMaterial, this.ssrRenderTarget);\n      if (this.blur) {\n        this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget);\n        this.renderPass(renderer, this.blurMaterial2, this.blurRenderTarget2);\n      }\n      switch (this.output) {\n        case _SSRPass.OUTPUT.Default:\n          if (this.bouncing) {\n            this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.copyMaterial.blending = NoBlending;\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget);\n            if (this.blur) this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget2.texture;else this.copyMaterial.uniforms[\"tDiffuse\"].value = this.ssrRenderTarget.texture;\n            this.copyMaterial.blending = NormalBlending;\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget);\n            this.copyMaterial.uniforms[\"tDiffuse\"].value = this.prevRenderTarget.texture;\n            this.copyMaterial.blending = NoBlending;\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          } else {\n            this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.copyMaterial.blending = NoBlending;\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n            if (this.blur) this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget2.texture;else this.copyMaterial.uniforms[\"tDiffuse\"].value = this.ssrRenderTarget.texture;\n            this.copyMaterial.blending = NormalBlending;\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          }\n          break;\n        case _SSRPass.OUTPUT.SSR:\n          if (this.blur) this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget2.texture;else this.copyMaterial.uniforms[\"tDiffuse\"].value = this.ssrRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          if (this.bouncing) {\n            if (this.blur) this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget2.texture;else this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.copyMaterial.blending = NoBlending;\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget);\n            this.copyMaterial.uniforms[\"tDiffuse\"].value = this.ssrRenderTarget.texture;\n            this.copyMaterial.blending = NormalBlending;\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget);\n          }\n          break;\n        case _SSRPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSRPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSRPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.normalRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSRPass.OUTPUT.Metalness:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.metalnessRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        default:\n          console.warn(\"THREE.SSRPass: Unknown output type.\");\n      }\n    }\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor));\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor);\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.fsQuad.material = passMaterial;\n      this.fsQuad.render(renderer);\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor));\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor);\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      clearColor = overrideMaterial.clearColor || clearColor;\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.scene.overrideMaterial = overrideMaterial;\n      renderer.render(this.scene, this.camera);\n      this.scene.overrideMaterial = null;\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    renderMetalness(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor));\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor);\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      clearColor = overrideMaterial.clearColor || clearColor;\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.scene.traverseVisible(child => {\n        child._SSRPassBackupMaterial = child.material;\n        if (this._selects.includes(child)) {\n          child.material = this.metalnessOnMaterial;\n        } else {\n          child.material = this.metalnessOffMaterial;\n        }\n      });\n      renderer.render(this.scene, this.camera);\n      this.scene.traverseVisible(child => {\n        child.material = child._SSRPassBackupMaterial;\n      });\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    setSize(width, height) {\n      this.width = width;\n      this.height = height;\n      this.ssrMaterial.defines.MAX_STEP = Math.sqrt(width * width + height * height);\n      this.ssrMaterial.needsUpdate = true;\n      this.beautyRenderTarget.setSize(width, height);\n      this.prevRenderTarget.setSize(width, height);\n      this.ssrRenderTarget.setSize(width, height);\n      this.normalRenderTarget.setSize(width, height);\n      this.metalnessRenderTarget.setSize(width, height);\n      this.blurRenderTarget.setSize(width, height);\n      this.blurRenderTarget2.setSize(width, height);\n      this.ssrMaterial.uniforms[\"resolution\"].value.set(width, height);\n      this.ssrMaterial.uniforms[\"cameraProjectionMatrix\"].value.copy(this.camera.projectionMatrix);\n      this.ssrMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.blurMaterial.uniforms[\"resolution\"].value.set(width, height);\n      this.blurMaterial2.uniforms[\"resolution\"].value.set(width, height);\n    }\n  };\n  let SSRPass2 = _SSRPass;\n  __publicField(SSRPass2, \"OUTPUT\", {\n    Default: 0,\n    SSR: 1,\n    Beauty: 3,\n    Depth: 4,\n    Normal: 5,\n    Metalness: 7\n  });\n  return SSRPass2;\n})();\nexport { SSRPass };", "map": {"version": 3, "names": ["SSRPass", "_SSRPass", "Pass", "constructor", "renderer", "scene", "camera", "width", "height", "selects", "bouncing", "groundReflector", "clear", "opacity", "SSRShader", "uniforms", "value", "output", "maxDistance", "thickness", "tempColor", "Color", "_selects", "selective", "Array", "isArray", "Object", "defineProperty", "get", "set", "val", "ssrMaterial", "defines", "SELECTIVE", "needsUpdate", "_bouncing", "prevRenderTarget", "texture", "beautyR<PERSON><PERSON><PERSON><PERSON>", "blur", "_distanceAttenuation", "DISTANCE_ATTENUATION", "_fresnel", "FRESNEL", "_infiniteThick", "INFINITE_THICK", "depthTexture", "DepthTexture", "type", "UnsignedShortType", "minFilter", "NearestFilter", "magFilter", "WebGLRenderTarget", "HalfFloatType", "depthBuffer", "normalRenderTarget", "metalnessRenderTarget", "ssrRenderTarget", "blurRenderTarget", "clone", "blurRenderTarget2", "ShaderMaterial", "assign", "MAX_STEP", "Math", "sqrt", "UniformsUtils", "vertexShader", "fragmentShader", "blending", "NoBlending", "near", "far", "copy", "projectionMatrix", "projectionMatrixInverse", "normalMaterial", "MeshNormalMaterial", "metalnessOnMaterial", "MeshBasicMaterial", "color", "metalnessOffMaterial", "blurMaterial", "SSR<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "blurMaterial2", "depthRenderMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copyMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transparent", "depthTest", "depthWrite", "blendSrc", "SrcAlphaFactor", "blendDst", "OneMinusSrcAlphaFactor", "blendEquation", "AddEquation", "blendSrcAlpha", "blendDstAlpha", "blendEquationAlpha", "fsQuad", "FullScreenQuad", "originalClearColor", "dispose", "render", "writeBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visible", "doR<PERSON>", "renderOverride", "renderMetalness", "renderPass", "OUTPUT", "<PERSON><PERSON><PERSON>", "NormalBlending", "renderToScreen", "SSR", "Beauty", "De<PERSON><PERSON>", "Normal", "Metalness", "console", "warn", "passMaterial", "renderTarget", "clearColor", "clearAlpha", "getClearColor", "originalClearAlpha", "getClearAlpha", "originalAutoClear", "autoClear", "setClearColor", "setClearAlpha", "material", "overrideMaterial", "traverseVisible", "child", "_SSRPassBackupMaterial", "includes", "setSize", "SSRPass2", "__publicField"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/postprocessing/SSRPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  NormalBlending,\n  DepthTexture,\n  SrcAlphaFactor,\n  OneMinusSrcAlphaFactor,\n  MeshNormalMaterial,\n  MeshBasicMaterial,\n  NearestFilter,\n  NoBlending,\n  ShaderMaterial,\n  UniformsUtils,\n  UnsignedShortType,\n  WebGLRenderTarget,\n  HalfFloatType,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SSRShader } from '../shaders/SSRShader'\nimport { SSRBlurShader } from '../shaders/SSRShader'\nimport { SSRDepthShader } from '../shaders/SSRShader'\nimport { CopyShader } from '../shaders/CopyShader'\n\nconst SSRPass = /* @__PURE__ */ (() => {\n  class SSRPass extends Pass {\n    static OUTPUT = {\n      Default: 0,\n      SSR: 1,\n      Beauty: 3,\n      Depth: 4,\n      Normal: 5,\n      Metalness: 7,\n    }\n    constructor({ renderer, scene, camera, width, height, selects, bouncing = false, groundReflector }) {\n      super()\n\n      this.width = width !== undefined ? width : 512\n      this.height = height !== undefined ? height : 512\n\n      this.clear = true\n\n      this.renderer = renderer\n      this.scene = scene\n      this.camera = camera\n      this.groundReflector = groundReflector\n\n      this.opacity = SSRShader.uniforms.opacity.value\n      this.output = 0\n\n      this.maxDistance = SSRShader.uniforms.maxDistance.value\n      this.thickness = SSRShader.uniforms.thickness.value\n\n      this.tempColor = new Color()\n\n      this._selects = selects\n      this.selective = Array.isArray(this._selects)\n      Object.defineProperty(this, 'selects', {\n        get() {\n          return this._selects\n        },\n        set(val) {\n          if (this._selects === val) return\n          this._selects = val\n          if (Array.isArray(val)) {\n            this.selective = true\n            this.ssrMaterial.defines.SELECTIVE = true\n            this.ssrMaterial.needsUpdate = true\n          } else {\n            this.selective = false\n            this.ssrMaterial.defines.SELECTIVE = false\n            this.ssrMaterial.needsUpdate = true\n          }\n        },\n      })\n\n      this._bouncing = bouncing\n      Object.defineProperty(this, 'bouncing', {\n        get() {\n          return this._bouncing\n        },\n        set(val) {\n          if (this._bouncing === val) return\n          this._bouncing = val\n          if (val) {\n            this.ssrMaterial.uniforms['tDiffuse'].value = this.prevRenderTarget.texture\n          } else {\n            this.ssrMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          }\n        },\n      })\n\n      this.blur = true\n\n      this._distanceAttenuation = SSRShader.defines.DISTANCE_ATTENUATION\n      Object.defineProperty(this, 'distanceAttenuation', {\n        get() {\n          return this._distanceAttenuation\n        },\n        set(val) {\n          if (this._distanceAttenuation === val) return\n          this._distanceAttenuation = val\n          this.ssrMaterial.defines.DISTANCE_ATTENUATION = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      this._fresnel = SSRShader.defines.FRESNEL\n      Object.defineProperty(this, 'fresnel', {\n        get() {\n          return this._fresnel\n        },\n        set(val) {\n          if (this._fresnel === val) return\n          this._fresnel = val\n          this.ssrMaterial.defines.FRESNEL = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      this._infiniteThick = SSRShader.defines.INFINITE_THICK\n      Object.defineProperty(this, 'infiniteThick', {\n        get() {\n          return this._infiniteThick\n        },\n        set(val) {\n          if (this._infiniteThick === val) return\n          this._infiniteThick = val\n          this.ssrMaterial.defines.INFINITE_THICK = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      // beauty render target with depth buffer\n\n      const depthTexture = new DepthTexture()\n      depthTexture.type = UnsignedShortType\n      depthTexture.minFilter = NearestFilter\n      depthTexture.magFilter = NearestFilter\n\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n        depthTexture: depthTexture,\n        depthBuffer: true,\n      })\n\n      //for bouncing\n      this.prevRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n      })\n\n      // normal render target\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n\n      // metalness render target\n\n      this.metalnessRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n\n      // ssr render target\n\n      this.ssrRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n      })\n\n      this.blurRenderTarget = this.ssrRenderTarget.clone()\n      this.blurRenderTarget2 = this.ssrRenderTarget.clone()\n      // this.blurRenderTarget3 = this.ssrRenderTarget.clone();\n\n      // ssr material\n\n      this.ssrMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRShader.defines, {\n          MAX_STEP: Math.sqrt(this.width * this.width + this.height * this.height),\n        }),\n        uniforms: UniformsUtils.clone(SSRShader.uniforms),\n        vertexShader: SSRShader.vertexShader,\n        fragmentShader: SSRShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.ssrMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n      this.ssrMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.ssrMaterial.defines.SELECTIVE = this.selective\n      this.ssrMaterial.needsUpdate = true\n      this.ssrMaterial.uniforms['tMetalness'].value = this.metalnessRenderTarget.texture\n      this.ssrMaterial.uniforms['tDepth'].value = this.beautyRenderTarget.depthTexture\n      this.ssrMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.ssrMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.ssrMaterial.uniforms['thickness'].value = this.thickness\n      this.ssrMaterial.uniforms['resolution'].value.set(this.width, this.height)\n      this.ssrMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssrMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      // normal material\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      // metalnessOn material\n\n      this.metalnessOnMaterial = new MeshBasicMaterial({\n        color: 'white',\n      })\n\n      // metalnessOff material\n\n      this.metalnessOffMaterial = new MeshBasicMaterial({\n        color: 'black',\n      })\n\n      // blur material\n\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader,\n      })\n      this.blurMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n      this.blurMaterial.uniforms['resolution'].value.set(this.width, this.height)\n\n      // blur material 2\n\n      this.blurMaterial2 = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader,\n      })\n      this.blurMaterial2.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n      this.blurMaterial2.uniforms['resolution'].value.set(this.width, this.height)\n\n      // // blur material 3\n\n      // this.blurMaterial3 = new ShaderMaterial({\n      //   defines: Object.assign({}, SSRBlurShader.defines),\n      //   uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n      //   vertexShader: SSRBlurShader.vertexShader,\n      //   fragmentShader: SSRBlurShader.fragmentShader\n      // });\n      // this.blurMaterial3.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture;\n      // this.blurMaterial3.uniforms['resolution'].value.set(this.width, this.height);\n\n      // material for rendering the depth\n\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRDepthShader.defines),\n        uniforms: UniformsUtils.clone(SSRDepthShader.uniforms),\n        vertexShader: SSRDepthShader.vertexShader,\n        fragmentShader: SSRDepthShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.depthRenderMaterial.uniforms['tDepth'].value = this.beautyRenderTarget.depthTexture\n      this.depthRenderMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.depthRenderMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      // material for rendering the content of a render target\n\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: SrcAlphaFactor,\n        blendDst: OneMinusSrcAlphaFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: SrcAlphaFactor,\n        blendDstAlpha: OneMinusSrcAlphaFactor,\n        blendEquationAlpha: AddEquation,\n        // premultipliedAlpha:true,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n\n      this.originalClearColor = new Color()\n    }\n\n    dispose() {\n      // dispose render targets\n\n      this.beautyRenderTarget.dispose()\n      this.prevRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.metalnessRenderTarget.dispose()\n      this.ssrRenderTarget.dispose()\n      this.blurRenderTarget.dispose()\n      this.blurRenderTarget2.dispose()\n      // this.blurRenderTarget3.dispose();\n\n      // dispose materials\n\n      this.normalMaterial.dispose()\n      this.metalnessOnMaterial.dispose()\n      this.metalnessOffMaterial.dispose()\n      this.blurMaterial.dispose()\n      this.blurMaterial2.dispose()\n      this.copyMaterial.dispose()\n      this.depthRenderMaterial.dispose()\n\n      // dipsose full screen quad\n\n      this.fsQuad.dispose()\n    }\n\n    render(renderer, writeBuffer /*, readBuffer, deltaTime, maskActive */) {\n      // render beauty and depth\n\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      if (this.groundReflector) {\n        this.groundReflector.visible = false\n        this.groundReflector.doRender(this.renderer, this.scene, this.camera)\n        this.groundReflector.visible = true\n      }\n\n      renderer.render(this.scene, this.camera)\n      if (this.groundReflector) this.groundReflector.visible = false\n\n      // render normals\n\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0, 0)\n\n      // render metalnesses\n\n      if (this.selective) {\n        this.renderMetalness(renderer, this.metalnessOnMaterial, this.metalnessRenderTarget, 0, 0)\n      }\n\n      // render SSR\n\n      this.ssrMaterial.uniforms['opacity'].value = this.opacity\n      this.ssrMaterial.uniforms['maxDistance'].value = this.maxDistance\n      this.ssrMaterial.uniforms['thickness'].value = this.thickness\n      this.renderPass(renderer, this.ssrMaterial, this.ssrRenderTarget)\n\n      // render blur\n\n      if (this.blur) {\n        this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget)\n        this.renderPass(renderer, this.blurMaterial2, this.blurRenderTarget2)\n        // this.renderPass(renderer, this.blurMaterial3, this.blurRenderTarget3);\n      }\n\n      // output result to screen\n\n      switch (this.output) {\n        case SSRPass.OUTPUT.Default:\n          if (this.bouncing) {\n            this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            this.copyMaterial.uniforms['tDiffuse'].value = this.prevRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n          } else {\n            this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n          }\n\n          break\n        case SSRPass.OUTPUT.SSR:\n          if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n          else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          if (this.bouncing) {\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n          }\n\n          break\n\n        case SSRPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Metalness:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.metalnessRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        default:\n          console.warn('THREE.SSRPass: Unknown output type.')\n      }\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderMetalness(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.traverseVisible((child) => {\n        child._SSRPassBackupMaterial = child.material\n        if (this._selects.includes(child)) {\n          child.material = this.metalnessOnMaterial\n        } else {\n          child.material = this.metalnessOffMaterial\n        }\n      })\n      renderer.render(this.scene, this.camera)\n      this.scene.traverseVisible((child) => {\n        child.material = child._SSRPassBackupMaterial\n      })\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.width = width\n      this.height = height\n\n      this.ssrMaterial.defines.MAX_STEP = Math.sqrt(width * width + height * height)\n      this.ssrMaterial.needsUpdate = true\n      this.beautyRenderTarget.setSize(width, height)\n      this.prevRenderTarget.setSize(width, height)\n      this.ssrRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.metalnessRenderTarget.setSize(width, height)\n      this.blurRenderTarget.setSize(width, height)\n      this.blurRenderTarget2.setSize(width, height)\n      // this.blurRenderTarget3.setSize(width, height);\n\n      this.ssrMaterial.uniforms['resolution'].value.set(width, height)\n      this.ssrMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssrMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      this.blurMaterial.uniforms['resolution'].value.set(width, height)\n      this.blurMaterial2.uniforms['resolution'].value.set(width, height)\n    }\n  }\n\n  return SSRPass\n})()\n\nexport { SSRPass }\n"], "mappings": ";;;;;;;;;;;;;;;AAuBK,MAACA,OAAA,GAA2B,sBAAM;EACrC,MAAMC,QAAA,GAAN,cAAsBC,IAAA,CAAK;IASzBC,YAAY;MAAEC,QAAA;MAAUC,KAAA;MAAOC,MAAA;MAAQC,KAAA;MAAOC,MAAA;MAAQC,OAAA;MAASC,QAAA,GAAW;MAAOC;IAAe,GAAI;MAClG,MAAO;MAEP,KAAKJ,KAAA,GAAQA,KAAA,KAAU,SAAYA,KAAA,GAAQ;MAC3C,KAAKC,MAAA,GAASA,MAAA,KAAW,SAAYA,MAAA,GAAS;MAE9C,KAAKI,KAAA,GAAQ;MAEb,KAAKR,QAAA,GAAWA,QAAA;MAChB,KAAKC,KAAA,GAAQA,KAAA;MACb,KAAKC,MAAA,GAASA,MAAA;MACd,KAAKK,eAAA,GAAkBA,eAAA;MAEvB,KAAKE,OAAA,GAAUC,SAAA,CAAUC,QAAA,CAASF,OAAA,CAAQG,KAAA;MAC1C,KAAKC,MAAA,GAAS;MAEd,KAAKC,WAAA,GAAcJ,SAAA,CAAUC,QAAA,CAASG,WAAA,CAAYF,KAAA;MAClD,KAAKG,SAAA,GAAYL,SAAA,CAAUC,QAAA,CAASI,SAAA,CAAUH,KAAA;MAE9C,KAAKI,SAAA,GAAY,IAAIC,KAAA,CAAO;MAE5B,KAAKC,QAAA,GAAWb,OAAA;MAChB,KAAKc,SAAA,GAAYC,KAAA,CAAMC,OAAA,CAAQ,KAAKH,QAAQ;MAC5CI,MAAA,CAAOC,cAAA,CAAe,MAAM,WAAW;QACrCC,IAAA,EAAM;UACJ,OAAO,KAAKN,QAAA;QACb;QACDO,IAAIC,GAAA,EAAK;UACP,IAAI,KAAKR,QAAA,KAAaQ,GAAA,EAAK;UAC3B,KAAKR,QAAA,GAAWQ,GAAA;UAChB,IAAIN,KAAA,CAAMC,OAAA,CAAQK,GAAG,GAAG;YACtB,KAAKP,SAAA,GAAY;YACjB,KAAKQ,WAAA,CAAYC,OAAA,CAAQC,SAAA,GAAY;YACrC,KAAKF,WAAA,CAAYG,WAAA,GAAc;UAC3C,OAAiB;YACL,KAAKX,SAAA,GAAY;YACjB,KAAKQ,WAAA,CAAYC,OAAA,CAAQC,SAAA,GAAY;YACrC,KAAKF,WAAA,CAAYG,WAAA,GAAc;UAChC;QACF;MACT,CAAO;MAED,KAAKC,SAAA,GAAYzB,QAAA;MACjBgB,MAAA,CAAOC,cAAA,CAAe,MAAM,YAAY;QACtCC,IAAA,EAAM;UACJ,OAAO,KAAKO,SAAA;QACb;QACDN,IAAIC,GAAA,EAAK;UACP,IAAI,KAAKK,SAAA,KAAcL,GAAA,EAAK;UAC5B,KAAKK,SAAA,GAAYL,GAAA;UACjB,IAAIA,GAAA,EAAK;YACP,KAAKC,WAAA,CAAYhB,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKoB,gBAAA,CAAiBC,OAAA;UAChF,OAAiB;YACL,KAAKN,WAAA,CAAYhB,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;UACvE;QACF;MACT,CAAO;MAED,KAAKE,IAAA,GAAO;MAEZ,KAAKC,oBAAA,GAAuB1B,SAAA,CAAUkB,OAAA,CAAQS,oBAAA;MAC9Cf,MAAA,CAAOC,cAAA,CAAe,MAAM,uBAAuB;QACjDC,IAAA,EAAM;UACJ,OAAO,KAAKY,oBAAA;QACb;QACDX,IAAIC,GAAA,EAAK;UACP,IAAI,KAAKU,oBAAA,KAAyBV,GAAA,EAAK;UACvC,KAAKU,oBAAA,GAAuBV,GAAA;UAC5B,KAAKC,WAAA,CAAYC,OAAA,CAAQS,oBAAA,GAAuBX,GAAA;UAChD,KAAKC,WAAA,CAAYG,WAAA,GAAc;QAChC;MACT,CAAO;MAED,KAAKQ,QAAA,GAAW5B,SAAA,CAAUkB,OAAA,CAAQW,OAAA;MAClCjB,MAAA,CAAOC,cAAA,CAAe,MAAM,WAAW;QACrCC,IAAA,EAAM;UACJ,OAAO,KAAKc,QAAA;QACb;QACDb,IAAIC,GAAA,EAAK;UACP,IAAI,KAAKY,QAAA,KAAaZ,GAAA,EAAK;UAC3B,KAAKY,QAAA,GAAWZ,GAAA;UAChB,KAAKC,WAAA,CAAYC,OAAA,CAAQW,OAAA,GAAUb,GAAA;UACnC,KAAKC,WAAA,CAAYG,WAAA,GAAc;QAChC;MACT,CAAO;MAED,KAAKU,cAAA,GAAiB9B,SAAA,CAAUkB,OAAA,CAAQa,cAAA;MACxCnB,MAAA,CAAOC,cAAA,CAAe,MAAM,iBAAiB;QAC3CC,IAAA,EAAM;UACJ,OAAO,KAAKgB,cAAA;QACb;QACDf,IAAIC,GAAA,EAAK;UACP,IAAI,KAAKc,cAAA,KAAmBd,GAAA,EAAK;UACjC,KAAKc,cAAA,GAAiBd,GAAA;UACtB,KAAKC,WAAA,CAAYC,OAAA,CAAQa,cAAA,GAAiBf,GAAA;UAC1C,KAAKC,WAAA,CAAYG,WAAA,GAAc;QAChC;MACT,CAAO;MAID,MAAMY,YAAA,GAAe,IAAIC,YAAA,CAAc;MACvCD,YAAA,CAAaE,IAAA,GAAOC,iBAAA;MACpBH,YAAA,CAAaI,SAAA,GAAYC,aAAA;MACzBL,YAAA,CAAaM,SAAA,GAAYD,aAAA;MAEzB,KAAKb,kBAAA,GAAqB,IAAIe,iBAAA,CAAkB,KAAK9C,KAAA,EAAO,KAAKC,MAAA,EAAQ;QACvE0C,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD,aAAA;QACXH,IAAA,EAAMM,aAAA;QACNR,YAAA;QACAS,WAAA,EAAa;MACrB,CAAO;MAGD,KAAKnB,gBAAA,GAAmB,IAAIiB,iBAAA,CAAkB,KAAK9C,KAAA,EAAO,KAAKC,MAAA,EAAQ;QACrE0C,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD;MACnB,CAAO;MAID,KAAKK,kBAAA,GAAqB,IAAIH,iBAAA,CAAkB,KAAK9C,KAAA,EAAO,KAAKC,MAAA,EAAQ;QACvE0C,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD,aAAA;QACXH,IAAA,EAAMM;MACd,CAAO;MAID,KAAKG,qBAAA,GAAwB,IAAIJ,iBAAA,CAAkB,KAAK9C,KAAA,EAAO,KAAKC,MAAA,EAAQ;QAC1E0C,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD,aAAA;QACXH,IAAA,EAAMM;MACd,CAAO;MAID,KAAKI,eAAA,GAAkB,IAAIL,iBAAA,CAAkB,KAAK9C,KAAA,EAAO,KAAKC,MAAA,EAAQ;QACpE0C,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD;MACnB,CAAO;MAED,KAAKQ,gBAAA,GAAmB,KAAKD,eAAA,CAAgBE,KAAA,CAAO;MACpD,KAAKC,iBAAA,GAAoB,KAAKH,eAAA,CAAgBE,KAAA,CAAO;MAKrD,KAAK7B,WAAA,GAAc,IAAI+B,cAAA,CAAe;QACpC9B,OAAA,EAASN,MAAA,CAAOqC,MAAA,CAAO,IAAIjD,SAAA,CAAUkB,OAAA,EAAS;UAC5CgC,QAAA,EAAUC,IAAA,CAAKC,IAAA,CAAK,KAAK3D,KAAA,GAAQ,KAAKA,KAAA,GAAQ,KAAKC,MAAA,GAAS,KAAKA,MAAM;QACjF,CAAS;QACDO,QAAA,EAAUoD,aAAA,CAAcP,KAAA,CAAM9C,SAAA,CAAUC,QAAQ;QAChDqD,YAAA,EAActD,SAAA,CAAUsD,YAAA;QACxBC,cAAA,EAAgBvD,SAAA,CAAUuD,cAAA;QAC1BC,QAAA,EAAUC;MAClB,CAAO;MAED,KAAKxC,WAAA,CAAYhB,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;MACtE,KAAKN,WAAA,CAAYhB,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQ,KAAKwC,kBAAA,CAAmBnB,OAAA;MACrE,KAAKN,WAAA,CAAYC,OAAA,CAAQC,SAAA,GAAY,KAAKV,SAAA;MAC1C,KAAKQ,WAAA,CAAYG,WAAA,GAAc;MAC/B,KAAKH,WAAA,CAAYhB,QAAA,CAAS,YAAY,EAAEC,KAAA,GAAQ,KAAKyC,qBAAA,CAAsBpB,OAAA;MAC3E,KAAKN,WAAA,CAAYhB,QAAA,CAAS,QAAQ,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBQ,YAAA;MACpE,KAAKf,WAAA,CAAYhB,QAAA,CAAS,YAAY,EAAEC,KAAA,GAAQ,KAAKV,MAAA,CAAOkE,IAAA;MAC5D,KAAKzC,WAAA,CAAYhB,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAKV,MAAA,CAAOmE,GAAA;MAC3D,KAAK1C,WAAA,CAAYhB,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAKG,SAAA;MACpD,KAAKY,WAAA,CAAYhB,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAI,KAAKtB,KAAA,EAAO,KAAKC,MAAM;MACzE,KAAKuB,WAAA,CAAYhB,QAAA,CAAS,wBAAwB,EAAEC,KAAA,CAAM0D,IAAA,CAAK,KAAKpE,MAAA,CAAOqE,gBAAgB;MAC3F,KAAK5C,WAAA,CAAYhB,QAAA,CAAS,+BAA+B,EAAEC,KAAA,CAAM0D,IAAA,CAAK,KAAKpE,MAAA,CAAOsE,uBAAuB;MAIzG,KAAKC,cAAA,GAAiB,IAAIC,kBAAA,CAAoB;MAC9C,KAAKD,cAAA,CAAeP,QAAA,GAAWC,UAAA;MAI/B,KAAKQ,mBAAA,GAAsB,IAAIC,iBAAA,CAAkB;QAC/CC,KAAA,EAAO;MACf,CAAO;MAID,KAAKC,oBAAA,GAAuB,IAAIF,iBAAA,CAAkB;QAChDC,KAAA,EAAO;MACf,CAAO;MAID,KAAKE,YAAA,GAAe,IAAIrB,cAAA,CAAe;QACrC9B,OAAA,EAASN,MAAA,CAAOqC,MAAA,CAAO,IAAIqB,aAAA,CAAcpD,OAAO;QAChDjB,QAAA,EAAUoD,aAAA,CAAcP,KAAA,CAAMwB,aAAA,CAAcrE,QAAQ;QACpDqD,YAAA,EAAcgB,aAAA,CAAchB,YAAA;QAC5BC,cAAA,EAAgBe,aAAA,CAAcf;MACtC,CAAO;MACD,KAAKc,YAAA,CAAapE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK0C,eAAA,CAAgBrB,OAAA;MACpE,KAAK8C,YAAA,CAAapE,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAI,KAAKtB,KAAA,EAAO,KAAKC,MAAM;MAI1E,KAAK6E,aAAA,GAAgB,IAAIvB,cAAA,CAAe;QACtC9B,OAAA,EAASN,MAAA,CAAOqC,MAAA,CAAO,IAAIqB,aAAA,CAAcpD,OAAO;QAChDjB,QAAA,EAAUoD,aAAA,CAAcP,KAAA,CAAMwB,aAAA,CAAcrE,QAAQ;QACpDqD,YAAA,EAAcgB,aAAA,CAAchB,YAAA;QAC5BC,cAAA,EAAgBe,aAAA,CAAcf;MACtC,CAAO;MACD,KAAKgB,aAAA,CAActE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK2C,gBAAA,CAAiBtB,OAAA;MACtE,KAAKgD,aAAA,CAActE,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAI,KAAKtB,KAAA,EAAO,KAAKC,MAAM;MAe3E,KAAK8E,mBAAA,GAAsB,IAAIxB,cAAA,CAAe;QAC5C9B,OAAA,EAASN,MAAA,CAAOqC,MAAA,CAAO,IAAIwB,cAAA,CAAevD,OAAO;QACjDjB,QAAA,EAAUoD,aAAA,CAAcP,KAAA,CAAM2B,cAAA,CAAexE,QAAQ;QACrDqD,YAAA,EAAcmB,cAAA,CAAenB,YAAA;QAC7BC,cAAA,EAAgBkB,cAAA,CAAelB,cAAA;QAC/BC,QAAA,EAAUC;MAClB,CAAO;MACD,KAAKe,mBAAA,CAAoBvE,QAAA,CAAS,QAAQ,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBQ,YAAA;MAC5E,KAAKwC,mBAAA,CAAoBvE,QAAA,CAAS,YAAY,EAAEC,KAAA,GAAQ,KAAKV,MAAA,CAAOkE,IAAA;MACpE,KAAKc,mBAAA,CAAoBvE,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAKV,MAAA,CAAOmE,GAAA;MAInE,KAAKe,YAAA,GAAe,IAAI1B,cAAA,CAAe;QACrC/C,QAAA,EAAUoD,aAAA,CAAcP,KAAA,CAAM6B,UAAA,CAAW1E,QAAQ;QACjDqD,YAAA,EAAcqB,UAAA,CAAWrB,YAAA;QACzBC,cAAA,EAAgBoB,UAAA,CAAWpB,cAAA;QAC3BqB,WAAA,EAAa;QACbC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZC,QAAA,EAAUC,cAAA;QACVC,QAAA,EAAUC,sBAAA;QACVC,aAAA,EAAeC,WAAA;QACfC,aAAA,EAAeL,cAAA;QACfM,aAAA,EAAeJ,sBAAA;QACfK,kBAAA,EAAoBH;QAAA;MAE5B,CAAO;MAED,KAAKI,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;MAErC,KAAKC,kBAAA,GAAqB,IAAInF,KAAA,CAAO;IACtC;IAEDoF,QAAA,EAAU;MAGR,KAAKnE,kBAAA,CAAmBmE,OAAA,CAAS;MACjC,KAAKrE,gBAAA,CAAiBqE,OAAA,CAAS;MAC/B,KAAKjD,kBAAA,CAAmBiD,OAAA,CAAS;MACjC,KAAKhD,qBAAA,CAAsBgD,OAAA,CAAS;MACpC,KAAK/C,eAAA,CAAgB+C,OAAA,CAAS;MAC9B,KAAK9C,gBAAA,CAAiB8C,OAAA,CAAS;MAC/B,KAAK5C,iBAAA,CAAkB4C,OAAA,CAAS;MAKhC,KAAK5B,cAAA,CAAe4B,OAAA,CAAS;MAC7B,KAAK1B,mBAAA,CAAoB0B,OAAA,CAAS;MAClC,KAAKvB,oBAAA,CAAqBuB,OAAA,CAAS;MACnC,KAAKtB,YAAA,CAAasB,OAAA,CAAS;MAC3B,KAAKpB,aAAA,CAAcoB,OAAA,CAAS;MAC5B,KAAKjB,YAAA,CAAaiB,OAAA,CAAS;MAC3B,KAAKnB,mBAAA,CAAoBmB,OAAA,CAAS;MAIlC,KAAKH,MAAA,CAAOG,OAAA,CAAS;IACtB;IAEDC,OAAOtG,QAAA,EAAUuG,WAAA,EAAsD;MAGrEvG,QAAA,CAASwG,eAAA,CAAgB,KAAKtE,kBAAkB;MAChDlC,QAAA,CAASQ,KAAA,CAAO;MAChB,IAAI,KAAKD,eAAA,EAAiB;QACxB,KAAKA,eAAA,CAAgBkG,OAAA,GAAU;QAC/B,KAAKlG,eAAA,CAAgBmG,QAAA,CAAS,KAAK1G,QAAA,EAAU,KAAKC,KAAA,EAAO,KAAKC,MAAM;QACpE,KAAKK,eAAA,CAAgBkG,OAAA,GAAU;MAChC;MAEDzG,QAAA,CAASsG,MAAA,CAAO,KAAKrG,KAAA,EAAO,KAAKC,MAAM;MACvC,IAAI,KAAKK,eAAA,EAAiB,KAAKA,eAAA,CAAgBkG,OAAA,GAAU;MAIzD,KAAKE,cAAA,CAAe3G,QAAA,EAAU,KAAKyE,cAAA,EAAgB,KAAKrB,kBAAA,EAAoB,GAAG,CAAC;MAIhF,IAAI,KAAKjC,SAAA,EAAW;QAClB,KAAKyF,eAAA,CAAgB5G,QAAA,EAAU,KAAK2E,mBAAA,EAAqB,KAAKtB,qBAAA,EAAuB,GAAG,CAAC;MAC1F;MAID,KAAK1B,WAAA,CAAYhB,QAAA,CAAS,SAAS,EAAEC,KAAA,GAAQ,KAAKH,OAAA;MAClD,KAAKkB,WAAA,CAAYhB,QAAA,CAAS,aAAa,EAAEC,KAAA,GAAQ,KAAKE,WAAA;MACtD,KAAKa,WAAA,CAAYhB,QAAA,CAAS,WAAW,EAAEC,KAAA,GAAQ,KAAKG,SAAA;MACpD,KAAK8F,UAAA,CAAW7G,QAAA,EAAU,KAAK2B,WAAA,EAAa,KAAK2B,eAAe;MAIhE,IAAI,KAAKnB,IAAA,EAAM;QACb,KAAK0E,UAAA,CAAW7G,QAAA,EAAU,KAAK+E,YAAA,EAAc,KAAKxB,gBAAgB;QAClE,KAAKsD,UAAA,CAAW7G,QAAA,EAAU,KAAKiF,aAAA,EAAe,KAAKxB,iBAAiB;MAErE;MAID,QAAQ,KAAK5C,MAAA;QACX,KAAKhB,QAAA,CAAQiH,MAAA,CAAOC,OAAA;UAClB,IAAI,KAAKzG,QAAA,EAAU;YACjB,KAAK8E,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;YACvE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;YAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAKpD,gBAAgB;YAElE,IAAI,KAAKG,IAAA,EAAM,KAAKiD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK6C,iBAAA,CAAkBxB,OAAA,MAChF,KAAKmD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK0C,eAAA,CAAgBrB,OAAA;YACzE,KAAKmD,YAAA,CAAalB,QAAA,GAAW8C,cAAA;YAC7B,KAAKH,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAKpD,gBAAgB;YAElE,KAAKoD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKoB,gBAAA,CAAiBC,OAAA;YACrE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;YAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UACjG,OAAiB;YACL,KAAKnB,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;YACvE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;YAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;YAErF,IAAI,KAAKpE,IAAA,EAAM,KAAKiD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK6C,iBAAA,CAAkBxB,OAAA,MAChF,KAAKmD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK0C,eAAA,CAAgBrB,OAAA;YACzE,KAAKmD,YAAA,CAAalB,QAAA,GAAW8C,cAAA;YAC7B,KAAKH,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UACtF;UAED;QACF,KAAK1G,QAAA,CAAQiH,MAAA,CAAOI,GAAA;UAClB,IAAI,KAAK/E,IAAA,EAAM,KAAKiD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK6C,iBAAA,CAAkBxB,OAAA,MAChF,KAAKmD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK0C,eAAA,CAAgBrB,OAAA;UACzE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UAErF,IAAI,KAAKjG,QAAA,EAAU;YACjB,IAAI,KAAK6B,IAAA,EAAM,KAAKiD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK6C,iBAAA,CAAkBxB,OAAA,MAChF,KAAKmD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;YAC5E,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;YAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAKpD,gBAAgB;YAElE,KAAKoD,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAK0C,eAAA,CAAgBrB,OAAA;YACpE,KAAKmD,YAAA,CAAalB,QAAA,GAAW8C,cAAA;YAC7B,KAAKH,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAKpD,gBAAgB;UACnE;UAED;QAEF,KAAKnC,QAAA,CAAQiH,MAAA,CAAOK,MAAA;UAClB,KAAK/B,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKsB,kBAAA,CAAmBD,OAAA;UACvE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UAErF;QAEF,KAAK1G,QAAA,CAAQiH,MAAA,CAAOM,KAAA;UAClB,KAAKP,UAAA,CAAW7G,QAAA,EAAU,KAAKkF,mBAAA,EAAqB,KAAK+B,cAAA,GAAiB,OAAOV,WAAW;UAE5F;QAEF,KAAK1G,QAAA,CAAQiH,MAAA,CAAOO,MAAA;UAClB,KAAKjC,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKwC,kBAAA,CAAmBnB,OAAA;UACvE,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UAErF;QAEF,KAAK1G,QAAA,CAAQiH,MAAA,CAAOQ,SAAA;UAClB,KAAKlC,YAAA,CAAazE,QAAA,CAAS,UAAU,EAAEC,KAAA,GAAQ,KAAKyC,qBAAA,CAAsBpB,OAAA;UAC1E,KAAKmD,YAAA,CAAalB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAW7G,QAAA,EAAU,KAAKoF,YAAA,EAAc,KAAK6B,cAAA,GAAiB,OAAOV,WAAW;UAErF;QAEF;UACEgB,OAAA,CAAQC,IAAA,CAAK,qCAAqC;MACrD;IACF;IAEDX,WAAW7G,QAAA,EAAUyH,YAAA,EAAcC,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAEvE,KAAKxB,kBAAA,CAAmB9B,IAAA,CAAKtE,QAAA,CAAS6H,aAAA,CAAc,KAAK7G,SAAS,CAAC;MACnE,MAAM8G,kBAAA,GAAqB9H,QAAA,CAAS+H,aAAA,CAAc,KAAK/G,SAAS;MAChE,MAAMgH,iBAAA,GAAoBhI,QAAA,CAASiI,SAAA;MAEnCjI,QAAA,CAASwG,eAAA,CAAgBkB,YAAY;MAGrC1H,QAAA,CAASiI,SAAA,GAAY;MACrB,IAAIN,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnD3H,QAAA,CAASkI,aAAA,CAAcP,UAAU;QACjC3H,QAAA,CAASmI,aAAA,CAAcP,UAAA,IAAc,CAAG;QACxC5H,QAAA,CAASQ,KAAA,CAAO;MACjB;MAED,KAAK0F,MAAA,CAAOkC,QAAA,GAAWX,YAAA;MACvB,KAAKvB,MAAA,CAAOI,MAAA,CAAOtG,QAAQ;MAG3BA,QAAA,CAASiI,SAAA,GAAYD,iBAAA;MACrBhI,QAAA,CAASkI,aAAA,CAAc,KAAK9B,kBAAkB;MAC9CpG,QAAA,CAASmI,aAAA,CAAcL,kBAAkB;IAC1C;IAEDnB,eAAe3G,QAAA,EAAUqI,gBAAA,EAAkBX,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAC/E,KAAKxB,kBAAA,CAAmB9B,IAAA,CAAKtE,QAAA,CAAS6H,aAAA,CAAc,KAAK7G,SAAS,CAAC;MACnE,MAAM8G,kBAAA,GAAqB9H,QAAA,CAAS+H,aAAA,CAAc,KAAK/G,SAAS;MAChE,MAAMgH,iBAAA,GAAoBhI,QAAA,CAASiI,SAAA;MAEnCjI,QAAA,CAASwG,eAAA,CAAgBkB,YAAY;MACrC1H,QAAA,CAASiI,SAAA,GAAY;MAErBN,UAAA,GAAaU,gBAAA,CAAiBV,UAAA,IAAcA,UAAA;MAC5CC,UAAA,GAAaS,gBAAA,CAAiBT,UAAA,IAAcA,UAAA;MAE5C,IAAID,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnD3H,QAAA,CAASkI,aAAA,CAAcP,UAAU;QACjC3H,QAAA,CAASmI,aAAA,CAAcP,UAAA,IAAc,CAAG;QACxC5H,QAAA,CAASQ,KAAA,CAAO;MACjB;MAED,KAAKP,KAAA,CAAMoI,gBAAA,GAAmBA,gBAAA;MAC9BrI,QAAA,CAASsG,MAAA,CAAO,KAAKrG,KAAA,EAAO,KAAKC,MAAM;MACvC,KAAKD,KAAA,CAAMoI,gBAAA,GAAmB;MAI9BrI,QAAA,CAASiI,SAAA,GAAYD,iBAAA;MACrBhI,QAAA,CAASkI,aAAA,CAAc,KAAK9B,kBAAkB;MAC9CpG,QAAA,CAASmI,aAAA,CAAcL,kBAAkB;IAC1C;IAEDlB,gBAAgB5G,QAAA,EAAUqI,gBAAA,EAAkBX,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAChF,KAAKxB,kBAAA,CAAmB9B,IAAA,CAAKtE,QAAA,CAAS6H,aAAA,CAAc,KAAK7G,SAAS,CAAC;MACnE,MAAM8G,kBAAA,GAAqB9H,QAAA,CAAS+H,aAAA,CAAc,KAAK/G,SAAS;MAChE,MAAMgH,iBAAA,GAAoBhI,QAAA,CAASiI,SAAA;MAEnCjI,QAAA,CAASwG,eAAA,CAAgBkB,YAAY;MACrC1H,QAAA,CAASiI,SAAA,GAAY;MAErBN,UAAA,GAAaU,gBAAA,CAAiBV,UAAA,IAAcA,UAAA;MAC5CC,UAAA,GAAaS,gBAAA,CAAiBT,UAAA,IAAcA,UAAA;MAE5C,IAAID,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnD3H,QAAA,CAASkI,aAAA,CAAcP,UAAU;QACjC3H,QAAA,CAASmI,aAAA,CAAcP,UAAA,IAAc,CAAG;QACxC5H,QAAA,CAASQ,KAAA,CAAO;MACjB;MAED,KAAKP,KAAA,CAAMqI,eAAA,CAAiBC,KAAA,IAAU;QACpCA,KAAA,CAAMC,sBAAA,GAAyBD,KAAA,CAAMH,QAAA;QACrC,IAAI,KAAKlH,QAAA,CAASuH,QAAA,CAASF,KAAK,GAAG;UACjCA,KAAA,CAAMH,QAAA,GAAW,KAAKzD,mBAAA;QAChC,OAAe;UACL4D,KAAA,CAAMH,QAAA,GAAW,KAAKtD,oBAAA;QACvB;MACT,CAAO;MACD9E,QAAA,CAASsG,MAAA,CAAO,KAAKrG,KAAA,EAAO,KAAKC,MAAM;MACvC,KAAKD,KAAA,CAAMqI,eAAA,CAAiBC,KAAA,IAAU;QACpCA,KAAA,CAAMH,QAAA,GAAWG,KAAA,CAAMC,sBAAA;MAC/B,CAAO;MAIDxI,QAAA,CAASiI,SAAA,GAAYD,iBAAA;MACrBhI,QAAA,CAASkI,aAAA,CAAc,KAAK9B,kBAAkB;MAC9CpG,QAAA,CAASmI,aAAA,CAAcL,kBAAkB;IAC1C;IAEDY,QAAQvI,KAAA,EAAOC,MAAA,EAAQ;MACrB,KAAKD,KAAA,GAAQA,KAAA;MACb,KAAKC,MAAA,GAASA,MAAA;MAEd,KAAKuB,WAAA,CAAYC,OAAA,CAAQgC,QAAA,GAAWC,IAAA,CAAKC,IAAA,CAAK3D,KAAA,GAAQA,KAAA,GAAQC,MAAA,GAASA,MAAM;MAC7E,KAAKuB,WAAA,CAAYG,WAAA,GAAc;MAC/B,KAAKI,kBAAA,CAAmBwG,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAC7C,KAAK4B,gBAAA,CAAiB0G,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAC3C,KAAKkD,eAAA,CAAgBoF,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAC1C,KAAKgD,kBAAA,CAAmBsF,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAC7C,KAAKiD,qBAAA,CAAsBqF,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAChD,KAAKmD,gBAAA,CAAiBmF,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAC3C,KAAKqD,iBAAA,CAAkBiF,OAAA,CAAQvI,KAAA,EAAOC,MAAM;MAG5C,KAAKuB,WAAA,CAAYhB,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAItB,KAAA,EAAOC,MAAM;MAC/D,KAAKuB,WAAA,CAAYhB,QAAA,CAAS,wBAAwB,EAAEC,KAAA,CAAM0D,IAAA,CAAK,KAAKpE,MAAA,CAAOqE,gBAAgB;MAC3F,KAAK5C,WAAA,CAAYhB,QAAA,CAAS,+BAA+B,EAAEC,KAAA,CAAM0D,IAAA,CAAK,KAAKpE,MAAA,CAAOsE,uBAAuB;MAEzG,KAAKO,YAAA,CAAapE,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAItB,KAAA,EAAOC,MAAM;MAChE,KAAK6E,aAAA,CAActE,QAAA,CAAS,YAAY,EAAEC,KAAA,CAAMa,GAAA,CAAItB,KAAA,EAAOC,MAAM;IAClE;EACF;EA5gBD,IAAMuI,QAAA,GAAN9I,QAAA;EACE+I,aAAA,CADID,QAAA,EACG,UAAS;IACd5B,OAAA,EAAS;IACTG,GAAA,EAAK;IACLC,MAAA,EAAQ;IACRC,KAAA,EAAO;IACPC,MAAA,EAAQ;IACRC,SAAA,EAAW;EACZ;EAsgBH,OAAOqB,QAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}