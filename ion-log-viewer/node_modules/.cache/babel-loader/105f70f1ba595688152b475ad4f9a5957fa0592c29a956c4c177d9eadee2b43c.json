{"ast": null, "code": "function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nexport { _setPrototypeOf as _, _isNativeReflectConstruct as a };", "map": {"version": 3, "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "prototype", "valueOf", "call", "e", "_", "a"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js"], "sourcesContent": ["function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport { _setPrototypeOf as _, _isNativeReflectConstruct as a };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7BF,eAAe,GAAGG,MAAM,CAACC,cAAc,IAAI,SAASJ,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACI,SAAS,GAAGH,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASI,yBAAyBA,CAAA,EAAG;EACnC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EACtE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;EACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAE5C,IAAI;IACFC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACP,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASf,eAAe,IAAIgB,CAAC,EAAEV,yBAAyB,IAAIW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}