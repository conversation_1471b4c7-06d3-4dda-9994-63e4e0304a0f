{"ast": null, "code": "import { BufferGeometry, Float32BufferAttribute } from \"three\";\nclass BoxLineGeometry extends BufferGeometry {\n  constructor(width, height, depth, widthSegments, heightSegments, depthSegments) {\n    super();\n    width = width || 1;\n    height = height || 1;\n    depth = depth || 1;\n    widthSegments = Math.floor(widthSegments) || 1;\n    heightSegments = Math.floor(heightSegments) || 1;\n    depthSegments = Math.floor(depthSegments) || 1;\n    const widthHalf = width / 2;\n    const heightHalf = height / 2;\n    const depthHalf = depth / 2;\n    const segmentWidth = width / widthSegments;\n    const segmentHeight = height / heightSegments;\n    const segmentDepth = depth / depthSegments;\n    const vertices = [];\n    let x = -widthHalf,\n      y = -heightHalf,\n      z = -depthHalf;\n    for (let i = 0; i <= widthSegments; i++) {\n      vertices.push(x, -heightHalf, -depthHalf, x, heightHalf, -depthHalf);\n      vertices.push(x, heightHalf, -depthHalf, x, heightHalf, depthHalf);\n      vertices.push(x, heightHalf, depthHalf, x, -heightHalf, depthHalf);\n      vertices.push(x, -heightHalf, depthHalf, x, -heightHalf, -depthHalf);\n      x += segmentWidth;\n    }\n    for (let i = 0; i <= heightSegments; i++) {\n      vertices.push(-widthHalf, y, -depthHalf, widthHalf, y, -depthHalf);\n      vertices.push(widthHalf, y, -depthHalf, widthHalf, y, depthHalf);\n      vertices.push(widthHalf, y, depthHalf, -widthHalf, y, depthHalf);\n      vertices.push(-widthHalf, y, depthHalf, -widthHalf, y, -depthHalf);\n      y += segmentHeight;\n    }\n    for (let i = 0; i <= depthSegments; i++) {\n      vertices.push(-widthHalf, -heightHalf, z, -widthHalf, heightHalf, z);\n      vertices.push(-widthHalf, heightHalf, z, widthHalf, heightHalf, z);\n      vertices.push(widthHalf, heightHalf, z, widthHalf, -heightHalf, z);\n      vertices.push(widthHalf, -heightHalf, z, -widthHalf, -heightHalf, z);\n      z += segmentDepth;\n    }\n    this.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n  }\n}\nexport { BoxLineGeometry };", "map": {"version": 3, "names": ["BoxLineGeometry", "BufferGeometry", "constructor", "width", "height", "depth", "widthSegments", "heightSegments", "depthSegments", "Math", "floor", "widthHalf", "heightHalf", "depthHalf", "segmentWidth", "segmentHeight", "segmentDepth", "vertices", "x", "y", "z", "i", "push", "setAttribute", "Float32BufferAttribute"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/geometries/BoxLineGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute } from 'three'\n\nclass BoxLineGeometry extends BufferGeometry {\n  constructor(width, height, depth, widthSegments, heightSegments, depthSegments) {\n    super()\n\n    width = width || 1\n    height = height || 1\n    depth = depth || 1\n\n    widthSegments = Math.floor(widthSegments) || 1\n    heightSegments = Math.floor(heightSegments) || 1\n    depthSegments = Math.floor(depthSegments) || 1\n\n    const widthHalf = width / 2\n    const heightHalf = height / 2\n    const depthHalf = depth / 2\n\n    const segmentWidth = width / widthSegments\n    const segmentHeight = height / heightSegments\n    const segmentDepth = depth / depthSegments\n\n    const vertices = []\n\n    let x = -widthHalf,\n      y = -heightHalf,\n      z = -depthHalf\n\n    for (let i = 0; i <= widthSegments; i++) {\n      vertices.push(x, -heightHalf, -depthHalf, x, heightHalf, -depthHalf)\n      vertices.push(x, heightHalf, -depthHalf, x, heightHalf, depthHalf)\n      vertices.push(x, heightHalf, depthHalf, x, -heightHalf, depthHalf)\n      vertices.push(x, -heightHalf, depthHalf, x, -heightHalf, -depthHalf)\n\n      x += segmentWidth\n    }\n\n    for (let i = 0; i <= heightSegments; i++) {\n      vertices.push(-widthHalf, y, -depthHalf, widthHalf, y, -depthHalf)\n      vertices.push(widthHalf, y, -depthHalf, widthHalf, y, depthHalf)\n      vertices.push(widthHalf, y, depthHalf, -widthHalf, y, depthHalf)\n      vertices.push(-widthHalf, y, depthHalf, -widthHalf, y, -depthHalf)\n\n      y += segmentHeight\n    }\n\n    for (let i = 0; i <= depthSegments; i++) {\n      vertices.push(-widthHalf, -heightHalf, z, -widthHalf, heightHalf, z)\n      vertices.push(-widthHalf, heightHalf, z, widthHalf, heightHalf, z)\n      vertices.push(widthHalf, heightHalf, z, widthHalf, -heightHalf, z)\n      vertices.push(widthHalf, -heightHalf, z, -widthHalf, -heightHalf, z)\n\n      z += segmentDepth\n    }\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n  }\n}\n\nexport { BoxLineGeometry }\n"], "mappings": ";AAEA,MAAMA,eAAA,SAAwBC,cAAA,CAAe;EAC3CC,YAAYC,KAAA,EAAOC,MAAA,EAAQC,KAAA,EAAOC,aAAA,EAAeC,cAAA,EAAgBC,aAAA,EAAe;IAC9E,MAAO;IAEPL,KAAA,GAAQA,KAAA,IAAS;IACjBC,MAAA,GAASA,MAAA,IAAU;IACnBC,KAAA,GAAQA,KAAA,IAAS;IAEjBC,aAAA,GAAgBG,IAAA,CAAKC,KAAA,CAAMJ,aAAa,KAAK;IAC7CC,cAAA,GAAiBE,IAAA,CAAKC,KAAA,CAAMH,cAAc,KAAK;IAC/CC,aAAA,GAAgBC,IAAA,CAAKC,KAAA,CAAMF,aAAa,KAAK;IAE7C,MAAMG,SAAA,GAAYR,KAAA,GAAQ;IAC1B,MAAMS,UAAA,GAAaR,MAAA,GAAS;IAC5B,MAAMS,SAAA,GAAYR,KAAA,GAAQ;IAE1B,MAAMS,YAAA,GAAeX,KAAA,GAAQG,aAAA;IAC7B,MAAMS,aAAA,GAAgBX,MAAA,GAASG,cAAA;IAC/B,MAAMS,YAAA,GAAeX,KAAA,GAAQG,aAAA;IAE7B,MAAMS,QAAA,GAAW,EAAE;IAEnB,IAAIC,CAAA,GAAI,CAACP,SAAA;MACPQ,CAAA,GAAI,CAACP,UAAA;MACLQ,CAAA,GAAI,CAACP,SAAA;IAEP,SAASQ,CAAA,GAAI,GAAGA,CAAA,IAAKf,aAAA,EAAee,CAAA,IAAK;MACvCJ,QAAA,CAASK,IAAA,CAAKJ,CAAA,EAAG,CAACN,UAAA,EAAY,CAACC,SAAA,EAAWK,CAAA,EAAGN,UAAA,EAAY,CAACC,SAAS;MACnEI,QAAA,CAASK,IAAA,CAAKJ,CAAA,EAAGN,UAAA,EAAY,CAACC,SAAA,EAAWK,CAAA,EAAGN,UAAA,EAAYC,SAAS;MACjEI,QAAA,CAASK,IAAA,CAAKJ,CAAA,EAAGN,UAAA,EAAYC,SAAA,EAAWK,CAAA,EAAG,CAACN,UAAA,EAAYC,SAAS;MACjEI,QAAA,CAASK,IAAA,CAAKJ,CAAA,EAAG,CAACN,UAAA,EAAYC,SAAA,EAAWK,CAAA,EAAG,CAACN,UAAA,EAAY,CAACC,SAAS;MAEnEK,CAAA,IAAKJ,YAAA;IACN;IAED,SAASO,CAAA,GAAI,GAAGA,CAAA,IAAKd,cAAA,EAAgBc,CAAA,IAAK;MACxCJ,QAAA,CAASK,IAAA,CAAK,CAACX,SAAA,EAAWQ,CAAA,EAAG,CAACN,SAAA,EAAWF,SAAA,EAAWQ,CAAA,EAAG,CAACN,SAAS;MACjEI,QAAA,CAASK,IAAA,CAAKX,SAAA,EAAWQ,CAAA,EAAG,CAACN,SAAA,EAAWF,SAAA,EAAWQ,CAAA,EAAGN,SAAS;MAC/DI,QAAA,CAASK,IAAA,CAAKX,SAAA,EAAWQ,CAAA,EAAGN,SAAA,EAAW,CAACF,SAAA,EAAWQ,CAAA,EAAGN,SAAS;MAC/DI,QAAA,CAASK,IAAA,CAAK,CAACX,SAAA,EAAWQ,CAAA,EAAGN,SAAA,EAAW,CAACF,SAAA,EAAWQ,CAAA,EAAG,CAACN,SAAS;MAEjEM,CAAA,IAAKJ,aAAA;IACN;IAED,SAASM,CAAA,GAAI,GAAGA,CAAA,IAAKb,aAAA,EAAea,CAAA,IAAK;MACvCJ,QAAA,CAASK,IAAA,CAAK,CAACX,SAAA,EAAW,CAACC,UAAA,EAAYQ,CAAA,EAAG,CAACT,SAAA,EAAWC,UAAA,EAAYQ,CAAC;MACnEH,QAAA,CAASK,IAAA,CAAK,CAACX,SAAA,EAAWC,UAAA,EAAYQ,CAAA,EAAGT,SAAA,EAAWC,UAAA,EAAYQ,CAAC;MACjEH,QAAA,CAASK,IAAA,CAAKX,SAAA,EAAWC,UAAA,EAAYQ,CAAA,EAAGT,SAAA,EAAW,CAACC,UAAA,EAAYQ,CAAC;MACjEH,QAAA,CAASK,IAAA,CAAKX,SAAA,EAAW,CAACC,UAAA,EAAYQ,CAAA,EAAG,CAACT,SAAA,EAAW,CAACC,UAAA,EAAYQ,CAAC;MAEnEA,CAAA,IAAKJ,YAAA;IACN;IAED,KAAKO,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBP,QAAA,EAAU,CAAC,CAAC;EACtE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}