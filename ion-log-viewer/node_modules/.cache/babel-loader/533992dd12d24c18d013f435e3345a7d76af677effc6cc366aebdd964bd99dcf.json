{"ast": null, "code": "import { Lo<PERSON>, <PERSON>Loader, Number<PERSON>eyframeTrack, <PERSON>Clip, BufferAttribute } from \"three\";\nclass MDDLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.load(url, function (data) {\n      onLoad(scope.parse(data));\n    }, onProgress, onError);\n  }\n  parse(data) {\n    const view = new DataView(data);\n    const totalFrames = view.getUint32(0);\n    const totalPoints = view.getUint32(4);\n    let offset = 8;\n    const times = new Float32Array(totalFrames);\n    const values = new Float32Array(totalFrames * totalFrames).fill(0);\n    for (let i = 0; i < totalFrames; i++) {\n      times[i] = view.getFloat32(offset);\n      offset += 4;\n      values[totalFrames * i + i] = 1;\n    }\n    const track = new NumberKeyframeTrack(\".morphTargetInfluences\", times, values);\n    const clip = new AnimationClip(\"default\", times[times.length - 1], [track]);\n    const morphTargets = [];\n    for (let i = 0; i < totalFrames; i++) {\n      const morphTarget = new Float32Array(totalPoints * 3);\n      for (let j = 0; j < totalPoints; j++) {\n        const stride = j * 3;\n        morphTarget[stride + 0] = view.getFloat32(offset);\n        offset += 4;\n        morphTarget[stride + 1] = view.getFloat32(offset);\n        offset += 4;\n        morphTarget[stride + 2] = view.getFloat32(offset);\n        offset += 4;\n      }\n      const attribute = new BufferAttribute(morphTarget, 3);\n      attribute.name = \"morph_\" + i;\n      morphTargets.push(attribute);\n    }\n    return {\n      morphTargets,\n      clip\n    };\n  }\n}\nexport { MDDLoader };", "map": {"version": 3, "names": ["MDDLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "data", "parse", "view", "DataView", "totalFrames", "getUint32", "totalPoints", "offset", "times", "Float32Array", "values", "fill", "i", "getFloat32", "track", "NumberKeyframeTrack", "clip", "AnimationClip", "length", "morphTargets", "morph<PERSON>arget", "j", "stride", "attribute", "BufferAttribute", "name", "push"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/MDDLoader.js"], "sourcesContent": ["/**\n * MDD is a special format that stores a position for every vertex in a model for every frame in an animation.\n * Similar to BVH, it can be used to transfer animation data between different 3D applications or engines.\n *\n * MDD stores its data in binary format (big endian) in the following way:\n *\n * number of frames (a single uint32)\n * number of vertices (a single uint32)\n * time values for each frame (sequence of float32)\n * vertex data for each frame (sequence of float32)\n */\n\nimport { AnimationClip, BufferAttribute, FileLoader, Loader, NumberKeyframeTrack } from 'three'\n\nclass MDDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.load(\n      url,\n      function (data) {\n        onLoad(scope.parse(data))\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    const view = new DataView(data)\n\n    const totalFrames = view.getUint32(0)\n    const totalPoints = view.getUint32(4)\n\n    let offset = 8\n\n    // animation clip\n\n    const times = new Float32Array(totalFrames)\n    const values = new Float32Array(totalFrames * totalFrames).fill(0)\n\n    for (let i = 0; i < totalFrames; i++) {\n      times[i] = view.getFloat32(offset)\n      offset += 4\n      values[totalFrames * i + i] = 1\n    }\n\n    const track = new NumberKeyframeTrack('.morphTargetInfluences', times, values)\n    const clip = new AnimationClip('default', times[times.length - 1], [track])\n\n    // morph targets\n\n    const morphTargets = []\n\n    for (let i = 0; i < totalFrames; i++) {\n      const morphTarget = new Float32Array(totalPoints * 3)\n\n      for (let j = 0; j < totalPoints; j++) {\n        const stride = j * 3\n\n        morphTarget[stride + 0] = view.getFloat32(offset)\n        offset += 4 // x\n        morphTarget[stride + 1] = view.getFloat32(offset)\n        offset += 4 // y\n        morphTarget[stride + 2] = view.getFloat32(offset)\n        offset += 4 // z\n      }\n\n      const attribute = new BufferAttribute(morphTarget, 3)\n      attribute.name = 'morph_' + i\n\n      morphTargets.push(attribute)\n    }\n\n    return {\n      morphTargets: morphTargets,\n      clip: clip,\n    }\n  }\n}\n\nexport { MDDLoader }\n"], "mappings": ";AAcA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKR,OAAO;IAC1CO,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;IACxBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUU,IAAA,EAAM;MACdT,MAAA,CAAOG,KAAA,CAAMO,KAAA,CAAMD,IAAI,CAAC;IACzB,GACDR,UAAA,EACAC,OACD;EACF;EAEDQ,MAAMD,IAAA,EAAM;IACV,MAAME,IAAA,GAAO,IAAIC,QAAA,CAASH,IAAI;IAE9B,MAAMI,WAAA,GAAcF,IAAA,CAAKG,SAAA,CAAU,CAAC;IACpC,MAAMC,WAAA,GAAcJ,IAAA,CAAKG,SAAA,CAAU,CAAC;IAEpC,IAAIE,MAAA,GAAS;IAIb,MAAMC,KAAA,GAAQ,IAAIC,YAAA,CAAaL,WAAW;IAC1C,MAAMM,MAAA,GAAS,IAAID,YAAA,CAAaL,WAAA,GAAcA,WAAW,EAAEO,IAAA,CAAK,CAAC;IAEjE,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,WAAA,EAAaQ,CAAA,IAAK;MACpCJ,KAAA,CAAMI,CAAC,IAAIV,IAAA,CAAKW,UAAA,CAAWN,MAAM;MACjCA,MAAA,IAAU;MACVG,MAAA,CAAON,WAAA,GAAcQ,CAAA,GAAIA,CAAC,IAAI;IAC/B;IAED,MAAME,KAAA,GAAQ,IAAIC,mBAAA,CAAoB,0BAA0BP,KAAA,EAAOE,MAAM;IAC7E,MAAMM,IAAA,GAAO,IAAIC,aAAA,CAAc,WAAWT,KAAA,CAAMA,KAAA,CAAMU,MAAA,GAAS,CAAC,GAAG,CAACJ,KAAK,CAAC;IAI1E,MAAMK,YAAA,GAAe,EAAE;IAEvB,SAASP,CAAA,GAAI,GAAGA,CAAA,GAAIR,WAAA,EAAaQ,CAAA,IAAK;MACpC,MAAMQ,WAAA,GAAc,IAAIX,YAAA,CAAaH,WAAA,GAAc,CAAC;MAEpD,SAASe,CAAA,GAAI,GAAGA,CAAA,GAAIf,WAAA,EAAae,CAAA,IAAK;QACpC,MAAMC,MAAA,GAASD,CAAA,GAAI;QAEnBD,WAAA,CAAYE,MAAA,GAAS,CAAC,IAAIpB,IAAA,CAAKW,UAAA,CAAWN,MAAM;QAChDA,MAAA,IAAU;QACVa,WAAA,CAAYE,MAAA,GAAS,CAAC,IAAIpB,IAAA,CAAKW,UAAA,CAAWN,MAAM;QAChDA,MAAA,IAAU;QACVa,WAAA,CAAYE,MAAA,GAAS,CAAC,IAAIpB,IAAA,CAAKW,UAAA,CAAWN,MAAM;QAChDA,MAAA,IAAU;MACX;MAED,MAAMgB,SAAA,GAAY,IAAIC,eAAA,CAAgBJ,WAAA,EAAa,CAAC;MACpDG,SAAA,CAAUE,IAAA,GAAO,WAAWb,CAAA;MAE5BO,YAAA,CAAaO,IAAA,CAAKH,SAAS;IAC5B;IAED,OAAO;MACLJ,YAAA;MACAH;IACD;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}