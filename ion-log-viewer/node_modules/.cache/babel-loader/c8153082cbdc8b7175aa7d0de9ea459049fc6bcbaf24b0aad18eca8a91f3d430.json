{"ast": null, "code": "export { default } from \"./palette.js\";\nexport * from \"./palette.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@mui/system/esm/palette/index.js"], "sourcesContent": ["export { default } from \"./palette.js\";\nexport * from \"./palette.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}