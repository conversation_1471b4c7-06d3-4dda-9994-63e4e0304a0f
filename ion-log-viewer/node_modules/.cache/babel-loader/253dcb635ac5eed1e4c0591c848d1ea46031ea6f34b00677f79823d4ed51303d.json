{"ast": null, "code": "export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHHelper } from './objects/MeshBVHHelper.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport * from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';\nimport * as _BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\nexport { _BVHShaderGLSL as BVHShaderGLSL }; // backwards compatibility\nimport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\nexport const shaderStructs = BVHShaderGLSL.bvh_struct_definitions;\nexport const shaderDistanceFunction = BVHShaderGLSL.bvh_distance_functions;\nexport const shaderIntersectFunction = `\n\t${BVHShaderGLSL.common_functions}\n\t${BVHShaderGLSL.bvh_ray_functions}\n`;", "map": {"version": 3, "names": ["MeshBVH", "MeshBVHHelper", "CENTER", "AVERAGE", "SAH", "NOT_INTERSECTED", "INTERSECTED", "CONTAINED", "getBVHExtremes", "estimateMemoryInBytes", "getJSONStructure", "validateBounds", "getTriangleHitPointInfo", "_BVHShaderGLSL", "BVHShaderGLSL", "shaderStructs", "bvh_struct_definitions", "shaderDistanceFunction", "bvh_distance_functions", "shaderIntersectFunction", "common_functions", "bvh_ray_functions"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/index.js"], "sourcesContent": ["export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHHelper } from './objects/MeshBVHHelper.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport * from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';\nexport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\n\n// backwards compatibility\nimport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\nexport const shaderStructs = BVHShaderGLSL.bvh_struct_definitions;\nexport const shaderDistanceFunction = BVHShaderGLSL.bvh_distance_functions;\nexport const shaderIntersectFunction = `\n\t${ BVHShaderGLSL.common_functions }\n\t${ BVHShaderGLSL.bvh_ray_functions }\n`;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,QAAQ,qBAAqB;AACnG,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,kBAAkB;AAC1G,cAAc,+BAA+B;AAC7C,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,cAAc,4BAA4B;AAC1C,cAAc,uBAAuB;AACrC,cAAc,+BAA+B;AAC7C,cAAc,iCAAiC;AAC/C,cAAc,oCAAoC;AAAC,YAAAC,cAAA,MACpB,wBAAwB;AAAA,SAAAA,cAAA,IAA3CC,aAAa,IAEzB;AACA,OAAO,KAAKA,aAAa,MAAM,wBAAwB;AACvD,OAAO,MAAMC,aAAa,GAAGD,aAAa,CAACE,sBAAsB;AACjE,OAAO,MAAMC,sBAAsB,GAAGH,aAAa,CAACI,sBAAsB;AAC1E,OAAO,MAAMC,uBAAuB,GAAG;AACvC,GAAIL,aAAa,CAACM,gBAAgB;AAClC,GAAIN,aAAa,CAACO,iBAAiB;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}