{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { Import } from \"./IonImport\";\nimport { LocalSymbolTable } from \"./IonLocalSymbolTable\";\nimport { SubstituteSymbolTable } from \"./IonSubstituteSymbolTable\";\nimport { getSystemSymbolTableImport } from \"./IonSystemSymbolTable\";\nimport { IonTypes } from \"./Ion\";\nexport const ion_symbol_table = \"$ion_symbol_table\";\nexport const ion_symbol_table_sid = 3;\nconst empty_struct = {};\nfunction load_imports(reader, catalog) {\n  let import_ = getSystemSymbolTableImport();\n  reader.stepIn();\n  while (reader.next()) {\n    reader.stepIn();\n    let name = null;\n    let version = 1;\n    let maxId = null;\n    while (reader.next()) {\n      switch (reader.fieldName()) {\n        case \"name\":\n          name = reader.stringValue();\n          break;\n        case \"version\":\n          version = reader.numberValue();\n          break;\n        case \"max_id\":\n          maxId = reader.numberValue();\n      }\n    }\n    if (version === null || version < 1) {\n      version = 1;\n    }\n    if (name && name !== \"$ion\") {\n      let symbolTable = catalog.getVersion(name, version);\n      if (!symbolTable) {\n        if (maxId === undefined) {\n          throw new Error(`No exact match found when trying to import symbol table ${name} version ${version}`);\n        } else {\n          symbolTable = catalog.getTable(name);\n        }\n      }\n      if (!symbolTable) {\n        symbolTable = new SubstituteSymbolTable(maxId);\n      }\n      import_ = new Import(import_, symbolTable, maxId);\n    }\n    reader.stepOut();\n  }\n  reader.stepOut();\n  return import_;\n}\nfunction load_symbols(reader) {\n  const symbols = [];\n  reader.stepIn();\n  while (reader.next()) {\n    symbols.push(reader.stringValue());\n  }\n  reader.stepOut();\n  return symbols;\n}\nexport function makeSymbolTable(catalog, reader, currentSymbolTable) {\n  let import_ = null;\n  let symbols = [];\n  let foundSymbols = false;\n  let foundImports = false;\n  let foundLstAppend = false;\n  reader.stepIn();\n  while (reader.next()) {\n    switch (reader.fieldName()) {\n      case \"imports\":\n        if (foundImports) {\n          throw new Error(\"Multiple import fields found.\");\n        }\n        let ion_type = reader.type();\n        if (ion_type === IonTypes.SYMBOL && reader.stringValue() === ion_symbol_table) {\n          import_ = currentSymbolTable.import;\n          let symbols_ = symbols;\n          symbols = currentSymbolTable.symbols;\n          symbols.push(...symbols_);\n          foundLstAppend = true;\n        } else if (ion_type === IonTypes.LIST) {\n          import_ = load_imports(reader, catalog);\n        } else {\n          throw new Error(`Expected import field name to be a list or symbol found ${ion_type}`);\n        }\n        foundImports = true;\n        break;\n      case \"symbols\":\n        if (foundSymbols) {\n          throw new Error(\"Multiple symbol fields found.\");\n        }\n        if (foundLstAppend) {\n          symbols.push(...load_symbols(reader));\n        } else {\n          symbols = load_symbols(reader);\n        }\n        foundSymbols = true;\n        break;\n    }\n  }\n  reader.stepOut();\n  return new LocalSymbolTable(import_, symbols);\n}", "map": {"version": 3, "names": ["Import", "LocalSymbolTable", "SubstituteSymbolTable", "getSystemSymbolTableImport", "IonTypes", "ion_symbol_table", "ion_symbol_table_sid", "empty_struct", "load_imports", "reader", "catalog", "import_", "stepIn", "next", "name", "version", "maxId", "fieldName", "stringValue", "numberValue", "symbolTable", "getVersion", "undefined", "Error", "getTable", "stepOut", "load_symbols", "symbols", "push", "makeSymbolTable", "currentSymbolTable", "foundSymbols", "foundImports", "foundLstAppend", "ion_type", "type", "SYMBOL", "import", "symbols_", "LIST"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSymbols.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nimport { Import } from \"./IonImport\";\nimport { LocalSymbolTable } from \"./IonLocalSymbolTable\";\nimport { SubstituteSymbolTable } from \"./IonSubstituteSymbolTable\";\nimport { getSystemSymbolTableImport } from \"./IonSystemSymbolTable\";\nimport { IonTypes } from \"./Ion\";\nexport const ion_symbol_table = \"$ion_symbol_table\";\nexport const ion_symbol_table_sid = 3;\nconst empty_struct = {};\nfunction load_imports(reader, catalog) {\n    let import_ = getSystemSymbolTableImport();\n    reader.stepIn();\n    while (reader.next()) {\n        reader.stepIn();\n        let name = null;\n        let version = 1;\n        let maxId = null;\n        while (reader.next()) {\n            switch (reader.fieldName()) {\n                case \"name\":\n                    name = reader.stringValue();\n                    break;\n                case \"version\":\n                    version = reader.numberValue();\n                    break;\n                case \"max_id\":\n                    maxId = reader.numberValue();\n            }\n        }\n        if (version === null || version < 1) {\n            version = 1;\n        }\n        if (name && name !== \"$ion\") {\n            let symbolTable = catalog.getVersion(name, version);\n            if (!symbolTable) {\n                if (maxId === undefined) {\n                    throw new Error(`No exact match found when trying to import symbol table ${name} version ${version}`);\n                }\n                else {\n                    symbolTable = catalog.getTable(name);\n                }\n            }\n            if (!symbolTable) {\n                symbolTable = new SubstituteSymbolTable(maxId);\n            }\n            import_ = new Import(import_, symbolTable, maxId);\n        }\n        reader.stepOut();\n    }\n    reader.stepOut();\n    return import_;\n}\nfunction load_symbols(reader) {\n    const symbols = [];\n    reader.stepIn();\n    while (reader.next()) {\n        symbols.push(reader.stringValue());\n    }\n    reader.stepOut();\n    return symbols;\n}\nexport function makeSymbolTable(catalog, reader, currentSymbolTable) {\n    let import_ = null;\n    let symbols = [];\n    let foundSymbols = false;\n    let foundImports = false;\n    let foundLstAppend = false;\n    reader.stepIn();\n    while (reader.next()) {\n        switch (reader.fieldName()) {\n            case \"imports\":\n                if (foundImports) {\n                    throw new Error(\"Multiple import fields found.\");\n                }\n                let ion_type = reader.type();\n                if (ion_type === IonTypes.SYMBOL &&\n                    reader.stringValue() === ion_symbol_table) {\n                    import_ = currentSymbolTable.import;\n                    let symbols_ = symbols;\n                    symbols = currentSymbolTable.symbols;\n                    symbols.push(...symbols_);\n                    foundLstAppend = true;\n                }\n                else if (ion_type === IonTypes.LIST) {\n                    import_ = load_imports(reader, catalog);\n                }\n                else {\n                    throw new Error(`Expected import field name to be a list or symbol found ${ion_type}`);\n                }\n                foundImports = true;\n                break;\n            case \"symbols\":\n                if (foundSymbols) {\n                    throw new Error(\"Multiple symbol fields found.\");\n                }\n                if (foundLstAppend) {\n                    symbols.push(...load_symbols(reader));\n                }\n                else {\n                    symbols = load_symbols(reader);\n                }\n                foundSymbols = true;\n                break;\n        }\n    }\n    reader.stepOut();\n    return new LocalSymbolTable(import_, symbols);\n}\n//# sourceMappingURL=IonSymbols.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,0BAA0B,QAAQ,wBAAwB;AACnE,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAO,MAAMC,gBAAgB,GAAG,mBAAmB;AACnD,OAAO,MAAMC,oBAAoB,GAAG,CAAC;AACrC,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACnC,IAAIC,OAAO,GAAGR,0BAA0B,CAAC,CAAC;EAC1CM,MAAM,CAACG,MAAM,CAAC,CAAC;EACf,OAAOH,MAAM,CAACI,IAAI,CAAC,CAAC,EAAE;IAClBJ,MAAM,CAACG,MAAM,CAAC,CAAC;IACf,IAAIE,IAAI,GAAG,IAAI;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAOP,MAAM,CAACI,IAAI,CAAC,CAAC,EAAE;MAClB,QAAQJ,MAAM,CAACQ,SAAS,CAAC,CAAC;QACtB,KAAK,MAAM;UACPH,IAAI,GAAGL,MAAM,CAACS,WAAW,CAAC,CAAC;UAC3B;QACJ,KAAK,SAAS;UACVH,OAAO,GAAGN,MAAM,CAACU,WAAW,CAAC,CAAC;UAC9B;QACJ,KAAK,QAAQ;UACTH,KAAK,GAAGP,MAAM,CAACU,WAAW,CAAC,CAAC;MACpC;IACJ;IACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,GAAG,CAAC,EAAE;MACjCA,OAAO,GAAG,CAAC;IACf;IACA,IAAID,IAAI,IAAIA,IAAI,KAAK,MAAM,EAAE;MACzB,IAAIM,WAAW,GAAGV,OAAO,CAACW,UAAU,CAACP,IAAI,EAAEC,OAAO,CAAC;MACnD,IAAI,CAACK,WAAW,EAAE;QACd,IAAIJ,KAAK,KAAKM,SAAS,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,2DAA2DT,IAAI,YAAYC,OAAO,EAAE,CAAC;QACzG,CAAC,MACI;UACDK,WAAW,GAAGV,OAAO,CAACc,QAAQ,CAACV,IAAI,CAAC;QACxC;MACJ;MACA,IAAI,CAACM,WAAW,EAAE;QACdA,WAAW,GAAG,IAAIlB,qBAAqB,CAACc,KAAK,CAAC;MAClD;MACAL,OAAO,GAAG,IAAIX,MAAM,CAACW,OAAO,EAAES,WAAW,EAAEJ,KAAK,CAAC;IACrD;IACAP,MAAM,CAACgB,OAAO,CAAC,CAAC;EACpB;EACAhB,MAAM,CAACgB,OAAO,CAAC,CAAC;EAChB,OAAOd,OAAO;AAClB;AACA,SAASe,YAAYA,CAACjB,MAAM,EAAE;EAC1B,MAAMkB,OAAO,GAAG,EAAE;EAClBlB,MAAM,CAACG,MAAM,CAAC,CAAC;EACf,OAAOH,MAAM,CAACI,IAAI,CAAC,CAAC,EAAE;IAClBc,OAAO,CAACC,IAAI,CAACnB,MAAM,CAACS,WAAW,CAAC,CAAC,CAAC;EACtC;EACAT,MAAM,CAACgB,OAAO,CAAC,CAAC;EAChB,OAAOE,OAAO;AAClB;AACA,OAAO,SAASE,eAAeA,CAACnB,OAAO,EAAED,MAAM,EAAEqB,kBAAkB,EAAE;EACjE,IAAInB,OAAO,GAAG,IAAI;EAClB,IAAIgB,OAAO,GAAG,EAAE;EAChB,IAAII,YAAY,GAAG,KAAK;EACxB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,cAAc,GAAG,KAAK;EAC1BxB,MAAM,CAACG,MAAM,CAAC,CAAC;EACf,OAAOH,MAAM,CAACI,IAAI,CAAC,CAAC,EAAE;IAClB,QAAQJ,MAAM,CAACQ,SAAS,CAAC,CAAC;MACtB,KAAK,SAAS;QACV,IAAIe,YAAY,EAAE;UACd,MAAM,IAAIT,KAAK,CAAC,+BAA+B,CAAC;QACpD;QACA,IAAIW,QAAQ,GAAGzB,MAAM,CAAC0B,IAAI,CAAC,CAAC;QAC5B,IAAID,QAAQ,KAAK9B,QAAQ,CAACgC,MAAM,IAC5B3B,MAAM,CAACS,WAAW,CAAC,CAAC,KAAKb,gBAAgB,EAAE;UAC3CM,OAAO,GAAGmB,kBAAkB,CAACO,MAAM;UACnC,IAAIC,QAAQ,GAAGX,OAAO;UACtBA,OAAO,GAAGG,kBAAkB,CAACH,OAAO;UACpCA,OAAO,CAACC,IAAI,CAAC,GAAGU,QAAQ,CAAC;UACzBL,cAAc,GAAG,IAAI;QACzB,CAAC,MACI,IAAIC,QAAQ,KAAK9B,QAAQ,CAACmC,IAAI,EAAE;UACjC5B,OAAO,GAAGH,YAAY,CAACC,MAAM,EAAEC,OAAO,CAAC;QAC3C,CAAC,MACI;UACD,MAAM,IAAIa,KAAK,CAAC,2DAA2DW,QAAQ,EAAE,CAAC;QAC1F;QACAF,YAAY,GAAG,IAAI;QACnB;MACJ,KAAK,SAAS;QACV,IAAID,YAAY,EAAE;UACd,MAAM,IAAIR,KAAK,CAAC,+BAA+B,CAAC;QACpD;QACA,IAAIU,cAAc,EAAE;UAChBN,OAAO,CAACC,IAAI,CAAC,GAAGF,YAAY,CAACjB,MAAM,CAAC,CAAC;QACzC,CAAC,MACI;UACDkB,OAAO,GAAGD,YAAY,CAACjB,MAAM,CAAC;QAClC;QACAsB,YAAY,GAAG,IAAI;QACnB;IACR;EACJ;EACAtB,MAAM,CAACgB,OAAO,CAAC,CAAC;EAChB,OAAO,IAAIxB,gBAAgB,CAACU,OAAO,EAAEgB,OAAO,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}