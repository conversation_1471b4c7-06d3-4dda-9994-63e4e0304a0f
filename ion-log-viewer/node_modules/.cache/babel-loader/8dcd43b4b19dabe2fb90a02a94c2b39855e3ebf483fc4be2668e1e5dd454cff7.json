{"ast": null, "code": "class WorkerPool {\n  constructor(pool = 4) {\n    this.pool = pool;\n    this.queue = [];\n    this.workers = [];\n    this.workersResolve = [];\n    this.workerStatus = 0;\n  }\n  _initWorker(workerId) {\n    if (!this.workers[workerId]) {\n      const worker = this.workerCreator();\n      worker.addEventListener(\"message\", this._onMessage.bind(this, workerId));\n      this.workers[workerId] = worker;\n    }\n  }\n  _getIdleWorker() {\n    for (let i = 0; i < this.pool; i++) if (!(this.workerStatus & 1 << i)) return i;\n    return -1;\n  }\n  _onMessage(workerId, msg) {\n    const resolve = this.workersResolve[workerId];\n    resolve && resolve(msg);\n    if (this.queue.length) {\n      const {\n        resolve: resolve2,\n        msg: msg2,\n        transfer\n      } = this.queue.shift();\n      this.workersResolve[workerId] = resolve2;\n      this.workers[workerId].postMessage(msg2, transfer);\n    } else {\n      this.workerStatus ^= 1 << workerId;\n    }\n  }\n  setWorkerCreator(workerCreator) {\n    this.workerCreator = workerCreator;\n  }\n  setWorkerLimit(pool) {\n    this.pool = pool;\n  }\n  postMessage(msg, transfer) {\n    return new Promise(resolve => {\n      const workerId = this._getIdleWorker();\n      if (workerId !== -1) {\n        this._initWorker(workerId);\n        this.workerStatus |= 1 << workerId;\n        this.workersResolve[workerId] = resolve;\n        this.workers[workerId].postMessage(msg, transfer);\n      } else {\n        this.queue.push({\n          resolve,\n          msg,\n          transfer\n        });\n      }\n    });\n  }\n  dispose() {\n    this.workers.forEach(worker => worker.terminate());\n    this.workersResolve.length = 0;\n    this.workers.length = 0;\n    this.queue.length = 0;\n    this.workerStatus = 0;\n  }\n}\nexport { WorkerPool };", "map": {"version": 3, "names": ["WorkerPool", "constructor", "pool", "queue", "workers", "workersResolve", "workerStatus", "_initWorker", "workerId", "worker", "workerCreator", "addEventListener", "_onMessage", "bind", "_getIdleWorker", "i", "msg", "resolve", "length", "resolve2", "msg2", "transfer", "shift", "postMessage", "setWorkerCreator", "setWorkerLimit", "Promise", "push", "dispose", "for<PERSON>ach", "terminate"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/utils/WorkerPool.js"], "sourcesContent": ["/**\n * <AUTHOR> / https://github.com/deepkolos\n */\n\nexport class WorkerPool {\n  constructor(pool = 4) {\n    this.pool = pool\n    this.queue = []\n    this.workers = []\n    this.workersResolve = []\n    this.workerStatus = 0\n  }\n\n  _initWorker(workerId) {\n    if (!this.workers[workerId]) {\n      const worker = this.workerCreator()\n      worker.addEventListener('message', this._onMessage.bind(this, workerId))\n      this.workers[workerId] = worker\n    }\n  }\n\n  _getIdleWorker() {\n    for (let i = 0; i < this.pool; i++) if (!(this.workerStatus & (1 << i))) return i\n\n    return -1\n  }\n\n  _onMessage(workerId, msg) {\n    const resolve = this.workersResolve[workerId]\n    resolve && resolve(msg)\n\n    if (this.queue.length) {\n      const { resolve, msg, transfer } = this.queue.shift()\n      this.workersResolve[workerId] = resolve\n      this.workers[workerId].postMessage(msg, transfer)\n    } else {\n      this.workerStatus ^= 1 << workerId\n    }\n  }\n\n  setWorkerCreator(workerCreator) {\n    this.workerCreator = workerCreator\n  }\n\n  setWorkerLimit(pool) {\n    this.pool = pool\n  }\n\n  postMessage(msg, transfer) {\n    return new Promise((resolve) => {\n      const workerId = this._getIdleWorker()\n\n      if (workerId !== -1) {\n        this._initWorker(workerId)\n        this.workerStatus |= 1 << workerId\n        this.workersResolve[workerId] = resolve\n        this.workers[workerId].postMessage(msg, transfer)\n      } else {\n        this.queue.push({ resolve, msg, transfer })\n      }\n    })\n  }\n\n  dispose() {\n    this.workers.forEach((worker) => worker.terminate())\n    this.workersResolve.length = 0\n    this.workers.length = 0\n    this.queue.length = 0\n    this.workerStatus = 0\n  }\n}\n"], "mappings": "AAIO,MAAMA,UAAA,CAAW;EACtBC,YAAYC,IAAA,GAAO,GAAG;IACpB,KAAKA,IAAA,GAAOA,IAAA;IACZ,KAAKC,KAAA,GAAQ,EAAE;IACf,KAAKC,OAAA,GAAU,EAAE;IACjB,KAAKC,cAAA,GAAiB,EAAE;IACxB,KAAKC,YAAA,GAAe;EACrB;EAEDC,YAAYC,QAAA,EAAU;IACpB,IAAI,CAAC,KAAKJ,OAAA,CAAQI,QAAQ,GAAG;MAC3B,MAAMC,MAAA,GAAS,KAAKC,aAAA,CAAe;MACnCD,MAAA,CAAOE,gBAAA,CAAiB,WAAW,KAAKC,UAAA,CAAWC,IAAA,CAAK,MAAML,QAAQ,CAAC;MACvE,KAAKJ,OAAA,CAAQI,QAAQ,IAAIC,MAAA;IAC1B;EACF;EAEDK,eAAA,EAAiB;IACf,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKb,IAAA,EAAMa,CAAA,IAAK,IAAI,EAAE,KAAKT,YAAA,GAAgB,KAAKS,CAAA,GAAK,OAAOA,CAAA;IAEhF,OAAO;EACR;EAEDH,WAAWJ,QAAA,EAAUQ,GAAA,EAAK;IACxB,MAAMC,OAAA,GAAU,KAAKZ,cAAA,CAAeG,QAAQ;IAC5CS,OAAA,IAAWA,OAAA,CAAQD,GAAG;IAEtB,IAAI,KAAKb,KAAA,CAAMe,MAAA,EAAQ;MACrB,MAAM;QAAED,OAAA,EAAAE,QAAA;QAASH,GAAA,EAAAI,IAAA;QAAKC;MAAU,IAAG,KAAKlB,KAAA,CAAMmB,KAAA,CAAO;MACrD,KAAKjB,cAAA,CAAeG,QAAQ,IAAIW,QAAA;MAChC,KAAKf,OAAA,CAAQI,QAAQ,EAAEe,WAAA,CAAYH,IAAA,EAAKC,QAAQ;IACtD,OAAW;MACL,KAAKf,YAAA,IAAgB,KAAKE,QAAA;IAC3B;EACF;EAEDgB,iBAAiBd,aAAA,EAAe;IAC9B,KAAKA,aAAA,GAAgBA,aAAA;EACtB;EAEDe,eAAevB,IAAA,EAAM;IACnB,KAAKA,IAAA,GAAOA,IAAA;EACb;EAEDqB,YAAYP,GAAA,EAAKK,QAAA,EAAU;IACzB,OAAO,IAAIK,OAAA,CAAST,OAAA,IAAY;MAC9B,MAAMT,QAAA,GAAW,KAAKM,cAAA,CAAgB;MAEtC,IAAIN,QAAA,KAAa,IAAI;QACnB,KAAKD,WAAA,CAAYC,QAAQ;QACzB,KAAKF,YAAA,IAAgB,KAAKE,QAAA;QAC1B,KAAKH,cAAA,CAAeG,QAAQ,IAAIS,OAAA;QAChC,KAAKb,OAAA,CAAQI,QAAQ,EAAEe,WAAA,CAAYP,GAAA,EAAKK,QAAQ;MACxD,OAAa;QACL,KAAKlB,KAAA,CAAMwB,IAAA,CAAK;UAAEV,OAAA;UAASD,GAAA;UAAKK;QAAA,CAAU;MAC3C;IACP,CAAK;EACF;EAEDO,QAAA,EAAU;IACR,KAAKxB,OAAA,CAAQyB,OAAA,CAASpB,MAAA,IAAWA,MAAA,CAAOqB,SAAA,EAAW;IACnD,KAAKzB,cAAA,CAAea,MAAA,GAAS;IAC7B,KAAKd,OAAA,CAAQc,MAAA,GAAS;IACtB,KAAKf,KAAA,CAAMe,MAAA,GAAS;IACpB,KAAKZ,YAAA,GAAe;EACrB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}