{"ast": null, "code": "import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nimport { Decimal } from \"./Decimal\";\nimport * as ion from \"../Ion\";\nconst _fromJsConstructor = new FromJsConstructorBuilder().withPrimitives(Primitives.Number).withClassesToUnbox(Number).build();\nexport class Float extends Value(Number, IonTypes.FLOAT, _fromJsConstructor) {\n  constructor(value, annotations = []) {\n    super(value);\n    this._setAnnotations(annotations);\n  }\n  numberValue() {\n    return +this.valueOf();\n  }\n  writeTo(writer) {\n    writer.setAnnotations(this.getAnnotations());\n    writer.writeFloat64(this.numberValue());\n  }\n  _valueEquals(other, options = {\n    epsilon: null,\n    ignoreAnnotations: false,\n    ignoreTimestampPrecision: false,\n    onlyCompareIon: true,\n    coerceNumericType: false\n  }) {\n    let isSupportedType = false;\n    let valueToCompare = null;\n    if (other instanceof Float) {\n      isSupportedType = true;\n      valueToCompare = other.numberValue();\n    } else if (options.coerceNumericType === true && other instanceof Decimal) {\n      let thisValue = new ion.Decimal(other.toString());\n      return thisValue.equals(other.decimalValue());\n    } else if (!options.onlyCompareIon) {\n      if (other instanceof Number || typeof other === \"number\") {\n        isSupportedType = true;\n        valueToCompare = other.valueOf();\n      }\n    }\n    if (!isSupportedType) {\n      return false;\n    }\n    let result = Object.is(this.numberValue(), valueToCompare);\n    if (options.epsilon != null) {\n      if (result || Math.abs(this.numberValue() - valueToCompare) <= options.epsilon) {\n        return true;\n      }\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["IonTypes", "FromJsConstructorBuilder", "Primitives", "Value", "Decimal", "ion", "_fromJsConstructor", "withPrimitives", "Number", "withClassesToUnbox", "build", "Float", "FLOAT", "constructor", "value", "annotations", "_setAnnotations", "numberValue", "valueOf", "writeTo", "writer", "setAnnotations", "getAnnotations", "writeFloat64", "_valueEquals", "other", "options", "epsilon", "ignoreAnnotations", "ignoreTimestampPrecision", "onlyCompareIon", "coerceNumericType", "isSupportedType", "valueToCompare", "thisValue", "toString", "equals", "decimalValue", "result", "Object", "is", "Math", "abs"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/dom/Float.js"], "sourcesContent": ["import { IonTypes } from \"../Ion\";\nimport { FromJsConstructorBuilder, Primitives, } from \"./FromJsConstructor\";\nimport { Value } from \"./Value\";\nimport { Decimal } from \"./Decimal\";\nimport * as ion from \"../Ion\";\nconst _fromJsConstructor = new FromJsConstructorBuilder()\n    .withPrimitives(Primitives.Number)\n    .withClassesToUnbox(Number)\n    .build();\nexport class Float extends Value(Number, IonTypes.FLOAT, _fromJsConstructor) {\n    constructor(value, annotations = []) {\n        super(value);\n        this._setAnnotations(annotations);\n    }\n    numberValue() {\n        return +this.valueOf();\n    }\n    writeTo(writer) {\n        writer.setAnnotations(this.getAnnotations());\n        writer.writeFloat64(this.numberValue());\n    }\n    _valueEquals(other, options = {\n        epsilon: null,\n        ignoreAnnotations: false,\n        ignoreTimestampPrecision: false,\n        onlyCompareIon: true,\n        coerceNumericType: false,\n    }) {\n        let isSupportedType = false;\n        let valueToCompare = null;\n        if (other instanceof Float) {\n            isSupportedType = true;\n            valueToCompare = other.numberValue();\n        }\n        else if (options.coerceNumericType === true && other instanceof Decimal) {\n            let thisValue = new ion.Decimal(other.toString());\n            return thisValue.equals(other.decimalValue());\n        }\n        else if (!options.onlyCompareIon) {\n            if (other instanceof Number || typeof other === \"number\") {\n                isSupportedType = true;\n                valueToCompare = other.valueOf();\n            }\n        }\n        if (!isSupportedType) {\n            return false;\n        }\n        let result = Object.is(this.numberValue(), valueToCompare);\n        if (options.epsilon != null) {\n            if (result ||\n                Math.abs(this.numberValue() - valueToCompare) <= options.epsilon) {\n                return true;\n            }\n        }\n        return result;\n    }\n}\n//# sourceMappingURL=Float.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,SAASC,wBAAwB,EAAEC,UAAU,QAAS,qBAAqB;AAC3E,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,KAAKC,GAAG,MAAM,QAAQ;AAC7B,MAAMC,kBAAkB,GAAG,IAAIL,wBAAwB,CAAC,CAAC,CACpDM,cAAc,CAACL,UAAU,CAACM,MAAM,CAAC,CACjCC,kBAAkB,CAACD,MAAM,CAAC,CAC1BE,KAAK,CAAC,CAAC;AACZ,OAAO,MAAMC,KAAK,SAASR,KAAK,CAACK,MAAM,EAAER,QAAQ,CAACY,KAAK,EAAEN,kBAAkB,CAAC,CAAC;EACzEO,WAAWA,CAACC,KAAK,EAAEC,WAAW,GAAG,EAAE,EAAE;IACjC,KAAK,CAACD,KAAK,CAAC;IACZ,IAAI,CAACE,eAAe,CAACD,WAAW,CAAC;EACrC;EACAE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZA,MAAM,CAACC,cAAc,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC5CF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC;EAC3C;EACAO,YAAYA,CAACC,KAAK,EAAEC,OAAO,GAAG;IAC1BC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,KAAK;IACxBC,wBAAwB,EAAE,KAAK;IAC/BC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE;EACvB,CAAC,EAAE;IACC,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIR,KAAK,YAAYd,KAAK,EAAE;MACxBqB,eAAe,GAAG,IAAI;MACtBC,cAAc,GAAGR,KAAK,CAACR,WAAW,CAAC,CAAC;IACxC,CAAC,MACI,IAAIS,OAAO,CAACK,iBAAiB,KAAK,IAAI,IAAIN,KAAK,YAAYrB,OAAO,EAAE;MACrE,IAAI8B,SAAS,GAAG,IAAI7B,GAAG,CAACD,OAAO,CAACqB,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC;MACjD,OAAOD,SAAS,CAACE,MAAM,CAACX,KAAK,CAACY,YAAY,CAAC,CAAC,CAAC;IACjD,CAAC,MACI,IAAI,CAACX,OAAO,CAACI,cAAc,EAAE;MAC9B,IAAIL,KAAK,YAAYjB,MAAM,IAAI,OAAOiB,KAAK,KAAK,QAAQ,EAAE;QACtDO,eAAe,GAAG,IAAI;QACtBC,cAAc,GAAGR,KAAK,CAACP,OAAO,CAAC,CAAC;MACpC;IACJ;IACA,IAAI,CAACc,eAAe,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAIM,MAAM,GAAGC,MAAM,CAACC,EAAE,CAAC,IAAI,CAACvB,WAAW,CAAC,CAAC,EAAEgB,cAAc,CAAC;IAC1D,IAAIP,OAAO,CAACC,OAAO,IAAI,IAAI,EAAE;MACzB,IAAIW,MAAM,IACNG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzB,WAAW,CAAC,CAAC,GAAGgB,cAAc,CAAC,IAAIP,OAAO,CAACC,OAAO,EAAE;QAClE,OAAO,IAAI;MACf;IACJ;IACA,OAAOW,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}