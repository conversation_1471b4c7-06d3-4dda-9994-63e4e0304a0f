{"ast": null, "code": "import { Loader, <PERSON><PERSON>oader, MeshStandardMaterial, LineBasicMaterial, ShaderMaterial, UniformsUtils, UniformsLib, Color, Vector3, BufferGeometry, BufferAttribute, LineSegments, Mesh, Ray, Matrix4, Group } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nconst FINISH_TYPE_DEFAULT = 0;\nconst FINISH_TYPE_CHROME = 1;\nconst FINISH_TYPE_PEARLESCENT = 2;\nconst FINISH_TYPE_RUBBER = 3;\nconst FINISH_TYPE_MATTE_METALLIC = 4;\nconst FINISH_TYPE_METAL = 5;\nconst FILE_LOCATION_AS_IS = 0;\nconst FILE_LOCATION_TRY_PARTS = 1;\nconst FILE_LOCATION_TRY_P = 2;\nconst FILE_LOCATION_TRY_MODELS = 3;\nconst FILE_LOCATION_TRY_RELATIVE = 4;\nconst FILE_LOCATION_TRY_ABSOLUTE = 5;\nconst FILE_LOCATION_NOT_FOUND = 6;\nconst MAIN_COLOUR_CODE = \"16\";\nconst MAIN_EDGE_COLOUR_CODE = \"24\";\nconst _tempVec0 = /* @__PURE__ */new Vector3();\nconst _tempVec1 = /* @__PURE__ */new Vector3();\nclass LDrawConditionalLineMaterial extends ShaderMaterial {\n  constructor(parameters) {\n    super({\n      uniforms: UniformsUtils.merge([UniformsLib.fog, {\n        diffuse: {\n          value: new Color()\n        },\n        opacity: {\n          value: 1\n        }\n      }]),\n      vertexShader: (/* glsl */\n      `\n        attribute vec3 control0;\n        attribute vec3 control1;\n        attribute vec3 direction;\n        varying float discardFlag;\n\n        #include <common>\n        #include <color_pars_vertex>\n        #include <fog_pars_vertex>\n        #include <logdepthbuf_pars_vertex>\n        #include <clipping_planes_pars_vertex>\n\n        void main() {\n          #include <color_vertex>\n\n          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n          gl_Position = projectionMatrix * mvPosition;\n\n          // Transform the line segment ends and control points into camera clip space\n          vec4 c0 = projectionMatrix * modelViewMatrix * vec4(control0, 1.0);\n          vec4 c1 = projectionMatrix * modelViewMatrix * vec4(control1, 1.0);\n          vec4 p0 = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          vec4 p1 = projectionMatrix * modelViewMatrix * vec4(position + direction, 1.0);\n\n          c0.xy /= c0.w;\n          c1.xy /= c1.w;\n          p0.xy /= p0.w;\n          p1.xy /= p1.w;\n\n          // Get the direction of the segment and an orthogonal vector\n          vec2 dir = p1.xy - p0.xy;\n          vec2 norm = vec2(-dir.y, dir.x);\n\n          // Get control point directions from the line\n          vec2 c0dir = c0.xy - p1.xy;\n          vec2 c1dir = c1.xy - p1.xy;\n\n          // If the vectors to the controls points are pointed in different directions away\n          // from the line segment then the line should not be drawn.\n          float d0 = dot(normalize(norm), normalize(c0dir));\n          float d1 = dot(normalize(norm), normalize(c1dir));\n          discardFlag = float(sign(d0) != sign(d1));\n\n          #include <logdepthbuf_vertex>\n          #include <clipping_planes_vertex>\n          #include <fog_vertex>\n        }\n      `),\n      fragmentShader: (/* glsl */\n      `\n        uniform vec3 diffuse;\n        uniform float opacity;\n        varying float discardFlag;\n\n        #include <common>\n        #include <color_pars_fragment>\n        #include <fog_pars_fragment>\n        #include <logdepthbuf_pars_fragment>\n        #include <clipping_planes_pars_fragment>\n\n        void main() {\n          if (discardFlag > 0.5) discard;\n\n          #include <clipping_planes_fragment>\n          vec3 outgoingLight = vec3(0.0);\n          vec4 diffuseColor = vec4(diffuse, opacity);\n          #include <logdepthbuf_fragment>\n          #include <color_fragment>\n          outgoingLight = diffuseColor.rgb; // simple shader\n          gl_FragColor = vec4(outgoingLight, diffuseColor.a);\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n          #include <fog_fragment>\n          #include <premultiplied_alpha_fragment>\n        }\n      `)\n    });\n    Object.defineProperties(this, {\n      opacity: {\n        get: function () {\n          return this.uniforms.opacity.value;\n        },\n        set: function (value) {\n          this.uniforms.opacity.value = value;\n        }\n      },\n      color: {\n        get: function () {\n          return this.uniforms.diffuse.value;\n        }\n      }\n    });\n    this.setValues(parameters);\n    this.isLDrawConditionalLineMaterial = true;\n  }\n}\nclass ConditionalLineSegments extends LineSegments {\n  constructor(geometry, material) {\n    super(geometry, material);\n    this.isConditionalLine = true;\n  }\n}\nfunction generateFaceNormals(faces) {\n  for (let i = 0, l = faces.length; i < l; i++) {\n    const face = faces[i];\n    const vertices = face.vertices;\n    const v0 = vertices[0];\n    const v1 = vertices[1];\n    const v2 = vertices[2];\n    _tempVec0.subVectors(v1, v0);\n    _tempVec1.subVectors(v2, v1);\n    face.faceNormal = new Vector3().crossVectors(_tempVec0, _tempVec1).normalize();\n  }\n}\nconst _ray = /* @__PURE__ */new Ray();\nfunction smoothNormals(faces, lineSegments, checkSubSegments = false) {\n  const hashMultiplier = (1 + 1e-10) * 100;\n  function hashVertex(v) {\n    const x = ~~(v.x * hashMultiplier);\n    const y = ~~(v.y * hashMultiplier);\n    const z = ~~(v.z * hashMultiplier);\n    return `${x},${y},${z}`;\n  }\n  function hashEdge(v0, v1) {\n    return `${hashVertex(v0)}_${hashVertex(v1)}`;\n  }\n  function toNormalizedRay(v0, v1, targetRay) {\n    targetRay.direction.subVectors(v1, v0).normalize();\n    const scalar = v0.dot(targetRay.direction);\n    targetRay.origin.copy(v0).addScaledVector(targetRay.direction, -scalar);\n    return targetRay;\n  }\n  function hashRay(ray) {\n    return hashEdge(ray.origin, ray.direction);\n  }\n  const hardEdges = /* @__PURE__ */new Set();\n  const hardEdgeRays = /* @__PURE__ */new Map();\n  const halfEdgeList = {};\n  const normals = [];\n  for (let i = 0, l = lineSegments.length; i < l; i++) {\n    const ls = lineSegments[i];\n    const vertices = ls.vertices;\n    const v0 = vertices[0];\n    const v1 = vertices[1];\n    hardEdges.add(hashEdge(v0, v1));\n    hardEdges.add(hashEdge(v1, v0));\n    if (checkSubSegments) {\n      const ray = toNormalizedRay(v0, v1, new Ray());\n      const rh1 = hashRay(ray);\n      if (!hardEdgeRays.has(rh1)) {\n        toNormalizedRay(v1, v0, ray);\n        const rh2 = hashRay(ray);\n        const info2 = {\n          ray,\n          distances: []\n        };\n        hardEdgeRays.set(rh1, info2);\n        hardEdgeRays.set(rh2, info2);\n      }\n      const info = hardEdgeRays.get(rh1);\n      let d0 = info.ray.direction.dot(v0);\n      let d1 = info.ray.direction.dot(v1);\n      if (d0 > d1) {\n        [d0, d1] = [d1, d0];\n      }\n      info.distances.push(d0, d1);\n    }\n  }\n  for (let i = 0, l = faces.length; i < l; i++) {\n    const tri = faces[i];\n    const vertices = tri.vertices;\n    const vertCount = vertices.length;\n    for (let i2 = 0; i2 < vertCount; i2++) {\n      const index = i2;\n      const next = (i2 + 1) % vertCount;\n      const v0 = vertices[index];\n      const v1 = vertices[next];\n      const hash = hashEdge(v0, v1);\n      if (hardEdges.has(hash)) {\n        continue;\n      }\n      if (checkSubSegments) {\n        toNormalizedRay(v0, v1, _ray);\n        const rayHash = hashRay(_ray);\n        if (hardEdgeRays.has(rayHash)) {\n          const info2 = hardEdgeRays.get(rayHash);\n          const {\n            ray,\n            distances\n          } = info2;\n          let d0 = ray.direction.dot(v0);\n          let d1 = ray.direction.dot(v1);\n          if (d0 > d1) {\n            [d0, d1] = [d1, d0];\n          }\n          let found = false;\n          for (let i3 = 0, l2 = distances.length; i3 < l2; i3 += 2) {\n            if (d0 >= distances[i3] && d1 <= distances[i3 + 1]) {\n              found = true;\n              break;\n            }\n          }\n          if (found) {\n            continue;\n          }\n        }\n      }\n      const info = {\n        index,\n        tri\n      };\n      halfEdgeList[hash] = info;\n    }\n  }\n  while (true) {\n    let halfEdge = null;\n    for (const key in halfEdgeList) {\n      halfEdge = halfEdgeList[key];\n      break;\n    }\n    if (halfEdge === null) {\n      break;\n    }\n    const queue = [halfEdge];\n    while (queue.length > 0) {\n      const tri = queue.pop().tri;\n      const vertices = tri.vertices;\n      const vertNormals = tri.normals;\n      const faceNormal = tri.faceNormal;\n      const vertCount = vertices.length;\n      for (let i2 = 0; i2 < vertCount; i2++) {\n        const index = i2;\n        const next = (i2 + 1) % vertCount;\n        const v0 = vertices[index];\n        const v1 = vertices[next];\n        const hash = hashEdge(v0, v1);\n        delete halfEdgeList[hash];\n        const reverseHash = hashEdge(v1, v0);\n        const otherInfo = halfEdgeList[reverseHash];\n        if (otherInfo) {\n          const otherTri = otherInfo.tri;\n          const otherIndex = otherInfo.index;\n          const otherNormals = otherTri.normals;\n          const otherVertCount = otherNormals.length;\n          const otherFaceNormal = otherTri.faceNormal;\n          if (Math.abs(otherTri.faceNormal.dot(tri.faceNormal)) < 0.25) {\n            continue;\n          }\n          if (reverseHash in halfEdgeList) {\n            queue.push(otherInfo);\n            delete halfEdgeList[reverseHash];\n          }\n          const otherNext = (otherIndex + 1) % otherVertCount;\n          if (vertNormals[index] && otherNormals[otherNext] && vertNormals[index] !== otherNormals[otherNext]) {\n            otherNormals[otherNext].norm.add(vertNormals[index].norm);\n            vertNormals[index].norm = otherNormals[otherNext].norm;\n          }\n          let sharedNormal1 = vertNormals[index] || otherNormals[otherNext];\n          if (sharedNormal1 === null) {\n            sharedNormal1 = {\n              norm: new Vector3()\n            };\n            normals.push(sharedNormal1.norm);\n          }\n          if (vertNormals[index] === null) {\n            vertNormals[index] = sharedNormal1;\n            sharedNormal1.norm.add(faceNormal);\n          }\n          if (otherNormals[otherNext] === null) {\n            otherNormals[otherNext] = sharedNormal1;\n            sharedNormal1.norm.add(otherFaceNormal);\n          }\n          if (vertNormals[next] && otherNormals[otherIndex] && vertNormals[next] !== otherNormals[otherIndex]) {\n            otherNormals[otherIndex].norm.add(vertNormals[next].norm);\n            vertNormals[next].norm = otherNormals[otherIndex].norm;\n          }\n          let sharedNormal2 = vertNormals[next] || otherNormals[otherIndex];\n          if (sharedNormal2 === null) {\n            sharedNormal2 = {\n              norm: new Vector3()\n            };\n            normals.push(sharedNormal2.norm);\n          }\n          if (vertNormals[next] === null) {\n            vertNormals[next] = sharedNormal2;\n            sharedNormal2.norm.add(faceNormal);\n          }\n          if (otherNormals[otherIndex] === null) {\n            otherNormals[otherIndex] = sharedNormal2;\n            sharedNormal2.norm.add(otherFaceNormal);\n          }\n        }\n      }\n    }\n  }\n  for (let i = 0, l = normals.length; i < l; i++) {\n    normals[i].normalize();\n  }\n}\nfunction isPartType(type) {\n  return type === \"Part\" || type === \"Unofficial_Part\";\n}\nfunction isPrimitiveType(type) {\n  return /primitive/i.test(type) || type === \"Subpart\";\n}\nclass LineParser {\n  constructor(line, lineNumber) {\n    this.line = line;\n    this.lineLength = line.length;\n    this.currentCharIndex = 0;\n    this.currentChar = \" \";\n    this.lineNumber = lineNumber;\n  }\n  seekNonSpace() {\n    while (this.currentCharIndex < this.lineLength) {\n      this.currentChar = this.line.charAt(this.currentCharIndex);\n      if (this.currentChar !== \" \" && this.currentChar !== \"\t\") {\n        return;\n      }\n      this.currentCharIndex++;\n    }\n  }\n  getToken() {\n    const pos0 = this.currentCharIndex++;\n    while (this.currentCharIndex < this.lineLength) {\n      this.currentChar = this.line.charAt(this.currentCharIndex);\n      if (this.currentChar === \" \" || this.currentChar === \"\t\") {\n        break;\n      }\n      this.currentCharIndex++;\n    }\n    const pos1 = this.currentCharIndex;\n    this.seekNonSpace();\n    return this.line.substring(pos0, pos1);\n  }\n  getVector() {\n    return new Vector3(parseFloat(this.getToken()), parseFloat(this.getToken()), parseFloat(this.getToken()));\n  }\n  getRemainingString() {\n    return this.line.substring(this.currentCharIndex, this.lineLength);\n  }\n  isAtTheEnd() {\n    return this.currentCharIndex >= this.lineLength;\n  }\n  setToEnd() {\n    this.currentCharIndex = this.lineLength;\n  }\n  getLineNumberString() {\n    return this.lineNumber >= 0 ? \" at line \" + this.lineNumber : \"\";\n  }\n}\nclass LDrawParsedCache {\n  constructor(loader) {\n    this.loader = loader;\n    this._cache = {};\n  }\n  cloneResult(original) {\n    const result = {};\n    result.faces = original.faces.map(face => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map(v => v.clone()),\n        normals: face.normals.map(() => null),\n        faceNormal: null\n      };\n    });\n    result.conditionalSegments = original.conditionalSegments.map(face => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map(v => v.clone()),\n        controlPoints: face.controlPoints.map(v => v.clone())\n      };\n    });\n    result.lineSegments = original.lineSegments.map(face => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map(v => v.clone())\n      };\n    });\n    result.type = original.type;\n    result.category = original.category;\n    result.keywords = original.keywords;\n    result.subobjects = original.subobjects;\n    result.totalFaces = original.totalFaces;\n    result.startingConstructionStep = original.startingConstructionStep;\n    result.materials = original.materials;\n    result.group = null;\n    return result;\n  }\n  async fetchData(fileName) {\n    let triedLowerCase = false;\n    let locationState = FILE_LOCATION_AS_IS;\n    while (locationState !== FILE_LOCATION_NOT_FOUND) {\n      let subobjectURL = fileName;\n      switch (locationState) {\n        case FILE_LOCATION_AS_IS:\n          locationState = locationState + 1;\n          break;\n        case FILE_LOCATION_TRY_PARTS:\n          subobjectURL = \"parts/\" + subobjectURL;\n          locationState = locationState + 1;\n          break;\n        case FILE_LOCATION_TRY_P:\n          subobjectURL = \"p/\" + subobjectURL;\n          locationState = locationState + 1;\n          break;\n        case FILE_LOCATION_TRY_MODELS:\n          subobjectURL = \"models/\" + subobjectURL;\n          locationState = locationState + 1;\n          break;\n        case FILE_LOCATION_TRY_RELATIVE:\n          subobjectURL = fileName.substring(0, fileName.lastIndexOf(\"/\") + 1) + subobjectURL;\n          locationState = locationState + 1;\n          break;\n        case FILE_LOCATION_TRY_ABSOLUTE:\n          if (triedLowerCase) {\n            locationState = FILE_LOCATION_NOT_FOUND;\n          } else {\n            fileName = fileName.toLowerCase();\n            subobjectURL = fileName;\n            triedLowerCase = true;\n            locationState = FILE_LOCATION_AS_IS;\n          }\n          break;\n      }\n      const loader = this.loader;\n      const fileLoader = new FileLoader(loader.manager);\n      fileLoader.setPath(loader.partsLibraryPath);\n      fileLoader.setRequestHeader(loader.requestHeader);\n      fileLoader.setWithCredentials(loader.withCredentials);\n      try {\n        const text = await fileLoader.loadAsync(subobjectURL);\n        return text;\n      } catch (e) {\n        continue;\n      }\n    }\n    throw new Error('LDrawLoader: Subobject \"' + fileName + '\" could not be loaded.');\n  }\n  parse(text, fileName = null) {\n    const loader = this.loader;\n    const faces = [];\n    const lineSegments = [];\n    const conditionalSegments = [];\n    const subobjects = [];\n    const materials = {};\n    const getLocalMaterial = colorCode => {\n      return materials[colorCode] || null;\n    };\n    let type = \"Model\";\n    let category = null;\n    let keywords = null;\n    let totalFaces = 0;\n    if (text.indexOf(\"\\r\\n\") !== -1) {\n      text = text.replace(/\\r\\n/g, \"\\n\");\n    }\n    const lines = text.split(\"\\n\");\n    const numLines = lines.length;\n    let parsingEmbeddedFiles = false;\n    let currentEmbeddedFileName = null;\n    let currentEmbeddedText = null;\n    let bfcCertified = false;\n    let bfcCCW = true;\n    let bfcInverted = false;\n    let bfcCull = true;\n    let startingConstructionStep = false;\n    for (let lineIndex = 0; lineIndex < numLines; lineIndex++) {\n      const line = lines[lineIndex];\n      if (line.length === 0) continue;\n      if (parsingEmbeddedFiles) {\n        if (line.startsWith(\"0 FILE \")) {\n          this.setData(currentEmbeddedFileName, currentEmbeddedText);\n          currentEmbeddedFileName = line.substring(7);\n          currentEmbeddedText = \"\";\n        } else {\n          currentEmbeddedText += line + \"\\n\";\n        }\n        continue;\n      }\n      const lp = new LineParser(line, lineIndex + 1);\n      lp.seekNonSpace();\n      if (lp.isAtTheEnd()) {\n        continue;\n      }\n      const lineType = lp.getToken();\n      let material;\n      let colorCode;\n      let segment;\n      let ccw;\n      let doubleSided;\n      let v0, v1, v2, v3, c0, c1;\n      switch (lineType) {\n        case \"0\":\n          const meta = lp.getToken();\n          if (meta) {\n            switch (meta) {\n              case \"!LDRAW_ORG\":\n                type = lp.getToken();\n                break;\n              case \"!COLOUR\":\n                material = loader.parseColorMetaDirective(lp);\n                if (material) {\n                  materials[material.userData.code] = material;\n                } else {\n                  console.warn(\"LDrawLoader: Error parsing material\" + lp.getLineNumberString());\n                }\n                break;\n              case \"!CATEGORY\":\n                category = lp.getToken();\n                break;\n              case \"!KEYWORDS\":\n                const newKeywords = lp.getRemainingString().split(\",\");\n                if (newKeywords.length > 0) {\n                  if (!keywords) {\n                    keywords = [];\n                  }\n                  newKeywords.forEach(function (keyword) {\n                    keywords.push(keyword.trim());\n                  });\n                }\n                break;\n              case \"FILE\":\n                if (lineIndex > 0) {\n                  parsingEmbeddedFiles = true;\n                  currentEmbeddedFileName = lp.getRemainingString();\n                  currentEmbeddedText = \"\";\n                  bfcCertified = false;\n                  bfcCCW = true;\n                }\n                break;\n              case \"BFC\":\n                while (!lp.isAtTheEnd()) {\n                  const token = lp.getToken();\n                  switch (token) {\n                    case \"CERTIFY\":\n                    case \"NOCERTIFY\":\n                      bfcCertified = token === \"CERTIFY\";\n                      bfcCCW = true;\n                      break;\n                    case \"CW\":\n                    case \"CCW\":\n                      bfcCCW = token === \"CCW\";\n                      break;\n                    case \"INVERTNEXT\":\n                      bfcInverted = true;\n                      break;\n                    case \"CLIP\":\n                    case \"NOCLIP\":\n                      bfcCull = token === \"CLIP\";\n                      break;\n                    default:\n                      console.warn('THREE.LDrawLoader: BFC directive \"' + token + '\" is unknown.');\n                      break;\n                  }\n                }\n                break;\n              case \"STEP\":\n                startingConstructionStep = true;\n                break;\n            }\n          }\n          break;\n        case \"1\":\n          colorCode = lp.getToken();\n          material = getLocalMaterial(colorCode);\n          const posX = parseFloat(lp.getToken());\n          const posY = parseFloat(lp.getToken());\n          const posZ = parseFloat(lp.getToken());\n          const m0 = parseFloat(lp.getToken());\n          const m1 = parseFloat(lp.getToken());\n          const m2 = parseFloat(lp.getToken());\n          const m3 = parseFloat(lp.getToken());\n          const m4 = parseFloat(lp.getToken());\n          const m5 = parseFloat(lp.getToken());\n          const m6 = parseFloat(lp.getToken());\n          const m7 = parseFloat(lp.getToken());\n          const m8 = parseFloat(lp.getToken());\n          const matrix = new Matrix4().set(m0, m1, m2, posX, m3, m4, m5, posY, m6, m7, m8, posZ, 0, 0, 0, 1);\n          let fileName2 = lp.getRemainingString().trim().replace(/\\\\/g, \"/\");\n          if (loader.fileMap[fileName2]) {\n            fileName2 = loader.fileMap[fileName2];\n          } else {\n            if (fileName2.startsWith(\"s/\")) {\n              fileName2 = \"parts/\" + fileName2;\n            } else if (fileName2.startsWith(\"48/\")) {\n              fileName2 = \"p/\" + fileName2;\n            }\n          }\n          subobjects.push({\n            material,\n            colorCode,\n            matrix,\n            fileName: fileName2,\n            inverted: bfcInverted,\n            startingConstructionStep\n          });\n          bfcInverted = false;\n          break;\n        case \"2\":\n          colorCode = lp.getToken();\n          material = getLocalMaterial(colorCode);\n          v0 = lp.getVector();\n          v1 = lp.getVector();\n          segment = {\n            material,\n            colorCode,\n            vertices: [v0, v1]\n          };\n          lineSegments.push(segment);\n          break;\n        case \"5\":\n          colorCode = lp.getToken();\n          material = getLocalMaterial(colorCode);\n          v0 = lp.getVector();\n          v1 = lp.getVector();\n          c0 = lp.getVector();\n          c1 = lp.getVector();\n          segment = {\n            material,\n            colorCode,\n            vertices: [v0, v1],\n            controlPoints: [c0, c1]\n          };\n          conditionalSegments.push(segment);\n          break;\n        case \"3\":\n          colorCode = lp.getToken();\n          material = getLocalMaterial(colorCode);\n          ccw = bfcCCW;\n          doubleSided = !bfcCertified || !bfcCull;\n          if (ccw === true) {\n            v0 = lp.getVector();\n            v1 = lp.getVector();\n            v2 = lp.getVector();\n          } else {\n            v2 = lp.getVector();\n            v1 = lp.getVector();\n            v0 = lp.getVector();\n          }\n          faces.push({\n            material,\n            colorCode,\n            faceNormal: null,\n            vertices: [v0, v1, v2],\n            normals: [null, null, null]\n          });\n          totalFaces++;\n          if (doubleSided === true) {\n            faces.push({\n              material,\n              colorCode,\n              faceNormal: null,\n              vertices: [v2, v1, v0],\n              normals: [null, null, null]\n            });\n            totalFaces++;\n          }\n          break;\n        case \"4\":\n          colorCode = lp.getToken();\n          material = getLocalMaterial(colorCode);\n          ccw = bfcCCW;\n          doubleSided = !bfcCertified || !bfcCull;\n          if (ccw === true) {\n            v0 = lp.getVector();\n            v1 = lp.getVector();\n            v2 = lp.getVector();\n            v3 = lp.getVector();\n          } else {\n            v3 = lp.getVector();\n            v2 = lp.getVector();\n            v1 = lp.getVector();\n            v0 = lp.getVector();\n          }\n          faces.push({\n            material,\n            colorCode,\n            faceNormal: null,\n            vertices: [v0, v1, v2, v3],\n            normals: [null, null, null, null]\n          });\n          totalFaces += 2;\n          if (doubleSided === true) {\n            faces.push({\n              material,\n              colorCode,\n              faceNormal: null,\n              vertices: [v3, v2, v1, v0],\n              normals: [null, null, null, null]\n            });\n            totalFaces += 2;\n          }\n          break;\n        default:\n          throw new Error('LDrawLoader: Unknown line type \"' + lineType + '\"' + lp.getLineNumberString() + \".\");\n      }\n    }\n    if (parsingEmbeddedFiles) {\n      this.setData(currentEmbeddedFileName, currentEmbeddedText);\n    }\n    return {\n      faces,\n      conditionalSegments,\n      lineSegments,\n      type,\n      category,\n      keywords,\n      subobjects,\n      totalFaces,\n      startingConstructionStep,\n      materials,\n      fileName,\n      group: null\n    };\n  }\n  // returns an (optionally cloned) instance of the data\n  getData(fileName, clone = true) {\n    const key = fileName.toLowerCase();\n    const result = this._cache[key];\n    if (result === null || result instanceof Promise) {\n      return null;\n    }\n    if (clone) {\n      return this.cloneResult(result);\n    } else {\n      return result;\n    }\n  }\n  // kicks off a fetch and parse of the requested data if it hasn't already been loaded. Returns when\n  // the data is ready to use and can be retrieved synchronously with \"getData\".\n  async ensureDataLoaded(fileName) {\n    const key = fileName.toLowerCase();\n    if (!(key in this._cache)) {\n      this._cache[key] = this.fetchData(fileName).then(text => {\n        const info = this.parse(text, fileName);\n        this._cache[key] = info;\n        return info;\n      });\n    }\n    await this._cache[key];\n  }\n  // sets the data in the cache from parsed data\n  setData(fileName, text) {\n    const key = fileName.toLowerCase();\n    this._cache[key] = this.parse(text, fileName);\n  }\n}\nfunction getMaterialFromCode(colorCode, parentColorCode, materialHierarchy, forEdge) {\n  const isPassthrough = !forEdge && colorCode === MAIN_COLOUR_CODE || forEdge && colorCode === MAIN_EDGE_COLOUR_CODE;\n  if (isPassthrough) {\n    colorCode = parentColorCode;\n  }\n  return materialHierarchy[colorCode] || null;\n}\nclass LDrawPartsGeometryCache {\n  constructor(loader) {\n    this.loader = loader;\n    this.parseCache = new LDrawParsedCache(loader);\n    this._cache = {};\n  }\n  // Convert the given file information into a mesh by processing subobjects.\n  async processIntoMesh(info) {\n    const loader = this.loader;\n    const parseCache = this.parseCache;\n    const faceMaterials = /* @__PURE__ */new Set();\n    const processInfoSubobjects = async (info2, subobject = null) => {\n      const subobjects = info2.subobjects;\n      const promises = [];\n      for (let i = 0, l = subobjects.length; i < l; i++) {\n        const subobject2 = subobjects[i];\n        const promise = parseCache.ensureDataLoaded(subobject2.fileName).then(() => {\n          const subobjectInfo = parseCache.getData(subobject2.fileName, false);\n          if (!isPrimitiveType(subobjectInfo.type)) {\n            return this.loadModel(subobject2.fileName).catch(error => {\n              console.warn(error);\n              return null;\n            });\n          }\n          return processInfoSubobjects(parseCache.getData(subobject2.fileName), subobject2);\n        });\n        promises.push(promise);\n      }\n      const group2 = new Group();\n      group2.userData.category = info2.category;\n      group2.userData.keywords = info2.keywords;\n      info2.group = group2;\n      const subobjectInfos = await Promise.all(promises);\n      for (let i = 0, l = subobjectInfos.length; i < l; i++) {\n        const subobject2 = info2.subobjects[i];\n        const subobjectInfo = subobjectInfos[i];\n        if (subobjectInfo === null) {\n          continue;\n        }\n        if (subobjectInfo.isGroup) {\n          const subobjectGroup = subobjectInfo;\n          subobject2.matrix.decompose(subobjectGroup.position, subobjectGroup.quaternion, subobjectGroup.scale);\n          subobjectGroup.userData.startingConstructionStep = subobject2.startingConstructionStep;\n          subobjectGroup.name = subobject2.fileName;\n          loader.applyMaterialsToMesh(subobjectGroup, subobject2.colorCode, info2.materials);\n          group2.add(subobjectGroup);\n          continue;\n        }\n        if (subobjectInfo.group.children.length) {\n          group2.add(subobjectInfo.group);\n        }\n        const parentLineSegments = info2.lineSegments;\n        const parentConditionalSegments = info2.conditionalSegments;\n        const parentFaces = info2.faces;\n        const lineSegments = subobjectInfo.lineSegments;\n        const conditionalSegments = subobjectInfo.conditionalSegments;\n        const faces = subobjectInfo.faces;\n        const matrix = subobject2.matrix;\n        const inverted = subobject2.inverted;\n        const matrixScaleInverted = matrix.determinant() < 0;\n        const colorCode = subobject2.colorCode;\n        const lineColorCode = colorCode === MAIN_COLOUR_CODE ? MAIN_EDGE_COLOUR_CODE : colorCode;\n        for (let i2 = 0, l2 = lineSegments.length; i2 < l2; i2++) {\n          const ls = lineSegments[i2];\n          const vertices = ls.vertices;\n          vertices[0].applyMatrix4(matrix);\n          vertices[1].applyMatrix4(matrix);\n          ls.colorCode = ls.colorCode === MAIN_EDGE_COLOUR_CODE ? lineColorCode : ls.colorCode;\n          ls.material = ls.material || getMaterialFromCode(ls.colorCode, ls.colorCode, info2.materials, true);\n          parentLineSegments.push(ls);\n        }\n        for (let i2 = 0, l2 = conditionalSegments.length; i2 < l2; i2++) {\n          const os = conditionalSegments[i2];\n          const vertices = os.vertices;\n          const controlPoints = os.controlPoints;\n          vertices[0].applyMatrix4(matrix);\n          vertices[1].applyMatrix4(matrix);\n          controlPoints[0].applyMatrix4(matrix);\n          controlPoints[1].applyMatrix4(matrix);\n          os.colorCode = os.colorCode === MAIN_EDGE_COLOUR_CODE ? lineColorCode : os.colorCode;\n          os.material = os.material || getMaterialFromCode(os.colorCode, os.colorCode, info2.materials, true);\n          parentConditionalSegments.push(os);\n        }\n        for (let i2 = 0, l2 = faces.length; i2 < l2; i2++) {\n          const tri = faces[i2];\n          const vertices = tri.vertices;\n          for (let i3 = 0, l3 = vertices.length; i3 < l3; i3++) {\n            vertices[i3].applyMatrix4(matrix);\n          }\n          tri.colorCode = tri.colorCode === MAIN_COLOUR_CODE ? colorCode : tri.colorCode;\n          tri.material = tri.material || getMaterialFromCode(tri.colorCode, colorCode, info2.materials, false);\n          faceMaterials.add(tri.colorCode);\n          if (matrixScaleInverted !== inverted) {\n            vertices.reverse();\n          }\n          parentFaces.push(tri);\n        }\n        info2.totalFaces += subobjectInfo.totalFaces;\n      }\n      if (subobject) {\n        loader.applyMaterialsToMesh(group2, subobject.colorCode, info2.materials);\n      }\n      return info2;\n    };\n    for (let i = 0, l = info.faces; i < l; i++) {\n      faceMaterials.add(info.faces[i].colorCode);\n    }\n    await processInfoSubobjects(info);\n    if (loader.smoothNormals) {\n      const checkSubSegments = faceMaterials.size > 1;\n      generateFaceNormals(info.faces);\n      smoothNormals(info.faces, info.lineSegments, checkSubSegments);\n    }\n    const group = info.group;\n    if (info.faces.length > 0) {\n      group.add(createObject(info.faces, 3, false, info.totalFaces));\n    }\n    if (info.lineSegments.length > 0) {\n      group.add(createObject(info.lineSegments, 2));\n    }\n    if (info.conditionalSegments.length > 0) {\n      group.add(createObject(info.conditionalSegments, 2, true));\n    }\n    return group;\n  }\n  hasCachedModel(fileName) {\n    return fileName !== null && fileName.toLowerCase() in this._cache;\n  }\n  async getCachedModel(fileName) {\n    if (fileName !== null && this.hasCachedModel(fileName)) {\n      const key = fileName.toLowerCase();\n      const group = await this._cache[key];\n      return group.clone();\n    } else {\n      return null;\n    }\n  }\n  // Loads and parses the model with the given file name. Returns a cached copy if available.\n  async loadModel(fileName) {\n    const parseCache = this.parseCache;\n    const key = fileName.toLowerCase();\n    if (this.hasCachedModel(fileName)) {\n      return this.getCachedModel(fileName);\n    } else {\n      await parseCache.ensureDataLoaded(fileName);\n      const info = parseCache.getData(fileName);\n      const promise = this.processIntoMesh(info);\n      if (this.hasCachedModel(fileName)) {\n        return this.getCachedModel(fileName);\n      }\n      if (isPartType(info.type)) {\n        this._cache[key] = promise;\n      }\n      const group = await promise;\n      return group.clone();\n    }\n  }\n  // parses the given model text into a renderable object. Returns cached copy if available.\n  async parseModel(text) {\n    const parseCache = this.parseCache;\n    const info = parseCache.parse(text);\n    if (isPartType(info.type) && this.hasCachedModel(info.fileName)) {\n      return this.getCachedModel(info.fileName);\n    }\n    return this.processIntoMesh(info);\n  }\n}\nfunction sortByMaterial(a, b) {\n  if (a.colorCode === b.colorCode) {\n    return 0;\n  }\n  if (a.colorCode < b.colorCode) {\n    return -1;\n  }\n  return 1;\n}\nfunction createObject(elements, elementSize, isConditionalSegments = false, totalElements = null) {\n  elements.sort(sortByMaterial);\n  if (totalElements === null) {\n    totalElements = elements.length;\n  }\n  const positions = new Float32Array(elementSize * totalElements * 3);\n  const normals = elementSize === 3 ? new Float32Array(elementSize * totalElements * 3) : null;\n  const materials = [];\n  const quadArray = new Array(6);\n  const bufferGeometry = new BufferGeometry();\n  let prevMaterial = null;\n  let index0 = 0;\n  let numGroupVerts = 0;\n  let offset = 0;\n  for (let iElem = 0, nElem = elements.length; iElem < nElem; iElem++) {\n    const elem = elements[iElem];\n    let vertices = elem.vertices;\n    if (vertices.length === 4) {\n      quadArray[0] = vertices[0];\n      quadArray[1] = vertices[1];\n      quadArray[2] = vertices[2];\n      quadArray[3] = vertices[0];\n      quadArray[4] = vertices[2];\n      quadArray[5] = vertices[3];\n      vertices = quadArray;\n    }\n    for (let j = 0, l = vertices.length; j < l; j++) {\n      const v = vertices[j];\n      const index = offset + j * 3;\n      positions[index + 0] = v.x;\n      positions[index + 1] = v.y;\n      positions[index + 2] = v.z;\n    }\n    if (elementSize === 3) {\n      if (!elem.faceNormal) {\n        const v0 = vertices[0];\n        const v1 = vertices[1];\n        const v2 = vertices[2];\n        _tempVec0.subVectors(v1, v0);\n        _tempVec1.subVectors(v2, v1);\n        elem.faceNormal = new Vector3().crossVectors(_tempVec0, _tempVec1).normalize();\n      }\n      let elemNormals = elem.normals;\n      if (elemNormals.length === 4) {\n        quadArray[0] = elemNormals[0];\n        quadArray[1] = elemNormals[1];\n        quadArray[2] = elemNormals[2];\n        quadArray[3] = elemNormals[0];\n        quadArray[4] = elemNormals[2];\n        quadArray[5] = elemNormals[3];\n        elemNormals = quadArray;\n      }\n      for (let j = 0, l = elemNormals.length; j < l; j++) {\n        let n = elem.faceNormal;\n        if (elemNormals[j]) {\n          n = elemNormals[j].norm;\n        }\n        const index = offset + j * 3;\n        normals[index + 0] = n.x;\n        normals[index + 1] = n.y;\n        normals[index + 2] = n.z;\n      }\n    }\n    if (prevMaterial !== elem.colorCode) {\n      if (prevMaterial !== null) {\n        bufferGeometry.addGroup(index0, numGroupVerts, materials.length - 1);\n      }\n      const material = elem.material;\n      if (material !== null) {\n        if (elementSize === 3) {\n          materials.push(material);\n        } else if (elementSize === 2) {\n          if (material !== null) {\n            if (isConditionalSegments) {\n              materials.push(material.userData.edgeMaterial.userData.conditionalEdgeMaterial);\n            } else {\n              materials.push(material.userData.edgeMaterial);\n            }\n          } else {\n            materials.push(null);\n          }\n        }\n      } else {\n        materials.push(elem.colorCode);\n      }\n      prevMaterial = elem.colorCode;\n      index0 = offset / 3;\n      numGroupVerts = vertices.length;\n    } else {\n      numGroupVerts += vertices.length;\n    }\n    offset += 3 * vertices.length;\n  }\n  if (numGroupVerts > 0) {\n    bufferGeometry.addGroup(index0, Infinity, materials.length - 1);\n  }\n  bufferGeometry.setAttribute(\"position\", new BufferAttribute(positions, 3));\n  if (normals !== null) {\n    bufferGeometry.setAttribute(\"normal\", new BufferAttribute(normals, 3));\n  }\n  let object3d = null;\n  if (elementSize === 2) {\n    if (isConditionalSegments) {\n      object3d = new ConditionalLineSegments(bufferGeometry, materials.length === 1 ? materials[0] : materials);\n    } else {\n      object3d = new LineSegments(bufferGeometry, materials.length === 1 ? materials[0] : materials);\n    }\n  } else if (elementSize === 3) {\n    object3d = new Mesh(bufferGeometry, materials.length === 1 ? materials[0] : materials);\n  }\n  if (isConditionalSegments) {\n    object3d.isConditionalLine = true;\n    const controlArray0 = new Float32Array(elements.length * 3 * 2);\n    const controlArray1 = new Float32Array(elements.length * 3 * 2);\n    const directionArray = new Float32Array(elements.length * 3 * 2);\n    for (let i = 0, l = elements.length; i < l; i++) {\n      const os = elements[i];\n      const vertices = os.vertices;\n      const controlPoints = os.controlPoints;\n      const c0 = controlPoints[0];\n      const c1 = controlPoints[1];\n      const v0 = vertices[0];\n      const v1 = vertices[1];\n      const index = i * 3 * 2;\n      controlArray0[index + 0] = c0.x;\n      controlArray0[index + 1] = c0.y;\n      controlArray0[index + 2] = c0.z;\n      controlArray0[index + 3] = c0.x;\n      controlArray0[index + 4] = c0.y;\n      controlArray0[index + 5] = c0.z;\n      controlArray1[index + 0] = c1.x;\n      controlArray1[index + 1] = c1.y;\n      controlArray1[index + 2] = c1.z;\n      controlArray1[index + 3] = c1.x;\n      controlArray1[index + 4] = c1.y;\n      controlArray1[index + 5] = c1.z;\n      directionArray[index + 0] = v1.x - v0.x;\n      directionArray[index + 1] = v1.y - v0.y;\n      directionArray[index + 2] = v1.z - v0.z;\n      directionArray[index + 3] = v1.x - v0.x;\n      directionArray[index + 4] = v1.y - v0.y;\n      directionArray[index + 5] = v1.z - v0.z;\n    }\n    bufferGeometry.setAttribute(\"control0\", new BufferAttribute(controlArray0, 3, false));\n    bufferGeometry.setAttribute(\"control1\", new BufferAttribute(controlArray1, 3, false));\n    bufferGeometry.setAttribute(\"direction\", new BufferAttribute(directionArray, 3, false));\n  }\n  return object3d;\n}\nclass LDrawLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.materials = [];\n    this.materialLibrary = {};\n    this.partsCache = new LDrawPartsGeometryCache(this);\n    this.fileMap = {};\n    this.setMaterials([]);\n    this.smoothNormals = true;\n    this.partsLibraryPath = \"\";\n  }\n  setPartsLibraryPath(path) {\n    this.partsLibraryPath = path;\n    return this;\n  }\n  async preloadMaterials(url) {\n    const fileLoader = new FileLoader(this.manager);\n    fileLoader.setPath(this.path);\n    fileLoader.setRequestHeader(this.requestHeader);\n    fileLoader.setWithCredentials(this.withCredentials);\n    const text = await fileLoader.loadAsync(url);\n    const colorLineRegex = /^0 !COLOUR/;\n    const lines = text.split(/[\\n\\r]/g);\n    const materials = [];\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i];\n      if (colorLineRegex.test(line)) {\n        const directive = line.replace(colorLineRegex, \"\");\n        const material = this.parseColorMetaDirective(new LineParser(directive));\n        materials.push(material);\n      }\n    }\n    this.setMaterials(materials);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const fileLoader = new FileLoader(this.manager);\n    fileLoader.setPath(this.path);\n    fileLoader.setRequestHeader(this.requestHeader);\n    fileLoader.setWithCredentials(this.withCredentials);\n    fileLoader.load(url, text => {\n      this.partsCache.parseModel(text, this.materialLibrary).then(group => {\n        this.applyMaterialsToMesh(group, MAIN_COLOUR_CODE, this.materialLibrary, true);\n        this.computeConstructionSteps(group);\n        onLoad(group);\n      }).catch(onError);\n    }, onProgress, onError);\n  }\n  parse(text, onLoad) {\n    this.partsCache.parseModel(text, this.materialLibrary).then(group => {\n      this.computeConstructionSteps(group);\n      onLoad(group);\n    });\n  }\n  setMaterials(materials) {\n    this.materialLibrary = {};\n    this.materials = [];\n    for (let i = 0, l = materials.length; i < l; i++) {\n      this.addMaterial(materials[i]);\n    }\n    this.addMaterial(this.parseColorMetaDirective(new LineParser(\"Main_Colour CODE 16 VALUE #FF8080 EDGE #333333\")));\n    this.addMaterial(this.parseColorMetaDirective(new LineParser(\"Edge_Colour CODE 24 VALUE #A0A0A0 EDGE #333333\")));\n    return this;\n  }\n  setFileMap(fileMap) {\n    this.fileMap = fileMap;\n    return this;\n  }\n  addMaterial(material) {\n    const matLib = this.materialLibrary;\n    if (!matLib[material.userData.code]) {\n      this.materials.push(material);\n      matLib[material.userData.code] = material;\n    }\n    return this;\n  }\n  getMaterial(colorCode) {\n    if (colorCode.startsWith(\"0x2\")) {\n      const color = colorCode.substring(3);\n      return this.parseColorMetaDirective(new LineParser(\"Direct_Color_\" + color + \" CODE -1 VALUE #\" + color + \" EDGE #\" + color));\n    }\n    return this.materialLibrary[colorCode] || null;\n  }\n  // Applies the appropriate materials to a prebuilt hierarchy of geometry. Assumes that color codes are present\n  // in the material array if they need to be filled in.\n  applyMaterialsToMesh(group, parentColorCode, materialHierarchy, finalMaterialPass = false) {\n    const loader = this;\n    const parentIsPassthrough = parentColorCode === MAIN_COLOUR_CODE;\n    group.traverse(c => {\n      if (c.isMesh || c.isLineSegments) {\n        if (Array.isArray(c.material)) {\n          for (let i = 0, l = c.material.length; i < l; i++) {\n            if (!c.material[i].isMaterial) {\n              c.material[i] = getMaterial(c, c.material[i]);\n            }\n          }\n        } else if (!c.material.isMaterial) {\n          c.material = getMaterial(c, c.material);\n        }\n      }\n    });\n    function getMaterial(c, colorCode) {\n      if (parentIsPassthrough && !(colorCode in materialHierarchy) && !finalMaterialPass) {\n        return colorCode;\n      }\n      const forEdge = c.isLineSegments || c.isConditionalLine;\n      const isPassthrough = !forEdge && colorCode === MAIN_COLOUR_CODE || forEdge && colorCode === MAIN_EDGE_COLOUR_CODE;\n      if (isPassthrough) {\n        colorCode = parentColorCode;\n      }\n      let material = null;\n      if (colorCode in materialHierarchy) {\n        material = materialHierarchy[colorCode];\n      } else if (finalMaterialPass) {\n        material = loader.getMaterial(colorCode);\n        if (material === null) {\n          throw new Error(`LDrawLoader: Material properties for code ${colorCode} not available.`);\n        }\n      } else {\n        return colorCode;\n      }\n      if (c.isLineSegments) {\n        material = material.userData.edgeMaterial;\n        if (c.isConditionalLine) {\n          material = material.userData.conditionalEdgeMaterial;\n        }\n      }\n      return material;\n    }\n  }\n  getMainMaterial() {\n    return this.getMaterial(MAIN_COLOUR_CODE);\n  }\n  getMainEdgeMaterial() {\n    return this.getMaterial(MAIN_EDGE_COLOUR_CODE);\n  }\n  parseColorMetaDirective(lineParser) {\n    let code = null;\n    let color = 16711935;\n    let edgeColor = 16711935;\n    let alpha = 1;\n    let isTransparent = false;\n    let luminance = 0;\n    let finishType = FINISH_TYPE_DEFAULT;\n    let edgeMaterial = null;\n    const name = lineParser.getToken();\n    if (!name) {\n      throw new Error('LDrawLoader: Material name was expected after \"!COLOUR tag' + lineParser.getLineNumberString() + \".\");\n    }\n    let token = null;\n    while (true) {\n      token = lineParser.getToken();\n      if (!token) {\n        break;\n      }\n      switch (token.toUpperCase()) {\n        case \"CODE\":\n          code = lineParser.getToken();\n          break;\n        case \"VALUE\":\n          color = lineParser.getToken();\n          if (color.startsWith(\"0x\")) {\n            color = \"#\" + color.substring(2);\n          } else if (!color.startsWith(\"#\")) {\n            throw new Error(\"LDrawLoader: Invalid color while parsing material\" + lineParser.getLineNumberString() + \".\");\n          }\n          break;\n        case \"EDGE\":\n          edgeColor = lineParser.getToken();\n          if (edgeColor.startsWith(\"0x\")) {\n            edgeColor = \"#\" + edgeColor.substring(2);\n          } else if (!edgeColor.startsWith(\"#\")) {\n            edgeMaterial = this.getMaterial(edgeColor);\n            if (!edgeMaterial) {\n              throw new Error(\"LDrawLoader: Invalid edge color while parsing material\" + lineParser.getLineNumberString() + \".\");\n            }\n            edgeMaterial = edgeMaterial.userData.edgeMaterial;\n          }\n          break;\n        case \"ALPHA\":\n          alpha = parseInt(lineParser.getToken());\n          if (isNaN(alpha)) {\n            throw new Error(\"LDrawLoader: Invalid alpha value in material definition\" + lineParser.getLineNumberString() + \".\");\n          }\n          alpha = Math.max(0, Math.min(1, alpha / 255));\n          if (alpha < 1) {\n            isTransparent = true;\n          }\n          break;\n        case \"LUMINANCE\":\n          luminance = parseInt(lineParser.getToken());\n          if (isNaN(luminance)) {\n            throw new Error(\"LDrawLoader: Invalid luminance value in material definition\" + LineParser.getLineNumberString() + \".\");\n          }\n          luminance = Math.max(0, Math.min(1, luminance / 255));\n          break;\n        case \"CHROME\":\n          finishType = FINISH_TYPE_CHROME;\n          break;\n        case \"PEARLESCENT\":\n          finishType = FINISH_TYPE_PEARLESCENT;\n          break;\n        case \"RUBBER\":\n          finishType = FINISH_TYPE_RUBBER;\n          break;\n        case \"MATTE_METALLIC\":\n          finishType = FINISH_TYPE_MATTE_METALLIC;\n          break;\n        case \"METAL\":\n          finishType = FINISH_TYPE_METAL;\n          break;\n        case \"MATERIAL\":\n          lineParser.setToEnd();\n          break;\n        default:\n          throw new Error('LDrawLoader: Unknown token \"' + token + '\" while parsing material' + lineParser.getLineNumberString() + \".\");\n      }\n    }\n    let material = null;\n    switch (finishType) {\n      case FINISH_TYPE_DEFAULT:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0.3,\n          metalness: 0\n        });\n        break;\n      case FINISH_TYPE_PEARLESCENT:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0.3,\n          metalness: 0.25\n        });\n        break;\n      case FINISH_TYPE_CHROME:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0,\n          metalness: 1\n        });\n        break;\n      case FINISH_TYPE_RUBBER:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0.9,\n          metalness: 0\n        });\n        break;\n      case FINISH_TYPE_MATTE_METALLIC:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0.8,\n          metalness: 0.4\n        });\n        break;\n      case FINISH_TYPE_METAL:\n        material = new MeshStandardMaterial({\n          color,\n          roughness: 0.2,\n          metalness: 0.85\n        });\n        break;\n    }\n    material.transparent = isTransparent;\n    material.premultipliedAlpha = true;\n    material.opacity = alpha;\n    material.depthWrite = !isTransparent;\n    material.polygonOffset = true;\n    material.polygonOffsetFactor = 1;\n    if (luminance !== 0) {\n      material.emissive.set(material.color).multiplyScalar(luminance);\n    }\n    if (!edgeMaterial) {\n      edgeMaterial = new LineBasicMaterial({\n        color: edgeColor,\n        transparent: isTransparent,\n        opacity: alpha,\n        depthWrite: !isTransparent\n      });\n      edgeMaterial.userData.code = code;\n      edgeMaterial.name = name + \" - Edge\";\n      edgeMaterial.userData.conditionalEdgeMaterial = new LDrawConditionalLineMaterial({\n        fog: true,\n        transparent: isTransparent,\n        depthWrite: !isTransparent,\n        color: edgeColor,\n        opacity: alpha\n      });\n    }\n    material.userData.code = code;\n    material.name = name;\n    material.userData.edgeMaterial = edgeMaterial;\n    this.addMaterial(material);\n    return material;\n  }\n  computeConstructionSteps(model) {\n    let stepNumber = 0;\n    model.traverse(c => {\n      if (c.isGroup) {\n        if (c.userData.startingConstructionStep) {\n          stepNumber++;\n        }\n        c.userData.constructionStep = stepNumber;\n      }\n    });\n    model.userData.numConstructionSteps = stepNumber + 1;\n  }\n}\nexport { LDrawLoader };", "map": {"version": 3, "names": ["FINISH_TYPE_DEFAULT", "FINISH_TYPE_CHROME", "FINISH_TYPE_PEARLESCENT", "FINISH_TYPE_RUBBER", "FINISH_TYPE_MATTE_METALLIC", "FINISH_TYPE_METAL", "FILE_LOCATION_AS_IS", "FILE_LOCATION_TRY_PARTS", "FILE_LOCATION_TRY_P", "FILE_LOCATION_TRY_MODELS", "FILE_LOCATION_TRY_RELATIVE", "FILE_LOCATION_TRY_ABSOLUTE", "FILE_LOCATION_NOT_FOUND", "MAIN_COLOUR_CODE", "MAIN_EDGE_COLOUR_CODE", "_tempVec0", "Vector3", "_tempVec1", "LDrawConditionalLineMaterial", "ShaderMaterial", "constructor", "parameters", "uniforms", "UniformsUtils", "merge", "UniformsLib", "fog", "diffuse", "value", "Color", "opacity", "vertexShader", "fragmentShader", "version", "Object", "defineProperties", "get", "set", "color", "set<PERSON><PERSON><PERSON>", "isLDrawConditionalLineMaterial", "ConditionalLineSegments", "LineSegments", "geometry", "material", "isConditionalLine", "generateFaceNormals", "faces", "i", "l", "length", "face", "vertices", "v0", "v1", "v2", "subVectors", "faceNormal", "crossVectors", "normalize", "_ray", "<PERSON>", "smoothNormals", "lineSegments", "checkSubSegments", "hashMultiplier", "hashVertex", "v", "x", "y", "z", "hashEdge", "toNormalizedRay", "targetRay", "direction", "scalar", "dot", "origin", "copy", "addScaledVector", "hashRay", "ray", "hard<PERSON><PERSON>", "Set", "hardEdgeRays", "Map", "halfEdgeList", "normals", "ls", "add", "rh1", "has", "rh2", "info2", "distances", "info", "d0", "d1", "push", "tri", "vertCount", "i2", "index", "next", "hash", "ray<PERSON><PERSON>", "found", "i3", "l2", "halfEdge", "key", "queue", "pop", "vertNormals", "reverseHash", "otherInfo", "otherTri", "otherIndex", "otherNormals", "otherVertCount", "otherFaceNormal", "Math", "abs", "otherNext", "norm", "sharedNormal1", "sharedNormal2", "isPartType", "type", "isPrimitiveType", "test", "<PERSON><PERSON><PERSON><PERSON>", "line", "lineNumber", "lineLength", "currentCharIndex", "currentChar", "seekNonSpace", "char<PERSON>t", "getToken", "pos0", "pos1", "substring", "getVector", "parseFloat", "getRemainingString", "isAtTheEnd", "setToEnd", "getLineNumberString", "LDrawParsedCache", "loader", "_cache", "cloneResult", "original", "result", "map", "colorCode", "clone", "conditionalSegments", "controlPoints", "category", "keywords", "subobjects", "totalFaces", "startingConstructionStep", "materials", "group", "fetchData", "fileName", "triedLowerCase", "locationState", "subobjectURL", "lastIndexOf", "toLowerCase", "fileLoader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "partsLibraryPath", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "loadAsync", "e", "Error", "parse", "getLocalMaterial", "indexOf", "replace", "lines", "split", "numLines", "parsingEmbeddedFiles", "currentEmbeddedFileName", "currentEmbeddedText", "bfcCertified", "bfcCCW", "bfcInverted", "bfcCull", "lineIndex", "startsWith", "setData", "lp", "lineType", "segment", "ccw", "doubleSided", "v3", "c0", "c1", "meta", "parseColorMetaDirective", "userData", "code", "console", "warn", "newKeywords", "for<PERSON>ach", "keyword", "trim", "token", "posX", "posY", "posZ", "m0", "m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "matrix", "Matrix4", "fileName2", "fileMap", "inverted", "getData", "Promise", "ensureDataLoaded", "then", "getMaterialFromCode", "parentColorCode", "materialHierarchy", "forEdge", "isPassthrough", "LDrawPartsGeometryCache", "parseCache", "processIntoMesh", "faceMaterials", "processInfoSubobjects", "subobject", "promises", "subobject2", "promise", "subobjectInfo", "loadModel", "catch", "error", "group2", "Group", "subobjectInfos", "all", "isGroup", "subobjectGroup", "decompose", "position", "quaternion", "scale", "name", "applyMaterialsToMesh", "children", "parentLineSegments", "parentConditionalSegments", "parentFaces", "matrixScaleInverted", "determinant", "lineColorCode", "applyMatrix4", "os", "l3", "reverse", "size", "createObject", "hasCachedModel", "getCachedModel", "parseModel", "sortByMaterial", "a", "b", "elements", "elementSize", "isConditionalSegments", "totalElements", "sort", "positions", "Float32Array", "quadArray", "Array", "bufferGeometry", "BufferGeometry", "prevMaterial", "index0", "numGroupVerts", "offset", "iElem", "nElem", "elem", "j", "elemNormals", "n", "addGroup", "edgeMaterial", "conditionalEdgeMaterial", "Infinity", "setAttribute", "BufferAttribute", "object3d", "<PERSON><PERSON>", "controlArray0", "controlArray1", "directionArray", "LDrawLoader", "Loader", "materialLibrary", "partsCache", "setMaterials", "setPartsLibraryPath", "path", "preloadMaterials", "url", "colorLineRegex", "directive", "load", "onLoad", "onProgress", "onError", "computeConstructionSteps", "addMaterial", "setFileMap", "<PERSON><PERSON><PERSON>", "getMaterial", "finalMaterialPass", "parentIsPassthrough", "traverse", "c", "<PERSON><PERSON><PERSON>", "isLineSegments", "isArray", "isMaterial", "getMainMaterial", "getMainEdgeMaterial", "<PERSON><PERSON><PERSON><PERSON>", "edgeColor", "alpha", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "luminance", "finishType", "toUpperCase", "parseInt", "isNaN", "max", "min", "MeshStandardMaterial", "roughness", "metalness", "transparent", "premultipliedAlpha", "depthWrite", "polygonOffset", "polygonOffsetFactor", "emissive", "multiplyScalar", "LineBasicMaterial", "model", "<PERSON><PERSON><PERSON><PERSON>", "constructionStep", "numConstructionSteps"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/loaders/LDrawLoader.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  FileLoader,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  Matrix4,\n  Mesh,\n  MeshStandardMaterial,\n  ShaderMaterial,\n  UniformsLib,\n  UniformsUtils,\n  Vector3,\n  Ray,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\n// Special surface finish tag types.\n// Note: \"MATERIAL\" tag (e.g. GLITTER, SPECKLE) is not implemented\nconst FINISH_TYPE_DEFAULT = 0\nconst FINISH_TYPE_CHROME = 1\nconst FINISH_TYPE_PEARLESCENT = 2\nconst FINISH_TYPE_RUBBER = 3\nconst FINISH_TYPE_MATTE_METALLIC = 4\nconst FINISH_TYPE_METAL = 5\n\n// State machine to search a subobject path.\n// The LDraw standard establishes these various possible subfolders.\nconst FILE_LOCATION_AS_IS = 0\nconst FILE_LOCATION_TRY_PARTS = 1\nconst FILE_LOCATION_TRY_P = 2\nconst FILE_LOCATION_TRY_MODELS = 3\nconst FILE_LOCATION_TRY_RELATIVE = 4\nconst FILE_LOCATION_TRY_ABSOLUTE = 5\nconst FILE_LOCATION_NOT_FOUND = 6\n\nconst MAIN_COLOUR_CODE = '16'\nconst MAIN_EDGE_COLOUR_CODE = '24'\n\nconst _tempVec0 = /* @__PURE__ */ new Vector3()\nconst _tempVec1 = /* @__PURE__ */ new Vector3()\n\nclass LDrawConditionalLineMaterial extends ShaderMaterial {\n  constructor(parameters) {\n    super({\n      uniforms: UniformsUtils.merge([\n        UniformsLib.fog,\n        {\n          diffuse: {\n            value: new Color(),\n          },\n          opacity: {\n            value: 1.0,\n          },\n        },\n      ]),\n\n      vertexShader: /* glsl */ `\n        attribute vec3 control0;\n        attribute vec3 control1;\n        attribute vec3 direction;\n        varying float discardFlag;\n\n        #include <common>\n        #include <color_pars_vertex>\n        #include <fog_pars_vertex>\n        #include <logdepthbuf_pars_vertex>\n        #include <clipping_planes_pars_vertex>\n\n        void main() {\n          #include <color_vertex>\n\n          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n          gl_Position = projectionMatrix * mvPosition;\n\n          // Transform the line segment ends and control points into camera clip space\n          vec4 c0 = projectionMatrix * modelViewMatrix * vec4(control0, 1.0);\n          vec4 c1 = projectionMatrix * modelViewMatrix * vec4(control1, 1.0);\n          vec4 p0 = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          vec4 p1 = projectionMatrix * modelViewMatrix * vec4(position + direction, 1.0);\n\n          c0.xy /= c0.w;\n          c1.xy /= c1.w;\n          p0.xy /= p0.w;\n          p1.xy /= p1.w;\n\n          // Get the direction of the segment and an orthogonal vector\n          vec2 dir = p1.xy - p0.xy;\n          vec2 norm = vec2(-dir.y, dir.x);\n\n          // Get control point directions from the line\n          vec2 c0dir = c0.xy - p1.xy;\n          vec2 c1dir = c1.xy - p1.xy;\n\n          // If the vectors to the controls points are pointed in different directions away\n          // from the line segment then the line should not be drawn.\n          float d0 = dot(normalize(norm), normalize(c0dir));\n          float d1 = dot(normalize(norm), normalize(c1dir));\n          discardFlag = float(sign(d0) != sign(d1));\n\n          #include <logdepthbuf_vertex>\n          #include <clipping_planes_vertex>\n          #include <fog_vertex>\n        }\n      `,\n\n      fragmentShader: /* glsl */ `\n        uniform vec3 diffuse;\n        uniform float opacity;\n        varying float discardFlag;\n\n        #include <common>\n        #include <color_pars_fragment>\n        #include <fog_pars_fragment>\n        #include <logdepthbuf_pars_fragment>\n        #include <clipping_planes_pars_fragment>\n\n        void main() {\n          if (discardFlag > 0.5) discard;\n\n          #include <clipping_planes_fragment>\n          vec3 outgoingLight = vec3(0.0);\n          vec4 diffuseColor = vec4(diffuse, opacity);\n          #include <logdepthbuf_fragment>\n          #include <color_fragment>\n          outgoingLight = diffuseColor.rgb; // simple shader\n          gl_FragColor = vec4(outgoingLight, diffuseColor.a);\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n          #include <fog_fragment>\n          #include <premultiplied_alpha_fragment>\n        }\n      `,\n    })\n\n    Object.defineProperties(this, {\n      opacity: {\n        get: function () {\n          return this.uniforms.opacity.value\n        },\n\n        set: function (value) {\n          this.uniforms.opacity.value = value\n        },\n      },\n\n      color: {\n        get: function () {\n          return this.uniforms.diffuse.value\n        },\n      },\n    })\n\n    this.setValues(parameters)\n    this.isLDrawConditionalLineMaterial = true\n  }\n}\n\nclass ConditionalLineSegments extends LineSegments {\n  constructor(geometry, material) {\n    super(geometry, material)\n    this.isConditionalLine = true\n  }\n}\n\nfunction generateFaceNormals(faces) {\n  for (let i = 0, l = faces.length; i < l; i++) {\n    const face = faces[i]\n    const vertices = face.vertices\n    const v0 = vertices[0]\n    const v1 = vertices[1]\n    const v2 = vertices[2]\n\n    _tempVec0.subVectors(v1, v0)\n    _tempVec1.subVectors(v2, v1)\n    face.faceNormal = new Vector3().crossVectors(_tempVec0, _tempVec1).normalize()\n  }\n}\n\nconst _ray = /* @__PURE__ */ new Ray()\nfunction smoothNormals(faces, lineSegments, checkSubSegments = false) {\n  // NOTE: 1e2 is pretty coarse but was chosen to quantize the resulting value because\n  // it allows edges to be smoothed as expected (see minifig arms).\n  // --\n  // And the vector values are initialize multiplied by 1 + 1e-10 to account for floating\n  // point errors on vertices along quantization boundaries. Ie after matrix multiplication\n  // vertices that should be merged might be set to \"1.7\" and \"1.6999...\" meaning they won't\n  // get merged. This added epsilon attempts to push these error values to the same quantized\n  // value for the sake of hashing. See \"AT-ST mini\" dishes. See mrdoob/three#23169.\n\n  const hashMultiplier = (1 + 1e-10) * 1e2\n  function hashVertex(v) {\n    const x = ~~(v.x * hashMultiplier)\n    const y = ~~(v.y * hashMultiplier)\n    const z = ~~(v.z * hashMultiplier)\n\n    return `${x},${y},${z}`\n  }\n\n  function hashEdge(v0, v1) {\n    return `${hashVertex(v0)}_${hashVertex(v1)}`\n  }\n\n  // converts the two vertices to a ray with a normalized direction and origin of 0, 0, 0 projected\n  // onto the original line.\n  function toNormalizedRay(v0, v1, targetRay) {\n    targetRay.direction.subVectors(v1, v0).normalize()\n\n    const scalar = v0.dot(targetRay.direction)\n    targetRay.origin.copy(v0).addScaledVector(targetRay.direction, -scalar)\n\n    return targetRay\n  }\n\n  function hashRay(ray) {\n    return hashEdge(ray.origin, ray.direction)\n  }\n\n  const hardEdges = new Set()\n  const hardEdgeRays = new Map()\n  const halfEdgeList = {}\n  const normals = []\n\n  // Save the list of hard edges by hash\n  for (let i = 0, l = lineSegments.length; i < l; i++) {\n    const ls = lineSegments[i]\n    const vertices = ls.vertices\n    const v0 = vertices[0]\n    const v1 = vertices[1]\n    hardEdges.add(hashEdge(v0, v1))\n    hardEdges.add(hashEdge(v1, v0))\n\n    // only generate the hard edge ray map if we're checking subsegments because it's more expensive to check\n    // and requires more memory.\n    if (checkSubSegments) {\n      // add both ray directions to the map\n      const ray = toNormalizedRay(v0, v1, new Ray())\n      const rh1 = hashRay(ray)\n      if (!hardEdgeRays.has(rh1)) {\n        toNormalizedRay(v1, v0, ray)\n        const rh2 = hashRay(ray)\n\n        const info = {\n          ray,\n          distances: [],\n        }\n\n        hardEdgeRays.set(rh1, info)\n        hardEdgeRays.set(rh2, info)\n      }\n\n      // store both segments ends in min, max order in the distances array to check if a face edge is a\n      // subsegment later.\n      const info = hardEdgeRays.get(rh1)\n      let d0 = info.ray.direction.dot(v0)\n      let d1 = info.ray.direction.dot(v1)\n      if (d0 > d1) {\n        ;[d0, d1] = [d1, d0]\n      }\n\n      info.distances.push(d0, d1)\n    }\n  }\n\n  // track the half edges associated with each triangle\n  for (let i = 0, l = faces.length; i < l; i++) {\n    const tri = faces[i]\n    const vertices = tri.vertices\n    const vertCount = vertices.length\n    for (let i2 = 0; i2 < vertCount; i2++) {\n      const index = i2\n      const next = (i2 + 1) % vertCount\n      const v0 = vertices[index]\n      const v1 = vertices[next]\n      const hash = hashEdge(v0, v1)\n\n      // don't add the triangle if the edge is supposed to be hard\n      if (hardEdges.has(hash)) {\n        continue\n      }\n\n      // if checking subsegments then check to see if this edge lies on a hard edge ray and whether its within any ray bounds\n      if (checkSubSegments) {\n        toNormalizedRay(v0, v1, _ray)\n\n        const rayHash = hashRay(_ray)\n        if (hardEdgeRays.has(rayHash)) {\n          const info = hardEdgeRays.get(rayHash)\n          const { ray, distances } = info\n          let d0 = ray.direction.dot(v0)\n          let d1 = ray.direction.dot(v1)\n\n          if (d0 > d1) {\n            ;[d0, d1] = [d1, d0]\n          }\n\n          // return early if the face edge is found to be a subsegment of a line edge meaning the edge will have \"hard\" normals\n          let found = false\n          for (let i = 0, l = distances.length; i < l; i += 2) {\n            if (d0 >= distances[i] && d1 <= distances[i + 1]) {\n              found = true\n              break\n            }\n          }\n\n          if (found) {\n            continue\n          }\n        }\n      }\n\n      const info = {\n        index: index,\n        tri: tri,\n      }\n      halfEdgeList[hash] = info\n    }\n  }\n\n  // Iterate until we've tried to connect all faces to share normals\n  while (true) {\n    // Stop if there are no more faces left\n    let halfEdge = null\n    for (const key in halfEdgeList) {\n      halfEdge = halfEdgeList[key]\n      break\n    }\n\n    if (halfEdge === null) {\n      break\n    }\n\n    // Exhaustively find all connected faces\n    const queue = [halfEdge]\n    while (queue.length > 0) {\n      // initialize all vertex normals in this triangle\n      const tri = queue.pop().tri\n      const vertices = tri.vertices\n      const vertNormals = tri.normals\n      const faceNormal = tri.faceNormal\n\n      // Check if any edge is connected to another triangle edge\n      const vertCount = vertices.length\n      for (let i2 = 0; i2 < vertCount; i2++) {\n        const index = i2\n        const next = (i2 + 1) % vertCount\n        const v0 = vertices[index]\n        const v1 = vertices[next]\n\n        // delete this triangle from the list so it won't be found again\n        const hash = hashEdge(v0, v1)\n        delete halfEdgeList[hash]\n\n        const reverseHash = hashEdge(v1, v0)\n        const otherInfo = halfEdgeList[reverseHash]\n        if (otherInfo) {\n          const otherTri = otherInfo.tri\n          const otherIndex = otherInfo.index\n          const otherNormals = otherTri.normals\n          const otherVertCount = otherNormals.length\n          const otherFaceNormal = otherTri.faceNormal\n\n          // NOTE: If the angle between faces is > 67.5 degrees then assume it's\n          // hard edge. There are some cases where the line segments do not line up exactly\n          // with or span multiple triangle edges (see Lunar Vehicle wheels).\n          if (Math.abs(otherTri.faceNormal.dot(tri.faceNormal)) < 0.25) {\n            continue\n          }\n\n          // if this triangle has already been traversed then it won't be in\n          // the halfEdgeList. If it has not then add it to the queue and delete\n          // it so it won't be found again.\n          if (reverseHash in halfEdgeList) {\n            queue.push(otherInfo)\n            delete halfEdgeList[reverseHash]\n          }\n\n          // share the first normal\n          const otherNext = (otherIndex + 1) % otherVertCount\n          if (vertNormals[index] && otherNormals[otherNext] && vertNormals[index] !== otherNormals[otherNext]) {\n            otherNormals[otherNext].norm.add(vertNormals[index].norm)\n            vertNormals[index].norm = otherNormals[otherNext].norm\n          }\n\n          let sharedNormal1 = vertNormals[index] || otherNormals[otherNext]\n          if (sharedNormal1 === null) {\n            // it's possible to encounter an edge of a triangle that has already been traversed meaning\n            // both edges already have different normals defined and shared. To work around this we create\n            // a wrapper object so when those edges are merged the normals can be updated everywhere.\n            sharedNormal1 = { norm: new Vector3() }\n            normals.push(sharedNormal1.norm)\n          }\n\n          if (vertNormals[index] === null) {\n            vertNormals[index] = sharedNormal1\n            sharedNormal1.norm.add(faceNormal)\n          }\n\n          if (otherNormals[otherNext] === null) {\n            otherNormals[otherNext] = sharedNormal1\n            sharedNormal1.norm.add(otherFaceNormal)\n          }\n\n          // share the second normal\n          if (vertNormals[next] && otherNormals[otherIndex] && vertNormals[next] !== otherNormals[otherIndex]) {\n            otherNormals[otherIndex].norm.add(vertNormals[next].norm)\n            vertNormals[next].norm = otherNormals[otherIndex].norm\n          }\n\n          let sharedNormal2 = vertNormals[next] || otherNormals[otherIndex]\n          if (sharedNormal2 === null) {\n            sharedNormal2 = { norm: new Vector3() }\n            normals.push(sharedNormal2.norm)\n          }\n\n          if (vertNormals[next] === null) {\n            vertNormals[next] = sharedNormal2\n            sharedNormal2.norm.add(faceNormal)\n          }\n\n          if (otherNormals[otherIndex] === null) {\n            otherNormals[otherIndex] = sharedNormal2\n            sharedNormal2.norm.add(otherFaceNormal)\n          }\n        }\n      }\n    }\n  }\n\n  // The normals of each face have been added up so now we average them by normalizing the vector.\n  for (let i = 0, l = normals.length; i < l; i++) {\n    normals[i].normalize()\n  }\n}\n\nfunction isPartType(type) {\n  return type === 'Part' || type === 'Unofficial_Part'\n}\n\nfunction isPrimitiveType(type) {\n  return /primitive/i.test(type) || type === 'Subpart'\n}\n\nclass LineParser {\n  constructor(line, lineNumber) {\n    this.line = line\n    this.lineLength = line.length\n    this.currentCharIndex = 0\n    this.currentChar = ' '\n    this.lineNumber = lineNumber\n  }\n\n  seekNonSpace() {\n    while (this.currentCharIndex < this.lineLength) {\n      this.currentChar = this.line.charAt(this.currentCharIndex)\n\n      if (this.currentChar !== ' ' && this.currentChar !== '\\t') {\n        return\n      }\n\n      this.currentCharIndex++\n    }\n  }\n\n  getToken() {\n    const pos0 = this.currentCharIndex++\n\n    // Seek space\n    while (this.currentCharIndex < this.lineLength) {\n      this.currentChar = this.line.charAt(this.currentCharIndex)\n\n      if (this.currentChar === ' ' || this.currentChar === '\\t') {\n        break\n      }\n\n      this.currentCharIndex++\n    }\n\n    const pos1 = this.currentCharIndex\n\n    this.seekNonSpace()\n\n    return this.line.substring(pos0, pos1)\n  }\n\n  getVector() {\n    return new Vector3(parseFloat(this.getToken()), parseFloat(this.getToken()), parseFloat(this.getToken()))\n  }\n\n  getRemainingString() {\n    return this.line.substring(this.currentCharIndex, this.lineLength)\n  }\n\n  isAtTheEnd() {\n    return this.currentCharIndex >= this.lineLength\n  }\n\n  setToEnd() {\n    this.currentCharIndex = this.lineLength\n  }\n\n  getLineNumberString() {\n    return this.lineNumber >= 0 ? ' at line ' + this.lineNumber : ''\n  }\n}\n\n// Fetches and parses an intermediate representation of LDraw parts files.\nclass LDrawParsedCache {\n  constructor(loader) {\n    this.loader = loader\n    this._cache = {}\n  }\n\n  cloneResult(original) {\n    const result = {}\n\n    // vertices are transformed and normals computed before being converted to geometry\n    // so these pieces must be cloned.\n    result.faces = original.faces.map((face) => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map((v) => v.clone()),\n        normals: face.normals.map(() => null),\n        faceNormal: null,\n      }\n    })\n\n    result.conditionalSegments = original.conditionalSegments.map((face) => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map((v) => v.clone()),\n        controlPoints: face.controlPoints.map((v) => v.clone()),\n      }\n    })\n\n    result.lineSegments = original.lineSegments.map((face) => {\n      return {\n        colorCode: face.colorCode,\n        material: face.material,\n        vertices: face.vertices.map((v) => v.clone()),\n      }\n    })\n\n    // none if this is subsequently modified\n    result.type = original.type\n    result.category = original.category\n    result.keywords = original.keywords\n    result.subobjects = original.subobjects\n    result.totalFaces = original.totalFaces\n    result.startingConstructionStep = original.startingConstructionStep\n    result.materials = original.materials\n    result.group = null\n    return result\n  }\n\n  async fetchData(fileName) {\n    let triedLowerCase = false\n    let locationState = FILE_LOCATION_AS_IS\n    while (locationState !== FILE_LOCATION_NOT_FOUND) {\n      let subobjectURL = fileName\n      switch (locationState) {\n        case FILE_LOCATION_AS_IS:\n          locationState = locationState + 1\n          break\n\n        case FILE_LOCATION_TRY_PARTS:\n          subobjectURL = 'parts/' + subobjectURL\n          locationState = locationState + 1\n          break\n\n        case FILE_LOCATION_TRY_P:\n          subobjectURL = 'p/' + subobjectURL\n          locationState = locationState + 1\n          break\n\n        case FILE_LOCATION_TRY_MODELS:\n          subobjectURL = 'models/' + subobjectURL\n          locationState = locationState + 1\n          break\n\n        case FILE_LOCATION_TRY_RELATIVE:\n          subobjectURL = fileName.substring(0, fileName.lastIndexOf('/') + 1) + subobjectURL\n          locationState = locationState + 1\n          break\n\n        case FILE_LOCATION_TRY_ABSOLUTE:\n          if (triedLowerCase) {\n            // Try absolute path\n            locationState = FILE_LOCATION_NOT_FOUND\n          } else {\n            // Next attempt is lower case\n            fileName = fileName.toLowerCase()\n            subobjectURL = fileName\n            triedLowerCase = true\n            locationState = FILE_LOCATION_AS_IS\n          }\n\n          break\n      }\n\n      const loader = this.loader\n      const fileLoader = new FileLoader(loader.manager)\n      fileLoader.setPath(loader.partsLibraryPath)\n      fileLoader.setRequestHeader(loader.requestHeader)\n      fileLoader.setWithCredentials(loader.withCredentials)\n\n      try {\n        const text = await fileLoader.loadAsync(subobjectURL)\n        return text\n      } catch {\n        continue\n      }\n    }\n\n    throw new Error('LDrawLoader: Subobject \"' + fileName + '\" could not be loaded.')\n  }\n\n  parse(text, fileName = null) {\n    const loader = this.loader\n\n    // final results\n    const faces = []\n    const lineSegments = []\n    const conditionalSegments = []\n    const subobjects = []\n    const materials = {}\n\n    const getLocalMaterial = (colorCode) => {\n      return materials[colorCode] || null\n    }\n\n    let type = 'Model'\n    let category = null\n    let keywords = null\n    let totalFaces = 0\n\n    // split into lines\n    if (text.indexOf('\\r\\n') !== -1) {\n      // This is faster than String.split with regex that splits on both\n      text = text.replace(/\\r\\n/g, '\\n')\n    }\n\n    const lines = text.split('\\n')\n    const numLines = lines.length\n\n    let parsingEmbeddedFiles = false\n    let currentEmbeddedFileName = null\n    let currentEmbeddedText = null\n\n    let bfcCertified = false\n    let bfcCCW = true\n    let bfcInverted = false\n    let bfcCull = true\n\n    let startingConstructionStep = false\n\n    // Parse all line commands\n    for (let lineIndex = 0; lineIndex < numLines; lineIndex++) {\n      const line = lines[lineIndex]\n\n      if (line.length === 0) continue\n\n      if (parsingEmbeddedFiles) {\n        if (line.startsWith('0 FILE ')) {\n          // Save previous embedded file in the cache\n          this.setData(currentEmbeddedFileName, currentEmbeddedText)\n\n          // New embedded text file\n          currentEmbeddedFileName = line.substring(7)\n          currentEmbeddedText = ''\n        } else {\n          currentEmbeddedText += line + '\\n'\n        }\n\n        continue\n      }\n\n      const lp = new LineParser(line, lineIndex + 1)\n      lp.seekNonSpace()\n\n      if (lp.isAtTheEnd()) {\n        // Empty line\n        continue\n      }\n\n      // Parse the line type\n      const lineType = lp.getToken()\n\n      let material\n      let colorCode\n      let segment\n      let ccw\n      let doubleSided\n      let v0, v1, v2, v3, c0, c1\n\n      switch (lineType) {\n        // Line type 0: Comment or META\n        case '0':\n          // Parse meta directive\n          const meta = lp.getToken()\n\n          if (meta) {\n            switch (meta) {\n              case '!LDRAW_ORG':\n                type = lp.getToken()\n                break\n\n              case '!COLOUR':\n                material = loader.parseColorMetaDirective(lp)\n                if (material) {\n                  materials[material.userData.code] = material\n                } else {\n                  console.warn('LDrawLoader: Error parsing material' + lp.getLineNumberString())\n                }\n\n                break\n\n              case '!CATEGORY':\n                category = lp.getToken()\n                break\n\n              case '!KEYWORDS':\n                const newKeywords = lp.getRemainingString().split(',')\n                if (newKeywords.length > 0) {\n                  if (!keywords) {\n                    keywords = []\n                  }\n\n                  newKeywords.forEach(function (keyword) {\n                    keywords.push(keyword.trim())\n                  })\n                }\n\n                break\n\n              case 'FILE':\n                if (lineIndex > 0) {\n                  // Start embedded text files parsing\n                  parsingEmbeddedFiles = true\n                  currentEmbeddedFileName = lp.getRemainingString()\n                  currentEmbeddedText = ''\n\n                  bfcCertified = false\n                  bfcCCW = true\n                }\n\n                break\n\n              case 'BFC':\n                // Changes to the backface culling state\n                while (!lp.isAtTheEnd()) {\n                  const token = lp.getToken()\n\n                  switch (token) {\n                    case 'CERTIFY':\n                    case 'NOCERTIFY':\n                      bfcCertified = token === 'CERTIFY'\n                      bfcCCW = true\n\n                      break\n\n                    case 'CW':\n                    case 'CCW':\n                      bfcCCW = token === 'CCW'\n\n                      break\n\n                    case 'INVERTNEXT':\n                      bfcInverted = true\n\n                      break\n\n                    case 'CLIP':\n                    case 'NOCLIP':\n                      bfcCull = token === 'CLIP'\n\n                      break\n\n                    default:\n                      console.warn('THREE.LDrawLoader: BFC directive \"' + token + '\" is unknown.')\n\n                      break\n                  }\n                }\n\n                break\n\n              case 'STEP':\n                startingConstructionStep = true\n\n                break\n\n              default:\n                // Other meta directives are not implemented\n                break\n            }\n          }\n\n          break\n\n        // Line type 1: Sub-object file\n        case '1':\n          colorCode = lp.getToken()\n          material = getLocalMaterial(colorCode)\n\n          const posX = parseFloat(lp.getToken())\n          const posY = parseFloat(lp.getToken())\n          const posZ = parseFloat(lp.getToken())\n          const m0 = parseFloat(lp.getToken())\n          const m1 = parseFloat(lp.getToken())\n          const m2 = parseFloat(lp.getToken())\n          const m3 = parseFloat(lp.getToken())\n          const m4 = parseFloat(lp.getToken())\n          const m5 = parseFloat(lp.getToken())\n          const m6 = parseFloat(lp.getToken())\n          const m7 = parseFloat(lp.getToken())\n          const m8 = parseFloat(lp.getToken())\n\n          const matrix = new Matrix4().set(m0, m1, m2, posX, m3, m4, m5, posY, m6, m7, m8, posZ, 0, 0, 0, 1)\n\n          let fileName = lp.getRemainingString().trim().replace(/\\\\/g, '/')\n\n          if (loader.fileMap[fileName]) {\n            // Found the subobject path in the preloaded file path map\n            fileName = loader.fileMap[fileName]\n          } else {\n            // Standardized subfolders\n            if (fileName.startsWith('s/')) {\n              fileName = 'parts/' + fileName\n            } else if (fileName.startsWith('48/')) {\n              fileName = 'p/' + fileName\n            }\n          }\n\n          subobjects.push({\n            material: material,\n            colorCode: colorCode,\n            matrix: matrix,\n            fileName: fileName,\n            inverted: bfcInverted,\n            startingConstructionStep: startingConstructionStep,\n          })\n\n          bfcInverted = false\n\n          break\n\n        // Line type 2: Line segment\n        case '2':\n          colorCode = lp.getToken()\n          material = getLocalMaterial(colorCode)\n          v0 = lp.getVector()\n          v1 = lp.getVector()\n\n          segment = {\n            material: material,\n            colorCode: colorCode,\n            vertices: [v0, v1],\n          }\n\n          lineSegments.push(segment)\n\n          break\n\n        // Line type 5: Conditional Line segment\n        case '5':\n          colorCode = lp.getToken()\n          material = getLocalMaterial(colorCode)\n          v0 = lp.getVector()\n          v1 = lp.getVector()\n          c0 = lp.getVector()\n          c1 = lp.getVector()\n\n          segment = {\n            material: material,\n            colorCode: colorCode,\n            vertices: [v0, v1],\n            controlPoints: [c0, c1],\n          }\n\n          conditionalSegments.push(segment)\n\n          break\n\n        // Line type 3: Triangle\n        case '3':\n          colorCode = lp.getToken()\n          material = getLocalMaterial(colorCode)\n          ccw = bfcCCW\n          doubleSided = !bfcCertified || !bfcCull\n\n          if (ccw === true) {\n            v0 = lp.getVector()\n            v1 = lp.getVector()\n            v2 = lp.getVector()\n          } else {\n            v2 = lp.getVector()\n            v1 = lp.getVector()\n            v0 = lp.getVector()\n          }\n\n          faces.push({\n            material: material,\n            colorCode: colorCode,\n            faceNormal: null,\n            vertices: [v0, v1, v2],\n            normals: [null, null, null],\n          })\n          totalFaces++\n\n          if (doubleSided === true) {\n            faces.push({\n              material: material,\n              colorCode: colorCode,\n              faceNormal: null,\n              vertices: [v2, v1, v0],\n              normals: [null, null, null],\n            })\n            totalFaces++\n          }\n\n          break\n\n        // Line type 4: Quadrilateral\n        case '4':\n          colorCode = lp.getToken()\n          material = getLocalMaterial(colorCode)\n          ccw = bfcCCW\n          doubleSided = !bfcCertified || !bfcCull\n\n          if (ccw === true) {\n            v0 = lp.getVector()\n            v1 = lp.getVector()\n            v2 = lp.getVector()\n            v3 = lp.getVector()\n          } else {\n            v3 = lp.getVector()\n            v2 = lp.getVector()\n            v1 = lp.getVector()\n            v0 = lp.getVector()\n          }\n\n          // specifically place the triangle diagonal in the v0 and v1 slots so we can\n          // account for the doubling of vertices later when smoothing normals.\n          faces.push({\n            material: material,\n            colorCode: colorCode,\n            faceNormal: null,\n            vertices: [v0, v1, v2, v3],\n            normals: [null, null, null, null],\n          })\n          totalFaces += 2\n\n          if (doubleSided === true) {\n            faces.push({\n              material: material,\n              colorCode: colorCode,\n              faceNormal: null,\n              vertices: [v3, v2, v1, v0],\n              normals: [null, null, null, null],\n            })\n            totalFaces += 2\n          }\n\n          break\n\n        default:\n          throw new Error('LDrawLoader: Unknown line type \"' + lineType + '\"' + lp.getLineNumberString() + '.')\n      }\n    }\n\n    if (parsingEmbeddedFiles) {\n      this.setData(currentEmbeddedFileName, currentEmbeddedText)\n    }\n\n    return {\n      faces,\n      conditionalSegments,\n      lineSegments,\n      type,\n      category,\n      keywords,\n      subobjects,\n      totalFaces,\n      startingConstructionStep,\n      materials,\n      fileName,\n      group: null,\n    }\n  }\n\n  // returns an (optionally cloned) instance of the data\n  getData(fileName, clone = true) {\n    const key = fileName.toLowerCase()\n    const result = this._cache[key]\n    if (result === null || result instanceof Promise) {\n      return null\n    }\n\n    if (clone) {\n      return this.cloneResult(result)\n    } else {\n      return result\n    }\n  }\n\n  // kicks off a fetch and parse of the requested data if it hasn't already been loaded. Returns when\n  // the data is ready to use and can be retrieved synchronously with \"getData\".\n  async ensureDataLoaded(fileName) {\n    const key = fileName.toLowerCase()\n    if (!(key in this._cache)) {\n      // replace the promise with a copy of the parsed data for immediate processing\n      this._cache[key] = this.fetchData(fileName).then((text) => {\n        const info = this.parse(text, fileName)\n        this._cache[key] = info\n        return info\n      })\n    }\n\n    await this._cache[key]\n  }\n\n  // sets the data in the cache from parsed data\n  setData(fileName, text) {\n    const key = fileName.toLowerCase()\n    this._cache[key] = this.parse(text, fileName)\n  }\n}\n\n// returns the material for an associated color code. If the color code is 16 for a face or 24 for\n// an edge then the passthroughColorCode is used.\nfunction getMaterialFromCode(colorCode, parentColorCode, materialHierarchy, forEdge) {\n  const isPassthrough = (!forEdge && colorCode === MAIN_COLOUR_CODE) || (forEdge && colorCode === MAIN_EDGE_COLOUR_CODE)\n  if (isPassthrough) {\n    colorCode = parentColorCode\n  }\n\n  return materialHierarchy[colorCode] || null\n}\n\n// Class used to parse and build LDraw parts as three.js objects and cache them if they're a \"Part\" type.\nclass LDrawPartsGeometryCache {\n  constructor(loader) {\n    this.loader = loader\n    this.parseCache = new LDrawParsedCache(loader)\n    this._cache = {}\n  }\n\n  // Convert the given file information into a mesh by processing subobjects.\n  async processIntoMesh(info) {\n    const loader = this.loader\n    const parseCache = this.parseCache\n    const faceMaterials = new Set()\n\n    // Processes the part subobject information to load child parts and merge geometry onto part\n    // piece object.\n    const processInfoSubobjects = async (info, subobject = null) => {\n      const subobjects = info.subobjects\n      const promises = []\n\n      // Trigger load of all subobjects. If a subobject isn't a primitive then load it as a separate\n      // group which lets instruction steps apply correctly.\n      for (let i = 0, l = subobjects.length; i < l; i++) {\n        const subobject = subobjects[i]\n        const promise = parseCache.ensureDataLoaded(subobject.fileName).then(() => {\n          const subobjectInfo = parseCache.getData(subobject.fileName, false)\n          if (!isPrimitiveType(subobjectInfo.type)) {\n            return this.loadModel(subobject.fileName).catch((error) => {\n              console.warn(error)\n              return null\n            })\n          }\n\n          return processInfoSubobjects(parseCache.getData(subobject.fileName), subobject)\n        })\n\n        promises.push(promise)\n      }\n\n      const group = new Group()\n      group.userData.category = info.category\n      group.userData.keywords = info.keywords\n      info.group = group\n\n      const subobjectInfos = await Promise.all(promises)\n      for (let i = 0, l = subobjectInfos.length; i < l; i++) {\n        const subobject = info.subobjects[i]\n        const subobjectInfo = subobjectInfos[i]\n\n        if (subobjectInfo === null) {\n          // the subobject failed to load\n          continue\n        }\n\n        // if the subobject was loaded as a separate group then apply the parent scopes materials\n        if (subobjectInfo.isGroup) {\n          const subobjectGroup = subobjectInfo\n          subobject.matrix.decompose(subobjectGroup.position, subobjectGroup.quaternion, subobjectGroup.scale)\n          subobjectGroup.userData.startingConstructionStep = subobject.startingConstructionStep\n          subobjectGroup.name = subobject.fileName\n\n          loader.applyMaterialsToMesh(subobjectGroup, subobject.colorCode, info.materials)\n\n          group.add(subobjectGroup)\n          continue\n        }\n\n        // add the subobject group if it has children in case it has both children and primitives\n        if (subobjectInfo.group.children.length) {\n          group.add(subobjectInfo.group)\n        }\n\n        // transform the primitives into the local space of the parent piece and append them to\n        // to the parent primitives list.\n        const parentLineSegments = info.lineSegments\n        const parentConditionalSegments = info.conditionalSegments\n        const parentFaces = info.faces\n\n        const lineSegments = subobjectInfo.lineSegments\n        const conditionalSegments = subobjectInfo.conditionalSegments\n\n        const faces = subobjectInfo.faces\n        const matrix = subobject.matrix\n        const inverted = subobject.inverted\n        const matrixScaleInverted = matrix.determinant() < 0\n        const colorCode = subobject.colorCode\n\n        const lineColorCode = colorCode === MAIN_COLOUR_CODE ? MAIN_EDGE_COLOUR_CODE : colorCode\n        for (let i = 0, l = lineSegments.length; i < l; i++) {\n          const ls = lineSegments[i]\n          const vertices = ls.vertices\n          vertices[0].applyMatrix4(matrix)\n          vertices[1].applyMatrix4(matrix)\n          ls.colorCode = ls.colorCode === MAIN_EDGE_COLOUR_CODE ? lineColorCode : ls.colorCode\n          ls.material = ls.material || getMaterialFromCode(ls.colorCode, ls.colorCode, info.materials, true)\n\n          parentLineSegments.push(ls)\n        }\n\n        for (let i = 0, l = conditionalSegments.length; i < l; i++) {\n          const os = conditionalSegments[i]\n          const vertices = os.vertices\n          const controlPoints = os.controlPoints\n          vertices[0].applyMatrix4(matrix)\n          vertices[1].applyMatrix4(matrix)\n          controlPoints[0].applyMatrix4(matrix)\n          controlPoints[1].applyMatrix4(matrix)\n          os.colorCode = os.colorCode === MAIN_EDGE_COLOUR_CODE ? lineColorCode : os.colorCode\n          os.material = os.material || getMaterialFromCode(os.colorCode, os.colorCode, info.materials, true)\n\n          parentConditionalSegments.push(os)\n        }\n\n        for (let i = 0, l = faces.length; i < l; i++) {\n          const tri = faces[i]\n          const vertices = tri.vertices\n          for (let i = 0, l = vertices.length; i < l; i++) {\n            vertices[i].applyMatrix4(matrix)\n          }\n\n          tri.colorCode = tri.colorCode === MAIN_COLOUR_CODE ? colorCode : tri.colorCode\n          tri.material = tri.material || getMaterialFromCode(tri.colorCode, colorCode, info.materials, false)\n          faceMaterials.add(tri.colorCode)\n\n          // If the scale of the object is negated then the triangle winding order\n          // needs to be flipped.\n          if (matrixScaleInverted !== inverted) {\n            vertices.reverse()\n          }\n\n          parentFaces.push(tri)\n        }\n\n        info.totalFaces += subobjectInfo.totalFaces\n      }\n\n      // Apply the parent subobjects pass through material code to this object. This is done several times due\n      // to material scoping.\n      if (subobject) {\n        loader.applyMaterialsToMesh(group, subobject.colorCode, info.materials)\n      }\n\n      return info\n    }\n\n    // Track material use to see if we need to use the normal smooth slow path for hard edges.\n    for (let i = 0, l = info.faces; i < l; i++) {\n      faceMaterials.add(info.faces[i].colorCode)\n    }\n\n    await processInfoSubobjects(info)\n\n    if (loader.smoothNormals) {\n      const checkSubSegments = faceMaterials.size > 1\n      generateFaceNormals(info.faces)\n      smoothNormals(info.faces, info.lineSegments, checkSubSegments)\n    }\n\n    // Add the primitive objects and metadata.\n    const group = info.group\n    if (info.faces.length > 0) {\n      group.add(createObject(info.faces, 3, false, info.totalFaces))\n    }\n\n    if (info.lineSegments.length > 0) {\n      group.add(createObject(info.lineSegments, 2))\n    }\n\n    if (info.conditionalSegments.length > 0) {\n      group.add(createObject(info.conditionalSegments, 2, true))\n    }\n\n    return group\n  }\n\n  hasCachedModel(fileName) {\n    return fileName !== null && fileName.toLowerCase() in this._cache\n  }\n\n  async getCachedModel(fileName) {\n    if (fileName !== null && this.hasCachedModel(fileName)) {\n      const key = fileName.toLowerCase()\n      const group = await this._cache[key]\n      return group.clone()\n    } else {\n      return null\n    }\n  }\n\n  // Loads and parses the model with the given file name. Returns a cached copy if available.\n  async loadModel(fileName) {\n    const parseCache = this.parseCache\n    const key = fileName.toLowerCase()\n    if (this.hasCachedModel(fileName)) {\n      // Return cached model if available.\n      return this.getCachedModel(fileName)\n    } else {\n      // Otherwise parse a new model.\n      // Ensure the file data is loaded and pre parsed.\n      await parseCache.ensureDataLoaded(fileName)\n\n      const info = parseCache.getData(fileName)\n      const promise = this.processIntoMesh(info)\n\n      // Now that the file has loaded it's possible that another part parse has been waiting in parallel\n      // so check the cache again to see if it's been added since the last async operation so we don't\n      // do unnecessary work.\n      if (this.hasCachedModel(fileName)) {\n        return this.getCachedModel(fileName)\n      }\n\n      // Cache object if it's a part so it can be reused later.\n      if (isPartType(info.type)) {\n        this._cache[key] = promise\n      }\n\n      // return a copy\n      const group = await promise\n      return group.clone()\n    }\n  }\n\n  // parses the given model text into a renderable object. Returns cached copy if available.\n  async parseModel(text) {\n    const parseCache = this.parseCache\n    const info = parseCache.parse(text)\n    if (isPartType(info.type) && this.hasCachedModel(info.fileName)) {\n      return this.getCachedModel(info.fileName)\n    }\n\n    return this.processIntoMesh(info)\n  }\n}\n\nfunction sortByMaterial(a, b) {\n  if (a.colorCode === b.colorCode) {\n    return 0\n  }\n\n  if (a.colorCode < b.colorCode) {\n    return -1\n  }\n\n  return 1\n}\n\nfunction createObject(elements, elementSize, isConditionalSegments = false, totalElements = null) {\n  // Creates a LineSegments (elementSize = 2) or a Mesh (elementSize = 3 )\n  // With per face / segment material, implemented with mesh groups and materials array\n\n  // Sort the faces or line segments by color code to make later the mesh groups\n  elements.sort(sortByMaterial)\n\n  if (totalElements === null) {\n    totalElements = elements.length\n  }\n\n  const positions = new Float32Array(elementSize * totalElements * 3)\n  const normals = elementSize === 3 ? new Float32Array(elementSize * totalElements * 3) : null\n  const materials = []\n\n  const quadArray = new Array(6)\n  const bufferGeometry = new BufferGeometry()\n  let prevMaterial = null\n  let index0 = 0\n  let numGroupVerts = 0\n  let offset = 0\n\n  for (let iElem = 0, nElem = elements.length; iElem < nElem; iElem++) {\n    const elem = elements[iElem]\n    let vertices = elem.vertices\n    if (vertices.length === 4) {\n      quadArray[0] = vertices[0]\n      quadArray[1] = vertices[1]\n      quadArray[2] = vertices[2]\n      quadArray[3] = vertices[0]\n      quadArray[4] = vertices[2]\n      quadArray[5] = vertices[3]\n      vertices = quadArray\n    }\n\n    for (let j = 0, l = vertices.length; j < l; j++) {\n      const v = vertices[j]\n      const index = offset + j * 3\n      positions[index + 0] = v.x\n      positions[index + 1] = v.y\n      positions[index + 2] = v.z\n    }\n\n    // create the normals array if this is a set of faces\n    if (elementSize === 3) {\n      if (!elem.faceNormal) {\n        const v0 = vertices[0]\n        const v1 = vertices[1]\n        const v2 = vertices[2]\n        _tempVec0.subVectors(v1, v0)\n        _tempVec1.subVectors(v2, v1)\n        elem.faceNormal = new Vector3().crossVectors(_tempVec0, _tempVec1).normalize()\n      }\n\n      let elemNormals = elem.normals\n      if (elemNormals.length === 4) {\n        quadArray[0] = elemNormals[0]\n        quadArray[1] = elemNormals[1]\n        quadArray[2] = elemNormals[2]\n        quadArray[3] = elemNormals[0]\n        quadArray[4] = elemNormals[2]\n        quadArray[5] = elemNormals[3]\n        elemNormals = quadArray\n      }\n\n      for (let j = 0, l = elemNormals.length; j < l; j++) {\n        // use face normal if a vertex normal is not provided\n        let n = elem.faceNormal\n        if (elemNormals[j]) {\n          n = elemNormals[j].norm\n        }\n\n        const index = offset + j * 3\n        normals[index + 0] = n.x\n        normals[index + 1] = n.y\n        normals[index + 2] = n.z\n      }\n    }\n\n    if (prevMaterial !== elem.colorCode) {\n      if (prevMaterial !== null) {\n        bufferGeometry.addGroup(index0, numGroupVerts, materials.length - 1)\n      }\n\n      const material = elem.material\n      if (material !== null) {\n        if (elementSize === 3) {\n          materials.push(material)\n        } else if (elementSize === 2) {\n          if (material !== null) {\n            if (isConditionalSegments) {\n              materials.push(material.userData.edgeMaterial.userData.conditionalEdgeMaterial)\n            } else {\n              materials.push(material.userData.edgeMaterial)\n            }\n          } else {\n            materials.push(null)\n          }\n        }\n      } else {\n        // If a material has not been made available yet then keep the color code string in the material array\n        // to save the spot for the material once a parent scopes materials are being applied to the object.\n        materials.push(elem.colorCode)\n      }\n\n      prevMaterial = elem.colorCode\n      index0 = offset / 3\n      numGroupVerts = vertices.length\n    } else {\n      numGroupVerts += vertices.length\n    }\n\n    offset += 3 * vertices.length\n  }\n\n  if (numGroupVerts > 0) {\n    bufferGeometry.addGroup(index0, Infinity, materials.length - 1)\n  }\n\n  bufferGeometry.setAttribute('position', new BufferAttribute(positions, 3))\n\n  if (normals !== null) {\n    bufferGeometry.setAttribute('normal', new BufferAttribute(normals, 3))\n  }\n\n  let object3d = null\n\n  if (elementSize === 2) {\n    if (isConditionalSegments) {\n      object3d = new ConditionalLineSegments(bufferGeometry, materials.length === 1 ? materials[0] : materials)\n    } else {\n      object3d = new LineSegments(bufferGeometry, materials.length === 1 ? materials[0] : materials)\n    }\n  } else if (elementSize === 3) {\n    object3d = new Mesh(bufferGeometry, materials.length === 1 ? materials[0] : materials)\n  }\n\n  if (isConditionalSegments) {\n    object3d.isConditionalLine = true\n\n    const controlArray0 = new Float32Array(elements.length * 3 * 2)\n    const controlArray1 = new Float32Array(elements.length * 3 * 2)\n    const directionArray = new Float32Array(elements.length * 3 * 2)\n    for (let i = 0, l = elements.length; i < l; i++) {\n      const os = elements[i]\n      const vertices = os.vertices\n      const controlPoints = os.controlPoints\n      const c0 = controlPoints[0]\n      const c1 = controlPoints[1]\n      const v0 = vertices[0]\n      const v1 = vertices[1]\n      const index = i * 3 * 2\n      controlArray0[index + 0] = c0.x\n      controlArray0[index + 1] = c0.y\n      controlArray0[index + 2] = c0.z\n      controlArray0[index + 3] = c0.x\n      controlArray0[index + 4] = c0.y\n      controlArray0[index + 5] = c0.z\n\n      controlArray1[index + 0] = c1.x\n      controlArray1[index + 1] = c1.y\n      controlArray1[index + 2] = c1.z\n      controlArray1[index + 3] = c1.x\n      controlArray1[index + 4] = c1.y\n      controlArray1[index + 5] = c1.z\n\n      directionArray[index + 0] = v1.x - v0.x\n      directionArray[index + 1] = v1.y - v0.y\n      directionArray[index + 2] = v1.z - v0.z\n      directionArray[index + 3] = v1.x - v0.x\n      directionArray[index + 4] = v1.y - v0.y\n      directionArray[index + 5] = v1.z - v0.z\n    }\n\n    bufferGeometry.setAttribute('control0', new BufferAttribute(controlArray0, 3, false))\n    bufferGeometry.setAttribute('control1', new BufferAttribute(controlArray1, 3, false))\n    bufferGeometry.setAttribute('direction', new BufferAttribute(directionArray, 3, false))\n  }\n\n  return object3d\n}\n\n//\n\nclass LDrawLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    // Array of THREE.Material\n    this.materials = []\n    this.materialLibrary = {}\n\n    // This also allows to handle the embedded text files (\"0 FILE\" lines)\n    this.partsCache = new LDrawPartsGeometryCache(this)\n\n    // This object is a map from file names to paths. It agilizes the paths search. If it is not set then files will be searched by trial and error.\n    this.fileMap = {}\n\n    // Initializes the materials library with default materials\n    this.setMaterials([])\n\n    // If this flag is set to true the vertex normals will be smoothed.\n    this.smoothNormals = true\n\n    // The path to load parts from the LDraw parts library from.\n    this.partsLibraryPath = ''\n  }\n\n  setPartsLibraryPath(path) {\n    this.partsLibraryPath = path\n    return this\n  }\n\n  async preloadMaterials(url) {\n    const fileLoader = new FileLoader(this.manager)\n    fileLoader.setPath(this.path)\n    fileLoader.setRequestHeader(this.requestHeader)\n    fileLoader.setWithCredentials(this.withCredentials)\n\n    const text = await fileLoader.loadAsync(url)\n    const colorLineRegex = /^0 !COLOUR/\n    const lines = text.split(/[\\n\\r]/g)\n    const materials = []\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i]\n      if (colorLineRegex.test(line)) {\n        const directive = line.replace(colorLineRegex, '')\n        const material = this.parseColorMetaDirective(new LineParser(directive))\n        materials.push(material)\n      }\n    }\n\n    this.setMaterials(materials)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const fileLoader = new FileLoader(this.manager)\n    fileLoader.setPath(this.path)\n    fileLoader.setRequestHeader(this.requestHeader)\n    fileLoader.setWithCredentials(this.withCredentials)\n    fileLoader.load(\n      url,\n      (text) => {\n        this.partsCache\n          .parseModel(text, this.materialLibrary)\n          .then((group) => {\n            this.applyMaterialsToMesh(group, MAIN_COLOUR_CODE, this.materialLibrary, true)\n            this.computeConstructionSteps(group)\n            onLoad(group)\n          })\n          .catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text, onLoad) {\n    this.partsCache.parseModel(text, this.materialLibrary).then((group) => {\n      this.computeConstructionSteps(group)\n      onLoad(group)\n    })\n  }\n\n  setMaterials(materials) {\n    this.materialLibrary = {}\n    this.materials = []\n    for (let i = 0, l = materials.length; i < l; i++) {\n      this.addMaterial(materials[i])\n    }\n\n    // Add default main triangle and line edge materials (used in pieces that can be colored with a main color)\n    this.addMaterial(this.parseColorMetaDirective(new LineParser('Main_Colour CODE 16 VALUE #FF8080 EDGE #333333')))\n    this.addMaterial(this.parseColorMetaDirective(new LineParser('Edge_Colour CODE 24 VALUE #A0A0A0 EDGE #333333')))\n\n    return this\n  }\n\n  setFileMap(fileMap) {\n    this.fileMap = fileMap\n\n    return this\n  }\n\n  addMaterial(material) {\n    // Adds a material to the material library which is on top of the parse scopes stack. And also to the materials array\n\n    const matLib = this.materialLibrary\n    if (!matLib[material.userData.code]) {\n      this.materials.push(material)\n      matLib[material.userData.code] = material\n    }\n\n    return this\n  }\n\n  getMaterial(colorCode) {\n    if (colorCode.startsWith('0x2')) {\n      // Special 'direct' material value (RGB color)\n      const color = colorCode.substring(3)\n\n      return this.parseColorMetaDirective(\n        new LineParser('Direct_Color_' + color + ' CODE -1 VALUE #' + color + ' EDGE #' + color + ''),\n      )\n    }\n\n    return this.materialLibrary[colorCode] || null\n  }\n\n  // Applies the appropriate materials to a prebuilt hierarchy of geometry. Assumes that color codes are present\n  // in the material array if they need to be filled in.\n  applyMaterialsToMesh(group, parentColorCode, materialHierarchy, finalMaterialPass = false) {\n    // find any missing materials as indicated by a color code string and replace it with a material from the current material lib\n    const loader = this\n    const parentIsPassthrough = parentColorCode === MAIN_COLOUR_CODE\n    group.traverse((c) => {\n      if (c.isMesh || c.isLineSegments) {\n        if (Array.isArray(c.material)) {\n          for (let i = 0, l = c.material.length; i < l; i++) {\n            if (!c.material[i].isMaterial) {\n              c.material[i] = getMaterial(c, c.material[i])\n            }\n          }\n        } else if (!c.material.isMaterial) {\n          c.material = getMaterial(c, c.material)\n        }\n      }\n    })\n\n    // Returns the appropriate material for the object (line or face) given color code. If the code is \"pass through\"\n    // (24 for lines, 16 for edges) then the pass through color code is used. If that is also pass through then it's\n    // simply returned for the subsequent material application.\n    function getMaterial(c, colorCode) {\n      // if our parent is a passthrough color code and we don't have the current material color available then\n      // return early.\n      if (parentIsPassthrough && !(colorCode in materialHierarchy) && !finalMaterialPass) {\n        return colorCode\n      }\n\n      const forEdge = c.isLineSegments || c.isConditionalLine\n      const isPassthrough =\n        (!forEdge && colorCode === MAIN_COLOUR_CODE) || (forEdge && colorCode === MAIN_EDGE_COLOUR_CODE)\n      if (isPassthrough) {\n        colorCode = parentColorCode\n      }\n\n      let material = null\n      if (colorCode in materialHierarchy) {\n        material = materialHierarchy[colorCode]\n      } else if (finalMaterialPass) {\n        // see if we can get the final material from from the \"getMaterial\" function which will attempt to\n        // parse the \"direct\" colors\n        material = loader.getMaterial(colorCode)\n        if (material === null) {\n          // otherwise throw an error if this is final opportunity to set the material\n          throw new Error(`LDrawLoader: Material properties for code ${colorCode} not available.`)\n        }\n      } else {\n        return colorCode\n      }\n\n      if (c.isLineSegments) {\n        material = material.userData.edgeMaterial\n\n        if (c.isConditionalLine) {\n          material = material.userData.conditionalEdgeMaterial\n        }\n      }\n\n      return material\n    }\n  }\n\n  getMainMaterial() {\n    return this.getMaterial(MAIN_COLOUR_CODE)\n  }\n\n  getMainEdgeMaterial() {\n    return this.getMaterial(MAIN_EDGE_COLOUR_CODE)\n  }\n\n  parseColorMetaDirective(lineParser) {\n    // Parses a color definition and returns a THREE.Material\n\n    let code = null\n\n    // Triangle and line colors\n    let color = 0xff00ff\n    let edgeColor = 0xff00ff\n\n    // Transparency\n    let alpha = 1\n    let isTransparent = false\n    // Self-illumination:\n    let luminance = 0\n\n    let finishType = FINISH_TYPE_DEFAULT\n\n    let edgeMaterial = null\n\n    const name = lineParser.getToken()\n    if (!name) {\n      throw new Error(\n        'LDrawLoader: Material name was expected after \"!COLOUR tag' + lineParser.getLineNumberString() + '.',\n      )\n    }\n\n    // Parse tag tokens and their parameters\n    let token = null\n    while (true) {\n      token = lineParser.getToken()\n\n      if (!token) {\n        break\n      }\n\n      switch (token.toUpperCase()) {\n        case 'CODE':\n          code = lineParser.getToken()\n          break\n\n        case 'VALUE':\n          color = lineParser.getToken()\n          if (color.startsWith('0x')) {\n            color = '#' + color.substring(2)\n          } else if (!color.startsWith('#')) {\n            throw new Error(\n              'LDrawLoader: Invalid color while parsing material' + lineParser.getLineNumberString() + '.',\n            )\n          }\n\n          break\n\n        case 'EDGE':\n          edgeColor = lineParser.getToken()\n          if (edgeColor.startsWith('0x')) {\n            edgeColor = '#' + edgeColor.substring(2)\n          } else if (!edgeColor.startsWith('#')) {\n            // Try to see if edge color is a color code\n            edgeMaterial = this.getMaterial(edgeColor)\n            if (!edgeMaterial) {\n              throw new Error(\n                'LDrawLoader: Invalid edge color while parsing material' + lineParser.getLineNumberString() + '.',\n              )\n            }\n\n            // Get the edge material for this triangle material\n            edgeMaterial = edgeMaterial.userData.edgeMaterial\n          }\n\n          break\n\n        case 'ALPHA':\n          alpha = parseInt(lineParser.getToken())\n\n          if (isNaN(alpha)) {\n            throw new Error(\n              'LDrawLoader: Invalid alpha value in material definition' + lineParser.getLineNumberString() + '.',\n            )\n          }\n\n          alpha = Math.max(0, Math.min(1, alpha / 255))\n\n          if (alpha < 1) {\n            isTransparent = true\n          }\n\n          break\n\n        case 'LUMINANCE':\n          luminance = parseInt(lineParser.getToken())\n\n          if (isNaN(luminance)) {\n            throw new Error(\n              'LDrawLoader: Invalid luminance value in material definition' + LineParser.getLineNumberString() + '.',\n            )\n          }\n\n          luminance = Math.max(0, Math.min(1, luminance / 255))\n\n          break\n\n        case 'CHROME':\n          finishType = FINISH_TYPE_CHROME\n          break\n\n        case 'PEARLESCENT':\n          finishType = FINISH_TYPE_PEARLESCENT\n          break\n\n        case 'RUBBER':\n          finishType = FINISH_TYPE_RUBBER\n          break\n\n        case 'MATTE_METALLIC':\n          finishType = FINISH_TYPE_MATTE_METALLIC\n          break\n\n        case 'METAL':\n          finishType = FINISH_TYPE_METAL\n          break\n\n        case 'MATERIAL':\n          // Not implemented\n          lineParser.setToEnd()\n          break\n\n        default:\n          throw new Error(\n            'LDrawLoader: Unknown token \"' +\n              token +\n              '\" while parsing material' +\n              lineParser.getLineNumberString() +\n              '.',\n          )\n      }\n    }\n\n    let material = null\n\n    switch (finishType) {\n      case FINISH_TYPE_DEFAULT:\n        material = new MeshStandardMaterial({ color: color, roughness: 0.3, metalness: 0 })\n        break\n\n      case FINISH_TYPE_PEARLESCENT:\n        // Try to imitate pearlescency by making the surface glossy\n        material = new MeshStandardMaterial({ color: color, roughness: 0.3, metalness: 0.25 })\n        break\n\n      case FINISH_TYPE_CHROME:\n        // Mirror finish surface\n        material = new MeshStandardMaterial({ color: color, roughness: 0, metalness: 1 })\n        break\n\n      case FINISH_TYPE_RUBBER:\n        // Rubber finish\n        material = new MeshStandardMaterial({ color: color, roughness: 0.9, metalness: 0 })\n        break\n\n      case FINISH_TYPE_MATTE_METALLIC:\n        // Brushed metal finish\n        material = new MeshStandardMaterial({ color: color, roughness: 0.8, metalness: 0.4 })\n        break\n\n      case FINISH_TYPE_METAL:\n        // Average metal finish\n        material = new MeshStandardMaterial({ color: color, roughness: 0.2, metalness: 0.85 })\n        break\n\n      default:\n        // Should not happen\n        break\n    }\n\n    material.transparent = isTransparent\n    material.premultipliedAlpha = true\n    material.opacity = alpha\n    material.depthWrite = !isTransparent\n\n    material.polygonOffset = true\n    material.polygonOffsetFactor = 1\n\n    if (luminance !== 0) {\n      material.emissive.set(material.color).multiplyScalar(luminance)\n    }\n\n    if (!edgeMaterial) {\n      // This is the material used for edges\n      edgeMaterial = new LineBasicMaterial({\n        color: edgeColor,\n        transparent: isTransparent,\n        opacity: alpha,\n        depthWrite: !isTransparent,\n      })\n      edgeMaterial.userData.code = code\n      edgeMaterial.name = name + ' - Edge'\n\n      // This is the material used for conditional edges\n      edgeMaterial.userData.conditionalEdgeMaterial = new LDrawConditionalLineMaterial({\n        fog: true,\n        transparent: isTransparent,\n        depthWrite: !isTransparent,\n        color: edgeColor,\n        opacity: alpha,\n      })\n    }\n\n    material.userData.code = code\n    material.name = name\n\n    material.userData.edgeMaterial = edgeMaterial\n\n    this.addMaterial(material)\n\n    return material\n  }\n\n  computeConstructionSteps(model) {\n    // Sets userdata.constructionStep number in Group objects and userData.numConstructionSteps number in the root Group object.\n\n    let stepNumber = 0\n\n    model.traverse((c) => {\n      if (c.isGroup) {\n        if (c.userData.startingConstructionStep) {\n          stepNumber++\n        }\n\n        c.userData.constructionStep = stepNumber\n      }\n    })\n\n    model.userData.numConstructionSteps = stepNumber + 1\n  }\n}\n\nexport { LDrawLoader }\n"], "mappings": ";;AAsBA,MAAMA,mBAAA,GAAsB;AAC5B,MAAMC,kBAAA,GAAqB;AAC3B,MAAMC,uBAAA,GAA0B;AAChC,MAAMC,kBAAA,GAAqB;AAC3B,MAAMC,0BAAA,GAA6B;AACnC,MAAMC,iBAAA,GAAoB;AAI1B,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,uBAAA,GAA0B;AAChC,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,wBAAA,GAA2B;AACjC,MAAMC,0BAAA,GAA6B;AACnC,MAAMC,0BAAA,GAA6B;AACnC,MAAMC,uBAAA,GAA0B;AAEhC,MAAMC,gBAAA,GAAmB;AACzB,MAAMC,qBAAA,GAAwB;AAE9B,MAAMC,SAAA,GAA4B,mBAAIC,OAAA,CAAS;AAC/C,MAAMC,SAAA,GAA4B,mBAAID,OAAA,CAAS;AAE/C,MAAME,4BAAA,SAAqCC,cAAA,CAAe;EACxDC,YAAYC,UAAA,EAAY;IACtB,MAAM;MACJC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,CAC5BC,WAAA,CAAYC,GAAA,EACZ;QACEC,OAAA,EAAS;UACPC,KAAA,EAAO,IAAIC,KAAA,CAAO;QACnB;QACDC,OAAA,EAAS;UACPF,KAAA,EAAO;QACR;MACF,EACF;MAEDG,YAAA;MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MAiDzBC,cAAA;MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAsBXC,OAAA,IAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;IAK/D,CAAK;IAEDC,MAAA,CAAOC,gBAAA,CAAiB,MAAM;MAC5BL,OAAA,EAAS;QACPM,GAAA,EAAK,SAAAA,CAAA,EAAY;UACf,OAAO,KAAKd,QAAA,CAASQ,OAAA,CAAQF,KAAA;QAC9B;QAEDS,GAAA,EAAK,SAAAA,CAAUT,KAAA,EAAO;UACpB,KAAKN,QAAA,CAASQ,OAAA,CAAQF,KAAA,GAAQA,KAAA;QAC/B;MACF;MAEDU,KAAA,EAAO;QACLF,GAAA,EAAK,SAAAA,CAAA,EAAY;UACf,OAAO,KAAKd,QAAA,CAASK,OAAA,CAAQC,KAAA;QAC9B;MACF;IACP,CAAK;IAED,KAAKW,SAAA,CAAUlB,UAAU;IACzB,KAAKmB,8BAAA,GAAiC;EACvC;AACH;AAEA,MAAMC,uBAAA,SAAgCC,YAAA,CAAa;EACjDtB,YAAYuB,QAAA,EAAUC,QAAA,EAAU;IAC9B,MAAMD,QAAA,EAAUC,QAAQ;IACxB,KAAKC,iBAAA,GAAoB;EAC1B;AACH;AAEA,SAASC,oBAAoBC,KAAA,EAAO;EAClC,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIF,KAAA,CAAMG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IAC5C,MAAMG,IAAA,GAAOJ,KAAA,CAAMC,CAAC;IACpB,MAAMI,QAAA,GAAWD,IAAA,CAAKC,QAAA;IACtB,MAAMC,EAAA,GAAKD,QAAA,CAAS,CAAC;IACrB,MAAME,EAAA,GAAKF,QAAA,CAAS,CAAC;IACrB,MAAMG,EAAA,GAAKH,QAAA,CAAS,CAAC;IAErBrC,SAAA,CAAUyC,UAAA,CAAWF,EAAA,EAAID,EAAE;IAC3BpC,SAAA,CAAUuC,UAAA,CAAWD,EAAA,EAAID,EAAE;IAC3BH,IAAA,CAAKM,UAAA,GAAa,IAAIzC,OAAA,CAAS,EAAC0C,YAAA,CAAa3C,SAAA,EAAWE,SAAS,EAAE0C,SAAA,CAAW;EAC/E;AACH;AAEA,MAAMC,IAAA,GAAuB,mBAAIC,GAAA,CAAK;AACtC,SAASC,cAAcf,KAAA,EAAOgB,YAAA,EAAcC,gBAAA,GAAmB,OAAO;EAUpE,MAAMC,cAAA,IAAkB,IAAI,SAAS;EACrC,SAASC,WAAWC,CAAA,EAAG;IACrB,MAAMC,CAAA,GAAI,CAAC,EAAED,CAAA,CAAEC,CAAA,GAAIH,cAAA;IACnB,MAAMI,CAAA,GAAI,CAAC,EAAEF,CAAA,CAAEE,CAAA,GAAIJ,cAAA;IACnB,MAAMK,CAAA,GAAI,CAAC,EAAEH,CAAA,CAAEG,CAAA,GAAIL,cAAA;IAEnB,OAAO,GAAGG,CAAA,IAAKC,CAAA,IAAKC,CAAA;EACrB;EAED,SAASC,SAASlB,EAAA,EAAIC,EAAA,EAAI;IACxB,OAAO,GAAGY,UAAA,CAAWb,EAAE,KAAKa,UAAA,CAAWZ,EAAE;EAC1C;EAID,SAASkB,gBAAgBnB,EAAA,EAAIC,EAAA,EAAImB,SAAA,EAAW;IAC1CA,SAAA,CAAUC,SAAA,CAAUlB,UAAA,CAAWF,EAAA,EAAID,EAAE,EAAEM,SAAA,CAAW;IAElD,MAAMgB,MAAA,GAAStB,EAAA,CAAGuB,GAAA,CAAIH,SAAA,CAAUC,SAAS;IACzCD,SAAA,CAAUI,MAAA,CAAOC,IAAA,CAAKzB,EAAE,EAAE0B,eAAA,CAAgBN,SAAA,CAAUC,SAAA,EAAW,CAACC,MAAM;IAEtE,OAAOF,SAAA;EACR;EAED,SAASO,QAAQC,GAAA,EAAK;IACpB,OAAOV,QAAA,CAASU,GAAA,CAAIJ,MAAA,EAAQI,GAAA,CAAIP,SAAS;EAC1C;EAED,MAAMQ,SAAA,GAAY,mBAAIC,GAAA,CAAK;EAC3B,MAAMC,YAAA,GAAe,mBAAIC,GAAA,CAAK;EAC9B,MAAMC,YAAA,GAAe,CAAE;EACvB,MAAMC,OAAA,GAAU,EAAE;EAGlB,SAASvC,CAAA,GAAI,GAAGC,CAAA,GAAIc,YAAA,CAAab,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IACnD,MAAMwC,EAAA,GAAKzB,YAAA,CAAaf,CAAC;IACzB,MAAMI,QAAA,GAAWoC,EAAA,CAAGpC,QAAA;IACpB,MAAMC,EAAA,GAAKD,QAAA,CAAS,CAAC;IACrB,MAAME,EAAA,GAAKF,QAAA,CAAS,CAAC;IACrB8B,SAAA,CAAUO,GAAA,CAAIlB,QAAA,CAASlB,EAAA,EAAIC,EAAE,CAAC;IAC9B4B,SAAA,CAAUO,GAAA,CAAIlB,QAAA,CAASjB,EAAA,EAAID,EAAE,CAAC;IAI9B,IAAIW,gBAAA,EAAkB;MAEpB,MAAMiB,GAAA,GAAMT,eAAA,CAAgBnB,EAAA,EAAIC,EAAA,EAAI,IAAIO,GAAA,CAAG,CAAE;MAC7C,MAAM6B,GAAA,GAAMV,OAAA,CAAQC,GAAG;MACvB,IAAI,CAACG,YAAA,CAAaO,GAAA,CAAID,GAAG,GAAG;QAC1BlB,eAAA,CAAgBlB,EAAA,EAAID,EAAA,EAAI4B,GAAG;QAC3B,MAAMW,GAAA,GAAMZ,OAAA,CAAQC,GAAG;QAEvB,MAAMY,KAAA,GAAO;UACXZ,GAAA;UACAa,SAAA,EAAW;QACZ;QAEDV,YAAA,CAAa/C,GAAA,CAAIqD,GAAA,EAAKG,KAAI;QAC1BT,YAAA,CAAa/C,GAAA,CAAIuD,GAAA,EAAKC,KAAI;MAC3B;MAID,MAAME,IAAA,GAAOX,YAAA,CAAahD,GAAA,CAAIsD,GAAG;MACjC,IAAIM,EAAA,GAAKD,IAAA,CAAKd,GAAA,CAAIP,SAAA,CAAUE,GAAA,CAAIvB,EAAE;MAClC,IAAI4C,EAAA,GAAKF,IAAA,CAAKd,GAAA,CAAIP,SAAA,CAAUE,GAAA,CAAItB,EAAE;MAClC,IAAI0C,EAAA,GAAKC,EAAA,EAAI;QACV,CAACD,EAAA,EAAIC,EAAE,IAAI,CAACA,EAAA,EAAID,EAAE;MACpB;MAEDD,IAAA,CAAKD,SAAA,CAAUI,IAAA,CAAKF,EAAA,EAAIC,EAAE;IAC3B;EACF;EAGD,SAASjD,CAAA,GAAI,GAAGC,CAAA,GAAIF,KAAA,CAAMG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IAC5C,MAAMmD,GAAA,GAAMpD,KAAA,CAAMC,CAAC;IACnB,MAAMI,QAAA,GAAW+C,GAAA,CAAI/C,QAAA;IACrB,MAAMgD,SAAA,GAAYhD,QAAA,CAASF,MAAA;IAC3B,SAASmD,EAAA,GAAK,GAAGA,EAAA,GAAKD,SAAA,EAAWC,EAAA,IAAM;MACrC,MAAMC,KAAA,GAAQD,EAAA;MACd,MAAME,IAAA,IAAQF,EAAA,GAAK,KAAKD,SAAA;MACxB,MAAM/C,EAAA,GAAKD,QAAA,CAASkD,KAAK;MACzB,MAAMhD,EAAA,GAAKF,QAAA,CAASmD,IAAI;MACxB,MAAMC,IAAA,GAAOjC,QAAA,CAASlB,EAAA,EAAIC,EAAE;MAG5B,IAAI4B,SAAA,CAAUS,GAAA,CAAIa,IAAI,GAAG;QACvB;MACD;MAGD,IAAIxC,gBAAA,EAAkB;QACpBQ,eAAA,CAAgBnB,EAAA,EAAIC,EAAA,EAAIM,IAAI;QAE5B,MAAM6C,OAAA,GAAUzB,OAAA,CAAQpB,IAAI;QAC5B,IAAIwB,YAAA,CAAaO,GAAA,CAAIc,OAAO,GAAG;UAC7B,MAAMZ,KAAA,GAAOT,YAAA,CAAahD,GAAA,CAAIqE,OAAO;UACrC,MAAM;YAAExB,GAAA;YAAKa;UAAS,IAAKD,KAAA;UAC3B,IAAIG,EAAA,GAAKf,GAAA,CAAIP,SAAA,CAAUE,GAAA,CAAIvB,EAAE;UAC7B,IAAI4C,EAAA,GAAKhB,GAAA,CAAIP,SAAA,CAAUE,GAAA,CAAItB,EAAE;UAE7B,IAAI0C,EAAA,GAAKC,EAAA,EAAI;YACV,CAACD,EAAA,EAAIC,EAAE,IAAI,CAACA,EAAA,EAAID,EAAE;UACpB;UAGD,IAAIU,KAAA,GAAQ;UACZ,SAASC,EAAA,GAAI,GAAGC,EAAA,GAAId,SAAA,CAAU5C,MAAA,EAAQyD,EAAA,GAAIC,EAAA,EAAGD,EAAA,IAAK,GAAG;YACnD,IAAIX,EAAA,IAAMF,SAAA,CAAUa,EAAC,KAAKV,EAAA,IAAMH,SAAA,CAAUa,EAAA,GAAI,CAAC,GAAG;cAChDD,KAAA,GAAQ;cACR;YACD;UACF;UAED,IAAIA,KAAA,EAAO;YACT;UACD;QACF;MACF;MAED,MAAMX,IAAA,GAAO;QACXO,KAAA;QACAH;MACD;MACDb,YAAA,CAAakB,IAAI,IAAIT,IAAA;IACtB;EACF;EAGD,OAAO,MAAM;IAEX,IAAIc,QAAA,GAAW;IACf,WAAWC,GAAA,IAAOxB,YAAA,EAAc;MAC9BuB,QAAA,GAAWvB,YAAA,CAAawB,GAAG;MAC3B;IACD;IAED,IAAID,QAAA,KAAa,MAAM;MACrB;IACD;IAGD,MAAME,KAAA,GAAQ,CAACF,QAAQ;IACvB,OAAOE,KAAA,CAAM7D,MAAA,GAAS,GAAG;MAEvB,MAAMiD,GAAA,GAAMY,KAAA,CAAMC,GAAA,CAAG,EAAGb,GAAA;MACxB,MAAM/C,QAAA,GAAW+C,GAAA,CAAI/C,QAAA;MACrB,MAAM6D,WAAA,GAAcd,GAAA,CAAIZ,OAAA;MACxB,MAAM9B,UAAA,GAAa0C,GAAA,CAAI1C,UAAA;MAGvB,MAAM2C,SAAA,GAAYhD,QAAA,CAASF,MAAA;MAC3B,SAASmD,EAAA,GAAK,GAAGA,EAAA,GAAKD,SAAA,EAAWC,EAAA,IAAM;QACrC,MAAMC,KAAA,GAAQD,EAAA;QACd,MAAME,IAAA,IAAQF,EAAA,GAAK,KAAKD,SAAA;QACxB,MAAM/C,EAAA,GAAKD,QAAA,CAASkD,KAAK;QACzB,MAAMhD,EAAA,GAAKF,QAAA,CAASmD,IAAI;QAGxB,MAAMC,IAAA,GAAOjC,QAAA,CAASlB,EAAA,EAAIC,EAAE;QAC5B,OAAOgC,YAAA,CAAakB,IAAI;QAExB,MAAMU,WAAA,GAAc3C,QAAA,CAASjB,EAAA,EAAID,EAAE;QACnC,MAAM8D,SAAA,GAAY7B,YAAA,CAAa4B,WAAW;QAC1C,IAAIC,SAAA,EAAW;UACb,MAAMC,QAAA,GAAWD,SAAA,CAAUhB,GAAA;UAC3B,MAAMkB,UAAA,GAAaF,SAAA,CAAUb,KAAA;UAC7B,MAAMgB,YAAA,GAAeF,QAAA,CAAS7B,OAAA;UAC9B,MAAMgC,cAAA,GAAiBD,YAAA,CAAapE,MAAA;UACpC,MAAMsE,eAAA,GAAkBJ,QAAA,CAAS3D,UAAA;UAKjC,IAAIgE,IAAA,CAAKC,GAAA,CAAIN,QAAA,CAAS3D,UAAA,CAAWmB,GAAA,CAAIuB,GAAA,CAAI1C,UAAU,CAAC,IAAI,MAAM;YAC5D;UACD;UAKD,IAAIyD,WAAA,IAAe5B,YAAA,EAAc;YAC/ByB,KAAA,CAAMb,IAAA,CAAKiB,SAAS;YACpB,OAAO7B,YAAA,CAAa4B,WAAW;UAChC;UAGD,MAAMS,SAAA,IAAaN,UAAA,GAAa,KAAKE,cAAA;UACrC,IAAIN,WAAA,CAAYX,KAAK,KAAKgB,YAAA,CAAaK,SAAS,KAAKV,WAAA,CAAYX,KAAK,MAAMgB,YAAA,CAAaK,SAAS,GAAG;YACnGL,YAAA,CAAaK,SAAS,EAAEC,IAAA,CAAKnC,GAAA,CAAIwB,WAAA,CAAYX,KAAK,EAAEsB,IAAI;YACxDX,WAAA,CAAYX,KAAK,EAAEsB,IAAA,GAAON,YAAA,CAAaK,SAAS,EAAEC,IAAA;UACnD;UAED,IAAIC,aAAA,GAAgBZ,WAAA,CAAYX,KAAK,KAAKgB,YAAA,CAAaK,SAAS;UAChE,IAAIE,aAAA,KAAkB,MAAM;YAI1BA,aAAA,GAAgB;cAAED,IAAA,EAAM,IAAI5G,OAAA;YAAW;YACvCuE,OAAA,CAAQW,IAAA,CAAK2B,aAAA,CAAcD,IAAI;UAChC;UAED,IAAIX,WAAA,CAAYX,KAAK,MAAM,MAAM;YAC/BW,WAAA,CAAYX,KAAK,IAAIuB,aAAA;YACrBA,aAAA,CAAcD,IAAA,CAAKnC,GAAA,CAAIhC,UAAU;UAClC;UAED,IAAI6D,YAAA,CAAaK,SAAS,MAAM,MAAM;YACpCL,YAAA,CAAaK,SAAS,IAAIE,aAAA;YAC1BA,aAAA,CAAcD,IAAA,CAAKnC,GAAA,CAAI+B,eAAe;UACvC;UAGD,IAAIP,WAAA,CAAYV,IAAI,KAAKe,YAAA,CAAaD,UAAU,KAAKJ,WAAA,CAAYV,IAAI,MAAMe,YAAA,CAAaD,UAAU,GAAG;YACnGC,YAAA,CAAaD,UAAU,EAAEO,IAAA,CAAKnC,GAAA,CAAIwB,WAAA,CAAYV,IAAI,EAAEqB,IAAI;YACxDX,WAAA,CAAYV,IAAI,EAAEqB,IAAA,GAAON,YAAA,CAAaD,UAAU,EAAEO,IAAA;UACnD;UAED,IAAIE,aAAA,GAAgBb,WAAA,CAAYV,IAAI,KAAKe,YAAA,CAAaD,UAAU;UAChE,IAAIS,aAAA,KAAkB,MAAM;YAC1BA,aAAA,GAAgB;cAAEF,IAAA,EAAM,IAAI5G,OAAA;YAAW;YACvCuE,OAAA,CAAQW,IAAA,CAAK4B,aAAA,CAAcF,IAAI;UAChC;UAED,IAAIX,WAAA,CAAYV,IAAI,MAAM,MAAM;YAC9BU,WAAA,CAAYV,IAAI,IAAIuB,aAAA;YACpBA,aAAA,CAAcF,IAAA,CAAKnC,GAAA,CAAIhC,UAAU;UAClC;UAED,IAAI6D,YAAA,CAAaD,UAAU,MAAM,MAAM;YACrCC,YAAA,CAAaD,UAAU,IAAIS,aAAA;YAC3BA,aAAA,CAAcF,IAAA,CAAKnC,GAAA,CAAI+B,eAAe;UACvC;QACF;MACF;IACF;EACF;EAGD,SAASxE,CAAA,GAAI,GAAGC,CAAA,GAAIsC,OAAA,CAAQrC,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;IAC9CuC,OAAA,CAAQvC,CAAC,EAAEW,SAAA,CAAW;EACvB;AACH;AAEA,SAASoE,WAAWC,IAAA,EAAM;EACxB,OAAOA,IAAA,KAAS,UAAUA,IAAA,KAAS;AACrC;AAEA,SAASC,gBAAgBD,IAAA,EAAM;EAC7B,OAAO,aAAaE,IAAA,CAAKF,IAAI,KAAKA,IAAA,KAAS;AAC7C;AAEA,MAAMG,UAAA,CAAW;EACf/G,YAAYgH,IAAA,EAAMC,UAAA,EAAY;IAC5B,KAAKD,IAAA,GAAOA,IAAA;IACZ,KAAKE,UAAA,GAAaF,IAAA,CAAKlF,MAAA;IACvB,KAAKqF,gBAAA,GAAmB;IACxB,KAAKC,WAAA,GAAc;IACnB,KAAKH,UAAA,GAAaA,UAAA;EACnB;EAEDI,aAAA,EAAe;IACb,OAAO,KAAKF,gBAAA,GAAmB,KAAKD,UAAA,EAAY;MAC9C,KAAKE,WAAA,GAAc,KAAKJ,IAAA,CAAKM,MAAA,CAAO,KAAKH,gBAAgB;MAEzD,IAAI,KAAKC,WAAA,KAAgB,OAAO,KAAKA,WAAA,KAAgB,KAAM;QACzD;MACD;MAED,KAAKD,gBAAA;IACN;EACF;EAEDI,SAAA,EAAW;IACT,MAAMC,IAAA,GAAO,KAAKL,gBAAA;IAGlB,OAAO,KAAKA,gBAAA,GAAmB,KAAKD,UAAA,EAAY;MAC9C,KAAKE,WAAA,GAAc,KAAKJ,IAAA,CAAKM,MAAA,CAAO,KAAKH,gBAAgB;MAEzD,IAAI,KAAKC,WAAA,KAAgB,OAAO,KAAKA,WAAA,KAAgB,KAAM;QACzD;MACD;MAED,KAAKD,gBAAA;IACN;IAED,MAAMM,IAAA,GAAO,KAAKN,gBAAA;IAElB,KAAKE,YAAA,CAAc;IAEnB,OAAO,KAAKL,IAAA,CAAKU,SAAA,CAAUF,IAAA,EAAMC,IAAI;EACtC;EAEDE,UAAA,EAAY;IACV,OAAO,IAAI/H,OAAA,CAAQgI,UAAA,CAAW,KAAKL,QAAA,CAAU,IAAGK,UAAA,CAAW,KAAKL,QAAA,CAAU,IAAGK,UAAA,CAAW,KAAKL,QAAA,CAAU,EAAC;EACzG;EAEDM,mBAAA,EAAqB;IACnB,OAAO,KAAKb,IAAA,CAAKU,SAAA,CAAU,KAAKP,gBAAA,EAAkB,KAAKD,UAAU;EAClE;EAEDY,WAAA,EAAa;IACX,OAAO,KAAKX,gBAAA,IAAoB,KAAKD,UAAA;EACtC;EAEDa,SAAA,EAAW;IACT,KAAKZ,gBAAA,GAAmB,KAAKD,UAAA;EAC9B;EAEDc,oBAAA,EAAsB;IACpB,OAAO,KAAKf,UAAA,IAAc,IAAI,cAAc,KAAKA,UAAA,GAAa;EAC/D;AACH;AAGA,MAAMgB,gBAAA,CAAiB;EACrBjI,YAAYkI,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKC,MAAA,GAAS,CAAE;EACjB;EAEDC,YAAYC,QAAA,EAAU;IACpB,MAAMC,MAAA,GAAS,CAAE;IAIjBA,MAAA,CAAO3G,KAAA,GAAQ0G,QAAA,CAAS1G,KAAA,CAAM4G,GAAA,CAAKxG,IAAA,IAAS;MAC1C,OAAO;QACLyG,SAAA,EAAWzG,IAAA,CAAKyG,SAAA;QAChBhH,QAAA,EAAUO,IAAA,CAAKP,QAAA;QACfQ,QAAA,EAAUD,IAAA,CAAKC,QAAA,CAASuG,GAAA,CAAKxF,CAAA,IAAMA,CAAA,CAAE0F,KAAA,EAAO;QAC5CtE,OAAA,EAASpC,IAAA,CAAKoC,OAAA,CAAQoE,GAAA,CAAI,MAAM,IAAI;QACpClG,UAAA,EAAY;MACb;IACP,CAAK;IAEDiG,MAAA,CAAOI,mBAAA,GAAsBL,QAAA,CAASK,mBAAA,CAAoBH,GAAA,CAAKxG,IAAA,IAAS;MACtE,OAAO;QACLyG,SAAA,EAAWzG,IAAA,CAAKyG,SAAA;QAChBhH,QAAA,EAAUO,IAAA,CAAKP,QAAA;QACfQ,QAAA,EAAUD,IAAA,CAAKC,QAAA,CAASuG,GAAA,CAAKxF,CAAA,IAAMA,CAAA,CAAE0F,KAAA,EAAO;QAC5CE,aAAA,EAAe5G,IAAA,CAAK4G,aAAA,CAAcJ,GAAA,CAAKxF,CAAA,IAAMA,CAAA,CAAE0F,KAAA,EAAO;MACvD;IACP,CAAK;IAEDH,MAAA,CAAO3F,YAAA,GAAe0F,QAAA,CAAS1F,YAAA,CAAa4F,GAAA,CAAKxG,IAAA,IAAS;MACxD,OAAO;QACLyG,SAAA,EAAWzG,IAAA,CAAKyG,SAAA;QAChBhH,QAAA,EAAUO,IAAA,CAAKP,QAAA;QACfQ,QAAA,EAAUD,IAAA,CAAKC,QAAA,CAASuG,GAAA,CAAKxF,CAAA,IAAMA,CAAA,CAAE0F,KAAA,EAAO;MAC7C;IACP,CAAK;IAGDH,MAAA,CAAO1B,IAAA,GAAOyB,QAAA,CAASzB,IAAA;IACvB0B,MAAA,CAAOM,QAAA,GAAWP,QAAA,CAASO,QAAA;IAC3BN,MAAA,CAAOO,QAAA,GAAWR,QAAA,CAASQ,QAAA;IAC3BP,MAAA,CAAOQ,UAAA,GAAaT,QAAA,CAASS,UAAA;IAC7BR,MAAA,CAAOS,UAAA,GAAaV,QAAA,CAASU,UAAA;IAC7BT,MAAA,CAAOU,wBAAA,GAA2BX,QAAA,CAASW,wBAAA;IAC3CV,MAAA,CAAOW,SAAA,GAAYZ,QAAA,CAASY,SAAA;IAC5BX,MAAA,CAAOY,KAAA,GAAQ;IACf,OAAOZ,MAAA;EACR;EAED,MAAMa,UAAUC,QAAA,EAAU;IACxB,IAAIC,cAAA,GAAiB;IACrB,IAAIC,aAAA,GAAgBpK,mBAAA;IACpB,OAAOoK,aAAA,KAAkB9J,uBAAA,EAAyB;MAChD,IAAI+J,YAAA,GAAeH,QAAA;MACnB,QAAQE,aAAA;QACN,KAAKpK,mBAAA;UACHoK,aAAA,GAAgBA,aAAA,GAAgB;UAChC;QAEF,KAAKnK,uBAAA;UACHoK,YAAA,GAAe,WAAWA,YAAA;UAC1BD,aAAA,GAAgBA,aAAA,GAAgB;UAChC;QAEF,KAAKlK,mBAAA;UACHmK,YAAA,GAAe,OAAOA,YAAA;UACtBD,aAAA,GAAgBA,aAAA,GAAgB;UAChC;QAEF,KAAKjK,wBAAA;UACHkK,YAAA,GAAe,YAAYA,YAAA;UAC3BD,aAAA,GAAgBA,aAAA,GAAgB;UAChC;QAEF,KAAKhK,0BAAA;UACHiK,YAAA,GAAeH,QAAA,CAAS1B,SAAA,CAAU,GAAG0B,QAAA,CAASI,WAAA,CAAY,GAAG,IAAI,CAAC,IAAID,YAAA;UACtED,aAAA,GAAgBA,aAAA,GAAgB;UAChC;QAEF,KAAK/J,0BAAA;UACH,IAAI8J,cAAA,EAAgB;YAElBC,aAAA,GAAgB9J,uBAAA;UAC5B,OAAiB;YAEL4J,QAAA,GAAWA,QAAA,CAASK,WAAA,CAAa;YACjCF,YAAA,GAAeH,QAAA;YACfC,cAAA,GAAiB;YACjBC,aAAA,GAAgBpK,mBAAA;UACjB;UAED;MACH;MAED,MAAMgJ,MAAA,GAAS,KAAKA,MAAA;MACpB,MAAMwB,UAAA,GAAa,IAAIC,UAAA,CAAWzB,MAAA,CAAO0B,OAAO;MAChDF,UAAA,CAAWG,OAAA,CAAQ3B,MAAA,CAAO4B,gBAAgB;MAC1CJ,UAAA,CAAWK,gBAAA,CAAiB7B,MAAA,CAAO8B,aAAa;MAChDN,UAAA,CAAWO,kBAAA,CAAmB/B,MAAA,CAAOgC,eAAe;MAEpD,IAAI;QACF,MAAMC,IAAA,GAAO,MAAMT,UAAA,CAAWU,SAAA,CAAUb,YAAY;QACpD,OAAOY,IAAA;MACf,SAAcE,CAAA,EAAN;QACA;MACD;IACF;IAED,MAAM,IAAIC,KAAA,CAAM,6BAA6BlB,QAAA,GAAW,wBAAwB;EACjF;EAEDmB,MAAMJ,IAAA,EAAMf,QAAA,GAAW,MAAM;IAC3B,MAAMlB,MAAA,GAAS,KAAKA,MAAA;IAGpB,MAAMvG,KAAA,GAAQ,EAAE;IAChB,MAAMgB,YAAA,GAAe,EAAE;IACvB,MAAM+F,mBAAA,GAAsB,EAAE;IAC9B,MAAMI,UAAA,GAAa,EAAE;IACrB,MAAMG,SAAA,GAAY,CAAE;IAEpB,MAAMuB,gBAAA,GAAoBhC,SAAA,IAAc;MACtC,OAAOS,SAAA,CAAUT,SAAS,KAAK;IAChC;IAED,IAAI5B,IAAA,GAAO;IACX,IAAIgC,QAAA,GAAW;IACf,IAAIC,QAAA,GAAW;IACf,IAAIE,UAAA,GAAa;IAGjB,IAAIoB,IAAA,CAAKM,OAAA,CAAQ,MAAM,MAAM,IAAI;MAE/BN,IAAA,GAAOA,IAAA,CAAKO,OAAA,CAAQ,SAAS,IAAI;IAClC;IAED,MAAMC,KAAA,GAAQR,IAAA,CAAKS,KAAA,CAAM,IAAI;IAC7B,MAAMC,QAAA,GAAWF,KAAA,CAAM7I,MAAA;IAEvB,IAAIgJ,oBAAA,GAAuB;IAC3B,IAAIC,uBAAA,GAA0B;IAC9B,IAAIC,mBAAA,GAAsB;IAE1B,IAAIC,YAAA,GAAe;IACnB,IAAIC,MAAA,GAAS;IACb,IAAIC,WAAA,GAAc;IAClB,IAAIC,OAAA,GAAU;IAEd,IAAIpC,wBAAA,GAA2B;IAG/B,SAASqC,SAAA,GAAY,GAAGA,SAAA,GAAYR,QAAA,EAAUQ,SAAA,IAAa;MACzD,MAAMrE,IAAA,GAAO2D,KAAA,CAAMU,SAAS;MAE5B,IAAIrE,IAAA,CAAKlF,MAAA,KAAW,GAAG;MAEvB,IAAIgJ,oBAAA,EAAsB;QACxB,IAAI9D,IAAA,CAAKsE,UAAA,CAAW,SAAS,GAAG;UAE9B,KAAKC,OAAA,CAAQR,uBAAA,EAAyBC,mBAAmB;UAGzDD,uBAAA,GAA0B/D,IAAA,CAAKU,SAAA,CAAU,CAAC;UAC1CsD,mBAAA,GAAsB;QAChC,OAAe;UACLA,mBAAA,IAAuBhE,IAAA,GAAO;QAC/B;QAED;MACD;MAED,MAAMwE,EAAA,GAAK,IAAIzE,UAAA,CAAWC,IAAA,EAAMqE,SAAA,GAAY,CAAC;MAC7CG,EAAA,CAAGnE,YAAA,CAAc;MAEjB,IAAImE,EAAA,CAAG1D,UAAA,IAAc;QAEnB;MACD;MAGD,MAAM2D,QAAA,GAAWD,EAAA,CAAGjE,QAAA,CAAU;MAE9B,IAAI/F,QAAA;MACJ,IAAIgH,SAAA;MACJ,IAAIkD,OAAA;MACJ,IAAIC,GAAA;MACJ,IAAIC,WAAA;MACJ,IAAI3J,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI0J,EAAA,EAAIC,EAAA,EAAIC,EAAA;MAExB,QAAQN,QAAA;QAEN,KAAK;UAEH,MAAMO,IAAA,GAAOR,EAAA,CAAGjE,QAAA,CAAU;UAE1B,IAAIyE,IAAA,EAAM;YACR,QAAQA,IAAA;cACN,KAAK;gBACHpF,IAAA,GAAO4E,EAAA,CAAGjE,QAAA,CAAU;gBACpB;cAEF,KAAK;gBACH/F,QAAA,GAAW0G,MAAA,CAAO+D,uBAAA,CAAwBT,EAAE;gBAC5C,IAAIhK,QAAA,EAAU;kBACZyH,SAAA,CAAUzH,QAAA,CAAS0K,QAAA,CAASC,IAAI,IAAI3K,QAAA;gBACtD,OAAuB;kBACL4K,OAAA,CAAQC,IAAA,CAAK,wCAAwCb,EAAA,CAAGxD,mBAAA,CAAmB,CAAE;gBAC9E;gBAED;cAEF,KAAK;gBACHY,QAAA,GAAW4C,EAAA,CAAGjE,QAAA,CAAU;gBACxB;cAEF,KAAK;gBACH,MAAM+E,WAAA,GAAcd,EAAA,CAAG3D,kBAAA,CAAkB,EAAG+C,KAAA,CAAM,GAAG;gBACrD,IAAI0B,WAAA,CAAYxK,MAAA,GAAS,GAAG;kBAC1B,IAAI,CAAC+G,QAAA,EAAU;oBACbA,QAAA,GAAW,EAAE;kBACd;kBAEDyD,WAAA,CAAYC,OAAA,CAAQ,UAAUC,OAAA,EAAS;oBACrC3D,QAAA,CAAS/D,IAAA,CAAK0H,OAAA,CAAQC,IAAA,EAAM;kBAChD,CAAmB;gBACF;gBAED;cAEF,KAAK;gBACH,IAAIpB,SAAA,GAAY,GAAG;kBAEjBP,oBAAA,GAAuB;kBACvBC,uBAAA,GAA0BS,EAAA,CAAG3D,kBAAA,CAAoB;kBACjDmD,mBAAA,GAAsB;kBAEtBC,YAAA,GAAe;kBACfC,MAAA,GAAS;gBACV;gBAED;cAEF,KAAK;gBAEH,OAAO,CAACM,EAAA,CAAG1D,UAAA,IAAc;kBACvB,MAAM4E,KAAA,GAAQlB,EAAA,CAAGjE,QAAA,CAAU;kBAE3B,QAAQmF,KAAA;oBACN,KAAK;oBACL,KAAK;sBACHzB,YAAA,GAAeyB,KAAA,KAAU;sBACzBxB,MAAA,GAAS;sBAET;oBAEF,KAAK;oBACL,KAAK;sBACHA,MAAA,GAASwB,KAAA,KAAU;sBAEnB;oBAEF,KAAK;sBACHvB,WAAA,GAAc;sBAEd;oBAEF,KAAK;oBACL,KAAK;sBACHC,OAAA,GAAUsB,KAAA,KAAU;sBAEpB;oBAEF;sBACEN,OAAA,CAAQC,IAAA,CAAK,uCAAuCK,KAAA,GAAQ,eAAe;sBAE3E;kBACH;gBACF;gBAED;cAEF,KAAK;gBACH1D,wBAAA,GAA2B;gBAE3B;YAKH;UACF;UAED;QAGF,KAAK;UACHR,SAAA,GAAYgD,EAAA,CAAGjE,QAAA,CAAU;UACzB/F,QAAA,GAAWgJ,gBAAA,CAAiBhC,SAAS;UAErC,MAAMmE,IAAA,GAAO/E,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACrC,MAAMqF,IAAA,GAAOhF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACrC,MAAMsF,IAAA,GAAOjF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACrC,MAAMuF,EAAA,GAAKlF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAMwF,EAAA,GAAKnF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAMyF,EAAA,GAAKpF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM0F,EAAA,GAAKrF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM2F,EAAA,GAAKtF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM4F,EAAA,GAAKvF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM6F,EAAA,GAAKxF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM8F,EAAA,GAAKzF,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UACnC,MAAM+F,EAAA,GAAK1F,UAAA,CAAW4D,EAAA,CAAGjE,QAAA,CAAQ,CAAE;UAEnC,MAAMgG,MAAA,GAAS,IAAIC,OAAA,CAAO,EAAGvM,GAAA,CAAI6L,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIL,IAAA,EAAMM,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIP,IAAA,EAAMQ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIT,IAAA,EAAM,GAAG,GAAG,GAAG,CAAC;UAEjG,IAAIY,SAAA,GAAWjC,EAAA,CAAG3D,kBAAA,CAAoB,EAAC4E,IAAA,CAAI,EAAG/B,OAAA,CAAQ,OAAO,GAAG;UAEhE,IAAIxC,MAAA,CAAOwF,OAAA,CAAQD,SAAQ,GAAG;YAE5BA,SAAA,GAAWvF,MAAA,CAAOwF,OAAA,CAAQD,SAAQ;UAC9C,OAAiB;YAEL,IAAIA,SAAA,CAASnC,UAAA,CAAW,IAAI,GAAG;cAC7BmC,SAAA,GAAW,WAAWA,SAAA;YACvB,WAAUA,SAAA,CAASnC,UAAA,CAAW,KAAK,GAAG;cACrCmC,SAAA,GAAW,OAAOA,SAAA;YACnB;UACF;UAED3E,UAAA,CAAWhE,IAAA,CAAK;YACdtD,QAAA;YACAgH,SAAA;YACA+E,MAAA;YACAnE,QAAA,EAAUqE,SAAA;YACVE,QAAA,EAAUxC,WAAA;YACVnC;UACZ,CAAW;UAEDmC,WAAA,GAAc;UAEd;QAGF,KAAK;UACH3C,SAAA,GAAYgD,EAAA,CAAGjE,QAAA,CAAU;UACzB/F,QAAA,GAAWgJ,gBAAA,CAAiBhC,SAAS;UACrCvG,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;UACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;UAEnB+D,OAAA,GAAU;YACRlK,QAAA;YACAgH,SAAA;YACAxG,QAAA,EAAU,CAACC,EAAA,EAAIC,EAAE;UAClB;UAEDS,YAAA,CAAamC,IAAA,CAAK4G,OAAO;UAEzB;QAGF,KAAK;UACHlD,SAAA,GAAYgD,EAAA,CAAGjE,QAAA,CAAU;UACzB/F,QAAA,GAAWgJ,gBAAA,CAAiBhC,SAAS;UACrCvG,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;UACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;UACnBmE,EAAA,GAAKN,EAAA,CAAG7D,SAAA,CAAW;UACnBoE,EAAA,GAAKP,EAAA,CAAG7D,SAAA,CAAW;UAEnB+D,OAAA,GAAU;YACRlK,QAAA;YACAgH,SAAA;YACAxG,QAAA,EAAU,CAACC,EAAA,EAAIC,EAAE;YACjByG,aAAA,EAAe,CAACmD,EAAA,EAAIC,EAAE;UACvB;UAEDrD,mBAAA,CAAoB5D,IAAA,CAAK4G,OAAO;UAEhC;QAGF,KAAK;UACHlD,SAAA,GAAYgD,EAAA,CAAGjE,QAAA,CAAU;UACzB/F,QAAA,GAAWgJ,gBAAA,CAAiBhC,SAAS;UACrCmD,GAAA,GAAMT,MAAA;UACNU,WAAA,GAAc,CAACX,YAAA,IAAgB,CAACG,OAAA;UAEhC,IAAIO,GAAA,KAAQ,MAAM;YAChB1J,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;YACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;YACnBxF,EAAA,GAAKqJ,EAAA,CAAG7D,SAAA,CAAW;UAC/B,OAAiB;YACLxF,EAAA,GAAKqJ,EAAA,CAAG7D,SAAA,CAAW;YACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;YACnB1F,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;UACpB;UAEDhG,KAAA,CAAMmD,IAAA,CAAK;YACTtD,QAAA;YACAgH,SAAA;YACAnG,UAAA,EAAY;YACZL,QAAA,EAAU,CAACC,EAAA,EAAIC,EAAA,EAAIC,EAAE;YACrBgC,OAAA,EAAS,CAAC,MAAM,MAAM,IAAI;UACtC,CAAW;UACD4E,UAAA;UAEA,IAAI6C,WAAA,KAAgB,MAAM;YACxBjK,KAAA,CAAMmD,IAAA,CAAK;cACTtD,QAAA;cACAgH,SAAA;cACAnG,UAAA,EAAY;cACZL,QAAA,EAAU,CAACG,EAAA,EAAID,EAAA,EAAID,EAAE;cACrBkC,OAAA,EAAS,CAAC,MAAM,MAAM,IAAI;YACxC,CAAa;YACD4E,UAAA;UACD;UAED;QAGF,KAAK;UACHP,SAAA,GAAYgD,EAAA,CAAGjE,QAAA,CAAU;UACzB/F,QAAA,GAAWgJ,gBAAA,CAAiBhC,SAAS;UACrCmD,GAAA,GAAMT,MAAA;UACNU,WAAA,GAAc,CAACX,YAAA,IAAgB,CAACG,OAAA;UAEhC,IAAIO,GAAA,KAAQ,MAAM;YAChB1J,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;YACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;YACnBxF,EAAA,GAAKqJ,EAAA,CAAG7D,SAAA,CAAW;YACnBkE,EAAA,GAAKL,EAAA,CAAG7D,SAAA,CAAW;UAC/B,OAAiB;YACLkE,EAAA,GAAKL,EAAA,CAAG7D,SAAA,CAAW;YACnBxF,EAAA,GAAKqJ,EAAA,CAAG7D,SAAA,CAAW;YACnBzF,EAAA,GAAKsJ,EAAA,CAAG7D,SAAA,CAAW;YACnB1F,EAAA,GAAKuJ,EAAA,CAAG7D,SAAA,CAAW;UACpB;UAIDhG,KAAA,CAAMmD,IAAA,CAAK;YACTtD,QAAA;YACAgH,SAAA;YACAnG,UAAA,EAAY;YACZL,QAAA,EAAU,CAACC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI0J,EAAE;YACzB1H,OAAA,EAAS,CAAC,MAAM,MAAM,MAAM,IAAI;UAC5C,CAAW;UACD4E,UAAA,IAAc;UAEd,IAAI6C,WAAA,KAAgB,MAAM;YACxBjK,KAAA,CAAMmD,IAAA,CAAK;cACTtD,QAAA;cACAgH,SAAA;cACAnG,UAAA,EAAY;cACZL,QAAA,EAAU,CAAC6J,EAAA,EAAI1J,EAAA,EAAID,EAAA,EAAID,EAAE;cACzBkC,OAAA,EAAS,CAAC,MAAM,MAAM,MAAM,IAAI;YAC9C,CAAa;YACD4E,UAAA,IAAc;UACf;UAED;QAEF;UACE,MAAM,IAAIuB,KAAA,CAAM,qCAAqCmB,QAAA,GAAW,MAAMD,EAAA,CAAGxD,mBAAA,CAAqB,IAAG,GAAG;MACvG;IACF;IAED,IAAI8C,oBAAA,EAAsB;MACxB,KAAKS,OAAA,CAAQR,uBAAA,EAAyBC,mBAAmB;IAC1D;IAED,OAAO;MACLrJ,KAAA;MACA+G,mBAAA;MACA/F,YAAA;MACAiE,IAAA;MACAgC,QAAA;MACAC,QAAA;MACAC,UAAA;MACAC,UAAA;MACAC,wBAAA;MACAC,SAAA;MACAG,QAAA;MACAF,KAAA,EAAO;IACR;EACF;EAAA;EAGD0E,QAAQxE,QAAA,EAAUX,KAAA,GAAQ,MAAM;IAC9B,MAAM/C,GAAA,GAAM0D,QAAA,CAASK,WAAA,CAAa;IAClC,MAAMnB,MAAA,GAAS,KAAKH,MAAA,CAAOzC,GAAG;IAC9B,IAAI4C,MAAA,KAAW,QAAQA,MAAA,YAAkBuF,OAAA,EAAS;MAChD,OAAO;IACR;IAED,IAAIpF,KAAA,EAAO;MACT,OAAO,KAAKL,WAAA,CAAYE,MAAM;IACpC,OAAW;MACL,OAAOA,MAAA;IACR;EACF;EAAA;EAAA;EAID,MAAMwF,iBAAiB1E,QAAA,EAAU;IAC/B,MAAM1D,GAAA,GAAM0D,QAAA,CAASK,WAAA,CAAa;IAClC,IAAI,EAAE/D,GAAA,IAAO,KAAKyC,MAAA,GAAS;MAEzB,KAAKA,MAAA,CAAOzC,GAAG,IAAI,KAAKyD,SAAA,CAAUC,QAAQ,EAAE2E,IAAA,CAAM5D,IAAA,IAAS;QACzD,MAAMxF,IAAA,GAAO,KAAK4F,KAAA,CAAMJ,IAAA,EAAMf,QAAQ;QACtC,KAAKjB,MAAA,CAAOzC,GAAG,IAAIf,IAAA;QACnB,OAAOA,IAAA;MACf,CAAO;IACF;IAED,MAAM,KAAKwD,MAAA,CAAOzC,GAAG;EACtB;EAAA;EAGD6F,QAAQnC,QAAA,EAAUe,IAAA,EAAM;IACtB,MAAMzE,GAAA,GAAM0D,QAAA,CAASK,WAAA,CAAa;IAClC,KAAKtB,MAAA,CAAOzC,GAAG,IAAI,KAAK6E,KAAA,CAAMJ,IAAA,EAAMf,QAAQ;EAC7C;AACH;AAIA,SAAS4E,oBAAoBxF,SAAA,EAAWyF,eAAA,EAAiBC,iBAAA,EAAmBC,OAAA,EAAS;EACnF,MAAMC,aAAA,GAAiB,CAACD,OAAA,IAAW3F,SAAA,KAAc/I,gBAAA,IAAsB0O,OAAA,IAAW3F,SAAA,KAAc9I,qBAAA;EAChG,IAAI0O,aAAA,EAAe;IACjB5F,SAAA,GAAYyF,eAAA;EACb;EAED,OAAOC,iBAAA,CAAkB1F,SAAS,KAAK;AACzC;AAGA,MAAM6F,uBAAA,CAAwB;EAC5BrO,YAAYkI,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKoG,UAAA,GAAa,IAAIrG,gBAAA,CAAiBC,MAAM;IAC7C,KAAKC,MAAA,GAAS,CAAE;EACjB;EAAA;EAGD,MAAMoG,gBAAgB5J,IAAA,EAAM;IAC1B,MAAMuD,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMoG,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAME,aAAA,GAAgB,mBAAIzK,GAAA,CAAK;IAI/B,MAAM0K,qBAAA,GAAwB,MAAAA,CAAOhK,KAAA,EAAMiK,SAAA,GAAY,SAAS;MAC9D,MAAM5F,UAAA,GAAarE,KAAA,CAAKqE,UAAA;MACxB,MAAM6F,QAAA,GAAW,EAAE;MAInB,SAAS/M,CAAA,GAAI,GAAGC,CAAA,GAAIiH,UAAA,CAAWhH,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACjD,MAAMgN,UAAA,GAAY9F,UAAA,CAAWlH,CAAC;QAC9B,MAAMiN,OAAA,GAAUP,UAAA,CAAWR,gBAAA,CAAiBc,UAAA,CAAUxF,QAAQ,EAAE2E,IAAA,CAAK,MAAM;UACzE,MAAMe,aAAA,GAAgBR,UAAA,CAAWV,OAAA,CAAQgB,UAAA,CAAUxF,QAAA,EAAU,KAAK;UAClE,IAAI,CAACvC,eAAA,CAAgBiI,aAAA,CAAclI,IAAI,GAAG;YACxC,OAAO,KAAKmI,SAAA,CAAUH,UAAA,CAAUxF,QAAQ,EAAE4F,KAAA,CAAOC,KAAA,IAAU;cACzD7C,OAAA,CAAQC,IAAA,CAAK4C,KAAK;cAClB,OAAO;YACrB,CAAa;UACF;UAED,OAAOR,qBAAA,CAAsBH,UAAA,CAAWV,OAAA,CAAQgB,UAAA,CAAUxF,QAAQ,GAAGwF,UAAS;QACxF,CAAS;QAEDD,QAAA,CAAS7J,IAAA,CAAK+J,OAAO;MACtB;MAED,MAAMK,MAAA,GAAQ,IAAIC,KAAA,CAAO;MACzBD,MAAA,CAAMhD,QAAA,CAAStD,QAAA,GAAWnE,KAAA,CAAKmE,QAAA;MAC/BsG,MAAA,CAAMhD,QAAA,CAASrD,QAAA,GAAWpE,KAAA,CAAKoE,QAAA;MAC/BpE,KAAA,CAAKyE,KAAA,GAAQgG,MAAA;MAEb,MAAME,cAAA,GAAiB,MAAMvB,OAAA,CAAQwB,GAAA,CAAIV,QAAQ;MACjD,SAAS/M,CAAA,GAAI,GAAGC,CAAA,GAAIuN,cAAA,CAAetN,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMgN,UAAA,GAAYnK,KAAA,CAAKqE,UAAA,CAAWlH,CAAC;QACnC,MAAMkN,aAAA,GAAgBM,cAAA,CAAexN,CAAC;QAEtC,IAAIkN,aAAA,KAAkB,MAAM;UAE1B;QACD;QAGD,IAAIA,aAAA,CAAcQ,OAAA,EAAS;UACzB,MAAMC,cAAA,GAAiBT,aAAA;UACvBF,UAAA,CAAUrB,MAAA,CAAOiC,SAAA,CAAUD,cAAA,CAAeE,QAAA,EAAUF,cAAA,CAAeG,UAAA,EAAYH,cAAA,CAAeI,KAAK;UACnGJ,cAAA,CAAerD,QAAA,CAASlD,wBAAA,GAA2B4F,UAAA,CAAU5F,wBAAA;UAC7DuG,cAAA,CAAeK,IAAA,GAAOhB,UAAA,CAAUxF,QAAA;UAEhClB,MAAA,CAAO2H,oBAAA,CAAqBN,cAAA,EAAgBX,UAAA,CAAUpG,SAAA,EAAW/D,KAAA,CAAKwE,SAAS;UAE/EiG,MAAA,CAAM7K,GAAA,CAAIkL,cAAc;UACxB;QACD;QAGD,IAAIT,aAAA,CAAc5F,KAAA,CAAM4G,QAAA,CAAShO,MAAA,EAAQ;UACvCoN,MAAA,CAAM7K,GAAA,CAAIyK,aAAA,CAAc5F,KAAK;QAC9B;QAID,MAAM6G,kBAAA,GAAqBtL,KAAA,CAAK9B,YAAA;QAChC,MAAMqN,yBAAA,GAA4BvL,KAAA,CAAKiE,mBAAA;QACvC,MAAMuH,WAAA,GAAcxL,KAAA,CAAK9C,KAAA;QAEzB,MAAMgB,YAAA,GAAemM,aAAA,CAAcnM,YAAA;QACnC,MAAM+F,mBAAA,GAAsBoG,aAAA,CAAcpG,mBAAA;QAE1C,MAAM/G,KAAA,GAAQmN,aAAA,CAAcnN,KAAA;QAC5B,MAAM4L,MAAA,GAASqB,UAAA,CAAUrB,MAAA;QACzB,MAAMI,QAAA,GAAWiB,UAAA,CAAUjB,QAAA;QAC3B,MAAMuC,mBAAA,GAAsB3C,MAAA,CAAO4C,WAAA,CAAW,IAAK;QACnD,MAAM3H,SAAA,GAAYoG,UAAA,CAAUpG,SAAA;QAE5B,MAAM4H,aAAA,GAAgB5H,SAAA,KAAc/I,gBAAA,GAAmBC,qBAAA,GAAwB8I,SAAA;QAC/E,SAASvD,EAAA,GAAI,GAAGO,EAAA,GAAI7C,YAAA,CAAab,MAAA,EAAQmD,EAAA,GAAIO,EAAA,EAAGP,EAAA,IAAK;UACnD,MAAMb,EAAA,GAAKzB,YAAA,CAAasC,EAAC;UACzB,MAAMjD,QAAA,GAAWoC,EAAA,CAAGpC,QAAA;UACpBA,QAAA,CAAS,CAAC,EAAEqO,YAAA,CAAa9C,MAAM;UAC/BvL,QAAA,CAAS,CAAC,EAAEqO,YAAA,CAAa9C,MAAM;UAC/BnJ,EAAA,CAAGoE,SAAA,GAAYpE,EAAA,CAAGoE,SAAA,KAAc9I,qBAAA,GAAwB0Q,aAAA,GAAgBhM,EAAA,CAAGoE,SAAA;UAC3EpE,EAAA,CAAG5C,QAAA,GAAW4C,EAAA,CAAG5C,QAAA,IAAYwM,mBAAA,CAAoB5J,EAAA,CAAGoE,SAAA,EAAWpE,EAAA,CAAGoE,SAAA,EAAW/D,KAAA,CAAKwE,SAAA,EAAW,IAAI;UAEjG8G,kBAAA,CAAmBjL,IAAA,CAAKV,EAAE;QAC3B;QAED,SAASa,EAAA,GAAI,GAAGO,EAAA,GAAIkD,mBAAA,CAAoB5G,MAAA,EAAQmD,EAAA,GAAIO,EAAA,EAAGP,EAAA,IAAK;UAC1D,MAAMqL,EAAA,GAAK5H,mBAAA,CAAoBzD,EAAC;UAChC,MAAMjD,QAAA,GAAWsO,EAAA,CAAGtO,QAAA;UACpB,MAAM2G,aAAA,GAAgB2H,EAAA,CAAG3H,aAAA;UACzB3G,QAAA,CAAS,CAAC,EAAEqO,YAAA,CAAa9C,MAAM;UAC/BvL,QAAA,CAAS,CAAC,EAAEqO,YAAA,CAAa9C,MAAM;UAC/B5E,aAAA,CAAc,CAAC,EAAE0H,YAAA,CAAa9C,MAAM;UACpC5E,aAAA,CAAc,CAAC,EAAE0H,YAAA,CAAa9C,MAAM;UACpC+C,EAAA,CAAG9H,SAAA,GAAY8H,EAAA,CAAG9H,SAAA,KAAc9I,qBAAA,GAAwB0Q,aAAA,GAAgBE,EAAA,CAAG9H,SAAA;UAC3E8H,EAAA,CAAG9O,QAAA,GAAW8O,EAAA,CAAG9O,QAAA,IAAYwM,mBAAA,CAAoBsC,EAAA,CAAG9H,SAAA,EAAW8H,EAAA,CAAG9H,SAAA,EAAW/D,KAAA,CAAKwE,SAAA,EAAW,IAAI;UAEjG+G,yBAAA,CAA0BlL,IAAA,CAAKwL,EAAE;QAClC;QAED,SAASrL,EAAA,GAAI,GAAGO,EAAA,GAAI7D,KAAA,CAAMG,MAAA,EAAQmD,EAAA,GAAIO,EAAA,EAAGP,EAAA,IAAK;UAC5C,MAAMF,GAAA,GAAMpD,KAAA,CAAMsD,EAAC;UACnB,MAAMjD,QAAA,GAAW+C,GAAA,CAAI/C,QAAA;UACrB,SAASuD,EAAA,GAAI,GAAGgL,EAAA,GAAIvO,QAAA,CAASF,MAAA,EAAQyD,EAAA,GAAIgL,EAAA,EAAGhL,EAAA,IAAK;YAC/CvD,QAAA,CAASuD,EAAC,EAAE8K,YAAA,CAAa9C,MAAM;UAChC;UAEDxI,GAAA,CAAIyD,SAAA,GAAYzD,GAAA,CAAIyD,SAAA,KAAc/I,gBAAA,GAAmB+I,SAAA,GAAYzD,GAAA,CAAIyD,SAAA;UACrEzD,GAAA,CAAIvD,QAAA,GAAWuD,GAAA,CAAIvD,QAAA,IAAYwM,mBAAA,CAAoBjJ,GAAA,CAAIyD,SAAA,EAAWA,SAAA,EAAW/D,KAAA,CAAKwE,SAAA,EAAW,KAAK;UAClGuF,aAAA,CAAcnK,GAAA,CAAIU,GAAA,CAAIyD,SAAS;UAI/B,IAAI0H,mBAAA,KAAwBvC,QAAA,EAAU;YACpC3L,QAAA,CAASwO,OAAA,CAAS;UACnB;UAEDP,WAAA,CAAYnL,IAAA,CAAKC,GAAG;QACrB;QAEDN,KAAA,CAAKsE,UAAA,IAAc+F,aAAA,CAAc/F,UAAA;MAClC;MAID,IAAI2F,SAAA,EAAW;QACbxG,MAAA,CAAO2H,oBAAA,CAAqBX,MAAA,EAAOR,SAAA,CAAUlG,SAAA,EAAW/D,KAAA,CAAKwE,SAAS;MACvE;MAED,OAAOxE,KAAA;IACR;IAGD,SAAS7C,CAAA,GAAI,GAAGC,CAAA,GAAI8C,IAAA,CAAKhD,KAAA,EAAOC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC1C4M,aAAA,CAAcnK,GAAA,CAAIM,IAAA,CAAKhD,KAAA,CAAMC,CAAC,EAAE4G,SAAS;IAC1C;IAED,MAAMiG,qBAAA,CAAsB9J,IAAI;IAEhC,IAAIuD,MAAA,CAAOxF,aAAA,EAAe;MACxB,MAAME,gBAAA,GAAmB4L,aAAA,CAAciC,IAAA,GAAO;MAC9C/O,mBAAA,CAAoBiD,IAAA,CAAKhD,KAAK;MAC9Be,aAAA,CAAciC,IAAA,CAAKhD,KAAA,EAAOgD,IAAA,CAAKhC,YAAA,EAAcC,gBAAgB;IAC9D;IAGD,MAAMsG,KAAA,GAAQvE,IAAA,CAAKuE,KAAA;IACnB,IAAIvE,IAAA,CAAKhD,KAAA,CAAMG,MAAA,GAAS,GAAG;MACzBoH,KAAA,CAAM7E,GAAA,CAAIqM,YAAA,CAAa/L,IAAA,CAAKhD,KAAA,EAAO,GAAG,OAAOgD,IAAA,CAAKoE,UAAU,CAAC;IAC9D;IAED,IAAIpE,IAAA,CAAKhC,YAAA,CAAab,MAAA,GAAS,GAAG;MAChCoH,KAAA,CAAM7E,GAAA,CAAIqM,YAAA,CAAa/L,IAAA,CAAKhC,YAAA,EAAc,CAAC,CAAC;IAC7C;IAED,IAAIgC,IAAA,CAAK+D,mBAAA,CAAoB5G,MAAA,GAAS,GAAG;MACvCoH,KAAA,CAAM7E,GAAA,CAAIqM,YAAA,CAAa/L,IAAA,CAAK+D,mBAAA,EAAqB,GAAG,IAAI,CAAC;IAC1D;IAED,OAAOQ,KAAA;EACR;EAEDyH,eAAevH,QAAA,EAAU;IACvB,OAAOA,QAAA,KAAa,QAAQA,QAAA,CAASK,WAAA,CAAa,KAAI,KAAKtB,MAAA;EAC5D;EAED,MAAMyI,eAAexH,QAAA,EAAU;IAC7B,IAAIA,QAAA,KAAa,QAAQ,KAAKuH,cAAA,CAAevH,QAAQ,GAAG;MACtD,MAAM1D,GAAA,GAAM0D,QAAA,CAASK,WAAA,CAAa;MAClC,MAAMP,KAAA,GAAQ,MAAM,KAAKf,MAAA,CAAOzC,GAAG;MACnC,OAAOwD,KAAA,CAAMT,KAAA,CAAO;IAC1B,OAAW;MACL,OAAO;IACR;EACF;EAAA;EAGD,MAAMsG,UAAU3F,QAAA,EAAU;IACxB,MAAMkF,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAM5I,GAAA,GAAM0D,QAAA,CAASK,WAAA,CAAa;IAClC,IAAI,KAAKkH,cAAA,CAAevH,QAAQ,GAAG;MAEjC,OAAO,KAAKwH,cAAA,CAAexH,QAAQ;IACzC,OAAW;MAGL,MAAMkF,UAAA,CAAWR,gBAAA,CAAiB1E,QAAQ;MAE1C,MAAMzE,IAAA,GAAO2J,UAAA,CAAWV,OAAA,CAAQxE,QAAQ;MACxC,MAAMyF,OAAA,GAAU,KAAKN,eAAA,CAAgB5J,IAAI;MAKzC,IAAI,KAAKgM,cAAA,CAAevH,QAAQ,GAAG;QACjC,OAAO,KAAKwH,cAAA,CAAexH,QAAQ;MACpC;MAGD,IAAIzC,UAAA,CAAWhC,IAAA,CAAKiC,IAAI,GAAG;QACzB,KAAKuB,MAAA,CAAOzC,GAAG,IAAImJ,OAAA;MACpB;MAGD,MAAM3F,KAAA,GAAQ,MAAM2F,OAAA;MACpB,OAAO3F,KAAA,CAAMT,KAAA,CAAO;IACrB;EACF;EAAA;EAGD,MAAMoI,WAAW1G,IAAA,EAAM;IACrB,MAAMmE,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAM3J,IAAA,GAAO2J,UAAA,CAAW/D,KAAA,CAAMJ,IAAI;IAClC,IAAIxD,UAAA,CAAWhC,IAAA,CAAKiC,IAAI,KAAK,KAAK+J,cAAA,CAAehM,IAAA,CAAKyE,QAAQ,GAAG;MAC/D,OAAO,KAAKwH,cAAA,CAAejM,IAAA,CAAKyE,QAAQ;IACzC;IAED,OAAO,KAAKmF,eAAA,CAAgB5J,IAAI;EACjC;AACH;AAEA,SAASmM,eAAeC,CAAA,EAAGC,CAAA,EAAG;EAC5B,IAAID,CAAA,CAAEvI,SAAA,KAAcwI,CAAA,CAAExI,SAAA,EAAW;IAC/B,OAAO;EACR;EAED,IAAIuI,CAAA,CAAEvI,SAAA,GAAYwI,CAAA,CAAExI,SAAA,EAAW;IAC7B,OAAO;EACR;EAED,OAAO;AACT;AAEA,SAASkI,aAAaO,QAAA,EAAUC,WAAA,EAAaC,qBAAA,GAAwB,OAAOC,aAAA,GAAgB,MAAM;EAKhGH,QAAA,CAASI,IAAA,CAAKP,cAAc;EAE5B,IAAIM,aAAA,KAAkB,MAAM;IAC1BA,aAAA,GAAgBH,QAAA,CAASnP,MAAA;EAC1B;EAED,MAAMwP,SAAA,GAAY,IAAIC,YAAA,CAAaL,WAAA,GAAcE,aAAA,GAAgB,CAAC;EAClE,MAAMjN,OAAA,GAAU+M,WAAA,KAAgB,IAAI,IAAIK,YAAA,CAAaL,WAAA,GAAcE,aAAA,GAAgB,CAAC,IAAI;EACxF,MAAMnI,SAAA,GAAY,EAAE;EAEpB,MAAMuI,SAAA,GAAY,IAAIC,KAAA,CAAM,CAAC;EAC7B,MAAMC,cAAA,GAAiB,IAAIC,cAAA,CAAgB;EAC3C,IAAIC,YAAA,GAAe;EACnB,IAAIC,MAAA,GAAS;EACb,IAAIC,aAAA,GAAgB;EACpB,IAAIC,MAAA,GAAS;EAEb,SAASC,KAAA,GAAQ,GAAGC,KAAA,GAAQhB,QAAA,CAASnP,MAAA,EAAQkQ,KAAA,GAAQC,KAAA,EAAOD,KAAA,IAAS;IACnE,MAAME,IAAA,GAAOjB,QAAA,CAASe,KAAK;IAC3B,IAAIhQ,QAAA,GAAWkQ,IAAA,CAAKlQ,QAAA;IACpB,IAAIA,QAAA,CAASF,MAAA,KAAW,GAAG;MACzB0P,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBwP,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBwP,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBwP,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBwP,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBwP,SAAA,CAAU,CAAC,IAAIxP,QAAA,CAAS,CAAC;MACzBA,QAAA,GAAWwP,SAAA;IACZ;IAED,SAASW,CAAA,GAAI,GAAGtQ,CAAA,GAAIG,QAAA,CAASF,MAAA,EAAQqQ,CAAA,GAAItQ,CAAA,EAAGsQ,CAAA,IAAK;MAC/C,MAAMpP,CAAA,GAAIf,QAAA,CAASmQ,CAAC;MACpB,MAAMjN,KAAA,GAAQ6M,MAAA,GAASI,CAAA,GAAI;MAC3Bb,SAAA,CAAUpM,KAAA,GAAQ,CAAC,IAAInC,CAAA,CAAEC,CAAA;MACzBsO,SAAA,CAAUpM,KAAA,GAAQ,CAAC,IAAInC,CAAA,CAAEE,CAAA;MACzBqO,SAAA,CAAUpM,KAAA,GAAQ,CAAC,IAAInC,CAAA,CAAEG,CAAA;IAC1B;IAGD,IAAIgO,WAAA,KAAgB,GAAG;MACrB,IAAI,CAACgB,IAAA,CAAK7P,UAAA,EAAY;QACpB,MAAMJ,EAAA,GAAKD,QAAA,CAAS,CAAC;QACrB,MAAME,EAAA,GAAKF,QAAA,CAAS,CAAC;QACrB,MAAMG,EAAA,GAAKH,QAAA,CAAS,CAAC;QACrBrC,SAAA,CAAUyC,UAAA,CAAWF,EAAA,EAAID,EAAE;QAC3BpC,SAAA,CAAUuC,UAAA,CAAWD,EAAA,EAAID,EAAE;QAC3BgQ,IAAA,CAAK7P,UAAA,GAAa,IAAIzC,OAAA,CAAS,EAAC0C,YAAA,CAAa3C,SAAA,EAAWE,SAAS,EAAE0C,SAAA,CAAW;MAC/E;MAED,IAAI6P,WAAA,GAAcF,IAAA,CAAK/N,OAAA;MACvB,IAAIiO,WAAA,CAAYtQ,MAAA,KAAW,GAAG;QAC5B0P,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BZ,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BZ,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BZ,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BZ,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BZ,SAAA,CAAU,CAAC,IAAIY,WAAA,CAAY,CAAC;QAC5BA,WAAA,GAAcZ,SAAA;MACf;MAED,SAASW,CAAA,GAAI,GAAGtQ,CAAA,GAAIuQ,WAAA,CAAYtQ,MAAA,EAAQqQ,CAAA,GAAItQ,CAAA,EAAGsQ,CAAA,IAAK;QAElD,IAAIE,CAAA,GAAIH,IAAA,CAAK7P,UAAA;QACb,IAAI+P,WAAA,CAAYD,CAAC,GAAG;UAClBE,CAAA,GAAID,WAAA,CAAYD,CAAC,EAAE3L,IAAA;QACpB;QAED,MAAMtB,KAAA,GAAQ6M,MAAA,GAASI,CAAA,GAAI;QAC3BhO,OAAA,CAAQe,KAAA,GAAQ,CAAC,IAAImN,CAAA,CAAErP,CAAA;QACvBmB,OAAA,CAAQe,KAAA,GAAQ,CAAC,IAAImN,CAAA,CAAEpP,CAAA;QACvBkB,OAAA,CAAQe,KAAA,GAAQ,CAAC,IAAImN,CAAA,CAAEnP,CAAA;MACxB;IACF;IAED,IAAI0O,YAAA,KAAiBM,IAAA,CAAK1J,SAAA,EAAW;MACnC,IAAIoJ,YAAA,KAAiB,MAAM;QACzBF,cAAA,CAAeY,QAAA,CAAST,MAAA,EAAQC,aAAA,EAAe7I,SAAA,CAAUnH,MAAA,GAAS,CAAC;MACpE;MAED,MAAMN,QAAA,GAAW0Q,IAAA,CAAK1Q,QAAA;MACtB,IAAIA,QAAA,KAAa,MAAM;QACrB,IAAI0P,WAAA,KAAgB,GAAG;UACrBjI,SAAA,CAAUnE,IAAA,CAAKtD,QAAQ;QACjC,WAAmB0P,WAAA,KAAgB,GAAG;UAC5B,IAAI1P,QAAA,KAAa,MAAM;YACrB,IAAI2P,qBAAA,EAAuB;cACzBlI,SAAA,CAAUnE,IAAA,CAAKtD,QAAA,CAAS0K,QAAA,CAASqG,YAAA,CAAarG,QAAA,CAASsG,uBAAuB;YAC5F,OAAmB;cACLvJ,SAAA,CAAUnE,IAAA,CAAKtD,QAAA,CAAS0K,QAAA,CAASqG,YAAY;YAC9C;UACb,OAAiB;YACLtJ,SAAA,CAAUnE,IAAA,CAAK,IAAI;UACpB;QACF;MACT,OAAa;QAGLmE,SAAA,CAAUnE,IAAA,CAAKoN,IAAA,CAAK1J,SAAS;MAC9B;MAEDoJ,YAAA,GAAeM,IAAA,CAAK1J,SAAA;MACpBqJ,MAAA,GAASE,MAAA,GAAS;MAClBD,aAAA,GAAgB9P,QAAA,CAASF,MAAA;IAC/B,OAAW;MACLgQ,aAAA,IAAiB9P,QAAA,CAASF,MAAA;IAC3B;IAEDiQ,MAAA,IAAU,IAAI/P,QAAA,CAASF,MAAA;EACxB;EAED,IAAIgQ,aAAA,GAAgB,GAAG;IACrBJ,cAAA,CAAeY,QAAA,CAAST,MAAA,EAAQY,QAAA,EAAUxJ,SAAA,CAAUnH,MAAA,GAAS,CAAC;EAC/D;EAED4P,cAAA,CAAegB,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBrB,SAAA,EAAW,CAAC,CAAC;EAEzE,IAAInN,OAAA,KAAY,MAAM;IACpBuN,cAAA,CAAegB,YAAA,CAAa,UAAU,IAAIC,eAAA,CAAgBxO,OAAA,EAAS,CAAC,CAAC;EACtE;EAED,IAAIyO,QAAA,GAAW;EAEf,IAAI1B,WAAA,KAAgB,GAAG;IACrB,IAAIC,qBAAA,EAAuB;MACzByB,QAAA,GAAW,IAAIvR,uBAAA,CAAwBqQ,cAAA,EAAgBzI,SAAA,CAAUnH,MAAA,KAAW,IAAImH,SAAA,CAAU,CAAC,IAAIA,SAAS;IAC9G,OAAW;MACL2J,QAAA,GAAW,IAAItR,YAAA,CAAaoQ,cAAA,EAAgBzI,SAAA,CAAUnH,MAAA,KAAW,IAAImH,SAAA,CAAU,CAAC,IAAIA,SAAS;IAC9F;EACL,WAAaiI,WAAA,KAAgB,GAAG;IAC5B0B,QAAA,GAAW,IAAIC,IAAA,CAAKnB,cAAA,EAAgBzI,SAAA,CAAUnH,MAAA,KAAW,IAAImH,SAAA,CAAU,CAAC,IAAIA,SAAS;EACtF;EAED,IAAIkI,qBAAA,EAAuB;IACzByB,QAAA,CAASnR,iBAAA,GAAoB;IAE7B,MAAMqR,aAAA,GAAgB,IAAIvB,YAAA,CAAaN,QAAA,CAASnP,MAAA,GAAS,IAAI,CAAC;IAC9D,MAAMiR,aAAA,GAAgB,IAAIxB,YAAA,CAAaN,QAAA,CAASnP,MAAA,GAAS,IAAI,CAAC;IAC9D,MAAMkR,cAAA,GAAiB,IAAIzB,YAAA,CAAaN,QAAA,CAASnP,MAAA,GAAS,IAAI,CAAC;IAC/D,SAASF,CAAA,GAAI,GAAGC,CAAA,GAAIoP,QAAA,CAASnP,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC/C,MAAM0O,EAAA,GAAKW,QAAA,CAASrP,CAAC;MACrB,MAAMI,QAAA,GAAWsO,EAAA,CAAGtO,QAAA;MACpB,MAAM2G,aAAA,GAAgB2H,EAAA,CAAG3H,aAAA;MACzB,MAAMmD,EAAA,GAAKnD,aAAA,CAAc,CAAC;MAC1B,MAAMoD,EAAA,GAAKpD,aAAA,CAAc,CAAC;MAC1B,MAAM1G,EAAA,GAAKD,QAAA,CAAS,CAAC;MACrB,MAAME,EAAA,GAAKF,QAAA,CAAS,CAAC;MACrB,MAAMkD,KAAA,GAAQtD,CAAA,GAAI,IAAI;MACtBkR,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG9I,CAAA;MAC9B8P,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG7I,CAAA;MAC9B6P,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG5I,CAAA;MAC9B4P,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG9I,CAAA;MAC9B8P,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG7I,CAAA;MAC9B6P,aAAA,CAAc5N,KAAA,GAAQ,CAAC,IAAI4G,EAAA,CAAG5I,CAAA;MAE9B6P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG/I,CAAA;MAC9B+P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG9I,CAAA;MAC9B8P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG7I,CAAA;MAC9B6P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG/I,CAAA;MAC9B+P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG9I,CAAA;MAC9B8P,aAAA,CAAc7N,KAAA,GAAQ,CAAC,IAAI6G,EAAA,CAAG7I,CAAA;MAE9B8P,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGc,CAAA,GAAIf,EAAA,CAAGe,CAAA;MACtCgQ,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGe,CAAA,GAAIhB,EAAA,CAAGgB,CAAA;MACtC+P,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGgB,CAAA,GAAIjB,EAAA,CAAGiB,CAAA;MACtC8P,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGc,CAAA,GAAIf,EAAA,CAAGe,CAAA;MACtCgQ,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGe,CAAA,GAAIhB,EAAA,CAAGgB,CAAA;MACtC+P,cAAA,CAAe9N,KAAA,GAAQ,CAAC,IAAIhD,EAAA,CAAGgB,CAAA,GAAIjB,EAAA,CAAGiB,CAAA;IACvC;IAEDwO,cAAA,CAAegB,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBG,aAAA,EAAe,GAAG,KAAK,CAAC;IACpFpB,cAAA,CAAegB,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBI,aAAA,EAAe,GAAG,KAAK,CAAC;IACpFrB,cAAA,CAAegB,YAAA,CAAa,aAAa,IAAIC,eAAA,CAAgBK,cAAA,EAAgB,GAAG,KAAK,CAAC;EACvF;EAED,OAAOJ,QAAA;AACT;AAIA,MAAMK,WAAA,SAAoBC,MAAA,CAAO;EAC/BlT,YAAY4J,OAAA,EAAS;IACnB,MAAMA,OAAO;IAGb,KAAKX,SAAA,GAAY,EAAE;IACnB,KAAKkK,eAAA,GAAkB,CAAE;IAGzB,KAAKC,UAAA,GAAa,IAAI/E,uBAAA,CAAwB,IAAI;IAGlD,KAAKX,OAAA,GAAU,CAAE;IAGjB,KAAK2F,YAAA,CAAa,EAAE;IAGpB,KAAK3Q,aAAA,GAAgB;IAGrB,KAAKoH,gBAAA,GAAmB;EACzB;EAEDwJ,oBAAoBC,IAAA,EAAM;IACxB,KAAKzJ,gBAAA,GAAmByJ,IAAA;IACxB,OAAO;EACR;EAED,MAAMC,iBAAiBC,GAAA,EAAK;IAC1B,MAAM/J,UAAA,GAAa,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC9CF,UAAA,CAAWG,OAAA,CAAQ,KAAK0J,IAAI;IAC5B7J,UAAA,CAAWK,gBAAA,CAAiB,KAAKC,aAAa;IAC9CN,UAAA,CAAWO,kBAAA,CAAmB,KAAKC,eAAe;IAElD,MAAMC,IAAA,GAAO,MAAMT,UAAA,CAAWU,SAAA,CAAUqJ,GAAG;IAC3C,MAAMC,cAAA,GAAiB;IACvB,MAAM/I,KAAA,GAAQR,IAAA,CAAKS,KAAA,CAAM,SAAS;IAClC,MAAM3B,SAAA,GAAY,EAAE;IACpB,SAASrH,CAAA,GAAI,GAAGC,CAAA,GAAI8I,KAAA,CAAM7I,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,MAAMoF,IAAA,GAAO2D,KAAA,CAAM/I,CAAC;MACpB,IAAI8R,cAAA,CAAe5M,IAAA,CAAKE,IAAI,GAAG;QAC7B,MAAM2M,SAAA,GAAY3M,IAAA,CAAK0D,OAAA,CAAQgJ,cAAA,EAAgB,EAAE;QACjD,MAAMlS,QAAA,GAAW,KAAKyK,uBAAA,CAAwB,IAAIlF,UAAA,CAAW4M,SAAS,CAAC;QACvE1K,SAAA,CAAUnE,IAAA,CAAKtD,QAAQ;MACxB;IACF;IAED,KAAK6R,YAAA,CAAapK,SAAS;EAC5B;EAED2K,KAAKH,GAAA,EAAKI,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMrK,UAAA,GAAa,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC9CF,UAAA,CAAWG,OAAA,CAAQ,KAAK0J,IAAI;IAC5B7J,UAAA,CAAWK,gBAAA,CAAiB,KAAKC,aAAa;IAC9CN,UAAA,CAAWO,kBAAA,CAAmB,KAAKC,eAAe;IAClDR,UAAA,CAAWkK,IAAA,CACTH,GAAA,EACCtJ,IAAA,IAAS;MACR,KAAKiJ,UAAA,CACFvC,UAAA,CAAW1G,IAAA,EAAM,KAAKgJ,eAAe,EACrCpF,IAAA,CAAM7E,KAAA,IAAU;QACf,KAAK2G,oBAAA,CAAqB3G,KAAA,EAAOzJ,gBAAA,EAAkB,KAAK0T,eAAA,EAAiB,IAAI;QAC7E,KAAKa,wBAAA,CAAyB9K,KAAK;QACnC2K,MAAA,CAAO3K,KAAK;MACxB,CAAW,EACA8F,KAAA,CAAM+E,OAAO;IACjB,GACDD,UAAA,EACAC,OACD;EACF;EAEDxJ,MAAMJ,IAAA,EAAM0J,MAAA,EAAQ;IAClB,KAAKT,UAAA,CAAWvC,UAAA,CAAW1G,IAAA,EAAM,KAAKgJ,eAAe,EAAEpF,IAAA,CAAM7E,KAAA,IAAU;MACrE,KAAK8K,wBAAA,CAAyB9K,KAAK;MACnC2K,MAAA,CAAO3K,KAAK;IAClB,CAAK;EACF;EAEDmK,aAAapK,SAAA,EAAW;IACtB,KAAKkK,eAAA,GAAkB,CAAE;IACzB,KAAKlK,SAAA,GAAY,EAAE;IACnB,SAASrH,CAAA,GAAI,GAAGC,CAAA,GAAIoH,SAAA,CAAUnH,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAChD,KAAKqS,WAAA,CAAYhL,SAAA,CAAUrH,CAAC,CAAC;IAC9B;IAGD,KAAKqS,WAAA,CAAY,KAAKhI,uBAAA,CAAwB,IAAIlF,UAAA,CAAW,gDAAgD,CAAC,CAAC;IAC/G,KAAKkN,WAAA,CAAY,KAAKhI,uBAAA,CAAwB,IAAIlF,UAAA,CAAW,gDAAgD,CAAC,CAAC;IAE/G,OAAO;EACR;EAEDmN,WAAWxG,OAAA,EAAS;IAClB,KAAKA,OAAA,GAAUA,OAAA;IAEf,OAAO;EACR;EAEDuG,YAAYzS,QAAA,EAAU;IAGpB,MAAM2S,MAAA,GAAS,KAAKhB,eAAA;IACpB,IAAI,CAACgB,MAAA,CAAO3S,QAAA,CAAS0K,QAAA,CAASC,IAAI,GAAG;MACnC,KAAKlD,SAAA,CAAUnE,IAAA,CAAKtD,QAAQ;MAC5B2S,MAAA,CAAO3S,QAAA,CAAS0K,QAAA,CAASC,IAAI,IAAI3K,QAAA;IAClC;IAED,OAAO;EACR;EAED4S,YAAY5L,SAAA,EAAW;IACrB,IAAIA,SAAA,CAAU8C,UAAA,CAAW,KAAK,GAAG;MAE/B,MAAMpK,KAAA,GAAQsH,SAAA,CAAUd,SAAA,CAAU,CAAC;MAEnC,OAAO,KAAKuE,uBAAA,CACV,IAAIlF,UAAA,CAAW,kBAAkB7F,KAAA,GAAQ,qBAAqBA,KAAA,GAAQ,YAAYA,KAAU,CAC7F;IACF;IAED,OAAO,KAAKiS,eAAA,CAAgB3K,SAAS,KAAK;EAC3C;EAAA;EAAA;EAIDqH,qBAAqB3G,KAAA,EAAO+E,eAAA,EAAiBC,iBAAA,EAAmBmG,iBAAA,GAAoB,OAAO;IAEzF,MAAMnM,MAAA,GAAS;IACf,MAAMoM,mBAAA,GAAsBrG,eAAA,KAAoBxO,gBAAA;IAChDyJ,KAAA,CAAMqL,QAAA,CAAUC,CAAA,IAAM;MACpB,IAAIA,CAAA,CAAEC,MAAA,IAAUD,CAAA,CAAEE,cAAA,EAAgB;QAChC,IAAIjD,KAAA,CAAMkD,OAAA,CAAQH,CAAA,CAAEhT,QAAQ,GAAG;UAC7B,SAASI,CAAA,GAAI,GAAGC,CAAA,GAAI2S,CAAA,CAAEhT,QAAA,CAASM,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACjD,IAAI,CAAC4S,CAAA,CAAEhT,QAAA,CAASI,CAAC,EAAEgT,UAAA,EAAY;cAC7BJ,CAAA,CAAEhT,QAAA,CAASI,CAAC,IAAIwS,WAAA,CAAYI,CAAA,EAAGA,CAAA,CAAEhT,QAAA,CAASI,CAAC,CAAC;YAC7C;UACF;QACF,WAAU,CAAC4S,CAAA,CAAEhT,QAAA,CAASoT,UAAA,EAAY;UACjCJ,CAAA,CAAEhT,QAAA,GAAW4S,WAAA,CAAYI,CAAA,EAAGA,CAAA,CAAEhT,QAAQ;QACvC;MACF;IACP,CAAK;IAKD,SAAS4S,YAAYI,CAAA,EAAGhM,SAAA,EAAW;MAGjC,IAAI8L,mBAAA,IAAuB,EAAE9L,SAAA,IAAa0F,iBAAA,KAAsB,CAACmG,iBAAA,EAAmB;QAClF,OAAO7L,SAAA;MACR;MAED,MAAM2F,OAAA,GAAUqG,CAAA,CAAEE,cAAA,IAAkBF,CAAA,CAAE/S,iBAAA;MACtC,MAAM2M,aAAA,GACH,CAACD,OAAA,IAAW3F,SAAA,KAAc/I,gBAAA,IAAsB0O,OAAA,IAAW3F,SAAA,KAAc9I,qBAAA;MAC5E,IAAI0O,aAAA,EAAe;QACjB5F,SAAA,GAAYyF,eAAA;MACb;MAED,IAAIzM,QAAA,GAAW;MACf,IAAIgH,SAAA,IAAa0F,iBAAA,EAAmB;QAClC1M,QAAA,GAAW0M,iBAAA,CAAkB1F,SAAS;MACvC,WAAU6L,iBAAA,EAAmB;QAG5B7S,QAAA,GAAW0G,MAAA,CAAOkM,WAAA,CAAY5L,SAAS;QACvC,IAAIhH,QAAA,KAAa,MAAM;UAErB,MAAM,IAAI8I,KAAA,CAAM,6CAA6C9B,SAAA,iBAA0B;QACxF;MACT,OAAa;QACL,OAAOA,SAAA;MACR;MAED,IAAIgM,CAAA,CAAEE,cAAA,EAAgB;QACpBlT,QAAA,GAAWA,QAAA,CAAS0K,QAAA,CAASqG,YAAA;QAE7B,IAAIiC,CAAA,CAAE/S,iBAAA,EAAmB;UACvBD,QAAA,GAAWA,QAAA,CAAS0K,QAAA,CAASsG,uBAAA;QAC9B;MACF;MAED,OAAOhR,QAAA;IACR;EACF;EAEDqT,gBAAA,EAAkB;IAChB,OAAO,KAAKT,WAAA,CAAY3U,gBAAgB;EACzC;EAEDqV,oBAAA,EAAsB;IACpB,OAAO,KAAKV,WAAA,CAAY1U,qBAAqB;EAC9C;EAEDuM,wBAAwB8I,UAAA,EAAY;IAGlC,IAAI5I,IAAA,GAAO;IAGX,IAAIjL,KAAA,GAAQ;IACZ,IAAI8T,SAAA,GAAY;IAGhB,IAAIC,KAAA,GAAQ;IACZ,IAAIC,aAAA,GAAgB;IAEpB,IAAIC,SAAA,GAAY;IAEhB,IAAIC,UAAA,GAAaxW,mBAAA;IAEjB,IAAI2T,YAAA,GAAe;IAEnB,MAAM3C,IAAA,GAAOmF,UAAA,CAAWxN,QAAA,CAAU;IAClC,IAAI,CAACqI,IAAA,EAAM;MACT,MAAM,IAAItF,KAAA,CACR,+DAA+DyK,UAAA,CAAW/M,mBAAA,CAAmB,IAAK,GACnG;IACF;IAGD,IAAI0E,KAAA,GAAQ;IACZ,OAAO,MAAM;MACXA,KAAA,GAAQqI,UAAA,CAAWxN,QAAA,CAAU;MAE7B,IAAI,CAACmF,KAAA,EAAO;QACV;MACD;MAED,QAAQA,KAAA,CAAM2I,WAAA,CAAa;QACzB,KAAK;UACHlJ,IAAA,GAAO4I,UAAA,CAAWxN,QAAA,CAAU;UAC5B;QAEF,KAAK;UACHrG,KAAA,GAAQ6T,UAAA,CAAWxN,QAAA,CAAU;UAC7B,IAAIrG,KAAA,CAAMoK,UAAA,CAAW,IAAI,GAAG;YAC1BpK,KAAA,GAAQ,MAAMA,KAAA,CAAMwG,SAAA,CAAU,CAAC;UAChC,WAAU,CAACxG,KAAA,CAAMoK,UAAA,CAAW,GAAG,GAAG;YACjC,MAAM,IAAIhB,KAAA,CACR,sDAAsDyK,UAAA,CAAW/M,mBAAA,CAAmB,IAAK,GAC1F;UACF;UAED;QAEF,KAAK;UACHgN,SAAA,GAAYD,UAAA,CAAWxN,QAAA,CAAU;UACjC,IAAIyN,SAAA,CAAU1J,UAAA,CAAW,IAAI,GAAG;YAC9B0J,SAAA,GAAY,MAAMA,SAAA,CAAUtN,SAAA,CAAU,CAAC;UACxC,WAAU,CAACsN,SAAA,CAAU1J,UAAA,CAAW,GAAG,GAAG;YAErCiH,YAAA,GAAe,KAAK6B,WAAA,CAAYY,SAAS;YACzC,IAAI,CAACzC,YAAA,EAAc;cACjB,MAAM,IAAIjI,KAAA,CACR,2DAA2DyK,UAAA,CAAW/M,mBAAA,CAAmB,IAAK,GAC/F;YACF;YAGDuK,YAAA,GAAeA,YAAA,CAAarG,QAAA,CAASqG,YAAA;UACtC;UAED;QAEF,KAAK;UACH0C,KAAA,GAAQK,QAAA,CAASP,UAAA,CAAWxN,QAAA,EAAU;UAEtC,IAAIgO,KAAA,CAAMN,KAAK,GAAG;YAChB,MAAM,IAAI3K,KAAA,CACR,4DAA4DyK,UAAA,CAAW/M,mBAAA,CAAmB,IAAK,GAChG;UACF;UAEDiN,KAAA,GAAQ5O,IAAA,CAAKmP,GAAA,CAAI,GAAGnP,IAAA,CAAKoP,GAAA,CAAI,GAAGR,KAAA,GAAQ,GAAG,CAAC;UAE5C,IAAIA,KAAA,GAAQ,GAAG;YACbC,aAAA,GAAgB;UACjB;UAED;QAEF,KAAK;UACHC,SAAA,GAAYG,QAAA,CAASP,UAAA,CAAWxN,QAAA,EAAU;UAE1C,IAAIgO,KAAA,CAAMJ,SAAS,GAAG;YACpB,MAAM,IAAI7K,KAAA,CACR,gEAAgEvD,UAAA,CAAWiB,mBAAA,CAAmB,IAAK,GACpG;UACF;UAEDmN,SAAA,GAAY9O,IAAA,CAAKmP,GAAA,CAAI,GAAGnP,IAAA,CAAKoP,GAAA,CAAI,GAAGN,SAAA,GAAY,GAAG,CAAC;UAEpD;QAEF,KAAK;UACHC,UAAA,GAAavW,kBAAA;UACb;QAEF,KAAK;UACHuW,UAAA,GAAatW,uBAAA;UACb;QAEF,KAAK;UACHsW,UAAA,GAAarW,kBAAA;UACb;QAEF,KAAK;UACHqW,UAAA,GAAapW,0BAAA;UACb;QAEF,KAAK;UACHoW,UAAA,GAAanW,iBAAA;UACb;QAEF,KAAK;UAEH8V,UAAA,CAAWhN,QAAA,CAAU;UACrB;QAEF;UACE,MAAM,IAAIuC,KAAA,CACR,iCACEoC,KAAA,GACA,6BACAqI,UAAA,CAAW/M,mBAAA,CAAqB,IAChC,GACH;MACJ;IACF;IAED,IAAIxG,QAAA,GAAW;IAEf,QAAQ4T,UAAA;MACN,KAAKxW,mBAAA;QACH4C,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAKC,SAAA,EAAW;QAAA,CAAG;QAClF;MAEF,KAAK9W,uBAAA;QAEH0C,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAKC,SAAA,EAAW;QAAA,CAAM;QACrF;MAEF,KAAK/W,kBAAA;QAEH2C,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAGC,SAAA,EAAW;QAAA,CAAG;QAChF;MAEF,KAAK7W,kBAAA;QAEHyC,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAKC,SAAA,EAAW;QAAA,CAAG;QAClF;MAEF,KAAK5W,0BAAA;QAEHwC,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAKC,SAAA,EAAW;QAAA,CAAK;QACpF;MAEF,KAAK3W,iBAAA;QAEHuC,QAAA,GAAW,IAAIkU,oBAAA,CAAqB;UAAExU,KAAA;UAAcyU,SAAA,EAAW;UAAKC,SAAA,EAAW;QAAA,CAAM;QACrF;IAKH;IAEDpU,QAAA,CAASqU,WAAA,GAAcX,aAAA;IACvB1T,QAAA,CAASsU,kBAAA,GAAqB;IAC9BtU,QAAA,CAASd,OAAA,GAAUuU,KAAA;IACnBzT,QAAA,CAASuU,UAAA,GAAa,CAACb,aAAA;IAEvB1T,QAAA,CAASwU,aAAA,GAAgB;IACzBxU,QAAA,CAASyU,mBAAA,GAAsB;IAE/B,IAAId,SAAA,KAAc,GAAG;MACnB3T,QAAA,CAAS0U,QAAA,CAASjV,GAAA,CAAIO,QAAA,CAASN,KAAK,EAAEiV,cAAA,CAAehB,SAAS;IAC/D;IAED,IAAI,CAAC5C,YAAA,EAAc;MAEjBA,YAAA,GAAe,IAAI6D,iBAAA,CAAkB;QACnClV,KAAA,EAAO8T,SAAA;QACPa,WAAA,EAAaX,aAAA;QACbxU,OAAA,EAASuU,KAAA;QACTc,UAAA,EAAY,CAACb;MACrB,CAAO;MACD3C,YAAA,CAAarG,QAAA,CAASC,IAAA,GAAOA,IAAA;MAC7BoG,YAAA,CAAa3C,IAAA,GAAOA,IAAA,GAAO;MAG3B2C,YAAA,CAAarG,QAAA,CAASsG,uBAAA,GAA0B,IAAI1S,4BAAA,CAA6B;QAC/EQ,GAAA,EAAK;QACLuV,WAAA,EAAaX,aAAA;QACba,UAAA,EAAY,CAACb,aAAA;QACbhU,KAAA,EAAO8T,SAAA;QACPtU,OAAA,EAASuU;MACjB,CAAO;IACF;IAEDzT,QAAA,CAAS0K,QAAA,CAASC,IAAA,GAAOA,IAAA;IACzB3K,QAAA,CAASoO,IAAA,GAAOA,IAAA;IAEhBpO,QAAA,CAAS0K,QAAA,CAASqG,YAAA,GAAeA,YAAA;IAEjC,KAAK0B,WAAA,CAAYzS,QAAQ;IAEzB,OAAOA,QAAA;EACR;EAEDwS,yBAAyBqC,KAAA,EAAO;IAG9B,IAAIC,UAAA,GAAa;IAEjBD,KAAA,CAAM9B,QAAA,CAAUC,CAAA,IAAM;MACpB,IAAIA,CAAA,CAAElF,OAAA,EAAS;QACb,IAAIkF,CAAA,CAAEtI,QAAA,CAASlD,wBAAA,EAA0B;UACvCsN,UAAA;QACD;QAED9B,CAAA,CAAEtI,QAAA,CAASqK,gBAAA,GAAmBD,UAAA;MAC/B;IACP,CAAK;IAEDD,KAAA,CAAMnK,QAAA,CAASsK,oBAAA,GAAuBF,UAAA,GAAa;EACpD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}