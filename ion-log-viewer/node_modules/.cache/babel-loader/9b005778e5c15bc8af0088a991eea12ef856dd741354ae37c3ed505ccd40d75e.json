{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { OrthographicCamera, PlaneGeometry, Mesh } from \"three\";\nclass Pass {\n  constructor() {\n    // if set to true, the pass is processed by the composer\n    __publicField(this, \"enabled\", true);\n    // if set to true, the pass indicates to swap read and write buffer after rendering\n    __publicField(this, \"needsSwap\", true);\n    // if set to true, the pass clears its buffer before rendering\n    __publicField(this, \"clear\", false);\n    // if set to true, the result of the pass is rendered to screen. This is set automatically by EffectComposer.\n    __publicField(this, \"renderToScreen\", false);\n  }\n  setSize(width, height) {}\n  render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n    console.error(\"THREE.Pass: .render() must be implemented in derived pass.\");\n  }\n  dispose() {}\n}\nclass FullScreenQuad {\n  constructor(material) {\n    __publicField(this, \"camera\", new OrthographicCamera(-1, 1, 1, -1, 0, 1));\n    __publicField(this, \"geometry\", new PlaneGeometry(2, 2));\n    __publicField(this, \"mesh\");\n    this.mesh = new Mesh(this.geometry, material);\n  }\n  get material() {\n    return this.mesh.material;\n  }\n  set material(value) {\n    this.mesh.material = value;\n  }\n  dispose() {\n    this.mesh.geometry.dispose();\n  }\n  render(renderer) {\n    renderer.render(this.mesh, this.camera);\n  }\n}\nexport { FullScreenQuad, Pass };", "map": {"version": 3, "names": ["Pass", "constructor", "__publicField", "setSize", "width", "height", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "maskActive", "console", "error", "dispose", "FullScreenQuad", "material", "OrthographicCamera", "PlaneGeometry", "mesh", "<PERSON><PERSON>", "geometry", "value", "camera"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/postprocessing/Pass.ts"], "sourcesContent": ["import { OrthographicCamera, PlaneGeometry, Mesh, Material, WebGLRenderer, WebGLRenderTarget } from 'three'\n\nclass Pass {\n  // if set to true, the pass is processed by the composer\n  public enabled = true\n\n  // if set to true, the pass indicates to swap read and write buffer after rendering\n  public needsSwap = true\n\n  // if set to true, the pass clears its buffer before rendering\n  public clear = false\n\n  // if set to true, the result of the pass is rendered to screen. This is set automatically by EffectComposer.\n  public renderToScreen = false\n\n  public setSize(width: number, height: number): void {}\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive?: unknown,\n  ): void {\n    console.error('THREE.Pass: .render() must be implemented in derived pass.')\n  }\n\n  public dispose() {}\n}\n\n// Helper for passes that need to fill the viewport with a single quad.\nclass FullScreenQuad<TMaterial extends Material = Material> {\n  public camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n  public geometry = new PlaneGeometry(2, 2)\n  private mesh: Mesh<PlaneGeometry, TMaterial>\n\n  constructor(material: TMaterial) {\n    this.mesh = new Mesh(this.geometry, material)\n  }\n\n  public get material(): TMaterial {\n    return this.mesh.material\n  }\n\n  public set material(value: TMaterial) {\n    this.mesh.material = value\n  }\n\n  public dispose(): void {\n    this.mesh.geometry.dispose()\n  }\n\n  public render(renderer: WebGLRenderer): void {\n    renderer.render(this.mesh, this.camera)\n  }\n}\n\nexport { Pass, FullScreenQuad }\n"], "mappings": ";;;;;;;;;;;;AAEA,MAAMA,IAAA,CAAK;EAAXC,YAAA;IAES;IAAAC,aAAA,kBAAU;IAGV;IAAAA,aAAA,oBAAY;IAGZ;IAAAA,aAAA,gBAAQ;IAGR;IAAAA,aAAA,yBAAiB;EAAA;EAEjBC,QAAQC,KAAA,EAAeC,MAAA,EAAsB,CAAC;EAE9CC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACAC,SAAA,EACAC,UAAA,EACM;IACNC,OAAA,CAAQC,KAAA,CAAM,4DAA4D;EAC5E;EAEOC,QAAA,EAAU,CAAC;AACpB;AAGA,MAAMC,cAAA,CAAsD;EAK1Dd,YAAYe,QAAA,EAAqB;IAJ1Bd,aAAA,iBAAS,IAAIe,kBAAA,CAAmB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;IAClDf,aAAA,mBAAW,IAAIgB,aAAA,CAAc,GAAG,CAAC;IAChChB,aAAA;IAGN,KAAKiB,IAAA,GAAO,IAAIC,IAAA,CAAK,KAAKC,QAAA,EAAUL,QAAQ;EAC9C;EAEA,IAAWA,SAAA,EAAsB;IAC/B,OAAO,KAAKG,IAAA,CAAKH,QAAA;EACnB;EAEA,IAAWA,SAASM,KAAA,EAAkB;IACpC,KAAKH,IAAA,CAAKH,QAAA,GAAWM,KAAA;EACvB;EAEOR,QAAA,EAAgB;IAChB,KAAAK,IAAA,CAAKE,QAAA,CAASP,OAAA;EACrB;EAEOR,OAAOC,QAAA,EAA+B;IAC3CA,QAAA,CAASD,MAAA,CAAO,KAAKa,IAAA,EAAM,KAAKI,MAAM;EACxC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}