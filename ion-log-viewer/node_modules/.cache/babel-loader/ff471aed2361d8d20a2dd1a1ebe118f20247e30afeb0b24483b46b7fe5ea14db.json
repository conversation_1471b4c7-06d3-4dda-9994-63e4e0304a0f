{"ast": null, "code": "/*!\n * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class SymbolToken {\n  constructor(text, sid = SymbolToken._UNKNOWN_SYMBOL_ID) {\n    this.text = text;\n    this.sid = sid;\n  }\n  getText() {\n    return this.text;\n  }\n  getSid() {\n    return this.sid;\n  }\n}\nSymbolToken._UNKNOWN_SYMBOL_ID = -1;", "map": {"version": 3, "names": ["SymbolToken", "constructor", "text", "sid", "_UNKNOWN_SYMBOL_ID", "getText", "getSid"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/IonSymbolToken.js"], "sourcesContent": ["/*!\n * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class SymbolToken {\n    constructor(text, sid = SymbolToken._UNKNOWN_SYMBOL_ID) {\n        this.text = text;\n        this.sid = sid;\n    }\n    getText() {\n        return this.text;\n    }\n    getSid() {\n        return this.sid;\n    }\n}\nSymbolToken._UNKNOWN_SYMBOL_ID = -1;\n//# sourceMappingURL=IonSymbolToken.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,IAAI,EAAEC,GAAG,GAAGH,WAAW,CAACI,kBAAkB,EAAE;IACpD,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACH,IAAI;EACpB;EACAI,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACH,GAAG;EACnB;AACJ;AACAH,WAAW,CAACI,kBAAkB,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}