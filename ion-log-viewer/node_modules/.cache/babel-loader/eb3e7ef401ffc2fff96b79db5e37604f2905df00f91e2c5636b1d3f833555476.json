{"ast": null, "code": "/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class BigIntSerde {\n  static toSignedIntBytes(value, isNegative) {\n    let bytes = this.toUnsignedIntBytes(value);\n    if (bytes[0] >= 128) {\n      const extendedBytes = new Uint8Array(bytes.length + 1);\n      extendedBytes.set(bytes, 1);\n      bytes = extendedBytes;\n    }\n    if (isNegative) {\n      bytes[0] += 0x80;\n    }\n    return bytes;\n  }\n  static fromUnsignedBytes(bytes) {\n    let magnitude = 0n;\n    for (let m = 0; m < bytes.length; m++) {\n      const byte = BigInt(bytes[m]);\n      magnitude = magnitude << this.BITS_PER_BYTE;\n      magnitude = magnitude | byte;\n    }\n    return magnitude;\n  }\n  static toUnsignedIntBytes(value) {\n    if (value < 0n) {\n      value = -value;\n    }\n    const sizeInBytes = this.getUnsignedIntSizeInBytes(value);\n    const bytes = new Uint8Array(sizeInBytes);\n    for (let m = sizeInBytes - 1; m >= 0; m--) {\n      const lastByte = Number(value & this.BYTE_MAX_VALUE);\n      value = value >> this.BITS_PER_BYTE;\n      bytes[m] = lastByte;\n    }\n    return bytes;\n  }\n  static getUnsignedIntSizeInBytes(value) {\n    for (let m = 0; m < this.SIZE_THRESHOLDS.length; m++) {\n      const threshold = this.SIZE_THRESHOLDS[m];\n      if (value <= threshold) {\n        return m + 1;\n      }\n    }\n    let sizeInBytes = this.SIZE_THRESHOLDS.length;\n    let threshold = this.calculateSizeThreshold(sizeInBytes);\n    while (value > threshold) {\n      sizeInBytes++;\n      threshold = this.calculateSizeThreshold(sizeInBytes);\n    }\n    return sizeInBytes;\n  }\n  static calculateSizeThresholds() {\n    const thresholds = [];\n    for (let m = 1; m <= this.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE; m++) {\n      thresholds.push(this.calculateSizeThreshold(m));\n    }\n    return thresholds;\n  }\n  static calculateSizeThreshold(numberOfBytes) {\n    const exponent = BigInt(numberOfBytes) * this.BITS_PER_BYTE;\n    const threshold = 2n ** exponent;\n    return threshold - 1n;\n  }\n}\nBigIntSerde.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE = 64;\nBigIntSerde.BITS_PER_BYTE = 8n;\nBigIntSerde.BYTE_MAX_VALUE = BigInt(0xff);\nBigIntSerde.SIZE_THRESHOLDS = BigIntSerde.calculateSizeThresholds();", "map": {"version": 3, "names": ["BigIntSerde", "toSignedIntBytes", "value", "isNegative", "bytes", "toUnsignedIntBytes", "extendedBytes", "Uint8Array", "length", "set", "fromUnsignedBytes", "magnitude", "m", "byte", "BigInt", "BITS_PER_BYTE", "sizeInBytes", "getUnsignedIntSizeInBytes", "lastByte", "Number", "BYTE_MAX_VALUE", "SIZE_THRESHOLDS", "threshold", "calculateSizeThreshold", "calculateSizeThresholds", "thresholds", "SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE", "push", "numberOfBytes", "exponent"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/ion-js/dist/es6/es6/BigIntSerde.js"], "sourcesContent": ["/*!\n * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\").\n * You may not use this file except in compliance with the License.\n * A copy of the License is located at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed\n * on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either\n * express or implied. See the License for the specific language governing\n * permissions and limitations under the License.\n */\nexport class BigIntSerde {\n    static toSignedIntBytes(value, isNegative) {\n        let bytes = this.toUnsignedIntBytes(value);\n        if (bytes[0] >= 128) {\n            const extendedBytes = new Uint8Array(bytes.length + 1);\n            extendedBytes.set(bytes, 1);\n            bytes = extendedBytes;\n        }\n        if (isNegative) {\n            bytes[0] += 0x80;\n        }\n        return bytes;\n    }\n    static fromUnsignedBytes(bytes) {\n        let magnitude = 0n;\n        for (let m = 0; m < bytes.length; m++) {\n            const byte = BigInt(bytes[m]);\n            magnitude = magnitude << this.BITS_PER_BYTE;\n            magnitude = magnitude | byte;\n        }\n        return magnitude;\n    }\n    static toUnsignedIntBytes(value) {\n        if (value < 0n) {\n            value = -value;\n        }\n        const sizeInBytes = this.getUnsignedIntSizeInBytes(value);\n        const bytes = new Uint8Array(sizeInBytes);\n        for (let m = sizeInBytes - 1; m >= 0; m--) {\n            const lastByte = Number(value & this.BYTE_MAX_VALUE);\n            value = value >> this.BITS_PER_BYTE;\n            bytes[m] = lastByte;\n        }\n        return bytes;\n    }\n    static getUnsignedIntSizeInBytes(value) {\n        for (let m = 0; m < this.SIZE_THRESHOLDS.length; m++) {\n            const threshold = this.SIZE_THRESHOLDS[m];\n            if (value <= threshold) {\n                return m + 1;\n            }\n        }\n        let sizeInBytes = this.SIZE_THRESHOLDS.length;\n        let threshold = this.calculateSizeThreshold(sizeInBytes);\n        while (value > threshold) {\n            sizeInBytes++;\n            threshold = this.calculateSizeThreshold(sizeInBytes);\n        }\n        return sizeInBytes;\n    }\n    static calculateSizeThresholds() {\n        const thresholds = [];\n        for (let m = 1; m <= this.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE; m++) {\n            thresholds.push(this.calculateSizeThreshold(m));\n        }\n        return thresholds;\n    }\n    static calculateSizeThreshold(numberOfBytes) {\n        const exponent = BigInt(numberOfBytes) * this.BITS_PER_BYTE;\n        const threshold = 2n ** exponent;\n        return threshold - 1n;\n    }\n}\nBigIntSerde.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE = 64;\nBigIntSerde.BITS_PER_BYTE = 8n;\nBigIntSerde.BYTE_MAX_VALUE = BigInt(0xff);\nBigIntSerde.SIZE_THRESHOLDS = BigIntSerde.calculateSizeThresholds();\n//# sourceMappingURL=BigIntSerde.js.map"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrB,OAAOC,gBAAgBA,CAACC,KAAK,EAAEC,UAAU,EAAE;IACvC,IAAIC,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAACH,KAAK,CAAC;IAC1C,IAAIE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACjB,MAAME,aAAa,GAAG,IAAIC,UAAU,CAACH,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;MACtDF,aAAa,CAACG,GAAG,CAACL,KAAK,EAAE,CAAC,CAAC;MAC3BA,KAAK,GAAGE,aAAa;IACzB;IACA,IAAIH,UAAU,EAAE;MACZC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IACpB;IACA,OAAOA,KAAK;EAChB;EACA,OAAOM,iBAAiBA,CAACN,KAAK,EAAE;IAC5B,IAAIO,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACI,MAAM,EAAEI,CAAC,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAGC,MAAM,CAACV,KAAK,CAACQ,CAAC,CAAC,CAAC;MAC7BD,SAAS,GAAGA,SAAS,IAAI,IAAI,CAACI,aAAa;MAC3CJ,SAAS,GAAGA,SAAS,GAAGE,IAAI;IAChC;IACA,OAAOF,SAAS;EACpB;EACA,OAAON,kBAAkBA,CAACH,KAAK,EAAE;IAC7B,IAAIA,KAAK,GAAG,EAAE,EAAE;MACZA,KAAK,GAAG,CAACA,KAAK;IAClB;IACA,MAAMc,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAACf,KAAK,CAAC;IACzD,MAAME,KAAK,GAAG,IAAIG,UAAU,CAACS,WAAW,CAAC;IACzC,KAAK,IAAIJ,CAAC,GAAGI,WAAW,GAAG,CAAC,EAAEJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvC,MAAMM,QAAQ,GAAGC,MAAM,CAACjB,KAAK,GAAG,IAAI,CAACkB,cAAc,CAAC;MACpDlB,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACa,aAAa;MACnCX,KAAK,CAACQ,CAAC,CAAC,GAAGM,QAAQ;IACvB;IACA,OAAOd,KAAK;EAChB;EACA,OAAOa,yBAAyBA,CAACf,KAAK,EAAE;IACpC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACS,eAAe,CAACb,MAAM,EAAEI,CAAC,EAAE,EAAE;MAClD,MAAMU,SAAS,GAAG,IAAI,CAACD,eAAe,CAACT,CAAC,CAAC;MACzC,IAAIV,KAAK,IAAIoB,SAAS,EAAE;QACpB,OAAOV,CAAC,GAAG,CAAC;MAChB;IACJ;IACA,IAAII,WAAW,GAAG,IAAI,CAACK,eAAe,CAACb,MAAM;IAC7C,IAAIc,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAACP,WAAW,CAAC;IACxD,OAAOd,KAAK,GAAGoB,SAAS,EAAE;MACtBN,WAAW,EAAE;MACbM,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAACP,WAAW,CAAC;IACxD;IACA,OAAOA,WAAW;EACtB;EACA,OAAOQ,uBAAuBA,CAAA,EAAG;IAC7B,MAAMC,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACc,qCAAqC,EAAEd,CAAC,EAAE,EAAE;MAClEa,UAAU,CAACE,IAAI,CAAC,IAAI,CAACJ,sBAAsB,CAACX,CAAC,CAAC,CAAC;IACnD;IACA,OAAOa,UAAU;EACrB;EACA,OAAOF,sBAAsBA,CAACK,aAAa,EAAE;IACzC,MAAMC,QAAQ,GAAGf,MAAM,CAACc,aAAa,CAAC,GAAG,IAAI,CAACb,aAAa;IAC3D,MAAMO,SAAS,GAAG,EAAE,IAAIO,QAAQ;IAChC,OAAOP,SAAS,GAAG,EAAE;EACzB;AACJ;AACAtB,WAAW,CAAC0B,qCAAqC,GAAG,EAAE;AACtD1B,WAAW,CAACe,aAAa,GAAG,EAAE;AAC9Bf,WAAW,CAACoB,cAAc,GAAGN,MAAM,CAAC,IAAI,CAAC;AACzCd,WAAW,CAACqB,eAAe,GAAGrB,WAAW,CAACwB,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}