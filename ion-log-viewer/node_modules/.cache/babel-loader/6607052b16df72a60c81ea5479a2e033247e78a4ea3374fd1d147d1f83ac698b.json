{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector3, Color, Vector2, BufferGeometry, Float32BufferAttribute } from \"three\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nclass TessellateModifier {\n  constructor(maxEdgeLength = 0.1, maxIterations = 6) {\n    __publicField(this, \"maxEdgeLength\");\n    __publicField(this, \"maxIterations\");\n    __publicField(this, \"modify\", geometry => {\n      if (geometry.index !== null) {\n        geometry = geometry.toNonIndexed();\n      }\n      const maxIterations = this.maxIterations;\n      const maxEdgeLengthSquared = this.maxEdgeLength * this.maxEdgeLength;\n      const va = new Vector3();\n      const vb = new Vector3();\n      const vc = new Vector3();\n      const vm = new Vector3();\n      const vs = [va, vb, vc, vm];\n      const na = new Vector3();\n      const nb = new Vector3();\n      const nc = new Vector3();\n      const nm = new Vector3();\n      const ns = [na, nb, nc, nm];\n      const ca = new Color();\n      const cb = new Color();\n      const cc = new Color();\n      const cm = new Color();\n      const cs = [ca, cb, cc, cm];\n      const ua = new Vector2();\n      const ub = new Vector2();\n      const uc = new Vector2();\n      const um = new Vector2();\n      const us = [ua, ub, uc, um];\n      const u2a = new Vector2();\n      const u2b = new Vector2();\n      const u2c = new Vector2();\n      const u2m = new Vector2();\n      const u2s = [u2a, u2b, u2c, u2m];\n      const attributes = geometry.attributes;\n      const hasNormals = attributes.normal !== void 0;\n      const hasColors = attributes.color !== void 0;\n      const hasUVs = attributes.uv !== void 0;\n      const hasUV1s = attributes[UV1] !== void 0;\n      let positions = attributes.position.array;\n      let normals = hasNormals ? attributes.normal.array : null;\n      let colors = hasColors ? attributes.color.array : null;\n      let uvs = hasUVs ? attributes.uv.array : null;\n      let uv1s = hasUV1s ? attributes.uv1.array : null;\n      let positions2 = positions;\n      let normals2 = normals;\n      let colors2 = colors;\n      let uvs2 = uvs;\n      let uv1s2 = uv1s;\n      let iteration = 0;\n      let tessellating = true;\n      function addTriangle(a, b, c) {\n        const v1 = vs[a];\n        const v2 = vs[b];\n        const v3 = vs[c];\n        positions2.push(v1.x, v1.y, v1.z);\n        positions2.push(v2.x, v2.y, v2.z);\n        positions2.push(v3.x, v3.y, v3.z);\n        if (hasNormals) {\n          const n1 = ns[a];\n          const n2 = ns[b];\n          const n3 = ns[c];\n          normals2.push(n1.x, n1.y, n1.z);\n          normals2.push(n2.x, n2.y, n2.z);\n          normals2.push(n3.x, n3.y, n3.z);\n        }\n        if (hasColors) {\n          const c1 = cs[a];\n          const c2 = cs[b];\n          const c3 = cs[c];\n          colors2.push(c1.r, c1.g, c1.b);\n          colors2.push(c2.r, c2.g, c2.b);\n          colors2.push(c3.r, c3.g, c3.b);\n        }\n        if (hasUVs) {\n          const u1 = us[a];\n          const u2 = us[b];\n          const u3 = us[c];\n          uvs2.push(u1.x, u1.y);\n          uvs2.push(u2.x, u2.y);\n          uvs2.push(u3.x, u3.y);\n        }\n        if (hasUV1s) {\n          const u21 = u2s[a];\n          const u22 = u2s[b];\n          const u23 = u2s[c];\n          uv1s2.push(u21.x, u21.y);\n          uv1s2.push(u22.x, u22.y);\n          uv1s2.push(u23.x, u23.y);\n        }\n      }\n      while (tessellating && iteration < maxIterations) {\n        iteration++;\n        tessellating = false;\n        positions = positions2;\n        positions2 = [];\n        if (hasNormals) {\n          normals = normals2;\n          normals2 = [];\n        }\n        if (hasColors) {\n          colors = colors2;\n          colors2 = [];\n        }\n        if (hasUVs) {\n          uvs = uvs2;\n          uvs2 = [];\n        }\n        if (hasUV1s) {\n          uv1s = uv1s2;\n          uv1s2 = [];\n        }\n        for (let i = 0, i2 = 0, il = positions.length; i < il; i += 9, i2 += 6) {\n          va.fromArray(positions, i + 0);\n          vb.fromArray(positions, i + 3);\n          vc.fromArray(positions, i + 6);\n          if (hasNormals && normals) {\n            na.fromArray(normals, i + 0);\n            nb.fromArray(normals, i + 3);\n            nc.fromArray(normals, i + 6);\n          }\n          if (hasColors && colors) {\n            ca.fromArray(colors, i + 0);\n            cb.fromArray(colors, i + 3);\n            cc.fromArray(colors, i + 6);\n          }\n          if (hasUVs && uvs) {\n            ua.fromArray(uvs, i2 + 0);\n            ub.fromArray(uvs, i2 + 2);\n            uc.fromArray(uvs, i2 + 4);\n          }\n          if (hasUV1s && uv1s) {\n            u2a.fromArray(uv1s, i2 + 0);\n            u2b.fromArray(uv1s, i2 + 2);\n            u2c.fromArray(uv1s, i2 + 4);\n          }\n          const dab = va.distanceToSquared(vb);\n          const dbc = vb.distanceToSquared(vc);\n          const dac = va.distanceToSquared(vc);\n          if (dab > maxEdgeLengthSquared || dbc > maxEdgeLengthSquared || dac > maxEdgeLengthSquared) {\n            tessellating = true;\n            if (dab >= dbc && dab >= dac) {\n              vm.lerpVectors(va, vb, 0.5);\n              if (hasNormals) nm.lerpVectors(na, nb, 0.5);\n              if (hasColors) cm.lerpColors(ca, cb, 0.5);\n              if (hasUVs) um.lerpVectors(ua, ub, 0.5);\n              if (hasUV1s) u2m.lerpVectors(u2a, u2b, 0.5);\n              addTriangle(0, 3, 2);\n              addTriangle(3, 1, 2);\n            } else if (dbc >= dab && dbc >= dac) {\n              vm.lerpVectors(vb, vc, 0.5);\n              if (hasNormals) nm.lerpVectors(nb, nc, 0.5);\n              if (hasColors) cm.lerpColors(cb, cc, 0.5);\n              if (hasUVs) um.lerpVectors(ub, uc, 0.5);\n              if (hasUV1s) u2m.lerpVectors(u2b, u2c, 0.5);\n              addTriangle(0, 1, 3);\n              addTriangle(3, 2, 0);\n            } else {\n              vm.lerpVectors(va, vc, 0.5);\n              if (hasNormals) nm.lerpVectors(na, nc, 0.5);\n              if (hasColors) cm.lerpColors(ca, cc, 0.5);\n              if (hasUVs) um.lerpVectors(ua, uc, 0.5);\n              if (hasUV1s) u2m.lerpVectors(u2a, u2c, 0.5);\n              addTriangle(0, 1, 3);\n              addTriangle(3, 1, 2);\n            }\n          } else {\n            addTriangle(0, 1, 2);\n          }\n        }\n      }\n      const geometry2 = new BufferGeometry();\n      geometry2.setAttribute(\"position\", new Float32BufferAttribute(positions2, 3));\n      if (hasNormals) {\n        geometry2.setAttribute(\"normal\", new Float32BufferAttribute(normals2, 3));\n      }\n      if (hasColors) {\n        geometry2.setAttribute(\"color\", new Float32BufferAttribute(colors2, 3));\n      }\n      if (hasUVs) {\n        geometry2.setAttribute(\"uv\", new Float32BufferAttribute(uvs2, 2));\n      }\n      if (hasUV1s) {\n        geometry2.setAttribute(UV1, new Float32BufferAttribute(uv1s2, 2));\n      }\n      return geometry2;\n    });\n    this.maxEdgeLength = maxEdgeLength;\n    this.maxIterations = maxIterations;\n  }\n}\nexport { TessellateModifier };", "map": {"version": 3, "names": ["TessellateModifier", "constructor", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxIterations", "__publicField", "geometry", "index", "toNonIndexed", "maxEdgeLengthSquared", "va", "Vector3", "vb", "vc", "vm", "vs", "na", "nb", "nc", "nm", "ns", "ca", "Color", "cb", "cc", "cm", "cs", "ua", "Vector2", "ub", "uc", "um", "us", "u2a", "u2b", "u2c", "u2m", "u2s", "attributes", "hasNormals", "normal", "hasColors", "color", "hasUVs", "uv", "hasUV1s", "UV1", "positions", "position", "array", "normals", "colors", "uvs", "uv1s", "uv1", "positions2", "normals2", "colors2", "uvs2", "uv1s2", "iteration", "tessellating", "addTriangle", "a", "b", "c", "v1", "v2", "v3", "push", "x", "y", "z", "n1", "n2", "n3", "c1", "c2", "c3", "r", "g", "u1", "u2", "u3", "u21", "u22", "u23", "i", "i2", "il", "length", "fromArray", "dab", "distanceToSquared", "dbc", "dac", "lerpVectors", "lerpColors", "geometry2", "BufferGeometry", "setAttribute", "Float32BufferAttribute"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/modifiers/TessellateModifier.ts"], "sourcesContent": ["import { BufferGeometry, Color, Float32BufferAttribute, Vector2, Vector3 } from 'three'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * Break faces with edges longer than maxEdgeLength\n */\n\nclass TessellateModifier {\n  public maxEdgeLength: number\n  public maxIterations: number\n\n  constructor(maxEdgeLength = 0.1, maxIterations = 6) {\n    this.maxEdgeLength = maxEdgeLength\n    this.maxIterations = maxIterations\n  }\n\n  public modify = (geometry: BufferGeometry): BufferGeometry => {\n    if (geometry.index !== null) {\n      geometry = geometry.toNonIndexed()\n    }\n\n    //\n\n    const maxIterations = this.maxIterations\n    const maxEdgeLengthSquared = this.maxEdgeLength * this.maxEdgeLength\n\n    const va = new Vector3()\n    const vb = new Vector3()\n    const vc = new Vector3()\n    const vm = new Vector3()\n    const vs = [va, vb, vc, vm]\n\n    const na = new Vector3()\n    const nb = new Vector3()\n    const nc = new Vector3()\n    const nm = new Vector3()\n    const ns = [na, nb, nc, nm]\n\n    const ca = new Color()\n    const cb = new Color()\n    const cc = new Color()\n    const cm = new Color()\n    const cs = [ca, cb, cc, cm]\n\n    const ua = new Vector2()\n    const ub = new Vector2()\n    const uc = new Vector2()\n    const um = new Vector2()\n    const us = [ua, ub, uc, um]\n\n    const u2a = new Vector2()\n    const u2b = new Vector2()\n    const u2c = new Vector2()\n    const u2m = new Vector2()\n    const u2s = [u2a, u2b, u2c, u2m]\n\n    const attributes = geometry.attributes\n    const hasNormals = attributes.normal !== undefined\n    const hasColors = attributes.color !== undefined\n    const hasUVs = attributes.uv !== undefined\n    const hasUV1s = attributes[UV1] !== undefined\n\n    let positions = attributes.position.array\n    let normals = hasNormals ? attributes.normal.array : null\n    let colors = hasColors ? attributes.color.array : null\n    let uvs = hasUVs ? attributes.uv.array : null\n    let uv1s = hasUV1s ? attributes.uv1.array : null\n\n    let positions2 = (positions as unknown) as number[]\n    let normals2 = (normals as unknown) as number[]\n    let colors2 = (colors as unknown) as number[]\n    let uvs2 = (uvs as unknown) as number[]\n    let uv1s2 = (uv1s as unknown) as number[]\n\n    let iteration = 0\n    let tessellating = true\n\n    function addTriangle(a: number, b: number, c: number): void {\n      const v1 = vs[a]\n      const v2 = vs[b]\n      const v3 = vs[c]\n\n      positions2.push(v1.x, v1.y, v1.z)\n      positions2.push(v2.x, v2.y, v2.z)\n      positions2.push(v3.x, v3.y, v3.z)\n\n      if (hasNormals) {\n        const n1 = ns[a]\n        const n2 = ns[b]\n        const n3 = ns[c]\n\n        normals2.push(n1.x, n1.y, n1.z)\n        normals2.push(n2.x, n2.y, n2.z)\n        normals2.push(n3.x, n3.y, n3.z)\n      }\n\n      if (hasColors) {\n        const c1 = cs[a]\n        const c2 = cs[b]\n        const c3 = cs[c]\n\n        colors2.push(c1.r, c1.g, c1.b)\n        colors2.push(c2.r, c2.g, c2.b)\n        colors2.push(c3.r, c3.g, c3.b)\n      }\n\n      if (hasUVs) {\n        const u1 = us[a]\n        const u2 = us[b]\n        const u3 = us[c]\n\n        uvs2.push(u1.x, u1.y)\n        uvs2.push(u2.x, u2.y)\n        uvs2.push(u3.x, u3.y)\n      }\n\n      if (hasUV1s) {\n        const u21 = u2s[a]\n        const u22 = u2s[b]\n        const u23 = u2s[c]\n\n        uv1s2.push(u21.x, u21.y)\n        uv1s2.push(u22.x, u22.y)\n        uv1s2.push(u23.x, u23.y)\n      }\n    }\n\n    while (tessellating && iteration < maxIterations) {\n      iteration++\n      tessellating = false\n\n      positions = positions2 as any\n      positions2 = []\n\n      if (hasNormals) {\n        normals = normals2 as any\n        normals2 = []\n      }\n\n      if (hasColors) {\n        colors = colors2 as any\n        colors2 = []\n      }\n\n      if (hasUVs) {\n        uvs = uvs2 as any\n        uvs2 = []\n      }\n\n      if (hasUV1s) {\n        uv1s = uv1s2 as any\n        uv1s2 = []\n      }\n\n      for (let i = 0, i2 = 0, il = positions.length; i < il; i += 9, i2 += 6) {\n        va.fromArray(positions, i + 0)\n        vb.fromArray(positions, i + 3)\n        vc.fromArray(positions, i + 6)\n\n        if (hasNormals && normals) {\n          na.fromArray(normals, i + 0)\n          nb.fromArray(normals, i + 3)\n          nc.fromArray(normals, i + 6)\n        }\n\n        if (hasColors && colors) {\n          ca.fromArray(colors, i + 0)\n          cb.fromArray(colors, i + 3)\n          cc.fromArray(colors, i + 6)\n        }\n\n        if (hasUVs && uvs) {\n          ua.fromArray(uvs, i2 + 0)\n          ub.fromArray(uvs, i2 + 2)\n          uc.fromArray(uvs, i2 + 4)\n        }\n\n        if (hasUV1s && uv1s) {\n          u2a.fromArray(uv1s, i2 + 0)\n          u2b.fromArray(uv1s, i2 + 2)\n          u2c.fromArray(uv1s, i2 + 4)\n        }\n\n        const dab = va.distanceToSquared(vb)\n        const dbc = vb.distanceToSquared(vc)\n        const dac = va.distanceToSquared(vc)\n\n        if (dab > maxEdgeLengthSquared || dbc > maxEdgeLengthSquared || dac > maxEdgeLengthSquared) {\n          tessellating = true\n\n          if (dab >= dbc && dab >= dac) {\n            vm.lerpVectors(va, vb, 0.5)\n            if (hasNormals) nm.lerpVectors(na, nb, 0.5)\n            if (hasColors) cm.lerpColors(ca, cb, 0.5)\n            if (hasUVs) um.lerpVectors(ua, ub, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2a, u2b, 0.5)\n\n            addTriangle(0, 3, 2)\n            addTriangle(3, 1, 2)\n          } else if (dbc >= dab && dbc >= dac) {\n            vm.lerpVectors(vb, vc, 0.5)\n            if (hasNormals) nm.lerpVectors(nb, nc, 0.5)\n            if (hasColors) cm.lerpColors(cb, cc, 0.5)\n            if (hasUVs) um.lerpVectors(ub, uc, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2b, u2c, 0.5)\n\n            addTriangle(0, 1, 3)\n            addTriangle(3, 2, 0)\n          } else {\n            vm.lerpVectors(va, vc, 0.5)\n            if (hasNormals) nm.lerpVectors(na, nc, 0.5)\n            if (hasColors) cm.lerpColors(ca, cc, 0.5)\n            if (hasUVs) um.lerpVectors(ua, uc, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2a, u2c, 0.5)\n\n            addTriangle(0, 1, 3)\n            addTriangle(3, 1, 2)\n          }\n        } else {\n          addTriangle(0, 1, 2)\n        }\n      }\n    }\n\n    const geometry2 = new BufferGeometry()\n\n    geometry2.setAttribute('position', new Float32BufferAttribute(positions2, 3))\n\n    if (hasNormals) {\n      geometry2.setAttribute('normal', new Float32BufferAttribute(normals2 as any, 3))\n    }\n\n    if (hasColors) {\n      geometry2.setAttribute('color', new Float32BufferAttribute(colors2 as any, 3))\n    }\n\n    if (hasUVs) {\n      geometry2.setAttribute('uv', new Float32BufferAttribute(uvs2 as any, 2))\n    }\n\n    if (hasUV1s) {\n      geometry2.setAttribute(UV1, new Float32BufferAttribute(uv1s2 as any, 2))\n    }\n\n    return geometry2\n  }\n}\n\nexport { TessellateModifier }\n"], "mappings": ";;;;;;;;;;;;;AAOA,MAAMA,kBAAA,CAAmB;EAIvBC,YAAYC,aAAA,GAAgB,KAAKC,aAAA,GAAgB,GAAG;IAH7CC,aAAA;IACAA,aAAA;IAOAA,aAAA,iBAAUC,QAAA,IAA6C;MACxD,IAAAA,QAAA,CAASC,KAAA,KAAU,MAAM;QAC3BD,QAAA,GAAWA,QAAA,CAASE,YAAA;MACtB;MAIA,MAAMJ,aAAA,GAAgB,KAAKA,aAAA;MACrB,MAAAK,oBAAA,GAAuB,KAAKN,aAAA,GAAgB,KAAKA,aAAA;MAEjD,MAAAO,EAAA,GAAK,IAAIC,OAAA;MACT,MAAAC,EAAA,GAAK,IAAID,OAAA;MACT,MAAAE,EAAA,GAAK,IAAIF,OAAA;MACT,MAAAG,EAAA,GAAK,IAAIH,OAAA;MACf,MAAMI,EAAA,GAAK,CAACL,EAAA,EAAIE,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAEpB,MAAAE,EAAA,GAAK,IAAIL,OAAA;MACT,MAAAM,EAAA,GAAK,IAAIN,OAAA;MACT,MAAAO,EAAA,GAAK,IAAIP,OAAA;MACT,MAAAQ,EAAA,GAAK,IAAIR,OAAA;MACf,MAAMS,EAAA,GAAK,CAACJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAEpB,MAAAE,EAAA,GAAK,IAAIC,KAAA;MACT,MAAAC,EAAA,GAAK,IAAID,KAAA;MACT,MAAAE,EAAA,GAAK,IAAIF,KAAA;MACT,MAAAG,EAAA,GAAK,IAAIH,KAAA;MACf,MAAMI,EAAA,GAAK,CAACL,EAAA,EAAIE,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAEpB,MAAAE,EAAA,GAAK,IAAIC,OAAA;MACT,MAAAC,EAAA,GAAK,IAAID,OAAA;MACT,MAAAE,EAAA,GAAK,IAAIF,OAAA;MACT,MAAAG,EAAA,GAAK,IAAIH,OAAA;MACf,MAAMI,EAAA,GAAK,CAACL,EAAA,EAAIE,EAAA,EAAIC,EAAA,EAAIC,EAAE;MAEpB,MAAAE,GAAA,GAAM,IAAIL,OAAA;MACV,MAAAM,GAAA,GAAM,IAAIN,OAAA;MACV,MAAAO,GAAA,GAAM,IAAIP,OAAA;MACV,MAAAQ,GAAA,GAAM,IAAIR,OAAA;MAChB,MAAMS,GAAA,GAAM,CAACJ,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG;MAE/B,MAAME,UAAA,GAAahC,QAAA,CAASgC,UAAA;MACtB,MAAAC,UAAA,GAAaD,UAAA,CAAWE,MAAA,KAAW;MACnC,MAAAC,SAAA,GAAYH,UAAA,CAAWI,KAAA,KAAU;MACjC,MAAAC,MAAA,GAASL,UAAA,CAAWM,EAAA,KAAO;MAC3B,MAAAC,OAAA,GAAUP,UAAA,CAAWQ,GAAG,MAAM;MAEhC,IAAAC,SAAA,GAAYT,UAAA,CAAWU,QAAA,CAASC,KAAA;MACpC,IAAIC,OAAA,GAAUX,UAAA,GAAaD,UAAA,CAAWE,MAAA,CAAOS,KAAA,GAAQ;MACrD,IAAIE,MAAA,GAASV,SAAA,GAAYH,UAAA,CAAWI,KAAA,CAAMO,KAAA,GAAQ;MAClD,IAAIG,GAAA,GAAMT,MAAA,GAASL,UAAA,CAAWM,EAAA,CAAGK,KAAA,GAAQ;MACzC,IAAII,IAAA,GAAOR,OAAA,GAAUP,UAAA,CAAWgB,GAAA,CAAIL,KAAA,GAAQ;MAE5C,IAAIM,UAAA,GAAcR,SAAA;MAClB,IAAIS,QAAA,GAAYN,OAAA;MAChB,IAAIO,OAAA,GAAWN,MAAA;MACf,IAAIO,IAAA,GAAQN,GAAA;MACZ,IAAIO,KAAA,GAASN,IAAA;MAEb,IAAIO,SAAA,GAAY;MAChB,IAAIC,YAAA,GAAe;MAEV,SAAAC,YAAYC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAiB;QACpD,MAAAC,EAAA,GAAKnD,EAAA,CAAGgD,CAAC;QACT,MAAAI,EAAA,GAAKpD,EAAA,CAAGiD,CAAC;QACT,MAAAI,EAAA,GAAKrD,EAAA,CAAGkD,CAAC;QAEfV,UAAA,CAAWc,IAAA,CAAKH,EAAA,CAAGI,CAAA,EAAGJ,EAAA,CAAGK,CAAA,EAAGL,EAAA,CAAGM,CAAC;QAChCjB,UAAA,CAAWc,IAAA,CAAKF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAGJ,EAAA,CAAGK,CAAC;QAChCjB,UAAA,CAAWc,IAAA,CAAKD,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAC;QAEhC,IAAIjC,UAAA,EAAY;UACR,MAAAkC,EAAA,GAAKrD,EAAA,CAAG2C,CAAC;UACT,MAAAW,EAAA,GAAKtD,EAAA,CAAG4C,CAAC;UACT,MAAAW,EAAA,GAAKvD,EAAA,CAAG6C,CAAC;UAEfT,QAAA,CAASa,IAAA,CAAKI,EAAA,CAAGH,CAAA,EAAGG,EAAA,CAAGF,CAAA,EAAGE,EAAA,CAAGD,CAAC;UAC9BhB,QAAA,CAASa,IAAA,CAAKK,EAAA,CAAGJ,CAAA,EAAGI,EAAA,CAAGH,CAAA,EAAGG,EAAA,CAAGF,CAAC;UAC9BhB,QAAA,CAASa,IAAA,CAAKM,EAAA,CAAGL,CAAA,EAAGK,EAAA,CAAGJ,CAAA,EAAGI,EAAA,CAAGH,CAAC;QAChC;QAEA,IAAI/B,SAAA,EAAW;UACP,MAAAmC,EAAA,GAAKlD,EAAA,CAAGqC,CAAC;UACT,MAAAc,EAAA,GAAKnD,EAAA,CAAGsC,CAAC;UACT,MAAAc,EAAA,GAAKpD,EAAA,CAAGuC,CAAC;UAEfR,OAAA,CAAQY,IAAA,CAAKO,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGI,CAAA,EAAGJ,EAAA,CAAGZ,CAAC;UAC7BP,OAAA,CAAQY,IAAA,CAAKQ,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGG,CAAA,EAAGH,EAAA,CAAGb,CAAC;UAC7BP,OAAA,CAAQY,IAAA,CAAKS,EAAA,CAAGC,CAAA,EAAGD,EAAA,CAAGE,CAAA,EAAGF,EAAA,CAAGd,CAAC;QAC/B;QAEA,IAAIrB,MAAA,EAAQ;UACJ,MAAAsC,EAAA,GAAKjD,EAAA,CAAG+B,CAAC;UACT,MAAAmB,EAAA,GAAKlD,EAAA,CAAGgC,CAAC;UACT,MAAAmB,EAAA,GAAKnD,EAAA,CAAGiC,CAAC;UAEfP,IAAA,CAAKW,IAAA,CAAKY,EAAA,CAAGX,CAAA,EAAGW,EAAA,CAAGV,CAAC;UACpBb,IAAA,CAAKW,IAAA,CAAKa,EAAA,CAAGZ,CAAA,EAAGY,EAAA,CAAGX,CAAC;UACpBb,IAAA,CAAKW,IAAA,CAAKc,EAAA,CAAGb,CAAA,EAAGa,EAAA,CAAGZ,CAAC;QACtB;QAEA,IAAI1B,OAAA,EAAS;UACL,MAAAuC,GAAA,GAAM/C,GAAA,CAAI0B,CAAC;UACX,MAAAsB,GAAA,GAAMhD,GAAA,CAAI2B,CAAC;UACX,MAAAsB,GAAA,GAAMjD,GAAA,CAAI4B,CAAC;UAEjBN,KAAA,CAAMU,IAAA,CAAKe,GAAA,CAAId,CAAA,EAAGc,GAAA,CAAIb,CAAC;UACvBZ,KAAA,CAAMU,IAAA,CAAKgB,GAAA,CAAIf,CAAA,EAAGe,GAAA,CAAId,CAAC;UACvBZ,KAAA,CAAMU,IAAA,CAAKiB,GAAA,CAAIhB,CAAA,EAAGgB,GAAA,CAAIf,CAAC;QACzB;MACF;MAEO,OAAAV,YAAA,IAAgBD,SAAA,GAAYxD,aAAA,EAAe;QAChDwD,SAAA;QACeC,YAAA;QAEHd,SAAA,GAAAQ,UAAA;QACZA,UAAA,GAAa;QAEb,IAAIhB,UAAA,EAAY;UACJW,OAAA,GAAAM,QAAA;UACVA,QAAA,GAAW;QACb;QAEA,IAAIf,SAAA,EAAW;UACJU,MAAA,GAAAM,OAAA;UACTA,OAAA,GAAU;QACZ;QAEA,IAAId,MAAA,EAAQ;UACJS,GAAA,GAAAM,IAAA;UACNA,IAAA,GAAO;QACT;QAEA,IAAIb,OAAA,EAAS;UACJQ,IAAA,GAAAM,KAAA;UACPA,KAAA,GAAQ;QACV;QAEA,SAAS4B,CAAA,GAAI,GAAGC,EAAA,GAAK,GAAGC,EAAA,GAAK1C,SAAA,CAAU2C,MAAA,EAAQH,CAAA,GAAIE,EAAA,EAAIF,CAAA,IAAK,GAAGC,EAAA,IAAM,GAAG;UACnE9E,EAAA,CAAAiF,SAAA,CAAU5C,SAAA,EAAWwC,CAAA,GAAI,CAAC;UAC1B3E,EAAA,CAAA+E,SAAA,CAAU5C,SAAA,EAAWwC,CAAA,GAAI,CAAC;UAC1B1E,EAAA,CAAA8E,SAAA,CAAU5C,SAAA,EAAWwC,CAAA,GAAI,CAAC;UAE7B,IAAIhD,UAAA,IAAcW,OAAA,EAAS;YACtBlC,EAAA,CAAA2E,SAAA,CAAUzC,OAAA,EAASqC,CAAA,GAAI,CAAC;YACxBtE,EAAA,CAAA0E,SAAA,CAAUzC,OAAA,EAASqC,CAAA,GAAI,CAAC;YACxBrE,EAAA,CAAAyE,SAAA,CAAUzC,OAAA,EAASqC,CAAA,GAAI,CAAC;UAC7B;UAEA,IAAI9C,SAAA,IAAaU,MAAA,EAAQ;YACpB9B,EAAA,CAAAsE,SAAA,CAAUxC,MAAA,EAAQoC,CAAA,GAAI,CAAC;YACvBhE,EAAA,CAAAoE,SAAA,CAAUxC,MAAA,EAAQoC,CAAA,GAAI,CAAC;YACvB/D,EAAA,CAAAmE,SAAA,CAAUxC,MAAA,EAAQoC,CAAA,GAAI,CAAC;UAC5B;UAEA,IAAI5C,MAAA,IAAUS,GAAA,EAAK;YACdzB,EAAA,CAAAgE,SAAA,CAAUvC,GAAA,EAAKoC,EAAA,GAAK,CAAC;YACrB3D,EAAA,CAAA8D,SAAA,CAAUvC,GAAA,EAAKoC,EAAA,GAAK,CAAC;YACrB1D,EAAA,CAAA6D,SAAA,CAAUvC,GAAA,EAAKoC,EAAA,GAAK,CAAC;UAC1B;UAEA,IAAI3C,OAAA,IAAWQ,IAAA,EAAM;YACfpB,GAAA,CAAA0D,SAAA,CAAUtC,IAAA,EAAMmC,EAAA,GAAK,CAAC;YACtBtD,GAAA,CAAAyD,SAAA,CAAUtC,IAAA,EAAMmC,EAAA,GAAK,CAAC;YACtBrD,GAAA,CAAAwD,SAAA,CAAUtC,IAAA,EAAMmC,EAAA,GAAK,CAAC;UAC5B;UAEM,MAAAI,GAAA,GAAMlF,EAAA,CAAGmF,iBAAA,CAAkBjF,EAAE;UAC7B,MAAAkF,GAAA,GAAMlF,EAAA,CAAGiF,iBAAA,CAAkBhF,EAAE;UAC7B,MAAAkF,GAAA,GAAMrF,EAAA,CAAGmF,iBAAA,CAAkBhF,EAAE;UAEnC,IAAI+E,GAAA,GAAMnF,oBAAA,IAAwBqF,GAAA,GAAMrF,oBAAA,IAAwBsF,GAAA,GAAMtF,oBAAA,EAAsB;YAC3EoD,YAAA;YAEX,IAAA+B,GAAA,IAAOE,GAAA,IAAOF,GAAA,IAAOG,GAAA,EAAK;cACzBjF,EAAA,CAAAkF,WAAA,CAAYtF,EAAA,EAAIE,EAAA,EAAI,GAAG;cACtB,IAAA2B,UAAA,EAAepB,EAAA,CAAA6E,WAAA,CAAYhF,EAAA,EAAIC,EAAA,EAAI,GAAG;cACtC,IAAAwB,SAAA,EAAchB,EAAA,CAAAwE,UAAA,CAAW5E,EAAA,EAAIE,EAAA,EAAI,GAAG;cACpC,IAAAoB,MAAA,EAAWZ,EAAA,CAAAiE,WAAA,CAAYrE,EAAA,EAAIE,EAAA,EAAI,GAAG;cAClC,IAAAgB,OAAA,EAAaT,GAAA,CAAA4D,WAAA,CAAY/D,GAAA,EAAKC,GAAA,EAAK,GAAG;cAE9B4B,WAAA,IAAG,GAAG,CAAC;cACPA,WAAA,IAAG,GAAG,CAAC;YACV,WAAAgC,GAAA,IAAOF,GAAA,IAAOE,GAAA,IAAOC,GAAA,EAAK;cAChCjF,EAAA,CAAAkF,WAAA,CAAYpF,EAAA,EAAIC,EAAA,EAAI,GAAG;cACtB,IAAA0B,UAAA,EAAepB,EAAA,CAAA6E,WAAA,CAAY/E,EAAA,EAAIC,EAAA,EAAI,GAAG;cACtC,IAAAuB,SAAA,EAAchB,EAAA,CAAAwE,UAAA,CAAW1E,EAAA,EAAIC,EAAA,EAAI,GAAG;cACpC,IAAAmB,MAAA,EAAWZ,EAAA,CAAAiE,WAAA,CAAYnE,EAAA,EAAIC,EAAA,EAAI,GAAG;cAClC,IAAAe,OAAA,EAAaT,GAAA,CAAA4D,WAAA,CAAY9D,GAAA,EAAKC,GAAA,EAAK,GAAG;cAE9B2B,WAAA,IAAG,GAAG,CAAC;cACPA,WAAA,IAAG,GAAG,CAAC;YAAA,OACd;cACFhD,EAAA,CAAAkF,WAAA,CAAYtF,EAAA,EAAIG,EAAA,EAAI,GAAG;cACtB,IAAA0B,UAAA,EAAepB,EAAA,CAAA6E,WAAA,CAAYhF,EAAA,EAAIE,EAAA,EAAI,GAAG;cACtC,IAAAuB,SAAA,EAAchB,EAAA,CAAAwE,UAAA,CAAW5E,EAAA,EAAIG,EAAA,EAAI,GAAG;cACpC,IAAAmB,MAAA,EAAWZ,EAAA,CAAAiE,WAAA,CAAYrE,EAAA,EAAIG,EAAA,EAAI,GAAG;cAClC,IAAAe,OAAA,EAAaT,GAAA,CAAA4D,WAAA,CAAY/D,GAAA,EAAKE,GAAA,EAAK,GAAG;cAE9B2B,WAAA,IAAG,GAAG,CAAC;cACPA,WAAA,IAAG,GAAG,CAAC;YACrB;UAAA,OACK;YACOA,WAAA,IAAG,GAAG,CAAC;UACrB;QACF;MACF;MAEM,MAAAoC,SAAA,GAAY,IAAIC,cAAA;MAEtBD,SAAA,CAAUE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB9C,UAAA,EAAY,CAAC,CAAC;MAE5E,IAAIhB,UAAA,EAAY;QACd2D,SAAA,CAAUE,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuB7C,QAAA,EAAiB,CAAC,CAAC;MACjF;MAEA,IAAIf,SAAA,EAAW;QACbyD,SAAA,CAAUE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuB5C,OAAA,EAAgB,CAAC,CAAC;MAC/E;MAEA,IAAId,MAAA,EAAQ;QACVuD,SAAA,CAAUE,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuB3C,IAAA,EAAa,CAAC,CAAC;MACzE;MAEA,IAAIb,OAAA,EAAS;QACXqD,SAAA,CAAUE,YAAA,CAAatD,GAAA,EAAK,IAAIuD,sBAAA,CAAuB1C,KAAA,EAAc,CAAC,CAAC;MACzE;MAEO,OAAAuC,SAAA;IAAA;IAxOP,KAAK/F,aAAA,GAAgBA,aAAA;IACrB,KAAKC,aAAA,GAAgBA,aAAA;EACvB;AAwOF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}