{"ast": null, "code": "import * as THREE from \"three\";\nconst PINCH_MAX = 0.05;\nconst PINCH_THRESHOLD = 0.02;\nconst PINCH_MIN = 0.01;\nconst POINTER_ADVANCE_MAX = 0.02;\nconst POINTER_OPACITY_MAX = 1;\nconst POINTER_OPACITY_MIN = 0.4;\nconst POINTER_FRONT_RADIUS = 2e-3;\nconst POINTER_REAR_RADIUS = 0.01;\nconst POINTER_REAR_RADIUS_MIN = 3e-3;\nconst POINTER_LENGTH = 0.035;\nconst POINTER_SEGMENTS = 16;\nconst POINTER_RINGS = 12;\nconst POINTER_HEMISPHERE_ANGLE = 110;\nconst YAXIS = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst ZAXIS = /* @__PURE__ */new THREE.Vector3(0, 0, 1);\nconst CURSOR_RADIUS = 0.02;\nconst CURSOR_MAX_DISTANCE = 1.5;\nclass OculusHandPointerModel extends THREE.Object3D {\n  constructor(hand, controller) {\n    super();\n    this.hand = hand;\n    this.controller = controller;\n    this.motionController = null;\n    this.envMap = null;\n    this.mesh = null;\n    this.pointerGeometry = null;\n    this.pointerMesh = null;\n    this.pointerObject = null;\n    this.pinched = false;\n    this.attached = false;\n    this.cursorObject = null;\n    this.raycaster = null;\n    this._onConnected = this._onConnected.bind(this);\n    this._onDisconnected = this._onDisconnected.bind(this);\n    this.hand.addEventListener(\"connected\", this._onConnected);\n    this.hand.addEventListener(\"disconnected\", this._onDisconnected);\n  }\n  _onConnected(event) {\n    const xrInputSource = event.data;\n    if (xrInputSource.hand) {\n      this.visible = true;\n      this.xrInputSource = xrInputSource;\n      this.createPointer();\n    }\n  }\n  _onDisconnected() {\n    var _a, _b;\n    this.visible = false;\n    this.xrInputSource = null;\n    (_a = this.pointerGeometry) == null ? void 0 : _a.dispose();\n    (_b = this.pointerMesh) == null ? void 0 : _b.material.dispose();\n    this.clear();\n  }\n  _drawVerticesRing(vertices, baseVector, ringIndex) {\n    const segmentVector = baseVector.clone();\n    for (var i = 0; i < POINTER_SEGMENTS; i++) {\n      segmentVector.applyAxisAngle(ZAXIS, Math.PI * 2 / POINTER_SEGMENTS);\n      const vid = ringIndex * POINTER_SEGMENTS + i;\n      vertices[3 * vid] = segmentVector.x;\n      vertices[3 * vid + 1] = segmentVector.y;\n      vertices[3 * vid + 2] = segmentVector.z;\n    }\n  }\n  _updatePointerVertices(rearRadius) {\n    const vertices = this.pointerGeometry.attributes.position.array;\n    const frontFaceBase = new THREE.Vector3(POINTER_FRONT_RADIUS, 0, -1 * (POINTER_LENGTH - rearRadius));\n    this._drawVerticesRing(vertices, frontFaceBase, 0);\n    const rearBase = new THREE.Vector3(Math.sin(Math.PI * POINTER_HEMISPHERE_ANGLE / 180) * rearRadius, Math.cos(Math.PI * POINTER_HEMISPHERE_ANGLE / 180) * rearRadius, 0);\n    for (var i = 0; i < POINTER_RINGS; i++) {\n      this._drawVerticesRing(vertices, rearBase, i + 1);\n      rearBase.applyAxisAngle(YAXIS, Math.PI * POINTER_HEMISPHERE_ANGLE / 180 / (POINTER_RINGS * -2));\n    }\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS);\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1;\n    const frontCenter = new THREE.Vector3(0, 0, -1 * (POINTER_LENGTH - rearRadius));\n    vertices[frontCenterIndex * 3] = frontCenter.x;\n    vertices[frontCenterIndex * 3 + 1] = frontCenter.y;\n    vertices[frontCenterIndex * 3 + 2] = frontCenter.z;\n    const rearCenter = new THREE.Vector3(0, 0, rearRadius);\n    vertices[rearCenterIndex * 3] = rearCenter.x;\n    vertices[rearCenterIndex * 3 + 1] = rearCenter.y;\n    vertices[rearCenterIndex * 3 + 2] = rearCenter.z;\n    this.pointerGeometry.setAttribute(\"position\", new THREE.Float32BufferAttribute(vertices, 3));\n  }\n  createPointer() {\n    var i, j;\n    const vertices = new Array(((POINTER_RINGS + 1) * POINTER_SEGMENTS + 2) * 3).fill(0);\n    const indices = [];\n    this.pointerGeometry = new THREE.BufferGeometry();\n    this.pointerGeometry.setAttribute(\"position\", new THREE.Float32BufferAttribute(vertices, 3));\n    this._updatePointerVertices(POINTER_REAR_RADIUS);\n    for (i = 0; i < POINTER_RINGS; i++) {\n      for (j = 0; j < POINTER_SEGMENTS - 1; j++) {\n        indices.push(i * POINTER_SEGMENTS + j, i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j);\n        indices.push(i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j);\n      }\n      indices.push((i + 1) * POINTER_SEGMENTS - 1, i * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1);\n      indices.push(i * POINTER_SEGMENTS, (i + 1) * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1);\n    }\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS);\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1;\n    for (i = 0; i < POINTER_SEGMENTS - 1; i++) {\n      indices.push(frontCenterIndex, i + 1, i);\n      indices.push(rearCenterIndex, i + POINTER_SEGMENTS * POINTER_RINGS, i + POINTER_SEGMENTS * POINTER_RINGS + 1);\n    }\n    indices.push(frontCenterIndex, 0, POINTER_SEGMENTS - 1);\n    indices.push(rearCenterIndex, POINTER_SEGMENTS * (POINTER_RINGS + 1) - 1, POINTER_SEGMENTS * POINTER_RINGS);\n    const material = new THREE.MeshBasicMaterial();\n    material.transparent = true;\n    material.opacity = POINTER_OPACITY_MIN;\n    this.pointerGeometry.setIndex(indices);\n    this.pointerMesh = new THREE.Mesh(this.pointerGeometry, material);\n    this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS);\n    this.pointerObject = new THREE.Object3D();\n    this.pointerObject.add(this.pointerMesh);\n    this.raycaster = new THREE.Raycaster();\n    const cursorGeometry = new THREE.SphereGeometry(CURSOR_RADIUS, 10, 10);\n    const cursorMaterial = new THREE.MeshBasicMaterial();\n    cursorMaterial.transparent = true;\n    cursorMaterial.opacity = POINTER_OPACITY_MIN;\n    this.cursorObject = new THREE.Mesh(cursorGeometry, cursorMaterial);\n    this.pointerObject.add(this.cursorObject);\n    this.add(this.pointerObject);\n  }\n  _updateRaycaster() {\n    if (this.raycaster) {\n      const pointerMatrix = this.pointerObject.matrixWorld;\n      const tempMatrix = new THREE.Matrix4();\n      tempMatrix.identity().extractRotation(pointerMatrix);\n      this.raycaster.ray.origin.setFromMatrixPosition(pointerMatrix);\n      this.raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix);\n    }\n  }\n  _updatePointer() {\n    this.pointerObject.visible = this.controller.visible;\n    const indexTip = this.hand.joints[\"index-finger-tip\"];\n    const thumbTip = this.hand.joints[\"thumb-tip\"];\n    const distance = indexTip.position.distanceTo(thumbTip.position);\n    const position = indexTip.position.clone().add(thumbTip.position).multiplyScalar(0.5);\n    this.pointerObject.position.copy(position);\n    this.pointerObject.quaternion.copy(this.controller.quaternion);\n    this.pinched = distance <= PINCH_THRESHOLD;\n    const pinchScale = (distance - PINCH_MIN) / (PINCH_MAX - PINCH_MIN);\n    const focusScale = (distance - PINCH_MIN) / (PINCH_THRESHOLD - PINCH_MIN);\n    if (pinchScale > 1) {\n      this._updatePointerVertices(POINTER_REAR_RADIUS);\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS);\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MIN;\n    } else if (pinchScale > 0) {\n      const rearRadius = (POINTER_REAR_RADIUS - POINTER_REAR_RADIUS_MIN) * pinchScale + POINTER_REAR_RADIUS_MIN;\n      this._updatePointerVertices(rearRadius);\n      if (focusScale < 1) {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius - (1 - focusScale) * POINTER_ADVANCE_MAX);\n        this.pointerMesh.material.opacity = POINTER_OPACITY_MIN + (1 - focusScale) * (POINTER_OPACITY_MAX - POINTER_OPACITY_MIN);\n      } else {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius);\n        this.pointerMesh.material.opacity = POINTER_OPACITY_MIN;\n      }\n    } else {\n      this._updatePointerVertices(POINTER_REAR_RADIUS_MIN);\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS_MIN - POINTER_ADVANCE_MAX);\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MAX;\n    }\n    this.cursorObject.material.opacity = this.pointerMesh.material.opacity;\n  }\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force);\n    if (this.pointerGeometry) {\n      this._updatePointer();\n      this._updateRaycaster();\n    }\n  }\n  isPinched() {\n    return this.pinched;\n  }\n  setAttached(attached) {\n    this.attached = attached;\n  }\n  isAttached() {\n    return this.attached;\n  }\n  intersectObject(object, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObject(object, recursive);\n    }\n  }\n  intersectObjects(objects, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObjects(objects, recursive);\n    }\n  }\n  checkIntersections(objects, recursive = false) {\n    if (this.raycaster && !this.attached) {\n      const intersections = this.raycaster.intersectObjects(objects, recursive);\n      const direction = new THREE.Vector3(0, 0, -1);\n      if (intersections.length > 0) {\n        const intersection = intersections[0];\n        const distance = intersection.distance;\n        this.cursorObject.position.copy(direction.multiplyScalar(distance));\n      } else {\n        this.cursorObject.position.copy(direction.multiplyScalar(CURSOR_MAX_DISTANCE));\n      }\n    }\n  }\n  setCursor(distance) {\n    const direction = new THREE.Vector3(0, 0, -1);\n    if (this.raycaster && !this.attached) {\n      this.cursorObject.position.copy(direction.multiplyScalar(distance));\n    }\n  }\n  dispose() {\n    this._onDisconnected();\n    this.hand.removeEventListener(\"connected\", this._onConnected);\n    this.hand.removeEventListener(\"disconnected\", this._onDisconnected);\n  }\n}\nexport { OculusHandPointerModel };", "map": {"version": 3, "names": ["PINCH_MAX", "PINCH_THRESHOLD", "PINCH_MIN", "POINTER_ADVANCE_MAX", "POINTER_OPACITY_MAX", "POINTER_OPACITY_MIN", "POINTER_FRONT_RADIUS", "POINTER_REAR_RADIUS", "POINTER_REAR_RADIUS_MIN", "POINTER_LENGTH", "POINTER_SEGMENTS", "POINTER_RINGS", "POINTER_HEMISPHERE_ANGLE", "YAXIS", "THREE", "Vector3", "ZAXIS", "CURSOR_RADIUS", "CURSOR_MAX_DISTANCE", "OculusHandPointerModel", "Object3D", "constructor", "hand", "controller", "motionController", "envMap", "mesh", "pointerGeometry", "pointer<PERSON><PERSON>", "pointerObject", "pinched", "attached", "cursorObject", "raycaster", "_onConnected", "bind", "_onDisconnected", "addEventListener", "event", "xrInputSource", "data", "visible", "createPointer", "_a", "dispose", "_b", "material", "clear", "_drawVerticesRing", "vertices", "baseVector", "ringIndex", "segmentVector", "clone", "i", "applyAxisAngle", "Math", "PI", "vid", "x", "y", "z", "_updatePointerVertices", "rearRadius", "attributes", "position", "array", "frontFaceBase", "rearBase", "sin", "cos", "frontCenterIndex", "rearCenterIndex", "frontCenter", "rearCenter", "setAttribute", "Float32BufferAttribute", "j", "Array", "fill", "indices", "BufferGeometry", "push", "MeshBasicMaterial", "transparent", "opacity", "setIndex", "<PERSON><PERSON>", "set", "add", "Raycaster", "cursorGeometry", "SphereGeometry", "cursorMaterial", "_updateRaycaster", "pointerMatrix", "matrixWorld", "tempMatrix", "Matrix4", "identity", "extractRotation", "ray", "origin", "setFromMatrixPosition", "direction", "applyMatrix4", "_updatePointer", "indexTip", "joints", "thumbTip", "distance", "distanceTo", "multiplyScalar", "copy", "quaternion", "pinchScale", "focusScale", "updateMatrixWorld", "force", "isPinched", "setAttached", "isAttached", "intersectObject", "object", "recursive", "intersectObjects", "objects", "checkIntersections", "intersections", "length", "intersection", "setCursor", "removeEventListener"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/webxr/OculusHandPointerModel.js"], "sourcesContent": ["import * as THREE from 'three'\n\nconst PINCH_MAX = 0.05\nconst <PERSON>INCH_THRESHOLD = 0.02\nconst PINCH_MIN = 0.01\nconst POINTER_ADVANCE_MAX = 0.02\nconst POINTER_OPACITY_MAX = 1\nconst POINTER_OPACITY_MIN = 0.4\nconst POINTER_FRONT_RADIUS = 0.002\nconst POINTER_REAR_RADIUS = 0.01\nconst POINTER_REAR_RADIUS_MIN = 0.003\nconst POINTER_LENGTH = 0.035\nconst POINTER_SEGMENTS = 16\nconst POINTER_RINGS = 12\nconst POINTER_HEMISPHERE_ANGLE = 110\nconst YAXIS = /* @__PURE__ */ new THREE.Vector3(0, 1, 0)\nconst ZAXIS = /* @__PURE__ */ new THREE.Vector3(0, 0, 1)\n\nconst CURSOR_RADIUS = 0.02\nconst C<PERSON><PERSON><PERSON>_MAX_DISTANCE = 1.5\n\nclass OculusHandPointerModel extends THREE.Object3D {\n  constructor(hand, controller) {\n    super()\n\n    this.hand = hand\n    this.controller = controller\n\n    // Unused\n    this.motionController = null\n    this.envMap = null\n    this.mesh = null\n\n    this.pointerGeometry = null\n    this.pointerMesh = null\n    this.pointerObject = null\n\n    this.pinched = false\n    this.attached = false\n\n    this.cursorObject = null\n\n    this.raycaster = null\n\n    this._onConnected = this._onConnected.bind(this)\n    this._onDisconnected = this._onDisconnected.bind(this)\n    this.hand.addEventListener('connected', this._onConnected)\n    this.hand.addEventListener('disconnected', this._onDisconnected)\n  }\n\n  _onConnected(event) {\n    const xrInputSource = event.data\n    if (xrInputSource.hand) {\n      this.visible = true\n      this.xrInputSource = xrInputSource\n\n      this.createPointer()\n    }\n  }\n\n  _onDisconnected() {\n    this.visible = false\n    this.xrInputSource = null\n\n    this.pointerGeometry?.dispose()\n    this.pointerMesh?.material.dispose()\n\n    this.clear()\n  }\n\n  _drawVerticesRing(vertices, baseVector, ringIndex) {\n    const segmentVector = baseVector.clone()\n    for (var i = 0; i < POINTER_SEGMENTS; i++) {\n      segmentVector.applyAxisAngle(ZAXIS, (Math.PI * 2) / POINTER_SEGMENTS)\n      const vid = ringIndex * POINTER_SEGMENTS + i\n      vertices[3 * vid] = segmentVector.x\n      vertices[3 * vid + 1] = segmentVector.y\n      vertices[3 * vid + 2] = segmentVector.z\n    }\n  }\n\n  _updatePointerVertices(rearRadius) {\n    const vertices = this.pointerGeometry.attributes.position.array\n    // first ring for front face\n    const frontFaceBase = new THREE.Vector3(POINTER_FRONT_RADIUS, 0, -1 * (POINTER_LENGTH - rearRadius))\n    this._drawVerticesRing(vertices, frontFaceBase, 0)\n\n    // rings for rear hemisphere\n    const rearBase = new THREE.Vector3(\n      Math.sin((Math.PI * POINTER_HEMISPHERE_ANGLE) / 180) * rearRadius,\n      Math.cos((Math.PI * POINTER_HEMISPHERE_ANGLE) / 180) * rearRadius,\n      0,\n    )\n    for (var i = 0; i < POINTER_RINGS; i++) {\n      this._drawVerticesRing(vertices, rearBase, i + 1)\n      rearBase.applyAxisAngle(YAXIS, (Math.PI * POINTER_HEMISPHERE_ANGLE) / 180 / (POINTER_RINGS * -2))\n    }\n\n    // front and rear face center vertices\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS)\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1\n    const frontCenter = new THREE.Vector3(0, 0, -1 * (POINTER_LENGTH - rearRadius))\n    vertices[frontCenterIndex * 3] = frontCenter.x\n    vertices[frontCenterIndex * 3 + 1] = frontCenter.y\n    vertices[frontCenterIndex * 3 + 2] = frontCenter.z\n    const rearCenter = new THREE.Vector3(0, 0, rearRadius)\n    vertices[rearCenterIndex * 3] = rearCenter.x\n    vertices[rearCenterIndex * 3 + 1] = rearCenter.y\n    vertices[rearCenterIndex * 3 + 2] = rearCenter.z\n\n    this.pointerGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))\n    // verticesNeedUpdate = true;\n  }\n\n  createPointer() {\n    var i, j\n    const vertices = new Array(((POINTER_RINGS + 1) * POINTER_SEGMENTS + 2) * 3).fill(0)\n    // const vertices = [];\n    const indices = []\n    this.pointerGeometry = new THREE.BufferGeometry()\n\n    this.pointerGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))\n\n    this._updatePointerVertices(POINTER_REAR_RADIUS)\n\n    // construct faces to connect rings\n    for (i = 0; i < POINTER_RINGS; i++) {\n      for (j = 0; j < POINTER_SEGMENTS - 1; j++) {\n        indices.push(i * POINTER_SEGMENTS + j, i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j)\n        indices.push(i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j)\n      }\n\n      indices.push((i + 1) * POINTER_SEGMENTS - 1, i * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1)\n      indices.push(i * POINTER_SEGMENTS, (i + 1) * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1)\n    }\n\n    // construct front and rear face\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS)\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1\n\n    for (i = 0; i < POINTER_SEGMENTS - 1; i++) {\n      indices.push(frontCenterIndex, i + 1, i)\n      indices.push(rearCenterIndex, i + POINTER_SEGMENTS * POINTER_RINGS, i + POINTER_SEGMENTS * POINTER_RINGS + 1)\n    }\n\n    indices.push(frontCenterIndex, 0, POINTER_SEGMENTS - 1)\n    indices.push(rearCenterIndex, POINTER_SEGMENTS * (POINTER_RINGS + 1) - 1, POINTER_SEGMENTS * POINTER_RINGS)\n\n    const material = new THREE.MeshBasicMaterial()\n    material.transparent = true\n    material.opacity = POINTER_OPACITY_MIN\n\n    this.pointerGeometry.setIndex(indices)\n\n    this.pointerMesh = new THREE.Mesh(this.pointerGeometry, material)\n\n    this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS)\n    this.pointerObject = new THREE.Object3D()\n    this.pointerObject.add(this.pointerMesh)\n\n    this.raycaster = new THREE.Raycaster()\n\n    // create cursor\n    const cursorGeometry = new THREE.SphereGeometry(CURSOR_RADIUS, 10, 10)\n    const cursorMaterial = new THREE.MeshBasicMaterial()\n    cursorMaterial.transparent = true\n    cursorMaterial.opacity = POINTER_OPACITY_MIN\n\n    this.cursorObject = new THREE.Mesh(cursorGeometry, cursorMaterial)\n    this.pointerObject.add(this.cursorObject)\n\n    this.add(this.pointerObject)\n  }\n\n  _updateRaycaster() {\n    if (this.raycaster) {\n      const pointerMatrix = this.pointerObject.matrixWorld\n      const tempMatrix = new THREE.Matrix4()\n      tempMatrix.identity().extractRotation(pointerMatrix)\n      this.raycaster.ray.origin.setFromMatrixPosition(pointerMatrix)\n      this.raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix)\n    }\n  }\n\n  _updatePointer() {\n    this.pointerObject.visible = this.controller.visible\n    const indexTip = this.hand.joints['index-finger-tip']\n    const thumbTip = this.hand.joints['thumb-tip']\n    const distance = indexTip.position.distanceTo(thumbTip.position)\n    const position = indexTip.position.clone().add(thumbTip.position).multiplyScalar(0.5)\n    this.pointerObject.position.copy(position)\n    this.pointerObject.quaternion.copy(this.controller.quaternion)\n\n    this.pinched = distance <= PINCH_THRESHOLD\n\n    const pinchScale = (distance - PINCH_MIN) / (PINCH_MAX - PINCH_MIN)\n    const focusScale = (distance - PINCH_MIN) / (PINCH_THRESHOLD - PINCH_MIN)\n    if (pinchScale > 1) {\n      this._updatePointerVertices(POINTER_REAR_RADIUS)\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS)\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MIN\n    } else if (pinchScale > 0) {\n      const rearRadius = (POINTER_REAR_RADIUS - POINTER_REAR_RADIUS_MIN) * pinchScale + POINTER_REAR_RADIUS_MIN\n      this._updatePointerVertices(rearRadius)\n      if (focusScale < 1) {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius - (1 - focusScale) * POINTER_ADVANCE_MAX)\n        this.pointerMesh.material.opacity =\n          POINTER_OPACITY_MIN + (1 - focusScale) * (POINTER_OPACITY_MAX - POINTER_OPACITY_MIN)\n      } else {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius)\n        this.pointerMesh.material.opacity = POINTER_OPACITY_MIN\n      }\n    } else {\n      this._updatePointerVertices(POINTER_REAR_RADIUS_MIN)\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS_MIN - POINTER_ADVANCE_MAX)\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MAX\n    }\n\n    this.cursorObject.material.opacity = this.pointerMesh.material.opacity\n  }\n\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force)\n    if (this.pointerGeometry) {\n      this._updatePointer()\n      this._updateRaycaster()\n    }\n  }\n\n  isPinched() {\n    return this.pinched\n  }\n\n  setAttached(attached) {\n    this.attached = attached\n  }\n\n  isAttached() {\n    return this.attached\n  }\n\n  intersectObject(object, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObject(object, recursive)\n    }\n  }\n\n  intersectObjects(objects, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObjects(objects, recursive)\n    }\n  }\n\n  checkIntersections(objects, recursive = false) {\n    if (this.raycaster && !this.attached) {\n      const intersections = this.raycaster.intersectObjects(objects, recursive)\n      const direction = new THREE.Vector3(0, 0, -1)\n      if (intersections.length > 0) {\n        const intersection = intersections[0]\n        const distance = intersection.distance\n        this.cursorObject.position.copy(direction.multiplyScalar(distance))\n      } else {\n        this.cursorObject.position.copy(direction.multiplyScalar(CURSOR_MAX_DISTANCE))\n      }\n    }\n  }\n\n  setCursor(distance) {\n    const direction = new THREE.Vector3(0, 0, -1)\n    if (this.raycaster && !this.attached) {\n      this.cursorObject.position.copy(direction.multiplyScalar(distance))\n    }\n  }\n\n  dispose() {\n    this._onDisconnected()\n    this.hand.removeEventListener('connected', this._onConnected)\n    this.hand.removeEventListener('disconnected', this._onDisconnected)\n  }\n}\n\nexport { OculusHandPointerModel }\n"], "mappings": ";AAEA,MAAMA,SAAA,GAAY;AAClB,MAAMC,eAAA,GAAkB;AACxB,MAAMC,SAAA,GAAY;AAClB,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,oBAAA,GAAuB;AAC7B,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,uBAAA,GAA0B;AAChC,MAAMC,cAAA,GAAiB;AACvB,MAAMC,gBAAA,GAAmB;AACzB,MAAMC,aAAA,GAAgB;AACtB,MAAMC,wBAAA,GAA2B;AACjC,MAAMC,KAAA,GAAwB,mBAAIC,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAG,CAAC;AACvD,MAAMC,KAAA,GAAwB,mBAAIF,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAG,CAAC;AAEvD,MAAME,aAAA,GAAgB;AACtB,MAAMC,mBAAA,GAAsB;AAE5B,MAAMC,sBAAA,SAA+BL,KAAA,CAAMM,QAAA,CAAS;EAClDC,YAAYC,IAAA,EAAMC,UAAA,EAAY;IAC5B,MAAO;IAEP,KAAKD,IAAA,GAAOA,IAAA;IACZ,KAAKC,UAAA,GAAaA,UAAA;IAGlB,KAAKC,gBAAA,GAAmB;IACxB,KAAKC,MAAA,GAAS;IACd,KAAKC,IAAA,GAAO;IAEZ,KAAKC,eAAA,GAAkB;IACvB,KAAKC,WAAA,GAAc;IACnB,KAAKC,aAAA,GAAgB;IAErB,KAAKC,OAAA,GAAU;IACf,KAAKC,QAAA,GAAW;IAEhB,KAAKC,YAAA,GAAe;IAEpB,KAAKC,SAAA,GAAY;IAEjB,KAAKC,YAAA,GAAe,KAAKA,YAAA,CAAaC,IAAA,CAAK,IAAI;IAC/C,KAAKC,eAAA,GAAkB,KAAKA,eAAA,CAAgBD,IAAA,CAAK,IAAI;IACrD,KAAKb,IAAA,CAAKe,gBAAA,CAAiB,aAAa,KAAKH,YAAY;IACzD,KAAKZ,IAAA,CAAKe,gBAAA,CAAiB,gBAAgB,KAAKD,eAAe;EAChE;EAEDF,aAAaI,KAAA,EAAO;IAClB,MAAMC,aAAA,GAAgBD,KAAA,CAAME,IAAA;IAC5B,IAAID,aAAA,CAAcjB,IAAA,EAAM;MACtB,KAAKmB,OAAA,GAAU;MACf,KAAKF,aAAA,GAAgBA,aAAA;MAErB,KAAKG,aAAA,CAAe;IACrB;EACF;EAEDN,gBAAA,EAAkB;;IAChB,KAAKK,OAAA,GAAU;IACf,KAAKF,aAAA,GAAgB;IAErB,CAAAI,EAAA,QAAKhB,eAAA,KAAL,gBAAAgB,EAAA,CAAsBC,OAAA;IACtB,CAAAC,EAAA,QAAKjB,WAAA,KAAL,gBAAAiB,EAAA,CAAkBC,QAAA,CAASF,OAAA;IAE3B,KAAKG,KAAA,CAAO;EACb;EAEDC,kBAAkBC,QAAA,EAAUC,UAAA,EAAYC,SAAA,EAAW;IACjD,MAAMC,aAAA,GAAgBF,UAAA,CAAWG,KAAA,CAAO;IACxC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI5C,gBAAA,EAAkB4C,CAAA,IAAK;MACzCF,aAAA,CAAcG,cAAA,CAAevC,KAAA,EAAQwC,IAAA,CAAKC,EAAA,GAAK,IAAK/C,gBAAgB;MACpE,MAAMgD,GAAA,GAAMP,SAAA,GAAYzC,gBAAA,GAAmB4C,CAAA;MAC3CL,QAAA,CAAS,IAAIS,GAAG,IAAIN,aAAA,CAAcO,CAAA;MAClCV,QAAA,CAAS,IAAIS,GAAA,GAAM,CAAC,IAAIN,aAAA,CAAcQ,CAAA;MACtCX,QAAA,CAAS,IAAIS,GAAA,GAAM,CAAC,IAAIN,aAAA,CAAcS,CAAA;IACvC;EACF;EAEDC,uBAAuBC,UAAA,EAAY;IACjC,MAAMd,QAAA,GAAW,KAAKtB,eAAA,CAAgBqC,UAAA,CAAWC,QAAA,CAASC,KAAA;IAE1D,MAAMC,aAAA,GAAgB,IAAIrD,KAAA,CAAMC,OAAA,CAAQT,oBAAA,EAAsB,GAAG,MAAMG,cAAA,GAAiBsD,UAAA,CAAW;IACnG,KAAKf,iBAAA,CAAkBC,QAAA,EAAUkB,aAAA,EAAe,CAAC;IAGjD,MAAMC,QAAA,GAAW,IAAItD,KAAA,CAAMC,OAAA,CACzByC,IAAA,CAAKa,GAAA,CAAKb,IAAA,CAAKC,EAAA,GAAK7C,wBAAA,GAA4B,GAAG,IAAImD,UAAA,EACvDP,IAAA,CAAKc,GAAA,CAAKd,IAAA,CAAKC,EAAA,GAAK7C,wBAAA,GAA4B,GAAG,IAAImD,UAAA,EACvD,CACD;IACD,SAAST,CAAA,GAAI,GAAGA,CAAA,GAAI3C,aAAA,EAAe2C,CAAA,IAAK;MACtC,KAAKN,iBAAA,CAAkBC,QAAA,EAAUmB,QAAA,EAAUd,CAAA,GAAI,CAAC;MAChDc,QAAA,CAASb,cAAA,CAAe1C,KAAA,EAAQ2C,IAAA,CAAKC,EAAA,GAAK7C,wBAAA,GAA4B,OAAOD,aAAA,GAAgB,GAAG;IACjG;IAGD,MAAM4D,gBAAA,GAAmB7D,gBAAA,IAAoB,IAAIC,aAAA;IACjD,MAAM6D,eAAA,GAAkB9D,gBAAA,IAAoB,IAAIC,aAAA,IAAiB;IACjE,MAAM8D,WAAA,GAAc,IAAI3D,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAG,MAAMN,cAAA,GAAiBsD,UAAA,CAAW;IAC9Ed,QAAA,CAASsB,gBAAA,GAAmB,CAAC,IAAIE,WAAA,CAAYd,CAAA;IAC7CV,QAAA,CAASsB,gBAAA,GAAmB,IAAI,CAAC,IAAIE,WAAA,CAAYb,CAAA;IACjDX,QAAA,CAASsB,gBAAA,GAAmB,IAAI,CAAC,IAAIE,WAAA,CAAYZ,CAAA;IACjD,MAAMa,UAAA,GAAa,IAAI5D,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAGgD,UAAU;IACrDd,QAAA,CAASuB,eAAA,GAAkB,CAAC,IAAIE,UAAA,CAAWf,CAAA;IAC3CV,QAAA,CAASuB,eAAA,GAAkB,IAAI,CAAC,IAAIE,UAAA,CAAWd,CAAA;IAC/CX,QAAA,CAASuB,eAAA,GAAkB,IAAI,CAAC,IAAIE,UAAA,CAAWb,CAAA;IAE/C,KAAKlC,eAAA,CAAgBgD,YAAA,CAAa,YAAY,IAAI7D,KAAA,CAAM8D,sBAAA,CAAuB3B,QAAA,EAAU,CAAC,CAAC;EAE5F;EAEDP,cAAA,EAAgB;IACd,IAAIY,CAAA,EAAGuB,CAAA;IACP,MAAM5B,QAAA,GAAW,IAAI6B,KAAA,GAAQnE,aAAA,GAAgB,KAAKD,gBAAA,GAAmB,KAAK,CAAC,EAAEqE,IAAA,CAAK,CAAC;IAEnF,MAAMC,OAAA,GAAU,EAAE;IAClB,KAAKrD,eAAA,GAAkB,IAAIb,KAAA,CAAMmE,cAAA,CAAgB;IAEjD,KAAKtD,eAAA,CAAgBgD,YAAA,CAAa,YAAY,IAAI7D,KAAA,CAAM8D,sBAAA,CAAuB3B,QAAA,EAAU,CAAC,CAAC;IAE3F,KAAKa,sBAAA,CAAuBvD,mBAAmB;IAG/C,KAAK+C,CAAA,GAAI,GAAGA,CAAA,GAAI3C,aAAA,EAAe2C,CAAA,IAAK;MAClC,KAAKuB,CAAA,GAAI,GAAGA,CAAA,GAAInE,gBAAA,GAAmB,GAAGmE,CAAA,IAAK;QACzCG,OAAA,CAAQE,IAAA,CAAK5B,CAAA,GAAI5C,gBAAA,GAAmBmE,CAAA,EAAGvB,CAAA,GAAI5C,gBAAA,GAAmBmE,CAAA,GAAI,IAAIvB,CAAA,GAAI,KAAK5C,gBAAA,GAAmBmE,CAAC;QACnGG,OAAA,CAAQE,IAAA,CAAK5B,CAAA,GAAI5C,gBAAA,GAAmBmE,CAAA,GAAI,IAAIvB,CAAA,GAAI,KAAK5C,gBAAA,GAAmBmE,CAAA,GAAI,IAAIvB,CAAA,GAAI,KAAK5C,gBAAA,GAAmBmE,CAAC;MAC9G;MAEDG,OAAA,CAAQE,IAAA,EAAM5B,CAAA,GAAI,KAAK5C,gBAAA,GAAmB,GAAG4C,CAAA,GAAI5C,gBAAA,GAAmB4C,CAAA,GAAI,KAAK5C,gBAAA,GAAmB,CAAC;MACjGsE,OAAA,CAAQE,IAAA,CAAK5B,CAAA,GAAI5C,gBAAA,GAAmB4C,CAAA,GAAI,KAAK5C,gBAAA,GAAmB4C,CAAA,GAAI,KAAK5C,gBAAA,GAAmB,CAAC;IAC9F;IAGD,MAAM6D,gBAAA,GAAmB7D,gBAAA,IAAoB,IAAIC,aAAA;IACjD,MAAM6D,eAAA,GAAkB9D,gBAAA,IAAoB,IAAIC,aAAA,IAAiB;IAEjE,KAAK2C,CAAA,GAAI,GAAGA,CAAA,GAAI5C,gBAAA,GAAmB,GAAG4C,CAAA,IAAK;MACzC0B,OAAA,CAAQE,IAAA,CAAKX,gBAAA,EAAkBjB,CAAA,GAAI,GAAGA,CAAC;MACvC0B,OAAA,CAAQE,IAAA,CAAKV,eAAA,EAAiBlB,CAAA,GAAI5C,gBAAA,GAAmBC,aAAA,EAAe2C,CAAA,GAAI5C,gBAAA,GAAmBC,aAAA,GAAgB,CAAC;IAC7G;IAEDqE,OAAA,CAAQE,IAAA,CAAKX,gBAAA,EAAkB,GAAG7D,gBAAA,GAAmB,CAAC;IACtDsE,OAAA,CAAQE,IAAA,CAAKV,eAAA,EAAiB9D,gBAAA,IAAoBC,aAAA,GAAgB,KAAK,GAAGD,gBAAA,GAAmBC,aAAa;IAE1G,MAAMmC,QAAA,GAAW,IAAIhC,KAAA,CAAMqE,iBAAA,CAAmB;IAC9CrC,QAAA,CAASsC,WAAA,GAAc;IACvBtC,QAAA,CAASuC,OAAA,GAAUhF,mBAAA;IAEnB,KAAKsB,eAAA,CAAgB2D,QAAA,CAASN,OAAO;IAErC,KAAKpD,WAAA,GAAc,IAAId,KAAA,CAAMyE,IAAA,CAAK,KAAK5D,eAAA,EAAiBmB,QAAQ;IAEhE,KAAKlB,WAAA,CAAYqC,QAAA,CAASuB,GAAA,CAAI,GAAG,GAAG,KAAKjF,mBAAmB;IAC5D,KAAKsB,aAAA,GAAgB,IAAIf,KAAA,CAAMM,QAAA,CAAU;IACzC,KAAKS,aAAA,CAAc4D,GAAA,CAAI,KAAK7D,WAAW;IAEvC,KAAKK,SAAA,GAAY,IAAInB,KAAA,CAAM4E,SAAA,CAAW;IAGtC,MAAMC,cAAA,GAAiB,IAAI7E,KAAA,CAAM8E,cAAA,CAAe3E,aAAA,EAAe,IAAI,EAAE;IACrE,MAAM4E,cAAA,GAAiB,IAAI/E,KAAA,CAAMqE,iBAAA,CAAmB;IACpDU,cAAA,CAAeT,WAAA,GAAc;IAC7BS,cAAA,CAAeR,OAAA,GAAUhF,mBAAA;IAEzB,KAAK2B,YAAA,GAAe,IAAIlB,KAAA,CAAMyE,IAAA,CAAKI,cAAA,EAAgBE,cAAc;IACjE,KAAKhE,aAAA,CAAc4D,GAAA,CAAI,KAAKzD,YAAY;IAExC,KAAKyD,GAAA,CAAI,KAAK5D,aAAa;EAC5B;EAEDiE,iBAAA,EAAmB;IACjB,IAAI,KAAK7D,SAAA,EAAW;MAClB,MAAM8D,aAAA,GAAgB,KAAKlE,aAAA,CAAcmE,WAAA;MACzC,MAAMC,UAAA,GAAa,IAAInF,KAAA,CAAMoF,OAAA,CAAS;MACtCD,UAAA,CAAWE,QAAA,CAAQ,EAAGC,eAAA,CAAgBL,aAAa;MACnD,KAAK9D,SAAA,CAAUoE,GAAA,CAAIC,MAAA,CAAOC,qBAAA,CAAsBR,aAAa;MAC7D,KAAK9D,SAAA,CAAUoE,GAAA,CAAIG,SAAA,CAAUhB,GAAA,CAAI,GAAG,GAAG,EAAE,EAAEiB,YAAA,CAAaR,UAAU;IACnE;EACF;EAEDS,eAAA,EAAiB;IACf,KAAK7E,aAAA,CAAcY,OAAA,GAAU,KAAKlB,UAAA,CAAWkB,OAAA;IAC7C,MAAMkE,QAAA,GAAW,KAAKrF,IAAA,CAAKsF,MAAA,CAAO,kBAAkB;IACpD,MAAMC,QAAA,GAAW,KAAKvF,IAAA,CAAKsF,MAAA,CAAO,WAAW;IAC7C,MAAME,QAAA,GAAWH,QAAA,CAAS1C,QAAA,CAAS8C,UAAA,CAAWF,QAAA,CAAS5C,QAAQ;IAC/D,MAAMA,QAAA,GAAW0C,QAAA,CAAS1C,QAAA,CAASZ,KAAA,CAAO,EAACoC,GAAA,CAAIoB,QAAA,CAAS5C,QAAQ,EAAE+C,cAAA,CAAe,GAAG;IACpF,KAAKnF,aAAA,CAAcoC,QAAA,CAASgD,IAAA,CAAKhD,QAAQ;IACzC,KAAKpC,aAAA,CAAcqF,UAAA,CAAWD,IAAA,CAAK,KAAK1F,UAAA,CAAW2F,UAAU;IAE7D,KAAKpF,OAAA,GAAUgF,QAAA,IAAY7G,eAAA;IAE3B,MAAMkH,UAAA,IAAcL,QAAA,GAAW5G,SAAA,KAAcF,SAAA,GAAYE,SAAA;IACzD,MAAMkH,UAAA,IAAcN,QAAA,GAAW5G,SAAA,KAAcD,eAAA,GAAkBC,SAAA;IAC/D,IAAIiH,UAAA,GAAa,GAAG;MAClB,KAAKrD,sBAAA,CAAuBvD,mBAAmB;MAC/C,KAAKqB,WAAA,CAAYqC,QAAA,CAASuB,GAAA,CAAI,GAAG,GAAG,KAAKjF,mBAAmB;MAC5D,KAAKqB,WAAA,CAAYkB,QAAA,CAASuC,OAAA,GAAUhF,mBAAA;IAC1C,WAAe8G,UAAA,GAAa,GAAG;MACzB,MAAMpD,UAAA,IAAcxD,mBAAA,GAAsBC,uBAAA,IAA2B2G,UAAA,GAAa3G,uBAAA;MAClF,KAAKsD,sBAAA,CAAuBC,UAAU;MACtC,IAAIqD,UAAA,GAAa,GAAG;QAClB,KAAKxF,WAAA,CAAYqC,QAAA,CAASuB,GAAA,CAAI,GAAG,GAAG,KAAKzB,UAAA,IAAc,IAAIqD,UAAA,IAAcjH,mBAAmB;QAC5F,KAAKyB,WAAA,CAAYkB,QAAA,CAASuC,OAAA,GACxBhF,mBAAA,IAAuB,IAAI+G,UAAA,KAAehH,mBAAA,GAAsBC,mBAAA;MAC1E,OAAa;QACL,KAAKuB,WAAA,CAAYqC,QAAA,CAASuB,GAAA,CAAI,GAAG,GAAG,KAAKzB,UAAU;QACnD,KAAKnC,WAAA,CAAYkB,QAAA,CAASuC,OAAA,GAAUhF,mBAAA;MACrC;IACP,OAAW;MACL,KAAKyD,sBAAA,CAAuBtD,uBAAuB;MACnD,KAAKoB,WAAA,CAAYqC,QAAA,CAASuB,GAAA,CAAI,GAAG,GAAG,KAAKhF,uBAAA,GAA0BL,mBAAmB;MACtF,KAAKyB,WAAA,CAAYkB,QAAA,CAASuC,OAAA,GAAUjF,mBAAA;IACrC;IAED,KAAK4B,YAAA,CAAac,QAAA,CAASuC,OAAA,GAAU,KAAKzD,WAAA,CAAYkB,QAAA,CAASuC,OAAA;EAChE;EAEDgC,kBAAkBC,KAAA,EAAO;IACvB,MAAMD,iBAAA,CAAkBC,KAAK;IAC7B,IAAI,KAAK3F,eAAA,EAAiB;MACxB,KAAK+E,cAAA,CAAgB;MACrB,KAAKZ,gBAAA,CAAkB;IACxB;EACF;EAEDyB,UAAA,EAAY;IACV,OAAO,KAAKzF,OAAA;EACb;EAED0F,YAAYzF,QAAA,EAAU;IACpB,KAAKA,QAAA,GAAWA,QAAA;EACjB;EAED0F,WAAA,EAAa;IACX,OAAO,KAAK1F,QAAA;EACb;EAED2F,gBAAgBC,MAAA,EAAQC,SAAA,GAAY,MAAM;IACxC,IAAI,KAAK3F,SAAA,EAAW;MAClB,OAAO,KAAKA,SAAA,CAAUyF,eAAA,CAAgBC,MAAA,EAAQC,SAAS;IACxD;EACF;EAEDC,iBAAiBC,OAAA,EAASF,SAAA,GAAY,MAAM;IAC1C,IAAI,KAAK3F,SAAA,EAAW;MAClB,OAAO,KAAKA,SAAA,CAAU4F,gBAAA,CAAiBC,OAAA,EAASF,SAAS;IAC1D;EACF;EAEDG,mBAAmBD,OAAA,EAASF,SAAA,GAAY,OAAO;IAC7C,IAAI,KAAK3F,SAAA,IAAa,CAAC,KAAKF,QAAA,EAAU;MACpC,MAAMiG,aAAA,GAAgB,KAAK/F,SAAA,CAAU4F,gBAAA,CAAiBC,OAAA,EAASF,SAAS;MACxE,MAAMpB,SAAA,GAAY,IAAI1F,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAG,EAAE;MAC5C,IAAIiH,aAAA,CAAcC,MAAA,GAAS,GAAG;QAC5B,MAAMC,YAAA,GAAeF,aAAA,CAAc,CAAC;QACpC,MAAMlB,QAAA,GAAWoB,YAAA,CAAapB,QAAA;QAC9B,KAAK9E,YAAA,CAAaiC,QAAA,CAASgD,IAAA,CAAKT,SAAA,CAAUQ,cAAA,CAAeF,QAAQ,CAAC;MAC1E,OAAa;QACL,KAAK9E,YAAA,CAAaiC,QAAA,CAASgD,IAAA,CAAKT,SAAA,CAAUQ,cAAA,CAAe9F,mBAAmB,CAAC;MAC9E;IACF;EACF;EAEDiH,UAAUrB,QAAA,EAAU;IAClB,MAAMN,SAAA,GAAY,IAAI1F,KAAA,CAAMC,OAAA,CAAQ,GAAG,GAAG,EAAE;IAC5C,IAAI,KAAKkB,SAAA,IAAa,CAAC,KAAKF,QAAA,EAAU;MACpC,KAAKC,YAAA,CAAaiC,QAAA,CAASgD,IAAA,CAAKT,SAAA,CAAUQ,cAAA,CAAeF,QAAQ,CAAC;IACnE;EACF;EAEDlE,QAAA,EAAU;IACR,KAAKR,eAAA,CAAiB;IACtB,KAAKd,IAAA,CAAK8G,mBAAA,CAAoB,aAAa,KAAKlG,YAAY;IAC5D,KAAKZ,IAAA,CAAK8G,mBAAA,CAAoB,gBAAgB,KAAKhG,eAAe;EACnE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}