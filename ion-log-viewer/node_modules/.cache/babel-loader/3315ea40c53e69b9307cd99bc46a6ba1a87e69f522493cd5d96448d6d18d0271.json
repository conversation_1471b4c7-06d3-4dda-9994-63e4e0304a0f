{"ast": null, "code": "import { Vector2 } from \"three\";\nconst BokehShader2 = {\n  uniforms: {\n    textureWidth: {\n      value: 1\n    },\n    textureHeight: {\n      value: 1\n    },\n    focalDepth: {\n      value: 1\n    },\n    focalLength: {\n      value: 24\n    },\n    fstop: {\n      value: 0.9\n    },\n    tColor: {\n      value: null\n    },\n    tDepth: {\n      value: null\n    },\n    maxblur: {\n      value: 1\n    },\n    showFocus: {\n      value: 0\n    },\n    manualdof: {\n      value: 0\n    },\n    vignetting: {\n      value: 0\n    },\n    depthblur: {\n      value: 0\n    },\n    threshold: {\n      value: 0.5\n    },\n    gain: {\n      value: 2\n    },\n    bias: {\n      value: 0.5\n    },\n    fringe: {\n      value: 0.7\n    },\n    znear: {\n      value: 0.1\n    },\n    zfar: {\n      value: 100\n    },\n    noise: {\n      value: 1\n    },\n    dithering: {\n      value: 1e-4\n    },\n    pentagon: {\n      value: 0\n    },\n    shaderFocus: {\n      value: 1\n    },\n    focusCoords: {\n      value: /* @__PURE__ */new Vector2()\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n\n    varying vec2 vUv;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n    uniform float textureWidth;\n    uniform float textureHeight;\n\n    uniform float focalDepth;  //focal distance value in meters, but you may use autofocus option below\n    uniform float focalLength; //focal length in mm\n    uniform float fstop; //f-stop value\n    uniform bool showFocus; //show debug focus point and focal range (red = focal point, green = focal range)\n\n    /*\n    make sure that these two values are the same for your camera, otherwise distances will be wrong.\n    */\n\n    uniform float znear; // camera clipping start\n    uniform float zfar; // camera clipping end\n\n    //------------------------------------------\n    //user variables\n\n    const int samples = SAMPLES; //samples on the first ring\n    const int rings = RINGS; //ring count\n\n    const int maxringsamples = rings * samples;\n\n    uniform bool manualdof; // manual dof calculation\n    float ndofstart = 1.0; // near dof blur start\n    float ndofdist = 2.0; // near dof blur falloff distance\n    float fdofstart = 1.0; // far dof blur start\n    float fdofdist = 3.0; // far dof blur falloff distance\n\n    float CoC = 0.03; //circle of confusion size in mm (35mm film = 0.03mm)\n\n    uniform bool vignetting; // use optical lens vignetting\n\n    float vignout = 1.3; // vignetting outer border\n    float vignin = 0.0; // vignetting inner border\n    float vignfade = 22.0; // f-stops till vignete fades\n\n    uniform bool shaderFocus;\n    // disable if you use external focalDepth value\n\n    uniform vec2 focusCoords;\n    // autofocus point on screen (0.0,0.0 - left lower corner, 1.0,1.0 - upper right)\n    // if center of screen use vec2(0.5, 0.5);\n\n    uniform float maxblur;\n    //clamp value of max blur (0.0 = no blur, 1.0 default)\n\n    uniform float threshold; // highlight threshold;\n    uniform float gain; // highlight gain;\n\n    uniform float bias; // bokeh edge bias\n    uniform float fringe; // bokeh chromatic aberration / fringing\n\n    uniform bool noise; //use noise instead of pattern for sample dithering\n\n    uniform float dithering;\n\n    uniform bool depthblur; // blur the depth buffer\n    float dbsize = 1.25; // depth blur size\n\n    /*\n    next part is experimental\n    not looking good with small sample and ring count\n    looks okay starting from samples = 4, rings = 4\n    */\n\n    uniform bool pentagon; //use pentagon as bokeh shape?\n    float feather = 0.4; //pentagon shape feather\n\n    //------------------------------------------\n\n    float penta(vec2 coords) {\n    \t//pentagonal shape\n    \tfloat scale = float(rings) - 1.3;\n    \tvec4  HS0 = vec4( 1.0,         0.0,         0.0,  1.0);\n    \tvec4  HS1 = vec4( 0.309016994, 0.951056516, 0.0,  1.0);\n    \tvec4  HS2 = vec4(-0.809016994, 0.587785252, 0.0,  1.0);\n    \tvec4  HS3 = vec4(-0.809016994,-0.587785252, 0.0,  1.0);\n    \tvec4  HS4 = vec4( 0.309016994,-0.951056516, 0.0,  1.0);\n    \tvec4  HS5 = vec4( 0.0        ,0.0         , 1.0,  1.0);\n\n    \tvec4  one = vec4( 1.0 );\n\n    \tvec4 P = vec4((coords),vec2(scale, scale));\n\n    \tvec4 dist = vec4(0.0);\n    \tfloat inorout = -4.0;\n\n    \tdist.x = dot( P, HS0 );\n    \tdist.y = dot( P, HS1 );\n    \tdist.z = dot( P, HS2 );\n    \tdist.w = dot( P, HS3 );\n\n    \tdist = smoothstep( -feather, feather, dist );\n\n    \tinorout += dot( dist, one );\n\n    \tdist.x = dot( P, HS4 );\n    \tdist.y = HS5.w - abs( P.z );\n\n    \tdist = smoothstep( -feather, feather, dist );\n    \tinorout += dist.x;\n\n    \treturn clamp( inorout, 0.0, 1.0 );\n    }\n\n    float bdepth(vec2 coords) {\n    \t// Depth buffer blur\n    \tfloat d = 0.0;\n    \tfloat kernel[9];\n    \tvec2 offset[9];\n\n    \tvec2 wh = vec2(1.0/textureWidth,1.0/textureHeight) * dbsize;\n\n    \toffset[0] = vec2(-wh.x,-wh.y);\n    \toffset[1] = vec2( 0.0, -wh.y);\n    \toffset[2] = vec2( wh.x -wh.y);\n\n    \toffset[3] = vec2(-wh.x,  0.0);\n    \toffset[4] = vec2( 0.0,   0.0);\n    \toffset[5] = vec2( wh.x,  0.0);\n\n    \toffset[6] = vec2(-wh.x, wh.y);\n    \toffset[7] = vec2( 0.0,  wh.y);\n    \toffset[8] = vec2( wh.x, wh.y);\n\n    \tkernel[0] = 1.0/16.0;   kernel[1] = 2.0/16.0;   kernel[2] = 1.0/16.0;\n    \tkernel[3] = 2.0/16.0;   kernel[4] = 4.0/16.0;   kernel[5] = 2.0/16.0;\n    \tkernel[6] = 1.0/16.0;   kernel[7] = 2.0/16.0;   kernel[8] = 1.0/16.0;\n\n    \tfor( int i=0; i<9; i++ ) {\n    \t\tfloat tmp = texture2D(tDepth, coords + offset[i]).r;\n    \t\td += tmp * kernel[i];\n    \t}\n\n    \treturn d;\n    }\n\n    vec3 color(vec2 coords,float blur) {\n    \t//processing the sample\n\n    \tvec3 col = vec3(0.0);\n    \tvec2 texel = vec2(1.0/textureWidth,1.0/textureHeight);\n\n    \tcol.r = texture2D(tColor,coords + vec2(0.0,1.0)*texel*fringe*blur).r;\n    \tcol.g = texture2D(tColor,coords + vec2(-0.866,-0.5)*texel*fringe*blur).g;\n    \tcol.b = texture2D(tColor,coords + vec2(0.866,-0.5)*texel*fringe*blur).b;\n\n    \tvec3 lumcoeff = vec3(0.299,0.587,0.114);\n    \tfloat lum = dot(col.rgb, lumcoeff);\n    \tfloat thresh = max((lum-threshold)*gain, 0.0);\n    \treturn col+mix(vec3(0.0),col,thresh*blur);\n    }\n\n    vec3 debugFocus(vec3 col, float blur, float depth) {\n    \tfloat edge = 0.002*depth; //distance based edge smoothing\n    \tfloat m = clamp(smoothstep(0.0,edge,blur),0.0,1.0);\n    \tfloat e = clamp(smoothstep(1.0-edge,1.0,blur),0.0,1.0);\n\n    \tcol = mix(col,vec3(1.0,0.5,0.0),(1.0-m)*0.6);\n    \tcol = mix(col,vec3(0.0,0.5,1.0),((1.0-e)-(1.0-m))*0.2);\n\n    \treturn col;\n    }\n\n    float linearize(float depth) {\n    \treturn -zfar * znear / (depth * (zfar - znear) - zfar);\n    }\n\n    float vignette() {\n    \tfloat dist = distance(vUv.xy, vec2(0.5,0.5));\n    \tdist = smoothstep(vignout+(fstop/vignfade), vignin+(fstop/vignfade), dist);\n    \treturn clamp(dist,0.0,1.0);\n    }\n\n    float gather(float i, float j, int ringsamples, inout vec3 col, float w, float h, float blur) {\n    \tfloat rings2 = float(rings);\n    \tfloat step = PI*2.0 / float(ringsamples);\n    \tfloat pw = cos(j*step)*i;\n    \tfloat ph = sin(j*step)*i;\n    \tfloat p = 1.0;\n    \tif (pentagon) {\n    \t\tp = penta(vec2(pw,ph));\n    \t}\n    \tcol += color(vUv.xy + vec2(pw*w,ph*h), blur) * mix(1.0, i/rings2, bias) * p;\n    \treturn 1.0 * mix(1.0, i /rings2, bias) * p;\n    }\n\n    void main() {\n    \t//scene depth calculation\n\n    \tfloat depth = linearize(texture2D(tDepth,vUv.xy).x);\n\n    \t// Blur depth?\n    \tif ( depthblur ) {\n    \t\tdepth = linearize(bdepth(vUv.xy));\n    \t}\n\n    \t//focal plane calculation\n\n    \tfloat fDepth = focalDepth;\n\n    \tif (shaderFocus) {\n\n    \t\tfDepth = linearize(texture2D(tDepth,focusCoords).x);\n\n    \t}\n\n    \t// dof blur factor calculation\n\n    \tfloat blur = 0.0;\n\n    \tif (manualdof) {\n    \t\tfloat a = depth-fDepth; // Focal plane\n    \t\tfloat b = (a-fdofstart)/fdofdist; // Far DoF\n    \t\tfloat c = (-a-ndofstart)/ndofdist; // Near Dof\n    \t\tblur = (a>0.0) ? b : c;\n    \t} else {\n    \t\tfloat f = focalLength; // focal length in mm\n    \t\tfloat d = fDepth*1000.0; // focal plane in mm\n    \t\tfloat o = depth*1000.0; // depth in mm\n\n    \t\tfloat a = (o*f)/(o-f);\n    \t\tfloat b = (d*f)/(d-f);\n    \t\tfloat c = (d-f)/(d*fstop*CoC);\n\n    \t\tblur = abs(a-b)*c;\n    \t}\n\n    \tblur = clamp(blur,0.0,1.0);\n\n    \t// calculation of pattern for dithering\n\n    \tvec2 noise = vec2(rand(vUv.xy), rand( vUv.xy + vec2( 0.4, 0.6 ) ) )*dithering*blur;\n\n    \t// getting blur x and y step factor\n\n    \tfloat w = (1.0/textureWidth)*blur*maxblur+noise.x;\n    \tfloat h = (1.0/textureHeight)*blur*maxblur+noise.y;\n\n    \t// calculation of final color\n\n    \tvec3 col = vec3(0.0);\n\n    \tif(blur < 0.05) {\n    \t\t//some optimization thingy\n    \t\tcol = texture2D(tColor, vUv.xy).rgb;\n    \t} else {\n    \t\tcol = texture2D(tColor, vUv.xy).rgb;\n    \t\tfloat s = 1.0;\n    \t\tint ringsamples;\n\n    \t\tfor (int i = 1; i <= rings; i++) {\n    \t\t\t/*unboxstart*/\n    \t\t\tringsamples = i * samples;\n\n    \t\t\tfor (int j = 0 ; j < maxringsamples ; j++) {\n    \t\t\t\tif (j >= ringsamples) break;\n    \t\t\t\ts += gather(float(i), float(j), ringsamples, col, w, h, blur);\n    \t\t\t}\n    \t\t\t/*unboxend*/\n    \t\t}\n\n    \t\tcol /= s; //divide by sample count\n    \t}\n\n    \tif (showFocus) {\n    \t\tcol = debugFocus(col, blur, depth);\n    \t}\n\n    \tif (vignetting) {\n    \t\tcol *= vignette();\n    \t}\n\n    \tgl_FragColor.rgb = col;\n    \tgl_FragColor.a = 1.0;\n    } \n  `)\n};\nconst BokehDepthShader = {\n  uniforms: {\n    mNear: {\n      value: 1\n    },\n    mFar: {\n      value: 1e3\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying float vViewZDepth;\n\n    void main() {\n\n    \t#include <begin_vertex>\n    \t#include <project_vertex>\n\n    \tvViewZDepth = - mvPosition.z;\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float mNear;\n    uniform float mFar;\n\n    varying float vViewZDepth;\n\n    void main() {\n\n    \tfloat color = 1.0 - smoothstep( mNear, mFar, vViewZDepth );\n    \tgl_FragColor = vec4( vec3( color ), 1.0 );\n\n    } \n  `)\n};\nexport { BokehDepthShader, BokehShader2 };", "map": {"version": 3, "names": ["BokehShader2", "uniforms", "textureWidth", "value", "textureHeight", "focalDepth", "focal<PERSON>ength", "fstop", "tColor", "tD<PERSON>h", "maxblur", "showFocus", "<PERSON><PERSON>f", "vignetting", "depthblur", "threshold", "gain", "bias", "fringe", "znear", "zfar", "noise", "dithering", "pentagon", "shaderFocus", "focusCoords", "Vector2", "vertexShader", "fragmentShader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mNear", "mFar"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/shaders/BokehShader2.ts"], "sourcesContent": ["import { IUniform, Texture, Vector2 } from 'three'\n\nexport interface BokehShader2Uniforms {\n  textureWidth: IUniform<number>\n  textureHeight: IUniform<number>\n\n  focalDepth: IUniform<number>\n  focalLength: IUniform<number>\n  fstop: IUniform<number>\n\n  tColor: IUniform<Texture | null>\n  tDepth: IUniform<Texture | null>\n\n  maxblur: IUniform<number>\n\n  showFocus: IUniform<number>\n  manualdof: IUniform<number>\n  vignetting: IUniform<number>\n  depthblur: IUniform<number>\n\n  threshold: IUniform<number>\n  gain: IUniform<number>\n  bias: IUniform<number>\n  fringe: IUniform<number>\n\n  znear: IUniform<number>\n  zfar: IUniform<number>\n\n  noise: IUniform<number>\n  dithering: IUniform<number>\n  pentagon: IUniform<number>\n\n  shaderFocus: IUniform<number>\n  focusCoords: IUniform<Vector2>\n}\n\n/**\n * Depth-of-field shader with bokeh\n * ported from GLSL shader by <PERSON>\n * http://blenderartists.org/forum/showthread.php?237488-GLSL-depth-of-field-with-bokeh-v2-4-(update)\n *\n * Requires #define RINGS and SAMPLES integers\n */\n\nexport const BokehShader2: {\n  uniforms: BokehShader2Uniforms\n  vertexShader: string\n  fragmentShader: string\n} = {\n  uniforms: {\n    textureWidth: { value: 1.0 },\n    textureHeight: { value: 1.0 },\n\n    focalDepth: { value: 1.0 },\n    focalLength: { value: 24.0 },\n    fstop: { value: 0.9 },\n\n    tColor: { value: null },\n    tDepth: { value: null },\n\n    maxblur: { value: 1.0 },\n\n    showFocus: { value: 0 },\n    manualdof: { value: 0 },\n    vignetting: { value: 0 },\n    depthblur: { value: 0 },\n\n    threshold: { value: 0.5 },\n    gain: { value: 2.0 },\n    bias: { value: 0.5 },\n    fringe: { value: 0.7 },\n\n    znear: { value: 0.1 },\n    zfar: { value: 100 },\n\n    noise: { value: 1 },\n    dithering: { value: 0.0001 },\n    pentagon: { value: 0 },\n\n    shaderFocus: { value: 1 },\n    focusCoords: { value: /* @__PURE__ */ new Vector2() },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    varying vec2 vUv;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n    uniform float textureWidth;\n    uniform float textureHeight;\n\n    uniform float focalDepth;  //focal distance value in meters, but you may use autofocus option below\n    uniform float focalLength; //focal length in mm\n    uniform float fstop; //f-stop value\n    uniform bool showFocus; //show debug focus point and focal range (red = focal point, green = focal range)\n\n    /*\n    make sure that these two values are the same for your camera, otherwise distances will be wrong.\n    */\n\n    uniform float znear; // camera clipping start\n    uniform float zfar; // camera clipping end\n\n    //------------------------------------------\n    //user variables\n\n    const int samples = SAMPLES; //samples on the first ring\n    const int rings = RINGS; //ring count\n\n    const int maxringsamples = rings * samples;\n\n    uniform bool manualdof; // manual dof calculation\n    float ndofstart = 1.0; // near dof blur start\n    float ndofdist = 2.0; // near dof blur falloff distance\n    float fdofstart = 1.0; // far dof blur start\n    float fdofdist = 3.0; // far dof blur falloff distance\n\n    float CoC = 0.03; //circle of confusion size in mm (35mm film = 0.03mm)\n\n    uniform bool vignetting; // use optical lens vignetting\n\n    float vignout = 1.3; // vignetting outer border\n    float vignin = 0.0; // vignetting inner border\n    float vignfade = 22.0; // f-stops till vignete fades\n\n    uniform bool shaderFocus;\n    // disable if you use external focalDepth value\n\n    uniform vec2 focusCoords;\n    // autofocus point on screen (0.0,0.0 - left lower corner, 1.0,1.0 - upper right)\n    // if center of screen use vec2(0.5, 0.5);\n\n    uniform float maxblur;\n    //clamp value of max blur (0.0 = no blur, 1.0 default)\n\n    uniform float threshold; // highlight threshold;\n    uniform float gain; // highlight gain;\n\n    uniform float bias; // bokeh edge bias\n    uniform float fringe; // bokeh chromatic aberration / fringing\n\n    uniform bool noise; //use noise instead of pattern for sample dithering\n\n    uniform float dithering;\n\n    uniform bool depthblur; // blur the depth buffer\n    float dbsize = 1.25; // depth blur size\n\n    /*\n    next part is experimental\n    not looking good with small sample and ring count\n    looks okay starting from samples = 4, rings = 4\n    */\n\n    uniform bool pentagon; //use pentagon as bokeh shape?\n    float feather = 0.4; //pentagon shape feather\n\n    //------------------------------------------\n\n    float penta(vec2 coords) {\n    \t//pentagonal shape\n    \tfloat scale = float(rings) - 1.3;\n    \tvec4  HS0 = vec4( 1.0,         0.0,         0.0,  1.0);\n    \tvec4  HS1 = vec4( 0.309016994, 0.951056516, 0.0,  1.0);\n    \tvec4  HS2 = vec4(-0.809016994, 0.587785252, 0.0,  1.0);\n    \tvec4  HS3 = vec4(-0.809016994,-0.587785252, 0.0,  1.0);\n    \tvec4  HS4 = vec4( 0.309016994,-0.951056516, 0.0,  1.0);\n    \tvec4  HS5 = vec4( 0.0        ,0.0         , 1.0,  1.0);\n\n    \tvec4  one = vec4( 1.0 );\n\n    \tvec4 P = vec4((coords),vec2(scale, scale));\n\n    \tvec4 dist = vec4(0.0);\n    \tfloat inorout = -4.0;\n\n    \tdist.x = dot( P, HS0 );\n    \tdist.y = dot( P, HS1 );\n    \tdist.z = dot( P, HS2 );\n    \tdist.w = dot( P, HS3 );\n\n    \tdist = smoothstep( -feather, feather, dist );\n\n    \tinorout += dot( dist, one );\n\n    \tdist.x = dot( P, HS4 );\n    \tdist.y = HS5.w - abs( P.z );\n\n    \tdist = smoothstep( -feather, feather, dist );\n    \tinorout += dist.x;\n\n    \treturn clamp( inorout, 0.0, 1.0 );\n    }\n\n    float bdepth(vec2 coords) {\n    \t// Depth buffer blur\n    \tfloat d = 0.0;\n    \tfloat kernel[9];\n    \tvec2 offset[9];\n\n    \tvec2 wh = vec2(1.0/textureWidth,1.0/textureHeight) * dbsize;\n\n    \toffset[0] = vec2(-wh.x,-wh.y);\n    \toffset[1] = vec2( 0.0, -wh.y);\n    \toffset[2] = vec2( wh.x -wh.y);\n\n    \toffset[3] = vec2(-wh.x,  0.0);\n    \toffset[4] = vec2( 0.0,   0.0);\n    \toffset[5] = vec2( wh.x,  0.0);\n\n    \toffset[6] = vec2(-wh.x, wh.y);\n    \toffset[7] = vec2( 0.0,  wh.y);\n    \toffset[8] = vec2( wh.x, wh.y);\n\n    \tkernel[0] = 1.0/16.0;   kernel[1] = 2.0/16.0;   kernel[2] = 1.0/16.0;\n    \tkernel[3] = 2.0/16.0;   kernel[4] = 4.0/16.0;   kernel[5] = 2.0/16.0;\n    \tkernel[6] = 1.0/16.0;   kernel[7] = 2.0/16.0;   kernel[8] = 1.0/16.0;\n\n    \tfor( int i=0; i<9; i++ ) {\n    \t\tfloat tmp = texture2D(tDepth, coords + offset[i]).r;\n    \t\td += tmp * kernel[i];\n    \t}\n\n    \treturn d;\n    }\n\n    vec3 color(vec2 coords,float blur) {\n    \t//processing the sample\n\n    \tvec3 col = vec3(0.0);\n    \tvec2 texel = vec2(1.0/textureWidth,1.0/textureHeight);\n\n    \tcol.r = texture2D(tColor,coords + vec2(0.0,1.0)*texel*fringe*blur).r;\n    \tcol.g = texture2D(tColor,coords + vec2(-0.866,-0.5)*texel*fringe*blur).g;\n    \tcol.b = texture2D(tColor,coords + vec2(0.866,-0.5)*texel*fringe*blur).b;\n\n    \tvec3 lumcoeff = vec3(0.299,0.587,0.114);\n    \tfloat lum = dot(col.rgb, lumcoeff);\n    \tfloat thresh = max((lum-threshold)*gain, 0.0);\n    \treturn col+mix(vec3(0.0),col,thresh*blur);\n    }\n\n    vec3 debugFocus(vec3 col, float blur, float depth) {\n    \tfloat edge = 0.002*depth; //distance based edge smoothing\n    \tfloat m = clamp(smoothstep(0.0,edge,blur),0.0,1.0);\n    \tfloat e = clamp(smoothstep(1.0-edge,1.0,blur),0.0,1.0);\n\n    \tcol = mix(col,vec3(1.0,0.5,0.0),(1.0-m)*0.6);\n    \tcol = mix(col,vec3(0.0,0.5,1.0),((1.0-e)-(1.0-m))*0.2);\n\n    \treturn col;\n    }\n\n    float linearize(float depth) {\n    \treturn -zfar * znear / (depth * (zfar - znear) - zfar);\n    }\n\n    float vignette() {\n    \tfloat dist = distance(vUv.xy, vec2(0.5,0.5));\n    \tdist = smoothstep(vignout+(fstop/vignfade), vignin+(fstop/vignfade), dist);\n    \treturn clamp(dist,0.0,1.0);\n    }\n\n    float gather(float i, float j, int ringsamples, inout vec3 col, float w, float h, float blur) {\n    \tfloat rings2 = float(rings);\n    \tfloat step = PI*2.0 / float(ringsamples);\n    \tfloat pw = cos(j*step)*i;\n    \tfloat ph = sin(j*step)*i;\n    \tfloat p = 1.0;\n    \tif (pentagon) {\n    \t\tp = penta(vec2(pw,ph));\n    \t}\n    \tcol += color(vUv.xy + vec2(pw*w,ph*h), blur) * mix(1.0, i/rings2, bias) * p;\n    \treturn 1.0 * mix(1.0, i /rings2, bias) * p;\n    }\n\n    void main() {\n    \t//scene depth calculation\n\n    \tfloat depth = linearize(texture2D(tDepth,vUv.xy).x);\n\n    \t// Blur depth?\n    \tif ( depthblur ) {\n    \t\tdepth = linearize(bdepth(vUv.xy));\n    \t}\n\n    \t//focal plane calculation\n\n    \tfloat fDepth = focalDepth;\n\n    \tif (shaderFocus) {\n\n    \t\tfDepth = linearize(texture2D(tDepth,focusCoords).x);\n\n    \t}\n\n    \t// dof blur factor calculation\n\n    \tfloat blur = 0.0;\n\n    \tif (manualdof) {\n    \t\tfloat a = depth-fDepth; // Focal plane\n    \t\tfloat b = (a-fdofstart)/fdofdist; // Far DoF\n    \t\tfloat c = (-a-ndofstart)/ndofdist; // Near Dof\n    \t\tblur = (a>0.0) ? b : c;\n    \t} else {\n    \t\tfloat f = focalLength; // focal length in mm\n    \t\tfloat d = fDepth*1000.0; // focal plane in mm\n    \t\tfloat o = depth*1000.0; // depth in mm\n\n    \t\tfloat a = (o*f)/(o-f);\n    \t\tfloat b = (d*f)/(d-f);\n    \t\tfloat c = (d-f)/(d*fstop*CoC);\n\n    \t\tblur = abs(a-b)*c;\n    \t}\n\n    \tblur = clamp(blur,0.0,1.0);\n\n    \t// calculation of pattern for dithering\n\n    \tvec2 noise = vec2(rand(vUv.xy), rand( vUv.xy + vec2( 0.4, 0.6 ) ) )*dithering*blur;\n\n    \t// getting blur x and y step factor\n\n    \tfloat w = (1.0/textureWidth)*blur*maxblur+noise.x;\n    \tfloat h = (1.0/textureHeight)*blur*maxblur+noise.y;\n\n    \t// calculation of final color\n\n    \tvec3 col = vec3(0.0);\n\n    \tif(blur < 0.05) {\n    \t\t//some optimization thingy\n    \t\tcol = texture2D(tColor, vUv.xy).rgb;\n    \t} else {\n    \t\tcol = texture2D(tColor, vUv.xy).rgb;\n    \t\tfloat s = 1.0;\n    \t\tint ringsamples;\n\n    \t\tfor (int i = 1; i <= rings; i++) {\n    \t\t\t/*unboxstart*/\n    \t\t\tringsamples = i * samples;\n\n    \t\t\tfor (int j = 0 ; j < maxringsamples ; j++) {\n    \t\t\t\tif (j >= ringsamples) break;\n    \t\t\t\ts += gather(float(i), float(j), ringsamples, col, w, h, blur);\n    \t\t\t}\n    \t\t\t/*unboxend*/\n    \t\t}\n\n    \t\tcol /= s; //divide by sample count\n    \t}\n\n    \tif (showFocus) {\n    \t\tcol = debugFocus(col, blur, depth);\n    \t}\n\n    \tif (vignetting) {\n    \t\tcol *= vignette();\n    \t}\n\n    \tgl_FragColor.rgb = col;\n    \tgl_FragColor.a = 1.0;\n    } \n  `,\n}\n\nexport const BokehDepthShader = {\n  uniforms: {\n    mNear: { value: 1.0 },\n    mFar: { value: 1000.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying float vViewZDepth;\n\n    void main() {\n\n    \t#include <begin_vertex>\n    \t#include <project_vertex>\n\n    \tvViewZDepth = - mvPosition.z;\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float mNear;\n    uniform float mFar;\n\n    varying float vViewZDepth;\n\n    void main() {\n\n    \tfloat color = 1.0 - smoothstep( mNear, mFar, vViewZDepth );\n    \tgl_FragColor = vec4( vec3( color ), 1.0 );\n\n    } \n  `,\n}\n"], "mappings": ";AA4CO,MAAMA,YAAA,GAIT;EACFC,QAAA,EAAU;IACRC,YAAA,EAAc;MAAEC,KAAA,EAAO;IAAI;IAC3BC,aAAA,EAAe;MAAED,KAAA,EAAO;IAAI;IAE5BE,UAAA,EAAY;MAAEF,KAAA,EAAO;IAAI;IACzBG,WAAA,EAAa;MAAEH,KAAA,EAAO;IAAK;IAC3BI,KAAA,EAAO;MAAEJ,KAAA,EAAO;IAAI;IAEpBK,MAAA,EAAQ;MAAEL,KAAA,EAAO;IAAK;IACtBM,MAAA,EAAQ;MAAEN,KAAA,EAAO;IAAK;IAEtBO,OAAA,EAAS;MAAEP,KAAA,EAAO;IAAI;IAEtBQ,SAAA,EAAW;MAAER,KAAA,EAAO;IAAE;IACtBS,SAAA,EAAW;MAAET,KAAA,EAAO;IAAE;IACtBU,UAAA,EAAY;MAAEV,KAAA,EAAO;IAAE;IACvBW,SAAA,EAAW;MAAEX,KAAA,EAAO;IAAE;IAEtBY,SAAA,EAAW;MAAEZ,KAAA,EAAO;IAAI;IACxBa,IAAA,EAAM;MAAEb,KAAA,EAAO;IAAI;IACnBc,IAAA,EAAM;MAAEd,KAAA,EAAO;IAAI;IACnBe,MAAA,EAAQ;MAAEf,KAAA,EAAO;IAAI;IAErBgB,KAAA,EAAO;MAAEhB,KAAA,EAAO;IAAI;IACpBiB,IAAA,EAAM;MAAEjB,KAAA,EAAO;IAAI;IAEnBkB,KAAA,EAAO;MAAElB,KAAA,EAAO;IAAE;IAClBmB,SAAA,EAAW;MAAEnB,KAAA,EAAO;IAAO;IAC3BoB,QAAA,EAAU;MAAEpB,KAAA,EAAO;IAAE;IAErBqB,WAAA,EAAa;MAAErB,KAAA,EAAO;IAAE;IACxBsB,WAAA,EAAa;MAAEtB,KAAA,EAAuB,mBAAIuB,OAAA;IAAU;EACtD;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6R7B;AAEO,MAAMC,gBAAA,GAAmB;EAC9B5B,QAAA,EAAU;IACR6B,KAAA,EAAO;MAAE3B,KAAA,EAAO;IAAI;IACpB4B,IAAA,EAAM;MAAE5B,KAAA,EAAO;IAAO;EACxB;EAEAwB,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAazBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAa7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}