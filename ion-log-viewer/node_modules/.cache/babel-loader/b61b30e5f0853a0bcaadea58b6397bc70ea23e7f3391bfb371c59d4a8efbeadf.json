{"ast": null, "code": "import { UniformsUtils, ShaderMaterial, NoBlending, WebGLRenderTarget, MeshBasicMaterial, LinearMipmapLinearFilter } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nimport { LuminosityShader } from \"../shaders/LuminosityShader.js\";\nimport { ToneMapShader } from \"../shaders/ToneMapShader.js\";\nclass AdaptiveToneMappingPass extends Pass {\n  constructor(adaptive, resolution) {\n    super();\n    this.resolution = resolution !== void 0 ? resolution : 256;\n    this.needsInit = true;\n    this.adaptive = adaptive !== void 0 ? !!adaptive : true;\n    this.luminanceRT = null;\n    this.previousLuminanceRT = null;\n    this.currentLuminanceRT = null;\n    const copyShader = CopyShader;\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms);\n    this.materialCopy = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      blending: NoBlending,\n      depthTest: false\n    });\n    this.materialLuminance = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(LuminosityShader.uniforms),\n      vertexShader: LuminosityShader.vertexShader,\n      fragmentShader: LuminosityShader.fragmentShader,\n      blending: NoBlending\n    });\n    this.adaptLuminanceShader = {\n      defines: {\n        MIP_LEVEL_1X1: (Math.log(this.resolution) / Math.log(2)).toFixed(1)\n      },\n      uniforms: {\n        lastLum: {\n          value: null\n        },\n        currentLum: {\n          value: null\n        },\n        minLuminance: {\n          value: 0.01\n        },\n        delta: {\n          value: 0.016\n        },\n        tau: {\n          value: 1\n        }\n      },\n      vertexShader: `varying vec2 vUv;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\t}`,\n      fragmentShader: `varying vec2 vUv;\n\n\t\t\t\tuniform sampler2D lastLum;\n\t\t\t\tuniform sampler2D currentLum;\n\t\t\t\tuniform float minLuminance;\n\t\t\t\tuniform float delta;\n\t\t\t\tuniform float tau;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec4 lastLum = texture2D( lastLum, vUv, MIP_LEVEL_1X1 );\n\t\t\t\t\tvec4 currentLum = texture2D( currentLum, vUv, MIP_LEVEL_1X1 );\n\n\t\t\t\t\tfloat fLastLum = max( minLuminance, lastLum.r );\n\t\t\t\t\tfloat fCurrentLum = max( minLuminance, currentLum.r );\n\n\t\t\t\t\t//The adaption seems to work better in extreme lighting differences\n\t\t\t\t\t//if the input luminance is squared.\n\t\t\t\t\tfCurrentLum *= fCurrentLum;\n\n\t\t\t\t\t// Adapt the luminance using Pattanaik's technique\n\t\t\t\t\tfloat fAdaptedLum = fLastLum + (fCurrentLum - fLastLum) * (1.0 - exp(-delta * tau));\n\t\t\t\t\t// \"fAdaptedLum = sqrt(fAdaptedLum);\n\t\t\t\t\tgl_FragColor.r = fAdaptedLum;\n\t\t\t\t}`\n    };\n    this.materialAdaptiveLum = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(this.adaptLuminanceShader.uniforms),\n      vertexShader: this.adaptLuminanceShader.vertexShader,\n      fragmentShader: this.adaptLuminanceShader.fragmentShader,\n      defines: Object.assign({}, this.adaptLuminanceShader.defines),\n      blending: NoBlending\n    });\n    this.materialToneMap = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(ToneMapShader.uniforms),\n      vertexShader: ToneMapShader.vertexShader,\n      fragmentShader: ToneMapShader.fragmentShader,\n      blending: NoBlending\n    });\n    this.fsQuad = new FullScreenQuad(null);\n  }\n  render(renderer, writeBuffer, readBuffer, deltaTime) {\n    if (this.needsInit) {\n      this.reset(renderer);\n      this.luminanceRT.texture.type = readBuffer.texture.type;\n      this.previousLuminanceRT.texture.type = readBuffer.texture.type;\n      this.currentLuminanceRT.texture.type = readBuffer.texture.type;\n      this.needsInit = false;\n    }\n    if (this.adaptive) {\n      this.fsQuad.material = this.materialLuminance;\n      this.materialLuminance.uniforms.tDiffuse.value = readBuffer.texture;\n      renderer.setRenderTarget(this.currentLuminanceRT);\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.materialAdaptiveLum;\n      this.materialAdaptiveLum.uniforms.delta.value = deltaTime;\n      this.materialAdaptiveLum.uniforms.lastLum.value = this.previousLuminanceRT.texture;\n      this.materialAdaptiveLum.uniforms.currentLum.value = this.currentLuminanceRT.texture;\n      renderer.setRenderTarget(this.luminanceRT);\n      this.fsQuad.render(renderer);\n      this.fsQuad.material = this.materialCopy;\n      this.copyUniforms.tDiffuse.value = this.luminanceRT.texture;\n      renderer.setRenderTarget(this.previousLuminanceRT);\n      this.fsQuad.render(renderer);\n    }\n    this.fsQuad.material = this.materialToneMap;\n    this.materialToneMap.uniforms.tDiffuse.value = readBuffer.texture;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n  reset() {\n    if (this.luminanceRT) {\n      this.luminanceRT.dispose();\n    }\n    if (this.currentLuminanceRT) {\n      this.currentLuminanceRT.dispose();\n    }\n    if (this.previousLuminanceRT) {\n      this.previousLuminanceRT.dispose();\n    }\n    this.luminanceRT = new WebGLRenderTarget(this.resolution, this.resolution);\n    this.luminanceRT.texture.name = \"AdaptiveToneMappingPass.l\";\n    this.luminanceRT.texture.generateMipmaps = false;\n    this.previousLuminanceRT = new WebGLRenderTarget(this.resolution, this.resolution);\n    this.previousLuminanceRT.texture.name = \"AdaptiveToneMappingPass.pl\";\n    this.previousLuminanceRT.texture.generateMipmaps = false;\n    const pars = {\n      minFilter: LinearMipmapLinearFilter,\n      generateMipmaps: true\n    };\n    this.currentLuminanceRT = new WebGLRenderTarget(this.resolution, this.resolution, pars);\n    this.currentLuminanceRT.texture.name = \"AdaptiveToneMappingPass.cl\";\n    if (this.adaptive) {\n      this.materialToneMap.defines[\"ADAPTED_LUMINANCE\"] = \"\";\n      this.materialToneMap.uniforms.luminanceMap.value = this.luminanceRT.texture;\n    }\n    this.fsQuad.material = new MeshBasicMaterial({\n      color: 7829367\n    });\n    this.materialLuminance.needsUpdate = true;\n    this.materialAdaptiveLum.needsUpdate = true;\n    this.materialToneMap.needsUpdate = true;\n  }\n  setAdaptive(adaptive) {\n    if (adaptive) {\n      this.adaptive = true;\n      this.materialToneMap.defines[\"ADAPTED_LUMINANCE\"] = \"\";\n      this.materialToneMap.uniforms.luminanceMap.value = this.luminanceRT.texture;\n    } else {\n      this.adaptive = false;\n      delete this.materialToneMap.defines[\"ADAPTED_LUMINANCE\"];\n      this.materialToneMap.uniforms.luminanceMap.value = null;\n    }\n    this.materialToneMap.needsUpdate = true;\n  }\n  setAdaptionRate(rate) {\n    if (rate) {\n      this.materialAdaptiveLum.uniforms.tau.value = Math.abs(rate);\n    }\n  }\n  setMinLuminance(minLum) {\n    if (minLum) {\n      this.materialToneMap.uniforms.minLuminance.value = minLum;\n      this.materialAdaptiveLum.uniforms.minLuminance.value = minLum;\n    }\n  }\n  setMaxLuminance(maxLum) {\n    if (maxLum) {\n      this.materialToneMap.uniforms.maxLuminance.value = maxLum;\n    }\n  }\n  setAverageLuminance(avgLum) {\n    if (avgLum) {\n      this.materialToneMap.uniforms.averageLuminance.value = avgLum;\n    }\n  }\n  setMiddleGrey(middleGrey) {\n    if (middleGrey) {\n      this.materialToneMap.uniforms.middleGrey.value = middleGrey;\n    }\n  }\n  dispose() {\n    if (this.luminanceRT) {\n      this.luminanceRT.dispose();\n    }\n    if (this.previousLuminanceRT) {\n      this.previousLuminanceRT.dispose();\n    }\n    if (this.currentLuminanceRT) {\n      this.currentLuminanceRT.dispose();\n    }\n    if (this.materialLuminance) {\n      this.materialLuminance.dispose();\n    }\n    if (this.materialAdaptiveLum) {\n      this.materialAdaptiveLum.dispose();\n    }\n    if (this.materialCopy) {\n      this.materialCopy.dispose();\n    }\n    if (this.materialToneMap) {\n      this.materialToneMap.dispose();\n    }\n  }\n}\nexport { AdaptiveToneMappingPass };", "map": {"version": 3, "names": ["AdaptiveToneMappingPass", "Pass", "constructor", "adaptive", "resolution", "needsInit", "luminanceRT", "previousLuminanceRT", "currentLuminanceRT", "copyShader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyUniforms", "UniformsUtils", "clone", "uniforms", "materialCopy", "ShaderMaterial", "vertexShader", "fragmentShader", "blending", "NoBlending", "depthTest", "materialLuminance", "Luminosity<PERSON><PERSON>er", "adaptLuminanceShader", "defines", "MIP_LEVEL_1X1", "Math", "log", "toFixed", "lastLum", "value", "currentLum", "minLuminance", "delta", "tau", "materialAdaptiveLum", "Object", "assign", "materialToneMap", "ToneMap<PERSON><PERSON>er", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "reset", "texture", "type", "material", "tDiffuse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToScreen", "clear", "dispose", "WebGLRenderTarget", "name", "generateMipmaps", "pars", "minFilter", "LinearMipmapLinearFilter", "luminanceMap", "MeshBasicMaterial", "color", "needsUpdate", "setAdaptive", "setAdaptionRate", "rate", "abs", "setMinLuminance", "minLum", "setMaxLuminance", "maxLum", "maxLuminance", "setAverageLuminance", "avgLum", "averageLuminance", "set<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/postprocessing/AdaptiveToneMappingPass.js"], "sourcesContent": ["import {\n  LinearMipmapLinearFilter,\n  MeshBasicMaterial,\n  NoBlending,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { LuminosityShader } from '../shaders/LuminosityShader'\nimport { ToneMapShader } from '../shaders/ToneMapShader'\n\n/**\n * Generate a texture that represents the luminosity of the current scene, adapted over time\n * to simulate the optic nerve responding to the amount of light it is receiving.\n * Based on a GDC2007 presentation by <PERSON> titled \"Post-Processing Pipeline\"\n *\n * Full-screen tone-mapping shader based on http://www.graphics.cornell.edu/~jaf/publications/sig02_paper.pdf\n */\n\nclass AdaptiveToneMappingPass extends Pass {\n  constructor(adaptive, resolution) {\n    super()\n\n    this.resolution = resolution !== undefined ? resolution : 256\n    this.needsInit = true\n    this.adaptive = adaptive !== undefined ? !!adaptive : true\n\n    this.luminanceRT = null\n    this.previousLuminanceRT = null\n    this.currentLuminanceRT = null\n\n    const copyShader = CopyShader\n\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n\n    this.materialCopy = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      blending: NoBlending,\n      depthTest: false,\n    })\n\n    this.materialLuminance = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(LuminosityShader.uniforms),\n      vertexShader: LuminosityShader.vertexShader,\n      fragmentShader: LuminosityShader.fragmentShader,\n      blending: NoBlending,\n    })\n\n    this.adaptLuminanceShader = {\n      defines: {\n        MIP_LEVEL_1X1: (Math.log(this.resolution) / Math.log(2.0)).toFixed(1),\n      },\n      uniforms: {\n        lastLum: { value: null },\n        currentLum: { value: null },\n        minLuminance: { value: 0.01 },\n        delta: { value: 0.016 },\n        tau: { value: 1.0 },\n      },\n      vertexShader: `varying vec2 vUv;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t\t}`,\n\n      fragmentShader: `varying vec2 vUv;\n\n\t\t\t\tuniform sampler2D lastLum;\n\t\t\t\tuniform sampler2D currentLum;\n\t\t\t\tuniform float minLuminance;\n\t\t\t\tuniform float delta;\n\t\t\t\tuniform float tau;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec4 lastLum = texture2D( lastLum, vUv, MIP_LEVEL_1X1 );\n\t\t\t\t\tvec4 currentLum = texture2D( currentLum, vUv, MIP_LEVEL_1X1 );\n\n\t\t\t\t\tfloat fLastLum = max( minLuminance, lastLum.r );\n\t\t\t\t\tfloat fCurrentLum = max( minLuminance, currentLum.r );\n\n\t\t\t\t\t//The adaption seems to work better in extreme lighting differences\n\t\t\t\t\t//if the input luminance is squared.\n\t\t\t\t\tfCurrentLum *= fCurrentLum;\n\n\t\t\t\t\t// Adapt the luminance using Pattanaik's technique\n\t\t\t\t\tfloat fAdaptedLum = fLastLum + (fCurrentLum - fLastLum) * (1.0 - exp(-delta * tau));\n\t\t\t\t\t// \"fAdaptedLum = sqrt(fAdaptedLum);\n\t\t\t\t\tgl_FragColor.r = fAdaptedLum;\n\t\t\t\t}`,\n    }\n\n    this.materialAdaptiveLum = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(this.adaptLuminanceShader.uniforms),\n      vertexShader: this.adaptLuminanceShader.vertexShader,\n      fragmentShader: this.adaptLuminanceShader.fragmentShader,\n      defines: Object.assign({}, this.adaptLuminanceShader.defines),\n      blending: NoBlending,\n    })\n\n    this.materialToneMap = new ShaderMaterial({\n      uniforms: UniformsUtils.clone(ToneMapShader.uniforms),\n      vertexShader: ToneMapShader.vertexShader,\n      fragmentShader: ToneMapShader.fragmentShader,\n      blending: NoBlending,\n    })\n\n    this.fsQuad = new FullScreenQuad(null)\n  }\n\n  render(renderer, writeBuffer, readBuffer, deltaTime /*, maskActive*/) {\n    if (this.needsInit) {\n      this.reset(renderer)\n\n      this.luminanceRT.texture.type = readBuffer.texture.type\n      this.previousLuminanceRT.texture.type = readBuffer.texture.type\n      this.currentLuminanceRT.texture.type = readBuffer.texture.type\n      this.needsInit = false\n    }\n\n    if (this.adaptive) {\n      //Render the luminance of the current scene into a render target with mipmapping enabled\n      this.fsQuad.material = this.materialLuminance\n      this.materialLuminance.uniforms.tDiffuse.value = readBuffer.texture\n      renderer.setRenderTarget(this.currentLuminanceRT)\n      this.fsQuad.render(renderer)\n\n      //Use the new luminance values, the previous luminance and the frame delta to\n      //adapt the luminance over time.\n      this.fsQuad.material = this.materialAdaptiveLum\n      this.materialAdaptiveLum.uniforms.delta.value = deltaTime\n      this.materialAdaptiveLum.uniforms.lastLum.value = this.previousLuminanceRT.texture\n      this.materialAdaptiveLum.uniforms.currentLum.value = this.currentLuminanceRT.texture\n      renderer.setRenderTarget(this.luminanceRT)\n      this.fsQuad.render(renderer)\n\n      //Copy the new adapted luminance value so that it can be used by the next frame.\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms.tDiffuse.value = this.luminanceRT.texture\n      renderer.setRenderTarget(this.previousLuminanceRT)\n      this.fsQuad.render(renderer)\n    }\n\n    this.fsQuad.material = this.materialToneMap\n    this.materialToneMap.uniforms.tDiffuse.value = readBuffer.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n\n      if (this.clear) renderer.clear()\n\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  reset() {\n    // render targets\n    if (this.luminanceRT) {\n      this.luminanceRT.dispose()\n    }\n\n    if (this.currentLuminanceRT) {\n      this.currentLuminanceRT.dispose()\n    }\n\n    if (this.previousLuminanceRT) {\n      this.previousLuminanceRT.dispose()\n    }\n\n    this.luminanceRT = new WebGLRenderTarget(this.resolution, this.resolution)\n    this.luminanceRT.texture.name = 'AdaptiveToneMappingPass.l'\n    this.luminanceRT.texture.generateMipmaps = false\n\n    this.previousLuminanceRT = new WebGLRenderTarget(this.resolution, this.resolution)\n    this.previousLuminanceRT.texture.name = 'AdaptiveToneMappingPass.pl'\n    this.previousLuminanceRT.texture.generateMipmaps = false\n\n    // We only need mipmapping for the current luminosity because we want a down-sampled version to sample in our adaptive shader\n\n    const pars = { minFilter: LinearMipmapLinearFilter, generateMipmaps: true }\n\n    this.currentLuminanceRT = new WebGLRenderTarget(this.resolution, this.resolution, pars)\n    this.currentLuminanceRT.texture.name = 'AdaptiveToneMappingPass.cl'\n\n    if (this.adaptive) {\n      this.materialToneMap.defines['ADAPTED_LUMINANCE'] = ''\n      this.materialToneMap.uniforms.luminanceMap.value = this.luminanceRT.texture\n    }\n\n    //Put something in the adaptive luminance texture so that the scene can render initially\n    this.fsQuad.material = new MeshBasicMaterial({ color: 0x777777 })\n    this.materialLuminance.needsUpdate = true\n    this.materialAdaptiveLum.needsUpdate = true\n    this.materialToneMap.needsUpdate = true\n    // renderer.render( this.scene, this.camera, this.luminanceRT );\n    // renderer.render( this.scene, this.camera, this.previousLuminanceRT );\n    // renderer.render( this.scene, this.camera, this.currentLuminanceRT );\n  }\n\n  setAdaptive(adaptive) {\n    if (adaptive) {\n      this.adaptive = true\n      this.materialToneMap.defines['ADAPTED_LUMINANCE'] = ''\n      this.materialToneMap.uniforms.luminanceMap.value = this.luminanceRT.texture\n    } else {\n      this.adaptive = false\n      delete this.materialToneMap.defines['ADAPTED_LUMINANCE']\n      this.materialToneMap.uniforms.luminanceMap.value = null\n    }\n\n    this.materialToneMap.needsUpdate = true\n  }\n\n  setAdaptionRate(rate) {\n    if (rate) {\n      this.materialAdaptiveLum.uniforms.tau.value = Math.abs(rate)\n    }\n  }\n\n  setMinLuminance(minLum) {\n    if (minLum) {\n      this.materialToneMap.uniforms.minLuminance.value = minLum\n      this.materialAdaptiveLum.uniforms.minLuminance.value = minLum\n    }\n  }\n\n  setMaxLuminance(maxLum) {\n    if (maxLum) {\n      this.materialToneMap.uniforms.maxLuminance.value = maxLum\n    }\n  }\n\n  setAverageLuminance(avgLum) {\n    if (avgLum) {\n      this.materialToneMap.uniforms.averageLuminance.value = avgLum\n    }\n  }\n\n  setMiddleGrey(middleGrey) {\n    if (middleGrey) {\n      this.materialToneMap.uniforms.middleGrey.value = middleGrey\n    }\n  }\n\n  dispose() {\n    if (this.luminanceRT) {\n      this.luminanceRT.dispose()\n    }\n\n    if (this.previousLuminanceRT) {\n      this.previousLuminanceRT.dispose()\n    }\n\n    if (this.currentLuminanceRT) {\n      this.currentLuminanceRT.dispose()\n    }\n\n    if (this.materialLuminance) {\n      this.materialLuminance.dispose()\n    }\n\n    if (this.materialAdaptiveLum) {\n      this.materialAdaptiveLum.dispose()\n    }\n\n    if (this.materialCopy) {\n      this.materialCopy.dispose()\n    }\n\n    if (this.materialToneMap) {\n      this.materialToneMap.dispose()\n    }\n  }\n}\n\nexport { AdaptiveToneMappingPass }\n"], "mappings": ";;;;;AAqBA,MAAMA,uBAAA,SAAgCC,IAAA,CAAK;EACzCC,YAAYC,QAAA,EAAUC,UAAA,EAAY;IAChC,MAAO;IAEP,KAAKA,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;IAC1D,KAAKC,SAAA,GAAY;IACjB,KAAKF,QAAA,GAAWA,QAAA,KAAa,SAAY,CAAC,CAACA,QAAA,GAAW;IAEtD,KAAKG,WAAA,GAAc;IACnB,KAAKC,mBAAA,GAAsB;IAC3B,KAAKC,kBAAA,GAAqB;IAE1B,MAAMC,UAAA,GAAaC,UAAA;IAEnB,KAAKC,YAAA,GAAeC,aAAA,CAAcC,KAAA,CAAMJ,UAAA,CAAWK,QAAQ;IAE3D,KAAKC,YAAA,GAAe,IAAIC,cAAA,CAAe;MACrCF,QAAA,EAAU,KAAKH,YAAA;MACfM,YAAA,EAAcR,UAAA,CAAWQ,YAAA;MACzBC,cAAA,EAAgBT,UAAA,CAAWS,cAAA;MAC3BC,QAAA,EAAUC,UAAA;MACVC,SAAA,EAAW;IACjB,CAAK;IAED,KAAKC,iBAAA,GAAoB,IAAIN,cAAA,CAAe;MAC1CF,QAAA,EAAUF,aAAA,CAAcC,KAAA,CAAMU,gBAAA,CAAiBT,QAAQ;MACvDG,YAAA,EAAcM,gBAAA,CAAiBN,YAAA;MAC/BC,cAAA,EAAgBK,gBAAA,CAAiBL,cAAA;MACjCC,QAAA,EAAUC;IAChB,CAAK;IAED,KAAKI,oBAAA,GAAuB;MAC1BC,OAAA,EAAS;QACPC,aAAA,GAAgBC,IAAA,CAAKC,GAAA,CAAI,KAAKxB,UAAU,IAAIuB,IAAA,CAAKC,GAAA,CAAI,CAAG,GAAGC,OAAA,CAAQ,CAAC;MACrE;MACDf,QAAA,EAAU;QACRgB,OAAA,EAAS;UAAEC,KAAA,EAAO;QAAM;QACxBC,UAAA,EAAY;UAAED,KAAA,EAAO;QAAM;QAC3BE,YAAA,EAAc;UAAEF,KAAA,EAAO;QAAM;QAC7BG,KAAA,EAAO;UAAEH,KAAA,EAAO;QAAO;QACvBI,GAAA,EAAK;UAAEJ,KAAA,EAAO;QAAK;MACpB;MACDd,YAAA,EAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;MASdC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAyBjB;IAED,KAAKkB,mBAAA,GAAsB,IAAIpB,cAAA,CAAe;MAC5CF,QAAA,EAAUF,aAAA,CAAcC,KAAA,CAAM,KAAKW,oBAAA,CAAqBV,QAAQ;MAChEG,YAAA,EAAc,KAAKO,oBAAA,CAAqBP,YAAA;MACxCC,cAAA,EAAgB,KAAKM,oBAAA,CAAqBN,cAAA;MAC1CO,OAAA,EAASY,MAAA,CAAOC,MAAA,CAAO,CAAE,GAAE,KAAKd,oBAAA,CAAqBC,OAAO;MAC5DN,QAAA,EAAUC;IAChB,CAAK;IAED,KAAKmB,eAAA,GAAkB,IAAIvB,cAAA,CAAe;MACxCF,QAAA,EAAUF,aAAA,CAAcC,KAAA,CAAM2B,aAAA,CAAc1B,QAAQ;MACpDG,YAAA,EAAcuB,aAAA,CAAcvB,YAAA;MAC5BC,cAAA,EAAgBsB,aAAA,CAActB,cAAA;MAC9BC,QAAA,EAAUC;IAChB,CAAK;IAED,KAAKqB,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;EACtC;EAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAYC,SAAA,EAA4B;IACpE,IAAI,KAAK1C,SAAA,EAAW;MAClB,KAAK2C,KAAA,CAAMJ,QAAQ;MAEnB,KAAKtC,WAAA,CAAY2C,OAAA,CAAQC,IAAA,GAAOJ,UAAA,CAAWG,OAAA,CAAQC,IAAA;MACnD,KAAK3C,mBAAA,CAAoB0C,OAAA,CAAQC,IAAA,GAAOJ,UAAA,CAAWG,OAAA,CAAQC,IAAA;MAC3D,KAAK1C,kBAAA,CAAmByC,OAAA,CAAQC,IAAA,GAAOJ,UAAA,CAAWG,OAAA,CAAQC,IAAA;MAC1D,KAAK7C,SAAA,GAAY;IAClB;IAED,IAAI,KAAKF,QAAA,EAAU;MAEjB,KAAKsC,MAAA,CAAOU,QAAA,GAAW,KAAK7B,iBAAA;MAC5B,KAAKA,iBAAA,CAAkBR,QAAA,CAASsC,QAAA,CAASrB,KAAA,GAAQe,UAAA,CAAWG,OAAA;MAC5DL,QAAA,CAASS,eAAA,CAAgB,KAAK7C,kBAAkB;MAChD,KAAKiC,MAAA,CAAOE,MAAA,CAAOC,QAAQ;MAI3B,KAAKH,MAAA,CAAOU,QAAA,GAAW,KAAKf,mBAAA;MAC5B,KAAKA,mBAAA,CAAoBtB,QAAA,CAASoB,KAAA,CAAMH,KAAA,GAAQgB,SAAA;MAChD,KAAKX,mBAAA,CAAoBtB,QAAA,CAASgB,OAAA,CAAQC,KAAA,GAAQ,KAAKxB,mBAAA,CAAoB0C,OAAA;MAC3E,KAAKb,mBAAA,CAAoBtB,QAAA,CAASkB,UAAA,CAAWD,KAAA,GAAQ,KAAKvB,kBAAA,CAAmByC,OAAA;MAC7EL,QAAA,CAASS,eAAA,CAAgB,KAAK/C,WAAW;MACzC,KAAKmC,MAAA,CAAOE,MAAA,CAAOC,QAAQ;MAG3B,KAAKH,MAAA,CAAOU,QAAA,GAAW,KAAKpC,YAAA;MAC5B,KAAKJ,YAAA,CAAayC,QAAA,CAASrB,KAAA,GAAQ,KAAKzB,WAAA,CAAY2C,OAAA;MACpDL,QAAA,CAASS,eAAA,CAAgB,KAAK9C,mBAAmB;MACjD,KAAKkC,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC5B;IAED,KAAKH,MAAA,CAAOU,QAAA,GAAW,KAAKZ,eAAA;IAC5B,KAAKA,eAAA,CAAgBzB,QAAA,CAASsC,QAAA,CAASrB,KAAA,GAAQe,UAAA,CAAWG,OAAA;IAE1D,IAAI,KAAKK,cAAA,EAAgB;MACvBV,QAAA,CAASS,eAAA,CAAgB,IAAI;MAC7B,KAAKZ,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IACjC,OAAW;MACLA,QAAA,CAASS,eAAA,CAAgBR,WAAW;MAEpC,IAAI,KAAKU,KAAA,EAAOX,QAAA,CAASW,KAAA,CAAO;MAEhC,KAAKd,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAC5B;EACF;EAEDI,MAAA,EAAQ;IAEN,IAAI,KAAK1C,WAAA,EAAa;MACpB,KAAKA,WAAA,CAAYkD,OAAA,CAAS;IAC3B;IAED,IAAI,KAAKhD,kBAAA,EAAoB;MAC3B,KAAKA,kBAAA,CAAmBgD,OAAA,CAAS;IAClC;IAED,IAAI,KAAKjD,mBAAA,EAAqB;MAC5B,KAAKA,mBAAA,CAAoBiD,OAAA,CAAS;IACnC;IAED,KAAKlD,WAAA,GAAc,IAAImD,iBAAA,CAAkB,KAAKrD,UAAA,EAAY,KAAKA,UAAU;IACzE,KAAKE,WAAA,CAAY2C,OAAA,CAAQS,IAAA,GAAO;IAChC,KAAKpD,WAAA,CAAY2C,OAAA,CAAQU,eAAA,GAAkB;IAE3C,KAAKpD,mBAAA,GAAsB,IAAIkD,iBAAA,CAAkB,KAAKrD,UAAA,EAAY,KAAKA,UAAU;IACjF,KAAKG,mBAAA,CAAoB0C,OAAA,CAAQS,IAAA,GAAO;IACxC,KAAKnD,mBAAA,CAAoB0C,OAAA,CAAQU,eAAA,GAAkB;IAInD,MAAMC,IAAA,GAAO;MAAEC,SAAA,EAAWC,wBAAA;MAA0BH,eAAA,EAAiB;IAAM;IAE3E,KAAKnD,kBAAA,GAAqB,IAAIiD,iBAAA,CAAkB,KAAKrD,UAAA,EAAY,KAAKA,UAAA,EAAYwD,IAAI;IACtF,KAAKpD,kBAAA,CAAmByC,OAAA,CAAQS,IAAA,GAAO;IAEvC,IAAI,KAAKvD,QAAA,EAAU;MACjB,KAAKoC,eAAA,CAAgBd,OAAA,CAAQ,mBAAmB,IAAI;MACpD,KAAKc,eAAA,CAAgBzB,QAAA,CAASiD,YAAA,CAAahC,KAAA,GAAQ,KAAKzB,WAAA,CAAY2C,OAAA;IACrE;IAGD,KAAKR,MAAA,CAAOU,QAAA,GAAW,IAAIa,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAA,CAAU;IAChE,KAAK3C,iBAAA,CAAkB4C,WAAA,GAAc;IACrC,KAAK9B,mBAAA,CAAoB8B,WAAA,GAAc;IACvC,KAAK3B,eAAA,CAAgB2B,WAAA,GAAc;EAIpC;EAEDC,YAAYhE,QAAA,EAAU;IACpB,IAAIA,QAAA,EAAU;MACZ,KAAKA,QAAA,GAAW;MAChB,KAAKoC,eAAA,CAAgBd,OAAA,CAAQ,mBAAmB,IAAI;MACpD,KAAKc,eAAA,CAAgBzB,QAAA,CAASiD,YAAA,CAAahC,KAAA,GAAQ,KAAKzB,WAAA,CAAY2C,OAAA;IAC1E,OAAW;MACL,KAAK9C,QAAA,GAAW;MAChB,OAAO,KAAKoC,eAAA,CAAgBd,OAAA,CAAQ,mBAAmB;MACvD,KAAKc,eAAA,CAAgBzB,QAAA,CAASiD,YAAA,CAAahC,KAAA,GAAQ;IACpD;IAED,KAAKQ,eAAA,CAAgB2B,WAAA,GAAc;EACpC;EAEDE,gBAAgBC,IAAA,EAAM;IACpB,IAAIA,IAAA,EAAM;MACR,KAAKjC,mBAAA,CAAoBtB,QAAA,CAASqB,GAAA,CAAIJ,KAAA,GAAQJ,IAAA,CAAK2C,GAAA,CAAID,IAAI;IAC5D;EACF;EAEDE,gBAAgBC,MAAA,EAAQ;IACtB,IAAIA,MAAA,EAAQ;MACV,KAAKjC,eAAA,CAAgBzB,QAAA,CAASmB,YAAA,CAAaF,KAAA,GAAQyC,MAAA;MACnD,KAAKpC,mBAAA,CAAoBtB,QAAA,CAASmB,YAAA,CAAaF,KAAA,GAAQyC,MAAA;IACxD;EACF;EAEDC,gBAAgBC,MAAA,EAAQ;IACtB,IAAIA,MAAA,EAAQ;MACV,KAAKnC,eAAA,CAAgBzB,QAAA,CAAS6D,YAAA,CAAa5C,KAAA,GAAQ2C,MAAA;IACpD;EACF;EAEDE,oBAAoBC,MAAA,EAAQ;IAC1B,IAAIA,MAAA,EAAQ;MACV,KAAKtC,eAAA,CAAgBzB,QAAA,CAASgE,gBAAA,CAAiB/C,KAAA,GAAQ8C,MAAA;IACxD;EACF;EAEDE,cAAcC,UAAA,EAAY;IACxB,IAAIA,UAAA,EAAY;MACd,KAAKzC,eAAA,CAAgBzB,QAAA,CAASkE,UAAA,CAAWjD,KAAA,GAAQiD,UAAA;IAClD;EACF;EAEDxB,QAAA,EAAU;IACR,IAAI,KAAKlD,WAAA,EAAa;MACpB,KAAKA,WAAA,CAAYkD,OAAA,CAAS;IAC3B;IAED,IAAI,KAAKjD,mBAAA,EAAqB;MAC5B,KAAKA,mBAAA,CAAoBiD,OAAA,CAAS;IACnC;IAED,IAAI,KAAKhD,kBAAA,EAAoB;MAC3B,KAAKA,kBAAA,CAAmBgD,OAAA,CAAS;IAClC;IAED,IAAI,KAAKlC,iBAAA,EAAmB;MAC1B,KAAKA,iBAAA,CAAkBkC,OAAA,CAAS;IACjC;IAED,IAAI,KAAKpB,mBAAA,EAAqB;MAC5B,KAAKA,mBAAA,CAAoBoB,OAAA,CAAS;IACnC;IAED,IAAI,KAAKzC,YAAA,EAAc;MACrB,KAAKA,YAAA,CAAayC,OAAA,CAAS;IAC5B;IAED,IAAI,KAAKjB,eAAA,EAAiB;MACxB,KAAKA,eAAA,CAAgBiB,OAAA,CAAS;IAC/B;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}