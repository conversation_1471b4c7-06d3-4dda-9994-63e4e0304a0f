{"ast": null, "code": "import { Matrix3, Vector3, Matrix4 } from \"three\";\nimport { VolumeSlice } from \"./VolumeSlice.js\";\nclass Volume {\n  constructor(xLength, yLength, zLength, type, arrayBuffer) {\n    if (xLength !== void 0) {\n      this.xLength = Number(xLength) || 1;\n      this.yLength = Number(yLength) || 1;\n      this.zLength = Number(zLength) || 1;\n      this.axisOrder = [\"x\", \"y\", \"z\"];\n      switch (type) {\n        case \"Uint8\":\n        case \"uint8\":\n        case \"uchar\":\n        case \"unsigned char\":\n        case \"uint8_t\":\n          this.data = new Uint8Array(arrayBuffer);\n          break;\n        case \"Int8\":\n        case \"int8\":\n        case \"signed char\":\n        case \"int8_t\":\n          this.data = new Int8Array(arrayBuffer);\n          break;\n        case \"Int16\":\n        case \"int16\":\n        case \"short\":\n        case \"short int\":\n        case \"signed short\":\n        case \"signed short int\":\n        case \"int16_t\":\n          this.data = new Int16Array(arrayBuffer);\n          break;\n        case \"Uint16\":\n        case \"uint16\":\n        case \"ushort\":\n        case \"unsigned short\":\n        case \"unsigned short int\":\n        case \"uint16_t\":\n          this.data = new Uint16Array(arrayBuffer);\n          break;\n        case \"Int32\":\n        case \"int32\":\n        case \"int\":\n        case \"signed int\":\n        case \"int32_t\":\n          this.data = new Int32Array(arrayBuffer);\n          break;\n        case \"Uint32\":\n        case \"uint32\":\n        case \"uint\":\n        case \"unsigned int\":\n        case \"uint32_t\":\n          this.data = new Uint32Array(arrayBuffer);\n          break;\n        case \"longlong\":\n        case \"long long\":\n        case \"long long int\":\n        case \"signed long long\":\n        case \"signed long long int\":\n        case \"int64\":\n        case \"int64_t\":\n        case \"ulonglong\":\n        case \"unsigned long long\":\n        case \"unsigned long long int\":\n        case \"uint64\":\n        case \"uint64_t\":\n          throw new Error(\"Error in Volume constructor : this type is not supported in JavaScript\");\n        case \"Float32\":\n        case \"float32\":\n        case \"float\":\n          this.data = new Float32Array(arrayBuffer);\n          break;\n        case \"Float64\":\n        case \"float64\":\n        case \"double\":\n          this.data = new Float64Array(arrayBuffer);\n          break;\n        default:\n          this.data = new Uint8Array(arrayBuffer);\n      }\n      if (this.data.length !== this.xLength * this.yLength * this.zLength) {\n        throw new Error(\"Error in Volume constructor, lengths are not matching arrayBuffer size\");\n      }\n    }\n    this.spacing = [1, 1, 1];\n    this.offset = [0, 0, 0];\n    this.matrix = new Matrix3();\n    this.matrix.identity();\n    let lowerThreshold = -Infinity;\n    Object.defineProperty(this, \"lowerThreshold\", {\n      get: function () {\n        return lowerThreshold;\n      },\n      set: function (value) {\n        lowerThreshold = value;\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true;\n        });\n      }\n    });\n    let upperThreshold = Infinity;\n    Object.defineProperty(this, \"upperThreshold\", {\n      get: function () {\n        return upperThreshold;\n      },\n      set: function (value) {\n        upperThreshold = value;\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true;\n        });\n      }\n    });\n    this.sliceList = [];\n    this.segmentation = false;\n  }\n  /**\n   * @member {Function} getData Shortcut for data[access(i,j,k)]\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  value in the data array\n   */\n  getData(i, j, k) {\n    return this.data[k * this.xLength * this.yLength + j * this.xLength + i];\n  }\n  /**\n   * @member {Function} access compute the index in the data array corresponding to the given coordinates in IJK system\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  index\n   */\n  access(i, j, k) {\n    return k * this.xLength * this.yLength + j * this.xLength + i;\n  }\n  /**\n   * @member {Function} reverseAccess Retrieve the IJK coordinates of the voxel corresponding of the given index in the data\n   * @memberof Volume\n   * @param {number} index index of the voxel\n   * @returns {Array}  [x,y,z]\n   */\n  reverseAccess(index) {\n    const z = Math.floor(index / (this.yLength * this.xLength));\n    const y = Math.floor((index - z * this.yLength * this.xLength) / this.xLength);\n    const x = index - z * this.yLength * this.xLength - y * this.xLength;\n    return [x, y, z];\n  }\n  /**\n   * @member {Function} map Apply a function to all the voxels, be careful, the value will be replaced\n   * @memberof Volume\n   * @param {Function} functionToMap A function to apply to every voxel, will be called with the following parameters :\n   *                                 value of the voxel\n   *                                 index of the voxel\n   *                                 the data (TypedArray)\n   * @param {Object}   context    You can specify a context in which call the function, default if this Volume\n   * @returns {Volume}   this\n   */\n  map(functionToMap, context) {\n    const length = this.data.length;\n    context = context || this;\n    for (let i = 0; i < length; i++) {\n      this.data[i] = functionToMap.call(context, this.data[i], i, this.data);\n    }\n    return this;\n  }\n  /**\n   * @member {Function} extractPerpendicularPlane Compute the orientation of the slice and returns all the information relative to the geometry such as sliceAccess, the plane matrix (orientation and position in RAS coordinate) and the dimensions of the plane in both coordinate system.\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {Object} an object containing all the usefull information on the geometry of the slice\n   */\n  extractPerpendicularPlane(axis, RASIndex) {\n    let firstSpacing, secondSpacing, positionOffset, IJKIndex;\n    const axisInIJK = new Vector3(),\n      firstDirection = new Vector3(),\n      secondDirection = new Vector3(),\n      planeMatrix = new Matrix4().identity(),\n      volume = this;\n    const dimensions = new Vector3(this.xLength, this.yLength, this.zLength);\n    switch (axis) {\n      case \"x\":\n        axisInIJK.set(1, 0, 0);\n        firstDirection.set(0, 0, -1);\n        secondDirection.set(0, -1, 0);\n        firstSpacing = this.spacing[this.axisOrder.indexOf(\"z\")];\n        secondSpacing = this.spacing[this.axisOrder.indexOf(\"y\")];\n        IJKIndex = new Vector3(RASIndex, 0, 0);\n        planeMatrix.multiply(new Matrix4().makeRotationY(Math.PI / 2));\n        positionOffset = (volume.RASDimensions[0] - 1) / 2;\n        planeMatrix.setPosition(new Vector3(RASIndex - positionOffset, 0, 0));\n        break;\n      case \"y\":\n        axisInIJK.set(0, 1, 0);\n        firstDirection.set(1, 0, 0);\n        secondDirection.set(0, 0, 1);\n        firstSpacing = this.spacing[this.axisOrder.indexOf(\"x\")];\n        secondSpacing = this.spacing[this.axisOrder.indexOf(\"z\")];\n        IJKIndex = new Vector3(0, RASIndex, 0);\n        planeMatrix.multiply(new Matrix4().makeRotationX(-Math.PI / 2));\n        positionOffset = (volume.RASDimensions[1] - 1) / 2;\n        planeMatrix.setPosition(new Vector3(0, RASIndex - positionOffset, 0));\n        break;\n      case \"z\":\n      default:\n        axisInIJK.set(0, 0, 1);\n        firstDirection.set(1, 0, 0);\n        secondDirection.set(0, -1, 0);\n        firstSpacing = this.spacing[this.axisOrder.indexOf(\"x\")];\n        secondSpacing = this.spacing[this.axisOrder.indexOf(\"y\")];\n        IJKIndex = new Vector3(0, 0, RASIndex);\n        positionOffset = (volume.RASDimensions[2] - 1) / 2;\n        planeMatrix.setPosition(new Vector3(0, 0, RASIndex - positionOffset));\n        break;\n    }\n    let iLength, jLength;\n    if (!this.segmentation) {\n      firstDirection.applyMatrix4(volume.inverseMatrix).normalize();\n      secondDirection.applyMatrix4(volume.inverseMatrix).normalize();\n      axisInIJK.applyMatrix4(volume.inverseMatrix).normalize();\n    }\n    firstDirection.arglet = \"i\";\n    secondDirection.arglet = \"j\";\n    iLength = Math.floor(Math.abs(firstDirection.dot(dimensions)));\n    jLength = Math.floor(Math.abs(secondDirection.dot(dimensions)));\n    const planeWidth = Math.abs(iLength * firstSpacing);\n    const planeHeight = Math.abs(jLength * secondSpacing);\n    IJKIndex = Math.abs(Math.round(IJKIndex.applyMatrix4(volume.inverseMatrix).dot(axisInIJK)));\n    const base = [new Vector3(1, 0, 0), new Vector3(0, 1, 0), new Vector3(0, 0, 1)];\n    const iDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[0])) > 0.9;\n    });\n    const jDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[1])) > 0.9;\n    });\n    const kDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[2])) > 0.9;\n    });\n    function sliceAccess(i, j) {\n      const si = iDirection === axisInIJK ? IJKIndex : iDirection.arglet === \"i\" ? i : j;\n      const sj = jDirection === axisInIJK ? IJKIndex : jDirection.arglet === \"i\" ? i : j;\n      const sk = kDirection === axisInIJK ? IJKIndex : kDirection.arglet === \"i\" ? i : j;\n      const accessI = iDirection.dot(base[0]) > 0 ? si : volume.xLength - 1 - si;\n      const accessJ = jDirection.dot(base[1]) > 0 ? sj : volume.yLength - 1 - sj;\n      const accessK = kDirection.dot(base[2]) > 0 ? sk : volume.zLength - 1 - sk;\n      return volume.access(accessI, accessJ, accessK);\n    }\n    return {\n      iLength,\n      jLength,\n      sliceAccess,\n      matrix: planeMatrix,\n      planeWidth,\n      planeHeight\n    };\n  }\n  /**\n   * @member {Function} extractSlice Returns a slice corresponding to the given axis and index\n   *                        The coordinate are given in the Right Anterior Superior coordinate format\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {VolumeSlice} the extracted slice\n   */\n  extractSlice(axis, index) {\n    const slice = new VolumeSlice(this, index, axis);\n    this.sliceList.push(slice);\n    return slice;\n  }\n  /**\n   * @member {Function} repaintAllSlices Call repaint on all the slices extracted from this volume\n   * @see VolumeSlice.repaint\n   * @memberof Volume\n   * @returns {Volume} this\n   */\n  repaintAllSlices() {\n    this.sliceList.forEach(function (slice) {\n      slice.repaint();\n    });\n    return this;\n  }\n  /**\n   * @member {Function} computeMinMax Compute the minimum and the maximum of the data in the volume\n   * @memberof Volume\n   * @returns {Array} [min,max]\n   */\n  computeMinMax() {\n    let min = Infinity;\n    let max = -Infinity;\n    const datasize = this.data.length;\n    let i = 0;\n    for (i = 0; i < datasize; i++) {\n      if (!isNaN(this.data[i])) {\n        const value = this.data[i];\n        min = Math.min(min, value);\n        max = Math.max(max, value);\n      }\n    }\n    this.min = min;\n    this.max = max;\n    return [min, max];\n  }\n}\nexport { Volume };", "map": {"version": 3, "names": ["Volume", "constructor", "xLength", "y<PERSON><PERSON><PERSON>", "z<PERSON>ength", "type", "arrayBuffer", "Number", "axisOrder", "data", "Uint8Array", "Int8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Error", "Float32Array", "Float64Array", "length", "spacing", "offset", "matrix", "Matrix3", "identity", "lowerThreshold", "Infinity", "Object", "defineProperty", "get", "set", "value", "sliceList", "for<PERSON>ach", "slice", "geometryNeedsUpdate", "upperThreshold", "segmentation", "getData", "i", "j", "k", "access", "reverseAccess", "index", "z", "Math", "floor", "y", "x", "map", "functionToMap", "context", "call", "extractPerpendicularPlane", "axis", "RASIndex", "firstSpacing", "secondSpacing", "positionOffset", "IJKIndex", "axisInIJK", "Vector3", "firstDirection", "secondDirection", "planeMatrix", "Matrix4", "volume", "dimensions", "indexOf", "multiply", "makeRotationY", "PI", "RASDimensions", "setPosition", "makeRotationX", "i<PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "applyMatrix4", "inverseMatrix", "normalize", "arglet", "abs", "dot", "planeWidth", "planeHeight", "round", "base", "iDirection", "find", "jDirection", "kDirection", "sliceAccess", "si", "sj", "sk", "accessI", "accessJ", "accessK", "extractSlice", "VolumeSlice", "push", "repaintAllSlices", "repaint", "computeMinMax", "min", "max", "datasize", "isNaN"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/misc/Volume.js"], "sourcesContent": ["import { Matrix3, Matrix4, Vector3 } from 'three'\nimport { VolumeSlice } from '../misc/VolumeSlice'\n\n/**\n * This class had been written to handle the output of the NRRD loader.\n * It contains a volume of data and informations about it.\n * For now it only handles 3 dimensional data.\n * See the webgl_loader_nrrd.html example and the loaderNRRD.js file to see how to use this class.\n * @class\n * @param   {number}        xLength         Width of the volume\n * @param   {number}        yLength         Length of the volume\n * @param   {number}        zLength         Depth of the volume\n * @param   {string}        type            The type of data (uint8, uint16, ...)\n * @param   {ArrayBuffer}   arrayBuffer     The buffer with volume data\n */\nclass Volume {\n  constructor(xLength, yLength, zLength, type, arrayBuffer) {\n    if (xLength !== undefined) {\n      /**\n       * @member {number} xLength Width of the volume in the IJK coordinate system\n       */\n      this.xLength = Number(xLength) || 1\n      /**\n       * @member {number} yLength Height of the volume in the IJK coordinate system\n       */\n      this.yLength = Number(yLength) || 1\n      /**\n       * @member {number} zLength Depth of the volume in the IJK coordinate system\n       */\n      this.zLength = Number(zLength) || 1\n      /**\n       * @member {Array<string>} The order of the Axis dictated by the NRRD header\n       */\n      this.axisOrder = ['x', 'y', 'z']\n      /**\n       * @member {TypedArray} data Data of the volume\n       */\n\n      switch (type) {\n        case 'Uint8':\n        case 'uint8':\n        case 'uchar':\n        case 'unsigned char':\n        case 'uint8_t':\n          this.data = new Uint8Array(arrayBuffer)\n          break\n        case 'Int8':\n        case 'int8':\n        case 'signed char':\n        case 'int8_t':\n          this.data = new Int8Array(arrayBuffer)\n          break\n        case 'Int16':\n        case 'int16':\n        case 'short':\n        case 'short int':\n        case 'signed short':\n        case 'signed short int':\n        case 'int16_t':\n          this.data = new Int16Array(arrayBuffer)\n          break\n        case 'Uint16':\n        case 'uint16':\n        case 'ushort':\n        case 'unsigned short':\n        case 'unsigned short int':\n        case 'uint16_t':\n          this.data = new Uint16Array(arrayBuffer)\n          break\n        case 'Int32':\n        case 'int32':\n        case 'int':\n        case 'signed int':\n        case 'int32_t':\n          this.data = new Int32Array(arrayBuffer)\n          break\n        case 'Uint32':\n        case 'uint32':\n        case 'uint':\n        case 'unsigned int':\n        case 'uint32_t':\n          this.data = new Uint32Array(arrayBuffer)\n          break\n        case 'longlong':\n        case 'long long':\n        case 'long long int':\n        case 'signed long long':\n        case 'signed long long int':\n        case 'int64':\n        case 'int64_t':\n        case 'ulonglong':\n        case 'unsigned long long':\n        case 'unsigned long long int':\n        case 'uint64':\n        case 'uint64_t':\n          throw new Error('Error in Volume constructor : this type is not supported in JavaScript')\n          break\n        case 'Float32':\n        case 'float32':\n        case 'float':\n          this.data = new Float32Array(arrayBuffer)\n          break\n        case 'Float64':\n        case 'float64':\n        case 'double':\n          this.data = new Float64Array(arrayBuffer)\n          break\n        default:\n          this.data = new Uint8Array(arrayBuffer)\n      }\n\n      if (this.data.length !== this.xLength * this.yLength * this.zLength) {\n        throw new Error('Error in Volume constructor, lengths are not matching arrayBuffer size')\n      }\n    }\n\n    /**\n     * @member {Array}  spacing Spacing to apply to the volume from IJK to RAS coordinate system\n     */\n    this.spacing = [1, 1, 1]\n    /**\n     * @member {Array}  offset Offset of the volume in the RAS coordinate system\n     */\n    this.offset = [0, 0, 0]\n    /**\n     * @member {Martrix3} matrix The IJK to RAS matrix\n     */\n    this.matrix = new Matrix3()\n    this.matrix.identity()\n    /**\n     * @member {Martrix3} inverseMatrix The RAS to IJK matrix\n     */\n    /**\n     * @member {number} lowerThreshold The voxels with values under this threshold won't appear in the slices.\n     *                      If changed, geometryNeedsUpdate is automatically set to true on all the slices associated to this volume\n     */\n    let lowerThreshold = -Infinity\n    Object.defineProperty(this, 'lowerThreshold', {\n      get: function () {\n        return lowerThreshold\n      },\n      set: function (value) {\n        lowerThreshold = value\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true\n        })\n      },\n    })\n    /**\n     * @member {number} upperThreshold The voxels with values over this threshold won't appear in the slices.\n     *                      If changed, geometryNeedsUpdate is automatically set to true on all the slices associated to this volume\n     */\n    let upperThreshold = Infinity\n    Object.defineProperty(this, 'upperThreshold', {\n      get: function () {\n        return upperThreshold\n      },\n      set: function (value) {\n        upperThreshold = value\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true\n        })\n      },\n    })\n\n    /**\n     * @member {Array} sliceList The list of all the slices associated to this volume\n     */\n    this.sliceList = []\n\n    /**\n     * @member {boolean} segmentation in segmentation mode, it can load 16-bits nrrds correctly\n     */\n    this.segmentation = false\n\n    /**\n     * @member {Array} RASDimensions This array holds the dimensions of the volume in the RAS space\n     */\n  }\n\n  /**\n   * @member {Function} getData Shortcut for data[access(i,j,k)]\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  value in the data array\n   */\n  getData(i, j, k) {\n    return this.data[k * this.xLength * this.yLength + j * this.xLength + i]\n  }\n\n  /**\n   * @member {Function} access compute the index in the data array corresponding to the given coordinates in IJK system\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  index\n   */\n  access(i, j, k) {\n    return k * this.xLength * this.yLength + j * this.xLength + i\n  }\n\n  /**\n   * @member {Function} reverseAccess Retrieve the IJK coordinates of the voxel corresponding of the given index in the data\n   * @memberof Volume\n   * @param {number} index index of the voxel\n   * @returns {Array}  [x,y,z]\n   */\n  reverseAccess(index) {\n    const z = Math.floor(index / (this.yLength * this.xLength))\n    const y = Math.floor((index - z * this.yLength * this.xLength) / this.xLength)\n    const x = index - z * this.yLength * this.xLength - y * this.xLength\n    return [x, y, z]\n  }\n\n  /**\n   * @member {Function} map Apply a function to all the voxels, be careful, the value will be replaced\n   * @memberof Volume\n   * @param {Function} functionToMap A function to apply to every voxel, will be called with the following parameters :\n   *                                 value of the voxel\n   *                                 index of the voxel\n   *                                 the data (TypedArray)\n   * @param {Object}   context    You can specify a context in which call the function, default if this Volume\n   * @returns {Volume}   this\n   */\n  map(functionToMap, context) {\n    const length = this.data.length\n    context = context || this\n\n    for (let i = 0; i < length; i++) {\n      this.data[i] = functionToMap.call(context, this.data[i], i, this.data)\n    }\n\n    return this\n  }\n\n  /**\n   * @member {Function} extractPerpendicularPlane Compute the orientation of the slice and returns all the information relative to the geometry such as sliceAccess, the plane matrix (orientation and position in RAS coordinate) and the dimensions of the plane in both coordinate system.\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {Object} an object containing all the usefull information on the geometry of the slice\n   */\n  extractPerpendicularPlane(axis, RASIndex) {\n    let firstSpacing, secondSpacing, positionOffset, IJKIndex\n\n    const axisInIJK = new Vector3(),\n      firstDirection = new Vector3(),\n      secondDirection = new Vector3(),\n      planeMatrix = new Matrix4().identity(),\n      volume = this\n\n    const dimensions = new Vector3(this.xLength, this.yLength, this.zLength)\n\n    switch (axis) {\n      case 'x':\n        axisInIJK.set(1, 0, 0)\n        firstDirection.set(0, 0, -1)\n        secondDirection.set(0, -1, 0)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('z')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('y')]\n        IJKIndex = new Vector3(RASIndex, 0, 0)\n\n        planeMatrix.multiply(new Matrix4().makeRotationY(Math.PI / 2))\n        positionOffset = (volume.RASDimensions[0] - 1) / 2\n        planeMatrix.setPosition(new Vector3(RASIndex - positionOffset, 0, 0))\n        break\n      case 'y':\n        axisInIJK.set(0, 1, 0)\n        firstDirection.set(1, 0, 0)\n        secondDirection.set(0, 0, 1)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('x')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('z')]\n        IJKIndex = new Vector3(0, RASIndex, 0)\n\n        planeMatrix.multiply(new Matrix4().makeRotationX(-Math.PI / 2))\n        positionOffset = (volume.RASDimensions[1] - 1) / 2\n        planeMatrix.setPosition(new Vector3(0, RASIndex - positionOffset, 0))\n        break\n      case 'z':\n      default:\n        axisInIJK.set(0, 0, 1)\n        firstDirection.set(1, 0, 0)\n        secondDirection.set(0, -1, 0)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('x')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('y')]\n        IJKIndex = new Vector3(0, 0, RASIndex)\n\n        positionOffset = (volume.RASDimensions[2] - 1) / 2\n        planeMatrix.setPosition(new Vector3(0, 0, RASIndex - positionOffset))\n        break\n    }\n\n    let iLength, jLength\n\n    if (!this.segmentation) {\n      firstDirection.applyMatrix4(volume.inverseMatrix).normalize()\n      secondDirection.applyMatrix4(volume.inverseMatrix).normalize()\n      axisInIJK.applyMatrix4(volume.inverseMatrix).normalize()\n    }\n    firstDirection.arglet = 'i'\n    secondDirection.arglet = 'j'\n    iLength = Math.floor(Math.abs(firstDirection.dot(dimensions)))\n    jLength = Math.floor(Math.abs(secondDirection.dot(dimensions)))\n    const planeWidth = Math.abs(iLength * firstSpacing)\n    const planeHeight = Math.abs(jLength * secondSpacing)\n\n    IJKIndex = Math.abs(Math.round(IJKIndex.applyMatrix4(volume.inverseMatrix).dot(axisInIJK)))\n    const base = [new Vector3(1, 0, 0), new Vector3(0, 1, 0), new Vector3(0, 0, 1)]\n    const iDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[0])) > 0.9\n    })\n    const jDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[1])) > 0.9\n    })\n    const kDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[2])) > 0.9\n    })\n\n    function sliceAccess(i, j) {\n      const si = iDirection === axisInIJK ? IJKIndex : iDirection.arglet === 'i' ? i : j\n      const sj = jDirection === axisInIJK ? IJKIndex : jDirection.arglet === 'i' ? i : j\n      const sk = kDirection === axisInIJK ? IJKIndex : kDirection.arglet === 'i' ? i : j\n\n      // invert indices if necessary\n\n      const accessI = iDirection.dot(base[0]) > 0 ? si : volume.xLength - 1 - si\n      const accessJ = jDirection.dot(base[1]) > 0 ? sj : volume.yLength - 1 - sj\n      const accessK = kDirection.dot(base[2]) > 0 ? sk : volume.zLength - 1 - sk\n\n      return volume.access(accessI, accessJ, accessK)\n    }\n\n    return {\n      iLength: iLength,\n      jLength: jLength,\n      sliceAccess: sliceAccess,\n      matrix: planeMatrix,\n      planeWidth: planeWidth,\n      planeHeight: planeHeight,\n    }\n  }\n\n  /**\n   * @member {Function} extractSlice Returns a slice corresponding to the given axis and index\n   *                        The coordinate are given in the Right Anterior Superior coordinate format\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {VolumeSlice} the extracted slice\n   */\n  extractSlice(axis, index) {\n    const slice = new VolumeSlice(this, index, axis)\n    this.sliceList.push(slice)\n    return slice\n  }\n\n  /**\n   * @member {Function} repaintAllSlices Call repaint on all the slices extracted from this volume\n   * @see VolumeSlice.repaint\n   * @memberof Volume\n   * @returns {Volume} this\n   */\n  repaintAllSlices() {\n    this.sliceList.forEach(function (slice) {\n      slice.repaint()\n    })\n\n    return this\n  }\n\n  /**\n   * @member {Function} computeMinMax Compute the minimum and the maximum of the data in the volume\n   * @memberof Volume\n   * @returns {Array} [min,max]\n   */\n  computeMinMax() {\n    let min = Infinity\n    let max = -Infinity\n\n    // buffer the length\n    const datasize = this.data.length\n\n    let i = 0\n\n    for (i = 0; i < datasize; i++) {\n      if (!isNaN(this.data[i])) {\n        const value = this.data[i]\n        min = Math.min(min, value)\n        max = Math.max(max, value)\n      }\n    }\n\n    this.min = min\n    this.max = max\n\n    return [min, max]\n  }\n}\n\nexport { Volume }\n"], "mappings": ";;AAeA,MAAMA,MAAA,CAAO;EACXC,YAAYC,OAAA,EAASC,OAAA,EAASC,OAAA,EAASC,IAAA,EAAMC,WAAA,EAAa;IACxD,IAAIJ,OAAA,KAAY,QAAW;MAIzB,KAAKA,OAAA,GAAUK,MAAA,CAAOL,OAAO,KAAK;MAIlC,KAAKC,OAAA,GAAUI,MAAA,CAAOJ,OAAO,KAAK;MAIlC,KAAKC,OAAA,GAAUG,MAAA,CAAOH,OAAO,KAAK;MAIlC,KAAKI,SAAA,GAAY,CAAC,KAAK,KAAK,GAAG;MAK/B,QAAQH,IAAA;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKI,IAAA,GAAO,IAAIC,UAAA,CAAWJ,WAAW;UACtC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAIE,SAAA,CAAUL,WAAW;UACrC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAIG,UAAA,CAAWN,WAAW;UACtC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAII,WAAA,CAAYP,WAAW;UACvC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAIK,UAAA,CAAWR,WAAW;UACtC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAIM,WAAA,CAAYT,WAAW;UACvC;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACH,MAAM,IAAIU,KAAA,CAAM,wEAAwE;QAE1F,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKP,IAAA,GAAO,IAAIQ,YAAA,CAAaX,WAAW;UACxC;QACF,KAAK;QACL,KAAK;QACL,KAAK;UACH,KAAKG,IAAA,GAAO,IAAIS,YAAA,CAAaZ,WAAW;UACxC;QACF;UACE,KAAKG,IAAA,GAAO,IAAIC,UAAA,CAAWJ,WAAW;MACzC;MAED,IAAI,KAAKG,IAAA,CAAKU,MAAA,KAAW,KAAKjB,OAAA,GAAU,KAAKC,OAAA,GAAU,KAAKC,OAAA,EAAS;QACnE,MAAM,IAAIY,KAAA,CAAM,wEAAwE;MACzF;IACF;IAKD,KAAKI,OAAA,GAAU,CAAC,GAAG,GAAG,CAAC;IAIvB,KAAKC,MAAA,GAAS,CAAC,GAAG,GAAG,CAAC;IAItB,KAAKC,MAAA,GAAS,IAAIC,OAAA,CAAS;IAC3B,KAAKD,MAAA,CAAOE,QAAA,CAAU;IAQtB,IAAIC,cAAA,GAAiB,CAAAC,QAAA;IACrBC,MAAA,CAAOC,cAAA,CAAe,MAAM,kBAAkB;MAC5CC,GAAA,EAAK,SAAAA,CAAA,EAAY;QACf,OAAOJ,cAAA;MACR;MACDK,GAAA,EAAK,SAAAA,CAAUC,KAAA,EAAO;QACpBN,cAAA,GAAiBM,KAAA;QACjB,KAAKC,SAAA,CAAUC,OAAA,CAAQ,UAAUC,KAAA,EAAO;UACtCA,KAAA,CAAMC,mBAAA,GAAsB;QACtC,CAAS;MACF;IACP,CAAK;IAKD,IAAIC,cAAA,GAAiBV,QAAA;IACrBC,MAAA,CAAOC,cAAA,CAAe,MAAM,kBAAkB;MAC5CC,GAAA,EAAK,SAAAA,CAAA,EAAY;QACf,OAAOO,cAAA;MACR;MACDN,GAAA,EAAK,SAAAA,CAAUC,KAAA,EAAO;QACpBK,cAAA,GAAiBL,KAAA;QACjB,KAAKC,SAAA,CAAUC,OAAA,CAAQ,UAAUC,KAAA,EAAO;UACtCA,KAAA,CAAMC,mBAAA,GAAsB;QACtC,CAAS;MACF;IACP,CAAK;IAKD,KAAKH,SAAA,GAAY,EAAE;IAKnB,KAAKK,YAAA,GAAe;EAKrB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDC,QAAQC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IACf,OAAO,KAAKhC,IAAA,CAAKgC,CAAA,GAAI,KAAKvC,OAAA,GAAU,KAAKC,OAAA,GAAUqC,CAAA,GAAI,KAAKtC,OAAA,GAAUqC,CAAC;EACxE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDG,OAAOH,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;IACd,OAAOA,CAAA,GAAI,KAAKvC,OAAA,GAAU,KAAKC,OAAA,GAAUqC,CAAA,GAAI,KAAKtC,OAAA,GAAUqC,CAAA;EAC7D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDI,cAAcC,KAAA,EAAO;IACnB,MAAMC,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAMH,KAAA,IAAS,KAAKzC,OAAA,GAAU,KAAKD,OAAA,CAAQ;IAC1D,MAAM8C,CAAA,GAAIF,IAAA,CAAKC,KAAA,EAAOH,KAAA,GAAQC,CAAA,GAAI,KAAK1C,OAAA,GAAU,KAAKD,OAAA,IAAW,KAAKA,OAAO;IAC7E,MAAM+C,CAAA,GAAIL,KAAA,GAAQC,CAAA,GAAI,KAAK1C,OAAA,GAAU,KAAKD,OAAA,GAAU8C,CAAA,GAAI,KAAK9C,OAAA;IAC7D,OAAO,CAAC+C,CAAA,EAAGD,CAAA,EAAGH,CAAC;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYDK,IAAIC,aAAA,EAAeC,OAAA,EAAS;IAC1B,MAAMjC,MAAA,GAAS,KAAKV,IAAA,CAAKU,MAAA;IACzBiC,OAAA,GAAUA,OAAA,IAAW;IAErB,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAIpB,MAAA,EAAQoB,CAAA,IAAK;MAC/B,KAAK9B,IAAA,CAAK8B,CAAC,IAAIY,aAAA,CAAcE,IAAA,CAAKD,OAAA,EAAS,KAAK3C,IAAA,CAAK8B,CAAC,GAAGA,CAAA,EAAG,KAAK9B,IAAI;IACtE;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD6C,0BAA0BC,IAAA,EAAMC,QAAA,EAAU;IACxC,IAAIC,YAAA,EAAcC,aAAA,EAAeC,cAAA,EAAgBC,QAAA;IAEjD,MAAMC,SAAA,GAAY,IAAIC,OAAA,CAAS;MAC7BC,cAAA,GAAiB,IAAID,OAAA,CAAS;MAC9BE,eAAA,GAAkB,IAAIF,OAAA,CAAS;MAC/BG,WAAA,GAAc,IAAIC,OAAA,CAAS,EAAC1C,QAAA,CAAU;MACtC2C,MAAA,GAAS;IAEX,MAAMC,UAAA,GAAa,IAAIN,OAAA,CAAQ,KAAK5D,OAAA,EAAS,KAAKC,OAAA,EAAS,KAAKC,OAAO;IAEvE,QAAQmD,IAAA;MACN,KAAK;QACHM,SAAA,CAAU/B,GAAA,CAAI,GAAG,GAAG,CAAC;QACrBiC,cAAA,CAAejC,GAAA,CAAI,GAAG,GAAG,EAAE;QAC3BkC,eAAA,CAAgBlC,GAAA,CAAI,GAAG,IAAI,CAAC;QAC5B2B,YAAA,GAAe,KAAKrC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACvDX,aAAA,GAAgB,KAAKtC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACxDT,QAAA,GAAW,IAAIE,OAAA,CAAQN,QAAA,EAAU,GAAG,CAAC;QAErCS,WAAA,CAAYK,QAAA,CAAS,IAAIJ,OAAA,CAAS,EAACK,aAAA,CAAczB,IAAA,CAAK0B,EAAA,GAAK,CAAC,CAAC;QAC7Db,cAAA,IAAkBQ,MAAA,CAAOM,aAAA,CAAc,CAAC,IAAI,KAAK;QACjDR,WAAA,CAAYS,WAAA,CAAY,IAAIZ,OAAA,CAAQN,QAAA,GAAWG,cAAA,EAAgB,GAAG,CAAC,CAAC;QACpE;MACF,KAAK;QACHE,SAAA,CAAU/B,GAAA,CAAI,GAAG,GAAG,CAAC;QACrBiC,cAAA,CAAejC,GAAA,CAAI,GAAG,GAAG,CAAC;QAC1BkC,eAAA,CAAgBlC,GAAA,CAAI,GAAG,GAAG,CAAC;QAC3B2B,YAAA,GAAe,KAAKrC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACvDX,aAAA,GAAgB,KAAKtC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACxDT,QAAA,GAAW,IAAIE,OAAA,CAAQ,GAAGN,QAAA,EAAU,CAAC;QAErCS,WAAA,CAAYK,QAAA,CAAS,IAAIJ,OAAA,CAAS,EAACS,aAAA,CAAc,CAAC7B,IAAA,CAAK0B,EAAA,GAAK,CAAC,CAAC;QAC9Db,cAAA,IAAkBQ,MAAA,CAAOM,aAAA,CAAc,CAAC,IAAI,KAAK;QACjDR,WAAA,CAAYS,WAAA,CAAY,IAAIZ,OAAA,CAAQ,GAAGN,QAAA,GAAWG,cAAA,EAAgB,CAAC,CAAC;QACpE;MACF,KAAK;MACL;QACEE,SAAA,CAAU/B,GAAA,CAAI,GAAG,GAAG,CAAC;QACrBiC,cAAA,CAAejC,GAAA,CAAI,GAAG,GAAG,CAAC;QAC1BkC,eAAA,CAAgBlC,GAAA,CAAI,GAAG,IAAI,CAAC;QAC5B2B,YAAA,GAAe,KAAKrC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACvDX,aAAA,GAAgB,KAAKtC,OAAA,CAAQ,KAAKZ,SAAA,CAAU6D,OAAA,CAAQ,GAAG,CAAC;QACxDT,QAAA,GAAW,IAAIE,OAAA,CAAQ,GAAG,GAAGN,QAAQ;QAErCG,cAAA,IAAkBQ,MAAA,CAAOM,aAAA,CAAc,CAAC,IAAI,KAAK;QACjDR,WAAA,CAAYS,WAAA,CAAY,IAAIZ,OAAA,CAAQ,GAAG,GAAGN,QAAA,GAAWG,cAAc,CAAC;QACpE;IACH;IAED,IAAIiB,OAAA,EAASC,OAAA;IAEb,IAAI,CAAC,KAAKxC,YAAA,EAAc;MACtB0B,cAAA,CAAee,YAAA,CAAaX,MAAA,CAAOY,aAAa,EAAEC,SAAA,CAAW;MAC7DhB,eAAA,CAAgBc,YAAA,CAAaX,MAAA,CAAOY,aAAa,EAAEC,SAAA,CAAW;MAC9DnB,SAAA,CAAUiB,YAAA,CAAaX,MAAA,CAAOY,aAAa,EAAEC,SAAA,CAAW;IACzD;IACDjB,cAAA,CAAekB,MAAA,GAAS;IACxBjB,eAAA,CAAgBiB,MAAA,GAAS;IACzBL,OAAA,GAAU9B,IAAA,CAAKC,KAAA,CAAMD,IAAA,CAAKoC,GAAA,CAAInB,cAAA,CAAeoB,GAAA,CAAIf,UAAU,CAAC,CAAC;IAC7DS,OAAA,GAAU/B,IAAA,CAAKC,KAAA,CAAMD,IAAA,CAAKoC,GAAA,CAAIlB,eAAA,CAAgBmB,GAAA,CAAIf,UAAU,CAAC,CAAC;IAC9D,MAAMgB,UAAA,GAAatC,IAAA,CAAKoC,GAAA,CAAIN,OAAA,GAAUnB,YAAY;IAClD,MAAM4B,WAAA,GAAcvC,IAAA,CAAKoC,GAAA,CAAIL,OAAA,GAAUnB,aAAa;IAEpDE,QAAA,GAAWd,IAAA,CAAKoC,GAAA,CAAIpC,IAAA,CAAKwC,KAAA,CAAM1B,QAAA,CAASkB,YAAA,CAAaX,MAAA,CAAOY,aAAa,EAAEI,GAAA,CAAItB,SAAS,CAAC,CAAC;IAC1F,MAAM0B,IAAA,GAAO,CAAC,IAAIzB,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAG,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,GAAG,IAAIA,OAAA,CAAQ,GAAG,GAAG,CAAC,CAAC;IAC9E,MAAM0B,UAAA,GAAa,CAACzB,cAAA,EAAgBC,eAAA,EAAiBH,SAAS,EAAE4B,IAAA,CAAK,UAAUxC,CAAA,EAAG;MAChF,OAAOH,IAAA,CAAKoC,GAAA,CAAIjC,CAAA,CAAEkC,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,CAAC,IAAI;IACxC,CAAK;IACD,MAAMG,UAAA,GAAa,CAAC3B,cAAA,EAAgBC,eAAA,EAAiBH,SAAS,EAAE4B,IAAA,CAAK,UAAUxC,CAAA,EAAG;MAChF,OAAOH,IAAA,CAAKoC,GAAA,CAAIjC,CAAA,CAAEkC,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,CAAC,IAAI;IACxC,CAAK;IACD,MAAMI,UAAA,GAAa,CAAC5B,cAAA,EAAgBC,eAAA,EAAiBH,SAAS,EAAE4B,IAAA,CAAK,UAAUxC,CAAA,EAAG;MAChF,OAAOH,IAAA,CAAKoC,GAAA,CAAIjC,CAAA,CAAEkC,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,CAAC,IAAI;IACxC,CAAK;IAED,SAASK,YAAYrD,CAAA,EAAGC,CAAA,EAAG;MACzB,MAAMqD,EAAA,GAAKL,UAAA,KAAe3B,SAAA,GAAYD,QAAA,GAAW4B,UAAA,CAAWP,MAAA,KAAW,MAAM1C,CAAA,GAAIC,CAAA;MACjF,MAAMsD,EAAA,GAAKJ,UAAA,KAAe7B,SAAA,GAAYD,QAAA,GAAW8B,UAAA,CAAWT,MAAA,KAAW,MAAM1C,CAAA,GAAIC,CAAA;MACjF,MAAMuD,EAAA,GAAKJ,UAAA,KAAe9B,SAAA,GAAYD,QAAA,GAAW+B,UAAA,CAAWV,MAAA,KAAW,MAAM1C,CAAA,GAAIC,CAAA;MAIjF,MAAMwD,OAAA,GAAUR,UAAA,CAAWL,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,IAAI,IAAIM,EAAA,GAAK1B,MAAA,CAAOjE,OAAA,GAAU,IAAI2F,EAAA;MACxE,MAAMI,OAAA,GAAUP,UAAA,CAAWP,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,IAAI,IAAIO,EAAA,GAAK3B,MAAA,CAAOhE,OAAA,GAAU,IAAI2F,EAAA;MACxE,MAAMI,OAAA,GAAUP,UAAA,CAAWR,GAAA,CAAII,IAAA,CAAK,CAAC,CAAC,IAAI,IAAIQ,EAAA,GAAK5B,MAAA,CAAO/D,OAAA,GAAU,IAAI2F,EAAA;MAExE,OAAO5B,MAAA,CAAOzB,MAAA,CAAOsD,OAAA,EAASC,OAAA,EAASC,OAAO;IAC/C;IAED,OAAO;MACLtB,OAAA;MACAC,OAAA;MACAe,WAAA;MACAtE,MAAA,EAAQ2C,WAAA;MACRmB,UAAA;MACAC;IACD;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDc,aAAa5C,IAAA,EAAMX,KAAA,EAAO;IACxB,MAAMV,KAAA,GAAQ,IAAIkE,WAAA,CAAY,MAAMxD,KAAA,EAAOW,IAAI;IAC/C,KAAKvB,SAAA,CAAUqE,IAAA,CAAKnE,KAAK;IACzB,OAAOA,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDoE,iBAAA,EAAmB;IACjB,KAAKtE,SAAA,CAAUC,OAAA,CAAQ,UAAUC,KAAA,EAAO;MACtCA,KAAA,CAAMqE,OAAA,CAAS;IACrB,CAAK;IAED,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODC,cAAA,EAAgB;IACd,IAAIC,GAAA,GAAM/E,QAAA;IACV,IAAIgF,GAAA,GAAM,CAAAhF,QAAA;IAGV,MAAMiF,QAAA,GAAW,KAAKlG,IAAA,CAAKU,MAAA;IAE3B,IAAIoB,CAAA,GAAI;IAER,KAAKA,CAAA,GAAI,GAAGA,CAAA,GAAIoE,QAAA,EAAUpE,CAAA,IAAK;MAC7B,IAAI,CAACqE,KAAA,CAAM,KAAKnG,IAAA,CAAK8B,CAAC,CAAC,GAAG;QACxB,MAAMR,KAAA,GAAQ,KAAKtB,IAAA,CAAK8B,CAAC;QACzBkE,GAAA,GAAM3D,IAAA,CAAK2D,GAAA,CAAIA,GAAA,EAAK1E,KAAK;QACzB2E,GAAA,GAAM5D,IAAA,CAAK4D,GAAA,CAAIA,GAAA,EAAK3E,KAAK;MAC1B;IACF;IAED,KAAK0E,GAAA,GAAMA,GAAA;IACX,KAAKC,GAAA,GAAMA,GAAA;IAEX,OAAO,CAACD,GAAA,EAAKC,GAAG;EACjB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}