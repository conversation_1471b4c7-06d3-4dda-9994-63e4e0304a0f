{"ast": null, "code": "const GammaCorrectionShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 tex = texture2D( tDiffuse, vUv );\n\n    \t#ifdef LinearTosRGB\n    \t\tgl_FragColor = LinearTosRGB( tex );\n    \t#else\n    \t\tgl_FragColor = sRGBTransferOETF( tex );\n    \t#endif\n\n    }\n  `)\n};\nexport { GammaCorrectionShader };", "map": {"version": 3, "names": ["GammaCorrectionShader", "uniforms", "tDiffuse", "value", "vertexShader", "fragmentShader"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/src/shaders/GammaCorrectionShader.ts"], "sourcesContent": ["/**\n * Gamma Correction Shader\n * http://en.wikipedia.org/wiki/gamma_correction\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type GammaCorrectionShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IGammaCorrectionShader extends IShader<GammaCorrectionShaderUniforms> {}\n\nexport const GammaCorrectionShader: IGammaCorrectionShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 tex = texture2D( tDiffuse, vUv );\n\n    \t#ifdef LinearTosRGB\n    \t\tgl_FragColor = LinearTosRGB( tex );\n    \t#else\n    \t\tgl_FragColor = sRGBTransferOETF( tex );\n    \t#endif\n\n    }\n  `,\n}\n"], "mappings": "AAcO,MAAMA,qBAAA,GAAgD;EAC3DC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;EAC1B;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}