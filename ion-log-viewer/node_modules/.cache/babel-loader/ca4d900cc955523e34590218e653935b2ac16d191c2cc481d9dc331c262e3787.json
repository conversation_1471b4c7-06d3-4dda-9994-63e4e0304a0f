{"ast": null, "code": "export function arrayToBox(nodeIndex32, array, target) {\n  target.min.x = array[nodeIndex32];\n  target.min.y = array[nodeIndex32 + 1];\n  target.min.z = array[nodeIndex32 + 2];\n  target.max.x = array[nodeIndex32 + 3];\n  target.max.y = array[nodeIndex32 + 4];\n  target.max.z = array[nodeIndex32 + 5];\n  return target;\n}\nexport function makeEmptyBounds(target) {\n  target[0] = target[1] = target[2] = Infinity;\n  target[3] = target[4] = target[5] = -Infinity;\n}\nexport function getLongestEdgeIndex(bounds) {\n  let splitDimIdx = -1;\n  let splitDist = -Infinity;\n  for (let i = 0; i < 3; i++) {\n    const dist = bounds[i + 3] - bounds[i];\n    if (dist > splitDist) {\n      splitDist = dist;\n      splitDimIdx = i;\n    }\n  }\n  return splitDimIdx;\n}\n\n// copies bounds a into bounds b\nexport function copyBounds(source, target) {\n  target.set(source);\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds(a, b, target) {\n  let aVal, bVal;\n  for (let d = 0; d < 3; d++) {\n    const d3 = d + 3;\n\n    // set the minimum values\n    aVal = a[d];\n    bVal = b[d];\n    target[d] = aVal < bVal ? aVal : bVal;\n\n    // set the max values\n    aVal = a[d3];\n    bVal = b[d3];\n    target[d3] = aVal > bVal ? aVal : bVal;\n  }\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds(startIndex, triangleBounds, bounds) {\n  for (let d = 0; d < 3; d++) {\n    const tCenter = triangleBounds[startIndex + 2 * d];\n    const tHalf = triangleBounds[startIndex + 2 * d + 1];\n    const tMin = tCenter - tHalf;\n    const tMax = tCenter + tHalf;\n    if (tMin < bounds[d]) {\n      bounds[d] = tMin;\n    }\n    if (tMax > bounds[d + 3]) {\n      bounds[d + 3] = tMax;\n    }\n  }\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea(bounds) {\n  const d0 = bounds[3] - bounds[0];\n  const d1 = bounds[4] - bounds[1];\n  const d2 = bounds[5] - bounds[2];\n  return 2 * (d0 * d1 + d1 * d2 + d2 * d0);\n}", "map": {"version": 3, "names": ["arrayToBox", "nodeIndex32", "array", "target", "min", "x", "y", "z", "max", "makeEmptyBounds", "Infinity", "getLongestEdgeIndex", "bounds", "splitDimIdx", "splitDist", "i", "dist", "copyBounds", "source", "set", "unionBounds", "a", "b", "aVal", "bVal", "d", "d3", "expandByTriangleBounds", "startIndex", "triangleBounds", "tCenter", "tHalf", "tMin", "tMax", "computeSurfaceArea", "d0", "d1", "d2"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/utils/ArrayBoxUtilities.js"], "sourcesContent": ["export function arrayToBox( nodeIndex32, array, target ) {\n\n\ttarget.min.x = array[ nodeIndex32 ];\n\ttarget.min.y = array[ nodeIndex32 + 1 ];\n\ttarget.min.z = array[ nodeIndex32 + 2 ];\n\n\ttarget.max.x = array[ nodeIndex32 + 3 ];\n\ttarget.max.y = array[ nodeIndex32 + 4 ];\n\ttarget.max.z = array[ nodeIndex32 + 5 ];\n\n\treturn target;\n\n}\n\nexport function makeEmptyBounds( target ) {\n\n\ttarget[ 0 ] = target[ 1 ] = target[ 2 ] = Infinity;\n\ttarget[ 3 ] = target[ 4 ] = target[ 5 ] = - Infinity;\n\n}\n\nexport function getLongestEdgeIndex( bounds ) {\n\n\tlet splitDimIdx = - 1;\n\tlet splitDist = - Infinity;\n\n\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\tconst dist = bounds[ i + 3 ] - bounds[ i ];\n\t\tif ( dist > splitDist ) {\n\n\t\t\tsplitDist = dist;\n\t\t\tsplitDimIdx = i;\n\n\t\t}\n\n\t}\n\n\treturn splitDimIdx;\n\n}\n\n// copies bounds a into bounds b\nexport function copyBounds( source, target ) {\n\n\ttarget.set( source );\n\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds( a, b, target ) {\n\n\tlet aVal, bVal;\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst d3 = d + 3;\n\n\t\t// set the minimum values\n\t\taVal = a[ d ];\n\t\tbVal = b[ d ];\n\t\ttarget[ d ] = aVal < bVal ? aVal : bVal;\n\n\t\t// set the max values\n\t\taVal = a[ d3 ];\n\t\tbVal = b[ d3 ];\n\t\ttarget[ d3 ] = aVal > bVal ? aVal : bVal;\n\n\t}\n\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds( startIndex, triangleBounds, bounds ) {\n\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst tCenter = triangleBounds[ startIndex + 2 * d ];\n\t\tconst tHalf = triangleBounds[ startIndex + 2 * d + 1 ];\n\n\t\tconst tMin = tCenter - tHalf;\n\t\tconst tMax = tCenter + tHalf;\n\n\t\tif ( tMin < bounds[ d ] ) {\n\n\t\t\tbounds[ d ] = tMin;\n\n\t\t}\n\n\t\tif ( tMax > bounds[ d + 3 ] ) {\n\n\t\t\tbounds[ d + 3 ] = tMax;\n\n\t\t}\n\n\t}\n\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea( bounds ) {\n\n\tconst d0 = bounds[ 3 ] - bounds[ 0 ];\n\tconst d1 = bounds[ 4 ] - bounds[ 1 ];\n\tconst d2 = bounds[ 5 ] - bounds[ 2 ];\n\n\treturn 2 * ( d0 * d1 + d1 * d2 + d2 * d0 );\n\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAG;EAExDA,MAAM,CAACC,GAAG,CAACC,CAAC,GAAGH,KAAK,CAAED,WAAW,CAAE;EACnCE,MAAM,CAACC,GAAG,CAACE,CAAC,GAAGJ,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EACvCE,MAAM,CAACC,GAAG,CAACG,CAAC,GAAGL,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EAEvCE,MAAM,CAACK,GAAG,CAACH,CAAC,GAAGH,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EACvCE,MAAM,CAACK,GAAG,CAACF,CAAC,GAAGJ,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EACvCE,MAAM,CAACK,GAAG,CAACD,CAAC,GAAGL,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EAEvC,OAAOE,MAAM;AAEd;AAEA,OAAO,SAASM,eAAeA,CAAEN,MAAM,EAAG;EAEzCA,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE,GAAGO,QAAQ;EAClDP,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE,GAAG,CAAEO,QAAQ;AAErD;AAEA,OAAO,SAASC,mBAAmBA,CAAEC,MAAM,EAAG;EAE7C,IAAIC,WAAW,GAAG,CAAE,CAAC;EACrB,IAAIC,SAAS,GAAG,CAAEJ,QAAQ;EAE1B,KAAM,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;IAE9B,MAAMC,IAAI,GAAGJ,MAAM,CAAEG,CAAC,GAAG,CAAC,CAAE,GAAGH,MAAM,CAAEG,CAAC,CAAE;IAC1C,IAAKC,IAAI,GAAGF,SAAS,EAAG;MAEvBA,SAAS,GAAGE,IAAI;MAChBH,WAAW,GAAGE,CAAC;IAEhB;EAED;EAEA,OAAOF,WAAW;AAEnB;;AAEA;AACA,OAAO,SAASI,UAAUA,CAAEC,MAAM,EAAEf,MAAM,EAAG;EAE5CA,MAAM,CAACgB,GAAG,CAAED,MAAO,CAAC;AAErB;;AAEA;AACA,OAAO,SAASE,WAAWA,CAAEC,CAAC,EAAEC,CAAC,EAAEnB,MAAM,EAAG;EAE3C,IAAIoB,IAAI,EAAEC,IAAI;EACd,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;IAE9B,MAAMC,EAAE,GAAGD,CAAC,GAAG,CAAC;;IAEhB;IACAF,IAAI,GAAGF,CAAC,CAAEI,CAAC,CAAE;IACbD,IAAI,GAAGF,CAAC,CAAEG,CAAC,CAAE;IACbtB,MAAM,CAAEsB,CAAC,CAAE,GAAGF,IAAI,GAAGC,IAAI,GAAGD,IAAI,GAAGC,IAAI;;IAEvC;IACAD,IAAI,GAAGF,CAAC,CAAEK,EAAE,CAAE;IACdF,IAAI,GAAGF,CAAC,CAAEI,EAAE,CAAE;IACdvB,MAAM,CAAEuB,EAAE,CAAE,GAAGH,IAAI,GAAGC,IAAI,GAAGD,IAAI,GAAGC,IAAI;EAEzC;AAED;;AAEA;AACA,OAAO,SAASG,sBAAsBA,CAAEC,UAAU,EAAEC,cAAc,EAAEjB,MAAM,EAAG;EAE5E,KAAM,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;IAE9B,MAAMK,OAAO,GAAGD,cAAc,CAAED,UAAU,GAAG,CAAC,GAAGH,CAAC,CAAE;IACpD,MAAMM,KAAK,GAAGF,cAAc,CAAED,UAAU,GAAG,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAE;IAEtD,MAAMO,IAAI,GAAGF,OAAO,GAAGC,KAAK;IAC5B,MAAME,IAAI,GAAGH,OAAO,GAAGC,KAAK;IAE5B,IAAKC,IAAI,GAAGpB,MAAM,CAAEa,CAAC,CAAE,EAAG;MAEzBb,MAAM,CAAEa,CAAC,CAAE,GAAGO,IAAI;IAEnB;IAEA,IAAKC,IAAI,GAAGrB,MAAM,CAAEa,CAAC,GAAG,CAAC,CAAE,EAAG;MAE7Bb,MAAM,CAAEa,CAAC,GAAG,CAAC,CAAE,GAAGQ,IAAI;IAEvB;EAED;AAED;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAAEtB,MAAM,EAAG;EAE5C,MAAMuB,EAAE,GAAGvB,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE;EACpC,MAAMwB,EAAE,GAAGxB,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE;EACpC,MAAMyB,EAAE,GAAGzB,MAAM,CAAE,CAAC,CAAE,GAAGA,MAAM,CAAE,CAAC,CAAE;EAEpC,OAAO,CAAC,IAAKuB,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGF,EAAE,CAAE;AAE3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}