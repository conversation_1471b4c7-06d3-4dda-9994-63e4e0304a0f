{"ast": null, "code": "/**\n * Returns a boolean indicating if the event's target has :focus-visible\n */\nexport default function isFocusVisible(element) {\n  try {\n    return element.matches(':focus-visible');\n  } catch (error) {\n    // Do not warn on jsdom tests, otherwise all tests that rely on focus have to be skipped\n    // Tests that rely on `:focus-visible` will still have to be skipped in jsdom\n    if (process.env.NODE_ENV !== 'production' && !/jsdom/.test(window.navigator.userAgent)) {\n      console.warn(['MUI: The `:focus-visible` pseudo class is not supported in this browser.', 'Some components rely on this feature to work properly.'].join('\\n'));\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["isFocusVisible", "element", "matches", "error", "process", "env", "NODE_ENV", "test", "window", "navigator", "userAgent", "console", "warn", "join"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@mui/utils/esm/isFocusVisible/isFocusVisible.js"], "sourcesContent": ["/**\n * Returns a boolean indicating if the event's target has :focus-visible\n */\nexport default function isFocusVisible(element) {\n  try {\n    return element.matches(':focus-visible');\n  } catch (error) {\n    // Do not warn on jsdom tests, otherwise all tests that rely on focus have to be skipped\n    // Tests that rely on `:focus-visible` will still have to be skipped in jsdom\n    if (process.env.NODE_ENV !== 'production' && !/jsdom/.test(window.navigator.userAgent)) {\n      console.warn(['MUI: The `:focus-visible` pseudo class is not supported in this browser.', 'Some components rely on this feature to work properly.'].join('\\n'));\n    }\n  }\n  return false;\n}"], "mappings": "AAAA;AACA;AACA;AACA,eAAe,SAASA,cAAcA,CAACC,OAAO,EAAE;EAC9C,IAAI;IACF,OAAOA,OAAO,CAACC,OAAO,CAAC,gBAAgB,CAAC;EAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd;IACA;IACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,OAAO,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;MACtFC,OAAO,CAACC,IAAI,CAAC,CAAC,0EAA0E,EAAE,wDAAwD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjK;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}