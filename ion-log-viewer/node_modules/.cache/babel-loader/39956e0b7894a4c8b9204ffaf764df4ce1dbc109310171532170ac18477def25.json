{"ast": null, "code": "import { Vector2, Vector3, <PERSON> } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle(tri, i, index, pos) {\n  const ta = tri.a;\n  const tb = tri.b;\n  const tc = tri.c;\n  let i0 = i;\n  let i1 = i + 1;\n  let i2 = i + 2;\n  if (index) {\n    i0 = index.getX(i0);\n    i1 = index.getX(i1);\n    i2 = index.getX(i2);\n  }\n  ta.x = pos.getX(i0);\n  ta.y = pos.getY(i0);\n  ta.z = pos.getZ(i0);\n  tb.x = pos.getX(i1);\n  tb.y = pos.getY(i1);\n  tb.z = pos.getZ(i1);\n  tc.x = pos.getX(i2);\n  tc.y = pos.getY(i2);\n  tc.z = pos.getZ(i2);\n}\nconst tempV1 = /* @__PURE__ */new Vector3();\nconst tempV2 = /* @__PURE__ */new Vector3();\nconst tempV3 = /* @__PURE__ */new Vector3();\nconst tempUV1 = /* @__PURE__ */new Vector2();\nconst tempUV2 = /* @__PURE__ */new Vector2();\nconst tempUV3 = /* @__PURE__ */new Vector2();\nexport function getTriangleHitPointInfo(point, geometry, triangleIndex, target) {\n  const indices = geometry.getIndex().array;\n  const positions = geometry.getAttribute('position');\n  const uvs = geometry.getAttribute('uv');\n  const a = indices[triangleIndex * 3];\n  const b = indices[triangleIndex * 3 + 1];\n  const c = indices[triangleIndex * 3 + 2];\n  tempV1.fromBufferAttribute(positions, a);\n  tempV2.fromBufferAttribute(positions, b);\n  tempV3.fromBufferAttribute(positions, c);\n\n  // find the associated material index\n  let materialIndex = 0;\n  const groups = geometry.groups;\n  const firstVertexIndex = triangleIndex * 3;\n  for (let i = 0, l = groups.length; i < l; i++) {\n    const group = groups[i];\n    const {\n      start,\n      count\n    } = group;\n    if (firstVertexIndex >= start && firstVertexIndex < start + count) {\n      materialIndex = group.materialIndex;\n      break;\n    }\n  }\n\n  // extract barycoord\n  const barycoord = target && target.barycoord ? target.barycoord : new Vector3();\n  Triangle.getBarycoord(point, tempV1, tempV2, tempV3, barycoord);\n\n  // extract uvs\n  let uv = null;\n  if (uvs) {\n    tempUV1.fromBufferAttribute(uvs, a);\n    tempUV2.fromBufferAttribute(uvs, b);\n    tempUV3.fromBufferAttribute(uvs, c);\n    if (target && target.uv) uv = target.uv;else uv = new Vector2();\n    Triangle.getInterpolation(point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv);\n  }\n\n  // adjust the provided target or create a new one\n  if (target) {\n    if (!target.face) target.face = {};\n    target.face.a = a;\n    target.face.b = b;\n    target.face.c = c;\n    target.face.materialIndex = materialIndex;\n    if (!target.face.normal) target.face.normal = new Vector3();\n    Triangle.getNormal(tempV1, tempV2, tempV3, target.face.normal);\n    if (uv) target.uv = uv;\n    target.barycoord = barycoord;\n    return target;\n  } else {\n    return {\n      face: {\n        a: a,\n        b: b,\n        c: c,\n        materialIndex: materialIndex,\n        normal: Triangle.getNormal(tempV1, tempV2, tempV3, new Vector3())\n      },\n      uv: uv,\n      barycoord: barycoord\n    };\n  }\n}", "map": {"version": 3, "names": ["Vector2", "Vector3", "Triangle", "set<PERSON>riangle", "tri", "i", "index", "pos", "ta", "a", "tb", "b", "tc", "c", "i0", "i1", "i2", "getX", "x", "y", "getY", "z", "getZ", "tempV1", "tempV2", "tempV3", "tempUV1", "tempUV2", "tempUV3", "getTriangleHitPointInfo", "point", "geometry", "triangleIndex", "target", "indices", "getIndex", "array", "positions", "getAttribute", "uvs", "fromBufferAttribute", "materialIndex", "groups", "firstVertexIndex", "l", "length", "group", "start", "count", "barycoord", "getBarycoord", "uv", "getInterpolation", "face", "normal", "getNormal"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/three-mesh-bvh/src/utils/TriangleUtilities.js"], "sourcesContent": ["\nimport { Vector2, Vector3, <PERSON> } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i0 );\n\t\ti1 = index.getX( i1 );\n\t\ti2 = index.getX( i2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract barycoord\n\tconst barycoord = target && target.barycoord ? target.barycoord : new Vector3();\n\tTriangle.getBarycoord( point, tempV1, tempV2, tempV3, barycoord );\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getInterpolation( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\t\ttarget.barycoord = barycoord;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv,\n\t\t\tbarycoord: barycoord,\n\t\t};\n\n\t}\n\n}\n"], "mappings": "AACA,SAASA,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;;AAElD;AACA,OAAO,SAASC,WAAWA,CAAEC,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAEC,GAAG,EAAG;EAEjD,MAAMC,EAAE,GAAGJ,GAAG,CAACK,CAAC;EAChB,MAAMC,EAAE,GAAGN,GAAG,CAACO,CAAC;EAChB,MAAMC,EAAE,GAAGR,GAAG,CAACS,CAAC;EAEhB,IAAIC,EAAE,GAAGT,CAAC;EACV,IAAIU,EAAE,GAAGV,CAAC,GAAG,CAAC;EACd,IAAIW,EAAE,GAAGX,CAAC,GAAG,CAAC;EACd,IAAKC,KAAK,EAAG;IAEZQ,EAAE,GAAGR,KAAK,CAACW,IAAI,CAAEH,EAAG,CAAC;IACrBC,EAAE,GAAGT,KAAK,CAACW,IAAI,CAAEF,EAAG,CAAC;IACrBC,EAAE,GAAGV,KAAK,CAACW,IAAI,CAAED,EAAG,CAAC;EAEtB;EAEAR,EAAE,CAACU,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAEH,EAAG,CAAC;EACrBN,EAAE,CAACW,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEN,EAAG,CAAC;EACrBN,EAAE,CAACa,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAER,EAAG,CAAC;EAErBJ,EAAE,CAACQ,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAEF,EAAG,CAAC;EACrBL,EAAE,CAACS,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEL,EAAG,CAAC;EACrBL,EAAE,CAACW,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAEP,EAAG,CAAC;EAErBH,EAAE,CAACM,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAED,EAAG,CAAC;EACrBJ,EAAE,CAACO,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEJ,EAAG,CAAC;EACrBJ,EAAE,CAACS,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAEN,EAAG,CAAC;AAEtB;AAEA,MAAMO,MAAM,GAAG,eAAgB,IAAItB,OAAO,CAAC,CAAC;AAC5C,MAAMuB,MAAM,GAAG,eAAgB,IAAIvB,OAAO,CAAC,CAAC;AAC5C,MAAMwB,MAAM,GAAG,eAAgB,IAAIxB,OAAO,CAAC,CAAC;AAC5C,MAAMyB,OAAO,GAAG,eAAgB,IAAI1B,OAAO,CAAC,CAAC;AAC7C,MAAM2B,OAAO,GAAG,eAAgB,IAAI3B,OAAO,CAAC,CAAC;AAC7C,MAAM4B,OAAO,GAAG,eAAgB,IAAI5B,OAAO,CAAC,CAAC;AAE7C,OAAO,SAAS6B,uBAAuBA,CAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAG;EAEjF,MAAMC,OAAO,GAAGH,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK;EACzC,MAAMC,SAAS,GAAGN,QAAQ,CAACO,YAAY,CAAE,UAAW,CAAC;EACrD,MAAMC,GAAG,GAAGR,QAAQ,CAACO,YAAY,CAAE,IAAK,CAAC;EAEzC,MAAM7B,CAAC,GAAGyB,OAAO,CAAEF,aAAa,GAAG,CAAC,CAAE;EACtC,MAAMrB,CAAC,GAAGuB,OAAO,CAAEF,aAAa,GAAG,CAAC,GAAG,CAAC,CAAE;EAC1C,MAAMnB,CAAC,GAAGqB,OAAO,CAAEF,aAAa,GAAG,CAAC,GAAG,CAAC,CAAE;EAE1CT,MAAM,CAACiB,mBAAmB,CAAEH,SAAS,EAAE5B,CAAE,CAAC;EAC1Ce,MAAM,CAACgB,mBAAmB,CAAEH,SAAS,EAAE1B,CAAE,CAAC;EAC1Cc,MAAM,CAACe,mBAAmB,CAAEH,SAAS,EAAExB,CAAE,CAAC;;EAE1C;EACA,IAAI4B,aAAa,GAAG,CAAC;EACrB,MAAMC,MAAM,GAAGX,QAAQ,CAACW,MAAM;EAC9B,MAAMC,gBAAgB,GAAGX,aAAa,GAAG,CAAC;EAC1C,KAAM,IAAI3B,CAAC,GAAG,CAAC,EAAEuC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAExC,CAAC,GAAGuC,CAAC,EAAEvC,CAAC,EAAG,EAAG;IAEjD,MAAMyC,KAAK,GAAGJ,MAAM,CAAErC,CAAC,CAAE;IACzB,MAAM;MAAE0C,KAAK;MAAEC;IAAM,CAAC,GAAGF,KAAK;IAC9B,IAAKH,gBAAgB,IAAII,KAAK,IAAIJ,gBAAgB,GAAGI,KAAK,GAAGC,KAAK,EAAG;MAEpEP,aAAa,GAAGK,KAAK,CAACL,aAAa;MACnC;IAED;EAED;;EAEA;EACA,MAAMQ,SAAS,GAAGhB,MAAM,IAAIA,MAAM,CAACgB,SAAS,GAAGhB,MAAM,CAACgB,SAAS,GAAG,IAAIhD,OAAO,CAAC,CAAC;EAC/EC,QAAQ,CAACgD,YAAY,CAAEpB,KAAK,EAAEP,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEwB,SAAU,CAAC;;EAEjE;EACA,IAAIE,EAAE,GAAG,IAAI;EACb,IAAKZ,GAAG,EAAG;IAEVb,OAAO,CAACc,mBAAmB,CAAED,GAAG,EAAE9B,CAAE,CAAC;IACrCkB,OAAO,CAACa,mBAAmB,CAAED,GAAG,EAAE5B,CAAE,CAAC;IACrCiB,OAAO,CAACY,mBAAmB,CAAED,GAAG,EAAE1B,CAAE,CAAC;IAErC,IAAKoB,MAAM,IAAIA,MAAM,CAACkB,EAAE,EAAGA,EAAE,GAAGlB,MAAM,CAACkB,EAAE,CAAC,KACrCA,EAAE,GAAG,IAAInD,OAAO,CAAC,CAAC;IAEvBE,QAAQ,CAACkD,gBAAgB,CAAEtB,KAAK,EAAEP,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEuB,EAAG,CAAC;EAE1F;;EAEA;EACA,IAAKlB,MAAM,EAAG;IAEb,IAAK,CAAEA,MAAM,CAACoB,IAAI,EAAGpB,MAAM,CAACoB,IAAI,GAAG,CAAE,CAAC;IACtCpB,MAAM,CAACoB,IAAI,CAAC5C,CAAC,GAAGA,CAAC;IACjBwB,MAAM,CAACoB,IAAI,CAAC1C,CAAC,GAAGA,CAAC;IACjBsB,MAAM,CAACoB,IAAI,CAACxC,CAAC,GAAGA,CAAC;IACjBoB,MAAM,CAACoB,IAAI,CAACZ,aAAa,GAAGA,aAAa;IACzC,IAAK,CAAER,MAAM,CAACoB,IAAI,CAACC,MAAM,EAAGrB,MAAM,CAACoB,IAAI,CAACC,MAAM,GAAG,IAAIrD,OAAO,CAAC,CAAC;IAC9DC,QAAQ,CAACqD,SAAS,CAAEhC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEQ,MAAM,CAACoB,IAAI,CAACC,MAAO,CAAC;IAEhE,IAAKH,EAAE,EAAGlB,MAAM,CAACkB,EAAE,GAAGA,EAAE;IACxBlB,MAAM,CAACgB,SAAS,GAAGA,SAAS;IAE5B,OAAOhB,MAAM;EAEd,CAAC,MAAM;IAEN,OAAO;MACNoB,IAAI,EAAE;QACL5C,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEA,CAAC;QACJ4B,aAAa,EAAEA,aAAa;QAC5Ba,MAAM,EAAEpD,QAAQ,CAACqD,SAAS,CAAEhC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,IAAIxB,OAAO,CAAC,CAAE;MACnE,CAAC;MACDkD,EAAE,EAAEA,EAAE;MACNF,SAAS,EAAEA;IACZ,CAAC;EAEF;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}