{"ast": null, "code": "import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\nclass ConvolutionMaterial extends THREE.ShaderMaterial {\n  constructor(texelSize = new THREE.Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new THREE.Uniform(null),\n        depthBuffer: new THREE.Uniform(null),\n        resolution: new THREE.Uniform(new THREE.Vector2()),\n        texelSize: new THREE.Uniform(new THREE.Vector2()),\n        halfTexelSize: new THREE.Uniform(new THREE.Vector2()),\n        kernel: new THREE.Uniform(0.0),\n        scale: new THREE.Uniform(1.0),\n        cameraNear: new THREE.Uniform(0.0),\n        cameraFar: new THREE.Uniform(1.0),\n        minDepthThreshold: new THREE.Uniform(0.0),\n        maxDepthThreshold: new THREE.Uniform(1.0),\n        depthScale: new THREE.Uniform(0.0),\n        depthToBlurRatioBias: new THREE.Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: THREE.NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n}\nexport { ConvolutionMaterial };", "map": {"version": 3, "names": ["THREE", "version", "ConvolutionMaterial", "ShaderMaterial", "constructor", "texelSize", "Vector2", "uniforms", "inputBuffer", "Uniform", "depthBuffer", "resolution", "halfTexelSize", "kernel", "scale", "cameraNear", "cameraFar", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "fragmentShader", "vertexShader", "blending", "NoBlending", "depthWrite", "depthTest", "toneMapped", "setTexelSize", "x", "y", "Float32Array", "value", "set", "multiplyScalar", "setResolution", "copy"], "sources": ["/Users/<USER>/Documents/augment-projects/ion/ion-log-viewer/node_modules/@react-three/drei/materials/ConvolutionMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\n\nclass ConvolutionMaterial extends THREE.ShaderMaterial {\n  constructor(texelSize = new THREE.Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new THREE.Uniform(null),\n        depthBuffer: new THREE.Uniform(null),\n        resolution: new THREE.Uniform(new THREE.Vector2()),\n        texelSize: new THREE.Uniform(new THREE.Vector2()),\n        halfTexelSize: new THREE.Uniform(new THREE.Vector2()),\n        kernel: new THREE.Uniform(0.0),\n        scale: new THREE.Uniform(1.0),\n        cameraNear: new THREE.Uniform(0.0),\n        cameraFar: new THREE.Uniform(1.0),\n        minDepthThreshold: new THREE.Uniform(0.0),\n        maxDepthThreshold: new THREE.Uniform(1.0),\n        depthScale: new THREE.Uniform(0.0),\n        depthToBlurRatioBias: new THREE.Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: THREE.NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n}\n\nexport { ConvolutionMaterial };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,MAAMC,mBAAmB,SAASF,KAAK,CAACG,cAAc,CAAC;EACrDC,WAAWA,CAACC,SAAS,GAAG,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE;IAC3C,KAAK,CAAC;MACJC,QAAQ,EAAE;QACRC,WAAW,EAAE,IAAIR,KAAK,CAACS,OAAO,CAAC,IAAI,CAAC;QACpCC,WAAW,EAAE,IAAIV,KAAK,CAACS,OAAO,CAAC,IAAI,CAAC;QACpCE,UAAU,EAAE,IAAIX,KAAK,CAACS,OAAO,CAAC,IAAIT,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;QAClDD,SAAS,EAAE,IAAIL,KAAK,CAACS,OAAO,CAAC,IAAIT,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;QACjDM,aAAa,EAAE,IAAIZ,KAAK,CAACS,OAAO,CAAC,IAAIT,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;QACrDO,MAAM,EAAE,IAAIb,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QAC9BK,KAAK,EAAE,IAAId,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QAC7BM,UAAU,EAAE,IAAIf,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QAClCO,SAAS,EAAE,IAAIhB,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QACjCQ,iBAAiB,EAAE,IAAIjB,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QACzCS,iBAAiB,EAAE,IAAIlB,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QACzCU,UAAU,EAAE,IAAInB,KAAK,CAACS,OAAO,CAAC,GAAG,CAAC;QAClCW,oBAAoB,EAAE,IAAIpB,KAAK,CAACS,OAAO,CAAC,IAAI;MAC9C,CAAC;MACDY,cAAc,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBpB,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AACnF,UAAU;MACJqB,YAAY,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MACJC,QAAQ,EAAEvB,KAAK,CAACwB,UAAU;MAC1BC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,YAAY,CAACvB,SAAS,CAACwB,CAAC,EAAExB,SAAS,CAACyB,CAAC,CAAC;IAC3C,IAAI,CAACjB,MAAM,GAAG,IAAIkB,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC3D;EACAH,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACvB,QAAQ,CAACF,SAAS,CAAC2B,KAAK,CAACC,GAAG,CAACJ,CAAC,EAAEC,CAAC,CAAC;IACvC,IAAI,CAACvB,QAAQ,CAACK,aAAa,CAACoB,KAAK,CAACC,GAAG,CAACJ,CAAC,EAAEC,CAAC,CAAC,CAACI,cAAc,CAAC,GAAG,CAAC;EACjE;EACAC,aAAaA,CAACxB,UAAU,EAAE;IACxB,IAAI,CAACJ,QAAQ,CAACI,UAAU,CAACqB,KAAK,CAACI,IAAI,CAACzB,UAAU,CAAC;EACjD;AACF;AAEA,SAAST,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}