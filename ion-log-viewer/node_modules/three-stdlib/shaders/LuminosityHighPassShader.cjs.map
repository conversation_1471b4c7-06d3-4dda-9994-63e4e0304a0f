{"version": 3, "file": "LuminosityHighPassShader.cjs", "sources": ["../../src/shaders/LuminosityHighPassShader.ts"], "sourcesContent": ["import { Color } from 'three'\n\n/**\n * Luminosity\n * http://en.wikipedia.org/wiki/Luminosity\n */\n\nexport const LuminosityHighPassShader = {\n  shaderID: 'luminosityHighPass',\n\n  uniforms: {\n    tDiffuse: { value: null },\n    luminosityThreshold: { value: 1.0 },\n    smoothWidth: { value: 1.0 },\n    defaultColor: { value: /* @__PURE__ */ new Color(0x000000) },\n    defaultOpacity: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform vec3 defaultColor;\n    uniform float defaultOpacity;\n    uniform float luminosityThreshold;\n    uniform float smoothWidth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tvec4 outputColor = vec4( defaultColor.rgb, defaultOpacity );\n\n    \tfloat alpha = smoothstep( luminosityThreshold, luminosityThreshold + smoothWidth, v );\n\n    \tgl_FragColor = mix( outputColor, texel, alpha );\n\n    }\n  `,\n}\n"], "names": ["Color"], "mappings": ";;;AAOO,MAAM,2BAA2B;AAAA,EACtC,UAAU;AAAA,EAEV,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,qBAAqB,EAAE,OAAO,EAAI;AAAA,IAClC,aAAa,EAAE,OAAO,EAAI;AAAA,IAC1B,cAAc,EAAE,OAA2B,oBAAAA,MAAA,MAAM,CAAQ,EAAE;AAAA,IAC3D,gBAAgB,EAAE,OAAO,EAAI;AAAA,EAC/B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B;;"}