{"version": 3, "file": "MMDExporter.js", "sources": ["../../src/exporters/MMDExporter.ts"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'three'\n// @ts-ignore\nimport { CharsetEncoder } from '../libs/mmdparser'\n\n/**\n * Dependencies\n *  - mmd-parser https://github.com/takahirox/mmd-parser\n */\n\nclass MMDExporter {\n  /* TODO: implement\n\t// mesh -> pmd\n\tthis.parsePmd = function ( object ) {\n\t};\n\t*/\n\n  /* TODO: implement\n\t// mesh -> pmx\n\tthis.parsePmx = function ( object ) {\n\t};\n\t*/\n\n  /* TODO: implement\n\t// animation + skeleton -> vmd\n\tthis.parseVmd = function ( object ) {\n\t};\n\t*/\n\n  /*\n   * skeleton -> vpd\n   * Returns Shift_JIS encoded Uint8Array. Otherwise return strings.\n   */\n  public parseVpd(skin: SkinnedMesh, outputShiftJis: boolean, useOriginalBones: boolean): Uint8Array | string | null {\n    if (skin.isSkinnedMesh !== true) {\n      console.warn('THREE.MMDExporter: parseVpd() requires SkinnedMesh instance.')\n      return null\n    }\n\n    function toStringsFromNumber(num: number): string {\n      if (Math.abs(num) < 1e-6) num = 0\n\n      let a = num.toString()\n\n      if (a.indexOf('.') === -1) {\n        a += '.'\n      }\n\n      a += '000000'\n\n      const index = a.indexOf('.')\n\n      const d = a.slice(0, index)\n      const p = a.slice(index + 1, index + 7)\n\n      return d + '.' + p\n    }\n\n    function toStringsFromArray(array: number[]): string {\n      const a = []\n\n      for (let i = 0, il = array.length; i < il; i++) {\n        a.push(toStringsFromNumber(array[i]))\n      }\n\n      return a.join(',')\n    }\n\n    skin.updateMatrixWorld(true)\n\n    const bones = skin.skeleton.bones\n    const bones2 = this.getBindBones(skin)\n\n    const position = new Vector3()\n    const quaternion = new Quaternion()\n    const quaternion2 = new Quaternion()\n    const matrix = new Matrix4()\n\n    const array = []\n    array.push('Vocaloid Pose Data file')\n    array.push('')\n    array.push((skin.name !== '' ? skin.name.replace(/\\s/g, '_') : 'skin') + '.osm;')\n    array.push(bones.length + ';')\n    array.push('')\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      const bone = bones[i]\n      const bone2 = bones2[i]\n\n      /*\n       * use the bone matrix saved before solving IK.\n       * see CCDIKSolver for the detail.\n       */\n      if (\n        useOriginalBones === true &&\n        bone.userData.ik !== undefined &&\n        bone.userData.ik.originalMatrix !== undefined\n      ) {\n        matrix.fromArray(bone.userData.ik.originalMatrix)\n      } else {\n        matrix.copy(bone.matrix)\n      }\n\n      position.setFromMatrixPosition(matrix)\n      quaternion.setFromRotationMatrix(matrix)\n\n      const pArray = position.sub(bone2.position).toArray()\n      const qArray = quaternion2.copy(bone2.quaternion).conjugate().multiply(quaternion).toArray()\n\n      // right to left\n      pArray[2] = -pArray[2]\n      qArray[0] = -qArray[0]\n      qArray[1] = -qArray[1]\n\n      array.push('Bone' + i + '{' + bone.name)\n      array.push('  ' + toStringsFromArray(pArray) + ';')\n      array.push('  ' + toStringsFromArray(qArray) + ';')\n      array.push('}')\n      array.push('')\n    }\n\n    array.push('')\n\n    const lines = array.join('\\n')\n\n    return outputShiftJis === true ? this.unicodeToShiftjis(lines) : lines\n  }\n\n  // Unicode to Shift_JIS table\n  private u2sTable: { [key: string]: number | undefined } | undefined\n\n  private unicodeToShiftjis(str: string): Uint8Array {\n    if (this.u2sTable === undefined) {\n      const encoder = new CharsetEncoder()\n      const table = encoder.s2uTable\n      this.u2sTable = {}\n\n      const keys = Object.keys(table)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        let key = keys[i]\n\n        const value = table[key]\n\n        this.u2sTable[value] = parseInt(key)\n      }\n    }\n\n    const array = []\n\n    for (let i = 0, il = str.length; i < il; i++) {\n      const code = str.charCodeAt(i)\n\n      const value = this.u2sTable[code]\n\n      if (value === undefined) {\n        throw 'cannot convert charcode 0x' + code.toString(16)\n      } else if (value > 0xff) {\n        array.push((value >> 8) & 0xff)\n        array.push(value & 0xff)\n      } else {\n        array.push(value & 0xff)\n      }\n    }\n\n    return new Uint8Array(array)\n  }\n\n  private getBindBones(skin: SkinnedMesh): Bone[] {\n    // any more efficient ways?\n    const poseSkin = skin.clone()\n    poseSkin.pose()\n    return poseSkin.skeleton.bones\n  }\n}\n\nexport { MMDExporter }\n"], "names": ["array"], "mappings": ";;;;;;;;AASA,MAAM,YAAY;AAAA,EAAlB;AAuHU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAhGD,SAAS,MAAmB,gBAAyB,kBAAuD;AAC7G,QAAA,KAAK,kBAAkB,MAAM;AAC/B,cAAQ,KAAK,8DAA8D;AACpE,aAAA;AAAA,IACT;AAEA,aAAS,oBAAoB,KAAqB;AAC5C,UAAA,KAAK,IAAI,GAAG,IAAI;AAAY,cAAA;AAE5B,UAAA,IAAI,IAAI;AAEZ,UAAI,EAAE,QAAQ,GAAG,MAAM,IAAI;AACpB,aAAA;AAAA,MACP;AAEK,WAAA;AAEC,YAAA,QAAQ,EAAE,QAAQ,GAAG;AAE3B,YAAM,IAAI,EAAE,MAAM,GAAG,KAAK;AAC1B,YAAM,IAAI,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAEtC,aAAO,IAAI,MAAM;AAAA,IACnB;AAEA,aAAS,mBAAmBA,QAAyB;AACnD,YAAM,IAAI,CAAA;AAEV,eAAS,IAAI,GAAG,KAAKA,OAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,UAAE,KAAK,oBAAoBA,OAAM,CAAC,CAAC,CAAC;AAAA,MACtC;AAEO,aAAA,EAAE,KAAK,GAAG;AAAA,IACnB;AAEA,SAAK,kBAAkB,IAAI;AAErB,UAAA,QAAQ,KAAK,SAAS;AACtB,UAAA,SAAS,KAAK,aAAa,IAAI;AAE/B,UAAA,WAAW,IAAI;AACf,UAAA,aAAa,IAAI;AACjB,UAAA,cAAc,IAAI;AAClB,UAAA,SAAS,IAAI;AAEnB,UAAM,QAAQ,CAAA;AACd,UAAM,KAAK,yBAAyB;AACpC,UAAM,KAAK,EAAE;AACP,UAAA,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,QAAQ,OAAO,GAAG,IAAI,UAAU,OAAO;AAC1E,UAAA,KAAK,MAAM,SAAS,GAAG;AAC7B,UAAM,KAAK,EAAE;AAEb,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACxC,YAAA,OAAO,MAAM,CAAC;AACd,YAAA,QAAQ,OAAO,CAAC;AAOpB,UAAA,qBAAqB,QACrB,KAAK,SAAS,OAAO,UACrB,KAAK,SAAS,GAAG,mBAAmB,QACpC;AACA,eAAO,UAAU,KAAK,SAAS,GAAG,cAAc;AAAA,MAAA,OAC3C;AACE,eAAA,KAAK,KAAK,MAAM;AAAA,MACzB;AAEA,eAAS,sBAAsB,MAAM;AACrC,iBAAW,sBAAsB,MAAM;AAEvC,YAAM,SAAS,SAAS,IAAI,MAAM,QAAQ,EAAE;AACtC,YAAA,SAAS,YAAY,KAAK,MAAM,UAAU,EAAE,UAAA,EAAY,SAAS,UAAU,EAAE,QAAQ;AAG3F,aAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AACrB,aAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AACrB,aAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AAErB,YAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI;AACvC,YAAM,KAAK,OAAO,mBAAmB,MAAM,IAAI,GAAG;AAClD,YAAM,KAAK,OAAO,mBAAmB,MAAM,IAAI,GAAG;AAClD,YAAM,KAAK,GAAG;AACd,YAAM,KAAK,EAAE;AAAA,IACf;AAEA,UAAM,KAAK,EAAE;AAEP,UAAA,QAAQ,MAAM,KAAK,IAAI;AAE7B,WAAO,mBAAmB,OAAO,KAAK,kBAAkB,KAAK,IAAI;AAAA,EACnE;AAAA,EAKQ,kBAAkB,KAAyB;AAC7C,QAAA,KAAK,aAAa,QAAW;AACzB,YAAA,UAAU,IAAI;AACpB,YAAM,QAAQ,QAAQ;AACtB,WAAK,WAAW;AAEV,YAAA,OAAO,OAAO,KAAK,KAAK;AAE9B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AACzC,YAAA,MAAM,KAAK,CAAC;AAEV,cAAA,QAAQ,MAAM,GAAG;AAEvB,aAAK,SAAS,KAAK,IAAI,SAAS,GAAG;AAAA,MACrC;AAAA,IACF;AAEA,UAAM,QAAQ,CAAA;AAEd,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AACtC,YAAA,OAAO,IAAI,WAAW,CAAC;AAEvB,YAAA,QAAQ,KAAK,SAAS,IAAI;AAEhC,UAAI,UAAU,QAAW;AACjB,cAAA,+BAA+B,KAAK,SAAS,EAAE;AAAA,MAAA,WAC5C,QAAQ,KAAM;AACjB,cAAA,KAAM,SAAS,IAAK,GAAI;AACxB,cAAA,KAAK,QAAQ,GAAI;AAAA,MAAA,OAClB;AACC,cAAA,KAAK,QAAQ,GAAI;AAAA,MACzB;AAAA,IACF;AAEO,WAAA,IAAI,WAAW,KAAK;AAAA,EAC7B;AAAA,EAEQ,aAAa,MAA2B;AAExC,UAAA,WAAW,KAAK;AACtB,aAAS,KAAK;AACd,WAAO,SAAS,SAAS;AAAA,EAC3B;AACF;"}