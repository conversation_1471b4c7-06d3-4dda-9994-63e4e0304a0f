{"version": 3, "file": "MDDLoader.cjs", "sources": ["../../src/loaders/MDDLoader.js"], "sourcesContent": ["/**\n * MDD is a special format that stores a position for every vertex in a model for every frame in an animation.\n * Similar to BVH, it can be used to transfer animation data between different 3D applications or engines.\n *\n * MDD stores its data in binary format (big endian) in the following way:\n *\n * number of frames (a single uint32)\n * number of vertices (a single uint32)\n * time values for each frame (sequence of float32)\n * vertex data for each frame (sequence of float32)\n */\n\nimport { AnimationClip, BufferAttribute, FileLoader, Loader, NumberKeyframeTrack } from 'three'\n\nclass MDDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.load(\n      url,\n      function (data) {\n        onLoad(scope.parse(data))\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    const view = new DataView(data)\n\n    const totalFrames = view.getUint32(0)\n    const totalPoints = view.getUint32(4)\n\n    let offset = 8\n\n    // animation clip\n\n    const times = new Float32Array(totalFrames)\n    const values = new Float32Array(totalFrames * totalFrames).fill(0)\n\n    for (let i = 0; i < totalFrames; i++) {\n      times[i] = view.getFloat32(offset)\n      offset += 4\n      values[totalFrames * i + i] = 1\n    }\n\n    const track = new NumberKeyframeTrack('.morphTargetInfluences', times, values)\n    const clip = new AnimationClip('default', times[times.length - 1], [track])\n\n    // morph targets\n\n    const morphTargets = []\n\n    for (let i = 0; i < totalFrames; i++) {\n      const morphTarget = new Float32Array(totalPoints * 3)\n\n      for (let j = 0; j < totalPoints; j++) {\n        const stride = j * 3\n\n        morphTarget[stride + 0] = view.getFloat32(offset)\n        offset += 4 // x\n        morphTarget[stride + 1] = view.getFloat32(offset)\n        offset += 4 // y\n        morphTarget[stride + 2] = view.getFloat32(offset)\n        offset += 4 // z\n      }\n\n      const attribute = new BufferAttribute(morphTarget, 3)\n      attribute.name = 'morph_' + i\n\n      morphTargets.push(attribute)\n    }\n\n    return {\n      morphTargets: morphTargets,\n      clip: clip,\n    }\n  }\n}\n\nexport { MDDLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "NumberKeyframeTrack", "AnimationClip", "BufferAttribute"], "mappings": ";;;AAcA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,eAAO,MAAM,MAAM,IAAI,CAAC;AAAA,MACzB;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,UAAM,OAAO,IAAI,SAAS,IAAI;AAE9B,UAAM,cAAc,KAAK,UAAU,CAAC;AACpC,UAAM,cAAc,KAAK,UAAU,CAAC;AAEpC,QAAI,SAAS;AAIb,UAAM,QAAQ,IAAI,aAAa,WAAW;AAC1C,UAAM,SAAS,IAAI,aAAa,cAAc,WAAW,EAAE,KAAK,CAAC;AAEjE,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,CAAC,IAAI,KAAK,WAAW,MAAM;AACjC,gBAAU;AACV,aAAO,cAAc,IAAI,CAAC,IAAI;AAAA,IAC/B;AAED,UAAM,QAAQ,IAAIC,MAAAA,oBAAoB,0BAA0B,OAAO,MAAM;AAC7E,UAAM,OAAO,IAAIC,oBAAc,WAAW,MAAM,MAAM,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;AAI1E,UAAM,eAAe,CAAE;AAEvB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,cAAc,IAAI,aAAa,cAAc,CAAC;AAEpD,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,cAAM,SAAS,IAAI;AAEnB,oBAAY,SAAS,CAAC,IAAI,KAAK,WAAW,MAAM;AAChD,kBAAU;AACV,oBAAY,SAAS,CAAC,IAAI,KAAK,WAAW,MAAM;AAChD,kBAAU;AACV,oBAAY,SAAS,CAAC,IAAI,KAAK,WAAW,MAAM;AAChD,kBAAU;AAAA,MACX;AAED,YAAM,YAAY,IAAIC,sBAAgB,aAAa,CAAC;AACpD,gBAAU,OAAO,WAAW;AAE5B,mBAAa,KAAK,SAAS;AAAA,IAC5B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACD;AAAA,EACF;AACH;;"}