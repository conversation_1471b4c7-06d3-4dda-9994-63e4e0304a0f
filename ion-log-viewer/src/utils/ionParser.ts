import * as ion from 'ion-js';

// Define interfaces for the ION log structure
export interface IonMessage {
  timestamp: number;
  publishTime?: number;
  content: any;
}

export interface IonTopic {
  name: string;
  type: string;
  frequency?: number;
  messages: IonMessage[];
  isHumanReadable: boolean;
}

export interface IonSessionInfo {
  startTime?: number;
  endTime?: number;
  duration?: number;
  recordingDate?: string;
  version?: string;
  [key: string]: any;
}

export interface IonRobotInfo {
  model?: string;
  name?: string;
  version?: string;
  botModel?: Uint8Array; // 3D model data
  [key: string]: any;
}

export interface IonLogData {
  sessionInfo: IonSessionInfo;
  robotInfo: IonRobotInfo;
  topics: IonTopic[];
  metadata: any;
  totalDuration: number;
  startTime: number;
  endTime: number;
}

// ION value handlers for both ION DOM and plain JavaScript objects
export class IonValueHandler {
  static handleValue(value: any): any {
    if (value === null || value === undefined) {
      return null;
    }

    // Handle ION DOM objects
    if (value && typeof value === 'object' && typeof value.getType === 'function') {
      try {
        const ionType = value.getType();

        switch (ionType) {
          case ion.IonTypes.NULL:
            return null;
          case ion.IonTypes.BOOL:
            return value.booleanValue();
          case ion.IonTypes.INT:
            return value.numberValue();
          case ion.IonTypes.FLOAT:
            return value.numberValue();
          case ion.IonTypes.DECIMAL:
            return value.numberValue();
          case ion.IonTypes.TIMESTAMP:
            return value.timestampValue()?.getTime() || value.numberValue();
          case ion.IonTypes.STRING:
            return value.stringValue();
          case ion.IonTypes.SYMBOL:
            return value.stringValue();
          case ion.IonTypes.BLOB:
            return value.uInt8ArrayValue();
          case ion.IonTypes.CLOB:
            return value.stringValue();
          case ion.IonTypes.LIST:
            try {
              const elements = value.elements();
              if (elements && typeof elements[Symbol.iterator] === 'function') {
                return Array.from(elements).map((item: any) => this.handleValue(item));
              }
              return [];
            } catch (error) {
              console.warn('Error processing ION list:', error);
              return [];
            }
          case ion.IonTypes.SEXP:
            try {
              const elements = value.elements();
              if (elements && typeof elements[Symbol.iterator] === 'function') {
                return Array.from(elements).map((item: any) => this.handleValue(item));
              }
              return [];
            } catch (error) {
              console.warn('Error processing ION S-expression:', error);
              return [];
            }
          case ion.IonTypes.STRUCT:
            try {
              const result: any = {};
              const fields = value.fields();
              if (fields && typeof fields[Symbol.iterator] === 'function') {
                for (const field of fields) {
                  const fieldName = field.fieldName();
                  if (fieldName) {
                    result[fieldName] = this.handleValue(field);
                  }
                }
              }
              return result;
            } catch (error) {
              console.warn('Error processing ION struct:', error);
              return {};
            }
          default:
            console.warn('Unknown ION type:', ionType);
            return value.value ? value.value() : value;
        }
      } catch (error) {
        console.warn('Error processing ION value:', error);
        // Fall back to trying to get the raw value
        try {
          return value.value ? value.value() : value;
        } catch {
          return String(value);
        }
      }
    }

    // Handle plain JavaScript values
    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {
      return value;
    }

    if (Array.isArray(value)) {
      return value.map(item => this.handleValue(item));
    }

    if (typeof value === 'object') {
      const result: any = {};
      for (const [key, val] of Object.entries(value)) {
        result[key] = this.handleValue(val);
      }
      return result;
    }

    return value;
  }

  static isHumanReadable(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') return true;

    // Check for ION DOM objects
    if (value && typeof value === 'object' && typeof value.getType === 'function') {
      try {
        const ionType = value.getType();
        if (ionType === ion.IonTypes.BLOB) return false; // Binary data
        if (ionType === ion.IonTypes.CLOB) return true;
        if (ionType === ion.IonTypes.LIST || ionType === ion.IonTypes.SEXP) {
          return value.elements().every((item: any) => this.isHumanReadable(item));
        }
        if (ionType === ion.IonTypes.STRUCT) {
          return value.fields().every((field: any) => this.isHumanReadable(field));
        }
        return true;
      } catch {
        return true; // Default to human readable if we can't determine
      }
    }

    // Check for binary data patterns
    if (value instanceof Uint8Array || value instanceof ArrayBuffer) return false;
    if (typeof value === 'object' && value.type === 'Buffer') return false; // Node.js Buffer

    if (Array.isArray(value)) {
      return value.every(item => this.isHumanReadable(item));
    }

    if (typeof value === 'object') {
      return Object.values(value).every(val => this.isHumanReadable(val));
    }

    return true;
  }
}

// Main parser function
export async function parseIonLog(data: Uint8Array): Promise<IonLogData> {
  try {
    let parsedData: any;

    // First try to parse as ION binary format
    try {
      console.log('Attempting to parse as ION binary format...');
      parsedData = ion.load(data);
      console.log('Successfully parsed as ION binary');
    } catch (ionError) {
      console.log('ION binary parsing failed, trying text format...', ionError);

      // Try as ION text format
      try {
        const textData = new TextDecoder().decode(data);
        parsedData = ion.load(textData);
        console.log('Successfully parsed as ION text');
      } catch (ionTextError) {
        console.log('ION text parsing failed, trying JSON...', ionTextError);

        // Finally try as JSON
        try {
          const textData = new TextDecoder().decode(data);
          parsedData = JSON.parse(textData);
          console.log('Successfully parsed as JSON');
        } catch (jsonError) {
          throw new Error(`Unable to parse file. Tried ION binary, ION text, and JSON formats. Last error: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`);
        }
      }
    }

    if (!parsedData) {
      throw new Error('Parsed data is null or undefined');
    }

    console.log('Parsed data structure:', parsedData);

    // Convert ION DOM to plain JavaScript objects
    let processedData: any;
    try {
      processedData = IonValueHandler.handleValue(parsedData);
      console.log('Processed data:', processedData);
    } catch (error) {
      console.error('Error processing ION data:', error);
      // If processing fails, try to use the raw parsed data
      processedData = parsedData;
      console.log('Using raw parsed data due to processing error');
    }

    if (!processedData || typeof processedData !== 'object') {
      throw new Error('Invalid log format: root must be a struct/object');
    }

    // Extract session information
    console.log('Extracting session info from:', processedData);
    const sessionInfo: IonSessionInfo = extractSessionInfo(processedData);
    console.log('Extracted session info:', sessionInfo);

    // Extract robot information
    console.log('Extracting robot info from:', processedData);
    const robotInfo: IonRobotInfo = extractRobotInfo(processedData);
    console.log('Extracted robot info:', robotInfo);

    // Extract topics
    console.log('Extracting topics from:', processedData);
    const topics: IonTopic[] = extractTopics(processedData);
    console.log('Extracted topics:', topics.map(t => ({ name: t.name, messageCount: t.messages.length })));

    // Calculate time bounds
    const { startTime, endTime, totalDuration } = calculateTimeBounds(topics);

    console.log('Extraction complete:', { sessionInfo, robotInfo, topicCount: topics.length });

    return {
      sessionInfo,
      robotInfo,
      topics,
      metadata: processedData,
      totalDuration,
      startTime,
      endTime
    };
  } catch (error) {
    console.error('Parsing error:', error);
    throw new Error(`Failed to parse log: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function extractSessionInfo(data: any): IonSessionInfo {
  const sessionInfo: IonSessionInfo = {};

  console.log('Session extraction - input data keys:', Object.keys(data));

  // Look for session-related fields in various possible locations
  if (data.session) {
    console.log('Found session object:', data.session);
    Object.assign(sessionInfo, data.session);
  }

  if (data.metadata) {
    console.log('Found metadata object:', data.metadata);
    const metadata = data.metadata;
    if (metadata.startTime) sessionInfo.startTime = metadata.startTime;
    if (metadata.endTime) sessionInfo.endTime = metadata.endTime;
    if (metadata.duration) sessionInfo.duration = metadata.duration;
    if (metadata.recordingDate) sessionInfo.recordingDate = metadata.recordingDate;
    if (metadata.version) sessionInfo.version = metadata.version;
  }

  // Look for session info in root level
  if (data.startTime) sessionInfo.startTime = data.startTime;
  if (data.endTime) sessionInfo.endTime = data.endTime;
  if (data.duration) sessionInfo.duration = data.duration;
  if (data.recordingDate) sessionInfo.recordingDate = data.recordingDate;
  if (data.version) sessionInfo.version = data.version;

  // Look for common ION log fields
  if (data.header) {
    console.log('Found header object:', data.header);
    const header = data.header;
    if (header.startTime) sessionInfo.startTime = header.startTime;
    if (header.endTime) sessionInfo.endTime = header.endTime;
    if (header.duration) sessionInfo.duration = header.duration;
  }

  // Look for timestamp-related fields that might indicate session bounds
  if (data.start_time) sessionInfo.startTime = data.start_time;
  if (data.end_time) sessionInfo.endTime = data.end_time;
  if (data.recording_start) sessionInfo.startTime = data.recording_start;
  if (data.recording_end) sessionInfo.endTime = data.recording_end;

  // Look for version info in various places
  if (data.file_version) sessionInfo.version = data.file_version;
  if (data.format_version) sessionInfo.version = data.format_version;

  // Try to extract from any nested structures
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'object' && value !== null) {
      // Check if this looks like session metadata
      if (key.toLowerCase().includes('session') ||
          key.toLowerCase().includes('metadata') ||
          key.toLowerCase().includes('header') ||
          key.toLowerCase().includes('info')) {
        console.log(`Found potential session data in ${key}:`, value);

        const obj = value as any;
        if (obj.startTime) sessionInfo.startTime = obj.startTime;
        if (obj.endTime) sessionInfo.endTime = obj.endTime;
        if (obj.duration) sessionInfo.duration = obj.duration;
        if (obj.recordingDate) sessionInfo.recordingDate = obj.recordingDate;
        if (obj.version) sessionInfo.version = obj.version;
        if (obj.start_time) sessionInfo.startTime = obj.start_time;
        if (obj.end_time) sessionInfo.endTime = obj.end_time;
      }
    }
  }

  console.log('Final session info:', sessionInfo);
  return sessionInfo;
}

function extractRobotInfo(data: any): IonRobotInfo {
  const robotInfo: IonRobotInfo = {};

  console.log('Robot extraction - input data keys:', Object.keys(data));

  // Look for robot-related fields
  if (data.robot) {
    console.log('Found robot object:', data.robot);
    Object.assign(robotInfo, data.robot);
  }

  if (data.metadata) {
    console.log('Found metadata object for robot:', data.metadata);
    const metadata = data.metadata;
    if (metadata.robotModel) robotInfo.model = metadata.robotModel;
    if (metadata.robotName) robotInfo.name = metadata.robotName;
    if (metadata.robotVersion) robotInfo.version = metadata.robotVersion;
    if (metadata.botModel) robotInfo.botModel = metadata.botModel;
    if (metadata.robot_model) robotInfo.model = metadata.robot_model;
    if (metadata.robot_name) robotInfo.name = metadata.robot_name;
    if (metadata.robot_version) robotInfo.version = metadata.robot_version;
  }

  // Look for robot info in root level
  if (data.robotModel) robotInfo.model = data.robotModel;
  if (data.robotName) robotInfo.name = data.robotName;
  if (data.robotVersion) robotInfo.version = data.robotVersion;
  if (data.botModel) robotInfo.botModel = data.botModel;
  if (data.robot_model) robotInfo.model = data.robot_model;
  if (data.robot_name) robotInfo.name = data.robot_name;
  if (data.robot_version) robotInfo.version = data.robot_version;

  // Look for common robot fields
  if (data.model) robotInfo.model = data.model;
  if (data.name) robotInfo.name = data.name;
  if (data.platform) robotInfo.model = data.platform;
  if (data.device) robotInfo.model = data.device;

  // Try to extract from any nested structures
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'object' && value !== null) {
      // Check if this looks like robot metadata
      if (key.toLowerCase().includes('robot') ||
          key.toLowerCase().includes('device') ||
          key.toLowerCase().includes('platform') ||
          key.toLowerCase().includes('system')) {
        console.log(`Found potential robot data in ${key}:`, value);

        const obj = value as any;
        if (obj.model) robotInfo.model = obj.model;
        if (obj.name) robotInfo.name = obj.name;
        if (obj.version) robotInfo.version = obj.version;
        if (obj.robot_model) robotInfo.model = obj.robot_model;
        if (obj.robot_name) robotInfo.name = obj.robot_name;
        if (obj.robot_version) robotInfo.version = obj.robot_version;
        if (obj.botModel) robotInfo.botModel = obj.botModel;
      }
    }
  }

  console.log('Final robot info:', robotInfo);
  return robotInfo;
}

function extractTopics(data: any): IonTopic[] {
  const topics: IonTopic[] = [];

  // Try different possible structures for topics
  let topicsData: any[] = [];

  if (data.topics && Array.isArray(data.topics)) {
    topicsData = data.topics;
  } else if (data.messages && Array.isArray(data.messages)) {
    // Some ION logs might have messages directly
    topicsData = data.messages;
  } else if (Array.isArray(data)) {
    // The root might be an array of topics/messages
    topicsData = data;
  } else {
    // Try to find topic-like structures in the data
    for (const [key, value] of Object.entries(data)) {
      if (Array.isArray(value) && value.length > 0) {
        // Check if this looks like a topic
        const firstItem = value[0];
        if (firstItem && typeof firstItem === 'object' &&
            (firstItem.timestamp || firstItem.time || firstItem.header)) {
          // This looks like a topic with messages
          topicsData.push({
            name: key,
            type: 'unknown',
            messages: value
          });
        }
      }
    }
  }

  for (const topicData of topicsData) {
    const topic = topicData;

    // Extract topic name and type
    let topicName = topic.name || topic.topic || 'unknown';
    let topicType = topic.type || topic.messageType || 'unknown';

    // If this is a direct message array, use the key as topic name
    if (!topic.name && !topic.topic && topicData === data[topicName]) {
      // This case is handled above
    }

    const messages: IonMessage[] = [];

    // Extract messages
    let messagesArray = topic.messages || topic.data || [];
    if (!Array.isArray(messagesArray) && topic.timestamp) {
      // This might be a single message
      messagesArray = [topic];
    }

    if (Array.isArray(messagesArray)) {
      for (const msgData of messagesArray) {
        const message = msgData;

        // Extract timestamp from various possible fields
        let timestamp = message.timestamp || message.time || message.stamp || 0;
        if (message.header && message.header.stamp) {
          timestamp = message.header.stamp.sec * 1000 + (message.header.stamp.nsec || 0) / 1000000;
        }

        messages.push({
          timestamp: timestamp,
          publishTime: message.publishTime || message.publish_time,
          content: message.content || message.data || message
        });
      }
    }

    // Sort messages by timestamp
    messages.sort((a, b) => a.timestamp - b.timestamp);

    // Determine if topic is human readable
    const isHumanReadable = messages.length === 0 ||
      messages.every(msg => IonValueHandler.isHumanReadable(msg.content));

    if (topicName && messages.length > 0) {
      topics.push({
        name: topicName,
        type: topicType,
        frequency: topic.frequency,
        messages,
        isHumanReadable
      });
    }
  }

  console.log(`Extracted ${topics.length} topics:`, topics.map(t => ({ name: t.name, messageCount: t.messages.length })));
  return topics;
}

function calculateTimeBounds(topics: IonTopic[]): { startTime: number; endTime: number; totalDuration: number } {
  let startTime = Infinity;
  let endTime = -Infinity;

  for (const topic of topics) {
    for (const message of topic.messages) {
      if (message.timestamp < startTime) {
        startTime = message.timestamp;
      }
      if (message.timestamp > endTime) {
        endTime = message.timestamp;
      }
    }
  }

  if (startTime === Infinity) {
    startTime = 0;
    endTime = 0;
  }

  const totalDuration = endTime - startTime;

  return { startTime, endTime, totalDuration };
}

// Utility function to find messages at a specific time
export function findMessageAtTime(messages: IonMessage[], targetTime: number): IonMessage | null {
  if (messages.length === 0) return null;

  // Binary search for the closest message
  let left = 0;
  let right = messages.length - 1;
  let closest = messages[0];

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const message = messages[mid];

    if (Math.abs(message.timestamp - targetTime) < Math.abs(closest.timestamp - targetTime)) {
      closest = message;
    }

    if (message.timestamp < targetTime) {
      left = mid + 1;
    } else if (message.timestamp > targetTime) {
      right = mid - 1;
    } else {
      return message;
    }
  }

  return closest;
}

// Utility function to get all messages up to a specific time
export function getMessagesUpToTime(messages: IonMessage[], targetTime: number): IonMessage[] {
  return messages.filter(msg => msg.timestamp <= targetTime);
}
