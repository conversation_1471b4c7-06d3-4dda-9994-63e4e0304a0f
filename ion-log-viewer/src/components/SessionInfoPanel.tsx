import React from 'react';
import {
  Paper,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Chip
} from '@mui/material';
import { IonLogData } from '../utils/ionParser';

interface SessionInfoPanelProps {
  data: IonLogData;
}

const SessionInfoPanel: React.FC<SessionInfoPanelProps> = ({ data }) => {
  const { sessionInfo, totalDuration, startTime, endTime, topics } = data;

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  // Calculate some basic stats from the log data
  const totalTopics = data?.topics?.length || 0;
  const totalMessages = data?.topics?.reduce((sum, topic) => sum + topic.messages.length, 0) || 0;
  const calculatedDuration = data?.totalDuration || 0;
  const calculatedStartTime = data?.startTime || 0;
  const calculatedEndTime = data?.endTime || 0;

  const sessionRows = [
    {
      label: 'Start Time',
      value: sessionInfo.startTime ? formatTimestamp(sessionInfo.startTime) :
             calculatedStartTime ? formatTimestamp(calculatedStartTime) :
             startTime ? formatTimestamp(startTime) : 'Unknown'
    },
    {
      label: 'End Time',
      value: sessionInfo.endTime ? formatTimestamp(sessionInfo.endTime) :
             calculatedEndTime ? formatTimestamp(calculatedEndTime) :
             endTime ? formatTimestamp(endTime) : 'Unknown'
    },
    {
      label: 'Duration',
      value: sessionInfo.duration ? formatDuration(sessionInfo.duration) :
             calculatedDuration ? formatDuration(calculatedDuration) :
             totalDuration ? formatDuration(totalDuration) : 'Unknown'
    },
    {
      label: 'Recording Date',
      value: sessionInfo.recordingDate ||
             (calculatedStartTime ? new Date(calculatedStartTime).toDateString() : 'Unknown')
    },
    {
      label: 'Version',
      value: sessionInfo.version || 'Unknown'
    },
    {
      label: 'Total Topics',
      value: totalTopics.toString()
    },
    {
      label: 'Total Messages',
      value: totalMessages.toString()
    },
    {
      label: 'Human Readable Topics',
      value: topics.filter(t => t.isHumanReadable).length.toString()
    },
    {
      label: 'Binary Topics',
      value: topics.filter(t => !t.isHumanReadable).length.toString()
    }
  ];

  // Add any additional session info fields
  Object.entries(sessionInfo).forEach(([key, value]) => {
    if (!['startTime', 'endTime', 'duration', 'recordingDate', 'version'].includes(key)) {
      sessionRows.push({
        label: key.charAt(0).toUpperCase() + key.slice(1),
        value: typeof value === 'object' ? JSON.stringify(value) : String(value)
      });
    }
  });

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" component="h2" gutterBottom>
          Session Information
        </Typography>
        <Chip
          label="Question 1"
          color="primary"
          size="small"
          sx={{ mb: 1 }}
        />
      </Box>

      <TableContainer>
        <Table size="small">
          <TableBody>
            {sessionRows.map((row, index) => (
              <TableRow key={index}>
                <TableCell component="th" scope="row" sx={{ fontWeight: 'bold', width: '40%' }}>
                  {row.label}
                </TableCell>
                <TableCell>
                  {row.value}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Topic Summary */}
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Topic Summary
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {topics.slice(0, 5).map((topic, index) => (
            <Chip
              key={index}
              label={topic.name}
              size="small"
              color={topic.isHumanReadable ? 'success' : 'warning'}
              variant="outlined"
            />
          ))}
          {topics.length > 5 && (
            <Chip
              label={`+${topics.length - 5} more`}
              size="small"
              variant="outlined"
            />
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default SessionInfoPanel;
