import React from 'react';
import {
  Paper,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Chip,
  Alert
} from '@mui/material';
import { IonLogData } from '../utils/ionParser';

interface RobotInfoPanelProps {
  data: IonLogData;
}

const RobotInfoPanel: React.FC<RobotInfoPanelProps> = ({ data }) => {
  const { robotInfo, metadata } = data;

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Try to extract robot info from metadata if not found in robotInfo
  const extractedModel = robotInfo.model ||
                         (metadata && (metadata.robotModel || metadata.robot_model || metadata.model || metadata.platform)) ||
                         'Unknown';
  const extractedName = robotInfo.name ||
                        (metadata && (metadata.robotName || metadata.robot_name || metadata.name)) ||
                        'Unknown';
  const extractedVersion = robotInfo.version ||
                           (metadata && (metadata.robotVersion || metadata.robot_version || metadata.version)) ||
                           'Unknown';

  const robotRows = [
    {
      label: 'Model',
      value: extractedModel
    },
    {
      label: 'Name',
      value: extractedName
    },
    {
      label: 'Version',
      value: extractedVersion
    }
  ];

  // Add 3D model information if available
  if (robotInfo.botModel) {
    robotRows.push({
      label: '3D Model Size',
      value: formatBytes(robotInfo.botModel.length)
    });
  }

  // Add any additional robot info fields
  Object.entries(robotInfo).forEach(([key, value]) => {
    if (!['model', 'name', 'version', 'botModel'].includes(key)) {
      robotRows.push({
        label: key.charAt(0).toUpperCase() + key.slice(1),
        value: typeof value === 'object' ? JSON.stringify(value) : String(value)
      });
    }
  });

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" component="h2" gutterBottom>
          Robot Information
        </Typography>
        <Chip
          label="Question 1"
          color="primary"
          size="small"
          sx={{ mb: 1 }}
        />
      </Box>

      <TableContainer>
        <Table size="small">
          <TableBody>
            {robotRows.map((row, index) => (
              <TableRow key={index}>
                <TableCell component="th" scope="row" sx={{ fontWeight: 'bold', width: '40%' }}>
                  {row.label}
                </TableCell>
                <TableCell>
                  {row.value}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 3D Model Status */}
      {robotInfo.botModel ? (
        <Alert severity="success" sx={{ mt: 2 }}>
          3D model available for visualization
        </Alert>
      ) : (
        <Alert severity="info" sx={{ mt: 2 }}>
          No 3D model data found
        </Alert>
      )}

      {/* Additional Robot Capabilities */}
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Capabilities
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {robotInfo.botModel && (
            <Chip
              label="3D Model"
              size="small"
              color="success"
              variant="outlined"
            />
          )}
          {robotInfo.model && (
            <Chip
              label="Model Info"
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
          {robotInfo.version && (
            <Chip
              label="Version Info"
              size="small"
              color="secondary"
              variant="outlined"
            />
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default RobotInfoPanel;
